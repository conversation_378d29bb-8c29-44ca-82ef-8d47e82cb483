import { createTheme } from "@mui/material/styles";

// note - use this guide for hexadecimal transparency values...
// https://gist.github.com/lopspower/03fb1cc0ac9f32ef38f4

export const colors = {
  primary: {
    main: "#84D2EF",
    dark: "#59C0E8",
    light: "#3DB4E5", // note this is not in the figma
    contrastText: "#000000",
  },
  secondary: {
    main: "#FFFFFF",
  },
  text: {
    primary: "#FFFFFF",
    secondary: "#FFFFFFB3",
    disabled: "#FFFFFF61",
    yellow: "#FFC107",
    donutMain: "#2196f3",
    donutLight: "#84D2EF",
  },
  background: {
    primary: "#313131",
    secondary: "#2d3039",
  },
  success: { main: "#81C784" },
  error: { main: "#F44336" },
  warning: { main: "#FFA726" },
  info: { main: "#29B6F6" },
  custom: {
    main: "#FFC107",
  },
  action: {
    hover: "rgba(61, 180, 229, 0.08)",
    selected: "rgba(61, 180, 229, 0.16)",
  },
  legends: {
    color1: "#275CE2", // Blue
    color2: "#29A6E1", // Teal Blue
    color3: "#66BB6A", // Green
    color4: "#FFA726", // Orange
    color5: "#F44336", // Red
    color6: "#84D2EF", // Light Blue
    color7: "#C3D4FB", // Light Lavender Blue
    color8: "#59C0E8", // Sky Blue
    color9: "#B4E4F6", // Pale Blue
    color10: "#29B6F6", // Cyan
  },
};

const Theme = createTheme({
  palette: {
    mode: "dark", // Or 'light' for light theme
    ...colors,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: "none",
          fontSize: "15px",
        },
      },
    },
    MuiAutocomplete: {
      styleOverrides: {
        root: {
          "& .MuiButtonBase-root.MuiChip-root": {
            backgroundColor: "#2196f3",
          },
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        root: {
          "& .MuiTab-root": {
            borderBottom: "3px solid rgba(255,255,255,.05)",
          },
          "& .Mui-selected": {
            background: "rgba(255,255,255,.05)",
            borderRadius: "4px",
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundColor: colors.background.secondary,
          scrollbarColor: "#FFFFFF24 #FFFFFF00",
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: 6,
          "&:last-child": {
            paddingBottom: 6,
          },
        },
      },
    },
    MuiDataGrid: {
      styleOverrides: {
        root: {
          borderRadius: 0,
          backgroundColor: colors.background.secondary,
          "& .MuiDataGrid-container--top [role=row]": {
            backgroundColor: `${colors.background.secondary}`,
          }, // defaults to DARK color
          "& .MuiDataGrid-row:nth-of-type(odd)": {
            backgroundColor: `${colors.secondary.main}0A`,
          },
          "& .MuiDataGrid-row:hover": {
            backgroundColor: `${colors.primary.main}1A`,
          },
          "& .MuiDataGrid-cell:hover": {
            color: colors.primary.main,
          },
        },
      },
    },
  },
  typography: {
    fontFamily: "Roboto",
    fontSize: 16,
    fontWeightLight: 300,
    fontWeightRegular: 400,
    fontWeightMedium: 500,
    fontWeightBold: 700,
    h1: {
      fontSize: "96px",
    },
    h2: {
      fontSize: "60px",
    },
    h3: {
      fontSize: "48px",
    },
    h4: {
      fontSize: "34px",
    },
    h5: {
      fontSize: "24px",
    },
    h6: {
      fontSize: "20px",
    },
    subtitle1: {
      fontSize: "16px",
    },
    subtitle2: {
      fontSize: "14px",
    },
    body1: {
      fontSize: "16px",
    },
    body2: {
      fontSize: "14px",
    },
    caption: {
      fontSize: "12px",
    },
    overline: {
      fontSize: "12px",
      fontWeight: "bold",
      textTransform: "uppercase",
    },
  },
});

export default Theme;
