// NPM PACKAGE IMPORTS
import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faUsers,
  faUser,
  faDollarSign,
} from "@fortawesome/free-solid-svg-icons";
import axios from "axios";
import jwt from "jsonwebtoken";
import moment from "moment";
import "moment-timezone";
import { create, all } from "mathjs";
import { v4 as uuid } from "uuid";
import conversions from "./utils/_conversions";

// needed when running groupBy test cases
import Object from "core-js/actual/object";

import seedrandom from "seedrandom";

// REDUX IMPORTS
import store from "./redux/store";
import { notify } from "./components/reusable/alertPopup/alertPopupActions";
import {
  handleEnqueueSpinner,
  handleDequeueSpinner,
} from "./components/reusable/generalSpinner/generalSpinnerActions";
import { handleSaveColumnState } from "./redux/generalActions";

// CONSTANTS
import { SystemSettings } from "./utils/system-settings";
import { DateTime } from "luxon";

const config = {};
const math = create(all, config);

// EXPORTS
export const axiosApiCall = (token, testStore = null) => (
  apiPath,
  method,
  body = null,
  successMessage = null,
  disableErrorAlert = false,
  disableLoadingSpinner = false,
  customErrorHandler = null,
  spinnerTimeout = 3000
) => {
  const spinnerId = uuid();
  if (!disableLoadingSpinner)
    (testStore || store).dispatch(
      handleEnqueueSpinner(spinnerId, spinnerTimeout)
    );

  let config = {
    method,
    url: apiPath,
    baseURL: process.env.REACT_APP_API,
    headers: { Authorization: `Bearer ${token}` },
  };

  if (body) Object.assign(config, { data: body });
  return axios(config)
    .then((res) => {
      if (!disableLoadingSpinner)
        (testStore || store).dispatch(handleDequeueSpinner(spinnerId));

      if (customErrorHandler) {
        customErrorHandler(res.data, (message) =>
          (testStore || store).dispatch(
            notify({ id: Date.now(), type: "ERROR", message })
          )
        );
      }

      const handleSuccessMessage = () => {
        let type, message;
        let successMessageResponse =
          typeof successMessage === "function"
            ? successMessage(res)
            : successMessage;
        if (!successMessageResponse) successMessageResponse = res.data;
        if (
          typeof successMessage === "function" &&
          typeof successMessageResponse === "object"
        ) {
          let obj = successMessageResponse;
          type = obj.type || "SUCCESS";
          message = obj.message;
        } else if (typeof successMessage === "function") {
          type = "SUCCESS";
          message = successMessageResponse;
        } else if (typeof successMessage === "object") {
          type = successMessage.type;
          message = successMessage.message;
        } else if (typeof successMessage === "string") {
          type = "SUCCESS";
          message = successMessage;
        }

        (testStore || store).dispatch(
          notify({
            id: Date.now(),
            type,
            message,
          })
        );
      };

      if (Array.isArray(res.data)) {
        const matrix = res.data.filter((r) => Array.isArray(r));
        if (matrix.length) {
          matrix.flat().forEach((r) => {
            if (r.failed_rows) {
              const failedRows = JSON.parse(r.failed_rows)[0];
              const message = failedRows.rows
                ? `Failed to create ${
                    failedRows.rows.split(",").length
                  } item(s) (rows ${failedRows.rows
                    .split(",")
                    .join(", ")}) due to: ${failedRows.reason}.`
                : r.message;
              (testStore || store).dispatch(
                notify({
                  id: Date.now(),
                  type: "ERROR",
                  message,
                })
              );
            } else if (
              r.total_items_created !== undefined &&
              r.total_items_created > 0
            ) {
              (testStore || store).dispatch(
                notify({
                  id: Date.now(),
                  type: "SUCCESS",
                  message: `Successfully created ${r.total_items_created} item(s).`,
                })
              );
            }
          });
        } else if (
          res.data &&
          ((res.data[0] &&
            res.data[0].state &&
            ["error", "warning"].includes(res.data[0].state)) ||
            (res.data.state && ["error", "warning"].includes(res.data.state)))
        ) {
          // eslint-disable-next-line no-throw-literal
          throw {
            response: {
              data: {
                error: {
                  status: 400,
                  message: (res.data[0] || res.data).message,
                },
              },
            },
          };
        } else if (successMessage) {
          handleSuccessMessage();
        }
      } else if (
        res.data &&
        ((res.data[0] &&
          res.data[0].state &&
          ["error", "warning"].includes(res.data[0].state)) ||
          (res.data.state && ["error", "warning"].includes(res.data.state)))
      ) {
        // eslint-disable-next-line no-throw-literal
        throw {
          response: {
            data: {
              error: {
                status: 400,
                message: (res.data[0] || res.data).message,
              },
            },
          },
        };
      } else if (successMessage) {
        handleSuccessMessage();
      }

      return res.data;
    })
    .catch((err) => {
      if (!disableLoadingSpinner)
        (testStore || store).dispatch(handleDequeueSpinner(spinnerId));

      // CAN'T TEST THIS KICKOUT SECTION
      // NODEJS DOESN'T KNOW WHAT DOCUMENT OR WINDOW ARE
      if (
        err.response &&
        err.response.data &&
        err.response.data.error &&
        err.response.data.error.status === 401
      ) {
        document.cookie = document.cookie
          .split("; ")
          .filter((c) => !/^PHPSESSID/.test(c))
          .join("; ");
        return window.location.assign(`${process.env.REACT_APP_LOGIN}`);
      }

      // @todo update the issue here...
      if (customErrorHandler) {
        customErrorHandler(err.response.data, (message) =>
          (testStore || store).dispatch(
            notify({ id: Date.now(), type: "ERROR", message })
          )
        );
      } else if (
        typeof disableErrorAlert === "function"
          ? !disableErrorAlert(err.response && err.response.data)
          : !disableErrorAlert
      ) {
        // @todo where the error is currently being caught
        (testStore || store).dispatch(
          notify({
            id: Date.now(),
            type: "ERROR",
            message:
              err.response && err.response.data
                ? err.response.data.error
                  ? err.response.data.error.message
                  : err.response.data.message
                : "Error completing action",
          })
        );
      }

      return err.response && err.response.data
        ? err.response.data.error
          ? err.response.data
          : { error: err.response.data }
        : { error: { status: 500, message: "Error completing action" } };
    });
};

export const useApiCall = (testStore) => {
  const { token } = store.getState().profileData;
  const tokenToUse = token || process.env.REACT_APP_TEST_TOKEN;
  const decodedToken = tokenToUse ? jwt.decode(tokenToUse).data : null;
  const user_id = decodedToken ? decodedToken.id : null;
  const system_features = decodedToken ? decodedToken.features : [];
  const apiCall = axiosApiCall(tokenToUse, testStore);

  return { apiCall, tokenToUse, user_id, system_features };
};

export const axiosApiCallv2 = (token, testStore = null) => (
  apiPath,
  method,
  body = null,
  successMessage = null,
  disableErrorAlert = false,
  disableLoadingSpinner = false,
  errorAlertType = 0, // 0 for notify, 1 for modal, 2... etc.
  customErrorHandler = null
) => {
  const spinnerId = uuid();
  if (!disableLoadingSpinner)
    (testStore || store).dispatch(handleEnqueueSpinner(spinnerId));

  let config = {
    method,
    url: apiPath,
    baseURL: process.env.REACT_APP_API,
    headers: { Authorization: `Bearer ${token}` },
  };
  if (body) Object.assign(config, { data: body });
  return axios(config)
    .then((res) => {
      if (!disableLoadingSpinner)
        (testStore || store).dispatch(handleDequeueSpinner(spinnerId));

      if (customErrorHandler) {
        customErrorHandler(res.data, (message) =>
          (testStore || store).dispatch(
            notify({ id: Date.now(), type: "ERROR", message })
          )
        );
      }

      const handleSuccessMessage = () => {
        let type, message;

        if (
          typeof successMessage === "function" &&
          typeof successMessage(res) === "object"
        ) {
          let obj = successMessage(res);
          type = obj.type;
          message = obj.message;
        } else if (typeof successMessage === "function") {
          type = "SUCCESS";
          message = successMessage(res);
        } else if (typeof successMessage === "object") {
          type = successMessage.type;
          message = successMessage.message;
        } else if (typeof successMessage === "string") {
          type = "SUCCESS";
          message = successMessage;
        }

        (testStore || store).dispatch(
          notify({
            id: Date.now(),
            type,
            message,
          })
        );
      };

      if (Array.isArray(res.data)) {
        const matrix = res.data.filter((r) => Array.isArray(r));
        if (matrix.length) {
          matrix.flat().forEach((r) => {
            if (r.failed_rows) {
              const failedRows = JSON.parse(r.failed_rows)[0];
              const message = failedRows.rows
                ? `Failed to create ${
                    failedRows.rows.split(",").length
                  } item(s) (rows ${failedRows.rows
                    .split(",")
                    .join(", ")}) due to: ${failedRows.reason}.`
                : r.message;
              (testStore || store).dispatch(
                notify({
                  id: Date.now(),
                  type: "ERROR",
                  message,
                })
              );
            } else if (
              r.total_items_created !== undefined &&
              r.total_items_created > 0
            ) {
              (testStore || store).dispatch(
                notify({
                  id: Date.now(),
                  type: "SUCCESS",
                  message: `Successfully created ${r.total_items_created} item(s).`,
                })
              );
            }
          });
        } else if (
          res.data &&
          ((res.data[0] &&
            res.data[0].state &&
            ["error", "warning"].includes(res.data[0].state)) ||
            (res.data.state && ["error", "warning"].includes(res.data.state)))
        ) {
          // eslint-disable-next-line no-throw-literal
          throw {
            response: {
              data: {
                error: {
                  status: 400,
                  message: (res.data[0] || res.data).message,
                },
              },
            },
          };
        } else if (successMessage) {
          handleSuccessMessage();
        }
      } else if (
        res.data &&
        ((res.data[0] &&
          res.data[0].state &&
          ["error", "warning"].includes(res.data[0].state)) ||
          (res.data.state && ["error", "warning"].includes(res.data.state)))
      ) {
        // eslint-disable-next-line no-throw-literal
        throw {
          response: {
            data: {
              error: {
                status: 400,
                message: (res.data[0] || res.data).message,
              },
            },
          },
        };
      } else if (successMessage) {
        handleSuccessMessage();
      }

      return res.data;
    })
    .catch((err) => {
      if (!disableLoadingSpinner)
        (testStore || store).dispatch(handleDequeueSpinner(spinnerId));

      // CAN'T TEST THIS KICKOUT SECTION
      // NODEJS DOESN'T KNOW WHAT DOCUMENT OR WINDOW ARE
      if (
        err.response &&
        err.response.data &&
        err.response.data.error &&
        err.response.data.error.status === 401
      ) {
        document.cookie = document.cookie
          .split("; ")
          .filter((c) => !/^PHPSESSID/.test(c))
          .join("; ");
        return window.location.assign(`${process.env.REACT_APP_LOGIN}`);
      }

      // @todo update the issue here...
      if (customErrorHandler) {
        customErrorHandler(err.response.data, (message) =>
          (testStore || store).dispatch(
            notify({ id: Date.now(), type: "ERROR", message })
          )
        );
      } else if (
        typeof disableErrorAlert === "function"
          ? !disableErrorAlert(err.response && err.response.data)
          : !disableErrorAlert
      ) {
        // @todo where the error is currently being caught
        (testStore || store).dispatch(
          notify({
            id: Date.now(),
            type: "ERROR",
            message:
              err.response && err.response.data
                ? err.response.data.error
                  ? err.response.data.error.message
                  : err.response.data.message
                : "Error completing action",
          })
        );
      }

      return err.response && err.response.data
        ? err.response.data.error
          ? err.response.data
          : { error: err.response.data }
        : { error: { status: 500, message: "Error completing action" } };
    });
};

export const useApiCallv2 = (testStore) => {
  const { token } = store.getState().profileData;
  const tokenToUse = token || process.env.REACT_APP_TEST_TOKEN;
  const decodedToken = tokenToUse ? jwt.decode(tokenToUse).data : null;
  const user_id = decodedToken ? decodedToken.id : null;
  const system_features = decodedToken ? decodedToken.features : [];
  // const apiCall = axiosApiCall(tokenToUse, testStore);
  const apiCall = axiosApiCallv2(tokenToUse, testStore);

  return { apiCall, tokenToUse, user_id, system_features };
};

export const generateIcon = (item, nav = false) => {
  switch (item.type) {
    case "ICOMOON":
      return (
        <i
          className={item.className}
          style={
            nav
              ? item.subsection
                ? { paddingTop: "calc(22.5px - 10px)" }
                : { alignSelf: "center" }
              : {}
          }
        />
      );
    case "LETTER":
      return (
        <span
          className={item.className}
          style={
            nav
              ? item.subsection
                ? { paddingTop: "calc(22.5px - 10px)" }
                : { alignSelf: "center" }
              : {}
          }
        >
          {item.icon}
        </span>
      );
    case "ICON":
      return (
        <FontAwesomeIcon
          className={item.className}
          style={
            nav
              ? item.subsection
                ? { paddingTop: "calc(22.5px - 10px)" }
                : { alignSelf: "center" }
              : {}
          }
          icon={item.icon}
        />
      );
    case "SVG":
      return (
        <svg
          className={item.className}
          xmlns="http://www.w3.org/2000/svg"
          xmlnsXlink="http://www.w3.org/1999/xlink"
          viewBox={item.icon.viewBox}
          style={
            nav
              ? item.subsection
                ? { paddingTop: "calc(22.5px - 10px)" }
                : { alignSelf: "center" }
              : {}
          }
        >
          {item.icon.paths.map((p, idx) => {
            if (typeof p === "string") return <path key={idx + p} d={p} />;
            if (typeof p === "object")
              return <path key={p.id + p.d} id={p.id} d={p.d} />;
            return "";
          })}
        </svg>
      );
    default:
      return "";
  }
};

// @todo this does not account for browsers passing in a time that is not in America/New_York time already
export const generateTime = (
  time,
  formatAsTime,
  withOffset = false,
  sign = "+",
  testStore = null,
  preventFormat = false
) => {
  const { systemSettings } = (testStore || store).getState().profileData;
  let format = null;
  if (formatAsTime) format = "hh:mm A";
  if (!formatAsTime && systemSettings && systemSettings.date_display)
    format = systemSettings.date_display;
  if (!formatAsTime && !format) format = "MM-DD-YYYY";
  const timezone = systemSettings ? systemSettings.timezone : null;
  // use given time to account for daylight savings correctly
  const utcOffset = timezone
    ? (moment.tz(timezone).utcOffset() -
        moment.tz("America/New_York").utcOffset()) *
      60 *
      1000
    : 0;

  const result =
    !preventFormat && format
      ? moment(
          time + (withOffset ? (sign === "+" ? utcOffset : -utcOffset) : 0)
        ).format(format)
      : moment(
          time + (withOffset ? (sign === "+" ? utcOffset : -utcOffset) : 0)
        );
  return result;
};

/**
 * Why v2? The original sproc assumes that the client is passing in the
 * time already in the server timezone (America/New_York).
 *
 * When we are parsing a TIME, we want to make sure it is specific to the timezone.
 * When we are parsing a DATE, we want to make sure it is matching the server's date.
 * When we are preventing formatting, we will just push back a generic time in the expected format.
 *
 * @param {*} utcTimestamp UNIX_TIMESTAMP * 1000 or the Date().getTime() output
 * @param {*} formatAsTime
 * @param {*} withOffset
 * @param {*} sign
 * @param {*} testStore
 * @param {*} preventFormat
 * @returns
 */
export const generateTimev2 = (
  utcTimestamp,
  formatAsTime,
  withOffset = false,
  sign = "+",
  testStore = null,
  preventFormat = false
) => {
  const { systemSettings } = (testStore || store).getState().profileData;
  let format = null;
  if (formatAsTime) format = "hh:mm A";
  if (!formatAsTime && systemSettings && systemSettings.date_display)
    format = systemSettings.date_display;
  if (!formatAsTime && !format) format = "MM-DD-YYYY";
  const timezone = systemSettings ? systemSettings.timezone : null;
  // use given time to account for daylight savings correctly
  const utcOffset = timezone
    ? (moment.tz(timezone).utcOffset() -
        moment.tz("America/New_York").utcOffset()) *
      60 *
      1000
    : 0;

  // convert UTC to EST if we aren't preventing formatting
  const universalTime = !preventFormat
    ? new Date(utcTimestamp).toLocaleString("en-US", {
        timeZone: "America/New_York",
      })
    : new Date(utcTimestamp);
  let time = new Date(universalTime).getTime();

  const result =
    !preventFormat && format
      ? moment(
          time + (withOffset ? (sign === "+" ? utcOffset : -utcOffset) : 0)
        ).format(format)
      : moment(
          time + (withOffset ? (sign === "+" ? utcOffset : -utcOffset) : 0)
        );

  return result;
};

// convert timezones using Intl - date is stored as EDT in server, we display as EDT date...
export const formatDate = (time, toServerUnixTime = false) => {
  // if we were using luxon instead of moment to format...
  let format = "LL-dd-yyyy"; //systemSettings.date_display not formatted for the new tool...
  let formatted;

  if (toServerUnixTime) {
    // Need to convert from UTC to EST date since our server stores as DATETIME and we want them to return as midnight dates, sigh
    // Need to update database to store dates as DATE and not DATETIME...
    // converts the date to the server timezone (if server tz is utc we can remove)
    const offset =
      DateTime.local().offset -
      DateTime.now().setZone(SystemSettings.SERVER_TIMEZONE).offset;
    formatted =
      new Date(new Date(time).getTime() + offset * 60 * 1000).getTime() / 1000;

    // this is utc and we convert to the local timezone - no offset needed
    // default format is 10/31/2024 so we will replace 10-31-2024 to match our system default
    // formatted = Intl.DateTimeFormat('en-US', {
    //   timeZone: SystemSettings.SERVER_TIMEZONE,
    // }).format(time * 1000).replaceAll("/", "-");
  } else {
    // expect incoming unixtime... (not millseconds)
    const toFormat =
      typeof time == "number" ? time : new Date(time).getTime() / 1000 ?? null;
    formatted = toFormat
      ? DateTime.fromSeconds(toFormat)
          .setZone(SystemSettings.SERVER_TIMEZONE)
          .toFormat(format)
      : "";
  }

  return formatted;
};

export const permissionLock = (data, testStore) => {
  const { permissions } = (testStore || store).getState().profileData;

  if (!data?.length) return [];

  const permissionLockedData = data
    .filter((d) => {
      if (!d.permissions) return true;
      if (!permissions) return false;
      for (let i = 0; i < d.permissions.length; i++) {
        if (!permissions.includes(d.permissions[i])) return false;
      }
      return true;
    })
    .map((d) => {
      let newObj = d;
      if (newObj.permissions) delete newObj.permissions;
      return newObj;
    });

  return permissionLockedData;
};

export const canDrop = (monitor, dropOrigin, blackListedOrigins = []) => {
  const item = monitor.getItem();

  return (
    item &&
    item.dropOrigin !== dropOrigin &&
    !blackListedOrigins.includes(item.dropOrigin) &&
    monitor.canDrop()
  );
};

export const timeFormatter = (value) => {
  let formattedTime = value.toString().split(".");
  formattedTime[1] = parseFloat(`0.${formattedTime[1]}`);
  formattedTime[1] *= 60;
  formattedTime[1] = Math.floor(formattedTime[1]);
  if (formattedTime[1] < 10) formattedTime[1] = `0${formattedTime[1]}`;

  return formattedTime.join(":");
};

export function convertHex(hex) {
  hex = hex.replace("#", "");
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return "rgba(" + r + "," + g + "," + b + ", 0.5)";
}

export const rowClassRules = {
  "--custom-grid-odd": (params) => params.node.rowIndex % 2 === 1,
  "--custom-grid-even": (params) => params.node.rowIndex % 2 === 0,
};

export const getRowHeight = (params) => {
  if (params.node.detail) return 100;
  return 60;
};

export const onSortChanged = (params) => {
  params.api.redrawRows();
};

export const generateKpis = (users) => {
  let totalExperience = 0;
  let totalLaborRate = 0;

  for (let i = 0; i < users.length; i++) {
    let { experience_years, labor_rate } = users[i];

    if (experience_years) totalExperience += experience_years;
    if (labor_rate) totalLaborRate += labor_rate;
  }

  const kpis = [
    {
      name: "Users Assigned",
      value: users.length,
      icon: faUsers,
      selected: true,
    },
    {
      name: "Average Experience",
      value: `${
        totalExperience > 0 ? Math.round(totalExperience / users.length) : 0
      } years`,
      icon: faUser,
      selected: true,
    },
    {
      name: "Average Wage",
      value: (totalLaborRate > 0 ? totalLaborRate / users.length : 0).toFixed(
        2
      ),
      icon: faDollarSign,
      selected: true,
    },
  ];

  return kpis;
};

export const convertStringToUnix = (string) => {
  const date = moment(string).unix();
  return date;
};

export const validateDateStringFormat = (dateStr) => {
  const months31Days = ["01", "03", "05", "07", ""],
    months30Days = ["04", "06", "09", "11"],
    month = dateStr.match(/^(0[1-9]|1[012])(?=[- /.])/);

  if (!month) return false;

  const dayRegex = months31Days.includes(month[0])
      ? "(0[1-9]|[12][0-9]|3[01])"
      : months30Days.includes(month[0])
      ? "(0[1-9]|[12][0-9]|30)"
      : "(0[1-9]|[12][0-9])",
    dateRegex = new RegExp(
      `^(0[1-9]|1[012])[- /.]${dayRegex}[- /.](19|20)\\d\\d$`,
      "g"
    );

  return dateRegex.test(dateStr);
};

export const populateDropdownTimes = (lastShift) => {
  let options = [];

  for (let i = 1; i <= 4; i++) {
    let newOption = {
      value: lastShift.shift_end * 1000 + 1000 * 15 * i * 60,
      display: generateTime(
        lastShift.shift_end * 1000 + 15 * i * 60 * 1000,
        true
      ),
    };
    options.push(newOption);
  }

  return options;
};

export const checkPreviousShiftTime = (shiftEnd) => {
  let now = moment();
  let f_shiftEnd = moment.unix(shiftEnd);

  let hours = f_shiftEnd.diff(now, "hours");
  hours = Math.abs(hours);
  return hours > 4 ? false : true;
};

export const buildCSV = (headers, data) => {
  if (!headers && !data) return;

  let csvContent = "";

  if (headers) {
    csvContent += headers.join(",") + "\r\n";
  }

  if (data) {
    data.forEach((row) => {
      csvContent += row.join(",") + "\r\n";
    });
  }

  return "data:text/csv;charset=utf-8," + encodeURIComponent(csvContent);
};

export const titleize = (str) => {
  if (!str) return "";
  const replacedString = str.split("_").join(" ");
  const splitString = replacedString.split(" ");
  const mappedString = splitString
    .map((s) => s.slice(0, 1).toUpperCase() + s.slice(1).toLowerCase())
    .join(" ");
  return mappedString;
};

export const transformPriorityToRank = (drawings) => {
  return drawings.sort((a, b) => a.priority - b.priority);
};

export const transformDrawingPriorityToRank = (items) => {
  // remove duplicate drawing ids for items, then map to drawing priority
  let uniquePriorityItems = items
    .filter(
      (value, index, array) =>
        array.findIndex((o) => o?.drawing_id === value?.drawing_id) === index
    )
    .map((item) => ({
      id: item?.drawing_id,
      drawing_priority: item?.drawing_priority,
    }))
    .sort((a, b) => a?.drawing_priority - b?.drawing_priority);

  return uniquePriorityItems.map((item, index) => ({
    ...item,
    drawing_priority: index + 1,
  }));
};

export const hashUnitInterval = (seed) => {
  // seedrandom
  return seedrandom.xor4096(seed)();
};

export const escapeRegExp = (text) => {
  return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&");
};

export const table = (type) => {
  switch (type) {
    case "JOBS":
      return "jobs";
    case "PACKAGES":
      return "packages";
    case "DRAWINGS":
      return "drawings";
    case "ITEMS":
      return "work_stages";
    case "ASSIGNED_DRAWINGS":
      return "assigned_drawings";
    case "MY_WORK_ITEMS":
      return "my_work_items";
    case "WIZARD_ITEMS":
      return "wizard_items";
    case "PACKAGES_PENDING_APPROVAL":
      return "packages_pending_approval";
    case "PACKAGES_SENT_BACK":
      return "packages_sent_back";
    case "DRAWINGS_PENDING_APPROVAL":
      return "drawings_pending_approval";
    case "DRAWINGS_SENT_BACK":
      return "drawings_sent_back";
    case "BOM":
      return "bom";
    default:
      return "";
  }
};

export const onDisplayedColumnsChanged = (
  tableType,
  params,
  stageIds = null,
  setGroupingColumnOptions = null,
  isGrouped,
  testStore = null
) => {
  let currentSavedColumnState;
  let canSaveColumnState = true;

  if (tableType === "MY_WORK_ITEMS" && isGrouped) return;
  let allColumns = params.columnApi.getAllColumns();

  if (setGroupingColumnOptions) setGroupingColumnOptions(allColumns);
  let newColumnState = allColumns
    .sort((a, b) => a.left - b.left)
    .map((c, idx) => ({
      header_name: c.colDef.headerName,
      pinned: c.pinned,
      visible: c.visible ? 1 : 0,
    }))
    .filter((c) => c.header_name !== "");

  switch (tableType) {
    case "JOBS":
      currentSavedColumnState = (testStore || store).getState().jobsData
        .savedColumnState;
      break;
    case "PACKAGES":
      currentSavedColumnState = (testStore || store).getState().packagesData
        .savedColumnState;
      break;
    case "DRAWINGS":
      currentSavedColumnState = (testStore || store).getState().drawingsData
        .savedColumnState;
      break;
    case "ITEMS":
      currentSavedColumnState = (testStore || store).getState().itemsData
        .savedColumnState;
      break;
    case "MY_WORK_ITEMS":
      currentSavedColumnState = (testStore || store).getState().myWorkData
        .itemsMenuColumnState;
      break;
    case "ASSIGNED_DRAWINGS":
      currentSavedColumnState = (testStore || store).getState().myWorkData
        .assignedDrawingsColumnState;
      break;
    case "WIZARD_ITEMS":
      currentSavedColumnState = (testStore || store).getState().wizardData
        .wizardItemsColumnState;
      break;
    case "PACKAGES_PENDING_APPROVAL":
      currentSavedColumnState = (testStore || store).getState()
        .packagesPendingApprovalData.savedColumnState;
      break;
    case "PACKAGES_SENT_BACK":
      currentSavedColumnState = (testStore || store).getState()
        .packagesPendingApprovalData.savedColumnState;
      break;
    case "DRAWINGS_PENDING_APPROVAL":
      currentSavedColumnState = (testStore || store).getState()
        .drawingsPendingApprovalData.savedColumnState;
      break;
    case "DRAWINGS_SENT_BACK":
      currentSavedColumnState = (testStore || store).getState()
        .drawingsPendingApprovalData.savedColumnState;
      break;
    default:
      return;
  }

  if (currentSavedColumnState) {
    // compare states with the position prop
    const newColumnStateWithPos = newColumnState.map((c, idx) => ({
      ...c,
      position: idx,
    }));
    canSaveColumnState = !deepArraysAreEqual(
      currentSavedColumnState,
      newColumnStateWithPos
    );
  }

  if (!canSaveColumnState) {
    return;
  }
  return (testStore || store).dispatch(
    handleSaveColumnState(tableType, newColumnState, stageIds)
  );
};

export const reduce = (numerator, denominator) => {
  let gcd = (a, b) => {
    return b ? gcd(b, a % b) : a;
  };
  gcd = gcd(numerator, denominator);
  return [numerator / gcd, denominator / gcd];
};
export const simpleFrac = ({ whole, num, den }) => {
  const NUM_GT_DEN = num > den;
  const NEW_NUM = NUM_GT_DEN ? num % den : num;
  const NEW_WHOLE = NUM_GT_DEN ? whole + Math.trunc(num / den) : whole;

  const NEW_FRAC = reduce(NEW_NUM, den);
  let REDUCED_NUM = NEW_FRAC[0];
  let REDUCED_DEN = NEW_FRAC[1];

  if (REDUCED_NUM === 0) {
    REDUCED_NUM = null;
    REDUCED_DEN = null;
  }

  return {
    whole: NEW_WHOLE,
    num: REDUCED_NUM,
    den: REDUCED_DEN,
  };
};
export const whiteSpaceToSingleSpace = (str) => str.replace(/\s+(?=\s)/g, "");
export const spacesAndDigits = (str) => str.replace(/[^0-9\s]+/g, "");
export const sanitize = (v) => whiteSpaceToSingleSpace(spacesAndDigits(v));
export const lengthParse = (val) => {
  const SANITIZED = sanitize((val + "").trim());
  const FEET = "′";
  const INCHES = "″";
  const DASH = "–";
  const SLASH = "/";
  const TOKENS = SANITIZED.split(" ").filter((s) => s !== "");
  let NUM_TOKENS = TOKENS.length;
  const IN_IN_FT = 12;
  const ret = {
    display: SANITIZED,
    value: 0,
  };

  if (NUM_TOKENS === 0) {
    return ret;
  }
  let FT = TOKENS[0];
  let IN = TOKENS[1];
  let NUM = TOKENS[2];
  let DEN = TOKENS[3];

  if (
    NUM !== undefined &&
    DEN !== undefined &&
    parseInt(NUM) > 0 &&
    parseInt(DEN) > 0
  ) {
    const SIMPLIFIED = simpleFrac({
      whole: parseInt(IN),
      num: parseInt(NUM),
      den: parseInt(DEN),
    });

    IN = SIMPLIFIED.whole + "";
    if (SIMPLIFIED.num !== null) {
      NUM = SIMPLIFIED.num + "";
    }
    if (SIMPLIFIED.den !== null) {
      DEN = SIMPLIFIED.den + "";
    }

    if (NUM === "1" && DEN === "1") {
      NUM = "";
      DEN = "";
      IN = parseInt(IN) + 1 + "";
    }
  }
  if (IN !== undefined && parseInt(IN) >= IN_IN_FT) {
    FT = "" + (parseInt(FT) + Math.trunc(parseInt(IN) / IN_IN_FT));
    IN = "" + (parseInt(IN) % IN_IN_FT);
  }
  NUM_TOKENS = 0;
  FT && NUM_TOKENS++;
  IN && NUM_TOKENS++;
  NUM && parseInt(NUM) > 0 && NUM_TOKENS++;
  DEN && parseInt(DEN) > 0 && NUM_TOKENS++;

  switch (true) {
    case NUM_TOKENS === 1:
      ret.value = parseInt(FT) * 12;
      ret.display = `${FT}${FEET}`;
      break;

    case NUM_TOKENS === 2:
      ret.value = parseInt(FT) * 12 + parseInt(IN);
      ret.display = `${FT}${FEET}${DASH}${IN}${INCHES}`;
      break;

    case NUM_TOKENS === 3:
      ret.value = parseInt(FT) * 12 + parseInt(IN); // frac in ignored
      ret.display = `${FT}${FEET}${DASH}${IN} ${NUM}${SLASH}${INCHES}`;
      break;

    case NUM_TOKENS >= 4:
      ret.value = parseInt(FT) * 12 + (parseInt(IN) + NUM / DEN);
      ret.display = `${FT}${FEET}${DASH}${IN} ${NUM}${SLASH}${DEN}${INCHES}`;
      break;

    default:
      return;
  }
  return ret;
};

const SNAP_IN_FRAC = 16;

// display length
export const DISPLAY_LENGTH = (
  ABS_LEN_FT,
  precision,
  roundToNearest = false
) => {
  let denom = precision ? precision : SNAP_IN_FRAC;
  let snapInch = 1 / denom;
  const INCHES_IN_FOOT = 12;
  const SNAP_FT = snapInch / INCHES_IN_FOOT;
  const WHOLE_FT = Math.trunc(ABS_LEN_FT);
  const FRAC_FT = ABS_LEN_FT - WHOLE_FT;
  const SNAPS_IN_FRAC_FT = FRAC_FT / SNAP_FT;
  const IN = SNAPS_IN_FRAC_FT / denom;
  const WHOLE_IN = Math.trunc(IN);
  const SNAPS_IN_FRAC_IN = (IN - WHOLE_IN) / snapInch;
  const WHOLE_SNAPS_IN_FRAC_IN = Math.trunc(
    roundToNearest ? Math.round(SNAPS_IN_FRAC_IN) : SNAPS_IN_FRAC_IN
  );
  const IN_NUM = WHOLE_SNAPS_IN_FRAC_IN;
  const IN_DEN = denom;
  const { whole: NEW_WHOLE_IN, num: NEW_NUM_IN, den: NEW_DEN_IN } = simpleFrac({
    whole: WHOLE_IN,
    num: IN_NUM,
    den: IN_DEN,
  });
  let isNegative =
    WHOLE_FT < 0 || NEW_WHOLE_IN < 0 || NEW_NUM_IN < 0 || NEW_DEN_IN < 0;

  const DIRTY = `${isNegative ? "-" : ""}${Math.abs(WHOLE_FT)}′-${Math.abs(
    NEW_WHOLE_IN
  )} ${Math.abs(NEW_NUM_IN)}/${Math.abs(NEW_DEN_IN)}″`;

  const CLEAN = DIRTY.replace("s0′-", "")
    .replace(" null/null", "")
    .replace("-0″", "")
    .replace("0 ", "")
    .replace(" 0/0", "")
    .replace("0/0", "0");

  return CLEAN;
};

/* NESTING */
// get string display for machine length
export const NESTING_LENGTH_DISPLAY = (totalInches) => {
  let WHOLE = Math.trunc(totalInches);
  let REMAINDER = totalInches - WHOLE;
  let FEET = WHOLE > 12 ? Math.trunc(WHOLE / 12) : 0;
  let INCHES = WHOLE - FEET * 12;
  let FRACTION = math
    .format(math.fraction(REMAINDER), { fraction: "ratio" })
    .replace("/", " ");
  const DIRTY = `${FEET} ${INCHES} ${REMAINDER > 0 ? FRACTION : "0  "}`;
  const CLEAN =
    INCHES && INCHES > 0
      ? DIRTY.replace(" null/null", "")
          .replace(" 0 ", "")
          .replace(" 0  ", "")
          .replace(" 0/0", "")
          .replace("0/0", "0")
      : DIRTY.replace(" null/null", "")
          .replace(" 0  ", "")
          .replace(" 0/0", "")
          .replace("0/0", "0");
  return CLEAN;
};

// generate obj, keys for length and value for count
export const getUniqueCounts = (cuts) => {
  let uniqueObj = {};
  for (let c in cuts) {
    // treated as a string, so pass it as one
    if (!Object.keys(uniqueObj).includes(`${cuts[c].length.decimal}`)) {
      uniqueObj[cuts[c].length.decimal] = 1;
    } else {
      uniqueObj[cuts[c].length.decimal] += 1;
    }
  }
  return uniqueObj;
};

export const nestCuts = (cuts, nestSettings, isTest = false) => {
  // calc min length to use, use tail if set and >= machine min
  let calcdMinLimit =
    nestSettings.shouldCutTail &&
    nestSettings.tailCutLength >= nestSettings.minLimit
      ? nestSettings.tailCutLength
      : nestSettings.minLimit;

  let calcdStockLength = nestSettings.stockLength;

  // reduce available stock length based on head and tail, head also needs to include kerf
  if (nestSettings.shouldCutHead) {
    calcdStockLength -= nestSettings.headCutLength + nestSettings.kerf;
  }
  if (nestSettings.shouldCutTail) {
    calcdStockLength -= calcdMinLimit;
  }

  // filter out cuts that are too big/small and sort smalllest to largest
  let filteredCuts = cuts
    .filter(
      (c) =>
        c.is_cut &&
        c.length.decimal < calcdStockLength &&
        c.length.decimal > nestSettings.minLength
    )
    .sort((a, b) => b.length.decimal - a.length.decimal);

  let allSettings = {
    ...nestSettings,
    calcdStockLength,
    calcdMinLimit,
  };

  return generateNests(filteredCuts, allSettings, isTest);
};

const sumTotalLength = (cuts, nestSettings) => {
  let totalCutLength = 0;
  for (let i = 0; i < cuts.length; i++) {
    totalCutLength += cuts[i].length.decimal + nestSettings.kerf;
  }
  return totalCutLength;
};

// sort longest to shortest before calling
const generateNests = (cuts, nestSettings, isTest) => {
  // each stick of pipe corresponds to a nest
  let sticksOfPipe = 0;
  let totalCutLength = sumTotalLength(cuts, nestSettings);
  let nestedArrays = [];

  // use total cut length to know # sticks of pipe needed, use user entered stock length
  sticksOfPipe = Math.ceil(totalCutLength / nestSettings.calcdStockLength);

  // if all cuts are small and trigger minlimit check
  // need to have cap on loop to not exceed # of cuts
  const maxSticksOfPipe = cuts.length;

  // loop over each stick of pipe and add largest cut as new object
  for (let i = 0; i < sticksOfPipe; i++) {
    // exit if no available cuts left
    if (!cuts.length) continue;

    // not cutting tail so need to add scrap to account for min limit if largest cut < min limit
    if (
      cuts[0].length.decimal <= nestSettings.minLimit &&
      !nestSettings.shouldCutTail
    ) {
      nestedArrays.push({
        cuts: [cuts[0]],
        totalLength: cuts[0].length.decimal + nestSettings.kerf,
        scrap:
          nestSettings.calcdStockLength -
          (cuts[0].length.decimal + nestSettings.kerf),
      });
      cuts.splice(0, 1);

      // update total cut length and update estimated sticks of pipe if needed
      totalCutLength += nestSettings.minLimit;
      if (
        totalCutLength / nestSettings.calcdStockLength > sticksOfPipe &&
        sticksOfPipe < maxSticksOfPipe
      ) {
        sticksOfPipe += 1;
      }
    } else {
      nestedArrays.push({
        cuts: [cuts[0]],
        totalLength: cuts[0].length.decimal + nestSettings.kerf,
        scrap:
          nestSettings.calcdStockLength -
          (cuts[0].length.decimal + nestSettings.kerf),
      });
      cuts.splice(0, 1);
    }
  }

  let newTotalLength = 0;
  // starting with largest cut, fill up each stick of pipe with cuts
  for (let i = 0; i < cuts.length; i++) {
    loop2: for (let j = 0; j < sticksOfPipe; j++) {
      newTotalLength =
        nestedArrays[j].totalLength +
        cuts[i].length.decimal +
        nestSettings.kerf;

      // if this cut fits in a nest, add it and move to next cut
      if (newTotalLength <= nestSettings.calcdStockLength) {
        nestedArrays[j].cuts.push(cuts[i]);
        nestedArrays[j] = {
          ...nestedArrays[j],
          totalLength: newTotalLength,
          scrap:
            nestedArrays[j].scrap -
            (cuts[i].length.decimal + nestSettings.kerf),
        };

        break loop2;
      }
    }
  }

  if (!nestedArrays.length) return [];

  nestedArrays = nestedArrays.sort((a, b) => a.scrap - b.scrap);
  let nestedCutList = nestedArrays[0].cuts.slice(0);
  // sort cuts smallest to largest
  nestedCutList = nestedCutList.sort(
    (a, b) => a.length.decimal - b.length.decimal
  );

  if (nestedCutList.length > 0) {
    // fake head cut handling
    if (nestSettings.shouldCutHead) {
      const fakeHeadCut = {
        ...nestedCutList[0],
        id: -1,
        _row_press_id: 0,
        drawing_name: "Head Cut",
        drawing_id: 0,
        tag_number: null,

        converted_length: DISPLAY_LENGTH(nestSettings.headCutLength / 12),
        length_feet: 0,
        length_inches: nestSettings.headCutLength,
        abs_length_feet: nestSettings.headCutLength / 12,
        drawing_priority: 0,
        identifier: null,
        length: {
          decimal: nestSettings.headCutLength,
          display: DISPLAY_LENGTH(nestSettings.headCutLength / 12),
        },
        is_cut: 1,
      };
      nestedCutList.unshift(fakeHeadCut);
    }
    // fake tail handling
    if (nestSettings.shouldCutTail) {
      let priority = 1;
      nestedCutList.forEach((c) => {
        if (c.drawing_priority ?? 0 > priority)
          priority = c.drawing_priority ?? 0;
      });
      const fakeTailCut = {
        ...nestedCutList[0],
        id: -2,
        _row_press_id: 0,
        drawing_name: "Tail Cut",
        drawing_id: 0,
        tag_number: null,

        converted_length: DISPLAY_LENGTH(nestSettings.tailCutLength / 12),
        length_feet: 0,
        length_inches: nestSettings.tailCutLength,
        abs_length_feet: nestSettings.tailCutLength / 12,
        drawing_priority: priority + 1,
        identifier: null,
        length: {
          decimal: nestSettings.tailCutLength,
          display: DISPLAY_LENGTH(nestSettings.tailCutLength / 12),
        },
        is_completed: 1,
        is_cut: 1,
      };
      nestedCutList.push(fakeTailCut);
    }
  }
  return !isTest ? nestedCutList : nestedArrays;
};

export const decimalRegex = /^\d{0,}\.?\d{1,}$/;
export const fractionalRegex = /^\d{0,}("|'|(\s?|-)\d{1,}(\s?|-)\d{0,}(\/[1-9]{1,})?"|'(\s?|-)\d{1,}(\s?|-)\d{0,}(\/[1-9]{1,})?"?)$/;
export const multidimensionRegex = /^\d{0,}(("|'|\s|-)?\d{0,}(\s?|-)\d{0,}(\/[1-9]{1,})?("|'\s|-)?\d{1,}(\s?|-)\d{0,}(\/[1-9]{1,})?"?)|((\.\d{1,}("|')?))?(\s?[x-]\s?\d{0,}(("|'|(\s|-)?\d{1,}(\s?|-)\d{0,}(\/[1-9]{1,})?("|'|\s|-)?\d{0,}(\s?|-)\d{0,}(\/[1-9]{1,})|(\.\d{1,}("|')?))?"?)?){0,2}$/;
export const multidimensionOnlyX = /[^a-wy-z]/i;

export const convertFracToDec = (fr) => {
  let splitFr;
  let result = 0;
  if (/'/.test(fr)) splitFr = fr.split("'").map((s) => s.trim());
  else if (/^\d{0,}\s?\d{0,}\/?\d{1,}"$/.test(fr))
    splitFr = fr.split(" ").map((s) => s.trim());
  else if (/^\d{0,}[-]?\d{0,}\/?\d{1,}"$/.test(fr))
    splitFr = fr.split("-").map((s) => s.trim());

  if (splitFr && splitFr.length === 2 && /'/.test(fr)) {
    splitFr[1] = splitFr[1].replace(/["-]/g, " ").trim();

    splitFr = splitFr.filter((f) => f !== "");
    if (splitFr.length === 2) {
      if (/\//.test(splitFr[1])) splitFr[1] = splitFr[1].split("/");
      if (/^\d{1,}\s\d{1,}$/.test(splitFr[1][0])) {
        splitFr[1][0] = splitFr[1][0].split(" ").map((s) => parseInt(s));
        splitFr[1][0] =
          splitFr[1][0][0] * parseInt(splitFr[1][1]) + splitFr[1][0][1];
      }

      if (Array.isArray(splitFr[1]))
        splitFr[1] = parseInt(splitFr[1][0]) / parseInt(splitFr[1][1]);
      else splitFr[1] = parseInt(splitFr[1]);
    }
    splitFr[0] = parseInt(splitFr[0]) * 12;

    splitFr.forEach((s) => (result += parseFloat(s)));
  } else if (
    splitFr &&
    splitFr.length === 1 &&
    /^\d{1,}\/\d{1,}"$/.test(splitFr[0])
  ) {
    splitFr[0] = splitFr[0].replace('"', "");
    if (/\//.test(splitFr[0])) splitFr[0] = splitFr[0].split("/");

    splitFr[0] =
      parseInt(splitFr[0][0]) /
      (splitFr[0][1] !== undefined ? parseInt(splitFr[0][1]) : 1);

    result = splitFr[0];
  } else if (splitFr && splitFr.length === 2) {
    splitFr[0] = parseInt(splitFr[0]);
    splitFr[1] = splitFr[1].replace('"', "").split("/");

    result =
      parseInt(splitFr[0]) + parseInt(splitFr[1][0]) / parseInt(splitFr[1][1]);
  } else if (splitFr && /^\d{1,}"$/.test(splitFr[0])) {
    result = parseInt(splitFr[0]);
  }

  return result;
};

export const dateStringToUnix = (dateStr) => {
  const unix = moment(dateStr).unix() * 1000;
  return unix;
};

export const objectColumns = [
  "length",
  "bend_deduct",
  "bend_dim_a",
  "bend_dim_b",
  "bend_dim_c",
  "bend_dim_d",
  "bend_dim_e",
  "bend_dim_f",
  "bend_dim_g",
  "bend_mark_1",
  "bend_mark_2",
];

// http://web.archive.org/web/20130826203933/http://my.opera.com/GreyWyvern/blog/show.dml/1671288
export const naturalSort = (a, b) => {
  const chunkify = (t) => {
    var tz = [],
      x = 0,
      y = -1,
      n = 0,
      i,
      j;

    while ((i = (j = t.charAt(x++)).charCodeAt(0))) {
      var m = i === 46 || (i >= 48 && i <= 57);
      if (m !== n) {
        tz[++y] = "";
        n = m;
      }
      tz[y] += j;
    }
    return tz;
  };

  var aa = chunkify(a.toLowerCase());
  var bb = chunkify(b.toLowerCase());

  for (let x = 0; aa[x] && bb[x]; x++) {
    if (aa[x] !== bb[x]) {
      var c = Number(aa[x]),
        d = Number(bb[x]);
      // eslint-disable-next-line eqeqeq
      if (c == aa[x] && d == bb[x]) {
        return c - d;
      } else return aa[x] > bb[x] ? 1 : -1;
    }
  }
  return aa.length - bb.length;
};

export const b64toBlob = (b64Data) => {
  return fetch(b64Data).then((res) => res.blob());
};

export const confirmPassword = (username, password) => {
  const { userId } = store.getState().profileData;
  const auth = {
    username,
    password,
  };
  return axios
    .get(`${process.env.REACT_APP_API}/auth/user`, { auth })
    .then((res) => {
      const decodedToken = res?.data ? jwt.decode(res.data) : null;
      if (!decodedToken) return false;

      return userId === decodedToken.data.id ? true : false;
    })
    .catch((err) => false);
};

export const validatePassword = (password) => {
  const { defaultPassword } = store.getState().profileData.systemSettings;

  // 1. Must be between 5-32 characters
  if (!(password.length >= 5 && password.length <= 32)) return false;

  // 2. Must NOT contain spaces, commas, single or double quotation marks, colons, or slashes (*,/':)
  // eslint-disable-next-line no-useless-escape
  const unavailableChars = /[*\/'":,\s]/;
  if (unavailableChars.test(password)) return false;

  // 3. Must not be default password (systemSettings.defaultPassword)
  if (password === defaultPassword) return false;

  return true;
};

export const customTextFilter = ({ filterOption, value, filterText, data }) => {
  const filterTextLowerCase = filterText.toLowerCase();
  const valueLowerCase = value.toString().toLowerCase();

  // display row if empty
  if (data.empty) return true;
  switch (filterOption) {
    case "contains":
      return valueLowerCase.indexOf(filterTextLowerCase) >= 0;
    case "notContains":
      return valueLowerCase.indexOf(filterTextLowerCase) === -1;
    case "equals":
      return valueLowerCase === filterTextLowerCase;
    case "notEqual":
      return valueLowerCase !== filterTextLowerCase;
    case "startsWith":
      return valueLowerCase.indexOf(filterTextLowerCase) === 0;
    case "endsWith":
      var index = valueLowerCase.lastIndexOf(filterTextLowerCase);
      return (
        index >= 0 &&
        index === valueLowerCase.length - filterTextLowerCase.length
      );
    default:
      // should never happen
      console.warn("invalid filter type " + filterOption);
      return false;
  }
};

// takes in a previous set of ids and a current set of ids
// determines if new selections have been made
export const checkForNewTableFilterSelections = (prevIds, currentIds) => {
  for (let i = 0; i < currentIds.length; i++) {
    if (!prevIds.includes(currentIds[i])) return true;
  }

  return false;
};

export const objectColumnDefs = {
  filterValueGetter: (field) => (params) => {
    return params.data && params.data[field] ? params.data[field].display : "";
  },
  valueFormatter: (field) => (params) => {
    if (!params.data) return;
    return params.data[field]
      ? params.data[field].decimal || field === "length"
        ? params.data[field].display
        : "-"
      : "";
  },
  getQuickFilterText: (field) => (params) => {
    if (params.data[field]) {
      return params.data[field].display;
    } else return 0;
  },
  comparator: (field) => (valueA, valueB, nodeA, nodeB) => {
    const nodeAValue = nodeA.data[field] ? nodeA.data[field].decimal : 0;
    const nodeBValue = nodeB.data[field] ? nodeB.data[field].decimal : 0;
    return nodeAValue - nodeBValue;
  },
};

// NOT CERTAIN THIS CAN BE UNIT TESTED BECAUSE IS DOWNLOADS FILES
/**
 * Downloads files based on the property and file download name/extension
 * Was only used for MAJ files... now deprecated.
 * @param {*} selectedEntities Array of objects
 * @param {*} file_property Object property with file link
 * @param {*} filename_property File name for download
 * @param {*} file_extension File extension for download
 * @param {*} testStore For testing
 */
export const downloadFiles = (
  selectedEntities,
  file_property,
  filename_property,
  file_extension,
  testStore = null
) => {
  if (
    selectedEntities.length &&
    selectedEntities.some((e) => e[file_property])
  ) {
    selectedEntities
      .filter((e) => e[file_property])
      .forEach((e) => {
        let tempLink = document.createElement("a");
        tempLink.download = e[filename_property] + file_extension;
        tempLink.href = e[file_property];
        tempLink.style.display = "none";
        document.body.appendChild(tempLink);
        tempLink.click();
        document.body.removeChild(tempLink);
      });
  } else {
    (testStore || store).dispatch(
      notify({
        id: Date.now(),
        type: "ERROR",
        message: "No files to download.",
      })
    );
  }
};

/**
 * Iterate through links and "open" in new tab for download
 * If the file type is application/octet-stream then it will download immediately...
 * Note - we don't use v1 here bc we are OK downloading with the same file name as was uploaded...
 *
 * @param {*} selectedEntities array of objects
 * @param {*} file_property url property of object
 * @param {*} testStore for testing
 */
export const downloadFilesV2 = (
  selectedEntities,
  file_property,
  testStore = null
) => {
  if (
    selectedEntities.length &&
    selectedEntities.some((e) => e[file_property])
  ) {
    // filter the entities with the property for download
    let urls = selectedEntities.filter((e) => e[file_property]);
    // need to give a break between clicks or else it only registers the last...
    let interval = setInterval(
      (urls) => {
        // get the url from the end
        let e = urls.pop();
        // insert an element for the client to click
        let tempLink = document.createElement("a");
        tempLink.download = "";
        tempLink.href = e[file_property];
        tempLink.style.display = "none";
        document.body.appendChild(tempLink);
        tempLink.click(); // click will download the file...
        document.body.removeChild(tempLink);

        // if we're done, exit...
        if (urls.length == 0) {
          clearInterval(interval);
        }
      },
      300,
      urls
    );
  } else {
    (testStore || store).dispatch(
      notify({
        id: Date.now(),
        type: "ERROR",
        message: "No files to download.",
      })
    );
  }
};

export const updateDrawingsHasMaj = (drawingsArr, drawingIds) => {
  if (!drawingsArr.length) return drawingsArr;

  let reducedDrawings = drawingsArr.reduce((acc, curr) => {
    acc[curr.id] = curr;
    return acc;
  }, {});

  for (let id of drawingIds) {
    reducedDrawings[id].has_maj = 1;
  }
  let newDrawings = [];
  for (let drawingId in reducedDrawings) {
    newDrawings.push(reducedDrawings[drawingId]);
  }
  return newDrawings;
};

export const flattenFlow = (nodesArr, linksArr) => {
  const adjacencyList = linksArr.reduce((acc, curr) => {
    const nodeIds = curr.id.split("-");

    if (!acc[nodeIds[0]]) acc[nodeIds[0]] = [];
    acc[nodeIds[0]].push(nodesArr.find((n) => n.id === nodeIds[1]));

    return acc;
  }, {});

  const dfs = (node) => {
    //https://www.tutorialspoint.com/Depth-first-search-traversal-in-Javascript
    // Create a Stack and add our initial node in it
    let result = [];
    let s = [];
    let explored = new Set();
    s.push(node);

    // Mark the first node as explored
    explored.add(node);

    // We'll continue till our Stack gets empty
    while (s.length) {
      let t = s.pop();

      // Log every element that comes out of the Stack
      if (explored.has(t)) result = result.filter((rn) => rn.id !== t.id);
      result.push(t);

      // 1. In the edges object, we search for nodes this node is directly connected to.
      // 2. We filter out the nodes that have already been explored.
      // 3. Then we mark each unexplored node as explored and push it to the Stack.
      if (adjacencyList[t.id]) {
        // eslint-disable-next-line no-loop-func
        adjacencyList[t.id].forEach((n) => {
          if (!explored.has(n)) explored.add(n);
          if (explored.has(n)) s = s.filter((sn) => sn.id !== n.id);
          s.push(n);
        });
      }
    }

    return result;
  };

  const result = dfs(nodesArr.find((n) => n.data.startingNode)).map(
    (n, idx) => {
      let { position, data } = n;
      let {
        nodeId,
        stageId,
        logicGate,
        stageStatusGroupName,
        stageStatusGroupColor,
        defaultWeight,
        includeInJointLog,
        stageWorkLevelId,
        shippingBlockPosition,
      } = data;

      const handleShippingStage = (position) => {
        switch (position) {
          case 1:
            return "Loading";
          case 2:
            return "Delivery";
          case 3:
            return "Receiving";
          default:
            return null;
        }
      };

      return {
        node_id: nodeId,
        stage_id: stageId || null,
        logic_gate_id: logicGate || null,
        stage_status_group_name: stageStatusGroupName
          ? stageStatusGroupName
          : stageWorkLevelId !== 4
          ? "In Fabrication"
          : handleShippingStage(shippingBlockPosition),
        stage_status_group_color: stageStatusGroupColor
          ? stageStatusGroupColor
          : "#000000",
        default_weight: defaultWeight || null,
        coordinate: JSON.stringify(position),
        _logic_gate_id: logicGate || null,
        stage_report: includeInJointLog ? 1 : 0,
        stage_order: idx + 1,
      };
    }
  );

  let nodeIdsWithSpaces = result.map((n) => n.node_id);
  let nodeIdsWithoutSpaces = result.map((n) => n.node_id).filter((n) => n);

  return result.map((n, idx) => {
    if (n.node_id) {
      return n;
    } else {
      let nodeId = nodeIdsWithoutSpaces.length
        ? Math.max(...nodeIdsWithoutSpaces) + 1
        : idx + 1;
      nodeIdsWithSpaces[idx] = nodeId;
      nodeIdsWithoutSpaces = nodeIdsWithSpaces.filter((n) => n);

      return { ...n, node_id: nodeId };
    }
  });
};

export const validateGuidInput = (guid) => {
  const pattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

  if (pattern.test(guid)) {
    return true;
  } else {
    return false;
  }
};

export const getCookie = (cname) => {
  var name = cname + "=";
  var decodedCookie = decodeURIComponent(document.cookie);
  var ca = decodedCookie.split(";");
  for (var i = 0; i < ca.length; i++) {
    var c = ca[i];
    while (c.charAt(0) === " ") {
      c = c.substring(1);
    }
    if (c.indexOf(name) === 0) {
      return c.substring(name.length, c.length);
    }
  }
  return "";
};

export const removeCookie = (cname) => {
  document.cookie =
    cname +
    `=;domain=.${process.env.REACT_APP_FABPRO};path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;`;
};

export const deepArraysAreEqual = (arr1, arr2) => {
  if (arr1.length !== arr2.length) {
    return false;
  }

  for (let i = 0; i < arr1.length; i++) {
    const item1 = arr1[i];
    const item2 = arr2[i];

    if (typeof item1 !== typeof item2) {
      return false;
    }

    if (typeof item1 === "object" && item1 !== null && item2 !== null) {
      if (!deepObjectsAreEqual(item1, item2)) {
        return false;
      }
    } else if (item1 !== item2) {
      return false;
    }
  }

  return true;
};

export const deepObjectsAreEqual = (obj1, obj2) => {
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    const val1 = obj1[key];
    const val2 = obj2[key];

    if (
      typeof val1 === "object" &&
      val1 !== null &&
      typeof val2 === "object" &&
      val2 !== null
    ) {
      if (!deepObjectsAreEqual(val1, val2)) {
        return false;
      }
    } else if (val1 !== val2) {
      return false;
    }
  }

  return true;
};

/**
 * Function to filter items based on their job_id/package_id/drawing_id
 * from a list of filter strings.
 *
 * @param {*} data array of data objects to filter
 * @param {*} params object of filter arrays for: jobIds, packageIds, drawingIds
 * @returns filtered array of data objects
 */
export const filterItems = (data, params = {}) => {
  const { jobIds = [], packageIds = [], drawingIds = [] } = params;
  if (!data) return [];
  let filteredItems = [...data];
  filteredItems = filteredItems.filter((data) => {
    // check the job/package/drawing filters
    return (
      (jobIds.length > 0 ? jobIds.includes(data.job_id) : true) &&
      (packageIds.length > 0 ? packageIds.includes(data.package_id) : true) &&
      (drawingIds.length > 0 ? drawingIds.includes(data.drawing_id) : true)
    );
  });
  return filteredItems;
};

/**
 * Will group items based on the input parameters
 * Functionality is pulled from sp_fetch_items sproc
 * @param {*} data array of data objects to group
 * @param {*} params stageIds, grouping, groupingColumns
 * @returns array of grouped data objects
 */
export const groupItems = (data, params = {}) => {
  const { stageIds = [], grouping = 0, groupingColumns = [] } = params;
  let finalData = [];

  // return if no data or no grouping
  if (!data || data?.length === 0) return finalData;
  // the only time we do not need grouping columns is when we are grouping by default
  if ((groupingColumns?.length === 0 && grouping != 3) || grouping === 0)
    return data;

  // setup the required constants
  const fractional_display =
    store?.getState()?.profileData?.systemSettings?.fractional_display || 0; // needed to calc quantity for lengths
  const useFeet = fractional_display === 1; // default to false for test cases
  const default_grouping = [
    "job_id",
    "package_id",
    "heat_number",
    "vendor",
    "material_name",
    "size",
  ]; // for grouping = 3
  const always_group_by =
    stageIds.length > 0 && !stageIds.includes(0)
      ? ["available", "completed", "is_cut"] // always group by these when the stage filter is not empty
      : ["is_cut"]; // is_cut required regardless to calculate the qty properly
  const cols = groupingColumns
    ? groupingColumns.map((col) => col?.normal_name || col)
    : []; // get the column names for the passed grouping columns chosen
  let groupByCols = [...cols, ...always_group_by];
  const includesLength = groupByCols.includes("length");

  switch (grouping) {
    case 1: // group by columns
    case 2: // group by columns and drawing
      if (grouping == 2) groupByCols.push("drawing_id"); // concat the drawing_id IF case 2
      groupByCols = [...groupByCols, "rejected"]; // concat rejected into the group by statement

      break;
    case 3: // default grouping
      // note: this scenario isn't observed in the initial implementation of this func
      groupByCols = [...default_grouping, ...always_group_by];
      break;
    default: // do nothing
  }

  // need to add the ids for the fields that are grouped by name only
  const idFieldList = [
    "job",
    "package",
    "drawing",
    "joining_procedure",
    "laydown_location",
  ];
  idFieldList.forEach((field) => {
    if (groupByCols.includes(`${field}_name`)) groupByCols.push(`${field}_id`);
  });

  // get the keys of the data set for grouping
  const properties = Object.keys(data[0]);
  // group rows by groupByCols results
  const groupedRows = Object.groupBy(data, ({ ...properties }) => {
    const group = Object.keys(properties).filter((c) =>
      groupByCols.includes(c)
    );
    const data = {};
    group.map((g) => {
      if (g == "length") {
        const req_length_cols = ["denominator", "round_direction_id"];
        // only group by length if the material is cut
        if (properties["is_cut"] && properties["is_cut"] === 1)
          data[g] = properties[`${g}`][`decimal`] ?? 0;
        // add required col data for calculating lengths/qty
        req_length_cols.forEach((k) => {
          data[k] = properties[k];
        });
      } else {
        data[g] = properties[g];
      }
    });
    return JSON.stringify({ ...data });
  });

  let id = 0;
  Object.keys(groupedRows).forEach((key) => {
    id++;
    let is_cut = key.includes(`is_cut\":1`);
    let quantity = 0;
    let lengthSum = 0;
    let work_item_ids = [];
    let job_id = null;
    // get the value for the quantity to summarize
    groupedRows[key].forEach((e) => {
      work_item_ids.push(e.id); // need the ids conct for updates
      // if a cut length, summarize by cut length
      if (is_cut) lengthSum += e?.length?.decimal ?? 0.0;
      else if (includesLength) {
        // not cut, but we need to sum length
        // @todo - do we want to be summarizing this? open question. this mimics current functionality.
        lengthSum += e?.length?.decimal ?? 0.0;
        quantity++;
      } else quantity++; // else summarize by count
      job_id = e.job_id;
    });
    const pow = Math.pow(10, 5);
    let quantitySum =
      Math.ceil(
        (is_cut ? (useFeet ? lengthSum / 12 : lengthSum) : quantity) * pow
      ) / pow;

    const data = JSON.parse(key);
    // combine the data outside of keys
    let summaryData = {
      quantity: quantitySum,
      id, // need to add an id for mass update
      work_item_ids: work_item_ids.toString(),
      job_id: job_id,
    };
    // if grouped by length, push the summarized length field
    if (includesLength) {
      data.length = Math.ceil(lengthSum * pow) / pow; // force push since some have 0 length
    }
    finalData.push({ ...data, ...summaryData });
  });

  // need to convert the array for the length fields to display properly
  return conversions.convertArray([...finalData], fractional_display);
};

/**
 * Performs the filterItems and groupItems functions on a dataset.
 * @param {*} data incoming dataset of arrays
 * @param {*} params similar params of sp_fetch_items - object of jobIds, packageIds, drawingIds, stageIds, grouping, groupingColumns
 * @returns filtered and grouped array of objects
 */
export const filterAndGroupItems = (
  data = [],
  params = {
    jobIds: [],
    packageIds: [],
    drawingIds: [],
    stageIds: [],
    grouping: 0,
    groupingColumns: [],
  }
) => {
  let filteredData = [...data];
  filteredData = filterItems(filteredData, params);
  if (params.grouping != 0) filteredData = groupItems(filteredData, params);

  return filteredData;
};

/**
 * Sort array of objects by name
 * @param {*} array unsorted array of objects
 * @returns sorted array by name
 */
export const alphaNumericSortByName = (array) => {
  const collator = new Intl.Collator(undefined, {
    numeric: true,
    sensitivity: "base",
  });

  const sorted = array.sort((a, b) => {
    return collator.compare(a.name, b.name);
  });

  return sorted;
};

/**
 * Returns today as a string YYYY-MM-DD
 */
export const getTodaysDate = () => {
  // Create a new Date object for the current date
  const today = new Date();

  // Get the year, month, and day
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0"); // Months are zero-based
  const day = String(today.getDate()).padStart(2, "0");

  // Format the date as YYYY-MM-DD
  return `${year}-${month}-${day}`;
};

const utils = {
  axiosApiCall,
  useApiCall,
  generateIcon,
  generateTime,
  generateTimev2,
  formatDate,
  permissionLock,
  canDrop,
  rowClassRules,
  getRowHeight,
  onSortChanged,
  timeFormatter,
  convertHex,
  generateKpis,
  convertStringToUnix,
  validateDateStringFormat,
  populateDropdownTimes,
  checkPreviousShiftTime,
  buildCSV,
  titleize,
  transformPriorityToRank,
  transformDrawingPriorityToRank,
  hashUnitInterval,
  escapeRegExp,
  table,
  onDisplayedColumnsChanged,
  reduce,
  simpleFrac,
  whiteSpaceToSingleSpace,
  spacesAndDigits,
  sanitize,
  lengthParse,
  DISPLAY_LENGTH,
  NESTING_LENGTH_DISPLAY,
  nestCuts,
  fractionalRegex,
  multidimensionRegex,
  convertFracToDec,
  dateStringToUnix,
  objectColumns,
  decimalRegex,
  getUniqueCounts,
  multidimensionOnlyX,
  naturalSort,
  b64toBlob,
  confirmPassword,
  validatePassword,
  checkForNewTableFilterSelections,
  objectColumnDefs,
  downloadFiles,
  downloadFilesV2,
  updateDrawingsHasMaj,
  deepArraysAreEqual,
  deepObjectsAreEqual,
  flattenFlow,
  validateGuidInput,
  getCookie,
  removeCookie,
  filterAndGroupItems,
  filterItems,
  groupItems,
  alphaNumericSortByName,
  getTodaysDate,
};

export default utils;
