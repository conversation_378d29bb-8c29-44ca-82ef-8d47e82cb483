const initialState = {
  pageTitle: "",
  navExpanded: false,
  language: "en-US",
  sortState: null,
  sortStateRight: null,
  sortStateLeft: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "SET_PAGE_TITLE":
      return { ...state, pageTitle: payload };
    case "SET_NAV_EXPANDED":
      return { ...state, navExpanded: payload };
    case "SET_LANGUAGE":
      return { ...state, language: payload };
    case "RECEIVE_SORT_STATE_STARTED":
      return { ...state, sortState: null };
    case "RECEIVE_SORT_STATE_SUCCEEDED":
      return { ...state, sortState: payload };
    case "RECEIVE_SORT_STATE_FAILED":
      return { ...state, sortState: {} };
    case "RECEIVE_SORT_STATE_RIGHT_STARTED":
      return { ...state, sortStateRight: null };
    case "RECEIVE_SORT_STATE_RIGHT_SUCCEEDED":
      return { ...state, sortStateRight: payload };
    case "RECEIVE_SORT_STATE_RIGHT_FAILED":
      return { ...state, sortStateRight: {} };
    case "RECEIVE_SORT_STATE_LEFT_STARTED":
      return { ...state, sortStateLeft: null };
    case "RECEIVE_SORT_STATE_LEFT_SUCCEEDED":
      return { ...state, sortStateLeft: payload };
    case "RECEIVE_SORT_STATE_LEFT_FAILED":
      return { ...state, sortStateLeft: {} };
    default:
      return state;
  }
}
