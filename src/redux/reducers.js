// NPM PACKAGE IMPORTS
import { combineReducers } from "redux";

// REDUCER IMPORTS
import generalReducer from "./generalReducer";
import jobsReducer from "../components/jobs/jobsReducer";
import packagesReducer from "../components/packages/packagesReducer";
import drawingsReducer from "../components/drawings/drawingsReducer";
import itemsReducer from "../components/items/itemsReducer";
import manpowerReducer from "../components/manpower/manpowerReducer";
import profileReducer from "../components/profile/profileReducer";
import breadcrumbsReducer from "../components/layout/body/breadcrumbs/breadcrumbsReducer";
import alertPopupReducer from "../components/reusable/alertPopup/alertPopupReducer";
import itemGroupsReducer from "../components/itemGroups/itemGroupsReducer";
import stageGroupsReducer from "../components/stageGroups/stageGroupsReducer";
import flowsReducer from "../components/flows/flowsReducer";
import wizardReducer from "../components/wizard/wizardReducer";
import termsOfServiceReducer from "../components/legalDocuments/termsOfService/termsOfServiceReducer";
import partnersReducer from "../components/layout/footer/partners/partnersReducer";
import myWorkReducer from "../components/myWork/myWorkReducer";
import shippingReducer from "../components/shipping/shippingReducer";
import timersReducer from "../components/timers/timersReducer";
import nestingReducer from "../components/myWork/stagesTimersStack/nesting/nestingReducer";
import throughputsReducer from "../components/throughputs/throughputsReducer";
import generalSpinnerReducer from "../components/reusable/generalSpinner/generalSpinnerReducer";
import cloudSheetCreationReducer from "../components/reusable/cloudSheetCreation/cloudSheetCreationReducer";
import crewsReducer from "../components/reusable/manageCrews/manageCrewsReducer";
import packagesPendingApprovalReducer from "../components/pendingApproval/packages/packagesPendingApprovalReducer";
import genericTimeReducer from "../components/genericTime/genericTimeReducer";
import drawingsPendingApprovalReducer from "../components/pendingApproval/drawings/drawingsPendingApprovalReducer";
import moveDrawingsReducer from "../components/reusable/moveDrawingsModal/moveDrawingsReducer";
import notificationsReducer from "../components/notifications/notificationsReducer";
import usersReducer from "../components/users/usersReducer";
import autodeskAccountSettings from "../components/autodeskAccountSettings/autodeskSettingsReducer";
import generalModalReducer from "../components/reusable/generalModal/generalModalReducer";
import customColumnsReducer from "../components/customColumns/customColumnsReducer";
import pspdfkitReducer from "../components/reusable/pdfViewer/pspdfkitViewerReducer";
import powerbiReducer from "../components/powerbiAnalytics/powerbiAnalyticsReducer";
import dashboardAnalyticsReducer from "../components/dashboardAnalytics/dashboardAnalyticsReducer";
import filesReducer from "../components/files/filesReducer";

// EXPORTS
export default combineReducers({
  generalData: generalReducer,
  jobsData: jobsReducer,
  manpowerData: manpowerReducer,
  profileData: profileReducer,
  breadcrumbsData: breadcrumbsReducer,
  alertPopupData: alertPopupReducer,
  itemGroupsData: itemGroupsReducer,
  stageGroupsData: stageGroupsReducer,
  flowsData: flowsReducer,
  packagesData: packagesReducer,
  drawingsData: drawingsReducer,
  itemsData: itemsReducer,
  wizardData: wizardReducer,
  termsOfServiceData: termsOfServiceReducer,
  partnersData: partnersReducer,
  myWorkData: myWorkReducer,
  shippingData: shippingReducer,
  timerData: timersReducer,
  nestingData: nestingReducer,
  throughputsData: throughputsReducer,
  generalSpinnerData: generalSpinnerReducer,
  cloudSheetCreationData: cloudSheetCreationReducer,
  crewsData: crewsReducer,
  packagesPendingApprovalData: packagesPendingApprovalReducer,
  genericTimeData: genericTimeReducer,
  drawingsPendingApprovalData: drawingsPendingApprovalReducer,
  moveDrawingsData: moveDrawingsReducer,
  notificationsData: notificationsReducer,
  usersData: usersReducer,
  autodeskAccountSettings: autodeskAccountSettings,
  modalData: generalModalReducer,
  customColumns: customColumnsReducer,
  pspdfkitData: pspdfkitReducer,
  powerbiData: powerbiReducer,
  dashboardAnalytics: dashboardAnalyticsReducer,
  filesData: filesReducer,
});
