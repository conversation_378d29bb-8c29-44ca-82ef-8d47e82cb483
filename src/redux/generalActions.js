import {
  fetchColumnState,
  saveColumnState,
  assignUnassignUser,
  saveSortState,
  fetchSortState,
  fetchForgeModelInfo,
} from "../_services";
import { trackMixPanelEvent } from "../utils/_mixPanelUtils";
import store from "../redux/store";

export const setPageTitle = (pageTitle) => ({
  type: "SET_PAGE_TITLE",
  payload: pageTitle,
});
export const setNavExpanded = (navExpanded) => ({
  type: "SET_NAV_EXPANDED",
  payload: navExpanded,
});
export const setLanguage = (language) => ({
  type: "SET_LANGUAGE",
  payload: language,
});
export const receiveColumnStateStarted = (type) => ({
  type: `RECEIVE_${type}_COLUMN_STATE_STARTED`,
});
export const receiveColumnStateSucceeded = (type, columnState) => ({
  type: `RECEIVE_${type}_COLUMN_STATE_SUCCEEDED`,
  payload: columnState,
});
export const receiveColumnStateFailed = (type, error) => ({
  type: `RECEIVE_${type}_COLUMN_STATE_FAILED`,
  payload: error,
});

export const updateStarted = (type) => ({ type: `UPDATE_${type}_STARTED` });
export const updateSucceeded = (type, payload) => ({
  type: `UPDATE_${type}_SUCCEEDED`,
  payload,
});
export const updateFailed = (type, error) => ({
  type: `UPDATE_${type}_FAILED`,
  payload: error,
});

export const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const handleSetPageTitle = (pageTitle) => (dispatch) =>
  dispatch(setPageTitle(pageTitle));
export const handleSetNavExpanded = (navExpanded) => (dispatch) =>
  dispatch(setNavExpanded(navExpanded));
export const handleSetLanguage = (language) => (dispatch) =>
  dispatch(setLanguage(language));

export const handleFetchColumnState = (type, stageIds) => (dispatch) => {
  dispatch(receiveColumnStateStarted(type));
  return fetchColumnState(type, stageIds).then((res) => {
    if (res.error) dispatch(receiveColumnStateFailed(type, res));
    else dispatch(receiveColumnStateSucceeded(type, res));

    return res;
  });
};
export const handleSaveColumnState = (type, columnState, stageIds) => (
  dispatch
) => {
  return saveColumnState(type, columnState, stageIds);
};
export const handleClearColumnState = (type) => (dispatch) =>
  dispatch(receiveColumnStateSucceeded(type, null));

export const handleUpdateUserAssignment = (
  action,
  itemId,
  userId,
  level,
  crewId,
  testWindow = null,
  testStore = null
) => (dispatch) => {
  const type = "USER_ASSIGNMENT";

  const mixPanelEventStart = Date.now();

  dispatch(updateStarted(type));
  return assignUnassignUser(action, itemId, userId, level, crewId).then(
    (res) => {
      if (res.error) dispatch(updateFailed(type, res));
      else dispatch(updateSucceeded(type, res));
      //churnzero event
      const { username } = (testStore || store).getState().profileData.userInfo;
      const { userId } = (testStore || store).getState().profileData;

      if (action === "assignTo") {
        (testWindow || window).ChurnZero.push([
          "trackEvent",
          "User Assigned",
          `User assigned to ${level}(s)`,
          1,
          {
            Product: "FabPro",
            SubGroup: "Assignments",
            Version: process.env.REACT_APP_ENVIRONMENT,
            UserName: username,
            UserId: userId,
          },
        ]);

        trackMixPanelEvent(
          "User Assigned",
          1,
          "Users",
          mixPanelEventStart,
          `User assigned to ${level}(s)`
        );
      }

      return res;
    }
  );
};

export const handleSaveSortState = (
  columnName,
  sortMethod,
  table,
  grouping = 1,
  stageIds = null
) => (dispatch) => {
  const type = "SORT_STATE";

  return saveSortState(columnName, sortMethod, table, grouping, stageIds).then(
    (res) => {
      if (res.error) dispatch(receiveFailed(type, res));
      else dispatch(receiveSucceeded(type, res));

      return res;
    }
  );
};
export const handleFetchSortState = (
  table,
  grouping = 1,
  stageIds = 0,
  specificity
) => (dispatch) => {
  let type = "SORT_STATE";

  if (specificity) type += `_${specificity}`;

  dispatch(receiveStarted(type));
  return fetchSortState(table, grouping, stageIds).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res[0]));

    return res;
  });
};

export const handleFetchForgeModelInfo = (id) => (dispatch) => {
  return fetchForgeModelInfo(id);
};
