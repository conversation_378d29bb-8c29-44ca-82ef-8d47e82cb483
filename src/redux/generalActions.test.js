// NPM PACKAGE IMPORTS
import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockAdapter from "axios-mock-adapter";
import thunk from "redux-thunk";

// REDUX IMPORTS
import {
  // ACTION CREATORS
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  setPageTitle,
  setNavExpanded,
  setLanguage,
  receiveColumnStateStarted,
  receiveColumnStateSucceeded,
  receiveColumnStateFailed,
  updateStarted,
  updateSucceeded,
  updateFailed,
  // ACTION HANDLERS
  handleSetPageTitle,
  handleSetNavExpanded,
  handleSetLanguage,
  handleFetchColumnState,
  handleSaveColumnState,
  handleClearColumnState,
  handleUpdateUserAssignment,
  handleSaveSortState,
  handleFetchSortState,
} from "./generalActions";

describe("GENERAL", () => {
  let store, httpMock;

  const testError = (mes) => ({
    error: { status: 404, message: mes },
  });

  const testReturn = [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }];

  beforeEach(() => {
    httpMock = new MockAdapter(axios);
    const mockStore = configureMockStore([thunk]);
    store = mockStore({});
  });

  describe("handleSetPageTitle", () => {
    it("should set the site's page title", () => {
      store.dispatch(handleSetPageTitle("Jobs"));
      store.dispatch(handleSetPageTitle("Packages"));

      const received = store.getActions(),
        expected1 = setPageTitle("Jobs"),
        expected2 = setPageTitle("Packages");

      expect(received.length).toBe(2);
      expect(received[0]).toEqual(expected1);
      expect(received[1]).toEqual(expected2);
    });
  });

  describe("handleSetNavExpanded", () => {
    it("should set the user's nav expansion preference", () => {
      store.dispatch(handleSetNavExpanded(true));
      store.dispatch(handleSetNavExpanded(false));

      const received = store.getActions(),
        expected1 = setNavExpanded(true),
        expected2 = setNavExpanded(false);

      expect(received.length).toBe(2);
      expect(received[0]).toEqual(expected1);
      expect(received[1]).toEqual(expected2);
    });
  });

  describe("handleSetLanguage", () => {
    it("should set the user's language for translations use", () => {
      store.dispatch(handleSetLanguage("en-US"));
      store.dispatch(handleSetLanguage("fr-EU"));

      const received = store.getActions(),
        expected1 = setLanguage("en-US"),
        expected2 = setLanguage("fr-EU");

      expect(received.length).toBe(2);
      expect(received[0]).toEqual(expected1);
      expect(received[1]).toEqual(expected2);
    });
  });

  describe("handleFetchColumnState", () => {
    it("should fetch the proper column state", async () => {
      httpMock
        .onGet("ag-table/column-state?app_type=fab&table=jobs")
        .replyOnce(200, testReturn)
        .onGet("ag-table/column-state?app_type=fab&table=packages")
        .replyOnce(200, testReturn)
        .onGet(
          "ag-table/column-state?app_type=fab&table=work_stages&work_stage_ids=1,2,3"
        )
        .replyOnce(200, testReturn)
        .onGet(
          "ag-table/column-state?app_type=fab&table=my_work_items&work_stage_ids=1,2,3"
        )
        .replyOnce(200, testReturn)
        .onGet("ag-table/column-state?app_type=fab&table=jobs")
        .replyOnce(404, testError("No column state found."));

      let response = await store.dispatch(handleFetchColumnState("JOBS"));

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveColumnStateStarted("JOBS"),
          receiveColumnStateSucceeded("JOBS", testReturn),
        ];

      expect(receivedActions).toEqual(expectedActions);
      expect(response).toEqual(testReturn);

      store.clearActions();

      response = await store.dispatch(handleFetchColumnState("PACKAGES"));

      receivedActions = store.getActions();
      expectedActions = [
        receiveColumnStateStarted("PACKAGES"),
        receiveColumnStateSucceeded("PACKAGES", testReturn),
      ];

      expect(receivedActions).toEqual(expectedActions);
      expect(response).toEqual(testReturn);

      store.clearActions();

      response = await store.dispatch(
        handleFetchColumnState("ITEMS", [1, 2, 3])
      );

      receivedActions = store.getActions();
      expectedActions = [
        receiveColumnStateStarted("ITEMS"),
        receiveColumnStateSucceeded("ITEMS", testReturn),
      ];

      expect(receivedActions).toEqual(expectedActions);
      expect(response).toEqual(testReturn);

      store.clearActions();

      response = await store.dispatch(
        handleFetchColumnState("MY_WORK_ITEMS", [1, 2, 3])
      );

      receivedActions = store.getActions();
      expectedActions = [
        receiveColumnStateStarted("MY_WORK_ITEMS"),
        receiveColumnStateSucceeded("MY_WORK_ITEMS", testReturn),
      ];

      expect(receivedActions).toEqual(expectedActions);
      expect(response).toEqual(testReturn);

      store.clearActions();

      response = await store.dispatch(handleFetchColumnState("JOBS"));

      receivedActions = store.getActions();
      expectedActions = [
        receiveColumnStateStarted("JOBS"),
        receiveColumnStateFailed("JOBS", testError("No column state found.")),
      ];

      expect(receivedActions).toEqual(expectedActions);
      expect(response).toEqual(testError("No column state found."));

      store.clearActions();
    });
  });

  describe("handleSaveColumnState", () => {
    it("should save the provided column state for the provided table", async () => {
      httpMock
        .onPost("ag-table/column-state")
        .replyOnce(200, testReturn)
        .onPost("ag-table/column-state")
        .replyOnce(200, testReturn)
        .onPost("ag-table/column-state")
        .replyOnce(200, testReturn)
        .onPost("ag-table/column-state")
        .replyOnce(200, testReturn)
        .onPost("ag-table/column-state")
        .replyOnce(404, testError("No column state found."));

      let response = await store.dispatch(
        handleSaveColumnState("JOBS", testReturn)
      );

      expect(response).toEqual(testReturn);
      expect(httpMock.history.post.length).toBe(1);
      expect(httpMock.history.post[0].data).toEqual(
        JSON.stringify({
          app_type: "fab",
          table: "jobs",
          columns: testReturn,
        })
      );

      store.clearActions();

      response = await store.dispatch(
        handleSaveColumnState("PACKAGES", testReturn)
      );

      expect(response).toEqual(testReturn);
      expect(httpMock.history.post.length).toBe(2);
      expect(httpMock.history.post[1].data).toEqual(
        JSON.stringify({
          app_type: "fab",
          table: "packages",
          columns: testReturn,
        })
      );

      store.clearActions();

      response = await store.dispatch(
        handleSaveColumnState("ITEMS", testReturn, [1, 2, 3])
      );

      expect(response).toEqual(testReturn);
      expect(httpMock.history.post.length).toBe(3);
      expect(httpMock.history.post[2].data).toEqual(
        JSON.stringify({
          app_type: "fab",
          table: "work_stages",
          columns: testReturn,
        })
      );

      store.clearActions();

      response = await store.dispatch(
        handleSaveColumnState("MY_WORK_ITEMS", testReturn, [1, 2, 3])
      );

      expect(response).toEqual(testReturn);
      expect(httpMock.history.post.length).toBe(4);
      expect(httpMock.history.post[3].data).toEqual(
        JSON.stringify({
          app_type: "fab",
          table: "my_work_items",
          columns: testReturn,
        })
      );

      store.clearActions();

      response = await store.dispatch(
        handleSaveColumnState("JOBS", testReturn)
      );

      expect(response).toEqual(testError("No column state found."));
      expect(httpMock.history.post.length).toBe(5);
      expect(httpMock.history.post[4].data).toEqual(
        JSON.stringify({
          app_type: "fab",
          table: "jobs",
          columns: testReturn,
        })
      );

      store.clearActions();
    });
  });

  describe("handleClearColumnState", () => {
    it("should set the specified table's column state to null", () => {
      store.dispatch(handleClearColumnState("JOBS"));

      let receivedActions = store.getActions(),
        expectedActions = [receiveColumnStateSucceeded("JOBS", null)];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      store.dispatch(handleClearColumnState("PACKAGES"));

      receivedActions = store.getActions();
      expectedActions = [receiveColumnStateSucceeded("PACKAGES", null)];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleUpdateUserAssignment", () => {
    const testStore = {
      getState: () => ({ profileData: testStore.profileData }),
      profileData: {
        userId: 1,
        userInfo: {
          username: "<EMAIL>",
        },
      },
    };

    it("should assign or remove the specified user to/from the specified item", async () => {
      httpMock
        .onPut("users")
        .replyOnce(404, testError("No user found."))
        .onPut("users")
        .reply(200, testReturn);

      const testWindow = {
          ChurnZero: {
            push: (arr) => testWindow.ChurnZero.data.push(arr),
            data: [],
          },
        },
        churnZeroData = [
          [
            "trackEvent",
            "User Assigned",
            `User assigned to drawing(s)`,
            1,
            {
              Product: "FabPro",
              SubGroup: "Assignments",
              Version: process.env.REACT_APP_ENVIRONMENT,
              UserName: "<EMAIL>",
              UserId: 1,
            },
          ],
        ];

      let response = await store.dispatch(
        handleUpdateUserAssignment(
          "assignTo",
          "15",
          1,
          "drawing",
          null,
          testWindow,
          testStore
        )
      );
      let receivedActions = store.getActions(),
        expectedActions = [
          updateStarted("USER_ASSIGNMENT"),
          updateFailed("USER_ASSIGNMENT", testError("No user found.")),
        ];

      expect(response).toEqual(testError("No user found."));
      expect(receivedActions).toEqual(expectedActions);
      expect(httpMock.history.put.length).toBe(1);
      expect(httpMock.history.put[0].data).toEqual(
        JSON.stringify({
          callAction: "assignTo",
          callParams: { itemId: "15", level: "drawing", userId: 1 },
        })
      );
      expect(testWindow.ChurnZero.data).toEqual(churnZeroData);

      testWindow.ChurnZero.data = [];
      store.clearActions();

      response = await store.dispatch(
        handleUpdateUserAssignment(
          "assignTo",
          "15",
          1,
          "drawing",
          null,
          testWindow,
          testStore
        )
      );
      receivedActions = store.getActions();
      expectedActions = [
        updateStarted("USER_ASSIGNMENT"),
        updateSucceeded("USER_ASSIGNMENT", testReturn),
      ];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);
      expect(httpMock.history.put.length).toBe(2);
      expect(httpMock.history.put[1].data).toEqual(
        JSON.stringify({
          callAction: "assignTo",
          callParams: { itemId: "15", level: "drawing", userId: 1 },
        })
      );
      expect(testWindow.ChurnZero.data).toEqual(churnZeroData);

      testWindow.ChurnZero.data = [];
      store.clearActions();

      response = await store.dispatch(
        handleUpdateUserAssignment(
          "assignTo",
          "15",
          null,
          "drawing",
          10,
          testWindow,
          testStore
        )
      );
      receivedActions = store.getActions();
      expectedActions = [
        updateStarted("USER_ASSIGNMENT"),
        updateSucceeded("USER_ASSIGNMENT", testReturn),
      ];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);
      expect(httpMock.history.put.length).toBe(3);
      expect(httpMock.history.put[2].data).toEqual(
        JSON.stringify({
          callAction: "assignTo",
          callParams: { itemId: "15", level: "drawing", crew_id: 10 },
        })
      );
      expect(testWindow.ChurnZero.data).toEqual(churnZeroData);

      testWindow.ChurnZero.data = [];
      store.clearActions();
    });
  });

  describe("handleSaveSortState", () => {
    it("should save the provided sort state", async () => {
      httpMock
        .onPost("ag-table/sort-state")
        .replyOnce(404, testError("Failed saving."))
        .onPost("ag-table/sort-state")
        .reply(200, testReturn);

      let response = await store.dispatch(
        handleSaveSortState("job_name", "asc", "JOBS", null)
      );
      let receivedActions = store.getActions(),
        expectedActions = [
          receiveFailed("SORT_STATE", testError("Failed saving.")),
        ];

      expect(response).toEqual(testError("Failed saving."));
      expect(receivedActions).toEqual(expectedActions);
      expect(httpMock.history.post.length).toBe(1);
      expect(httpMock.history.post[0].data).toEqual(
        JSON.stringify({
          app_type: "fab",
          table: "jobs",
          sorting_column_name: "job_name",
          sorting_method: "asc",
          grouping: 1,
          stage_ids: null,
        })
      );

      store.clearActions();

      response = await store.dispatch(
        handleSaveSortState("job_name", "asc", "JOBS", null)
      );
      receivedActions = store.getActions();
      expectedActions = [receiveSucceeded("SORT_STATE", testReturn)];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);
      expect(httpMock.history.post.length).toBe(2);
      expect(httpMock.history.post[1].data).toEqual(
        JSON.stringify({
          app_type: "fab",
          table: "jobs",
          sorting_column_name: "job_name",
          sorting_method: "asc",
          grouping: 1,
          stage_ids: null,
        })
      );

      store.clearActions();

      response = await store.dispatch(
        handleSaveSortState("id", "desc", "PACKAGES", 2, "1,2,3")
      );
      receivedActions = store.getActions();
      expectedActions = [receiveSucceeded("SORT_STATE", testReturn)];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);
      expect(httpMock.history.post.length).toBe(3);
      expect(httpMock.history.post[2].data).toEqual(
        JSON.stringify({
          app_type: "fab",
          table: "packages",
          sorting_column_name: "id",
          sorting_method: "desc",
          grouping: 2,
          stage_ids: "1,2,3",
        })
      );

      store.clearActions();
    });

    describe("handleFetchSortState", () => {
      it("should fetch the sort state for the specified table", async () => {
        httpMock
          .onGet("ag-table/sort-state?app_type=fab&table=jobs&grouping=1")
          .replyOnce(404, testError("Failed to fetch."))
          .onGet("ag-table/sort-state?app_type=fab&table=jobs&grouping=1")
          .replyOnce(200, testReturn)
          .onGet("ag-table/sort-state?app_type=fab&table=packages&grouping=2")
          .replyOnce(200, testReturn)
          .onGet(
            "ag-table/sort-state?app_type=fab&table=assigned_drawings&grouping=3&stage_ids=1,2,3"
          )
          .replyOnce(200, testReturn);

        let response = await store.dispatch(
          handleFetchSortState("JOBS", null, null)
        );
        let receivedActions = store.getActions(),
          expectedActions = [
            receiveStarted("SORT_STATE"),
            receiveFailed("SORT_STATE", testError("Failed to fetch.")),
          ];

        expect(response).toEqual(testError("Failed to fetch."));
        expect(receivedActions).toEqual(expectedActions);

        store.clearActions();

        response = await store.dispatch(handleFetchSortState("JOBS", 1, null));
        receivedActions = store.getActions();
        expectedActions = [
          receiveStarted("SORT_STATE"),
          receiveSucceeded("SORT_STATE", testReturn[0]),
        ];

        expect(response).toEqual(testReturn);
        expect(receivedActions).toEqual(expectedActions);

        store.clearActions();

        response = await store.dispatch(
          handleFetchSortState("PACKAGES", 2, null)
        );
        receivedActions = store.getActions();
        expectedActions = [
          receiveStarted("SORT_STATE"),
          receiveSucceeded("SORT_STATE", testReturn[0]),
        ];

        expect(response).toEqual(testReturn);
        expect(receivedActions).toEqual(expectedActions);

        store.clearActions();

        response = await store.dispatch(
          handleFetchSortState("ASSIGNED_DRAWINGS", 3, "1,2,3", "LEFT")
        );
        receivedActions = store.getActions();
        expectedActions = [
          receiveStarted("SORT_STATE_LEFT"),
          receiveSucceeded("SORT_STATE_LEFT", testReturn[0]),
        ];

        expect(response).toEqual(testReturn);
        expect(receivedActions).toEqual(expectedActions);

        store.clearActions();
      });
    });
  });
});
