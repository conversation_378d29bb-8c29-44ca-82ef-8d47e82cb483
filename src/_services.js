import utils from "./_utils";
import axios from "axios";

// REDUX IMPORTS
import store from "./redux/store";
import { notify } from "./components/reusable/alertPopup/alertPopupActions";

export const fetchJobs = (
  all = false,
  stages = null,
  testUserId = null,
  costCodes = null
) => {
  const { apiCall, user_id } = utils.useApiCall();

  let apiPath = `jobs?is_active=1`;
  const method = "GET";

  if (!all)
    apiPath += `&user_id=${testUserId || user_id}&with_assigned_count=1`;
  if (
    stages &&
    (!Array.isArray(stages) || (stages.length && stages[0] !== 0)) &&
    stages !== "0"
  )
    apiPath += `&stage_ids=${
      Array.isArray(stages) ? stages.join(",") : stages
    }`;

  if (costCodes && costCodes.length) {
    apiPath += `&cost_codes=${costCodes.join(",")}`;
  }

  return apiCall(apiPath, method, null, null, true);
};

export const fetchUsers = ({
  withAssignedGroups = 0,
  location_id,
  role = 0,
  includeInactive = true,
}) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "users?app_type=fab&with_stage_count=1";
  if (!!withAssignedGroups) {
    apiPath += `&send_assigned_groups=${withAssignedGroups}`;
  }
  if (location_id) {
    apiPath += `&location_id=${location_id}`;
  }
  if (!!role) {
    apiPath += `&role_ids=${role}`;
  }
  if (includeInactive) {
    apiPath += `&include_inactive=1`;
  }
  const method = "GET";
  return apiCall(apiPath, method, null, null, true);
};

export const fetchPackages = (
  jobIds,
  all = false,
  stages = null,
  testUserId = null
) => {
  const { apiCall, user_id } = utils.useApiCall();

  let apiPath = `packages?is_active=1`;
  const method = "GET";

  if (jobIds && jobIds.length) apiPath += `&job_ids=${jobIds.join(",")}`;
  if (!all)
    apiPath += `&user_id=${testUserId || user_id}&with_assigned_counts=1`;
  if (stages && stages !== "0") apiPath += `&stage_ids=${stages}`;

  return apiCall(apiPath, method, null, null, true);
};

export const fetchDrawings = (
  jobIds,
  packageIds,
  all = false,
  stages = null,
  withForge = true,
  genericTime = false
) => {
  const { apiCall, user_id } = utils.useApiCall();

  let apiPath = `drawings/simplified?app_type=fab&is_active=1`;
  const method = "GET";
  const testUser = 1; //used for react tests

  if (jobIds && jobIds.length) apiPath += `&job_ids=${jobIds.join(",")}`;
  if (packageIds && packageIds.length)
    apiPath += `&package_ids=${packageIds.join(",")}`;
  if (!all) apiPath += `&user_ids=${user_id || testUser}`;
  if (
    stages &&
    (!Array.isArray(stages) || (stages.length && stages[0] !== 0)) &&
    stages !== "0"
  )
    apiPath += `&stage_ids=${
      Array.isArray(stages) ? stages.join(",") : stages
    }`;
  if (withForge) apiPath += `&forge_info=1`;

  // filter out drawings in pending, on hold, and loaded
  if (genericTime) {
    apiPath += `&pending_approval=false&on_hold=false&loaded=false`;
  }

  return apiCall(apiPath, method, null, null, true);
};

/**
 * @deprecated We want to start splitting out the functionality from this mega function.
 * Please try to split out your use case if you need to use this, for now it is supported.
 * @param {*} jobIds
 * @param {*} packageIds
 * @param {*} drawingIds
 * @param {*} stageIds
 * @param {*} all
 * @param {*} jobIds String list of job ids
 * @param {*} packageIds String list of package ids
 * @param {*} drawingIds String list of drawing ids
 * @param {*} stageIds String list of stage ids
 * @param {*} all Filter by active user
 * @param {*} includeArchived
 * @param {*} grouping Number of grouping category
 * @param {*} groupingColumns String list of work item col ids
 * @returns List of item objects from database.
 * @returns
 */
export const fetchItems = (
  jobIds,
  packageIds,
  drawingIds,
  stageIds,
  all = false,
  includeArchived = false,
  grouping = null,
  groupingColumns = null
) => {
  const { apiCall } = utils.useApiCall();
  const apiPath = `items/fetch`;
  const method = "POST";

  let body = {
    app_type: "fab",
  };

  if (jobIds && jobIds.length)
    Object.assign(body, { job_ids: jobIds.join(",") });
  if (packageIds && packageIds.length)
    Object.assign(body, { package_ids: packageIds.join(",") });
  if (drawingIds && drawingIds.length)
    Object.assign(body, { drawing_ids: drawingIds.join(",") });
  if (stageIds && stageIds.length)
    Object.assign(body, { stage_ids: stageIds.join(",") });
  if (!all) Object.assign(body, { is_assigned: true });
  if (includeArchived) Object.assign(body, { include_archived: true });
  if (grouping && groupingColumns) {
    Object.assign(body, {
      grouped: grouping,
      work_item_column_ids: groupingColumns.map((c) => c.id).join(","),
    });
  }

  return apiCall(apiPath, method, body);
};

/**
 * Fetch items v2 which returns reduced column data.
 * @param {*} jobIds String list of job ids
 * @param {*} packageIds String list of package ids
 * @param {*} drawingIds String list of drawing ids
 * @param {*} stageIds String list of stage ids
 * @param {*} all Filter by active user
 * @param {*} grouping Number of grouping category
 * @param {*} groupingColumns String list of work item col ids
 * @returns List of item objects from database.
 */
export const fetchItemsv2 = (
  jobIds,
  packageIds,
  drawingIds,
  stageIds,
  all = false,
  grouping = null,
  groupingColumns = null
) => {
  const { apiCall } = utils.useApiCall();
  const apiPath = `v2/items/fetch`;
  const method = "POST";

  let body = {
    app_type: "fab",
  };

  if (jobIds && jobIds.length)
    Object.assign(body, { job_ids: jobIds.join(",") });
  if (packageIds && packageIds.length)
    Object.assign(body, { package_ids: packageIds.join(",") });
  if (drawingIds && drawingIds.length)
    Object.assign(body, { drawing_ids: drawingIds.join(",") });
  if (stageIds && stageIds.length)
    Object.assign(body, { stage_ids: stageIds.join(",") });
  if (!all) Object.assign(body, { is_assigned: true });
  if (grouping && groupingColumns) {
    Object.assign(body, {
      grouped: grouping,
      work_item_column_ids: groupingColumns.map((c) => c.id).join(","),
    });
  }

  return apiCall(apiPath, method, body);
};

export const fetchItemsById = (ids, queryParams = {}) => {
  const { apiCall } = utils.useApiCall();
  const apiPath = `items/${ids.join(",")}`;
  const method = "GET";

  // Add query parameters to the path, no fuss proper way
  const searchParams = new URLSearchParams(queryParams).toString();
  const fullPath = searchParams ? `${apiPath}?${searchParams}` : apiPath;

  return apiCall(fullPath, method);
};

export const fetchBOM = (parentIds, type, isGrouped) => {
  const { apiCall } = utils.useApiCall();
  const apiPath = `items/fetch`;
  const method = "POST";
  let body = { bom: true };
  if (isGrouped) {
    body.grouped = 3;
  }

  if (type === "JOBS" && parentIds && parentIds.length)
    Object.assign(body, { job_ids: parentIds.join(",") });
  if (type === "PACKAGES" && parentIds && parentIds.length)
    Object.assign(body, { package_ids: parentIds.join(",") });
  if (type === "DRAWINGS" && parentIds && parentIds.length)
    Object.assign(body, { drawing_ids: parentIds.join(",") });

  return apiCall(apiPath, method, body);
};

export const updatePackages = (idsToUpdate, updatedData) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `packages`;
  const method = "PUT";

  const body = {
    callParams: {
      idString: idsToUpdate.toString(),
      updateData: [],
    },
  };
  let resultObj = {};
  if (Array.isArray(updatedData)) {
    updatedData.forEach((data) => {
      const field = Object.keys(data)[0];
      const value = Object.values(data)[0];
      resultObj[field] = value;
    });
  } else resultObj = updatedData;

  body.callParams.updateData.push(resultObj);

  return apiCall(apiPath, method, body, "Successfully updated package.");
};

// JOB, DRAWINGS
export const editWork = (
  idsToUpdate,
  updatedData,
  itemType,
  action = "update"
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `${itemType}/${idsToUpdate}`;
  const method = "PUT";

  let callParams = {};
  let body = {};

  if (itemType !== "jobs")
    Object.assign(body, {
      callAction: action,
    });

  if (Array.isArray(updatedData)) {
    updatedData.forEach((data) => {
      let field = Object.keys(data)[0];
      const value = Object.values(data)[0];
      if (field === "laydown_location") {
        body.laydown_location = value;
        return;
      }
      // drawings PUT uses original_name instead of name
      if (field === "name" && itemType === "drawings") {
        field = `original_name`;
      }
      callParams[field] = value;
    });
  } else callParams = updatedData;

  if (itemType === "jobs") body.app_type = "fab";

  if (Object.keys(callParams).length) Object.assign(body, { callParams });

  return apiCall(
    apiPath,
    method,
    body,
    `Successfully updated ${itemType.slice(0, -1)}.`
  );
};

export const updateWorkItems = (idsToUpdate, updatedData, quantity) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `items`;
  const method = `PUT`;

  let body = {
    data: {},
  };

  if (Array.isArray(idsToUpdate) && quantity) {
    apiPath += "/edit-grouped-items";
    Object.assign(body, { quantity, group_ids: `${idsToUpdate.join(",")}` });
  } else Object.assign(body, { ids: `${idsToUpdate}` });

  if (Array.isArray(updatedData)) {
    updatedData.forEach((data) => {
      Object.assign(body.data, data);
    });
  } else body.data = updatedData;

  return apiCall(apiPath, method, body, "Successfully updated items.");
};

export const checkRegularShift = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "self/shiftNow";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true, true);
};

export const checkPartnerShifts = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "self/partners/shifts";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true, true);
};

export const fetchUserInformation = () => {
  const { apiCall } = utils.useApiCall();

  // update from users/${userId} to self/user for profile picture...
  const apiPath = `self/user`;
  const method = "GET";
  return apiCall(apiPath, method);
};

export const updateUserInfo = (userId, newData) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `users/${userId}`;
  const method = "PUT";

  const body = {};
  //if archiving, we only want to send that in the request
  if (!newData.hasOwnProperty("isActive")) {
    newData.username && Object.assign(body, { username: newData.username });
    newData.firstName && Object.assign(body, { first_name: newData.firstName });
    newData.lastName && Object.assign(body, { last_name: newData.lastName });
    // email and phone are not clearable in DB
    Object.assign(body, { email: newData.email || null });
    Object.assign(body, { phone_number: newData.phone || null });
    Object.assign(body, { welder_id: newData.welderId || null });
    Object.assign(body, { employee_number: newData.employeeNumber || null });
    newData.hasOwnProperty("laborRate") &&
      Object.assign(body, {
        labor_rate: newData.laborRate.length
          ? parseFloat(newData.laborRate).toFixed(2)
          : null,
      });
    newData.roleId && Object.assign(body, { role_id: newData.roleId });
    newData.tierId && Object.assign(body, { tier_id: newData.tierId });

    // should be clearable but API isn't allowing anything other than numbers
    newData.hasOwnProperty("titleId") &&
      Object.assign(body, { title_id: newData.titleId });
    newData.hasOwnProperty("floatMode") &&
      Object.assign(body, { float_mode: newData.floatMode });
  }
  newData.hasOwnProperty("isActive") &&
    Object.assign(body, { is_active: newData.isActive });

  const successMessage = newData.hasOwnProperty("isActive")
    ? `Successfully ${newData.isActive ? "activated" : "deactivated"} user.`
    : `Successfully updated user information.`;
  return apiCall(apiPath, method, body, successMessage);
};

export const updateWork = (idsToUpdate, column, newValue, itemType) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `${itemType}/${idsToUpdate}`;
  const method = "PUT";

  const body = {
    app_type: "fab",
    callAction: "update",
    callParams: {
      [column]: newValue,
    },
  };

  return apiCall(apiPath, method, body);
};

export const createUser = (newData) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `users`;
  const method = "POST";

  const body = {
    callAction: "create",
    callParams: {
      data: [],
    },
  };

  const data = {
    user_shifts: 0,
    notificationFrequency: 0,
    isActive: 1,
  };

  Object.assign(data, {
    username: newData.username,
    first_name: newData.firstName,
    last_name: newData.lastName,
    roleId: parseInt(newData.roleId),
    tierId: parseInt(newData.tierId),
    float_mode: newData.floatMode ? 1 : 0,
  });
  // email and phone are not clearable in DB
  newData.email && Object.assign(data, { email: newData.email });
  newData.phone && Object.assign(data, { phone_number: newData.phone });
  newData.password && Object.assign(data, { password: newData.password });
  newData.welderId && Object.assign(data, { welder_id: newData.welderId });
  newData.employeeNumber &&
    Object.assign(data, { employee_number: newData.employeeNumber });
  newData.laborRate &&
    Object.assign(data, {
      labor_rate: parseFloat(newData.laborRate).toFixed(2),
    });
  newData.titleId &&
    Object.assign(data, { title_id: parseInt(newData.titleId) });
  // newData.hangerworks && Object.assign(data, { hangerworks: newData.hangerworks ? 1 : 0});
  // newData.bimpro && Object.assign(data, { bimpro: newData.bimpro ? 1 : 0});

  body.callParams.data.push(data);

  return apiCall(apiPath, method, body, "Successfully created user.");
};

export const fetchLocations = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `locations`;
  const method = `GET`;

  return apiCall(apiPath, method);
};

export const fetchItemGroupTypes = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "item-groups/group-types";
  const method = "GET";

  return apiCall(apiPath, method);
};

export const fetchAppGroups = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "item-groups?groupType=app";
  const method = "GET";

  return apiCall(apiPath, method);
};

export const fetchItemGroups = ({
  assignedUserId,
  notAssignedUserId,
  groupTypeId,
}) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "item-groups";
  const method = "GET";

  if (assignedUserId) apiPath += "?assigned_user_id=" + assignedUserId;
  if (notAssignedUserId)
    apiPath += "?not_assigned_user_id=" + notAssignedUserId;
  if (groupTypeId) apiPath += "?group_type_id=" + groupTypeId;

  return apiCall(apiPath, method);
};

export const fetchAssignedUsers = (groupIds) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "item-groups/assigned-users/" + groupIds;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const fetchGroupContents = (groupId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "item-groups/contents/" + groupId;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const assignUsersToItemGroups = (userIds, groupIds) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "item-groups/assigned-users";
  const method = "PUT";
  const body = {
    user_ids: `${userIds}`,
    group_ids: `${groupIds}`,
    direction: 1,
  };
  const successMessage = "Users successfully assigned.";

  return apiCall(apiPath, method, body, successMessage);
};

export const unassignUsersFromItemGroups = (userIds, groupIds) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "item-groups/assigned-users";
  const method = "PUT";
  const body = {
    user_ids: `${userIds}`,
    group_ids: `${groupIds}`,
    direction: 0,
  };
  const successMessage = "Users successfully unassigned.";

  return apiCall(apiPath, method, body, successMessage);
};

export const fetchStages = (jobIds, userIds) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "stages?is_task=0";
  const method = "GET";

  if (jobIds) apiPath += `&job_ids=${jobIds.join(",")}`;

  return apiCall(apiPath, method);
};

export const fetchWorkStagesGet = () => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `work-stages?app_type=fab&exclude_shipping_blocks=1`;
  let method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchWorkStages = (
  workable = false,
  jobIds = null,
  packageIds = null,
  drawingIds = null,
  flowId = null
) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `work-stages/fetch`;
  let method = "POST";
  let body = {};

  if (jobIds && jobIds.length)
    Object.assign(body, { job_ids: jobIds.join(",") });
  if (packageIds && packageIds.length)
    Object.assign(body, { package_ids: packageIds.join(",") });
  if (drawingIds && drawingIds.length)
    Object.assign(body, { drawing_ids: drawingIds.join(",") });

  if (workable) {
    apiPath = `work-stages/workable`;
  } else {
    Object.assign(body, { app_type: "fab", exclude_shipping_blocks: 1 });
    if (flowId) Object.assign(body, { flow_ids: flowId.toString() });
  }

  return apiCall(apiPath, method, body || null, null, true);
};

export const fetchGenericTimeStages = (jobIds, packageIds, drawingIds) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `work-stages/gtime`;
  const method = "GET";

  if (drawingIds && drawingIds.length) {
    apiPath += `?drawing_ids=${drawingIds.join(",")}`;
  } else if (packageIds && packageIds.length) {
    apiPath += `?package_ids=${packageIds.join(",")}`;
  } else if (jobIds && jobIds.length) apiPath += `?job_ids=${jobIds.join(",")}`;

  return apiCall(apiPath, method);
};

export const fetchWorkStageColumns = (stageIds, includeCustom) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `work-stages/${stageIds.join("%2C")}/columns`;
  if (includeCustom) apiPath += `?is_custom=1`;

  const method = "GET";

  return apiCall(apiPath, method);
};

export const fetchFlows = () => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "work-flows";
  const method = "GET";

  return apiCall(apiPath, method);
};

export const assignUsersToStages = (userIds, stageIds) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "stages/assigned-users";
  const method = "PUT";
  const body = {
    user_ids: `${userIds}`,
    stage_ids: `${stageIds}`,
    direction: 1,
  };
  const successMessage = "Users successfully assigned.";

  return apiCall(apiPath, method, body, successMessage);
};

export const unassignUsersFromStages = (userIds, stageIds) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "stages/assigned-users";
  const method = "PUT";
  const body = {
    user_ids: `${userIds}`,
    stage_ids: `${stageIds}`,
    direction: 0,
  };
  const successMessage = "Users successfully unassigned.";

  return apiCall(apiPath, method, body, successMessage);
};

export const updateUserStatus = async (
  userIds,
  addStatusIds,
  removeStatusIds
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "users/statuses";
  const method = "PUT";
  const successMessage = "Updated user's status";
  let body;

  if (removeStatusIds) {
    body = {
      user_ids: userIds,
      status_ids: removeStatusIds,
      direction: 0,
    };

    await apiCall(apiPath, method, body);
  }

  body = {
    user_ids: userIds,
    status_ids: addStatusIds,
    direction: 1,
  };

  return apiCall(apiPath, method, body, successMessage);
};

export const fetchAllUserStatuses = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "users/statuses";
  const method = "GET";

  return apiCall(apiPath, method);
};

export const updateDrawingPriorities = (updatedItems) => {
  const { apiCall } = utils.useApiCall();
  const successMessage = `Successfully updated drawing priorities`;

  const apiPath = `drawings/priorities`;
  const method = "PUT";

  return apiCall(apiPath, method, updatedItems, successMessage);
};

export const fetchCostCodes = (
  pkgIds = [],
  includeGeneral = 1,
  splitOut = 0,
  jobIds = [],
  onlyGeneral = 0,
  drawingIds = []
) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `cost-codes?is_active=1&include_general=${includeGeneral}&split_out=${splitOut}&only_general=${onlyGeneral}`;
  const method = "GET";

  if (Array.isArray(drawingIds) && drawingIds.length) {
    apiPath += `&drawing_ids=${drawingIds.join(",")}`;
  }
  if (Array.isArray(pkgIds) && pkgIds.length) {
    apiPath += `&package_ids=${pkgIds.join(",")}`;
  }

  if (Array.isArray(jobIds) && jobIds.length) {
    apiPath += `&job_ids=${jobIds}`;
  }

  return apiCall(apiPath, method, null, null, true, true);
};

export const fetchJobSpecificCostCodes = (jobId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `jobs/cost-codes/${jobId}`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchCostCodeTypes = (includeInactive) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `cost-code-types`;
  const method = "GET";

  if (includeInactive) apiPath += `?is_active=0`;

  return apiCall(apiPath, method);
};

export const updateCostCodeType = (ids, isActive) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `cost-code-types`;
  const method = "PUT";

  const body = {
    cost_code_type_ids: ids + "",
    data: {
      is_active: isActive ? 1 : 0,
    },
  };

  return apiCall(apiPath, method, body, "Successfully updated cost code type.");
};

export const createCostCodeType = (name) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `cost-code-types`;
  const method = "POST";

  return apiCall(
    apiPath,
    method,
    [{ name: name }],
    "Successfully created cost code type."
  );
};

export const deleteCostCodeType = (id) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `cost-code-types/delete?ids=${id + ""}`;
  const method = "PUT";

  return apiCall(apiPath, method, null, "Successfully deleted cost code type.");
};

export const deletePackage = (pkgId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `packages/delete?ids=${pkgId}`;
  const method = "PUT";
  const successMessage = (res) => (res.data[0] || res.data).message;

  return apiCall(apiPath, method, null, successMessage);
};

export const archivePackage = (pkgId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `packages/archive?ids=${pkgId}`;
  const method = "PUT";
  const successMessage = (res) => (res.data[0] || res.data).message.item;

  return apiCall(apiPath, method, null, successMessage);
};

export const archiveDrawing = (drawingId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `drawings/archive/${drawingId}`;
  const method = "PUT";
  const successMessage = (res) => (res.data[0] || res.data).message;

  return apiCall(apiPath, method, null, successMessage);
};

export const deleteDrawing = (drawingIds) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `drawings/delete/${drawingIds}`;
  const method = "PUT";
  const successMessage = (res) => (res.data[0] || res.data).message;

  return apiCall(apiPath, method, null, successMessage);
};

export const archiveItem = (itemId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `items/archive?ids=${itemId}`;
  const method = "PUT";
  const successMessage = (res) => (res.data[0] || res.data).message;

  return apiCall(apiPath, method, null, successMessage);
};

export const deleteItem = (itemIds) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `items/delete`;
  const body = {
    ids: typeof itemIds !== "string" ? `${itemIds}` : itemIds,
  };
  const method = "PUT";
  const successMessage = (res) => (res.data[0] || res.data).message;

  return apiCall(apiPath, method, body, successMessage);
};

export const fetchMaterialTypes = (serviceTypeIds) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `material-types?`;

  if (serviceTypeIds && serviceTypeIds.length)
    apiPath += `service_type_ids=${serviceTypeIds.join(",")}&app_type=fab`;
  else apiPath += `app_type=fab`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchContainers = (jobIds) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `containers?is_active=1&locked=0&app_type=fab`;
  if (jobIds && jobIds.length) apiPath += `&job_ids=${jobIds.join(",")}`;

  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchEula = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "eulas";
  const method = "GET";

  return apiCall(apiPath, method);
};

export const fetchShiftNow = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "self/shiftNow";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true, true);
};

export const acceptEula = (version) => {
  const { apiCall, user_id } = utils.useApiCall();

  const apiPath = "eulas";
  const method = "PUT";
  const body = { user_id, eula_version: version };

  return apiCall(apiPath, method, body);
};

export const createShiftNow = (shift = null) => {
  const { apiCall } = utils.useApiCall();
  const apiPath = "self/shifts";
  const method = "POST";
  let body = { method: "clockIn" };

  if (shift) {
    body = {
      shift_type_id: shift.id,
      shift_start: Math.trunc(shift.start_time),
      shift_end: Math.trunc(shift.end_time),
    };
  }

  return apiCall(
    apiPath,
    method,
    body,
    shift && shift.start_time > Math.trunc(Date.now() / 1000)
      ? "Created upcoming shift."
      : "Clocked into shift."
  );
};

export const endShiftNow = (shiftId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "self/clockout";
  const method = "PUT";
  const body = { shift_id: shiftId };

  return apiCall(apiPath, method, body);
};

export const extendShift = (shiftId, endTime) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `self/shifts/${shiftId}`;
  const method = "PUT";
  const body = { shift_end: Math.trunc(parseInt(endTime) / 1000) };

  return apiCall(apiPath, method, body, "Shift extended.");
};

export const fetchLastShift = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `shifts?last_shift=true`;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const fetchPredefinedShifts = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath =
    "predefined-shifts?status=available&statusTime=" +
    Math.trunc(Date.now() / 1000);
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const clockIntoShift = (shift) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "self/shifts/" + shift.id;
  const method = "PUT";

  let { start_time, end_time, comment } = shift;

  let body = {
    shift_start: Math.trunc(start_time),
  };

  if (end_time) Object.assign(body, { shift_end: Math.trunc(end_time) });
  if (comment) Object.assign(body, { comment });

  return apiCall(apiPath, method, body, "Shift updated.");
};

export const fetchPartners = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "partners?status=available";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchPrimaryPartnerSessions = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "self/partners/shifts";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const createPrimaryPartnerSession = (partnerId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "self/partners";
  const method = "POST";
  const body = { partner_id: partnerId };

  return apiCall(apiPath, method, body, "Started partnership.");
};

export const fetchMyPartnerSessions = () => {
  const { apiCall, user_id } = utils.useApiCall();
  const testUser = 1; // used only to run React tests
  const apiPath = `partners/${user_id || testUser}?is_active=1`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const endPrimaryPartnerSession = (partnerId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "self/partners";
  const method = "PUT";
  const body = { callAction: "remove", callParams: { partner_id: partnerId } };

  return apiCall(apiPath, method, body, "Ended partnership.");
};

export const archiveJob = (jobId) => {
  const { apiCall } = utils.useApiCall();

  // PATH DOESNT MATCH SWAGGER
  const apiPath = `jobs/archive?ids=${jobId}`;
  const method = "PUT";
  const successMessage = (res) => (res.data[0] || res.data).message;

  return apiCall(apiPath, method, null, successMessage);
};

export const deleteJob = (jobId) => {
  const { apiCall } = utils.useApiCall();

  // PATH DOESNT MATCH SWAGGER
  const apiPath = `jobs/delete?ids=${jobId}`;
  const method = "PUT";
  const successMessage = (res) => (res.data[0] || res.data).message;

  return apiCall(apiPath, method, null, successMessage);
};

export const fetchFutureShift = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "self/shifts";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const createItemsViaCsv = (job_id, package_id, items) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "items/csv";
  const method = "POST";

  const body = {
    app_type: "fab",
    job_id,
    package_id,
    data: items,
  };

  return apiCall(apiPath, method, body, "Successfully created work items.");
};

export const createItems = (job_id, package_id, items) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "items";
  const method = "POST";

  const body = {
    app_type: "fab",
    origin_source: "manual",
    job_id,
    package_id,
    data: items,
  };

  return apiCall(apiPath, method, body, "Successfully created work items.");
};

export const fetchWorkItemCategories = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "work-item-categories";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const acknowledgeShiftEnd = (shiftId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "self/shifts/" + shiftId;
  const method = "PUT";
  const body = { warned: 1 };

  return apiCall(apiPath, method, body, null, true);
};

export const fetchFlowMap = (flowId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "work-flows/map-link?app_type=fab&id=" + flowId;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const saveFlowMap = (newFlowMap) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "work-flows/maps-links";
  const method = "POST";
  const body = {
    app_type: "fab",
    data: newFlowMap,
  };

  return apiCall(
    apiPath,
    method,
    body,
    "Successfully saved work flow. This change may take several minutes to take affect on existing work items."
  );
};

export const updateJointHeatNumber = (heatNumberObj) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `items/joint-heat-number`;
  const method = "PUT";

  const { work_item_ids, position, heat_number } = heatNumberObj;

  const body = {
    work_item_ids: work_item_ids.join(","),
    position: parseInt(position),
    heat_number,
  };

  return apiCall(
    apiPath,
    method,
    body,
    "Successfully updated joint heat number."
  );
};

export const fetchItemHeatNumbers = (
  type,
  jobIds,
  size,
  materialType,
  drawingIds
) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `items/heat-numbers?`;

  if (type === "JOINT") {
    apiPath += `drawing_ids=${drawingIds.join(",")}`;
  } else {
    apiPath += `size=${size}&material_type_name=${materialType}&job_ids=${jobIds.join(
      ","
    )}`;
  }

  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const saveFlow = (updatedFlow) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "work-flows";
  const method = "PUT";
  const body = updatedFlow;

  return apiCall(
    apiPath,
    method,
    body,
    "Successfully updated flow. This change may take several minutes to take affect on existing work items."
  );
};

export const fetchConditions = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "conditions";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchWorkItemColumns = (onlyVisible = false, table_target) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "columns";
  const method = "GET";

  if (onlyVisible) apiPath += "?visible=1";
  if (table_target)
    apiPath += `${onlyVisible ? "&" : "?"}table_target=${table_target}`;

  return apiCall(apiPath, method, null, null, true);
};

export const saveCustomColumn = (name, dataType, tableTarget) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "columns";
  const method = "POST";
  const body = [
    {
      display_name: name,
      data_type: dataType,
      table_target: tableTarget,
    },
  ];

  return apiCall(apiPath, method, body, "Successfully created custom column.");
};

export const fetchCustomColumnDataPerItem = (
  tableTarget,
  itemId,
  format = "array"
) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `${tableTarget}/${itemId}/columns?format=${format}`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchCustomColumnDataMultiItem = (
  tableTarget,
  itemIdsArray,
  format = "hashmap"
) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `${tableTarget}/${itemIdsArray.join(
    ","
  )}/columns?format=${format}`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const updateCustomColumn = (id, updatedName) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `columns/${id}`;
  const method = "PUT";

  const body = {
    display_name: updatedName,
  };

  return apiCall(apiPath, method, body, "Successfully updated custom column.");
};

export const updateCustomColumnData = (
  tableTarget,
  itemId,
  columnId,
  updatedValue
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `${tableTarget}/${itemId}/columns/${columnId}`;
  const method = "PUT";
  const body = {
    data: updatedValue,
  };

  return apiCall(apiPath, method, body, "Successfully updated custom column.");
};

export const updateManyCustomColumnData = (tableTarget, data) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `${tableTarget}/columns`;
  const method = "PUT";
  const body = { data };

  return apiCall(
    apiPath,
    method,
    body,
    "Successfully updated custom column(s)"
  );
};

export const deleteCustomColumn = (id) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `columns/${id}`;
  const method = "DELETE";

  return apiCall(apiPath, method, null, "Successfully deleted custom column.");
};

export const saveCondition = (conditionObj) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "conditions";
  const method = "POST";
  const body = {
    comparison: conditionObj.comparison,
    value: conditionObj.condition || null,
    work_item_column_id: conditionObj.filter,
  };

  return apiCall(apiPath, method, body, "Successfully created condition.");
};

export const fetchWorkStageConditions = (workStageId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "conditions/stage/" + workStageId;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const saveWorkStageConditions = (conditions) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "conditions/stage";
  const method = "POST";
  const body = conditions;

  return apiCall(
    apiPath,
    method,
    body,
    "Successfully updated stage conditions.",
    (res) => {
      if (res && res.error && res.error.status === 404) {
        store.dispatch(
          notify({
            id: Date.now(),
            type: "SUCCESS",
            message: "Successfully updated stage conditions.",
          })
        );
        return true;
      } else return false;
    }
  );
};

export const createWorkStage = (stageInfo) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "work-stages";
  const method = "POST";
  let body = {
    app_type: "fab",
    callParams: {
      data: [
        {
          name: stageInfo.name,
          stage_work_level_id: stageInfo.stageToDuplicate
            ? stageInfo.stageToDuplicate.stage_work_level_id
            : 1,
          shape: stageInfo.holdpoint ? "Diamond" : "Square",
          fab: 1,
          metric_work_item_column_id: stageInfo.metric,
          nestable: stageInfo.nestable ? 1 : 0,
          rejectable: stageInfo.rejectable ? 1 : 0,
          groupable: stageInfo.groupable ? 1 : 0,
        },
      ],
    },
  };

  return apiCall(
    apiPath,
    method,
    body,
    "Successfully created stage. Assign users to this stage in Settings > Users."
  );
};

export const fetchItemOpenConditions = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "work-flows/item-open-conditions";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const updateStageStatusGroup = (
  workStageId,
  workFlowId,
  statusGroupName,
  statusGroupColor
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "work-flows/stage-status-group";
  const method = "PUT";
  const body = {
    app_type: "fab",
    work_stage_id: workStageId,
    work_flow_id: workFlowId,
    stage_status_group_name: statusGroupName,
    stage_status_group_color: statusGroupColor,
  };

  return apiCall(apiPath, method, body, "Successfully updated status group.");
};

export const updateWorkStage = (stageInfo) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "work-stages";
  const method = "PUT";
  let body = {
    id: stageInfo.id,
    data: {
      name: stageInfo.name,
    },
  };

  if (Object.prototype.hasOwnProperty.call(stageInfo, "shape")) {
    Object.assign(body.data, { shape: stageInfo.shape });
  }

  if (Object.prototype.hasOwnProperty.call(stageInfo, "nestable")) {
    Object.assign(body.data, { nestable: stageInfo.nestable ? 1 : 0 });
  }

  if (Object.prototype.hasOwnProperty.call(stageInfo, "rejectable")) {
    Object.assign(body.data, { rejectable: stageInfo.rejectable ? 1 : 0 });
  }

  if (Object.prototype.hasOwnProperty.call(stageInfo, "groupable")) {
    Object.assign(body.data, { groupable: stageInfo.groupable ? 1 : 0 });
  }

  if (stageInfo.metric)
    Object.assign(body.data, {
      metric_work_item_column_id: stageInfo.metric,
    });

  if (
    stageInfo.flow_id &&
    Object.prototype.hasOwnProperty.call(stageInfo, "stage_code_id")
  ) {
    Object.assign(body.data, {
      flow_id: stageInfo.flow_id,
      stage_code_id: stageInfo.stage_code_id,
    });
  }

  return apiCall(
    apiPath,
    method,
    body,
    "Successfully updated stage. This change may take several minutes to take effect on existing work items."
  );
};

export const completeUncomplete = (direction, stage_id, work_item_id, all) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "items/completion";
  const method = "PUT";
  const body = {
    app_type: "fab",
    data: {
      stage_id: all ? "all" : stage_id,
      work_item_ids: `${work_item_id}`,
      direction,
    },
  };

  const successMessage = `Successfully ${
    direction === 1 ? "completed" : "uncompleted"
  } the item(s)`;

  return apiCall(apiPath, method, body, successMessage);
};

export const createWorkFlow = (flowInfo) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "work-flows";
  const method = "POST";
  let body = {};

  const duplicating = flowInfo.flow_to_duplicate;
  if (duplicating) {
    apiPath += "/duplicate";
    Object.assign(body, {
      work_flow_id: flowInfo.flow_to_duplicate,
      new_flow_name: flowInfo.name,
      default: flowInfo.default,
    });
  } else {
    body = {
      app_type: "fab",
      data: {
        name: flowInfo.name,
        default: flowInfo.default,
        fab: 1,
      },
    };
  }

  return apiCall(apiPath, method, body, "Successfully created flow.");
};

export const fetchWorkableJobs = (stageIds, costCodeIds) => {
  const { apiCall } = utils.useApiCall();
  let apiPath = `jobs/workable`;
  if (stageIds && stageIds.length)
    apiPath += `?stage_ids=${stageIds.join(",")}`;
  if (costCodeIds?.length)
    apiPath += `${stageIds?.length ? "&" : "?"}cost_code_ids=${costCodeIds.join(
      ","
    )}`;

  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchWorkablePackages = (jobIds, stageIds) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `packages/workable`;
  if (jobIds && jobIds.length) apiPath += `?job_ids=${jobIds.join(",")}`;
  if (stageIds && stageIds.length)
    apiPath += `${jobIds && jobIds.length ? "&" : "?"}stage_ids=${stageIds.join(
      ","
    )}`;

  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchWorkableDrawings = (jobIds, packageIds, stageIds) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `drawings/workable`;
  if (stageIds && stageIds.length)
    apiPath += `?stage_ids=${stageIds.join(",")}`;
  if (packageIds && packageIds.length)
    apiPath += `${
      stageIds && stageIds.length ? "&" : "?"
    }package_ids=${packageIds.join(",")}`;
  else if (jobIds && jobIds.length)
    apiPath += `${
      (packageIds && packageIds.length) || (stageIds && stageIds.length)
        ? "&"
        : "?"
    }job_ids=${jobIds.join(",")}`;

  const method = "GET";

  return apiCall(apiPath, method);
};

export const fetchWorkableStages = (jobIds, packageIds, drawingIds) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `work-stages/workable`;
  let method = "POST";
  let body = {};

  if (drawingIds && drawingIds.length)
    Object.assign(body, { drawing_ids: drawingIds.join(",") });
  else if (packageIds && packageIds.length)
    Object.assign(body, { package_ids: packageIds.join(",") });
  else if (jobIds && jobIds.length)
    Object.assign(body, { job_ids: jobIds.join(",") });

  return apiCall(apiPath, method, body, null, true);
};

export const fetchDrawingById = (id) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `drawings/${id}`;
  const method = "GET";
  return apiCall(apiPath, method);
};

export const fetchStatusTrackerStages = (type, id) => {
  const { apiCall } = utils.useApiCall();

  const apiPath =
    type === "drawings" || type === "packages"
      ? `${type}/status-tracker/${id}`
      : `status-tracker/${type}/${id}`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true, true);
};

export const changeItemWorkflow = (itemId, itemType, newWorkflow) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `${itemType}s/work-flow`;
  const method = "PUT";
  let body = {};
  if (itemType === "package") {
    body = {
      app_type: "fab",
      package_id: itemId,
      work_flow_id: newWorkflow,
    };
  } else {
    // drawings is still using old body params
    body = {
      callParams: { [`${itemType}_id`]: itemId, work_flow_id: newWorkflow },
    };
  }

  return apiCall(apiPath, method, body, "Successfully changed workflow.");
};

export const updateWorkStageColumns = (
  direction,
  stageId,
  workItemColumns,
  groupBy,
  origin = "EDIT",
  columnToPositionMap
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "work-stages/columns" + (direction === 0 ? "/delete" : "");
  const method = "PUT";
  let body = {
    app_type: "fab",
    updateData: [],
  };
  const successMessage = `Successfully ${
    direction === 0
      ? "removed"
      : groupBy !== undefined
      ? groupBy === 1
        ? "added"
        : "removed"
      : "added"
  } ${workItemColumns.length} ${
    groupBy !== undefined ? "grouping " : ""
  }columns.`;

  if (groupBy !== undefined) {
    let groupByColumns =
      typeof workItemColumns[0] === "object"
        ? (origin === "EDIT"
            ? workItemColumns.filter((c) => c.group_by)
            : workItemColumns
          ).map((c) => c.id)
        : groupBy === 1
        ? workItemColumns
        : [];
    let notGroupByColumns =
      origin === "EDIT"
        ? typeof workItemColumns[0] === "object"
          ? workItemColumns.filter((c) => !c.group_by).map((c) => c.id)
          : groupBy === 0
          ? workItemColumns
          : []
        : [];

    let tempObj = {};
    if (groupByColumns.length) {
      tempObj = {
        stage_id: stageId,
        group_by: 1,
      };
      if (direction === 0)
        tempObj.work_item_column_ids = groupByColumns.join(",");
      else tempObj.data = columnToPositionMap;
      body.updateData.push(tempObj);
    }
    if (notGroupByColumns.length) {
      tempObj = {
        stage_id: stageId,
        group_by: 0,
      };
      if (direction === 0)
        tempObj.work_item_column_ids = notGroupByColumns.join(",");
      else tempObj.data = columnToPositionMap;
      body.updateData.push(tempObj);
    }
  } else {
    body.updateData = [
      {
        stage_id: stageId,
        group_by: 0,
      },
    ];
    if (direction === 0)
      body.updateData[0].work_item_column_ids = workItemColumns
        .map((c) => c.id)
        .join(",");
    else body.updateData[0].data = columnToPositionMap;
  }

  return apiCall(apiPath, method, body, successMessage);
};

export const updateContainers = (id, data) => {
  const { apiCall } = utils.useApiCall();

  const { name, description, laydown_location } = data;

  const apiPath = `containers/${id}`;
  const method = "PUT";
  const body = {
    name,
    description,
    laydown_location,
  };

  const successMessage = `Successfully updated container.`;

  return apiCall(apiPath, method, body, successMessage);
};

export const createContainer = (
  jobId,
  drawingIds,
  stage,
  area,
  containerData
) => {
  const { apiCall } = utils.useApiCall();

  const { name, laydown_location, description } = containerData;

  const apiPath = `containers`;
  const method = `POST`;

  let body = {
    name,
    description: description || null,
    job_id: jobId,
    drawing_ids: drawingIds && drawingIds.length ? drawingIds : null,
    area: area || null,
  };

  if (stage) Object.assign(body, { stage_id: stage });

  if (laydown_location) body.laydown_location = laydown_location;

  const successMessage = `Successfully created container.`;

  return apiCall(apiPath, method, body, successMessage);
};

export const fetchWorkableItems = (
  jobIds = null,
  packageIds = null,
  drawingIds = null,
  stageIds = null,
  groupingType,
  maxGroupQuantity
) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "v2/items/workable?";
  const method = "GET";

  if (jobIds && jobIds.length) apiPath += "job_ids=" + jobIds.join(",") + "&";
  if (packageIds && packageIds.length)
    apiPath += "package_ids=" + packageIds.join(",") + "&";
  if (drawingIds && drawingIds.length)
    apiPath += "drawing_ids=" + drawingIds.join(",") + "&";
  if (stageIds && stageIds.length) apiPath += "stage_ids=" + stageIds.join(",");
  if (groupingType === "Ungrouped") apiPath += "&grouped=0";
  else if (groupingType === "Grouped") apiPath += "&grouped=1";
  else if (groupingType === "Grouped By Drawing") apiPath += "&grouped=2";
  else if (groupingType === "Advanced Grouping")
    apiPath += `&grouped=3&max_group_qty=${maxGroupQuantity}`;

  return apiCall(apiPath, method, null, null, true);
};

export const fetchAllCuttingMachines = () => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "tigerstop/machines";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const createCuttingMachine = (machineData) => {
  const { apiCall, user_id } = utils.useApiCall();

  let apiPath = "tigerstop/machines";
  const method = "POST";

  const body = {
    name: machineData.name,
    serial_number: machineData.serialNumber,
    description: machineData.description,
    type: machineData.type,
    stage_id: machineData.stageId,
    created_by: user_id,
  };

  return apiCall(apiPath, method, body, null, false);
};

export const fetchAllCuttingMachineSettings = (machineIds) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `tigerstop/settings?machine_ids=${machineIds}`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

const DEFAULT_SETTINGS = [
  {
    setting: "d10",
    value: 0,
    description: "max length",
    user_editable: 1,
    password_protected: 1,
  },
  {
    setting: "d11",
    value: 0,
    description: "min limit",
    user_editable: 1,
    password_protected: 1,
  },
  {
    setting: "no",
    value: 0,
    description: "min length",
    user_editable: 1,
    password_protected: 0,
  },
  {
    setting: "d23",
    value: 0,
    description: "kerf",
    user_editable: 1,
    password_protected: 1,
  },
  {
    setting: "d24",
    value: 0,
    description: "head cut",
    user_editable: 1,
    password_protected: 1,
  },
  {
    setting: "d25",
    value: 0,
    description: "tail cut",
    user_editable: 1,
    password_protected: 1,
  },
];

export const createUpdateCuttingMachineSettings = (
  machine,
  settings,
  useDefaultSettings = false,
  isNew = false
) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "tigerstop/settings";
  const method = "POST";

  let settingsToSend = [];
  let temp = {};

  if (useDefaultSettings) {
    settingsToSend = DEFAULT_SETTINGS;
  } else if (isNew) {
    settings.forEach((s) => {
      temp = {
        setting: s.command,
        description: s.type,
        user_editable: 1,
        password_protected: 1,
        value: s.value,
      };
      settingsToSend.push(temp);
    });
  } else {
    settings.forEach((s) => {
      temp = {
        setting: machine.settings[s.type].setting,
        description: machine.settings[s.type].description,
        user_editable: machine.settings[s.type].user_editable,
        password_protected: machine.settings[s.type].password_protected,
        value: s.value,
      };
      settingsToSend.push(temp);
    });
  }

  const body = {
    machine_id: machine.id,
    settings: settingsToSend,
  };

  return apiCall(
    apiPath,
    method,
    body,
    "Success updating machine settings",
    true
  );
};

export const fetchTableFilters = (
  area,
  viewing_all = 0,
  groupingType = 1,
  stageId = 0
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `table-filters?area=${area}&viewing_all=${viewing_all}&grouping=${groupingType}&stage_id=${stageId}`;

  const method = "GET";

  return apiCall(apiPath, method, null, null, true, true);
};

export const saveTableFilters = (
  area,
  filters,
  viewing_all = 0,
  grouping = 1,
  stage_id,
  drawing_id
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "table-filters";
  const method = "POST";
  const body = { area, viewing_all, grouping, data: filters };

  if (drawing_id) Object.assign(body, { drawing_id });
  if (stage_id) Object.assign(body, { stage_id });

  return apiCall(apiPath, method, body, null, true, true);
};

export const fetchLaydownLocations = (type = "materials") => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `laydown-locations?type=${type}`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const createLaydownLocation = (itemIds, newLocation) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "items/new-laydown-location";
  const method = "PUT";

  const body = {
    work_item_ids: itemIds.join(","),
    laydown_location_name: newLocation,
  };

  return apiCall(
    apiPath,
    method,
    body,
    "Successfully created new laydown location"
  );
};

export const fetchColumnState = (type, stageIds = null) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `ag-table/column-state?app_type=fab&table=${utils.table(type)}`;
  const method = "GET";
  if ((type === "ITEMS" || type === "MY_WORK_ITEMS") && stageIds)
    apiPath += "&work_stage_ids=" + stageIds.join(",");

  return apiCall(apiPath, method, null, null, true);
};

export const saveColumnState = (type, columnState, stageIds = null) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "ag-table/column-state";
  const method = "POST";
  let body = {
    app_type: "fab",
    table: utils.table(type),
  };

  if ((type === "ITEMS" || "MY_WORK_ITEMS") && stageIds)
    Object.assign(body, {
      columns: columnState.map((c) => {
        Object.assign(c, { work_stage_ids: stageIds.join(",") });
        return c;
      }),
    });
  else Object.assign(body, { columns: columnState });

  return apiCall(apiPath, method, body, null, true, true);
};

export const fetchSelfTimers = (activeTimers) => {
  const { apiCall } = utils.useApiCall();
  const method = "GET";
  let apiPath = `self/work-timers`;
  if (activeTimers) apiPath += `?status=running`;

  return apiCall(apiPath, method, null, null, true);
};

export const fetchAllTimers = (status = null, stageIds = null) => {
  const { apiCall } = utils.useApiCall();
  const method = "GET";
  let apiPath = `work-timers`;
  if (status && status.length) apiPath += `?status=${status}`;
  if (stageIds && stageIds.length)
    apiPath += "&stage_ids=" + stageIds.join(",");
  return apiCall(apiPath, method, null, null, true);
};

// create new timer AND add items to a running timer
export const startWorkTimer = (
  item_ids,
  shift_id,
  stage_id,
  timer_id,
  item_type = "work_item",
  is_generic = 0,
  cost_code_id = null
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `self/work-timers`;
  const method = "POST";
  const body = {
    shift_id,
    item_type,
    stage_id,
    generic: is_generic,
  };
  if (timer_id) body.timer_id = timer_id;
  if (item_ids && item_ids.length) body.item_ids = item_ids.join(",");
  if (cost_code_id) body.cost_code_id = cost_code_id;

  return apiCall(apiPath, method, body, null, false);
};

/**
 * POST v2/self/work-timers
 * Create a new timer if one is not already running for the work items
 * AND add items to a running timer IF item is available.
 *
 * Improvement - Returns error before creating a timer
 * if another user is already working on the same work-item
 * at the same stage.
 *
 * @param {*} item_ids send itemIds, comma separated, for the items to add to timer
 * @param {*} shift_id send shiftId for the active user shift
 * @param {*} stage_id send stageId for the timer
 * @param {*} timer_id if a timer exists, send timerId
 * @param {*} item_type job, package, drawing, or work-item
 * @param {*} is_generic 0 or 1
 * @param {*} cost_code_id cost code id
 * @returns user active timer or error
 */
export const startWorkTimerv2 = (
  item_ids,
  shift_id,
  stage_id,
  timer_id,
  item_type = "work_item",
  is_generic = 0,
  cost_code_id = null
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `v2/self/work-timers`;
  const method = "POST";
  const body = {
    shift_id,
    item_type,
    stage_id,
    generic: is_generic,
  };
  if (timer_id) body.timer_id = timer_id;
  if (item_ids && item_ids.length) body.item_ids = item_ids.join(",");
  if (cost_code_id) body.cost_code_id = cost_code_id;

  return apiCall(apiPath, method, body, null, true);
};

export const stopWorkTimer = (timer_id, stop_type_id = 1) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `self/work-timers`;
  const method = "PUT";
  const body = {
    timer_id,
    stop_type_id,
  };

  const successMessage = (res) => {
    const messageObj = res.data[0];
    if (messageObj) return messageObj.message;
  };

  return apiCall(apiPath, method, body, successMessage);
};

export const removeItemsFromTimer = (
  timer_id,
  item_ids,
  quantity,
  item_type = "work_item"
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `self/remove-timer-item`;
  const method = "PUT";
  const body = {
    timer_id,
    item_ids: typeof item_ids === "string" ? item_ids : item_ids.join(","),
    item_type,
  };

  if (quantity) body.quantity = quantity;

  return apiCall(apiPath, method, body, null, false);
};

export const fetchShippingBlocks = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "shipping-blocks";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchRejectionHistory = (workItemId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "items/rejection-history?work_item_ids=" + workItemId;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchThroughputs = (flowId, stageId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `work-stages/throughputs?flow_id=${flowId}&stage_id=${stageId}`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchColumnsForConditions = (stageId = null) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `${
    stageId ? "work-stages/" + stageId + "/columns" : "columns"
  }?usable_for_conditions=1`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchThroughputColumns = (stageId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `work-stages/throughput-columns?stage_id=${stageId}`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const updateThroughputColumns = (stageId, workItemColumnIds) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "work-stages/throughput-columns";
  const method = "POST";
  const body = { stage_id: stageId, work_item_column_ids: workItemColumnIds };

  return apiCall(
    apiPath,
    method,
    body,
    "Successfully updated columns for calculations."
  );
};

export const updateThroughputs = (id, staticCost, dynamicStartDate) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "throughputs";
  const method = "PUT";
  let body = { ids: id.toString() };

  if (!isNaN(staticCost)) Object.assign(body, { static_cost: staticCost });
  if (dynamicStartDate)
    Object.assign(body, { dynamic_start_date: dynamicStartDate });

  return apiCall(apiPath, method, body, "Successfully updated throughput(s).");
};

export const fetchPendingDrawingsExist = () => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "v2/drawings/pending?app_type=fab";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true, true);
};

export const resolveRejection = (workItemId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "items/rejection-resolution";
  const method = "PUT";
  const body = { work_item_ids: `${workItemId}` };

  return apiCall(apiPath, method, body, "Resolved rejection.");
};

export const fetchRejectionCategories = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `rejection-category`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchJoiningProcedures = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "joining-procedures";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const createJoiningProcedure = (name) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "joining-procedures";
  const method = "POST";

  const body = {
    name,
    isogrid: 0,
  };

  return apiCall(
    apiPath,
    method,
    body,
    "Successfully created joining procedure."
  );
};

export const updateCondition = (conditionId, callAction) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "conditions";
  const method = "PUT";
  const body = { condition_id: conditionId, call_action: callAction };

  return apiCall(apiPath, method, body, {
    type: "WARN",
    message: "Condition deleted. Will need to remove it from existing stages.",
  });
};

export const fetchFlowCodes = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "flow-codes";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchSentBackStages = (workItemIds, stageId = null) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `items/sent-back-stages/fetch`;
  const method = "POST";

  const body = {
    work_item_ids: workItemIds.toString(),
    stage_id: stageId,
  };

  return apiCall(apiPath, method, body, null);
};

export const fetchWorkStageGroupableColumns = (stageId, groupBy) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `work-stages/${stageId}/columns?groupable=1`;
  if (groupBy) apiPath += `&group_by=1`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const createFlowCode = (name) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "flow-codes";
  const method = "POST";
  const body = { flow_code_name: name };

  return apiCall(apiPath, method, body, null, true);
};

export const rejectItems = (
  itemId,
  rejectionCategoryId,
  type,
  currentStageId,
  sentBackStageId,
  description,
  workTimerId,
  quantity
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "items/reject";
  const method = "PUT";
  let body = {
    app_type: "fab",
    item_ids: "" + itemId,
    rejection_category_id: rejectionCategoryId,
    type,
  };
  const successMessage = "Successfully rejected item.";

  if (description) Object.assign(body, { description });
  if (currentStageId !== 0)
    Object.assign(body, { current_stage_id: currentStageId });
  if (sentBackStageId)
    Object.assign(body, { sent_to_stage_id: sentBackStageId });
  if (workTimerId) Object.assign(body, { work_timer_id: workTimerId });
  if (quantity) Object.assign(body, { quantity });

  return apiCall(apiPath, method, body, successMessage);
};

export const checkReleasesPageNotification = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "self/releases-page-notification";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true, true);
};

export const fetchStageCodes = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "stage-codes";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const createStageCode = (name) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "stage-codes";
  const method = "POST";
  const body = { stage_code_name: name };

  return apiCall(apiPath, method, body, null, true);
};

export const editGroupedItems = (groupIds, data, quantity) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `items/edit-grouped-items`;
  const method = "PUT";

  const body = {
    group_ids: groupIds,
    quantity,
  };

  if (data) body.data = { ...data };

  const successMessage = `Successfully updated grouped items.`;

  return apiCall(apiPath, method, body, successMessage);
};

export const createMaterialType = (materialName) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "material-types";
  const method = "POST";

  const body = {
    app_type: "fab",
    material_type_name: materialName,
  };

  return apiCall(apiPath, method, body, "Successfully created material type.");
};

export const assignUnassignUser = (
  action,
  itemId,
  userId = null,
  level,
  crewId = null
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `users`;
  const method = "PUT";

  const body = {
    callAction: action,

    callParams: {
      itemId,
      level,
    },
  };

  if (crewId) body.callParams.crew_id = crewId;
  if (userId) body.callParams.userId = userId;
  // if (roleId) body.callParams.role_id = roleId

  const successMessage =
    action === "assignTo"
      ? `Successfully assigned to ${level}(s)`
      : `Successfully unassigned from ${level}(s)`;

  return apiCall(apiPath, method, body, successMessage);
};

export const fetchUsersAssignedToItem = (itemId, level, roleId) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `users/assignments?item_id=${itemId}&item_level=${level}`;
  if (roleId) apiPath += `&role_id=${roleId}`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const sendMaterialReport = (
  recipients,
  subject,
  message,
  packageId,
  packageName,
  packageNumber,
  isGrouped,
  sendResolved
) => {
  let apiPath = `ajax/notifications/send_notification.php`;

  let body = {
    notification: "MatRejectReport",
    type: "MatRejectReport",
    recipients: recipients,
    package_id: packageId,
    subject: `${packageName} - ${packageNumber} Item Rejection Report`,
    grouping: isGrouped,
    send_resolved: sendResolved,
  };

  if (subject) body.subject = subject;
  if (message) body.message = message;

  return axios
    .post(`${process.env.REACT_APP_FABPRO}/${apiPath}`, body)
    .then((res) => res.data);
};

export const fetchItemRejectionsByPackage = (
  packageId,
  isGrouped,
  sendResolved
) => {
  // send_resolved - 1 for full history, 0 for unresolved history
  const { apiCall } = utils.useApiCall();

  const apiPath = `packages/rejection-report/${packageId}?grouped=${isGrouped}&app_type=fab&send_resolved=${sendResolved}`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchJobById = (id) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "jobs/" + id + "?app_type=fab";
  const method = "GET";

  return apiCall(apiPath, method);
};

export const fetchAvailableStages = (workItemId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "items/" + workItemId + "/available-stages";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true, true);
};

export const fetchTableViewSettings = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "self/table-view-settings";
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const updateTableViewSettings = (
  viewingAll = null,
  myWorkViewingAll = null,
  myWorkStageId = 0,
  groupingTypeId = 1,
  packagesPendingApprovalViewingAll = null,
  drawingsPendingApprovalViewingAll = null
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "self/table-view-settings";
  const method = "PUT";
  let body = {};
  if (viewingAll !== null) {
    Object.assign(body, { viewing_all: viewingAll });
  } else if (myWorkViewingAll !== null) {
    Object.assign(body, {
      my_work_viewing_all: myWorkViewingAll,
      my_work_stage_id: myWorkStageId,
      grouping_type_id: groupingTypeId,
    });
  } else if (packagesPendingApprovalViewingAll !== null) {
    Object.assign(body, {
      packages_pending_approval_viewing_all: packagesPendingApprovalViewingAll,
    });
  } else if (drawingsPendingApprovalViewingAll !== null) {
    Object.assign(body, {
      drawings_pending_approval_viewing_all: drawingsPendingApprovalViewingAll,
    });
  }

  return apiCall(apiPath, method, body, null, true, true);
};

export const deleteFlow = (flowId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "work-flows/delete";
  const method = "PUT";
  const body = { work_flow_id: flowId };

  return apiCall(apiPath, method, body, "Successfully deleted flow.");
};

export const deleteContainer = (containerId, stageId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `containers/${containerId}-${stageId}`;
  const method = "DELETE";

  return apiCall(apiPath, method, null, "Successfully deleted container.");
};

export const saveSortState = (
  columnName,
  sortMethod,
  type,
  grouping,
  stageIds = null
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "ag-table/sort-state";
  const method = "POST";
  const body = {
    app_type: "fab",
    table: utils.table(type),
    sorting_column_name: columnName,
    sorting_method: sortMethod,
    grouping: grouping ? grouping : 1,
    stage_ids: stageIds,
  };

  return apiCall(apiPath, method, body, null, true, true);
};

export const fetchSortState = (type, grouping, stageIds = null) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `ag-table/sort-state?app_type=fab&table=${utils.table(
    type
  )}&grouping=${grouping ? grouping : 1}`;
  const method = "GET";

  if (stageIds) {
    apiPath += `&stage_ids=${stageIds}`;
  }

  return apiCall(apiPath, method, null, null, true);
};

export const deleteStage = (stageId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "work-stages/delete";
  const method = "PUT";
  const body = {
    app_type: "fab",
    stage_ids: `${stageId}`,
  };

  return apiCall(apiPath, method, body, "Successfully deleted stage.");
};

export const fetchForgeModelInfo = (id) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `forge/${id}`;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const fetchForgeModelsByJob = (id) => {
  const { apiCall, system_features } = utils.useApiCall();

  const apiPath = `forgev2?job_id=${id}`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true, true);
};

export const createNewSheet = (multipartFormData) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `drawings/new-pdf`;
  const method = "PUT";
  const body = multipartFormData;

  return apiCall(apiPath, method, body, "Successfully created sheet.");
};

export const savePdfAnnotations = (multipartFormData) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "pdf-annotations";
  const method = "POST";
  const body = multipartFormData;

  return apiCall(apiPath, method, body, null, null, false, false, null, 0);
};

export const fetchAnnotations = (itemId, itemType) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "pdf-annotations";
  const method = "GET";

  switch (itemType) {
    case "DRAWING":
      apiPath += `?drawing_id=${itemId}`;
      break;
    case "WORK_ITEM":
      apiPath += `?drawing_id=${itemId}`;
      break;
    default:
      return Promise.resolve({ error: true });
  }
  return apiCall(apiPath, method, null, null, true);
};

export const fetchPdfUserPrefs = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "pdf-annotations/user-preferences?app_type=web";
  const method = "GET";

  return apiCall(apiPath, method);
};

export const savePdfUserPrefs = (userPrefs) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "pdf-annotations/user-preferences";
  const method = "POST";
  const body = { ...userPrefs, app_type: "web" };

  return apiCall(apiPath, method, body);
};

export const fetchSystemSettings = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "system-settings";
  const method = "GET";

  return apiCall(apiPath, method);
};

export const duplicateDrawing = (drawingId, qty = 1) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "drawings/duplicate/" + drawingId;
  const method = "POST";
  const body = { duplicate: qty };

  return apiCall(apiPath, method, body, "Successfully duplicated drawing.");
};

export const createDrawingsViaPdfs = (pkgId, files = []) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "drawings";
  const method = "POST";
  const body = new FormData();

  body.append("creation_source", "web");
  body.append("package_id", pkgId);

  for (let i = 0; i < files.length; i++) {
    body.append("files", files[i]);
  }

  return apiCall(apiPath, method, body, "Successfully created drawing(s).");
};

export const createPackages = (jobId, packagesData = []) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "packages";
  const method = "POST";
  const body = {
    app_type: "fab",
    callParams: {
      data: packagesData.map((p) => ({
        job_id: jobId,
        package_name: p.package_name,
        number: p.number || null,
        due_date: p.due_date,
        description: p.description || null,
        budget_hours: p.budget_hours || null,
        work_flow_id: p.work_flow_id,
        area: p.area || null,
        exclude_float_mode: p.exclude_float_mode || 0,
      })),
    },
  };

  const customErrorHandler = (res, callback) => {
    if (res[1].length) {
      callback(res[1].map((o) => `Row ${o.row_idx}: ${o.message}`).join(", "));
    }
  };

  return apiCall(
    apiPath,
    method,
    body,
    "Successfully created package(s).",
    false,
    false,
    customErrorHandler
  );
};

export const updateJobCostCodes = (
  jobId,
  action,
  costCodes = [],
  addedViaCreation = false
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `jobs/cost-codes/${jobId}`;
  const method = "PUT";
  const body = { action, data: costCodes };
  const customErrorHandler = (res, callback) => {
    if (res[1] && res[1].length) {
      callback(res[1].map((o) => `Row ${o.row_idx}: ${o.message}`).join(", "));
    }
  };

  let successMessage;
  switch (action) {
    case "add":
      successMessage = "Successfully added cost code(s) to job.";
      break;
    case "update":
      successMessage = "Successfully updated cost code.";
      break;
    default:
      successMessage = "Successfully updated cost code.";
  }

  return apiCall(
    apiPath,
    method,
    body,
    addedViaCreation ? null : successMessage,
    false,
    false,
    action === "add" ? customErrorHandler : null
  );
};

export const updatePackageCostCodes = (pkgId, action, costCodes = []) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `packages/cost-codes/${pkgId}`;
  const method = "PUT";
  const body = { action, cost_code_ids: costCodes.join(",") };

  return apiCall(apiPath, method, body, (res) => res.data.message);
};

export const createCostCodes = (data) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "cost-codes";
  const method = "POST";
  const body = data;

  const customErrorHandler = (res, callback) => {
    if (res[1].length) {
      callback(res[1].map((o) => `Row ${o.row_idx}: ${o.message}`).join(", "));
    }
  };

  return apiCall(
    apiPath,
    method,
    body,
    "Successfully created cost code(s).",
    false,
    false,
    customErrorHandler
  );
};

export const updateGeneralCostCode = (id, newData) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "cost-codes";
  const method = "PUT";
  const body = {
    cost_code_ids: id + "",
    data: [newData],
  };

  return apiCall(apiPath, method, body, "Successfully updated cost code.");
};

export const deleteGeneralCostCode = (id) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `cost-codes/delete?ids=${id}`;
  const method = "PUT";

  return apiCall(apiPath, method, null, "Successfully removed cost code.");
};

export const updatePackageMap = (pkgId, jobId, file) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `packages/package-map/${pkgId}`;
  const method = "PUT";
  let body = new FormData();

  body.append("job_id", jobId);
  body.append("file", file);

  return apiCall(apiPath, method, body, "Successfully updated package map.");
};

export const createJob = ({
  job_name,
  job_number,
  target_date,
  address,
  city,
  state,
  zip,
  budget_hours,
  exclude_float,
}) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "jobs";
  const method = "POST";
  let dataObj = { job_name, job_number };

  if (target_date) Object.assign(dataObj, { target_date });
  if (address) Object.assign(dataObj, { address });
  if (city) Object.assign(dataObj, { city });
  if (state) Object.assign(dataObj, { state });
  if (zip) Object.assign(dataObj, { zip });
  if (budget_hours) Object.assign(dataObj, { budget_hours });
  if (exclude_float) Object.assign(dataObj, { exclude_float });

  const body = {
    app_type: "fab",
    data: [dataObj],
  };

  return apiCall(
    apiPath,
    method,
    body,
    "Job was successfully created. Don't forget to assign personnel!"
  );
};

export const fetchCrews = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "crews";
  const method = "GET";

  return apiCall(apiPath, method);
};

export const updateCrewAssignments = (
  userIds,
  crewId,
  direction,
  all,
  updateUserAssignments
) => {
  const { apiCall, system_features } = utils.useApiCall();

  const apiPath = "crews/assignments";
  const method = "PUT";
  let body = {
    crew_id: crewId,
    direction,
    update_user_assignments: updateUserAssignments,
  };

  if (userIds) Object.assign(body, { user_ids: userIds });
  if (all !== undefined && all !== null) Object.assign(body, { all });
  const successMessage = system_features.includes(49)
    ? "Successfully updated crew. Work flow assignment changes may take several minutes to take effect."
    : null;
  return apiCall(apiPath, method, body, successMessage);
};

export const updateCrewInfo = (crewId, field, newValue) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `crews/${crewId}`;
  const method = "PUT";
  const body = { [field]: newValue };

  const formattedField = field === "role_id" ? "role" : field;

  return apiCall(
    apiPath,
    method,
    body,
    `Successfully updated crew ${formattedField}.`
  );
};

export const archiveCrew = (crewId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `crews/archive/${crewId}`;
  const method = "PUT";

  return apiCall(apiPath, method, null, "Successfully archived crew.");
};

export const createCrew = (name, roleId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "crews";
  const method = "POST";
  const body = { name, role_id: roleId || 0 };

  return apiCall(
    apiPath,
    method,
    body,
    "Successfully created new crew. Don't forget to assign personnel!"
  );
};

export const submitRevisedPdf = (drawingId, file) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `drawings/upload-revision/${drawingId}`;
  const method = "PUT";
  let body = new FormData();

  body.append("file", file);

  return apiCall(
    apiPath,
    method,
    body,
    "Successfully uploaded the revised PDF.",
    false,
    false,
    (res, callback) => {
      if (res.error)
        callback(
          /mimetype/.test(res.error.message)
            ? "Uploaded file must be a PDF."
            : res.error.message
        );
    }
  );
};

export const fetchPackagesPendingApproval = (isAssigned) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "packages/pending-approval";
  const method = "GET";

  if (isAssigned) apiPath += "?is_assigned=1";

  return apiCall(apiPath, method);
};

export const approvePackagesPendingApproval = (
  packageIds = [],
  resetDrawings = false
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "packages/approve-work";
  const method = "PUT";
  const body = {
    ids: packageIds.join(","),
    reset_drawings: resetDrawings ? 1 : 0,
  };

  return apiCall(apiPath, method, body, (res) =>
    /^[1-9]/.test(res.data.succeeded)
      ? {
          type: "SUCCESS",
          message: `${res.data.succeeded}\n${res.data.failed}`,
        }
      : { type: "ERROR", message: res.data.failed }
  );
};

export const sendBackPackagesPendingApproval = (
  packageIds = [],
  emailInfo = {}
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "packages/send-back";
  const method = "PUT";
  const body = {
    ids: packageIds.join(","),
    email_info: emailInfo,
  };

  return apiCall(apiPath, method, body, (res) =>
    res.data.state === "success"
      ? { type: "SUCCESS", message: res.data.message }
      : { type: "ERROR", message: "Failed to send back packages." }
  );
};

export const fetchPackagesSentBack = (isAssigned) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "packages/sent-back";
  const method = "GET";

  if (isAssigned) apiPath += "?is_assigned=1";

  return apiCall(apiPath, method);
};

export const resubmitPackagesSentBack = (packageIds = [], emailInfo = {}) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "packages/resubmit";
  const method = "PUT";
  const body = {
    ids: packageIds.join(","),
    email_info: emailInfo,
  };

  return apiCall(apiPath, method, body, (res) =>
    res.data.state === "success"
      ? { type: "SUCCESS", message: res.data.message }
      : { type: "ERROR", message: "Failed to resubmit packages." }
  );
};

export const fetchDrawingsPendingApproval = (packageId, isAssigned) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "drawings/pending-approval";
  const method = "GET";

  let queryArr = ["app_type=fab"];
  if (packageId) queryArr.push(`package_id=${packageId}`);
  if (isAssigned) queryArr.push("is_assigned=1");
  else queryArr.push("is_assigned=0");

  if (queryArr.length) apiPath += "?" + queryArr.join("&");

  return apiCall(apiPath, method);
};

export const approveDrawingsPendingApproval = (
  drawingIds = [],
  resetWork = false
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "drawings/approve-work";
  const method = "PUT";
  const body = {
    drawing_ids: drawingIds.join(","),
    reset_work: resetWork ? 1 : 0,
  };

  return apiCall(apiPath, method, body, (res) => {
    const succeeded = res.data.map((o) => o.drawing_id);
    const failed = drawingIds.filter(
      (d) => !res.data.find((o) => o.drawing_id === d)
    );

    return {
      type: "SUCCESS",
      message: `${succeeded.length} Drawings approved.\n${failed.length} Drawings failed to be approved.`,
    };
  });
};

export const fetchDrawingsSentBack = (packageId, isAssigned) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "drawings/sent-back-list";
  const method = "GET";

  let queryArr = ["app_type=fab"];
  if (packageId) queryArr.push(`package_id=${packageId}`);
  if (isAssigned) queryArr.push("is_assigned=1");
  else queryArr.push("is_assigned=0");

  if (queryArr.length) apiPath += "?" + queryArr.join("&");

  return apiCall(apiPath, method);
};

export const resubmitDrawingsSentBack = (drawingIds = [], emailInfo = {}) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "drawings/resubmit";
  const method = "PUT";
  const body = {
    drawing_ids: drawingIds.join(","),
    email_info: emailInfo,
  };

  return apiCall(apiPath, method, body, (res) =>
    res.data.state === "success"
      ? { type: "SUCCESS", message: res.data.message }
      : { type: "ERROR", message: "Failed to resubmit drawings." }
  );
};

export const sendBackDrawingsPendingApproval = (
  drawingIds = [],
  emailInfo = {}
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "drawings/send-back";
  const method = "PUT";
  const body = {
    drawing_ids: drawingIds.join(","),
    email_info: emailInfo,
  };

  return apiCall(apiPath, method, body, (res) =>
    res.data.state === "success"
      ? { type: "SUCCESS", message: res.data.message }
      : { type: "ERROR", message: "Failed to send back drawings." }
  );
};

export const moveDrawingsBetweenPackages = (
  drawingIds = [],
  targetPackageId
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `drawings/move-between-packages`;
  const method = "PUT";
  const body = {
    drawing_ids: drawingIds.join(","),
    target_package_id: targetPackageId,
  };

  const handleResponse = (res) =>
    res.error
      ? { type: "ERROR", message: res.error.message }
      : {
          type: "SUCCESS",
          message:
            "Drawing flow and assignments have been updated to inherit from new package.",
        };

  return apiCall(apiPath, method, body, handleResponse);
};

export const fetchHomePageOptions = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `system-settings/home-pages`;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const fetchUserSettings = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `self/settings`;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const updateUserSettings = (expandMenu, homePageId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `self/updateSettings`;
  const method = "PUT";
  let body = {};

  if (typeof expandMenu === "number") body.expand_menu = expandMenu;
  if (homePageId) body.home_page_id = homePageId;

  return apiCall(apiPath, method, body, "Successfully updated user settings.");
};

export const updateProfilePhoto = (file) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `self/profile-photo`;
  const method = "POST";

  let body = new FormData();

  body.append("profile_photo", file);

  return apiCall(apiPath, method, body, "Successfully updated profile photo.");
};

export const deleteProfilePhoto = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `self/profile-photo/delete`;
  const method = "PUT";

  return apiCall(apiPath, method, null, "Successfully removed profile photo.");
};

export const fetchNotifications = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `notifications`;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const fetchUserNotifications = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `self/notifications`;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const updateNotificationSubscriptions = (action, notificationIds) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `self/notifications`;
  const method = "PUT";

  const body = {
    callAction: action,
    notification_ids: notificationIds,
  };

  return apiCall(
    apiPath,
    method,
    body,
    "Successfully updated notification subscription."
  );
};

export const updatePassword = (password) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `self/password`;
  const method = "PUT";

  const body = { password };

  return apiCall(apiPath, method, body, "Successfully updated password.");
};

export const resetPassword = (userId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `users/${userId}/reset-password`;
  const method = "PUT";

  return apiCall(apiPath, method, null, `Successfully reset password`);
};

export const fetchUserTitles = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `users/titles`;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const fetchTiers = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `tiers`;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const fetchUserStageAssignments = (userId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `work-stages/user-assignments/${userId}`;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const updateUserStageAssignments = (
  userId,
  stageIds,
  action = "ASSIGN"
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `work-stages/user-assignments/${userId}`;
  const method = "PUT";

  const body = {
    stage_ids: stageIds,
    action: action.toLowerCase(),
  };

  return apiCall(
    apiPath,
    method,
    body,
    `Successfully updated stage assignments.`
  );
};

export const fetchUserPermissions = (userId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `users/${userId}/permissions`;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const updateUserPermissions = (
  userId,
  permissionIds,
  action = "ASSIGN"
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `users/${userId}/permissions`;
  const method = "PUT";

  const body = {
    permission_ids: permissionIds,
    action: action.toLowerCase(),
  };

  return apiCall(
    apiPath,
    method,
    body,
    `Successfully updated user permissions.`
  );
};

export const deleteUsers = (userIds) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `users/delete/${userIds}`;
  const method = `PUT`;
  const body = {
    callAction: "delete",
  };

  return apiCall(apiPath, method, body, `Successfully deleted user.`);
};

export const resetUserPermissions = (userId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `users/${userId}/permissions/reset`;
  const method = "PUT";

  return apiCall(apiPath, method, null, `Successfully reset user permissions.`);
};

export const fetchPackagesByCrew = (crewId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `crews/${crewId}/package-assignments`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchProducts = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `products`;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const fetchPlatformVersions = () => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `products/platform-versions`;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const fetchUserProducts = (userId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `users/${userId}/products`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const updateUserProducts = (userId, productIds, action) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `users/${userId}/products`;
  const method = "PUT";

  const body = {
    product_ids: productIds.join(","),
    action,
  };

  return apiCall(
    apiPath,
    method,
    body,
    `Successfully updated user's assigned products.`
  );
};

export const createDrawingsMAJMap = (drawings, jobId, file) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = "drawings/upload-majs";
  const method = "POST";
  let body = new FormData();

  body.append(
    "data",
    JSON.stringify(
      drawings.map((d) => ({ drawing_id: d.drawing_id, filename: file.name }))
    )
  );
  body.append("job_id", jobId);
  body.append("files", file);

  return apiCall(apiPath, method, body, "Successfully uploaded MAJs.");
};

export const fetchDrawingsMAJMap = (drawingIds = null, packageId = null) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = "drawings/majs?";
  const method = "GET";

  let query = [];
  if (drawingIds && drawingIds.length)
    query.push(`drawing_ids=${drawingIds.join(",")}`);

  if (packageId) query.push(`package_id=${packageId}`);

  apiPath += query.join("&");

  return apiCall(apiPath, method);
};
export const fetchAutodeskAccounts = (is_active) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `autodesk/accounts`;
  if (is_active !== undefined) apiPath += `?is_active=${is_active}`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const saveAutodeskAccount = (accountGuid, isDefault = 0) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `autodesk/accounts`;
  const method = "POST";
  const body = {
    account_guid: accountGuid,
    default: isDefault,
  };

  return apiCall(apiPath, method, body, "Successfully saved Autodesk Account.");
};

export const updateAutodeskAccounts = (accountIds, archived, isDefault) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `autodesk/accounts/${accountIds}`;
  const method = "PUT";

  const body = {};
  if (archived) body.archived = archived;
  if (isDefault) body.default = 1;

  return apiCall(apiPath, method, body, "Succesfully updated Autodesk Account");
};

export const fetchAssociatedJobs = (accountId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `autodesk/accounts/${accountId}/jobs`;
  const method = "GET";

  return apiCall(apiPath, method, null, null);
};

export const duplicateStage = (stageId, newStageName) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `work-stages/${stageId}/duplicate?new_stage_name=${newStageName}`;
  const method = "POST";

  return apiCall(apiPath, method, null, `Successfully duplicated stage.`);
};

export const fetchWorkflowAssignments = (workFlowId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `work-flows/${workFlowId}/assignments`;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const assignWorkflow = (
  workflowId,
  userIds,
  crewIds,
  updatePastAssignments
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `work-flows/${workflowId}/assignments`;
  const method = "POST";
  const body = {
    retroactive_assignment: updatePastAssignments,
  };

  if (!!userIds) Object.assign(body, { user_ids: userIds });
  if (!!crewIds) Object.assign(body, { crew_ids: crewIds });

  // api returns array of all workflow assignments, set custom success mes
  return apiCall(
    apiPath,
    method,
    body,
    `Successfully assigned ${!!userIds ? "user" : "crew"} to workflow.`,
    false,
    false
  );
};

export const unassignWorkflow = (
  workflowId,
  userIds,
  crewIds,
  updatePastAssignments
) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `work-flows/${workflowId}/assignments`;
  const method = "DELETE";
  const body = {
    retroactive_assignment: updatePastAssignments,
  };

  if (!!userIds) Object.assign(body, { user_ids: userIds });
  if (!!crewIds) Object.assign(body, { crew_ids: crewIds });

  // api returns array of all workflow assignments, set custom success mes
  return apiCall(
    apiPath,
    method,
    body,
    `Successfully unassigned ${!!userIds ? "user" : "crew"} from workflow.`,
    false,
    false
  );
};

export const fetchDashboardFiltersList = () => {
  const { apiCall } = utils.useApiCall();
  let apiPath = `/filters/jobs?level=2&include_archived=1`;
  const method = "GET";
  return apiCall(apiPath, method, null, null, true);
};

/**
 * @function fetchIsApprovalExists
 * @description Fetches a boolean indicating whether any packages are pending approval.
 * @returns {Promise.<boolean>} A promise resolving to true if there are packages pending approval, false otherwise.
 */
export const fetchIsApprovalExists = () => {
  const { apiCall } = utils.useApiCall();
  const apiPath = `packages/pending-approval/exists`;
  return apiCall(apiPath, "GET");
};

/**
 * Fetches job summary data for the dashboard analytics page
 * @returns {Promise} Resolves with job summary data
 */
export const fetchDashboardJobSummary = (filters = {}) => {
  const { apiCall } = utils.useApiCall();
  let apiPath = `/dashboard/job-summary`;
  const method = "POST";

  return apiCall(apiPath, method, filters, null, true, true);
};

export const fetchDashboardHourOverview = (payload = {}) => {
  const { apiCall } = utils.useApiCall();
  let apiPath = `dashboard/hours-overview`;
  const method = "POST";

  return apiCall(apiPath, method, payload, null, true, true);
};

/**
 * Fetches workflow stages summary data for the dashboard analytics page
 * @returns {Promise} Resolves with job summary data
 */
export const fetchDashboardWorkFlowStageSummary = (filters = {}) => {
  const { apiCall } = utils.useApiCall();
  let apiPath = `/dashboard/workflow-stage-summary`;
  const method = "POST";

  return apiCall(apiPath, method, filters, null, true, true);
};

/**
 * Fetches metric summary data for the dashboard analytics page
 * @returns {Promise} Resolves with metric overview grid data...
 */
export const fetchDashboardJobMetricOverview = (filters = {}) => {
  const { apiCall } = utils.useApiCall();
  let apiPath = `/dashboard/metric-overview`;
  const method = "POST";

  return apiCall(apiPath, method, filters, null, true, true);
};

/**
 * Fetches workflow stages summary data for the dashboard analytics page
 * @returns {Promise} Resolves with job summary data
 */
export const fetchDashboardJobMaterialOverview = (filters = {}) => {
  const { apiCall } = utils.useApiCall();
  let apiPath = `/dashboard/material-overview`;
  const method = "POST";

  return apiCall(apiPath, method, filters, null, true, true);
};

export const fetchDrawingFiles = (drawingIds, fileType = "original") => {
  const { apiCall } = utils.useApiCall();
  const apiPath = `drawings/files`;
  const method = "POST";
  let body = {};

  if (drawingIds) Object.assign(body, { ids: `${drawingIds}` });
  if (fileType) Object.assign(body, { types: fileType });

  // call without showing loader
  return apiCall(apiPath, method, body, null, null, true);
};

export const fetchPackagesFiles = (packageId) => {
  const { apiCall } = utils.useApiCall();
  const apiPath = `packages/${packageId}/maps/file`;

  // call without showing loader
  return apiCall(apiPath, "GET", null, null, true);
};

export const fetchDashboardFiltersValidationToken = (filters = {}) => {
  const { apiCall } = utils.useApiCall();
  let apiPath = `/dashboard/validate-filters`;
  const method = "POST";

  return apiCall(apiPath, method, filters, null, true, true);
};

export const fetchDashboardTrendsHours = () => {
  const { apiCall } = utils.useApiCall();
  let apiPath = `dashboard/trends/hours`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true, true);
};

export const fetchDashboardPackageTrendsByMonth = () => {
  const { apiCall } = utils.useApiCall();
  let apiPath = `dashboard/trends/packages`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true, true);
};
