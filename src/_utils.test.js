// NPM PACKAGE IMPORTS
import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockAdapter from "axios-mock-adapter";
import thunk from "redux-thunk";
import moment from "moment";
import "moment-timezone";
import { DateTime } from "luxon";

import utils from "./_utils";
import _ from "underscore";

describe("UTILS", () => {
  describe("axiosApiCall", () => {
    const testError = { error: { status: 400, message: "Message" } },
      testErrorMatrix = [
        [{ failed_rows: '[{ "rows": "1,2,3", "reason": "bad data" }]' }],
        [{ total_items_created: 10 }],
      ],
      testErrorStateArray = [{ state: "error", message: "Message" }],
      testWarningStateArray = [{ state: "warning", message: "Message" }],
      testErrorStateObject = { state: "error", message: "Message" },
      testWarningStateObject = { state: "warning", message: "Message" },
      testNoResponse = {
        error: { status: 500, message: "Error completing action" },
      };
    const successMessage = "Success Message",
      successMessageObject = {
        type: "WARN",
        message: "Success with warnings",
      },
      successMessageFunction = (res) => "Success Message Function";
    const messageActionType = "ENQUEUE_MESSAGE",
      messageObject = (message) => ({
        type: messageActionType,
        payload: {
          type: typeof message === "object" ? message.type : "SUCCESS",
          message:
            typeof message === "function"
              ? message()
              : typeof message === "object"
              ? message.message
              : message,
        },
      }),
      errorMessageObject = (message) => ({
        type: messageActionType,
        payload: { type: "ERROR", message },
      }),
      errorMessageFunction = (res, callback) => {
        if (res.error) callback("error");
      };
    const spinnerActionTypeEnqueue = "ENQUEUE_SPINNER",
      spinnerActionTypeDequeue = "DEQUEUE_SPINNER";
    const disableErrorAlertFunction = (err) => true;
    const testResponse = [{ id: 1 }],
      testApiPath = `${process.env.REACT_APP_API}/test`,
      testBody = { app_type: "fab", param1: "param1", param2: "param2" };

    let store, httpMock, apiCall;
    beforeEach(() => {
      httpMock = new MockAdapter(axios, { onNoMatch: "throwException" });
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
      apiCall = utils.useApiCall(store)["apiCall"];
    });

    afterEach(() => {
      store.clearActions();
    });

    it("should make a call to the api with the passed apiPath and method", async () => {
      httpMock.onGet(testApiPath).replyOnce(200, testResponse);

      const response = await apiCall("test", "GET", null, null, false, true);
      const receivedActions = store.getActions();

      expect(response).toEqual(testResponse);
      expect(receivedActions).toEqual([]);
    });

    it("should make a call to the api with the passed body", async () => {
      httpMock.onPost(testApiPath).replyOnce(200, testResponse);

      const response = await apiCall(
        "test",
        "POST",
        testBody,
        null,
        false,
        true
      );
      const receivedActions = store.getActions();

      expect(httpMock.history.post[0].data).toEqual(JSON.stringify(testBody));
      expect(response).toEqual(testResponse);
      expect(receivedActions).toEqual([]);
    });

    it("should make a call to the api and display a string success message", async () => {
      httpMock.onGet(testApiPath).replyOnce(200, testResponse);

      let response = await apiCall(
        "test",
        "GET",
        null,
        successMessage,
        false,
        true
      );
      let receivedActions = store.getActions();

      expect(response).toEqual(testResponse);
      expect(receivedActions.length).toBe(1);
      expect(receivedActions[0]).toMatchObject(messageObject(successMessage));
    });

    it("should make a call to the api and display an object success message", async () => {
      httpMock.onGet(testApiPath).replyOnce(200, testResponse);

      let response = await apiCall(
        "test",
        "GET",
        null,
        successMessageObject,
        false,
        true
      );
      let receivedActions = store.getActions();

      expect(response).toEqual(testResponse);
      expect(receivedActions.length).toBe(1);
      expect(receivedActions[0]).toMatchObject(
        messageObject(successMessageObject)
      );
    });

    it("should make a call to the api and display a function success message", async () => {
      httpMock.onGet(testApiPath).replyOnce(200, testResponse);

      let response = await apiCall(
        "test",
        "GET",
        null,
        successMessageFunction,
        false,
        true
      );
      let receivedActions = store.getActions();

      expect(response).toEqual(testResponse);
      expect(receivedActions.length).toBe(1);
      expect(receivedActions[0]).toMatchObject(
        messageObject(successMessageFunction)
      );
    });

    it("should make a call to the api and disable the error notification", async () => {
      httpMock
        .onGet(testApiPath)
        .replyOnce(400, testError)
        .onGet(testApiPath)
        .replyOnce(400, testError);

      let response = await apiCall("test", "GET", null, null, true, true);
      let receivedActions = store.getActions();

      expect(response).toEqual(testError);
      expect(receivedActions).toEqual([]);

      store.clearActions();

      response = await apiCall(
        "test",
        "GET",
        null,
        null,
        disableErrorAlertFunction,
        true
      );
      receivedActions = store.getActions();

      expect(response).toEqual(testError);
      expect(receivedActions).toEqual([]);
    });

    it("should make a call to the api and handle a basic error", async () => {
      httpMock.onGet(testApiPath).replyOnce(400, testError);

      let response = await apiCall("test", "GET", null, null, false, true);
      let receivedActions = store.getActions();

      expect(response).toEqual(testError);
      expect(receivedActions.length).toBe(1);
      expect(receivedActions[0]).toMatchObject(
        errorMessageObject(testError.error.message)
      );
    });

    it("should make a call to the api and handle a matrix error", async () => {
      httpMock.onGet(testApiPath).replyOnce(200, testErrorMatrix);

      let response = await apiCall("test", "GET", null, null, false, true);
      let receivedActions = store.getActions();

      expect(response).toEqual(testErrorMatrix);
      expect(receivedActions.length).toBe(2);
      expect(receivedActions[0]).toMatchObject(
        errorMessageObject(
          "Failed to create 3 item(s) (rows 1, 2, 3) due to: bad data."
        )
      );
      expect(receivedActions[1]).toMatchObject(
        messageObject("Successfully created 10 item(s).")
      );
    });

    it("should make a call to the api and handle a state array error", async () => {
      httpMock.onGet(testApiPath).replyOnce(200, testErrorStateArray);

      let response = await apiCall("test", "GET", null, null, false, true);
      let receivedActions = store.getActions();

      expect(response).toEqual({
        error: { status: 400, message: testErrorStateArray[0].message },
      });
      expect(receivedActions.length).toBe(1);
      expect(receivedActions[0]).toMatchObject(
        errorMessageObject(testErrorStateArray[0].message)
      );
    });

    it("should make a call to the api and handle a state array warning", async () => {
      httpMock.onGet(testApiPath).replyOnce(200, testWarningStateArray);

      let response = await apiCall("test", "GET", null, null, false, true);
      let receivedActions = store.getActions();

      expect(response).toEqual({
        error: { status: 400, message: testWarningStateArray[0].message },
      });
      expect(receivedActions.length).toBe(1);
      expect(receivedActions[0]).toMatchObject(
        errorMessageObject(testWarningStateArray[0].message)
      );
    });

    it("should make a call to the api and handle a state object error", async () => {
      httpMock.onGet(testApiPath).replyOnce(200, testErrorStateObject);

      let response = await apiCall("test", "GET", null, null, false, true);
      let receivedActions = store.getActions();

      expect(response).toEqual({
        error: { status: 400, message: testErrorStateObject.message },
      });
      expect(receivedActions.length).toBe(1);
      expect(receivedActions[0]).toMatchObject(
        errorMessageObject(testErrorStateObject.message)
      );
    });

    it("should make a call to the api and handle a state object warning", async () => {
      httpMock.onGet(testApiPath).replyOnce(200, testWarningStateObject);

      let response = await apiCall("test", "GET", null, null, false, true);
      let receivedActions = store.getActions();

      expect(response).toEqual({
        error: { status: 400, message: testWarningStateObject.message },
      });
      expect(receivedActions.length).toBe(1);
      expect(receivedActions[0]).toMatchObject(
        errorMessageObject(testWarningStateObject.message)
      );
    });

    it("should make a call to the api and handle no response", async () => {
      let response = await apiCall("test", "GET", null, null, false, true);
      let receivedActions = store.getActions();

      expect(response).toEqual(testNoResponse);
      expect(receivedActions.length).toBe(1);
      expect(receivedActions[0]).toMatchObject(
        errorMessageObject(testNoResponse.error.message)
      );
    });

    it("should make a call to the api and display a loading spinner", async () => {
      httpMock.onGet(testApiPath).replyOnce(200, testResponse);

      let response = await apiCall("test", "GET");
      let receivedActions = store.getActions();

      expect(response).toEqual(testResponse);
      expect(receivedActions.length).toBe(2);
      expect(receivedActions[0]).toHaveProperty(
        "type",
        spinnerActionTypeEnqueue
      );
      expect(receivedActions[0]).toHaveProperty("payload");
      expect(receivedActions[1]).toHaveProperty(
        "type",
        spinnerActionTypeDequeue
      );
      expect(receivedActions[1]).toHaveProperty("payload");
    });

    it("should accept a custom error function", async () => {
      httpMock.onGet(testApiPath).replyOnce(200, testError);

      let response = await apiCall(
        "test",
        "GET",
        null,
        null,
        false,
        true,
        errorMessageFunction
      );
      let receivedActions = store.getActions();

      expect(response).toEqual(testError);
      expect(receivedActions.length).toBe(1);
      expect(receivedActions[0]).toMatchObject(errorMessageObject("error"));
    });
  });

  describe("generateIcon", () => {
    it("ICOMOON", () => {
      const noNav = utils.generateIcon({ type: "ICOMOON", className: "test" }),
        navNoSubsection = utils.generateIcon(
          { type: "ICOMOON", className: "test" },
          true
        ),
        navWithSubsection = utils.generateIcon(
          { type: "ICOMOON", className: "test", subsection: true },
          true
        );

      expect(noNav).toMatchObject({
        type: "i",
        props: { className: "test", style: {} },
      });
      expect(navNoSubsection).toMatchObject({
        type: "i",
        props: { className: "test", style: { alignSelf: "center" } },
      });
      expect(navWithSubsection).toMatchObject({
        type: "i",
        props: {
          className: "test",
          style: { paddingTop: "calc(22.5px - 10px)" },
        },
      });
    });

    it("LETTER", () => {
      const noNav = utils.generateIcon({ type: "LETTER", className: "test" }),
        navNoSubsection = utils.generateIcon(
          { type: "LETTER", className: "test" },
          true
        ),
        navWithSubsection = utils.generateIcon(
          { type: "LETTER", className: "test", subsection: true },
          true
        );

      expect(noNav).toMatchObject({
        type: "span",
        props: { className: "test", style: {} },
      });
      expect(navNoSubsection).toMatchObject({
        type: "span",
        props: { className: "test", style: { alignSelf: "center" } },
      });
      expect(navWithSubsection).toMatchObject({
        type: "span",
        props: {
          className: "test",
          style: { paddingTop: "calc(22.5px - 10px)" },
        },
      });
    });

    it("ICON", () => {
      const noNav = utils.generateIcon({
          type: "ICON",
          className: "test",
          icon: "test",
        }),
        navNoSubsection = utils.generateIcon(
          { type: "ICON", className: "test", icon: "test" },
          true
        ),
        navWithSubsection = utils.generateIcon(
          { type: "ICON", className: "test", icon: "test", subsection: true },
          true
        );

      // type here is [Function FontAwesomeIcon]
      expect(noNav).toMatchObject({ props: { className: "test", style: {} } });
      expect(navNoSubsection).toMatchObject({
        props: { className: "test", style: { alignSelf: "center" } },
      });
      expect(navWithSubsection).toMatchObject({
        props: {
          className: "test",
          style: { paddingTop: "calc(22.5px - 10px)" },
        },
      });
    });

    it("SVG", () => {
      const noNav = utils.generateIcon({
          type: "SVG",
          className: "test",
          icon: { viewBox: {}, paths: ["1", "2", { id: 3, d: "d" }] },
        }),
        navNoSubsection = utils.generateIcon(
          {
            type: "SVG",
            className: "test",
            icon: { viewBox: {}, paths: ["1", "2", { id: 3, d: "d" }] },
          },
          true
        ),
        navWithSubsection = utils.generateIcon(
          {
            type: "SVG",
            className: "test",
            icon: { viewBox: {}, paths: ["1", "2", { id: 3, d: "d" }] },
            subsection: true,
          },
          true
        );

      expect(noNav).toMatchObject({
        type: "svg",
        props: { className: "test", style: {} },
      });
      expect(noNav.props.children.length).toBe(3);
      expect(navNoSubsection).toMatchObject({
        type: "svg",
        props: { className: "test", style: { alignSelf: "center" } },
      });
      expect(navNoSubsection.props.children.length).toBe(3);
      expect(navWithSubsection).toMatchObject({
        type: "svg",
        props: {
          className: "test",
          style: { paddingTop: "calc(22.5px - 10px)" },
        },
      });
      expect(navWithSubsection.props.children.length).toBe(3);
    });
  });

  describe("permissionLock", () => {
    const mockStore = configureMockStore([thunk]);

    it("should filter out items that the user does not have permission to utilize", () => {
      const store = mockStore({ profileData: { permissions: [1, 2, 3] } }),
        data = [
          { id: 1 },
          { id: 2, permissions: [5] },
          { id: 3, permissions: [1, 2] },
          { id: 4, permissions: [3] },
        ],
        permissionLockedData = utils.permissionLock(data, store),
        expected = [{ id: 1 }, { id: 3 }, { id: 4 }];

      expect(permissionLockedData).toEqual(expected);
    });

    it("should filter out items when no permissions in state", () => {
      const store = mockStore({ profileData: { permissions: null } }),
        data = [
          { id: 1 },
          { id: 2, permissions: [5] },
          { id: 3, permissions: [1, 2] },
          { id: 4, permissions: [3] },
        ],
        permissionLockedData = utils.permissionLock(data, store),
        expected = [{ id: 1 }];

      expect(permissionLockedData).toEqual(expected);
    });
  });

  describe("canDrop", () => {
    const monitor = {
        getItem: () => ({ dropOrigin: "top" }),
        canDrop: () => true,
      },
      blackListedOriginsEmpty = [],
      blackListedOrigins1 = ["bottom"],
      blackListedOrigins2 = ["top"],
      dropOrigin1 = "top",
      dropOrigin2 = "bottom";

    const received1 = utils.canDrop(
        monitor,
        dropOrigin1,
        blackListedOriginsEmpty
      ),
      received2 = utils.canDrop(monitor, dropOrigin1, blackListedOrigins1),
      received3 = utils.canDrop(monitor, dropOrigin1, blackListedOrigins2),
      received4 = utils.canDrop(monitor, dropOrigin2, blackListedOriginsEmpty),
      received5 = utils.canDrop(monitor, dropOrigin2, blackListedOrigins1),
      received6 = utils.canDrop(monitor, dropOrigin2, blackListedOrigins2);
    const expected1 = false,
      expected2 = false,
      expected3 = false,
      expected4 = true,
      expected5 = true,
      expected6 = false;

    expect(received1).toBe(expected1);
    expect(received2).toBe(expected2);
    expect(received3).toBe(expected3);
    expect(received4).toBe(expected4);
    expect(received5).toBe(expected5);
    expect(received6).toBe(expected6);
  });

  describe("timeFormatter", () => {
    it("should convert decimal time into hh:mm", () => {
      const expected1 = "10:45",
        received1 = utils.timeFormatter(10.75),
        expected2 = "10:09",
        received2 = utils.timeFormatter(10.15);

      expect(received1).toBe(expected1);
      expect(received2).toBe(expected2);
    });
  });

  describe("convertHex", () => {
    it("should convert a hexadecimal color to its rgba counterpart", () => {
      const expected1 = "rgba(255,0,0, 0.5)",
        received1 = utils.convertHex("#FF0000"),
        expected2 = "rgba(0,255,0, 0.5)",
        received2 = utils.convertHex("#00FF00"),
        expected3 = "rgba(0,0,255, 0.5)",
        received3 = utils.convertHex("#0000FF");

      expect(received1).toBe(expected1);
      expect(received2).toBe(expected2);
      expect(received3).toBe(expected3);
    });
  });

  describe("getRowHeight", () => {
    const expected1 = 100,
      received1 = utils.getRowHeight({ node: { detail: true } }),
      expected2 = 60,
      received2 = utils.getRowHeight({ node: {} });

    expect(received1).toBe(expected1);
    expect(received2).toBe(expected2);
  });

  describe("onSortChanged", () => {
    it("should redraw the rows", () => {
      let rowsRedrawn = false;

      utils.onSortChanged({ api: { redrawRows: () => (rowsRedrawn = true) } });

      expect(rowsRedrawn).toBe(true);
    });
  });

  describe("generateKpis", () => {
    const users = [
      {
        experience_years: 3.5,
        labor_rate: 10.0,
      },
      {
        experience_years: 5,
        labor_rate: 15.0,
      },
      {
        experience_years: 15,
        labor_rate: 75.0,
      },
    ];

    const kpisWithUsers = utils.generateKpis(users),
      kpisWithoutUsers = utils.generateKpis([]);

    expect(kpisWithUsers.length).toBe(3);
    expect(kpisWithUsers[0]).toMatchObject({
      name: "Users Assigned",
      value: 3,
      selected: true,
    });
    expect(kpisWithUsers[0]).toHaveProperty("icon");
    expect(kpisWithUsers[1]).toMatchObject({
      name: "Average Experience",
      value: "8 years",
      selected: true,
    });
    expect(kpisWithUsers[1]).toHaveProperty("icon");
    expect(kpisWithUsers[2]).toMatchObject({
      name: "Average Wage",
      value: (100 / 3).toFixed(2),
      selected: true,
    });
    expect(kpisWithUsers[2]).toHaveProperty("icon");

    expect(kpisWithoutUsers.length).toBe(3);
    expect(kpisWithoutUsers[0]).toMatchObject({
      name: "Users Assigned",
      value: 0,
      selected: true,
    });
    expect(kpisWithoutUsers[0]).toHaveProperty("icon");
    expect(kpisWithoutUsers[1]).toMatchObject({
      name: "Average Experience",
      value: "0 years",
      selected: true,
    });
    expect(kpisWithoutUsers[1]).toHaveProperty("icon");
    expect(kpisWithoutUsers[2]).toMatchObject({
      name: "Average Wage",
      value: "0.00",
      selected: true,
    });
    expect(kpisWithoutUsers[2]).toHaveProperty("icon");
  });

  describe("convertStringToUnix", () => {
    const str = new Date("1970-01-01"),
      received = utils.convertStringToUnix(str),
      expected = Math.floor(new Date("1970-01-01").valueOf() / 1000);

    expect(received).toBe(expected);
  });

  describe("validateDateStringFormat", () => {
    const validDate = "02/21/1990",
      invalidMonth = "13/21/1990",
      invalidDay = "02/30/1990",
      invalidYear = "02/21/1890";

    expect(utils.validateDateStringFormat(validDate)).toBe(true);
    expect(utils.validateDateStringFormat(invalidMonth)).toBe(false);
    expect(utils.validateDateStringFormat(invalidDay)).toBe(false);
    expect(utils.validateDateStringFormat(invalidYear)).toBe(false);
  });

  describe("checkPreviousShiftTime", () => {
    const shiftEndFalse = new Date("1970-01-01").valueOf(),
      shiftEndTrue = Math.floor(Date.now() / 1000),
      received1 = utils.checkPreviousShiftTime(shiftEndFalse),
      received2 = utils.checkPreviousShiftTime(shiftEndTrue);

    expect(received1).toBe(false);
    expect(received2).toBe(true);
  });

  describe("titleize", () => {
    expect(utils.titleize("work items")).toEqual("Work Items");
    expect(utils.titleize("work_items")).toEqual("Work Items");
    expect(utils.titleize("work_items_are_cool")).toEqual(
      "Work Items Are Cool"
    );
  });

  describe("transformPriorityToRank", () => {
    const drawings = [
        { id: 1, priority: 5 },
        { id: 2, priority: 3 },
        { id: 3, priority: 4 },
        { id: 4, priority: 2 },
        { id: 5, priority: 1 },
      ],
      expected = [
        { id: 5, priority: 1 },
        { id: 4, priority: 2 },
        { id: 2, priority: 3 },
        { id: 3, priority: 4 },
        { id: 1, priority: 5 },
      ],
      received = utils.transformPriorityToRank(drawings);

    expect(received).toEqual(expected);
  });

  describe("transformDrawingPriorityToRank", () => {
    const items = [
        { drawing_id: 1, drawing_priority: 5 },
        { drawing_id: 2, drawing_priority: 3 },
        { drawing_id: 3, drawing_priority: 4 },
        { drawing_id: 4, drawing_priority: 2 },
        { drawing_id: 5, drawing_priority: 1 },
        { drawing_id: 1, drawing_priority: 5 },
        { drawing_id: 2, drawing_priority: 3 },
        { drawing_id: 3, drawing_priority: 4 },
        { drawing_id: 4, drawing_priority: 2 },
        { drawing_id: 5, drawing_priority: 1 },
      ],
      expected = [
        { id: 5, drawing_priority: 1 },
        { id: 4, drawing_priority: 2 },
        { id: 2, drawing_priority: 3 },
        { id: 3, drawing_priority: 4 },
        { id: 1, drawing_priority: 5 },
      ],
      received = utils.transformDrawingPriorityToRank(items);

    expect(received).toEqual(expected);
  });

  describe("escapeRegExp", () => {
    const regex = "-[]{}()*+?.,\\^$|#s",
      expected = "\\-\\[\\]\\{\\}\\(\\)\\*\\+\\?\\.\\,\\\\\\^\\$\\|\\#s",
      received = utils.escapeRegExp(regex);

    expect(received).toBe(expected);
  });

  describe("table", () => {
    expect(utils.table("JOBS")).toBe("jobs");
    expect(utils.table("PACKAGES")).toBe("packages");
    expect(utils.table("DRAWINGS")).toBe("drawings");
    expect(utils.table("ITEMS")).toBe("work_stages");
    expect(utils.table("ASSIGNED_DRAWINGS")).toBe("assigned_drawings");
    expect(utils.table("MY_WORK_ITEMS")).toBe("my_work_items");
    expect(utils.table("BOM")).toBe("bom");
    expect(utils.table("PACKAGES_PENDING_APPROVAL")).toBe(
      "packages_pending_approval"
    );
    expect(utils.table("PACKAGES_SENT_BACK")).toBe("packages_sent_back");
    expect(utils.table("DRAWINGS_PENDING_APPROVAL")).toBe(
      "drawings_pending_approval"
    );
    expect(utils.table("DRAWINGS_SENT_BACK")).toBe("drawings_sent_back");
    expect(utils.table("INVALID")).toBe("");
  });

  describe("onDisplayedColumnsChanged", () => {
    let store,
      httpMock,
      groupingColumnOptionsSet = false;
    const allColumns = [
      {
        left: 3,
        colDef: {
          headerName: "Column 3",
        },
        pinned: "left",
        visible: true,
      },
      {
        left: 2,
        colDef: {
          headerName: "Column 2",
        },
        pinned: "right",
        visible: false,
      },
      {
        left: 1,
        colDef: {
          headerName: "Column 1",
        },
        pinned: null,
        visible: true,
      },
      {
        left: 4,
        colDef: {
          headerName: "",
        },
        pinned: null,
        visible: true,
      },
    ];
    const setGroupingColumnOptions = (receivedColumns) => {
      if (JSON.stringify(allColumns) === JSON.stringify(receivedColumns))
        groupingColumnOptionsSet = true;
    };
    const params = { columnApi: { getAllColumns: () => allColumns } },
      expectedColumns = [
        {
          header_name: "Column 1",
          pinned: null,
          visible: 1,
        },
        {
          header_name: "Column 2",
          pinned: "right",
          visible: 0,
        },
        {
          header_name: "Column 3",
          pinned: "left",
          visible: 1,
        },
      ];

    beforeEach(() => {
      httpMock = new MockAdapter(axios, { onNoMatch: "throwException" });
      const mockStore = configureMockStore([thunk]);
      store = mockStore({
        jobsData: {
          savedColumnState: null,
        },
        itemsData: {
          savedColumnState: null,
        },
        myWorkData: {
          savedColumnState: null,
        },
      });
    });

    afterEach(() => {
      store.clearActions();
    });

    it("should save the correct column format", async () => {
      httpMock
        .onPost(`${process.env.REACT_APP_API}/ag-table/column-state`)
        .replyOnce(200, "success");

      const received = await utils.onDisplayedColumnsChanged(
          "JOBS",
          params,
          null,
          null,
          false,
          store
        ),
        expected = "success";

      expect(received).toBe(expected);
      expect(httpMock.history.post.length).toBe(1);
      expect(httpMock.history.post[0].data).toBe(
        JSON.stringify({
          app_type: "fab",
          table: "jobs",
          columns: expectedColumns,
        })
      );
    });

    it("should not save the columns when viewing grouped items or bom", async () => {
      const received = await utils.onDisplayedColumnsChanged(
          "BOM",
          params,
          null,
          null,
          true,
          store
        ),
        expected = undefined;

      expect(received).toBe(expected);
      expect(httpMock.history.post.length).toBe(0);
    });

    it("should set the grouping column options and continue", async () => {
      httpMock
        .onPost(`${process.env.REACT_APP_API}/ag-table/column-state`)
        .replyOnce(200, "success");

      const received = await utils.onDisplayedColumnsChanged(
          "JOBS",
          params,
          null,
          setGroupingColumnOptions,
          false,
          store
        ),
        expected = "success";

      expect(received).toBe(expected);
      expect(groupingColumnOptionsSet).toBe(true);
      expect(httpMock.history.post.length).toBe(1);
      expect(httpMock.history.post[0].data).toBe(
        JSON.stringify({
          app_type: "fab",
          table: "jobs",
          columns: expectedColumns,
        })
      );
    });

    it("should send stage ids when ITEMS or MY_WORK_ITEMS", async () => {
      httpMock
        .onPost(`${process.env.REACT_APP_API}/ag-table/column-state`)
        .reply(200, "success");

      const received1 = await utils.onDisplayedColumnsChanged(
          "JOBS",
          params,
          null,
          setGroupingColumnOptions,
          false,
          store
        ),
        received2 = await utils.onDisplayedColumnsChanged(
          "ITEMS",
          params,
          [1, 2, 3],
          null,
          false,
          store
        ),
        received3 = await utils.onDisplayedColumnsChanged(
          "MY_WORK_ITEMS",
          params,
          [1, 2, 3],
          null,
          false,
          store
        ),
        expected = "success";

      expect(received1).toBe(expected);
      expect(received2).toBe(expected);
      expect(received3).toBe(expected);
      expect(httpMock.history.post.length).toBe(3);
      expect(httpMock.history.post[0].data).toBe(
        JSON.stringify({
          app_type: "fab",
          table: "jobs",
          columns: expectedColumns,
        })
      );
      expect(httpMock.history.post[1].data).toBe(
        JSON.stringify({
          app_type: "fab",
          table: "work_stages",
          columns: expectedColumns.map((c) => ({
            ...c,
            work_stage_ids: "1,2,3",
          })),
        })
      );
      expect(httpMock.history.post[2].data).toBe(
        JSON.stringify({
          app_type: "fab",
          table: "my_work_items",
          columns: expectedColumns.map((c) => ({
            ...c,
            work_stage_ids: "1,2,3",
          })),
        })
      );
    });
  });

  describe("reduce", () => {
    it("should reduce a fraction to its smallest form", () => {
      expect(utils.reduce(12, 15)).toEqual([4, 5]);
      expect(utils.reduce(100, 1000)).toEqual([1, 10]);
      expect(utils.reduce(45, 60)).toEqual([3, 4]);
    });
  });

  describe("simpleFrac", () => {
    it("should reduce a fraction to its smallest form", () => {
      expect(utils.simpleFrac({ whole: 3, num: 15, den: 12 })).toEqual({
        whole: 4,
        num: 1,
        den: 4,
      });
      expect(utils.simpleFrac({ whole: 15, num: 105, den: 12 })).toEqual({
        whole: 23,
        num: 3,
        den: 4,
      });
      expect(utils.simpleFrac({ whole: 0, num: 19, den: 8 })).toEqual({
        whole: 2,
        num: 3,
        den: 8,
      });
      expect(utils.simpleFrac({ whole: 0, num: 8, den: 19 })).toEqual({
        whole: 0,
        num: 8,
        den: 19,
      });
    });
  });

  describe("whiteSpaceToSingleSpace", () => {
    it("should remove extraneous white space blocks", () => {
      expect(
        utils.whiteSpaceToSingleSpace(" This is only a         test . ")
      ).toEqual(" This is only a test . ");
    });
  });

  describe("spacesAndDigits", () => {
    it("should remove everything but white space and numerical digits", () => {
      expect(
        utils.spacesAndDigits(
          " This is only the  123214,1324214th       test . "
        )
      ).toEqual("      1232141324214         ");
    });
  });

  describe("sanitize", () => {
    it("should remove everything except white space and numerical digits, then extraneous white space blocks", () => {
      expect(
        utils.sanitize(" This is only the  123214,1324214th       test . ")
      ).toEqual(" 1232141324214 ");
    });
  });

  describe("lengthParse", () => {
    it("should parse out the length, simplify it, and return the cleaned version", () => {
      expect(utils.lengthParse("1")).toMatchObject({
        display: "1′",
        value: 12,
      });
      expect(utils.lengthParse("1 2")).toMatchObject({
        display: "1′–2″",
        value: 14,
      });
      expect(utils.lengthParse("1 2 8")).toMatchObject({
        display: "1′–2 8/″",
        value: 14,
      });
      expect(utils.lengthParse("1 2 1 2")).toMatchObject({
        display: "1′–2 1/2″",
        value: 14.5,
      });
      expect(utils.lengthParse("15 21 24 32")).toMatchObject({
        display: "16′–9 3/4″",
        value: 201.75,
      });
    });
  });

  describe("DISPLAY_LENGTH", () => {
    it("should parse out the length, simplify it, and return the cleaned version", () => {
      expect(utils.DISPLAY_LENGTH(10, 2)).toEqual("10′-0″");
      expect(utils.DISPLAY_LENGTH(0.25, 4)).toEqual("0′-3″");
      expect(utils.DISPLAY_LENGTH("0.25", 4)).toEqual("0′-3″");
      expect(utils.DISPLAY_LENGTH(" 0.25", 4)).toEqual("0′-3″");
      expect(utils.DISPLAY_LENGTH(0.25, 4)).toEqual("0′-3″");
      expect(utils.DISPLAY_LENGTH(".25", 4)).toEqual("0′-3″");
      expect(utils.DISPLAY_LENGTH(" .25", 4)).toEqual("0′-3″");
      expect(utils.DISPLAY_LENGTH(1.25, 8)).toEqual("1′-3″");
      expect(utils.DISPLAY_LENGTH(15.32, 16)).toEqual("15′-3 13/16″");
      expect(utils.DISPLAY_LENGTH(10.32, 16)).toEqual("10′-3 13/16″");
    });
  });

  describe("NESTING_LENGTH_DISPLAY", () => {
    it("should parse out the length, simplify it, and return the cleaned version", () => {
      expect(utils.NESTING_LENGTH_DISPLAY(10)).toEqual("0 10 ");
      expect(utils.NESTING_LENGTH_DISPLAY(0.25)).toEqual("0 0 1 4");
      expect(utils.NESTING_LENGTH_DISPLAY(1.25)).toEqual("0 1 1 4");
      expect(utils.NESTING_LENGTH_DISPLAY(15.32)).toEqual("1 3 8 25");
    });
  });

  describe("getUniqueCounts", () => {
    it("should return a collection of the unique lengths and their counts", () => {
      const lengths = {
        1: { length: { decimal: 21 } },
        2: { length: { decimal: 21 } },
        3: { length: { decimal: 0.64 } },
        4: { length: { decimal: 21 } },
        5: { length: { decimal: 21 } },
        6: { length: { decimal: 0.64 } },
      };
      expect(utils.getUniqueCounts(lengths)).toMatchObject({
        21: 4,
        0.64: 2,
      });
    });
  });

  describe("decimalRegex", () => {
    it("should pass valid decimals", () => {
      expect(utils.decimalRegex.test(".25")).toBe(true);
      expect(utils.decimalRegex.test("0.25")).toBe(true);
      expect(utils.decimalRegex.test("1")).toBe(true);
      expect(utils.decimalRegex.test("1.25")).toBe(true);
      expect(utils.decimalRegex.test("21314.241225")).toBe(true);
    });

    it("should fail invalid input", () => {
      expect(utils.decimalRegex.test(".")).toBe(false);
      expect(utils.decimalRegex.test("32.sad")).toBe(false);
      expect(utils.decimalRegex.test("foobar")).toBe(false);
    });
  });

  describe("fractionalRegex", () => {
    it("should pass valid fractional feet/inches", () => {
      expect(utils.fractionalRegex.test('1/2"')).toBe(true);
      expect(utils.fractionalRegex.test("1' 3/4\"")).toBe(true);
      expect(utils.fractionalRegex.test("1'-3/4\"")).toBe(true);
      expect(utils.fractionalRegex.test("2'")).toBe(true);
      expect(utils.fractionalRegex.test('2"')).toBe(true);
      expect(utils.fractionalRegex.test("10'2\"")).toBe(true);
    });

    it("should fail invalid input", () => {
      expect(utils.fractionalRegex.test("32")).toBe(false);
      expect(utils.fractionalRegex.test("2'x2'x2'")).toBe(false);
      expect(utils.fractionalRegex.test("foobar")).toBe(false);
    });
  });

  describe("multidimensionRegex", () => {
    it("multidimensionRegex should pass valid fractional feet/inches", () => {
      expect(utils.multidimensionRegex.test('2 1/2"x2 1/2"x3/4"')).toBe(true);
      expect(utils.multidimensionRegex.test("2")).toBe(true);
      expect(utils.multidimensionRegex.test("2x2")).toBe(true);
      expect(utils.multidimensionRegex.test("2x2x2")).toBe(true);
      expect(utils.multidimensionRegex.test('2"')).toBe(true);
      expect(utils.multidimensionRegex.test("2'")).toBe(true);
      expect(utils.multidimensionRegex.test('2"x2"x2"')).toBe(true);
      expect(utils.multidimensionRegex.test('2x2x2"')).toBe(true);
      expect(utils.multidimensionRegex.test("1.2x3x5")).toBe(true);
      expect(utils.multidimensionRegex.test("1.2x1.2x1.2")).toBe(true);
      expect(utils.multidimensionRegex.test('.2x.2"')).toBe(true);
      expect(utils.multidimensionRegex.test("2 x 2 x 2")).toBe(true);
      expect(utils.multidimensionRegex.test('1\'-3/4" x 1/2" x 3/4"')).toBe(
        true
      );
      expect(utils.multidimensionRegex.test("1'-3/4\"")).toBe(true);
      expect(utils.multidimensionRegex.test('1\'-3/4" x 1/2"')).toBe(true);
    });

    it("multidimensionOnlyX should pass valid fractional feet/inches", () => {
      expect(utils.multidimensionOnlyX.test("foobar")).toBe(false);
      expect(utils.multidimensionOnlyX.test('1"x2" x 3"')).toBe(true);
      expect(utils.multidimensionOnlyX.test("1.2' - 5.2'")).toBe(true);
    });
  });

  describe("convertFracToDec", () => {
    it("should convert fractional feet/inches to decimal inches format", () => {
      expect(utils.convertFracToDec('3/4"')).toBe(0.75);
      expect(utils.convertFracToDec('1 3/4"')).toBe(1.75);
      expect(utils.convertFracToDec("2' 3/4\"")).toBe(24.75);
      expect(utils.convertFracToDec("2'-3/4\"")).toBe(24.75);
      expect(utils.convertFracToDec("2'-1 3/4\"")).toBe(25.75);
      expect(utils.convertFracToDec("10'23\"")).toBe(143);
    });
  });

  describe("dateStringToUnix", () => {
    it("should convert a date string into unix time", () => {
      const date = new Date("1970/01/01");
      expect(utils.dateStringToUnix(date)).toBe(date.valueOf());
    });
  });

  describe("generateTime - v1", () => {
    const mockStore = configureMockStore([thunk]),
      store = mockStore({
        profileData: {
          systemSettings: {
            date_display: "yyyy-MM-DD",
            timezone: "America/Chicago",
          },
        },
      }),
      store2 = mockStore({
        profileData: { systemSettings: { timezone: "America/Chicago" } },
      }),
      // set timezone for test cases so we can generate the output properly
      date = new Date("1970/01/01").getTime(),
      dateFormat = "yyyy-MM-DD",
      dateTimeFormat = "yyyy-MM-DDTHH:mm:ss.SSS",
      timeFormat = "hh:mm A",
      utcOffset =
        (moment.tz("America/Chicago").utcOffset() -
          moment.tz("America/New_York").utcOffset()) *
        60 *
        1000,
      execTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone,
      gmtTimezone = "Etc/GMT+0";

    it("Should return datetime without formatting or offset", () => {
      let expected = `"${moment(date)
        .tz(gmtTimezone)
        .format(dateTimeFormat)}Z"`;

      expect(
        JSON.stringify(
          utils.generateTime(date.valueOf(), false, false, null, store, true)
        )
      ).toBe(expected);
    });

    it("Should return formatted time without offset", () => {
      // this is NOT what we should be expecting...
      let expected = `"${moment(date).tz(execTimezone).format(timeFormat)}"`;
      expect(
        JSON.stringify(
          utils.generateTime(date.valueOf(), true, false, null, store2)
        )
      ).toEqual(expected);

      const timezone = moment.tz.guess();
      expected = `"${moment(date).tz(timezone).format(timeFormat)}"`;
      expect(
        JSON.stringify(
          utils.generateTime(date.valueOf(), true, false, null, store2)
        )
      ).toEqual(expected);
    });

    it("Should return formatted date without offset", () => {
      let expected = `"${moment(date).tz(gmtTimezone).format(dateFormat)}"`;
      expect(
        JSON.stringify(
          utils.generateTime(date.valueOf(), false, false, null, store)
        )
      ).toEqual(expected);
    });
    it("Should return time without formatting, minus offset", () => {
      let expected = `"${moment(date - utcOffset)
        .tz(gmtTimezone)
        .format(dateTimeFormat)}Z"`;
      expect(
        JSON.stringify(
          utils.generateTime(date.valueOf(), false, true, "-", store, true)
        )
      ).toBe(expected);
    });
    it("Should return time without formatting, plus offset", () => {
      let expected = `"${moment(date + utcOffset)
        .tz(gmtTimezone)
        .format(dateTimeFormat)}Z"`;
      expect(
        JSON.stringify(
          utils.generateTime(date.valueOf(), false, true, "+", store, true)
        )
      ).toBe(expected);
    });
  });

  describe("generateTime - v2", () => {
    // setup constants
    const clientTimezone = "America/Chicago",
      serverTimezone = "America/New_York",
      execTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone, // your timezone
      gmtTimezone = "Etc/GMT+0",
      // date = new Date("2024-01-01").getTime(),
      date = 1704085200 * 1000, // Mon Jan 01 2024 05:00:00 GMT+0000
      time = 1704140100 * 1000, // Mon Jan 01 2024 20:15:00 GMT+0000
      dateFormat = "yyyy-MM-DD",
      dateTimeFormat = "yyyy-MM-DDTHH:mm:ss.SSS",
      timeFormat = "hh:mm A",
      mockStore = configureMockStore([thunk]),
      store = mockStore({
        profileData: {
          systemSettings: {
            date_display: dateFormat,
            timezone: clientTimezone,
          },
        },
      }),
      utcOffset =
        (moment.tz(clientTimezone).utcOffset() -
          moment.tz(serverTimezone).utcOffset()) *
        60 *
        1000;

    let expectedStr, actualStr;

    it("Return datetime, no formatting, no offset", () => {
      // prevent formatting so will return GMT+0
      expectedStr = moment(date).tz(gmtTimezone).format(dateTimeFormat) + "Z";
      actualStr = JSON.stringify(
        utils.generateTimev2(date, false, false, null, store, true)
      );
      expect(actualStr).toBe(`"${expectedStr}"`); // 2024-01-01T05:00:00.000Z
      // console.log(`Expected: ${expectedStr}, Actual: ${actualStr}`);
    });

    it("Return datetime, no formatting, plus offset", () => {
      // prevent formatting so will return GMT+0
      expectedStr =
        moment(date + utcOffset)
          .tz(gmtTimezone)
          .format(dateTimeFormat) + "Z";
      actualStr = JSON.stringify(
        utils.generateTimev2(date, false, true, "+", store, true)
      );
      expect(actualStr).toBe(`"${expectedStr}"`); // 2024-01-01T04:00:00.000Z
      // console.log(`Expected: ${expectedStr}, Actual: ${actualStr}`);
    });

    it("Return datetime, no formatting, minus offset", () => {
      expectedStr =
        moment(date - utcOffset)
          .tz(gmtTimezone)
          .format(dateTimeFormat) + "Z";
      actualStr = JSON.stringify(
        utils.generateTimev2(date, false, true, "-", store, true)
      );
      expect(actualStr).toBe(`"${expectedStr}"`); // 2024-01-01T06:00:00.000Z
      // console.log(`Expected: ${expectedStr}, Actual: ${actualStr}`);
    });

    it("Return formatted time in server timezone (no offset)", () => {
      expectedStr = moment(time).tz(serverTimezone).format(timeFormat);
      actualStr = JSON.stringify(
        utils.generateTimev2(time, true, false, null, store)
      );
      expect(actualStr).toBe(`"${expectedStr}"`);
      // time is 2024-01-01 3:15PM EST (depending on daylight savings)
      // console.log(`Expected: ${expectedStr}, Actual: ${actualStr}`);
    });

    it("Return formatted time in client timezone (requires offset)", () => {
      expectedStr = moment(time).tz(clientTimezone).format(timeFormat);
      actualStr = JSON.stringify(
        utils.generateTimev2(time, true, true, "+", store)
      );
      expect(actualStr).toBe(`"${expectedStr}"`);
      // time is 2024-01-01 2:15PM CST (depending on daylight savings)
      // console.log(`Expected: ${expectedStr}, Actual: ${actualStr}`);
    });

    it("Return formatted date in server timezone (no offset)", () => {
      expectedStr = moment(date).tz(serverTimezone).format(dateFormat);
      actualStr = JSON.stringify(
        utils.generateTimev2(date, false, false, null, store)
      );
      expect(actualStr).toBe(`"${expectedStr}"`); // .toBe('"2024-01-01"');
      // console.log(`Expected: ${expectedStr}, Actual: ${actualStr}`);
    });

    it("Return formatted date in client timezone (requires offset)", () => {
      expectedStr = moment(date).tz(clientTimezone).format(dateFormat);
      actualStr = JSON.stringify(
        utils.generateTimev2(date, false, true, "+", store)
      );
      expect(actualStr).toBe(`"${expectedStr}"`); // .toBe('"2024-01-01"');
      // console.log(`Expected: ${expectedStr}, Actual: ${actualStr}`);
    });

    it("Return formatted date from unixtimestamp in GMT time", () => {
      expectedStr = moment(date).tz(gmtTimezone).format(dateFormat);
      actualStr = JSON.stringify(
        utils.generateTimev2(date, false, false, null, store)
      );
      expect(actualStr).toBe(`"${expectedStr}"`); // .toBe('"2024-01-01"');
      // console.log(`Expected: ${expectedStr}, Actual: ${actualStr}`);
    });
  });

  describe("naturalSort", () => {
    const arr = ["#444", "1", "10", "2", "abc"],
      sorted = arr.sort(utils.naturalSort);

    expect(sorted).toEqual(["#444", "1", "2", "10", "abc"]);
  });

  describe("convertFracToDec", () => {
    const fractionList = [
      `22 1/4"`,
      `2'-1/2"`,
      `1' 1/4"`,
      `10"`,
      `100/4"`,
      `8'-4 1/4"`,
    ];
    const decimalList = [22.25, 24.5, 12.25, 10, 25, 100.25];
    const result = [];
    fractionList.forEach((fr) => {
      result.push(utils.convertFracToDec(fr));
    });

    expect(JSON.stringify(result)).toEqual(JSON.stringify(decimalList));
  });

  describe("checkForNewTableFilterSelections", () => {
    const res = utils.checkForNewTableFilterSelections([1, 2, 3], [1, 2]);
    const res2 = utils.checkForNewTableFilterSelections([1, 2, 3], [1, 2, 3]);
    const res3 = utils.checkForNewTableFilterSelections(
      [1, 2, 3],
      [1, 20, 2, 3]
    );
    expect(res).toEqual(false);
    expect(res2).toEqual(false);
    expect(res3).toEqual(true);
  });

  describe("updateDrawingsHasMaj", () => {
    const startingArr = [
      { id: 1, has_maj: 0 },
      { id: 2, has_maj: 0 },
      { id: 3, has_maj: 0 },
    ];
    const res = utils.updateDrawingsHasMaj(startingArr, [2, 3]);
    expect(JSON.stringify(res)).toEqual(
      JSON.stringify(
        startingArr.map((r) => (r.id > 1 ? { ...r, has_maj: 1 } : r))
      )
    );
    expect(JSON.stringify(utils.updateDrawingsHasMaj([], [2, 3]))).toEqual(
      JSON.stringify([])
    );
  });

  describe("validateGuidInput", () => {
    it("should run regex check on string for guid", () => {
      const res = utils.validateGuidInput("");
      const res2 = utils.validateGuidInput("random string");
      const res3 = utils.validateGuidInput(
        "7224ad11-e7e8-49dd-b6a4-648ec05bfd1"
      );
      const res4 = utils.validateGuidInput(
        "7224ad11-e7e8-49dd-b6a4-648ec05bfd15"
      );
      expect(res).toEqual(false);
      expect(res2).toEqual(false);
      expect(res3).toEqual(false);
      expect(res4).toEqual(true);
    });
  });

  describe("Filter and grouping utils...", () => {
    const testData = {
      items: [
        {
          stage_id: 1,
          id: 1,
          work_item_ids: "1",
          job_id: 2,
          package_id: 3,
          drawing_id: 1,
          heat_number: 1,
          joint_heat_numbers: "[]",
          rejected: 0,
          is_cut: 0,
          length: {
            decimal: 2,
            display: '2"',
          },
          available: 0,
          completed: 0,
          completedness: "Not Completed",
          material_name: "Forged Steel",
          size: '1 1/2"',
        },
        {
          stage_id: 1,
          id: 2,
          work_item_ids: "2",
          job_id: 1,
          package_id: 1,
          drawing_id: 2,
          heat_number: 1,
          joint_heat_numbers: "[]",
          rejected: 0,
          is_cut: 0,
          length: {
            decimal: 21,
            display: '21"',
          },
          available: 0,
          completed: 0,
          completedness: "Not Completed",
          material_name: "Forged Steel",
          size: '1 1/2"',
        },
        {
          stage_id: 2,
          id: 3,
          work_item_ids: "3",
          job_id: 1,
          package_id: 2,
          drawing_id: 3,
          heat_number: 1,
          joint_heat_numbers: "[]",
          rejected: 0,
          is_cut: 0,
          length: {
            decimal: 12,
            display: '12"',
          },
          available: 0,
          completed: 0,
          completedness: "Not Completed",
          material_name: "Forged Steel",
          size: '2 1/2"',
        },
      ],
      simpleItems: [
        { job_id: 1, package_id: 1, drawing_id: 1 },
        { job_id: 2, package_id: 2, drawing_id: 2 },
        { job_id: 1, package_id: 1, drawing_id: 3 },
      ],
    };

    it("filterItems => Should filter items successfully by job", () => {
      // can filter by job successfully...
      expect(utils.filterItems(testData.simpleItems).length).toBe(3);
      expect(
        utils.filterItems(testData.simpleItems, {
          jobIds: [1, 2],
        }).length
      ).toBe(3);
      expect(
        utils.filterItems(testData.simpleItems, { jobIds: [1] }).length
      ).toBe(2);
      expect(
        utils.filterItems(testData.simpleItems, { jobIds: [2] }).length
      ).toBe(1);
      expect(
        utils.filterItems(testData.simpleItems, { jobIds: [3] }).length
      ).toBe(0);
    });

    it("filterItems => Should filter items successfully by package", () => {
      // can filter by package successfully...
      expect(utils.filterItems(testData.simpleItems).length).toBe(3);
      expect(
        utils.filterItems(testData.simpleItems, {
          jobIds: [1, 2],
          packageIds: [1, 2],
        }).length
      ).toBe(3);
      expect(
        utils.filterItems(testData.simpleItems, {
          jobIds: [1, 2],
          packageIds: [1],
        }).length
      ).toBe(2);
      expect(
        utils.filterItems(testData.simpleItems, {
          jobIds: [1, 2],
          packageIds: [2],
        }).length
      ).toBe(1);
      expect(
        utils.filterItems(testData.simpleItems, {
          jobIds: [1],
          packageIds: [2],
        }).length
      ).toBe(0);
    });

    it("filterItems => Should filter items successfully by drawing", () => {
      // can filter by drawing successfully...
      expect(utils.filterItems(testData.simpleItems).length).toBe(3);
      expect(
        utils.filterItems(testData.simpleItems, {
          jobIds: [1, 2],
          packageIds: [1, 2],
          drawingIds: [1, 2, 3],
        }).length
      ).toBe(3);
      expect(
        utils.filterItems(testData.simpleItems, {
          jobIds: [1],
          packageIds: [1],
          drawingIds: [1, 3],
        }).length
      ).toBe(2);
      expect(
        utils.filterItems(testData.simpleItems, {
          jobIds: [1],
          packageIds: [1],
          drawingIds: [1, 2],
        }).length
      ).toBe(1);
      expect(
        utils.filterItems(testData.simpleItems, { drawingIds: [1, 2] }).length
      ).toBe(2);
      expect(
        utils.filterItems(testData.simpleItems, { drawingIds: [1, 2, 3] })
          .length
      ).toBe(3);
    });

    it("groupItems => Should return dataset when no grouping", () => {
      let stageIds = [],
        grouping = 0,
        groupingColumns = null;

      // no grouping test
      let grouped = utils.groupItems(testData.items, {
        grouping,
        groupingColumns,
      });
      expect(grouped.length).toBe(testData.items.length);
      expect(grouped[0].id == testData.items[0].id);
    });

    it("groupItems => Successful quantity summary when is_cut = 1", () => {
      const length = 48,
        denominator = 0.5,
        round_direction_id = 2;
      const data = [
        {
          job_id: 1,
          package_id: 1,
          drawing_id: 1,
          rejected: 0,
          is_cut: 1,
          denominator: denominator,
          round_direction_id: round_direction_id,
          length: {
            decimal: length,
          },
          available: 0,
          completed: 0,
          completedness: "Not Completed",
          material_name: "Forged Steel",
          size: '1 1/2"',
        },
        {
          job_id: 1,
          package_id: 1,
          drawing_id: 1,
          rejected: 0,
          is_cut: 1,
          denominator: denominator,
          round_direction_id: round_direction_id,
          length: {
            decimal: length,
          },
          available: 0,
          completed: 0,
          completedness: "Not Completed",
          material_name: "Forged Steel",
          size: '1 1/2"',
        },
      ];
      let stageIds = [],
        grouping = 1,
        groupingColumns = ["material_name", "length"];
      // grouping by 1 column test
      let grouped = utils.groupItems(data, {
        stageIds,
        grouping,
        groupingColumns,
      });
      // Only 1 material, Forged Steel
      expect(grouped.length).toBe(1);
      expect(grouped[0].hasOwnProperty("material_name"));
      expect(grouped[0].hasOwnProperty("is_cut"));
      expect(grouped[0].hasOwnProperty("rejected"));
      expect(grouped[0].hasOwnProperty("length"));
      expect(grouped[0].hasOwnProperty("quantity"));
      expect(grouped[0].is_cut).toBe(1);
      expect(grouped[0].quantity).toBe(96);
      expect(grouped[0].length.display).toBe('96"');
      expect(grouped[0].length.decimal).toBe(96);
    });

    it("groupItems => Successful when grouping = 1 and groupingColumns provided", () => {
      let stageIds = [],
        grouping = 1,
        groupingColumns = ["material_name"];

      // grouping by 1 column test
      let grouped = utils.groupItems(testData.items, {
        stageIds,
        grouping,
        groupingColumns,
      });
      // Only 1 material, Forged Steel
      expect(grouped.length).toBe(1);
      expect(grouped[0].quantity).toBe(testData.items.length);
      expect(grouped[0].hasOwnProperty("is_cut"));
    });

    it("groupItems => Successful when stageIds present, grouping = 1 and groupingColumns provided", () => {
      let stageIds = [1, 2],
        grouping = 1,
        groupingColumns = ["material_name"];

      // grouping by 1 column test and staging
      let grouped = utils.groupItems(testData.items, {
        stageIds,
        grouping,
        groupingColumns,
      });

      expect(grouped.length).toBe(1); // Only 1 material, Forged Steel
      expect(grouped[0].quantity).toBe(testData.items.length);
      // confirm that the columns expected when stageId is present is factored into grouping
      const stageIdReqCols = ["available", "completed", "is_cut"];
      stageIdReqCols.forEach((i) => expect(grouped[0].hasOwnProperty(i)));
    });

    it("groupItems => Successful when grouping = 3 and no other params provided", () => {
      let stageIds = [],
        grouping = 3,
        groupingColumns = [];

      // default grouping for group type 3
      const defaultGroupingCols = [
        "job_id",
        "package_id",
        "heat_number",
        "vendor",
        "material_name", //"material_type_id",
        "size",
      ];
      let grouped = utils.groupItems(testData.items, { grouping });
      expect(grouped.length).toBe(3);
      // confirm all the expected columns are present
      // Grouping by job, package, heat_number, vendor, material_name, and size
      defaultGroupingCols.forEach((i) => expect(grouped[0].hasOwnProperty(i)));
    });

    it("filterAndGroupItems => Success for no filters applied", () => {
      let filteredData = utils.filterAndGroupItems(testData.items);
      expect(_.isEqual(filteredData, testData.items));
    });

    it("filterAndGroupItems => Success for only filtering applied", () => {
      // filter by only job id
      let filteredData = utils.filterAndGroupItems(testData.simpleItems, {
        jobIds: [1],
      });
      expect(filteredData.length).toBe(2);

      // filter by job and package
      filteredData = utils.filterAndGroupItems(testData.simpleItems, {
        jobIds: [1],
        packageIds: [1],
      });
      expect(filteredData.length).toBe(2);

      // filter by job and package and drawing
      filteredData = utils.filterAndGroupItems(testData.simpleItems, {
        jobIds: [1],
        packageIds: [1],
        drawingIds: [3],
      });
      expect(filteredData.length).toBe(1);
    });

    it("filterAndGroupItems => Success for only grouping applied", () => {
      // group by only job_id
      let filteredData = utils.filterAndGroupItems(testData.items, {
        grouping: 1,
        groupingColumns: ["job_id"],
      });
      expect(filteredData.length).toBe(2);

      // group by material_id
      filteredData = utils.filterAndGroupItems(testData.items, {
        grouping: 1,
        groupingColumns: ["material_name"],
      });
      expect(filteredData.length).toBe(1);
      expect(filteredData[0].quantity).toBe(3);

      // group by material_id and drawing_id
      filteredData = utils.filterAndGroupItems(testData.items, {
        grouping: 2,
        groupingColumns: ["material_name"],
      });
      expect(filteredData.length).toBe(3);
      expect(filteredData[0].quantity).toBe(1);

      // group by default columns
      filteredData = utils.filterAndGroupItems(testData.items, { grouping: 3 });
      expect(filteredData.length).toBe(3);
      expect(filteredData[0].quantity).toBe(1);
    });

    it("filterAndGroupItems => Success for filtering and grouping applied", () => {
      // filter by job_id and group by size
      let filteredData = utils.filterAndGroupItems(testData.items, {
        jobIds: [1],
        grouping: 1,
        groupingColumns: ["job_id", "size"],
      });
      expect(filteredData.length).toBe(2);

      // filter by job_id and group by size, drawing_id
      filteredData = utils.filterAndGroupItems(testData.items, {
        jobIds: [1],
        grouping: 2,
        groupingColumns: ["job_id"],
      });
      expect(filteredData.length).toBe(2);

      // filter by job_id and group by defaults
      filteredData = utils.filterAndGroupItems(testData.items, {
        jobIds: [1, 2],
        grouping: 3,
      });
      expect(filteredData.length).toBe(3);
    });
  });

  describe("validateGuidInput", () => {
    it("should run regex check on string for guid", () => {
      const res = utils.validateGuidInput("");
      const res2 = utils.validateGuidInput("random string");
      const res3 = utils.validateGuidInput(
        "7224ad11-e7e8-49dd-b6a4-648ec05bfd1"
      );
      const res4 = utils.validateGuidInput(
        "7224ad11-e7e8-49dd-b6a4-648ec05bfd15"
      );
      expect(res).toEqual(false);
      expect(res2).toEqual(false);
      expect(res3).toEqual(false);
      expect(res4).toEqual(true);
    });
  });

  describe("deepArraysAreEqual", () => {
    it("returns true for equal arrays", () => {
      const arrayA = [1, "hello", { name: "test" }];
      const arrayB = [1, "hello", { name: "test" }];
      expect(utils.deepArraysAreEqual(arrayA, arrayB)).toBe(true);
    });

    it("returns false for different arrays", () => {
      const arrayA = [1, "hello", { name: "test" }];
      const arrayC = [1, "world", { name: "test2" }];
      expect(utils.deepArraysAreEqual(arrayA, arrayC)).toBe(false);
    });

    it("handles arrays with different lengths", () => {
      const arrayA = [1, 2, 3];
      const arrayD = [1, 2];
      expect(utils.deepArraysAreEqual(arrayA, arrayD)).toBe(false);
    });

    it("handles arrays with nested arrays", () => {
      const arrayE = [1, [2, 3], { name: "test3" }];
      const arrayF = [1, [2, 3], { name: "test3" }];
      expect(utils.deepArraysAreEqual(arrayE, arrayF)).toBe(true);
    });

    it("handles arrays with different nested elements", () => {
      const arrayG = [1, [2, 3], { name: "test4" }];
      const arrayH = [1, [2, 4], { name: "test4" }];
      expect(utils.deepArraysAreEqual(arrayG, arrayH)).toBe(false);
    });
  });

  describe("deepObjectsAreEqual", () => {
    it("returns true for equal objects", () => {
      const objectA = {
        name: "test",
        age: 25,
        address: { city: "Houston", country: "Texas" },
      };
      const objectB = {
        name: "test",
        age: 25,
        address: { city: "Houston", country: "Texas" },
      };
      expect(utils.deepObjectsAreEqual(objectA, objectB)).toBe(true);
    });

    it("returns false for different objects", () => {
      const objectA = { name: "test", age: 25 };
      const objectC = { name: "test2", age: 30 };
      expect(utils.deepObjectsAreEqual(objectA, objectC)).toBe(false);
    });

    it("handles objects with different properties", () => {
      const objectD = { name: "test", age: 25 };
      const objectE = { name: "test", age: 25, address: { city: "Houston" } };
      expect(utils.deepObjectsAreEqual(objectD, objectE)).toBe(false);
    });

    it("handles objects with nested objects", () => {
      const objectF = {
        name: "test",
        info: { hobbies: ["reading", "painting"] },
      };
      const objectG = {
        name: "test",
        info: { hobbies: ["reading", "painting"] },
      };
      expect(utils.deepObjectsAreEqual(objectF, objectG)).toBe(true);
    });

    it("handles objects with different nested elements", () => {
      const objectH = {
        name: "test",
        info: { hobbies: ["reading", "cooking"] },
      };
      const objectI = {
        name: "test",
        info: { hobbies: ["reading", "painting"] },
      };
      expect(utils.deepObjectsAreEqual(objectH, objectI)).toBe(false);
    });
  });

  describe("validateGuidInput", () => {
    it("should run regex check on string for guid", () => {
      const res = utils.validateGuidInput("");
      const res2 = utils.validateGuidInput("random string");
      const res3 = utils.validateGuidInput(
        "7224ad11-e7e8-49dd-b6a4-648ec05bfd1"
      );
      const res4 = utils.validateGuidInput(
        "7224ad11-e7e8-49dd-b6a4-648ec05bfd15"
      );
      expect(res).toEqual(false);
      expect(res2).toEqual(false);
      expect(res3).toEqual(false);
      expect(res4).toEqual(true);
    });
  });

  describe("validateGuidInput", () => {
    it("should run regex check on string for guid", () => {
      const res = utils.validateGuidInput("");
      const res2 = utils.validateGuidInput("random string");
      const res3 = utils.validateGuidInput(
        "7224ad11-e7e8-49dd-b6a4-648ec05bfd1"
      );
      const res4 = utils.validateGuidInput(
        "7224ad11-e7e8-49dd-b6a4-648ec05bfd15"
      );
      expect(res).toEqual(false);
      expect(res2).toEqual(false);
      expect(res3).toEqual(false);
      expect(res4).toEqual(true);
    });
  });
  describe("alphaNumericSortByName", () => {
    it("should return an ordered array", () => {
      const unordered = [
        { id: 4, name: "Object" },
        { id: 2, name: "AB" },
        { id: 1, name: "1 Z" },
        { id: 5, name: "To" },
        { id: 3, name: "ab" },
      ];
      const orderedAnswer = [
        { id: 1, name: "1 Z" },
        { id: 2, name: "AB" },
        { id: 3, name: "ab" },
        { id: 4, name: "Object" },
        { id: 5, name: "To" },
      ];

      const orderedResult = utils.alphaNumericSortByName(unordered);

      expect(orderedAnswer[0].id === orderedResult[0].id).toEqual(true);
      expect(orderedAnswer[1].id === orderedResult[1].id).toEqual(true);
      expect(orderedAnswer[2].id === orderedResult[2].id).toEqual(true);
      expect(orderedAnswer[3].id === orderedResult[3].id).toEqual(true);
      expect(orderedAnswer[4].id === orderedResult[4].id).toEqual(true);
    });
  });
  describe("_utils.formatTime", () => {
    it("should format a Unix time (seconds) to the server timezone string", () => {
      const tests = [
        { input: 1704085200, expectedDate: "01-01-2024" }, // Sat Jan 04 2024 00:00:00 GMT-0500
        { input: 1735614000, expectedDate: "12-30-2024" }, // Mon Dec 30 2024 22:00:00 GMT-0500
        { input: 1735707600, expectedDate: "01-01-2025" }, // 2025-01-01 00:00:00.000 GMT-0500
      ];
      for (const { input, expectedDate } of tests) {
        expect(utils.formatDate(input)).toEqual(expectedDate);
      }
    });

    it("should convert user input to server unix time when toServerUnixTime=true", () => {
      // in order to make sure our in/out is right I used the client to help generate the timestamp...
      // what does the browser say is the time for new Date('01/01/2025').getTime() in PST...
      const tests = [
        { input: 1735718400000, output: 1735707600 }, // 01-01-2025
        { input: 1767168000000, output: 1767157200 }, // 12-31-2025
      ];

      // Mock DateTime.local().offset and DateTime.now().setZone().offset
      const localOffset = -420; // DateTime.now().setZone("America/Los_Angeles").offset // e.g., PST is UTC-8
      const serverOffset = -240; //  DateTime.now().setZone("America/New_York").offset // e.g., EST is UTC-5

      jest.spyOn(DateTime, "local").mockReturnValue({ offset: localOffset });
      jest.spyOn(DateTime, "now").mockReturnValue({
        setZone: () => ({ offset: serverOffset }),
      });

      for (const { input, output } of tests) {
        expect(utils.formatDate(input, true)).toEqual(output);
      }

      // Restore mocks
      DateTime.local.mockRestore();
      DateTime.now.mockRestore();
    });
  });
});
