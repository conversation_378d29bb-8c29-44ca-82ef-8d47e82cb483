// copied from msuite_api
"use strict";

const conversions = {
  findGcd: (a, b) => {
    return b ? conversions.findGcd(b, a % b) : a;
  },

  reduceFraction: (numerator, denominator) => {
    let gcd = conversions.findGcd(numerator, denominator);
    return [numerator / gcd, denominator / gcd];
  },

  inchToFeet: (wholeInches) => {
    let feet = Math.floor(wholeInches / 12);
    let inches = wholeInches % 12;
    return [feet, inches];
  },

  // TODO - move rounding to its own function
  decToArcInch: (decimal, denominator, useFeet, roundNearest) => {
    let whole = Math.trunc(decimal);
    let dec = decimal - Math.trunc(decimal);
    let num =
      parseInt(roundNearest) === 2
        ? Math.ceil(dec * denominator)
        : parseInt(roundNearest) === 1
        ? Math.floor(dec * denominator)
        : Math.round(dec * denominator);
    let rf = conversions.reduceFraction(num, denominator);

    if (num === denominator) {
      whole += 1;
      num = 0;
    }

    let output;
    if (useFeet) {
      let fi = conversions.inchToFeet(whole);
      output =
        num === 0
          ? fi[0] > 0
            ? `${fi[0]}'-${fi[1]}"`
            : `${fi[1]}"`
          : fi[0] > 0
          ? `${fi[0]}'-${fi[1]} ${rf[0]}/${rf[1]}"`
          : `${fi[1]} ${rf[0]}/${rf[1]}"`;
    } else {
      // todo - check if not including leading 0 is intentional
      output =
        num === 0
          ? `${whole}"`
          : whole > 0
          ? `${whole} ${rf[0]}/${rf[1]}"`
          : `${rf[0]}/${rf[1]}"`;
    }

    return output;
  },
  roundLen: (decimal, denominator, useFeet, roundNearest) => {
    let whole = Math.trunc(decimal);
    let dec = decimal - Math.trunc(decimal);
    let num =
      parseInt(roundNearest) === 2
        ? Math.ceil(dec * denominator)
        : parseInt(roundNearest) === 1
        ? Math.floor(dec * denominator)
        : Math.round(dec * denominator);
    let rf = conversions.reduceFraction(num, denominator);

    if (num === denominator) {
      whole += 1;
      num = 0;
    }

    if (useFeet) {
      let fi = conversions.inchToFeet(whole);
      return num === 0
        ? fi[0] > 0
          ? fi[0] * 12 + fi[1]
          : fi[1]
        : fi[0] > 0
        ? fi[0] * 12 + fi[1] + rf[0] / rf[1]
        : fi[1] + rf[0] / rf[1];
    } else {
      return num === 0
        ? whole
        : whole > 0
        ? whole + rf[0] / rf[1]
        : rf[0] / rf[1];
    }
  },
  secToTime: (s, m = 1) => {
    let hours =
      Math.floor(s / 3600) < 10
        ? "0" + Math.floor(s / 3600)
        : Math.floor(s / 3600);
    let minutes =
      Math.floor(s / 60) % 60 < 10
        ? "0" + (Math.floor(s / 60) % 60)
        : Math.floor(s / 60) % 60;
    let seconds = s % 60 < 10 ? "0" + (s % 60) : s % 60;

    if (s == 0) {
      return m == 1 ? "00:00" : "00:00:00";
    } else if (hours == "00" && minutes == "00" && m == 1) {
      return "<1min";
    } else {
      return m == 1
        ? hours + ":" + minutes
        : hours + ":" + minutes + ":" + seconds;
    }
  },

  convertArray: (arr, useFeet, apiType = null) => {
    const arrCopy = [...arr];
    // add keys to this array if null values should be displayed as 0"
    // individual length to be added if this function is used for MyWork
    const feetInchPropsNoNull = ["length"]; //, "individual_length"];
    // add to this one if null values should be returned as is
    const feetInchProps = [
      "bend_dim_a",
      "bend_dim_b",
      "bend_dim_c",
      "bend_dim_d",
      "bend_dim_e",
      "bend_dim_f",
      "bend_dim_g",
      "bend_mark_1",
      "bend_mark_2",
      "bend_deduct",
    ];
    return arrCopy.map((item) => {
      feetInchPropsNoNull.forEach((p) => {
        if (Object.prototype.hasOwnProperty.call(item, p)) {
          item[p] = {
            decimal: item[p],
            display: conversions.decToArcInch(
              item[p],
              1 / item.denominator,
              useFeet,
              item.round_direction_id
            ),
          };
        }
      });
      feetInchProps.forEach((p) => {
        if (Object.prototype.hasOwnProperty.call(item, p)) {
          item[p] = {
            decimal: item[p],
            display:
              item[p] === null
                ? item[p]
                : conversions.decToArcInch(
                    item[p],
                    1 / item.denominator,
                    useFeet,
                    item.round_direction_id
                  ),
          };
        }
      });

      return item;
    });
  },
};

export default conversions;
