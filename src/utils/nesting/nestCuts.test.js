import { nestCuts } from "../../_utils";
import testData from "./cuts";

describe("nesting algorithm tests - for accepted scrap and total nests", () => {
  describe("push mode", () => {
    describe("no head or tail", () => {
      test("nestCuts nests all cuts 20', no head or tail", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: false,
          shouldCutTail: false,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 240,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(10);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 10', no head or tail", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: false,
          shouldCutTail: false,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 120,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(13);
        expect(fivePlusScrapNests.length).toBe(1);
      });

      test("nestCuts nests all cuts 1', no head no tail", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: false,
          shouldCutTail: false,
          headCutLength: 0.5,
          tailCutLength: 0,
          minLimit: 10,
          minLength: 0,
          stockLength: 12,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(69);
        expect(fivePlusScrapNests.length).toBe(41);
      });

      test("nestCuts nests all cuts 42', no head or tail", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: false,
          shouldCutTail: false,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 504,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(3);
        expect(fivePlusScrapNests.length).toBe(1);
      });
    });

    describe("only head cut", () => {
      test("nestCuts nests all cuts 20', with head no tail", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: true,
          shouldCutTail: false,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 240,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(9);
        expect(fivePlusScrapNests.length).toBe(1);
      });

      test("nestCuts nests all cuts 10', with head no tail", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: true,
          shouldCutTail: false,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 120,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(15);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 1', with head no tail", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: true,
          shouldCutTail: false,
          headCutLength: 0.5,
          tailCutLength: 0,
          minLimit: 10,
          minLength: 0,
          stockLength: 12,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(61);
        expect(fivePlusScrapNests.length).toBe(36);
      });

      test("nestCuts nests all cuts 42', with head no tail", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: true,
          shouldCutTail: false,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 504,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(4);
        expect(fivePlusScrapNests.length).toBe(1);
      });
    });

    describe("only tail cut < min limit", () => {
      test("nestCuts nests all cuts 20', with tail < min limit, no head", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: false,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 240,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(8);
        expect(fivePlusScrapNests.length).toBe(1);
      });

      test("nestCuts nests all cuts 10', with tail < min limit, no head", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: false,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 120,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(12);
        expect(fivePlusScrapNests.length).toBe(1);
      });

      test("nestCuts nests all cuts 1', with tail < min limit, no head", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: false,
          shouldCutTail: true,
          headCutLength: 0.5,
          tailCutLength: 2,
          minLimit: 10,
          minLength: 0,
          stockLength: 12,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(0);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 42', with tail < min limit, no head", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: false,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 504,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(4);
        expect(fivePlusScrapNests.length).toBe(2);
      });
    });

    describe("tail cut < min limit and head cut", () => {
      test("nestCuts nests all cuts 20', with tail < min limit and head", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: true,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 240,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(8);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 10', with tail < min limit and head", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: true,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 120,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(8);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 1', with tail < min limit and head", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: true,
          shouldCutTail: true,
          headCutLength: 0.5,
          tailCutLength: 2,
          minLimit: 10,
          minLength: 0,
          stockLength: 12,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).toStrictEqual([]);
      });

      test("nestCuts nests all cuts 42', with tail < min limit and head", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: true,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 504,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(5);
        expect(fivePlusScrapNests.length).toBe(1);
      });
    });

    describe("only tail cut > min limit", () => {
      test("nestCuts nests all cuts 20', with tail > min limit, no head", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: false,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: -5,
          minLength: 4,
          stockLength: 240,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(8);
        expect(fivePlusScrapNests.length).toBe(1);
      });

      test("nestCuts nests all cuts 10', with tail > min limit, no head", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: false,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: -5,
          minLength: 4,
          stockLength: 120,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(17);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 1', with tail > min limit, no head", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: false,
          shouldCutTail: true,
          headCutLength: 0.5,
          tailCutLength: 2,
          minLimit: 1,
          minLength: 0,
          stockLength: 12,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(10);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 42', with tail > min limit, no head", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: false,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: -5,
          minLength: 4,
          stockLength: 504,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(5);
        expect(fivePlusScrapNests.length).toBe(1);
      });
    });

    describe("tail cut > min limit and head cut", () => {
      test("nestCuts nests all cuts 20', with tail > min limit and head", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: true,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: -5,
          minLength: 4,
          stockLength: 240,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(7);
        expect(fivePlusScrapNests.length).toBe(2);
      });

      test("nestCuts nests all cuts 10', with tail > min limit and head", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: true,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: -5,
          minLength: 4,
          stockLength: 120,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(9);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 1', with tail > min limit and head", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: true,
          shouldCutTail: true,
          headCutLength: 0.5,
          tailCutLength: 2,
          minLimit: 1,
          minLength: 0,
          stockLength: 12,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(11);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 42', with tail > min limit and head", () => {
        const nestSettings = {
          isPush: false,
          shouldCutHead: true,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: -5,
          minLength: 4,
          stockLength: 504,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(4);
        expect(fivePlusScrapNests.length).toBe(1);
      });
    });
  });

  describe("pull mode", () => {
    /**
     * Pull variant of above tests
     * Currently there is no functional difference in nesting for push vs pull
     * This is in case we ever do make changes to catch them
     */
    describe("no head or tail", () => {
      test("nestCuts nests all cuts 20', no head or tail, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: false,
          shouldCutTail: false,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 240,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(10);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 10', no head or tail, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: false,
          shouldCutTail: false,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 120,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(13);
        expect(fivePlusScrapNests.length).toBe(1);
      });

      test("nestCuts nests all cuts 1', no head or tail, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: false,
          shouldCutTail: false,
          headCutLength: 0.5,
          tailCutLength: 2,
          minLimit: 10,
          minLength: 0,
          stockLength: 12,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(69);
        expect(fivePlusScrapNests.length).toBe(41);
      });

      test("nestCuts nests all cuts 42', no head or tail, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: false,
          shouldCutTail: false,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 504,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(3);
        expect(fivePlusScrapNests.length).toBe(1);
      });
    });

    describe("only head cut", () => {
      test("nestCuts nests all cuts 20', with head no tail, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: true,
          shouldCutTail: false,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 240,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(9);
        expect(fivePlusScrapNests.length).toBe(1);
      });

      test("nestCuts nests all cuts 10', with head no tail, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: true,
          shouldCutTail: false,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 120,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(15);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 1', with head no tail, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: true,
          shouldCutTail: false,
          headCutLength: 0.5,
          tailCutLength: 2,
          minLimit: 10,
          minLength: 0,
          stockLength: 12,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(61);
        expect(fivePlusScrapNests.length).toBe(36);
      });

      test("nestCuts nests all cuts 42', with head no tail, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: true,
          shouldCutTail: false,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 504,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(4);
        expect(fivePlusScrapNests.length).toBe(1);
      });
    });

    describe("only tail cut < min limit", () => {
      test("nestCuts nests all cuts 20', with tail < min limit, no head, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: false,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 240,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(8);
        expect(fivePlusScrapNests.length).toBe(1);
      });

      test("nestCuts nests all cuts 10', with tail < min limit, no head, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: false,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 120,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(12);
        expect(fivePlusScrapNests.length).toBe(1);
      });

      test("nestCuts nests all cuts 1', with tail < min limit, no head, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: false,
          shouldCutTail: true,
          headCutLength: 0.5,
          tailCutLength: 2,
          minLimit: 10,
          minLength: 0,
          stockLength: 12,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(0);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 42', with tail < min limit, no head, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: false,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 504,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(4);
        expect(fivePlusScrapNests.length).toBe(2);
      });
    });

    describe("tail cut < min limit and head cut", () => {
      test("nestCuts nests all cuts 20', with tail < min limit and head, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: true,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 240,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(8);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 10', with tail < min limit and head, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: true,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 120,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(8);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 1', with tail < min limit and head, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: true,
          shouldCutTail: true,
          headCutLength: 0.5,
          tailCutLength: 2,
          minLimit: 10,
          minLength: 0,
          stockLength: 12,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).toStrictEqual([]);
      });

      test("nestCuts nests all cuts 42', with tail < min limit and head, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: true,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: 8,
          minLength: 4,
          stockLength: 504,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(5);
        expect(fivePlusScrapNests.length).toBe(1);
      });
    });

    describe("only tail cut > min limit", () => {
      test("nestCuts nests all cuts 20', with tail > min limit, no head, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: false,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: -5,
          minLength: 4,
          stockLength: 240,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(8);
        expect(fivePlusScrapNests.length).toBe(1);
      });

      test("nestCuts nests all cuts 10', with tail > min limit, no head, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: false,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: -5,
          minLength: 4,
          stockLength: 120,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(17);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 1', with tail > min limit, no head, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: false,
          shouldCutTail: true,
          headCutLength: 0.5,
          tailCutLength: 2,
          minLimit: 1,
          minLength: 0,
          stockLength: 12,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(10);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 42', with tail > min limit, no head, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: false,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: -5,
          minLength: 4,
          stockLength: 504,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(5);
        expect(fivePlusScrapNests.length).toBe(1);
      });
    });

    describe("tail cut > min limit and head cut", () => {
      test("nestCuts nests all cuts 20', with tail > min limit and head, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: true,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: -5,
          minLength: 4,
          stockLength: 240,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(7);
        expect(fivePlusScrapNests.length).toBe(2);
      });

      test("nestCuts nests all cuts 10', with tail > min limit and head, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: true,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: -5,
          minLength: 4,
          stockLength: 120,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(9);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 1', with tail > min limit and head, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: true,
          shouldCutTail: true,
          headCutLength: 0.5,
          tailCutLength: 2,
          minLimit: 1,
          minLength: 0,
          stockLength: 12,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(11);
        expect(fivePlusScrapNests.length).toBe(0);
      });

      test("nestCuts nests all cuts 42', with tail > min limit and head, pull", () => {
        const nestSettings = {
          isPush: true,
          shouldCutHead: true,
          shouldCutTail: true,
          headCutLength: 2,
          tailCutLength: 2,
          minLimit: -5,
          minLength: 4,
          stockLength: 504,
          kerf: 0.125,
          isMachine: true,
        };
        const nestedList = nestCuts(
          testData.defaultCutList,
          nestSettings,
          true
        );
        expect(nestedList).not.toStrictEqual([]);
        expect(nestedList[0].scrap).toBeLessThan(2);
        const twoPlusScrapNests = nestedList.filter((o) => o.scrap > 2);
        const fivePlusScrapNests = nestedList.filter((o) => o.scrap > 5);
        expect(twoPlusScrapNests.length).toBe(4);
        expect(fivePlusScrapNests.length).toBe(1);
      });
    });
  });
});
