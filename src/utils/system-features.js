/**
 *  This was auto-generated.
 *  Try to use these constants instead of numbers in code.
 */
const SystemFeatures = {
  SHIFTS: 1,
  ANNOTATOR: 2,
  GENERIC: 3,
  CREWS: 5,
  MULTI_USER_LOGIN: 6,
  HELP_AREA: 7,
  ARCHIVE_AREA: 8,
  NOTIFICATIONS: 9,
  TIGER<PERSON>OP_INTEGRATION: 10,
  PRIORITIES: 11,
  <PERSON><PERSON><PERSON><PERSON>: 12,
  PACKAGE_MAP: 13,
  RELEASES_PAGE: 14,
  ASCII_TIMESHEET: 15,
  FIELDPRO: 16,
  DELIVERY_SIGNATURES: 17,
  DELIVERY_PHOTO: 18,
  POWERBI: 19,
  CUTTING_MACHINE_EXPORT: 20,
  CLOUD_SPOOLING: 21,
  CLOUD_SHEETS: 22,
  FORGE_API_V2: 23,
  SINGLE_PICK_SPOOL_CREATION: 24,
  CS_BUTTONS_ALWAYS_VISIBLE: 25,
  CREATE_MANUAL_DIMENSION: 26,
  EDIT_MANUAL_DIMENSION: 27,
  DELETE_MANUAL_DIMENSION: 28,
  FOR<PERSON>_DIMENSIONS_V2: 29,
  V1_DIMENSION_MIGRATION_TO_DIMENSION_DEFINITIONS: 30,
  MOVED_REVIT_VERTICES_UPDATE_DIMENSIONS: 31,
  ENABLE_POST_PRE_SPOOLING___GRAPHING_DATA: 32,
  POWERBI_EXTRACT_TO_AZURE_BLOB_STORAGE: 33,
  POWERBI_EXTRACT_TO_SHAREPOINT: 34,
  POWERBI_EMBEDDED_ANALYTICS: 35,
  OLD_ECHART_PHP_REPORTS: 36,
  CONDUIT_BENDS_SUPPORT: 37,
  CONDUIT_BENDS_SUPPORT_PRE_SPOOLING: 38,
  FORGE_VIEWER_MARKUPS: 39,
  JOINT_LOG_SHOW_ONLY_TIMERS: 40,
  MANAGE_MAJS: 41,
  FLOATMODE_NWT_COMPATIBILITY: 42,
  FLOATMODE_NWT_COMPATIBILITY___DEPRECATED: 43,
  MIXPANEL_USER_BEHAVIOR_TRACKING: 44,
  LOGGER_CONSOLE_TRACE: 46,
  MULTIPLE_AUTODESK_ACCOUNT_MANAGEMENT: 47,
  IGNORE_DRAWING_EXCLUSION_FLAG: 48,
  WORK_FLOW_ASSIGNMENTS: 49,
  ADVANCED_GROUPING: 51,
  TIGERSTOP_LOGGING: 52,
  CUSTOM_COLUMNS: 53,
  NEW_TABLES_POC: 54,
};

export default SystemFeatures;
