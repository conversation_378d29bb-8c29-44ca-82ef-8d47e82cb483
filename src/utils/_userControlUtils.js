import { trackMixPanelEvent } from "./_mixPanelUtils";

export const setUpListeners = () => {
  const wrapper = document.getElementById("root");

  wrapper.addEventListener("click", (event) => {
    try {
      //ignore these
      if (
        event.target.nodeName === "DIV" &&
        ([
          "nav-menu ",
          "nav-menu nav-expanded",
          "table-container",
          "",
          "table-container-tabs",
          "action-row",
          "layout-body ",
          "selectable-filter-wrapper",
          "work-table-filter",
          "layout-header",
          "layout-footer",
          "header__nav",
          "ag-side-buttons",
          "ag-column-select-list ag-focus-managed",
          "ag-center-cols-viewport",
          "ag-react-container",
        ].includes(event.target.className) ||
          event.target.className.startsWith("ag-cell") ||
          event.target.className.startsWith("ag-row"))
      ) {
        return;
      }

      if (
        ![
          "BUTTON",
          "INPUT",
          "A",
          "I",
          "SPAN",
          "path",
          "P",
          "svg",
          "line",
          "DIV",
        ].includes(event.target.nodeName)
      ) {
        return;
      }

      if (event.target.nodeName === "svg") {
        const parentElement = event.target.parentElement;

        if (
          parentElement &&
          parentElement.dataset &&
          parentElement.dataset.filtertype
        ) {
          const filter = parentElement.dataset.filtertype;
          const category = "Filters";
          const description = `${category} | ${filter}`;
          const filterStatus = "removed";

          trackMixPanelEvent(
            `${filter} filter ${filterStatus}`,
            0,
            category,
            Date.now(),
            description
          );
          return;
        }
        return;
      }

      //#region nav links
      if (
        event.target.className &&
        event.target.className.includes("nav-menu-link-subsection-title")
      ) {
        const eventName = event.target.innerText;
        const category = "Nav Menu";
        const description = "Nav sub section link was clicked";
        trackMixPanelEvent(
          `${category} | ${eventName}`,
          0,
          category,
          Date.now(),
          description
        );
        return;
      }
      if (
        event.target.className &&
        (event.target.className.includes("nav-menu-link-icon") ||
          event.target.className.includes("nav-menu-link-title"))
      ) {
        const eventName = event.target.parentElement.lastChild.innerText;
        const category = "Nav Menu";
        const description = "Nav link was clicked";
        trackMixPanelEvent(
          `${category} | ${eventName}`,
          0,
          category,
          Date.now(),
          description
        );
        return;
      }
      if (
        event.target.className &&
        event.target.className.includes("nav-menu-link")
      ) {
        const eventName = event.target.lastChild.innerText;
        const category = "Nav Menu";
        const description = "Nav link was clicked";
        trackMixPanelEvent(
          `${category} | ${eventName}`,
          0,
          category,
          Date.now(),
          description
        );
        return;
      }

      if (
        event.target.className &&
        event.target.className.includes("pending-approvals-notification-icon")
      ) {
        const eventName = event.target.parentElement.lastChild.innerText;
        const category = "Nav Menu";
        const description = "Nav link was clicked";
        trackMixPanelEvent(
          `${category} | ${eventName}`,
          0,
          category,
          Date.now(),
          description
        );
        return;
      }
      //#endregion

      //#region Filters

      if (
        event.target.className &&
        event.target.className.includes("menu-item")
      ) {
        const wrapperElement = event.target.closest(
          ".selectable-filter-wrapper"
        );
        const filter = wrapperElement.firstChild.innerText;
        const category = "Filters";
        const description = `${category} | ${filter}`;

        const filterStatus = event.target.className.includes("selected")
          ? "removed"
          : "selected";
        trackMixPanelEvent(
          `${filter} filter ${filterStatus}`,
          0,
          category,
          Date.now(),
          description
        );
        return;
      }

      //#endregion

      //#region Work tabs
      if (
        event.target.className &&
        event.target.className.includes("table-container-tab")
      ) {
        const eventName = event.target.innerText;
        const category = "Work Tab";
        const description = "Work tab was clicked";
        trackMixPanelEvent(
          `${category} | ${eventName}`,
          0,
          category,
          Date.now(),
          description
        );
        return;
      }
      //#endregion

      const eventName = event?.target?.innerText;
      const category = null;
      const description = `${event?.target?.className} | ${event?.target?.nodeName}`;
      const excludedEvents = ["", "Upload", "Upload Drawings"];
      if (eventName && !excludedEvents.includes(eventName)) {
        trackMixPanelEvent(eventName, 0, category, Date.now(), description);
      }
    } catch {
      return;
    }
  });
};
