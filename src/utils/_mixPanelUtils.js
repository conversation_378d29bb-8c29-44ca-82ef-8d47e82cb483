import store from "../redux/store";

// DATA TRACKING
import { trackEvent, trackLinks } from "@sbd-ctg/user-behavior-tracking";
import {
  identifyUser,
  createProfile,
  reset,
} from "@sbd-ctg/user-behavior-tracking";

export const identifyMixPanelUser = async (username, features) => {
  if (!username || !features?.includes(44)) return;

  const name = username?.split("@")[0];
  reset();
  identifyUser(username);
  createProfile({ $email: username, $name: name });
};

export const trackMixPanelEvent = (
  eventName,
  quantity,
  category,
  eventStartTime,
  description
) => {
  const { features, userInfo, systemSettings } = store?.getState()?.profileData;

  if (!eventName || !features?.includes(44)) return;

  const eventEndTime = Date.now();
  let duration;

  // calculate event duration
  if (eventStartTime) duration = eventEndTime - eventStartTime;

  const data = {
    // number of events/results
    quantity: quantity || null,
    crm_id: systemSettings?.zoho_account_id ?? "",
    tenant_id: systemSettings?.domain_prefix ?? "",
    event_name: eventName,
    user_name: userInfo?.username ?? "",
    user_id: userInfo?.id ?? "",
    category: category || null,
    duration: duration ? `${duration}ms` : null,
    description: description || null,
  };

  trackEvent(eventName, data);
};

export const trackLinkEvent = (query, eventName, category, description) => {
  const { features, userInfo, systemSettings } = store?.getState()?.profileData;

  if (!eventName || !features?.includes(44)) return;

  const data = {
    // number of events/results
    quantity: 0,
    crm_id: systemSettings?.zoho_account_id ?? "",
    tenant_id: systemSettings?.domain_prefix ?? "",
    event_name: eventName,
    user_name: userInfo?.username ?? "",
    user_id: userInfo?.id ?? "",
    category: category || null,
    description: description || null,
  };

  trackLinks(query, eventName, data);
};
