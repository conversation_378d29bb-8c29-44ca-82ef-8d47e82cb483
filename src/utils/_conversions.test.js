import conversions from "./_conversions";

describe("conversion methods", () => {
  const testCases = [
    0.5,
    2.5,
    0.25,
    22.5,
    100.25,
    0.6,
    3.125,
    0.0625,
    12.03125,
  ];
  describe("length conversions", () => {
    const generateTestItems = (denominator, round_direction_id) => {
      let testItems = [];
      for (let i = 0; i < 9; i++) {
        testItems.push({
          length: testCases[i],
          denominator: denominator,
          round_direction_id: round_direction_id,
        });
      }
      return testItems;
    };

    // when number is median we are defaulting to ROUND UP
    describe("decToArcInch", () => {
      describe("useFeet - 1", () => {
        it("round down - 1/2", () => {
          const useFeet = 1;
          const round = 1;
          const expected = [
            `0 1/2"`,
            `2 1/2"`,
            `0"`,
            `1'-10 1/2"`,
            `8'-4"`,
            `0 1/2"`,
            `3"`,
            `0"`,
            `1'-0"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 2, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round to nearest - 1/2", () => {
          const useFeet = 1;
          const round = 3;
          const expected = [
            `0 1/2"`,
            `2 1/2"`,
            `0 1/2"`,
            `1'-10 1/2"`,
            `8'-4 1/2"`,
            `0 1/2"`,
            `3"`,
            `0"`,
            `1'-0"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 2, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round up - 1/2", () => {
          const useFeet = 1;
          const round = 2;
          const expected = [
            `0 1/2"`,
            `2 1/2"`,
            `0 1/2"`,
            `1'-10 1/2"`,
            `8'-4 1/2"`,
            `1"`,
            `3 1/2"`,
            `0 1/2"`,
            `1'-0 1/2"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 2, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round down - 1/4", () => {
          const useFeet = 1;
          const round = 1;
          const expected = [
            `0 1/2"`,
            `2 1/2"`,
            `0 1/4"`,
            `1'-10 1/2"`,
            `8'-4 1/4"`,
            `0 1/2"`,
            `3"`,
            `0"`,
            `1'-0"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 4, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round to nearest - 1/4", () => {
          const useFeet = 1;
          const round = 3;
          const expected = [
            `0 1/2"`,
            `2 1/2"`,
            `0 1/4"`,
            `1'-10 1/2"`,
            `8'-4 1/4"`,
            `0 1/2"`,
            `3 1/4"`,
            `0"`,
            `1'-0"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 4, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round up - 1/4", () => {
          const useFeet = 1;
          const round = 2;
          const expected = [
            `0 1/2"`,
            `2 1/2"`,
            `0 1/4"`,
            `1'-10 1/2"`,
            `8'-4 1/4"`,
            `0 3/4"`,
            `3 1/4"`,
            `0 1/4"`,
            `1'-0 1/4"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 4, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round down - 1/8", () => {
          const useFeet = 1;
          const round = 1;
          const expected = [
            `0 1/2"`,
            `2 1/2"`,
            `0 1/4"`,
            `1'-10 1/2"`,
            `8'-4 1/4"`,
            `0 1/2"`,
            `3 1/8"`,
            `0"`,
            `1'-0"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 8, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round to nearest - 1/8", () => {
          const useFeet = 1;
          const round = 3;
          const expected = [
            `0 1/2"`,
            `2 1/2"`,
            `0 1/4"`,
            `1'-10 1/2"`,
            `8'-4 1/4"`,
            `0 5/8"`,
            `3 1/8"`,
            `0 1/8"`,
            `1'-0"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 8, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round up - 1/8", () => {
          const useFeet = 1;
          const round = 2;
          const expected = [
            `0 1/2"`,
            `2 1/2"`,
            `0 1/4"`,
            `1'-10 1/2"`,
            `8'-4 1/4"`,
            `0 5/8"`,
            `3 1/8"`,
            `0 1/8"`,
            `1'-0 1/8"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 8, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round down - 1/16", () => {
          const useFeet = 1;
          const round = 1;
          const expected = [
            `0 1/2"`,
            `2 1/2"`,
            `0 1/4"`,
            `1'-10 1/2"`,
            `8'-4 1/4"`,
            `0 9/16"`,
            `3 1/8"`,
            `0 1/16"`,
            `1'-0"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 16, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round to nearest - 1/16", () => {
          const useFeet = 1;
          const round = 3;
          const expected = [
            `0 1/2"`,
            `2 1/2"`,
            `0 1/4"`,
            `1'-10 1/2"`,
            `8'-4 1/4"`,
            `0 5/8"`,
            `3 1/8"`,
            `0 1/16"`,
            `1'-0 1/16"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 16, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round up - 1/16", () => {
          const useFeet = 1;
          const round = 2;
          const expected = [
            `0 1/2"`,
            `2 1/2"`,
            `0 1/4"`,
            `1'-10 1/2"`,
            `8'-4 1/4"`,
            `0 5/8"`,
            `3 1/8"`,
            `0 1/16"`,
            `1'-0 1/16"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 16, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });
      });
      describe("useFeet - 0", () => {
        it("round down - 1/2", () => {
          const useFeet = 0;
          const round = 1;
          const expected = [
            `1/2"`,
            `2 1/2"`,
            `0"`,
            `22 1/2"`,
            `100"`,
            `1/2"`,
            `3"`,
            `0"`,
            `12"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 2, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round to nearest - 1/2", () => {
          const useFeet = 0;
          const round = 3;
          const expected = [
            `1/2"`,
            `2 1/2"`,
            `1/2"`,
            `22 1/2"`,
            `100 1/2"`,
            `1/2"`,
            `3"`,
            `0"`,
            `12"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 2, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round up - 1/2", () => {
          const useFeet = 0;
          const round = 2;
          const expected = [
            `1/2"`,
            `2 1/2"`,
            `1/2"`,
            `22 1/2"`,
            `100 1/2"`,
            `1"`,
            `3 1/2"`,
            `1/2"`,
            `12 1/2"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 2, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round down - 1/4", () => {
          const useFeet = 0;
          const round = 1;
          const expected = [
            `1/2"`,
            `2 1/2"`,
            `1/4"`,
            `22 1/2"`,
            `100 1/4"`,
            `1/2"`,
            `3"`,
            `0"`,
            `12"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 4, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round to nearest - 1/4", () => {
          const useFeet = 0;
          const round = 3;
          const expected = [
            `1/2"`,
            `2 1/2"`,
            `1/4"`,
            `22 1/2"`,
            `100 1/4"`,
            `1/2"`,
            `3 1/4"`,
            `0"`,
            `12"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 4, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round up - 1/4", () => {
          const useFeet = 0;
          const round = 2;
          const expected = [
            `1/2"`,
            `2 1/2"`,
            `1/4"`,
            `22 1/2"`,
            `100 1/4"`,
            `3/4"`,
            `3 1/4"`,
            `1/4"`,
            `12 1/4"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 4, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round down - 1/8", () => {
          const useFeet = 0;
          const round = 1;
          const expected = [
            `1/2"`,
            `2 1/2"`,
            `1/4"`,
            `22 1/2"`,
            `100 1/4"`,
            `1/2"`,
            `3 1/8"`,
            `0"`,
            `12"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 8, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round to nearest - 1/8", () => {
          const useFeet = 0;
          const round = 3;
          const expected = [
            `1/2"`,
            `2 1/2"`,
            `1/4"`,
            `22 1/2"`,
            `100 1/4"`,
            `5/8"`,
            `3 1/8"`,
            `1/8"`,
            `12"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 8, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round up - 1/8", () => {
          const useFeet = 0;
          const round = 2;
          const expected = [
            `1/2"`,
            `2 1/2"`,
            `1/4"`,
            `22 1/2"`,
            `100 1/4"`,
            `5/8"`,
            `3 1/8"`,
            `1/8"`,
            `12 1/8"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 8, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round down - 1/16", () => {
          const useFeet = 0;
          const round = 1;
          const expected = [
            `1/2"`,
            `2 1/2"`,
            `1/4"`,
            `22 1/2"`,
            `100 1/4"`,
            `9/16"`,
            `3 1/8"`,
            `1/16"`,
            `12"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 16, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round to nearest - 1/16", () => {
          const useFeet = 0;
          const round = 3;
          const expected = [
            `1/2"`,
            `2 1/2"`,
            `1/4"`,
            `22 1/2"`,
            `100 1/4"`,
            `5/8"`,
            `3 1/8"`,
            `1/16"`,
            `12 1/16"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 16, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });

        it("round up - 1/16", () => {
          const useFeet = 0;
          const round = 2;
          const expected = [
            `1/2"`,
            `2 1/2"`,
            `1/4"`,
            `22 1/2"`,
            `100 1/4"`,
            `5/8"`,
            `3 1/8"`,
            `1/16"`,
            `12 1/16"`,
          ];
          const received = [];

          testCases.forEach((dec) => {
            received.push(conversions.decToArcInch(dec, 16, useFeet, round));
          });

          expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
        });
      });
    });

    describe("convertArray", () => {
      it("should convert array to be an array of objects with display and decimal properties - 1/2 rounding - round down - useFeet", () => {
        const useFeet = 1;
        const testItems = generateTestItems(0.5, 1);

        const expected = [
          {
            length: {
              decimal: 0.5,
              display: '0 1/2"',
            },
            denominator: 0.5,
            round_direction_id: 1,
          },
          {
            length: {
              decimal: 2.5,
              display: '2 1/2"',
            },
            denominator: 0.5,
            round_direction_id: 1,
          },
          {
            length: {
              decimal: 0.25,
              display: '0"',
            },
            denominator: 0.5,
            round_direction_id: 1,
          },
          {
            length: {
              decimal: 22.5,
              display: `1'-10 1/2"`,
            },
            denominator: 0.5,
            round_direction_id: 1,
          },
          {
            length: {
              decimal: 100.25,
              display: `8'-4"`,
            },
            denominator: 0.5,
            round_direction_id: 1,
          },
          {
            length: {
              decimal: 0.6,
              display: `0 1/2"`,
            },
            denominator: 0.5,
            round_direction_id: 1,
          },
          {
            length: {
              decimal: 3.125,
              display: `3"`,
            },
            denominator: 0.5,
            round_direction_id: 1,
          },
          {
            length: {
              decimal: 0.0625,
              display: `0"`,
            },
            denominator: 0.5,
            round_direction_id: 1,
          },
          {
            length: {
              decimal: 12.03125,
              display: `1'-0"`,
            },
            denominator: 0.5,
            round_direction_id: 1,
          },
        ];

        const received = conversions.convertArray(testItems, useFeet);
        expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
      });

      it("should convert array to be an array of objects with display and decimal properties - 1/8 rounding - round up", () => {
        const useFeet = 0;
        const testItems = generateTestItems(0.125, 2);

        const expected = [
          {
            length: {
              decimal: 0.5,
              display: '1/2"',
            },
            denominator: 0.125,
            round_direction_id: 2,
          },
          {
            length: {
              decimal: 2.5,
              display: '2 1/2"',
            },
            denominator: 0.125,
            round_direction_id: 2,
          },
          {
            length: {
              decimal: 0.25,
              display: '1/4"',
            },
            denominator: 0.125,
            round_direction_id: 2,
          },
          {
            length: {
              decimal: 22.5,
              display: `22 1/2"`,
            },
            denominator: 0.125,
            round_direction_id: 2,
          },
          {
            length: {
              decimal: 100.25,
              display: `100 1/4"`,
            },
            denominator: 0.125,
            round_direction_id: 2,
          },
          {
            length: {
              decimal: 0.6,
              display: `5/8"`,
            },
            denominator: 0.125,
            round_direction_id: 2,
          },
          {
            length: {
              decimal: 3.125,
              display: `3 1/8"`,
            },
            denominator: 0.125,
            round_direction_id: 2,
          },
          {
            length: {
              decimal: 0.0625,
              display: `1/8"`,
            },
            denominator: 0.125,
            round_direction_id: 2,
          },
          {
            length: {
              decimal: 12.03125,
              display: `12 1/8"`,
            },
            denominator: 0.125,
            round_direction_id: 2,
          },
        ];

        const received = conversions.convertArray(testItems, useFeet);
        expect(JSON.stringify(expected)).toBe(JSON.stringify(received));
      });
    });
  });
});
