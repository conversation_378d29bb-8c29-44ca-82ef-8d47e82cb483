import { datadogLogs } from "@datadog/browser-logs";
import { datadogRum } from "@datadog/browser-rum";

class Logger {
  constructor() {
    if (Logger.instance) {
      return Logger.instance;
    }

    datadogRum.init({
      applicationId: process.env.REACT_APP_DATADOG_APP_ID,
      clientToken: process.env.REACT_APP_DATADOG_CLIENT_TOKEN,
      site: "datadoghq.com",
      service: "fabweb-react",
      env: process.env.REACT_APP_ENVIRONMENT,
      version: "2.5",
      sampleRate: 100,
      trackInteractions: true,
      defaultPrivacyLevel: "mask-user-input",
      forwardErrorsToLogs: true,
    });

    datadogRum.startSessionReplayRecording();

    this.rum = datadogRum;

    datadogLogs.init({
      clientToken: process.env.REACT_APP_DATADOG_CLIENT_TOKEN,
      site: "datadoghq.com",
      forwardErrorsToLogs: true,
      sampleRate: 100,
      env: process.env.REACT_APP_ENVIRONMENT,
    });

    this.rum = datadogRum;
    this.logs = datadogLogs;

    Logger.instance = this;
  }

  getLogger() {
    return this.logs.logger;
  }

  setUser(user) {
    this.rum.setUser(user);
  }
}

const logger = new Logger();
export const dataDogLogger = logger;
