import { comparator } from "./agTable";

describe("AG-Table helper fns", () => {
  it("comparator", () => {
    const rowNode = {
      data: {
        crew_id: null,
      },
    };
    const values = ["test user", "Austin Jess", "First Last", "Last First"];
    expect(values.sort((a, b) => comparator(a, b, rowNode))).toEqual([
      "Last First",
      "Austin Jess",
      "First Last",
      "test user",
    ]);
  });
});
