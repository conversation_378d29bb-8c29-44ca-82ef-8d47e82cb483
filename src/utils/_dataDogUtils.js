import { datadogLogs } from "@datadog/browser-logs";
import { datadogRum } from "@datadog/browser-rum";

export const logger = datadogLogs.logger;

export const initDatadogRum = () => {
  datadogRum.init({
    applicationId: process.env.DD_APP_ID,
    clientToken: process.env.DD_CLIENT_TOKEN,
    site: "datadoghq.com",
    service: process.env.DD_SERVICE,
    env: process.env.DD_ENV,
    sampleRate: 100,
    premiumSampleRate: 100,
    trackResources: true,
    trackLongTasks: true,
    trackUserInteractions: true,
    defaultPrivacyLevel: "mask-user-input",
    forwardErrorsToLogs: true,
  });

  datadogRum.startSessionReplayRecording();
};

export const initDatadogLogs = () => {
  datadogLogs.init({
    clientToken: process.env.DD_CLIENT_TOKEN,
    site: "datadoghq.com",
    service: process.env.DD_SERVICE,
    env: process.env.DD_ENV,
    sampleRate: 100,
    forwardErrorsToLogs: false,
    telemetrySampleRate: 0,
  });
};

export const setDatadogUser = (id, username, company) => {
  datadogRum.setUser({
    id: id,
    username: username,
    client: company,
  });

  datadogLogs.setUser({
    id: id,
    username: username,
    client: company,
  });
};
