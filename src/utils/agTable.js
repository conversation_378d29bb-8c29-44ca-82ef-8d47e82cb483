import moment from "moment";
import { generateTime, naturalSort } from "../_utils";

// custom sort to sort alphabetically by last name
export const comparator = (valueA, valueB, rowNodeA) => {
  const pattern = new RegExp(/(?:\s).*/);

  if (rowNodeA.data.crew_id) return -1;
  if (!pattern.exec(valueA) || !pattern.exec(valueB)) return -1;
  if (pattern.exec(valueA)[0] === pattern.exec(valueB)[0]) return 0;
  else return pattern.exec(valueA)[0] > pattern.exec(valueB)[0] ? 1 : -1;
};

// all items properties that have objects as their value
const objectValueColumns = [
  "length",
  "bend_deduct",
  "bend_dim_a",
  "bend_dim_b",
  "bend_dim_c",
  "bend_dim_d",
  "bend_dim_e",
  "bend_dim_f",
  "bend_dim_g",
  "bend_mark_1",
  "bend_mark_2",
];

export const columnsToExcludeFromGlobalSearch = [
  "Quantity",
  "status-indicator",
  "checkbox",
  "manage",
  "completedness",
];

// todo - figure out best way to sort size column
// The following methods are used with the Infinite Row Model
const sortData = (sortModel, data) => {
  let sortPresent = sortModel && sortModel.length > 0;
  if (!sortPresent) {
    return data;
  }

  let resultOfSort = data.slice();
  resultOfSort.sort(function (a, b) {
    // currently can only have one column sorted at a time but leaving this in case we change to multiple
    for (let k = 0; k < sortModel.length; k++) {
      let sortColModel = sortModel[k];
      let valueA;
      let valueB;

      if (sortModel?.[0].colId.includes("joint_heat_number")) {
        const position = sortModel?.[0].colId.slice(-1);
        const jointHeatNumbersArrayA = JSON.parse(a[`joint_heat_numbers`]);
        const jointHeatNumbersArrayB = JSON.parse(b[`joint_heat_numbers`]);

        valueA =
          jointHeatNumbersArrayA?.find(
            (hn) => hn.position === parseInt(position)
          )?.heat_number || "";
        valueB =
          jointHeatNumbersArrayB?.find(
            (hn) => hn.position === parseInt(position)
          )?.heat_number || "";
      } else if (sortModel?.[0].colId.includes("work_items")) {
        valueA = JSON.stringify(a["work_item_count"] ?? 0);
        valueB = JSON.stringify(b["work_item_count"] ?? 0);
      } else if (sortModel?.[0].colId.includes("drawings")) {
        valueA = JSON.stringify(a.drawing_count ?? "");
        valueB = JSON.stringify(b.drawing_count ?? "");
      } else if (objectValueColumns.includes(sortModel?.[0].colId)) {
        valueA = a.length?.decimal ? a.length.decimal.toString() : "";
        valueB = b.length?.decimal ? b.length.decimal.toString() : "";
      } else {
        valueA = a[sortColModel.colId] ? a[sortColModel.colId].toString() : "";
        valueB = b[sortColModel.colId] ? b[sortColModel.colId].toString() : "";
      }

      if (valueA === valueB) {
        continue;
      }

      if (sortColModel.sort === "asc") {
        return naturalSort(valueA, valueB);
      } else {
        return naturalSort(valueB, valueA);
      }
    }
    return 0;
  });
  return resultOfSort;
};

const filterData = (filterModel, data) => {
  let filterPresent = filterModel && Object.keys(filterModel).length > 0;
  if (!filterPresent) {
    return data;
  }

  let resultOfFilter = [];
  for (let i = 0; i < data.length; i++) {
    let item = data[i];

    for (const [key, value] of Object.entries(filterModel)) {
      const filterValue = value.filter.toLowerCase();
      const type = value.type;
      let itemValue =
        typeof item[key] === "string" ? item[key].toLowerCase() : item[key];

      if (key.includes("joint_heat_number")) {
        const position = key.slice(-1);
        const jointHeatNumberArray = JSON.parse(item["joint_heat_numbers"]);
        const jointHeatNumberObject = jointHeatNumberArray.find(
          (hn) => hn.position === parseInt(position)
        );

        jointHeatNumberObject
          ? (itemValue = jointHeatNumberObject.heat_number)
          : (itemValue = "");
      }

      if (objectValueColumns.includes(key)) {
        itemValue = item[key].display;
      }

      if (type === "equals") {
        if (itemValue !== filterValue) continue;
      } else if (type === "notEqual") {
        if (itemValue === filterValue) continue;
      } else if (type === "contains") {
        if (!itemValue?.includes(filterValue)) continue;
      } else if (type === "notContains") {
        if (itemValue?.indexOf(filterValue) === 0) continue;
      } else if (type === "startsWith") {
        if (!itemValue?.startsWith(filterValue)) continue;
      } else if (type === "endsWith") {
        if (!itemValue?.endsWith(filterValue)) continue;
      }

      resultOfFilter.push(item);
    }
  }

  return resultOfFilter;
};

export const sortAndFilter = (allOfTheData, sortModel, filterModel) => {
  return sortData(sortModel, filterData(filterModel, allOfTheData));
};

// takes in response from fetchCustomColumnDataPerItem as data and array of custom columns associated with current stage(s)
export const associateCustomColumnData = (data, customColumns) => {
  let result = {};

  for (let colData of data) {
    const columnObj = customColumns.find(
      (col) => col.id === colData.custom_columns_id
    );

    if (columnObj) {
      result[columnObj.normal_name] = colData.data;
    }
  }

  return result;
};

export const searchTable = (searchValue, data, visibleColumns) => {
  if (!visibleColumns || !searchValue) return data;

  let txt = new RegExp(searchValue, "i");
  let value = "";

  const results = data.filter((datum) => {
    const temp = visibleColumns
      .map((col) => {
        const key = col.colId ? col.colId : col.normal_name;

        if (datum && datum[key]) {
          if (objectValueColumns.includes(key)) {
            value = datum[key]?.display;
          } else if (
            key.includes("due_date") ||
            key.includes("target_date") ||
            key.includes("fab_completed_on")
          ) {
            const unixTimeSeconds =
              typeof datum[key] === "number"
                ? datum[key]
                : moment(datum[key]).unix();

            const formattedDate = generateTime(
              unixTimeSeconds * 1000,
              false,
              true,
              "-"
            );

            value = formattedDate;
          } else if (
            key === "percent_complete" ||
            key === "cost_time" ||
            key === "fp_budget" ||
            key === "est_time_left" ||
            key === "hours_ahead_behind"
          ) {
            value = datum[key].toFixed(1);
          } else if (key === "budget_hours") {
            value = datum[key].toFixed(2);
          } else {
            value = datum[key] + "";
          }
          return value;
        } else {
          return "";
        }
      })
      .join(":,:");

    return txt.test(temp);
  });

  return results;
};
