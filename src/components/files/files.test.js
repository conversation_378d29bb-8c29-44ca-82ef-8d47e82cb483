// NPM PACKAGE IMPORTS
import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockAdapter from "axios-mock-adapter";
import thunk from "redux-thunk";

// REDUX IMPORTS
import {
  // ACTION CREATORS
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  // ACTION HANDLERS
  handleFetchDrawingFiles,
} from "./filesActions";

describe("FILES", () => {
  describe("action handlers should perform the necessary functions", () => {
    let store;
    let httpMock;

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    const testError = (message, status) => ({
      error: { status, message },
    });

    it("handleFetchDrawingFiles fetches all PDF files related to drawing", async () => {
      const testDrawingIds = "1,2,3";
      const testDrawingFileResponse = {
        1: {
          original: "test-presignedURL",
        },
        2: {
          original: "test-presignedURL2",
        },
      };
      const testDrawingFileResponseWithAnnotated = {
        1: {
          original: "test-presignedURL",
          annotated: "test-presignedURLA",
        },
        2: {
          original: "test-presignedURL2",
          annotated: "test-presignedURL2A",
        },
      };
      const error = testError(`"ids" are required`, 404);

      httpMock
        .onPost(`${process.env.REACT_APP_API}/drawings/files`, {
          ids: testDrawingIds,
          types: "original,annotated",
        })
        .replyOnce(200, testDrawingFileResponseWithAnnotated)
        .onPost(`${process.env.REACT_APP_API}/drawings/files`, {
          ids: testDrawingIds,
        })
        .replyOnce(200, testDrawingFileResponse)
        .onPost(`${process.env.REACT_APP_API}/drawings/files`, {})
        .replyOnce(404, error);

      await store
        .dispatch(handleFetchDrawingFiles(testDrawingIds, "original,annotated"))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("DRAWING_FILES"),
            receiveSucceeded(
              "DRAWING_FILES",
              testDrawingFileResponseWithAnnotated
            ),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(JSON.stringify(receivedActions[1].payload)).toEqual(
            JSON.stringify(testDrawingFileResponseWithAnnotated)
          );
          store.clearActions();
        });

      await store
        .dispatch(handleFetchDrawingFiles(testDrawingIds, ""))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("DRAWING_FILES"),
            receiveSucceeded("DRAWING_FILES", testDrawingFileResponse),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(JSON.stringify(receivedActions[1].payload)).toEqual(
            JSON.stringify(testDrawingFileResponse)
          );
          store.clearActions();
        });

      return store.dispatch(handleFetchDrawingFiles("", "")).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("DRAWING_FILES"),
          receiveFailed("DRAWING_FILES", error),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });
  });
});
