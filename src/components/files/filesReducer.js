const initialState = {
  drawingFiles: {},
  packageFiles: {},
  isLoading: false,
  error: null,
};
export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_DRAWING_FILES_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_DRAWING_FILES_SUCCEEDED":
      // iterate through and update or append the properties...
      let keys = Object.keys(payload);
      keys.forEach((key) => {
        if (state.drawingFiles.hasOwnProperty(key)) {
          payload[key] = { ...state.drawingFiles[key], ...payload[key] };
        }
      });
      let updatedDrawingFiles = { ...state.drawingFiles, ...payload };
      return {
        ...state,
        isLoading: false,
        error: null,
        drawingFiles: updatedDrawingFiles,
      };
    case "RECEIVE_DRAWING_FILES_FAILED":
      return { ...state, isLoading: false, error: payload };

    case "RECEIVE_PACKAGES_ID_STARTED":
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case "RECEIVE_PACKAGES_ID_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        packageFiles: { ...state.packageFiles, ...payload },
      };
    case "RECEIVE_PACKAGES_ID_FAILED":
      return { ...state, isLoading: false, error: payload };
    default:
      return state;
  }
}
