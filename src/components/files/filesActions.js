import { fetchDrawingFiles, fetchPackagesFiles } from "../../_services";

export const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const handleFetchDrawingFiles = (drawingIds, fileTypes) => (
  dispatch
) => {
  const type = "DRAWING_FILES";
  dispatch(receiveStarted(type));
  return fetchDrawingFiles(drawingIds, fileTypes).then((res) => {
    if (res.error) {
      dispatch(receiveFailed(type, res));
      return res;
    }
    dispatch(receiveSucceeded(type, res));
    return res;
  });
};

export const handleFetchPackageFile = (packageId) => (dispatch) => {
  const type = "PACKAGES_ID";
  dispatch(receiveStarted(type));
  let result = {};
  // ignoring errors for when a file does not exist...
  return fetchPackagesFiles(packageId).then((res) => {
    // convert the single return url to a map object for storage...
    if (!res.error) result[packageId] = res;
    else result[packageId] = null;
    dispatch(receiveSucceeded(type, result));
    return result;
  });
};
