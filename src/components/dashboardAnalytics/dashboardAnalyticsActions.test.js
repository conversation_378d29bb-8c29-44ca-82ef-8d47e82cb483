import configureMockStore from "redux-mock-store";
import thunk from "redux-thunk";
import {
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  handleFetchDashboardFilters,
  handleFetchDashboardJobSummary,
} from "./dashboardAnalyticsActions";
import {
  fetchDashboardFiltersList,
  fetchDashboardJobSummary,
} from "../../_services";

jest.mock("../../_services"); // Mock the API calls

const middlewares = [thunk];
const mockStore = configureMockStore(middlewares);

describe("Dashboard Actions", () => {
  it("should create an action to set RECEIVE_STARTED", () => {
    const expectedAction = { type: "RECEIVE_DASHBOARD_FILTER_STARTED" };
    expect(receiveStarted("DASHBOARD_FILTER")).toEqual(expectedAction);
  });

  it("should create an action to set RECEIVE_SUCCEEDED", () => {
    const payload = { id: 1, name: "Filter 1" };
    const expectedAction = {
      type: "RECEIVE_DASHBOARD_FILTER_SUCCEEDED",
      payload,
    };
    expect(receiveSucceeded("DASHBOARD_FILTER", payload)).toEqual(
      expectedAction
    );
  });

  it("should create an action to set RECEIVE_FAILED", () => {
    const error = "Error fetching data";
    const expectedAction = {
      type: "RECEIVE_DASHBOARD_FILTER_FAILED",
      payload: error,
    };
    expect(receiveFailed("DASHBOARD_FILTER", error)).toEqual(expectedAction);
  });

  it("should dispatch correct actions when fetching dashboard filters succeeds", async () => {
    const store = mockStore({});
    const mockResponse = [{ id: 1, name: "Filter 1" }];
    fetchDashboardFiltersList.mockResolvedValue(mockResponse);

    await store.dispatch(handleFetchDashboardFilters());

    const actions = store.getActions();
    expect(actions[0]).toEqual(receiveStarted("DASHBOARD_FILTER"));
    expect(actions[1]).toEqual(
      receiveSucceeded("DASHBOARD_FILTER", mockResponse)
    );
  });

  it("should dispatch correct actions when fetching dashboard filters fails", async () => {
    const store = mockStore({});
    const mockError = { error: "Network Error" };
    fetchDashboardFiltersList.mockResolvedValue(mockError);

    await store.dispatch(handleFetchDashboardFilters());

    const actions = store.getActions();
    expect(actions[0]).toEqual(receiveStarted("DASHBOARD_FILTER"));
    expect(actions[1]).toEqual(receiveFailed("DASHBOARD_FILTER", mockError));
  });

  it("should dispatch correct actions when fetching job summary succeeds", async () => {
    const store = mockStore({});
    const mockResponse = [{ jobId: 101, title: "Job 101" }];
    fetchDashboardJobSummary.mockResolvedValue(mockResponse);

    await store.dispatch(handleFetchDashboardJobSummary());

    const actions = store.getActions();
    expect(actions[0]).toEqual(receiveStarted("DASHBOARD_JOB_SUMMARY"));
    expect(actions[1]).toEqual(
      receiveSucceeded("DASHBOARD_JOB_SUMMARY", mockResponse)
    );
  });

  it("should dispatch correct actions when fetching job summary fails", async () => {
    const store = mockStore({});
    const mockError = { error: "Server Error" };
    fetchDashboardJobSummary.mockResolvedValue(mockError);

    await store.dispatch(handleFetchDashboardJobSummary());

    const actions = store.getActions();
    expect(actions[0]).toEqual(receiveStarted("DASHBOARD_JOB_SUMMARY"));
    expect(actions[1]).toEqual(
      receiveFailed("DASHBOARD_JOB_SUMMARY", mockError)
    );
  });
});
