import { objectColumnDefs } from "../../_utils";

// @ToDo: Remove once we implement the ability to pass defaultColDef to AgTable (impacts 30+ grids so skipping for this ticket)
export const COLUMN_DEFAULTS = {
  // cellClass: ["no-border", "custom-wrap"], // do we need this? No visible difference
  resizable: true,
  wrapText: true,
  // suppressSizeToFit: true, // makes the columns wider than they need to be...
  // wrapText: true, // cannot add bc of the package_ids and drawing_ids fields
  // autoHeight: true, // cannot add bc of the package_ids and drawing_ids fields
};

export const GRID_DEFAULTS = {
  // imported from AgTable - enableValue (true), sortable (true), filter (true), enablePivot (false)
  suppressCellClickSelection: true,
  suppressRowClickSelection: true,
  enableRangeSelection: true,
  // @ToDo: Cannot implement these because AgTable does not support taking in defaultColDef options - need to put these in EACH of the cells for now...
  // defaultColDef: { ...COLUMN_DEFAULTS },
  // autoSizeStrategy: {
  //   type: "fitGridWidth",
  // }, // @ToDo: Not available in current version, must use the grid api...
  pagination: true,
  paginationPageSize: 25,
  // paginationPageSizeSelector: [5, 25, 50, 100], // @ToDo: not available yet...
};

// used for columns that traditionally have large data that we want to make auto height
const COLUMN_LARGE_TEXT_OPTIONS = {
  autoHeight: true,
};

// to compare the list of ids only by the first id...
const firstIdComparator = (valueA, valueB, isDescending) => {
  const getFirstId = (val) => {
    if (!val || typeof val !== "string") return 0;
    const first = val.split(",")[0].trim();
    return isNaN(first) ? 0 : parseInt(first);
  };
  if (getFirstId(valueA) == getFirstId(valueB)) return 0;
  return getFirstId(valueA) > getFirstId(valueB) ? 1 : -1;
};

const formatRoundedNumber = (params) => {
  return (
    params?.value?.toLocaleString("en-US", { maximumFractionDigits: 2 }) ?? "-"
  );
};

export const getColumns = (type) => {
  switch (type) {
    case "metric":
      // @ToDo: Remove COLUMN_DEFAULTS when AgTable allows defaultColDef
      return METRIC_OVERVIEW_COLUMN_DEFS.map((col) => ({
        ...col,
        ...COLUMN_DEFAULTS,
      }));
    case "material":
      // @ToDo: Remove COLUMN_DEFAULTS when AgTable allows defaultColDef
      return MATERIAL_OVERVIEW_COLUMN_DEFS.map((col) => ({
        ...col,
        ...COLUMN_DEFAULTS,
      }));
    default:
      return [];
  }
};

// note tooltips won't work until ag-grid v28 or newer...
const METRIC_OVERVIEW_COLUMN_DEFS = [
  { field: "id", hide: true },
  {
    field: "work_flow_name",
    tooltipField: "work_flow_name",
    headerName: "Work Flow Name",
    minWidth: 150,
    ...COLUMN_LARGE_TEXT_OPTIONS,
  },
  {
    field: "stage_name",
    tooltipField: "stage_name",
    headerName: "Stage Name",
    minWidth: 150,
    ...COLUMN_LARGE_TEXT_OPTIONS,
  },
  {
    field: "prod_metric_per_hr",
    tooltipField: "prod_metric_per_hr",
    headerName: "Productivity (metric/h)",
    headerTooltip: "Productivity per hour for the stage metric",
    filter: "agNumberColumnFilter",
    valueFormatter: formatRoundedNumber,
    minWidth: 170,
  },
  {
    field: "total_time_hrs",
    tooltipField: "total_time_hrs",
    headerName: "Total Time (h)",
    filter: "agNumberColumnFilter",
    valueFormatter: formatRoundedNumber,
    minWidth: 120,
  },
  {
    field: "metric_amount",
    tooltipField: "metric_amount",
    headerName: "Metric Amount",
    filter: "agNumberColumnFilter",
    valueFormatter: formatRoundedNumber,
    minWidth: 130,
  },
  {
    field: "stage_metric_name",
    tooltipField: "stage_metric_name",
    headerName: "Stage Metric",
    minWidth: 170,
    ...COLUMN_LARGE_TEXT_OPTIONS,
  },
];

// note tooltips won't work until ag-grid v28 or newer...
const MATERIAL_OVERVIEW_COLUMN_DEFS = [
  { field: "id", hide: true },
  {
    field: "material_name",
    tooltipField: "material_name",
    headerName: "Material",
    minWidth: 180,
    ...COLUMN_LARGE_TEXT_OPTIONS,
  },
  {
    field: "size",
    tooltipField: "size",
    headerName: "Size",
    minWidth: 100,
  },
  {
    field: "item_length",
    tooltipField: "item_length",
    headerName: "Length(ft)",
    filter: "agNumberColumnFilter",
    minWidth: 120,
    valueFormatter: objectColumnDefs.valueFormatter("item_length"),
    comparator: objectColumnDefs.comparator("item_length"),
  },
  {
    field: "item_weight",
    tooltipField: "item_weight",
    headerName: "Weight(lbs)",
    filter: "agNumberColumnFilter",
    valueFormatter: formatRoundedNumber,
    minWidth: 120,
  },
  {
    field: "job_ids",
    tooltipField: "job_ids",
    headerName: "Job",
    headerTooltip: "List of job ids with material",
    minWidth: 140,
    hide: true,
    comparator: firstIdComparator,
  },
  {
    field: "package_ids",
    tooltipField: "package_ids",
    headerName: "Package",
    headerTooltip: "List of package ids with material",
    minWidth: 140,
    comparator: firstIdComparator,
  },
  {
    field: "drawing_ids",
    tooltipField: "drawing_ids",
    headerName: "Drawing",
    headerTooltip: "List of drawing ids with material",
    minWidth: 140,
    comparator: firstIdComparator,
  },
];
