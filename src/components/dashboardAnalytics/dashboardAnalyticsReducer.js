const initialState = {
  isLoading: false,
  error: null,
  filterData: [],
  jobSummary: {
    isLoading: false,
    error: null,
    data: [],
  },
  hoursOverview: {
    isLoading: false,
    error: null,
    data: [],
  },
  workflowStageSummary: {
    isLoading: false,
    error: null,
    data: {},
  },
  metricOverview: {
    isLoading: false,
    error: null,
    data: {},
  },
  materialOverview: {
    isLoading: false,
    error: null,
    data: {},
  },
  filterValidationToken: {
    isLoading: false,
    error: null,
    data: {},
  },
  hoursTrends: {
    isLoading: false,
    error: null,
    data: [],
  },
  packageTrends: {
    isLoading: false,
    error: null,
    data: [],
  },
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_DASHBOARD_FILTER_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_DASHBOARD_FILTER_SUCCEEDED":
      return { ...state, isLoading: false, error: null, filterData: payload };
    case "RECEIVE_DASHBOARD_FILTER_FAILED":
      return { ...state, isLoading: false, filterData: [], error: payload };
    case "RECEIVE_DASHBOARD_JOB_SUMMARY_STARTED":
      return {
        ...state,
        jobSummary: { ...initialState.jobSummary, isLoading: true },
      };
    case "RECEIVE_DASHBOARD_JOB_SUMMARY_SUCCEEDED":
      return {
        ...state,
        jobSummary: { ...state.jobSummary, isLoading: false, data: payload },
      };
    case "RECEIVE_DASHBOARD_JOB_SUMMARY_FAILED":
      return {
        ...state,
        jobSummary: { ...state.jobSummary, isLoading: false, error: payload },
      };
    case "RECEIVE_DASHBOARD_HOURS_OVERVIEW_STARTED":
      return {
        ...state,
        hoursOverview: { ...initialState.hoursOverview, isLoading: true },
      };
    case "RECEIVE_DASHBOARD_HOURS_OVERVIEW_SUCCEEDED":
      return {
        ...state,
        hoursOverview: {
          ...state.hoursOverview,
          isLoading: false,
          data: payload,
        },
      };
    case "RECEIVE_DASHBOARD_HOURS_OVERVIEW_FAILED":
      return {
        ...state,
        hoursOverview: {
          ...state.hoursOverview,
          isLoading: false,
          error: payload,
        },
      };
    case "RECEIVE_DASHBOARD_WORKFLOW_STAGE_SUMMARY_STARTED":
      return {
        ...state,
        workflowStageSummary: {
          ...initialState.workflowStageSummary,
          isLoading: true,
        },
      };
    case "RECEIVE_DASHBOARD_WORKFLOW_STAGE_SUMMARY_SUCCEEDED":
      return {
        ...state,
        workflowStageSummary: {
          ...state.workflowStageSummary,
          isLoading: false,
          data: payload,
        },
      };
    case "RECEIVE_DASHBOARD_WORKFLOW_STAGE_SUMMARY_FAILED":
      return {
        ...state,
        workflowStageSummary: {
          ...state.workflowStageSummary,
          isLoading: false,
          error: payload,
        },
      };
    case "RECEIVE_DASHBOARD_JOB_METRIC_OVERVIEW_STARTED":
      return {
        ...state,
        metricOverview: { ...initialState.metricOverview, isLoading: true },
      };
    case "RECEIVE_DASHBOARD_JOB_METRIC_OVERVIEW_SUCCEEDED":
      return {
        ...state,
        metricOverview: {
          ...state.metricOverview,
          isLoading: false,
          data: payload,
        },
      };
    case "RECEIVE_DASHBOARD_JOB_METRIC_OVERVIEW_FAILED":
      return {
        ...state,
        metricOverview: {
          ...state.metricOverview,
          isLoading: false,
          error: payload,
        },
      };
    case "RECEIVE_DASHBOARD_JOB_MATERIAL_OVERVIEW_STARTED":
      return {
        ...state,
        materialOverview: { ...initialState.materialOverview, isLoading: true },
      };
    case "RECEIVE_DASHBOARD_JOB_MATERIAL_OVERVIEW_SUCCEEDED":
      return {
        ...state,
        materialOverview: {
          ...state.materialOverview,
          isLoading: false,
          data: payload,
        },
      };
    case "RECEIVE_DASHBOARD_JOB_MATERIAL_OVERVIEW_FAILED":
      return {
        ...state,
        materialOverview: {
          ...state.materialOverview,
          isLoading: false,
          error: payload,
        },
      };
    case "RECEIVE_DASHBOARD_GENERATE_FILTER_VALIDATION_TOKEN_STARTED":
      return {
        ...state,
        filterValidationToken: {
          ...initialState.filterValidationToken,
          isLoading: true,
        },
        jobSummary: { ...initialState.jobSummary, isLoading: true },
        hoursOverview: { ...initialState.hoursOverview, isLoading: true },
        workflowStageSummary: {
          ...initialState.workflowStageSummary,
          isLoading: true,
        },
        metricOverview: { ...initialState.metricOverview, isLoading: true },
        materialOverview: { ...initialState.materialOverview, isLoading: true },
      };
    case "RECEIVE_DASHBOARD_GENERATE_FILTER_VALIDATION_TOKEN_SUCCEEDED":
      return {
        ...state,
        filterValidationToken: {
          ...state.filterValidationToken,
          isLoading: false,
          data: payload,
        },
      };
    case "RECEIVE_DASHBOARD_GENERATE_FILTER_VALIDATION_TOKEN_FAILED":
      return {
        ...state,
        filterValidationToken: {
          ...state.filterValidationToken,
          isLoading: false,
          error: payload,
        },
        jobSummary: { ...initialState.jobSummary, error: payload },
        hoursOverview: { ...initialState.hoursOverview, error: payload },
        workflowStageSummary: {
          ...initialState.workflowStageSummary,
          error: payload,
        },
        metricOverview: { ...initialState.metricOverview, error: payload },
        materialOverview: { ...initialState.materialOverview, error: payload },
      };
    case "RECEIVE_DASHBOARD_HOURS_TRENDS_STARTED":
      return {
        ...state,
        hoursTrends: { ...initialState.hoursTrends, isLoading: true },
      };
    case "RECEIVE_DASHBOARD_HOURS_TRENDS_SUCCEEDED":
      return {
        ...state,
        hoursTrends: {
          ...state.hoursTrends,
          isLoading: false,
          data: payload,
        },
      };
    case "RECEIVE_DASHBOARD_HOURS_TRENDS_FAILED":
      return {
        ...state,
        hoursTrends: {
          ...state.hoursTrends,
          isLoading: false,
          error: payload,
        },
      };
    case "RECEIVE_DASHBOARD_PACKAGE_TRENDS_BY_MONTH_STARTED":
      return {
        ...state,
        packageTrends: { ...initialState.packageTrends, isLoading: true },
      };
    case "RECEIVE_DASHBOARD_PACKAGE_TRENDS_BY_MONTH_SUCCEEDED":
      return {
        ...state,
        packageTrends: {
          ...state.packageTrends,
          isLoading: false,
          data: payload,
        },
      };
    case "RECEIVE_DASHBOARD_PACKAGE_TRENDS_BY_MONTH_FAILED":
      return {
        ...state,
        packageTrends: {
          ...state.packageTrends,
          isLoading: false,
          error: payload,
        },
      };
    default:
      return state;
  }
}
