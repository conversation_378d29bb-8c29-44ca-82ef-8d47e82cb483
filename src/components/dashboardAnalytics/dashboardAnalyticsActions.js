import {
  fetchDashboardFiltersList,
  fetchDashboardJobMetricOverview,
  fetchDashboardJobSummary,
  fetchDashboardHourOverview,
  fetchDashboardWorkFlowStageSummary,
  fetchDashboardJobMaterialOverview,
  fetchDashboardFiltersValidationToken,
  fetchDashboardTrendsHours,
  fetchDashboardPackageTrendsByMonth,
} from "../../_services";

export const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const handleFetchDashboardFilters = () => (dispatch) => {
  const type = "DASHBOARD_FILTER";
  dispatch(receiveStarted(type));
  return fetchDashboardFiltersList().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));
    return res;
  });
};

export const handleFetchDashboardJobSummary = (filter) => (dispatch) => {
  const type = "DASHBOARD_JOB_SUMMARY";
  dispatch(receiveStarted(type));
  return fetchDashboardJobSummary(filter).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));
    return res;
  });
};

export const handleFetchDashboardHoursOverView = (payload) => (dispatch) => {
  const type = "DASHBOARD_HOURS_OVERVIEW";
  dispatch(receiveStarted(type));
  return fetchDashboardHourOverview(payload).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));
    return res;
  });
};

export const handleFetchDashboardWorkflowStageSummary = (filter) => (
  dispatch
) => {
  const type = "DASHBOARD_WORKFLOW_STAGE_SUMMARY";
  dispatch(receiveStarted(type));
  return fetchDashboardWorkFlowStageSummary(filter).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));
    return res;
  });
};

export const handleFetchDashboardJobMetricOverview = (filter) => (dispatch) => {
  const type = "DASHBOARD_JOB_METRIC_OVERVIEW";
  dispatch(receiveStarted(type));
  return fetchDashboardJobMetricOverview(filter).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));
    return res;
  });
};

export const handleFetchDashboardJobMaterialOverview = (filter) => (
  dispatch
) => {
  const type = "DASHBOARD_JOB_MATERIAL_OVERVIEW";
  dispatch(receiveStarted(type));
  return fetchDashboardJobMaterialOverview(filter).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));
    return res;
  });
};

export const handleFetchDashboardFiltersValidationToken = (filter) => (
  dispatch
) => {
  const type = "DASHBOARD_GENERATE_FILTER_VALIDATION_TOKEN";
  dispatch(receiveStarted(type));
  return fetchDashboardFiltersValidationToken(filter).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));
    return res;
  });
};

export const handleFetchDashboardTrendsHours = () => (dispatch, getState) => {
  const type = "DASHBOARD_HOURS_TRENDS";

  const { hoursTrends } = getState().dashboardAnalytics;
  if (hoursTrends && hoursTrends.data?.data?.length > 0) {
    // Cache hit, skip API call
    return Promise.resolve({ cached: true, ...hoursTrends });
  }

  dispatch(receiveStarted(type));

  return fetchDashboardTrendsHours().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));
    return res;
  });
};

export const handleFetchDashboardPackageTrendsByMonth = () => (
  dispatch,
  getState
) => {
  const type = "DASHBOARD_PACKAGE_TRENDS_BY_MONTH";
  const { packageTrends } = getState().dashboardAnalytics;

  if (packageTrends && packageTrends.data?.data?.length > 0) {
    // Cache hit, skip API call
    return Promise.resolve({ cached: true, ...packageTrends });
  }

  dispatch(receiveStarted(type));
  return fetchDashboardPackageTrendsByMonth().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));
    return res;
  });
};
