import React from "react";
import { <PERSON><PERSON><PERSON> } from "@mui/x-charts/PieChart";
import { useDrawingArea } from "@mui/x-charts/hooks";
import { styled, useTheme } from "@mui/material/styles";
import withLoader from "../../../reusable/hoc/withLoader";
import withError from "../../../reusable/hoc/withError";

const UNALLOCATED_KEY = "unallocated_hours";
const ALLOCATED_KEY = "allocated_hours";
const TOTAL_HOURS_KEY = "total_hours";
const UNALLOCATED_LABEL = "Unallocated Time";
const ALLOCATED_LABEL = "Allocated Time";
const LABEL_MAP = [
  { key: UNALLOCATED_KEY, label: UNALLOCATED_LABEL },
  { key: ALLOCATED_KEY, label: ALLOCATED_LABEL },
];

const StyledText = styled("text")(({ theme }) => ({
  fill: theme.palette.text.primary,
  textAnchor: "middle",
  dominantBaseline: "central",
  fontSize: 20,
}));

const PieCenterLabel = ({ children }) => {
  const { width, height, left, top } = useDrawingArea();
  return (
    <StyledText x={left + width / 2} y={top + height / 2}>
      {children}
    </StyledText>
  );
};

const HoursOverviewLoad = ({ dataOverview }) => {
  const theme = useTheme();

  const getFormattedLabel = (item, value) => {
    const formattedValue = `${value} hrs (${getDatawithPercentage(value)}%)`;
    const formatted_item =
      item.charAt(0).toUpperCase() +
      item.slice(1).toLowerCase().replace("_", " ");
    const label =
      LABEL_MAP.find((label) => label.key === item)?.label ?? formatted_item;
    return `${label} - ${formattedValue}`;
  };

  const getDatawithPercentage = (item) => {
    const total = dataOverview[TOTAL_HOURS_KEY] || 1; // prevent division by 0
    return ((item / total) * 100).toFixed(0);
  };

  const hasNoData =
    !dataOverview?.[UNALLOCATED_KEY] && !dataOverview?.[ALLOCATED_KEY];

  const getDataOverview = () => {
    if (hasNoData) {
      return [
        {
          id: 0,
          value: 1,
          label: "No Data Available",
          color: theme.palette.grey[400],
        },
      ];
    }

    return Object.keys(dataOverview).reduce((acc, item, i) => {
      if (item === UNALLOCATED_KEY || item === ALLOCATED_KEY) {
        const value = dataOverview[item];
        const keyTitle = getFormattedLabel(item, value);
        const color =
          item === UNALLOCATED_KEY
            ? theme.palette.text.donutMain
            : theme.palette.text.donutLight;
        acc.push({ id: i, value, label: keyTitle, color });
      }
      return acc;
    }, []);
  };

  const centerLabel = hasNoData
    ? "0 Hours"
    : `${dataOverview?.[TOTAL_HOURS_KEY] ?? 0} Hours`;

  return (
    <PieChart
      series={[
        {
          type: "pie",
          data: getDataOverview(),
          innerRadius: 65,
          paddingAngle: 5,
          outerRadius: 80,
          cornerRadius: 110,
        },
      ]}
      slotProps={{
        legend: {
          direction: "row",
          position: { horizontal: "middle", vertical: "bottom" },
          itemMarkWidth: 16,
          itemGap: 10,
          labelStyle: {
            opacity: hasNoData ? 0.3 : 1,
          },
          markStyle: {
            opacity: hasNoData ? 0.3 : 1,
          },
        },
      }}
      margin={{ left: 20, right: 20, top: 20, bottom: 80 }}
      height={300}
      sx={{
        "& .MuiPieArc-root": {
          strokeWidth: 0,
        },
      }}
    >
      <PieCenterLabel>{centerLabel}</PieCenterLabel>
    </PieChart>
  );
};

export default withLoader(withError(HoursOverviewLoad));
