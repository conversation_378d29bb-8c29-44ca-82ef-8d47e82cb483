import React from "react";
import { useSelector } from "react-redux";
import HoursOverviewLoad from "./HoursOverviewLoad";
import WidgetCardWrapper from "../widgetCardWrapper";

const HoursOverview = () => {
  const hoursOverview = useSelector(
    (state) => state.dashboardAnalytics.hoursOverview
  );
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState(null);
  const [resData, setResData] = React.useState({});

  React.useEffect(() => {
    // set the data before we remove the loading screen
    if (
      hoursOverview?.data?.hasOwnProperty("data") &&
      hoursOverview?.data?.data?.length > 0
    ) {
      setResData(hoursOverview?.data?.data[0]);
    }
    setError(hoursOverview?.error);
    setIsLoading(hoursOverview?.isLoading);
  }, [hoursOverview]);

  return (
    <WidgetCardWrapper
      dashboardName="Job Summary"
      title="Hours Overview"
      widgetData={[resData]}
      isLoading={isLoading}
      error={error}
      // showExport
      showExpand
    >
      <HoursOverviewLoad
        isLoading={isLoading}
        error={error}
        dataOverview={resData}
      />
    </WidgetCardWrapper>
  );
};

export default HoursOverview;
