import { Box, IconButton, Tooltip } from "@mui/material";
import { OpenInFull } from "@mui/icons-material";
import ExportDataMenu from "../../reusable/exportDataMenu/exportDataMenu";

// default to show export since it's the only button, parent implementations can override
const WidgetMenu = ({
  title,
  data,
  disabled = false,
  showExport = true,
  showExpand = false,
  handleExpand,
}) => {
  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        alignItems: "center",
        justifyContent: "flex-end",
      }}
    >
      {/* handles its own disabled if no data... */}
      {showExport && (
        <ExportDataMenu title={title} data={data} disabled={disabled} />
      )}
      {showExpand && (
        <Tooltip title={`Expand ${title}`}>
          <IconButton
            onClick={handleExpand}
            sx={{ ml: 2 }}
            disabled={!handleExpand || disabled}
            aria-haspopup="true"
            size="small"
          >
            <OpenInFull fontSize="small" />
          </IconButton>
        </Tooltip>
      )}
    </Box>
  );
};

export default WidgetMenu;
