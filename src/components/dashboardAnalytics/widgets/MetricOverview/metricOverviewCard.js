import React from "react";
import { useSelector } from "react-redux";
import GenericWidgetGrid from "./../genericWidgetGrid";
import WidgetCardWrapper from "../widgetCardWrapper";

const MetricOverviewCard = () => {
  const metricOverview = useSelector(
    (state) => state.dashboardAnalytics.metricOverview
  );
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState(null);
  const [gridData, setGridData] = React.useState([]);

  React.useEffect(() => {
    // set the data before we remove the loading screen
    setGridData(
      metricOverview?.data?.data?.map((r, index) => ({ ...r, id: index })) ?? []
    );
    setError(metricOverview?.error);
    setIsLoading(metricOverview?.isLoading);
  }, [metricOverview]);

  return (
    <WidgetCardWrapper
      dashboardName="Job Summary"
      title="Metric Overview"
      widgetData={gridData}
      isLoading={isLoading}
      error={error}
      showExport
      showExpand
    >
      <GenericWidgetGrid
        isLoading={isLoading}
        error={error}
        data={gridData}
        type="metric"
        id="job-summary-metric-overview"
      />
    </WidgetCardWrapper>
  );
};

export default MetricOverviewCard;
