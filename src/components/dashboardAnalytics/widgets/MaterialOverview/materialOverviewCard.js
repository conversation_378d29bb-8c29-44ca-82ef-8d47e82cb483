import React from "react";
import { useSelector } from "react-redux";
import { Grid2 as Grid, Typography } from "@mui/material";
import GenericWidgetGrid from "./../genericWidgetGrid";
import WidgetCardWrapper from "../widgetCardWrapper";
import conversions from "../../../../utils/_conversions";

const MaterialOverviewCard = () => {
  const materialOverview = useSelector(
    (state) => state.dashboardAnalytics.materialOverview
  );
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState(null);
  const [gridData, setGridData] = React.useState([]);
  const [totalLength, setTotalLength] = React.useState(0);
  const [totalWeight, setTotalWeight] = React.useState(0);

  React.useEffect(() => {
    let sumWeight = 0;
    let sumLength = 0;
    // set the data before we remove the loading screen
    const parsedData =
      materialOverview?.data?.data?.map((r, index) => {
        sumLength += r.item_length;
        sumWeight += r.item_weight;
        return {
          ...r,
          id: index,
          item_length: {
            display: conversions.decToArcInch(r.item_length, 8, true, 2),
            decimal: r.item_length,
          },
        };
      }) ?? [];

    // format totals
    const totalLength = conversions.decToArcInch(sumLength, 8, true, 2) ?? "-";
    const totalWeight =
      sumWeight.toLocaleString("en-US", { maximumFractionDigits: 2 }) ?? "-";

    // set state
    setGridData(parsedData);
    setTotalLength(totalLength);
    setTotalWeight(totalWeight);
    setError(materialOverview?.error);
    setIsLoading(materialOverview?.isLoading);
  }, [materialOverview]);

  const headerContent = () => {
    return (
      <Grid container spacing={2} alignItems="center" justifyContent="flex-end">
        <Grid item sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography variant="subtitle1">Total Length (ft)</Typography>
          <Typography
            variant="h6"
            sx={{ border: "1px solid black", padding: "2px 8px" }}
          >
            {totalLength}
          </Typography>
        </Grid>
        <Grid item sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography variant="subtitle1">Total Weight (lbs)</Typography>
          <Typography
            variant="h6"
            sx={{ border: "1px solid black", padding: "2px 8px" }}
          >
            {totalWeight}
          </Typography>
        </Grid>
      </Grid>
    );
  };

  return (
    <WidgetCardWrapper
      dashboardName="Job Summary"
      title="Material Overview"
      headerContent={headerContent()}
      widgetData={gridData}
      isLoading={isLoading}
      error={error}
      showExport
      showExpand
    >
      <GenericWidgetGrid
        isLoading={isLoading}
        error={error}
        data={gridData}
        type="material"
        id="job-summary-material-overview"
      />
    </WidgetCardWrapper>
  );
};

export default MaterialOverviewCard;
