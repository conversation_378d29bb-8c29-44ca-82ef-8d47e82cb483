import { useSelector } from "react-redux";

import WidgetCardWrapper from "../widgetCardWrapper";
import FabHoursTrends from "./FabHoursTrends";

const HoursTrends = () => {
  const { hoursTrends } = useSelector((state) => state.dashboardAnalytics);

  return (
    <WidgetCardWrapper
      dashboardName="Trends"
      title="Fabrication Hours By Month"
      widgetData={hoursTrends.data?.data || []}
      isLoading={hoursTrends.isLoading}
      error={hoursTrends.error}
      showExpand
    >
      <FabHoursTrends
        isLoading={hoursTrends.isLoading}
        error={hoursTrends.error}
        data={hoursTrends.data?.data || []}
      />
    </WidgetCardWrapper>
  );
};

export default HoursTrends;
