import { LineChart } from "@mui/x-charts/LineChart";
import { useState } from "react";
import { styled, useTheme } from "@mui/material/styles";
import withLoader from "../../../reusable/hoc/withLoader";
import withError from "../../../reusable/hoc/withError";

const FabHoursTrends = ({ data }) => {
  const theme = useTheme();
  const isEmptyDataset = (data = []) =>
    data.length === 0 ||
    data.every(
      (row) =>
        (row.allocated_time_hrs ?? 0) === 0 &&
        (row.unallocated_time_hrs ?? 0) === 0
    );

  const generateBlankDataset = (monthsCount = 6) => {
    const today = new Date();
    const dataset = [];

    for (let i = monthsCount - 1; i >= 0; i--) {
      const d = new Date(today.getFullYear(), today.getMonth() - i, 1);
      const monthStr = d.toLocaleString("en-US", {
        month: "short",
        year: "numeric",
      });
      dataset.push({
        month: monthStr,
        allocated_time_hrs: 0,
        unallocated_time_hrs: 0,
      });
    }

    return dataset;
  };

  const isBlank = isEmptyDataset(data);

  const chartData = isBlank
    ? generateBlankDataset(12)
    : data.map((d) => ({
        ...d,
        month: new Date(d.month)?.toLocaleString("en-US", {
          month: "short",
          year: "numeric",
        }),
      }));

  const [chartOptions, setChartOptions] = useState({
    dataset: chartData,
    series: isBlank
      ? [
          {
            dataKey: "allocated_time_hrs",
            label: "No Data",
            color: theme.palette.grey[400],
            showMark: true,
          },
        ]
      : [
          {
            dataKey: "allocated_time_hrs",
            label: "Allocated Hours",
            color: theme.palette.legends.color3,
            showMark: true,
          },
          {
            dataKey: "unallocated_time_hrs",
            label: "Unallocated Hours",
            color: theme.palette.legends.color2,
            showMark: true,
          },
          {
            dataKey: "total_time_hrs",
            label: "Total Hours",
            color: theme.palette.legends.color4,
            showMark: true,
          },
        ],
    xAxis: [
      {
        dataKey: "month",
        scaleType: "band",
        tickLabelStyle: { angle: -45, textAnchor: "end" },
      },
    ],
    yAxis: [
      {
        label: "Hours",
        width: 80,
        tickLabelStyle: {
          fill: theme.palette.text.primary,
        },
      },
    ],
    height: 350,
    margin: { top: 70, right: 30, bottom: 60, left: 90 },
    slotProps: {
      legend: {
        position: { vertical: "top", horizontal: "center" },
        direction: "row",
        itemMarkHeight: 12,
        itemMarkWidth: 12,
        labelStyle: {
          fill: theme.palette.text.primary,
        },
        padding: 5,
      },
    },
  });

  return <LineChart {...chartOptions} />;
};

export default withLoader(withError(FabHoursTrends));
