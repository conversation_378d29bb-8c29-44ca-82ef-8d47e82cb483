import { Fragment, useState } from "react";
import {
  Card,
  CardContent,
  Box,
  Grid2 as Grid,
  Typography,
} from "@mui/material";
import WidgetMenu from "./widgetMenu";
import GenericModal from "./../../reusable/muiModal/genericModal";

function WidgetCardWrapper({
  dashboardName,
  title,
  headerContent,
  children,
  widgetData,
  showExport = false,
  showExpand = false,
  setIsExpanded = (val) => {
    console.debug(`Widget is being ${val ? "expanded" : "coallapsed"}`);
  }, // do nothing by default...
  error = null,
  isLoading = false,
}) {
  const [expanded, setExpanded] = useState(false);
  const id = `${dashboardName}-${title}`;

  const handleExpand = () => {
    setExpanded(true);
    setIsExpanded(true);
  };
  const handleClose = () => {
    setExpanded(false);
    setIsExpanded(false);
  };

  return (
    <Fragment>
      <Card
        id={`${id}-card`}
        data-test-id={`${id}-card`}
        sx={{
          height: "100%",
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
          padding: 0.5,
          width: "100%",
        }}
      >
        <CardContent
          id={`${id}-card-content`}
          data-test-id={`${id}-card-content`}
        >
          <Grid
            container
            id={`${id}-header-container`}
            data-test-id={`${id}-header-container`}
            justifyContent="space-between"
            alignItems="flex-start"
            spacing={1}
            sx={{ pb: 1, pl: 1 }}
          >
            <Grid
              container
              size="grow"
              justifyContent="space-between"
              alignItems="center"
            >
              <Grid
                item
                id={`${id}-header-title`}
                data-test-id={`${id}-header-title`}
              >
                <Typography variant="h5">{title}</Typography>
              </Grid>
              {headerContent && (
                <Grid
                  item
                  id={`${id}-header-content`}
                  data-test-id={`${id}-header-content`}
                >
                  {headerContent}
                </Grid>
              )}
            </Grid>
            <Grid item sx={{ width: "auto" }}>
              {" "}
              {/* so it only takes up the minimum required amount of space */}
              <WidgetMenu
                title={id}
                data={widgetData}
                disabled={error || isLoading || (widgetData?.length ?? 0) < 1}
                showExport={showExport}
                showExpand={showExpand}
                handleExpand={handleExpand}
              />
            </Grid>
          </Grid>
          {children}
        </CardContent>
      </Card>
      <GenericModal
        isOpen={expanded}
        title={title}
        handleClose={handleClose}
        fullWidth
      >
        {headerContent && (
          <Box
            id={`${id}-modal-header`}
            data-test-id={`${id}-modal-header`}
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignContent: "center",
              padding: ".5em 0",
              width: "100%",
              boxSixing: "border-box", // includes padding in width
            }}
          >
            {headerContent}
          </Box>
        )}
        {children}
      </GenericModal>
    </Fragment>
  );
}

export default WidgetCardWrapper;
