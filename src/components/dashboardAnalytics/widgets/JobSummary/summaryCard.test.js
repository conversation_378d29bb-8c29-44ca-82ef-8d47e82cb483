import React from "react";
import { render } from "@testing-library/react";
import { JobSummaryCard } from "./summaryCard";

describe("JobSummaryCard component", () => {
  const jobData = {
    budget: 100,
    totalTime: 60,
    percentComplete: 50,
    packages: 10,
    drawings: 20,
    revisedDrawings: 5,
    rejections: 2,
    lastDrawingDue: "2023-03-15",
  };

  it("renders summary items correctly", () => {
    const { getByText } = render(<JobSummaryCard jobData={jobData} />);
    expect(getByText("Job Calculated Budget")).toBeInTheDocument();
    expect(getByText(`${jobData.budget} h`)).toBeInTheDocument();
    expect(getByText("Job Total Time")).toBeInTheDocument();
    expect(getByText(`${jobData.totalTime} m`)).toBeInTheDocument();
  });

  it("renders icons correctly", () => {
    const { getByTestId } = render(<JobSummaryCard jobData={jobData} />);
    expect(getByTestId("AttachMoneyIcon")).toBeInTheDocument();
    expect(getByTestId("AccessTimeIcon")).toBeInTheDocument();
  });

  it("renders SummaryCard components correctly", () => {
    const { getAllByRole } = render(<JobSummaryCard jobData={jobData} />);
    const summaryCards = getAllByRole("card");
    expect(summaryCards.length).toBe(8); // 8 summary items
  });

  it("handles loading state correctly", () => {
    const isLoading = true;
    const { getByText } = render(
      <JobSummaryCard jobData={jobData} isLoading={isLoading} />
    );
    expect(getByText("Loading...")).toBeInTheDocument();
  });

  it("handles error state correctly", () => {
    const error = "Error message";
    const { getByText } = render(
      <JobSummaryCard jobData={jobData} error={error} />
    );
    expect(getByText(error)).toBeInTheDocument();
  });
});
