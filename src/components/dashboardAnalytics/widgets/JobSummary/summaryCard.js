import React from "react";
import { Box, Grid2 as Grid } from "@mui/material";
import {
  Schedule,
  Timer,
  CheckCircle,
  InsertDriveFile,
  VisibilityOutlined,
  Inventory,
  CalendarToday,
  HighlightOff,
} from "@mui/icons-material";
import SummaryCard from "../../../widgets/summaryCard";
import withLoader from "../../../reusable/hoc/withLoader";
import withError from "../../../reusable/hoc/withError";
import WidgetMenu from "../widgetMenu";

const JobSummaryCard = ({ jobData }) => {
  const summaryItems = [
    {
      title: "Job Calculated Budget",
      value: jobData.job_budget ? `${jobData.job_budget}h` : "-",
      icon: <Schedule />,
    },
    {
      title: "Job Total Time",
      // need to round back to 2 decimal places after adding...
      value: jobData.job_allocated_time_hrs
        ? `${(
            (jobData.job_allocated_time_hrs ?? 0) +
            (jobData.job_unallocated_time_hrs ?? 0)
          ).toFixed(2)}h`
        : "-",
      icon: <Timer />,
    },
    {
      title: "Job Percent Complete",
      value: jobData.job_percent_complete
        ? `${jobData.job_percent_complete}%`
        : "-",
      icon: <CheckCircle />,
    },
    {
      title: "# of Packages",
      value: jobData.package_count ?? "-",
      icon: <Inventory />,
    },
    {
      title: "# of Drawings",
      value: jobData.drawing_count ?? "-",
      icon: <InsertDriveFile />,
    },
    {
      title: "# of Work Items",
      value: jobData.work_item_count ?? "-",
      icon: <VisibilityOutlined />,
    },
    {
      title: "# of Rejections",
      value: jobData.rejection_count ?? "-",
      icon: <HighlightOff />,
    },
    {
      title: "Last Drawing Due Date",
      value: jobData.latest_drawing_due_date ?? "-",
      icon: <CalendarToday />,
    },
  ];

  return (
    <Box
      sx={{
        height: "100%",
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-between",
      }}
    >
      {/* <WidgetMenu title="Job Summary Overview" data={[jobData]} /> */}
      <Grid container direction="row" spacing={2}>
        {summaryItems.map((item, index) => (
          <Grid item size={{ xs: 12, md: 6 }} key={index}>
            <SummaryCard
              title={item.title}
              value={item.value}
              icon={item.icon}
            />
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default withLoader(withError(JobSummaryCard)); // Apply HOCs
