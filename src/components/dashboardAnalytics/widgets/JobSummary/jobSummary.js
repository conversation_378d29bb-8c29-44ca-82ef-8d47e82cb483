import React from "react";
import { useSelector } from "react-redux";
import JobSummaryCard from "./summaryCard";

const JobSummary = () => {
  const jobSummary = useSelector(
    (state) => state.dashboardAnalytics.jobSummary
  );
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState(null);
  const [resData, setResData] = React.useState({});

  React.useEffect(() => {
    // set the data before we remove the loading screen
    if (
      jobSummary?.data?.hasOwnProperty("data") &&
      jobSummary?.data?.data?.length > 0
    ) {
      setResData(jobSummary?.data?.data[0]);
    }
    setError(jobSummary?.error);
    setIsLoading(jobSummary?.isLoading);
  }, [jobSummary]);

  return (
    <JobSummaryCard isLoading={isLoading} error={error} jobData={resData} />
  );
};

export default JobSummary;
