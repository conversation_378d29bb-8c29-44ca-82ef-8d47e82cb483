import { Bar<PERSON><PERSON> } from "@mui/x-charts/BarChart";
import { useState } from "react";

// import { useDrawingArea } from "@mui/x-charts/hooks";
import { styled, useTheme } from "@mui/material/styles";
import withLoader from "../../../reusable/hoc/withLoader";
import withError from "../../../reusable/hoc/withError";

const PackagesByMonth = ({ data }) => {
  const theme = useTheme();
  const defaults = {
    showMark: false,
    stack: "total",
    barWidth: 10, // Adjust bar width if needed
  };

  const isEmptyDataset = (data = []) =>
    data.length === 0 ||
    data.every(
      (row) => (row.uploaded ?? 0) === 0 && (row.completed ?? 0) === 0
    );

  const isBlank = isEmptyDataset(data);

  const series = isBlank
    ? [
        {
          dataKey: "uploaded",
          label: "No Data",
          color: theme.palette.grey[400],
          ...defaults,
        },
      ]
    : [
        {
          dataKey: "uploaded",
          label: "Packages Uploaded",
          color: theme.palette.legends.color2,
          ...defaults,
        },
        {
          dataKey: "completed",
          label: "Packages Completed",
          color: theme.palette.legends.color3,
          ...defaults,
        },
      ];

  const chartData = isBlank
    ? [{ month: "No Data", uploaded: 0 }]
    : data.map((d) => ({
        ...d,
        month: new Date(d.month)?.toLocaleString("en-US", {
          month: "short",
          year: "numeric",
        }),
      }));

  const [chartOptions, setChartOptions] = useState({
    dataset: chartData,
    series: series,
    xAxis: [
      {
        dataKey: "month",
        scaleType: "band",
        // height: 40,
        tickLabelStyle: { angle: -45, textAnchor: "end" },
      },
    ],
    yAxis: [
      {
        label: "# of Packages",
        width: 80,
        tickLabelStyle: {
          fill: theme.palette.text.primary,
        },
      },
    ],
    height: 350,
    margin: { top: 70, right: 30, bottom: 60, left: 90 },
    barGapRatio: 0.5,
    barCategoryGapRatio: 0.2,
    slotProps: {
      legend: {
        position: { vertical: "top", horizontal: "center" },
        direction: "row",
        itemMarkHeight: 12,
        itemMarkWidth: 12,
        labelStyle: {
          fill: theme.palette.text.primary,
        },
        padding: 5,
      },
    },
  });

  return <BarChart {...chartOptions} />;
};

export default withLoader(withError(PackagesByMonth));
