import { useEffect, useState } from "react";
import { useSelector } from "react-redux";

import WidgetCardWrapper from "../widgetCardWrapper";
import PackagesByMonth from "./PackagesByMonth";

const PackageTrends = () => {
  const { packageTrends } = useSelector((state) => state.dashboardAnalytics);

  return (
    <WidgetCardWrapper
      dashboardName="Trends"
      title="Packages By Month"
      widgetData={packageTrends?.data?.data || []}
      isLoading={packageTrends.isLoading}
      error={packageTrends.error}
      // showExport
      showExpand
    >
      <PackagesByMonth
        isLoading={packageTrends.isLoading}
        error={packageTrends.error}
        data={packageTrends?.data?.data || []}
      />
    </WidgetCardWrapper>
  );
};

export default PackageTrends;
