import React from "react";
import { Box } from "@mui/material";
import withLoader from "../../reusable/hoc/withLoader";
import withError from "../../reusable/hoc/withError";
import { GRID_DEFAULTS, getColumns } from "../dashboardConstants";

import AgTable from "../../reusable/agTable/AgTable";
// import muiAgGridTheme from "../muiAgGridTheme"; // not available in current version

// import css specific for dashboards
// @ToDo: Expand this as we expand MUI integrations
import "./../stylesDashboardGrids.scss";

const GenericWidgetGrid = ({ type, data, gridId = "widget-ag-grid" }) => {
  const columns = getColumns(type);
  const gridRef = React.useRef(null);
  // default all widget grids to 300px
  const gridStyle = React.useMemo(
    () => ({ width: "100%", height: "300px" }),
    []
  );

  const gridOptions = {
    // theme: muiAgGridTheme,
    columnDefs: columns,
    rowData: data,
    ref: gridRef,
    ...GRID_DEFAULTS,
  };

  return (
    <Box className="dashboard-widget-grid" sx={gridStyle}>
      <AgTable
        gridOptions={gridOptions}
        ariaLabel={gridId}
        id={gridId}
        data-test-id={gridId}
      />
    </Box>
  );
};

export default withLoader(withError(GenericWidgetGrid)); // Apply HOCs
