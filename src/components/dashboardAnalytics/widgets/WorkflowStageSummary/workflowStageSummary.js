import React from "react";
import { useSelector } from "react-redux";
import { Box, Typography } from "@mui/material";
import BarChartWidget from "./barChart";
import WidgetCardWrapper from "../widgetCardWrapper";

const WorkflowStageSummary = () => {
  const workflowStageSummary = useSelector(
    (state) => state.dashboardAnalytics.workflowStageSummary
  );
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState(null);
  const [resData, setResData] = React.useState([]);
  const [chartData, setChartData] = React.useState([]);
  const [isExpanded, setIsExpanded] = React.useState(false);

  React.useEffect(() => {
    // set the data before we remove the loading screen
    const parsedData = workflowStageSummary?.data?.data ?? [];
    setResData(parsedData);
    // Transform API data to chart data
    setChartData(
      parsedData?.map((item) => ({
        stage: `${item.work_flow_name}-${item.stage_name}`,
        allocated: item.allocated_time_hrs,
        unallocated: item.unallocated_time_hrs,
      }))
    );
    setError(workflowStageSummary?.error);
    setIsLoading(workflowStageSummary?.isLoading);
  }, [workflowStageSummary]);

  const headerContent = () => {
    return (
      <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Box
            sx={{
              width: 12,
              height: 12,
              borderRadius: "50%",
              backgroundColor: "#3498db",
            }}
          />
          <Typography variant="body2" color="#fff">
            Unallocated Time (h)
          </Typography>
        </Box>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Box
            sx={{
              width: 12,
              height: 12,
              borderRadius: "50%",
              backgroundColor: "#2ecc71",
            }}
          />
          <Typography variant="body2" color="#fff">
            Allocated Time (h)
          </Typography>
        </Box>
      </Box>
    );
  };

  return (
    <WidgetCardWrapper
      dashboardName="Job Summary"
      title="Hours by Work Flow and Stage"
      headerContent={headerContent()}
      widgetData={resData} // not this is different than the chart data... raw data
      isLoading={isLoading}
      error={error}
      // showExport
      showExpand
      setIsExpanded={setIsExpanded}
    >
      <Box
        maxHeight={isExpanded ? "70vh" : 300}
        overflow={chartData.length > 15 ? "scroll" : "auto"}
      >
        <BarChartWidget
          isLoading={isLoading}
          error={error}
          chartData={chartData}
        />
      </Box>
    </WidgetCardWrapper>
  );
};

export default WorkflowStageSummary;
