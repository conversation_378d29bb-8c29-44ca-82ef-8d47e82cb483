import reducer from "./dashboardAnalyticsReducer";

const initialState = {
  isLoading: false,
  error: null,
  filterData: [],
  jobSummary: {
    isLoading: false,
    error: null,
    data: [],
  },
};

describe("Dashboard Reducer", () => {
  it("should return the initial state", () => {
    expect(reducer(undefined, {})).toEqual(initialState);
  });

  it("should handle RECEIVE_DASHBOARD_FILTER_STARTED", () => {
    const action = { type: "RECEIVE_DASHBOARD_FILTER_STARTED" };
    expect(reducer(initialState, action)).toEqual({
      ...initialState,
      isLoading: true,
      error: null,
    });
  });

  it("should handle RECEIVE_DASHBOARD_FILTER_SUCCEEDED", () => {
    const payload = [{ id: 1, name: "Filter 1" }];
    const action = { type: "RECEIVE_DASHBOARD_FILTER_SUCCEEDED", payload };
    expect(reducer(initialState, action)).toEqual({
      ...initialState,
      isLoading: false,
      error: null,
      filterData: payload,
    });
  });

  it("should handle RECEIVE_DASHBOARD_FILTER_FAILED", () => {
    const error = "Error fetching filter data";
    const action = { type: "RECEIVE_DASHBOARD_FILTER_FAILED", payload: error };
    expect(reducer(initialState, action)).toEqual({
      ...initialState,
      isLoading: false,
      filterData: [],
      error,
    });
  });

  it("should handle RECEIVE_DASHBOARD_JOB_SUMMARY_STARTED", () => {
    const action = { type: "RECEIVE_DASHBOARD_JOB_SUMMARY_STARTED" };
    expect(reducer(initialState, action)).toEqual({
      ...initialState,
      jobSummary: { ...initialState.jobSummary, isLoading: true },
    });
  });

  it("should handle RECEIVE_DASHBOARD_JOB_SUMMARY_SUCCEEDED", () => {
    const payload = [{ jobId: 101, title: "Job 101" }];
    const action = { type: "RECEIVE_DASHBOARD_JOB_SUMMARY_SUCCEEDED", payload };
    expect(reducer(initialState, action)).toEqual({
      ...initialState,
      jobSummary: {
        ...initialState.jobSummary,
        isLoading: false,
        data: payload,
      },
    });
  });

  it("should handle RECEIVE_DASHBOARD_JOB_SUMMARY_FAILED", () => {
    const error = "Error fetching job summary";
    const action = {
      type: "RECEIVE_DASHBOARD_JOB_SUMMARY_FAILED",
      payload: error,
    };
    expect(reducer(initialState, action)).toEqual({
      ...initialState,
      jobSummary: { ...initialState.jobSummary, isLoading: false, error },
    });
  });
});
