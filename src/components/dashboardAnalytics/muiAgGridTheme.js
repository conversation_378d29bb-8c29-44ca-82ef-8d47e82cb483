// const { themeBalham } = require('ag-grid-enterprise');
const { colors } = require("../../theme/theme.js");

// ToDo: Apply once we upgrade ag-grid to customize theme to match MUI theme
const muiAgGridTheme = () => {
  return null;
  // return themeBalham.withParams({
  //     backgroundColor: colors.background.secondary,
  //     accentColor: colors.primary.main,
  //     oddRowBackgroundColor: `${colors.secondary.main}0A`,
  // });
};

export default muiAgGridTheme;
