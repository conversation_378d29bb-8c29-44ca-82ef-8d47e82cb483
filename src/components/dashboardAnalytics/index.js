import React, { useEffect } from "react";
import {
  Paper,
  Box,
  Typography,
  Tab,
  Tabs,
  Grid2 as Grid,
} from "@mui/material";
import DashboardJobsSummary from "./DashboardJobsSummary";
import TabPanel from "./TabPanel";
import { handleSetPageTitle } from "../../redux/generalActions";
import { useDispatch, useSelector } from "react-redux";
import Trends from "./Trends";

// import css specific for dashboards
import "./stylesDashboardAnalytics.scss";
// @ToDo: Expand this as we expand MUI integrations
import "./stylesDashboardGrids.scss";


const ANALYTICS_SYSTEM_FEATURE_ID = 35;
const ANALYTICS_PERMISSION_ID = 304;
const TAB_TRENDS_SYSTEM_FEATURE_ID = 58;

const tabsPanelIndex = [
  { component: <DashboardJobsSummary /> },
  { component: <Trends /> },
  { component: "Comparison (Coming Soon)" },
];

const DashboardAnalytics = () => {
  const dispatch = useDispatch();
  const [value, setValue] = React.useState(0);

  const { systemSettings, features, permissions, userSettings } = useSelector(
    (state) => state.profileData
  );

  useEffect(() => {
    dispatch(handleSetPageTitle());
  }, []);

  useEffect(() => {
    if (!features?.length || !permissions.length) return;

    // redirect to homepage if user doesn't have permission or system feature
    if (
      !features.includes(ANALYTICS_SYSTEM_FEATURE_ID) ||
      !permissions.includes(ANALYTICS_PERMISSION_ID)
    ) {
      const homePage = userSettings?.homePage
        ? `${userSettings?.home_page}`
        : "jobs";
      const URL = `${process.env.REACT_APP_FABPRO}/${homePage}`;
      window.location.assign(URL);
    }

    if (!features.includes(TAB_TRENDS_SYSTEM_FEATURE_ID))
      tabsPanelIndex.splice(1, 1);
  }, [features, userSettings, permissions]);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <Box
      id="dashboard-analytics-container"
      data-testid="dashboard-analytics-container"
    >
      <Box
        id="data-analytics-page-header"
        datatest-id="data-analytics-page-header"
        className="page-header"
        variant="outlined"
      >
        <Grid container direction="row" spacing={2}>
          <Grid item size={{ xs: 12, md: 4 }} sx={{ pt: 1.5 }}>
            <Typography
              variant="h5"
              sx={{
                textAlign: {
                  xs: "center",
                  md: "left",
                },
              }}
            >
              Dashboard Analytics
            </Typography>
          </Grid>
          <Grid item size={{ xs: 12, md: 8 }}>
            <Box
              id="dashboard-analytics-tabs"
              data-testid="dashboard-analytics-tabs"
            >
              <Tabs
                value={value}
                onChange={handleChange}
                variant="scrollable"
                scrollButtons
                allowScrollButtonsMobile
              >
                <Tab label="Jobs Summary" />
                {features?.includes(TAB_TRENDS_SYSTEM_FEATURE_ID) && (
                  <Tab label="Trends" />
                )}
                <Tab label="More Coming Soon" disabled />

              </Tabs>
            </Box>
          </Grid>
        </Grid>
      </Box>
      <Box id="dashboard-analytics-content" className="tab-panel-content">
        {tabsPanelIndex?.map((panel, index) => (
          <TabPanel value={value} index={index}>
            {panel?.component}

          </TabPanel>
        ))}
      </Box>
    </Box>
  );
};

export default DashboardAnalytics;
