@import "../styles/colors.scss";
@import "../styles/sizes.scss";

#dashboard-analytics-container,
.dashboard-widget-grid {
  & .ag-theme-balham-dark {
    margin: 0;
    width: 100%;
    height: 300px;

    // root background colors to match MUI theme...
    & .ag-root,
    & .ag-header,
    & .ag-side-buttons,
    & .ag-status-bar,
    & .ag-paging-panel {
      background-color: $backgroundSecondary;
    }

    // remove default dark colored backgrounds and just reflect the background of the grid...
    & .ag-row {
      background-color: transparent;
    }
    & .ag-row:nth-of-type(even) {
      background-color: rgba($secondaryMain, 0.07);
    }
    & .ag-row:nth-of-type(odd) {
      background-color: rgba($secondaryMain, 0.03);
    }

    & .ag-row:hover {
      background-color: $actionHover;
    }

    & .ag-cell:hover {
      color: $primaryMain;
    }

    // to prevent the grid from moving when the right click menu opens
    & .ag-popup {
      position: absolute;
    }
  }
}
