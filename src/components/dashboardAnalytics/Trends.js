import { Grid2 as Grid } from "@mui/material";
import { useEffect } from "react";
import { useDispatch } from "react-redux";

// import WidgetCardWrapper from "./widgets/widgetCardWrapper";
import HoursTrends from "./widgets/FabHoursTrends";
import PackageTrends from "./widgets/PackagesByMonth";
import {
  handleFetchDashboardTrendsHours,
  handleFetchDashboardPackageTrendsByMonth,
} from "./dashboardAnalyticsActions";

const Trends = () => {
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(handleFetchDashboardTrendsHours());
    dispatch(handleFetchDashboardPackageTrendsByMonth());
  }, []);

  return (
    <Grid
      container
      spacing={2}
      id="trends-container"
      data-testid="trends-container"
    >
      <Grid size={{ xs: 12, sm: 6 }}>
        <HoursTrends />
      </Grid>

      <Grid size={{ xs: 12, sm: 6 }}>
        <PackageTrends />
      </Grid>

      {/* <Grid size={{ xs: 12, sm: 6 }}>
        <WidgetCardWrapper
          dashboardName="trends"
          title="Metric Amount By Month"
        >
          Coming Soon
        </WidgetCardWrapper>
      </Grid>

      <Grid size={{ xs: 12, sm: 6 }}>
        <WidgetCardWrapper
          dashboardName="trends"
          title="Productivity (Metric per Hour) By Month"
        >
          Coming Soon
        </WidgetCardWrapper>
      </Grid> */}
    </Grid>
  );
};

export default Trends;
