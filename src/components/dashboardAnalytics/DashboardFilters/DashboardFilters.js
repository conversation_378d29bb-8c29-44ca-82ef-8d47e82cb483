import React, { useEffect, useState, useCallback, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Icon<PERSON>utton,
  Button,
  Switch,
  Typography,
  Stack,
  Grid2 as Grid,
  Tooltip,
  Box,
} from "@mui/material";
import AutocompleteSearch from "./Autocomplete";
import {
  handleFetchDashboardFilters,
  handleFetchDashboardJobSummary,
  handleFetchDashboardHoursOverView,
  handleFetchDashboardWorkflowStageSummary,
  handleFetchDashboardJobMetricOverview,
  handleFetchDashboardJobMaterialOverview,
  handleFetchDashboardFiltersValidationToken,
} from "../dashboardAnalyticsActions";
import { trackMixPanelEvent } from "../../../utils/_mixPanelUtils";
import { notify } from "../../reusable/alertPopup/alertPopupActions";
import CloseOutlinedIcon from "@mui/icons-material/CloseOutlined";
import { DateFilterGroup, getLastTwelveMonths } from "./DateFilterGroup";

const DashboardFilters = (params) => {
  const mxCategory = "dashboard";
  const filterTab = params.tab;
  const {
    onApplyFilters,
    onClearFilters,
    setFiltersToggle,
    filtersApplied,
  } = params;
  const dispatch = useDispatch();
  const { filterData, filterValidationToken, error } = useSelector(
    (state) => state.dashboardAnalytics
  );
  const [jobsSelectedOptions, setJobsSelectedOptions] = useState(null);
  const [packageSelectedOptions, setPackageSelectedOptions] = useState([]);
  const [drawingsSelectedOptions, setDrawingsSelectedOptions] = useState([]);
  const [archiveSelected, setArchiveSelected] = useState(false);
  const [dateRangeSelection, setDateRangeSelection] = useState(
    getLastTwelveMonths()
  );

  const [jobsFilteredList, setJobsFilteredList] = useState([]);
  const [packageFilteredList, setPackageFilteredList] = useState([]);
  const [drawingFilteredList, setDrawingFilteredList] = useState([]);
  const [inputJobs, setInputJobs] = useState("");
  const [inputPackges, setInputPackages] = useState("");
  const [inputDrawings, setInputDrawings] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [filters, setFilters] = useState({});

  const useDebounce = (callback, delay) => {
    const debounceRef = useRef(null);

    return useCallback(
      (...args) => {
        if (debounceRef.current) {
          clearTimeout(debounceRef.current);
        }
        debounceRef.current = setTimeout(() => {
          callback(...args);
        }, delay);
      },
      [callback, delay]
    );
  };

  // Debounced Function
  const debouncedTrackMixPanelEvent = useDebounce((type, length) => {
    trackMixPanelEvent(
      `${type} filter`,
      length,
      mxCategory,
      Date.now(),
      `dashboard filter ${type} changed`
    );
  }, 2000);

  /*--- Functions ----*/
  // filter based on the parent selection, archived, and sort the entries by name...
  const updateFilters = (filterSelected, filterDependantSelected, type) => {
    let filteredList = [];
    if (type === "jobs") {
      filteredList = archiveSelected
        ? filterSelected
        : filterSelected.filter((entry) => entry.archived == 0);
    } else {
      for (let item of filterSelected) {
        if (item && item[type] && item[type].length > 0) {
          const filteredItem = archiveSelected
            ? item[type]
            : item[type].filter((entry) => entry.archived == 0);
          filteredList = filteredList.concat(filteredItem);
        }
      }
    }

    const updatedSelectedData = filterDependantSelected
      ?.filter((item2) => filteredList.some((item1) => item1.id === item2.id))
      .sort((a, b) => a.name.localeCompare(b.name));

    return {
      filteredList: filteredList.sort((a, b) => a.name.localeCompare(b.name)),
      updatedSelectedData,
    };
  };

  const applySavedFilters = () => {
    // See if we have saved filters in the session to reload after initial load...
    const savedFilters = JSON.parse(sessionStorage.getItem("dashboardFilters"));

    if (savedFilters) {
      if (savedFilters.jobs) {
        setArchiveSelected(savedFilters.archived ?? false);
        setJobsSelectedOptions(savedFilters.jobs);

        const filters = {
          job_ids: `${savedFilters.jobs?.id}`,
          archived: savedFilters.archived ? 1 : 0,
        };

        if (savedFilters.packages) {
          const data = updateFilters(
            [jobsSelectedOptions],
            packageSelectedOptions,
            "packages"
          );
          setPackageFilteredList(data?.filteredList);
          setPackageSelectedOptions(savedFilters.packages);

          filters.package_ids = savedFilters.packages
            .map((item) => item.id)
            .join(",");

          if (savedFilters.drawings) {
            const data = updateFilters(
              packageSelectedOptions,
              drawingsSelectedOptions,
              "drawings"
            );
            setDrawingFilteredList(data?.filteredList);
            setDrawingsSelectedOptions(savedFilters.drawings);
            filters.drawing_ids = savedFilters.drawings
              .map((item) => item.id)
              .join(",");
          }
        }

       if (
          savedFilters.start_date &&
          savedFilters.end_date &&
          savedFilters.dateFilterSelected
        ) {
          setDateRangeSelection({
            start_date: savedFilters.start_date,
            end_date: savedFilters.end_date,
            dateFilterSelected: savedFilters.dateFilterSelected,
          });
        }

        setFilters(filters);
      }
    }
  };

  const updateJobsFilter = (reloadSavedFilters = false) => {
    if (filterData && filterData.length > 0) {
      const data = updateFilters(filterData, jobsSelectedOptions, "jobs");
      setJobsSelectedOptions(data?.updatedSelectedData);
      setJobsFilteredList(data?.filteredList);

      // if initial load, we want to load the saved filters else we ignore...
      if (reloadSavedFilters) applySavedFilters();
    }
  };

  const updatePackagesFilter = () => {
    const data = updateFilters(
      [jobsSelectedOptions],
      packageSelectedOptions,
      "packages"
    );
    setPackageSelectedOptions(data?.updatedSelectedData);
    setPackageFilteredList(data?.filteredList);
  };

  const updateDrawingsFilter = () => {
    const data = updateFilters(
      packageSelectedOptions,
      drawingsSelectedOptions,
      "drawings"
    );
    setDrawingsSelectedOptions(data?.updatedSelectedData);
    setDrawingFilteredList(data?.filteredList);
  };

  const updateDateFilter = (range) => {
    setDateRangeSelection(range);
  };

  const handleFilterChangeAction = (e, value, type) => {
    switch (type) {
      case "jobs":
        if (value) {
          setJobsSelectedOptions(value);
        } else {
          setJobsSelectedOptions(null);
        }
        break;

      case "packages":
        value.length > 0
          ? setPackageSelectedOptions(value)
          : setPackageSelectedOptions([]);
        break;

      case "drawings":
        value.length > 0
          ? setDrawingsSelectedOptions(value)
          : setDrawingsSelectedOptions([]);
        break;

      default:
        break;
    }

    debouncedTrackMixPanelEvent(
      type,
      type !== "jobs" ? value?.length || 0 : Number(!!value)
    );
  };

  const saveFiltersToSessionStorage = () => {
    // save applied filters
    const filtersToSave = {
      jobs: jobsSelectedOptions,
      packages: packageSelectedOptions,
      drawings: drawingsSelectedOptions,
      archived: archiveSelected,
      dateFilterSelected: dateRangeSelection.dateFilterSelected,
      start_date: dateRangeSelection.start_date,
      end_date: dateRangeSelection.end_date,
    };
    sessionStorage.setItem("dashboardFilters", JSON.stringify(filtersToSave));
  };

  const handleApplyAction = () => {
    const filters = {
      job_ids: `${jobsSelectedOptions?.id}`,
      package_ids: packageSelectedOptions.map((item) => item.id).join(","),
      drawing_ids: drawingsSelectedOptions.map((item) => item.id).join(","),
      archived: archiveSelected ? 1 : 0,
      start_date: dateRangeSelection.start_date,
      end_date: dateRangeSelection.end_date,
    };

    setFilters(filters);

    trackMixPanelEvent(
      `Apply filters`,
      0,
      mxCategory,
      Date.now(),
      `dashboard filters applied ${JSON.stringify(filters)}`
    );
  };


  const filterSearchOptions = (options, state) => {
    const query = state?.inputValue?.toLowerCase();
    return options?.filter(
      (option) =>
        option.name.toLowerCase().includes(query) ||
        option.id.toString().includes(query)
    );
  };

  const handleArchiveToggle = (event) => {
    setArchiveSelected(event.target.checked);
  };

  /*--- Hooks ----*/
  useEffect(() => {
    dispatch(handleFetchDashboardFilters());
  }, []);

  useEffect(() => {
    updateJobsFilter(true);
  }, [filterData]);

  useEffect(() => {
    updateJobsFilter();
    updatePackagesFilter();
    updateDrawingsFilter();
  }, [archiveSelected]);

  useEffect(() => {
    if (error) setErrorMessage(error?.error?.message);
  }, [error]);

  useEffect(() => {
    updatePackagesFilter();
  }, [jobsSelectedOptions]);

  useEffect(() => {
    updateDrawingsFilter();
  }, [packageSelectedOptions]);

  useEffect(() => {
    if (filters.job_ids) {
      // close the filter window
      setFiltersToggle(false);
      onApplyFilters();
      dispatch(handleFetchDashboardFiltersValidationToken(filters));
    }
  }, [filters]);

  useEffect(() => {
    if (filterValidationToken.error) {
      dispatch(
        notify({
          id: Date.now(),
          type: "ERROR",
          message: filterValidationToken.error?.error?.message,
        })
      );
    } else if (filterValidationToken.data?.valid) {
      const filterData = {
        ...filters,
        validation_token: filterValidationToken.data?.token,
      };

      dispatch(handleFetchDashboardJobSummary(filterData));
      dispatch(handleFetchDashboardHoursOverView(filterData));
      dispatch(handleFetchDashboardWorkflowStageSummary(filterData));
      dispatch(handleFetchDashboardJobMetricOverview(filterData));
      dispatch(handleFetchDashboardJobMaterialOverview(filterData));
    }
  }, [filterValidationToken]);

  /*--- Render ----*/
  return (
    <>
      <Grid
        container
        id={`${filterTab}-dashboard-filters`}
        className="filter-container"
        spacing={2}
        direction="row"
        alignContent="center"
        justifyContent="center"
      >
        <Grid item size={{ xs: filtersApplied ? 10 : 12 }}>
          <Typography variant="h6">Job Summary Filters</Typography>
        </Grid>
        {filtersApplied && (
          <Grid item size={{ xs: 2 }}>
            <IconButton
              onClick={() => setFiltersToggle(false)}
              aria-label="Close Filters"
            >
              <CloseOutlinedIcon fontSize="small" />
            </IconButton>
          </Grid>
        )}
        {/* <Grid item size={{ xs: 12 }}>

          <Tooltip title="Coming soon" arrow>
            <Stack
              direction="column"
              spacing={0}
              sx={{ justifyContent: "center" }}
            >
              <Typography variant="caption">Include Archived</Typography>
              <Switch
                checked={archiveSelected}
                onChange={handleArchiveToggle}
                inputProps={{
                  "data-test-id": `${filterTab}-include-archived`,
                  "aria-label": `${filterTab}-include-archived`,
                }}
                disabled
              />
            </Stack>
          </Tooltip>
        </Grid> */}

        <Grid item size={{ xs: 12 }}>

          <AutocompleteSearch
            multiple={false}
            isDisabled={false}
            name="Job"
            filterData={jobsFilteredList}
            otherSettings={{
              getOptionDisabled: (e) =>
                archiveSelected ? false : e.archived === 1,
            }}
            onChange={(e, newValue) =>
              handleFilterChangeAction(e, newValue, "jobs")
            }
            selectedOption={jobsSelectedOptions}
            label="Job"
            filterOptions={filterSearchOptions}
            inputValue={inputJobs}
            onInputChange={(event, newInputValue) =>
              setInputJobs(newInputValue)
            }
            placeholder={
              Number(!!jobsSelectedOptions) ? "Change Job" : "Select a Job"
            }
            id={`${filterTab}-job-filter`}
          />
        </Grid>

        <Grid item size={{ xs: 12 }}>
          <AutocompleteSearch
            isDisabled={!Object.keys(jobsSelectedOptions || {}).length}
            name="Package"
            multiple
            checkboxes
            filterData={packageFilteredList}
            onChange={(e, newValue) =>
              handleFilterChangeAction(e, newValue, "packages")
            }
            selectedOption={packageSelectedOptions}
            label={
              packageSelectedOptions.length > 0 ? "Packages" : "All Packages"
            }
            filterOptions={filterSearchOptions}
            inputValue={inputPackges}
            onInputChange={(event, newInputValue) =>
              setInputPackages(newInputValue)
            }
            id={`${filterTab}-package-filter`}
            otherSettings={{
              disableCloseOnSelect: true,
            }}
          />
        </Grid>

        <Grid item size={{ xs: 12 }}>
          <AutocompleteSearch
            isDisabled={
              !Object.keys(jobsSelectedOptions || {}).length ||
              packageSelectedOptions.length === 0
            }
            name="Drawings"
            multiple
            checkboxes
            filterData={drawingFilteredList}
            onChange={(e, newValue) =>
              handleFilterChangeAction(e, newValue, "drawings")
            }
            selectedOption={drawingsSelectedOptions}
            label={
              drawingsSelectedOptions.length > 0 ? "Drawings" : "All Drawings"
            }
            filterOptions={filterSearchOptions}
            inputValue={inputDrawings}
            onInputChange={(event, newInputValue) =>
              setInputDrawings(newInputValue)
            }
            id={`${filterTab}-drawing-filter`}
            otherSettings={{
              disableCloseOnSelect: true,
            }}
          />
        </Grid>

        <Grid container item size={12} spacing={2}>
          <DateFilterGroup
            years={3}
            selectedDateRange={
              dateRangeSelection
                ? dateRangeSelection.dateFilterSelected
                : getLastTwelveMonths()
            }
            onDateRangeSelect={updateDateFilter}
          />
        </Grid>

        <Grid
          item
          size={{ xs: 6 }}
          sx={{ justifyContent: "center", alignContent: "center" }}
        >
          <Tooltip title="Clear filters and dashboard">
            <Button
              id={`${filterTab}-clear-filters`}
              variant="outlined"
              onClick={() => {
                setJobsSelectedOptions(null);
                setArchiveSelected(false);
                setDateRangeSelection(getLastTwelveMonths());
                onClearFilters();
                sessionStorage.removeItem("dashboardFilters");
              }}
              disabled={!Object.keys(jobsSelectedOptions || {}).length}
              fullWidth
            >
              Clear
            </Button>
          </Tooltip>
        </Grid>

        <Grid
          item
          size={{ xs: 6 }}
          sx={{ justifyContent: "center", alignContent: "center" }}
        >
          <Tooltip
            title={
              !Object.keys(jobsSelectedOptions || {}).length
                ? "Select a job"
                : "Load dashboard"
            }
          >
            <span>
              <Button
                variant="contained"
                id={`${filterTab}-apply-filters`}
                onClick={() => {
                  saveFiltersToSessionStorage();
                  handleApplyAction();
                }}
                disabled={!Object.keys(jobsSelectedOptions || {}).length}
                fullWidth
              >
                Apply
              </Button>
            </span>
          </Tooltip>
        </Grid>
      </Grid>
      {errorMessage && <Typography color="error">{errorMessage}</Typography>}
    </>
  );
};

export default DashboardFilters;
