import { Button, Grid2 as Grid, Tooltip } from "@mui/material";

const dateFilterConstants = {
  LTM: "LTM",
  LTM_LABEL: "Last 12 Months",
  LTM_TOOLTIP: "Select data for the last 12 months",
  YEAR_TOOLTIP: "Select data for",
};

// Format the dates as 'YYYY-MM-DD'
function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-indexed
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}

export function getLastTwelveMonths() {
  // Get today's date
  const endDate = new Date();

  // Create a new date object for the start date (12 months ago)
  const startDate = new Date();
  startDate.setFullYear(startDate.getFullYear() - 1);

  return {
    start_date: formatDate(startDate),
    end_date: formatDate(endDate),
    dateFilterSelected: dateFilterConstants.LTM,
  };
}

function getYearDateRange(year) {
  return {
    start_date: `${year}-01-01`,
    end_date: `${year}-12-31`,
    dateFilterSelected: year,
  };
}

function DateFilterButton({ text, tooltipText, selected, onClick }) {
  let tag =
    text === dateFilterConstants.LTM_LABEL ? dateFilterConstants.LTM : text;

  return (
    <Grid item size={{ xs: 12, md: 6 }}>
      <Tooltip title={tooltipText}>
        <Button
          id={`${tag}-date-filters`}
          variant={selected ? "contained" : "outlined"}
          sx={{ textTransform: "uppercase", borderRadius: 1 }}
          onClick={onClick}
          fullWidth
        >
          {text}
        </Button>
      </Tooltip>
    </Grid>
  );
}

export function DateFilterGroup({
  years = 3,
  selectedDateRange,
  onDateRangeSelect,
}) {
  // Calculate the number of years to prepare buttons for.
  const yearArray = Array.from(
    { length: years },
    (x, i) => new Date().getFullYear() - i
  );

  if (yearArray.length < 1) {
    return <></>;
  }

  return (
    <>
      <DateFilterButton
        text={dateFilterConstants.LTM_LABEL}
        tooltipText={dateFilterConstants.LTM_TOOLTIP}
        selected={selectedDateRange == dateFilterConstants.LTM}
        onClick={() => onDateRangeSelect(getLastTwelveMonths())}
      />
      {yearArray.length >= 1 &&
        yearArray.map((year) => (
          <DateFilterButton
            key={year}
            text={year}
            tooltipText={`${dateFilterConstants.YEAR_TOOLTIP} ${year}`}
            selected={selectedDateRange == year}
            onClick={() => onDateRangeSelect(getYearDateRange(year))}
          />
        ))}
    </>
  );
}
