import * as React from "react";
import {
  Paper,
  Box,
  FormControlLabel,
  Divider,
  Autocomplete,
  TextField,
  Checkbox,
  Chip,
} from "@mui/material";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

const AutocompleteSearch = ({
  name,
  limitTags,
  filterData,
  onChange,
  label,
  placeholder,
  selectedOption,
  isDisabled,
  multiple,
  checkboxes,
  filterOptions,
  onInputChange,
  inputValue,
  allowSelectAll = false,
  otherSettings,
}) => {
  let multipleSettings = {};
  let styleSetting = {
    "& .MuiAutocomplete-tag": {
      maxWidth: "60%",
      overflow: "hidden",
      textOverflow: "ellipsis",
      whiteSpace: "nowrap",
    },
    "& .MuiInputBase-root": {
      maxWidth: "100%",
      overflow: "hidden",
    },
    "& .MuiAutocomplete-input": {
      minWidth: "unset !important",
    },
  };

  const [selectAll, setSelectAll] = React.useState(false);
  const handleToggleSelectAll = () => {
    setSelectAll((prev) => {
      if (!prev) onChange(null, [...filterData]);
      else onChange(null, []);
      return !prev;
    });
  };

  // update select all state on change...
  React.useEffect(() => {
    if (!allowSelectAll) return;
    setSelectAll(selectedOption?.length == filterData?.length);
  }, [selectedOption]);

  // modify how tags are rendered...
  if (multiple) {
    multipleSettings = {
      ...multipleSettings,
      limitTags: limitTags,
      renderTags: (value, getTagProps) => {
        const numTags = value.length;
        const limitTags = 1;

        return (
          <>
            {value.slice(0, limitTags).map((option, index) => (
              <Chip
                {...getTagProps({ index })}
                key={index}
                label={option.name}
                title={option.name}
              />
            ))}

            {numTags > limitTags && ` +${numTags - limitTags}`}
          </>
        );
      },
    };
  }

  if (checkboxes) {
    styleSetting = {
      "& .MuiAutocomplete-tag": {
        maxWidth: "50%",
        overflow: "hidden",
        textOverflow: "ellipsis",
        whiteSpace: "nowrap",
      },
      "& .MuiAutocomplete-input": {
        minWidth: "unset !important",
      },
    };

    // add checkboxes
    multipleSettings = {
      ...multipleSettings,
      renderOption: (props, option, { selected }) => {
        const { key, ...optionProps } = props;
        return (
          <li key={key} {...optionProps}>
            <Checkbox
              icon={icon}
              checkedIcon={checkedIcon}
              style={{ marginRight: 8 }}
              checked={selected}
            />
            ({option.id}) {option.name}
          </li>
        );
      },
    };
    // add select all option...
    if (allowSelectAll) {
      multipleSettings = {
        ...multipleSettings,
        PaperComponent: (paperProps) => {
          const { children, ...restPaperProps } = paperProps;
          return (
            <Paper {...restPaperProps}>
              {filterData && filterData.length > 0 && (
                <Box
                  onMouseDown={(e) => e.preventDefault()} // prevent blur
                >
                  <FormControlLabel
                    onClick={(e) => {
                      e.preventDefault(); // prevent blur
                      handleToggleSelectAll();
                    }}
                    sx={{ p: "6px 25px" }}
                    label="Select all"
                    data-test-id="autocomplete-select-all"
                    aria-label="Select all"
                    control={
                      <Checkbox
                        id="select-all-checkbox"
                        icon={icon}
                        checkedIcon={checkedIcon}
                        checked={selectAll}
                      />
                    }
                  />
                  <Divider />
                </Box>
              )}
              {children}
            </Paper>
          );
        },
      };
    }
  }
  return (
    <Autocomplete
      id={
        multiple
          ? "autocomplete-select"
          : "autocomplete-select-multiple-limit-tags"
      }
      sx={styleSetting}
      multiple={multiple}
      disabled={isDisabled}
      name={name}
      options={filterData}
      value={selectedOption}
      filterOptions={filterOptions || []}
      onChange={onChange}
      onInputChange={onInputChange}
      getOptionLabel={(option) => `(${option.id}) ${option.name}`}
      inputValue={inputValue || ""}
      renderInput={(params) => {
        const hasPlaceholder =
          placeholder || (selectedOption && selectedOption.length < 1);
        const placeholderProps = hasPlaceholder
          ? { placeholder: placeholder || "Search" }
          : {};
        return <TextField {...params} label={label} {...placeholderProps} />;
      }}
      {...multipleSettings}
      {...otherSettings}
      isOptionEqualToValue={(option, value) => option.id === value.id}
    />
  );
};

export default AutocompleteSearch;
