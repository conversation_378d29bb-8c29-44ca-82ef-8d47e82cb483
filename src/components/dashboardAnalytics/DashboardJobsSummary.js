import React from "react";
import { <PERSON>, Typography, Grid2 as <PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import DashboardFilters from "./DashboardFilters/DashboardFilters";
import JobSummary from "./widgets/JobSummary/jobSummary";
import HoursOverview from "./widgets/HoursOverView/index";
import WorkflowStageSummary from "./widgets/WorkflowStageSummary/workflowStageSummary";
import MetricOverviewCard from "./widgets/MetricOverview/metricOverviewCard";
import MaterialOverviewCard from "./widgets/MaterialOverview/materialOverviewCard";
import FilterListIcon from "@mui/icons-material/FilterList";
import "./stylesDashboardAnalytics.scss";

const DashboardJobSummary = () => {
  const [filtersApplied, setFiltersApplied] = React.useState(false);
  const [filtersToggle, setFiltersToggle] = React.useState(false);


  const handleApplyFilters = () => {
    setFiltersApplied(true);
  };

  const handleClearFilters = () => {
    setFiltersApplied(false);
  };

  React.useEffect(() => {
    // if no filters, open the filters panel...
    // this is to avoid us hiding the scrollbar on initial load with filters
    if (!filtersApplied) setFiltersToggle(true);
  }, []);

  React.useEffect(() => {
    // hide scroll ability if we have filters panel open...
    if (filtersToggle) {
      document.getElementById("dashboard-analytics-container").scrollTo(0, 0);
      document.getElementById("dashboard-analytics-container").style.overflowY =
        "hidden";
    } else {
      document.getElementById("dashboard-analytics-container").style.overflowY =
        "scroll";
    }
  }, [filtersToggle]);

  return (
    <>
      <Box
        id="job-summary-filters-container"
        className="drawer-container"
        sx={{ display: filtersToggle ? "block" : "none" }}
      >
        <DashboardFilters
          tab="jobs-summary"
          onApplyFilters={handleApplyFilters}
          onClearFilters={handleClearFilters}
          setFiltersToggle={setFiltersToggle}
          filtersApplied={filtersApplied}
        />
      </Box>
      <Box id="jobs-summary-container" data-testid="jobs-summary-container">
        {filtersApplied ? (
          <Grid
            container
            direction="row"
            spacing={2}
            id="grid-widget-container"
          >
            {/* Add the filters open/close button to the right of the top of the widgets */}
            <Grid item size={{ xs: 12 }} sx={{ textAlign: "right" }}>
              <Button
                variant="contained"
                id={`toggle-filters`}
                onClick={(e) => {
                  setFiltersToggle(!filtersToggle);
                }}
              >
                FILTERS
                <FilterListIcon fontSize="small" sx={{ ml: 1 }} />
              </Button>
            </Grid>
            <Grid
              item
              size={{ xs: 12, md: 6, xl: 4 }}
              data-testid="jobs-summary-overview"
            >
              <JobSummary />
            </Grid>
            <Grid
              item
              size={{ xs: 12, md: 6, xl: 3 }}
              data-testid="hours-overview-widget"
            >
              <HoursOverview />
            </Grid>
            <Grid
              item
              size={{ xs: 12, md: 12, xl: 5 }}
              data-testid="jobs-summary-hours-by-workflow"
            >
              <WorkflowStageSummary />
            </Grid>
            <Grid
              item
              size={{ xs: 12, md: 12, xl: 6 }}
              data-testid="jobs-summary-metrics-grid"
            >
              <MetricOverviewCard />
            </Grid>
            <Grid
              item
              size={{ xs: 12, md: 12, xl: 6 }}
              data-testid="jobs-summary-material-grid"
            >
              <MaterialOverviewCard />
            </Grid>
          </Grid>
        ) : (
          <Grid id="choose-filter-grid" container>
            <Typography>Please choose your filters</Typography>
          </Grid>
        )}
      </Box>
    </>
  );
};

export default DashboardJobSummary;
