import React from "react";
import { Card, CardContent, Typography, Box, Stack } from "@mui/material";

const SummaryCard = ({ title, value, icon }) => {
  return (
    <Card sx={{ borderRadius: 2, height: "100%" }}>
      <CardContent>
        <Stack direction="row" spacing={2} alignItems="center">
          {icon}
          <Box>
            <Typography variant="body2" color="textSecondary">
              {title}
            </Typography>
            <Typography variant="h6">{value}</Typography>
          </Box>
        </Stack>
      </CardContent>
    </Card>
  );
};

export default SummaryCard;
