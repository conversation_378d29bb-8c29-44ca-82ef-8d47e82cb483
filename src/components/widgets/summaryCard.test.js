import SummaryCard from "./summaryCard";
import { AttachMoney as AttachMoneyIcon } from "@mui/icons-material";

describe("SummaryCard Component", () => {
  it("renders without crashing", () => {
    const component = SummaryCard({
      title: "Job Calculated Budget",
      value: `6,238 h`,
      icon: "<AttachMoneyIcon />",
    });
    expect(component).toBeTruthy();
  });

  it("renders the title correctly", () => {
    const component = SummaryCard({
      title: "Job Calculated Budget",
      value: `6,238 h`,
      icon: "<AttachMoneyIcon />",
    });
    expect(
      component.props.children.props.children.props.children[1].props
        .children[0].props.children
    ).toBe("Job Calculated Budget");
  });

  it("renders the value correctly", () => {
    const component = SummaryCard({
      title: "Job Calculated Budget",
      value: `6,238 h`,
      icon: "<AttachMoneyIcon />",
    });
    expect(
      component.props.children.props.children.props.children[1].props
        .children[1].props.children
    ).toBe("6,238 h");
  });

  it("renders the icon", () => {
    const component = SummaryCard({
      title: "Job Calculated Budget",
      value: `6,238 h`,
      icon: "<AttachMoneyIcon />",
    });
    expect(component.props.children.props.children.props.children[0]).toBe(
      "<AttachMoneyIcon />"
    );
  });
});
