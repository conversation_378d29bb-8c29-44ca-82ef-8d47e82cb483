import React from "react";
import { Bar<PERSON>hart } from "@mui/x-charts/BarChart";

const BarChartWidget = ({ chartData }) => {
  const emptySeries = {
    xAxis: [{ min: 0, max: 50, axisLine: false, tickSize: 0 }],
    yAxis: [
      {
        scaleType: "band",
        data: ["Work Flow-Stage"],
        tickLabelStyle: { fill: "#bbb", fontSize: 12 },
        axisLine: false,
        tickSize: 0,
      },
    ],
    series: [],
    margin: { left: 225, right: 20, top: 20, bottom: 30 },
    slotProps: {
      legend: { hidden: true },
    },
    axisHighlight: {
      x: "none",
      y: "none",
    },
  };

  const hoursByWorkflowSeries = {
    xAxis: [
      {
        scaleType: "linear",
        valueFormatter: (value) => `${value}`,
        tickLabelStyle: { fill: "#bbb", fontSize: 12 },
        axisLine: false,
        tickSize: 0,
      },
    ],
    yAxis: [
      {
        scaleType: "band",
        dataKey: "stage",
        categoryGapRatio: 0.5,
        barGapRatio: 0.3,
        tickLabelStyle: { fill: "#fff", fontSize: 12 },
        axisLine: false,
        tickSize: 0,
      },
    ],
    dataset: chartData,
    series: [
      {
        dataKey: "unallocated",
        label: "Unallocated Time (h)",
        stack: "total",
        color: "#3498db",
        barRadius: 8,
      },
      {
        dataKey: "work",
        label: "Allocated Time (h)",
        stack: "total",
        color: "#2ecc71",
        barRadius: 8,
      },
    ],
    margin: { left: 225, right: 20, top: 20, bottom: 30 },
  };

  return (
    <BarChart
      xAxis={[
        {
          scaleType: "linear",
          tickLabelStyle: { fill: "#bbb", fontSize: 12 },
          tickSize: 0,
        },
      ]}
      yAxis={[
        {
          scaleType: "band",
          dataKey: "stage",
          tickSize: 0,
        },
      ]}
      dataset={chartData}
      series={[
        {
          dataKey: "unallocated",
          label: "Unallocated Time (h)",
          stack: "total",
          color: "#3498db",
          barRadius: 8,
        },
        {
          dataKey: "allocated",
          label: "Allocated Time (h)",
          stack: "total",
          color: "#2ecc71",
          barRadius: 8,
        },
      ]}
      layout="horizontal"
      height={chartData.length > 15 ? chartData.length * 20 : 300}
      margin={{ left: 225, right: 20, top: 20, bottom: 30 }}
      slotProps={{
        legend: { hidden: true },
      }}
      {...(chartData?.length > 0 ? hoursByWorkflowSeries : emptySeries)}
    />
  );
};

export default BarChartWidget;
