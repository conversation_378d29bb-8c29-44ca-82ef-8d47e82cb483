import configureMockStore from "redux-mock-store";
import axios from "axios";
import <PERSON>ck<PERSON><PERSON>pter from "axios-mock-adapter";
import thunk from "redux-thunk";

import {
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  receiveLeftMenuWidth,
  receiveRightMenuWidth,
  handleFetchWorkableJobs,
  handleFetchWorkablePackages,
  handleFetchWorkableDrawings,
  handleFetchWorkableStages,
  handleFetchDrawingById,
  handleReceiveRightMenuWidth,
  handleReceiveLeftMenuWidth,
  handleFetchWorkableTableFilters,
  handleClearWorkableTableFilters,
  handleSaveWorkableTableFilters,
} from "./myWorkActions";

describe("MyWork", () => {
  const testError = (type) => ({
    error: { status: 404, message: `No ${type} found.` },
  });

  describe("action handlers should perform the necessary functions", () => {
    let store;
    let httpMock;

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    const testJobs = [
      {
        id: 1,
        job_name: "test job 1",
        job_number: "1",
        package_count: 1,
      },
      {
        id: 2,
        job_name: "test job 2",
        job_number: "2",
        package_count: 1,
      },
    ];
    const testPackages = [
      {
        id: 1,
        job_id: 1,
        job_name: "test job 1",
        job_number: "1",
        package_name: "test package 1",
        number: "1",
        drawing_count: 1,
      },
      {
        id: 2,
        job_id: 2,
        job_name: "test job 2",
        job_number: "2",
        package_name: "test package 2",
        number: "2",
        drawing_count: 1,
      },
    ];
    const testDrawings = [
      {
        id: 1,
        job_id: 1,
        job_number: "1",
        job_title: "test job 1",
        name: "test drawing 1",
        package_name: "test package 1",
        package_id: 1,
        package_number: "1",
      },
      {
        id: 2,
        job_id: 2,
        job_number: "2",
        job_title: "test job 2",
        name: "test drawing 2",
        package_name: "test package 2",
        package_id: 2,
        package_number: "2",
      },
    ];
    const testStages = [
      {
        id: 1,
        name: "test stage 1",
        stage_work_level_id: 1,
        stage_code_id: 1,
      },
      {
        id: 2,
        name: "test stage 2",
        stage_work_level_id: 2,
        stage_code_id: 2,
      },
    ];

    it("handleFetchWorkableJobs fetches all workable jobs assigned to user", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/jobs/workable`)
        .replyOnce(200, testJobs)
        .onGet(`${process.env.REACT_APP_API}/jobs/workable?stage_ids=4`)
        .replyOnce(404, testError("jobs"));

      await store.dispatch(handleFetchWorkableJobs()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_JOBS"),
          receiveSucceeded("WORKABLE_JOBS", testJobs),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testJobs);

        store.clearActions();
      });

      return store.dispatch(handleFetchWorkableJobs([4])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_JOBS"),
          receiveFailed("WORKABLE_JOBS", testError("jobs")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchWorkablePackages fetches all workable packages assigned to user", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/packages/workable`)
        .replyOnce(200, testPackages)
        .onGet(`${process.env.REACT_APP_API}/packages/workable?job_ids=1`)
        .replyOnce(200, testPackages[0])
        .onGet(`${process.env.REACT_APP_API}/packages/workable`)
        .replyOnce(404, testError("packages"));

      await store.dispatch(handleFetchWorkablePackages()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_PACKAGES"),
          receiveSucceeded("WORKABLE_PACKAGES", testPackages),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testPackages);

        store.clearActions();
      });

      return store.dispatch(handleFetchWorkablePackages()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_PACKAGES"),
          receiveFailed("WORKABLE_PACKAGES", testError("packages")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchWorkablePackages fetches all workable packages assigned to user from a specific job", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/packages/workable?job_ids=1`)
        .replyOnce(200, testPackages[0]);

      await store.dispatch(handleFetchWorkablePackages([1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_PACKAGES"),
          receiveSucceeded("WORKABLE_PACKAGES", testPackages[0]),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testPackages[0]);

        store.clearActions();
      });
    });

    it("handleFetchWorkableDrawings fetches all workable drawings assigned to user", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/drawings/workable`)
        .replyOnce(200, testDrawings)
        .onGet(`${process.env.REACT_APP_API}/drawings/workable`)
        .replyOnce(404, testError("drawings"));

      await store.dispatch(handleFetchWorkableDrawings()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_DRAWINGS"),
          receiveSucceeded("WORKABLE_DRAWINGS", testDrawings),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testDrawings);

        store.clearActions();
      });

      return store.dispatch(handleFetchWorkableDrawings()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_DRAWINGS"),
          receiveFailed("WORKABLE_DRAWINGS", testError("drawings")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchWorkableDrawings fetches all workable drawings assigned to user based of a specific job", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/drawings/workable?job_ids=1`)
        .replyOnce(200, testDrawings[0]);

      await store.dispatch(handleFetchWorkableDrawings([1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_DRAWINGS"),
          receiveSucceeded("WORKABLE_DRAWINGS", testDrawings[0]),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testDrawings[0]);

        store.clearActions();
      });
    });

    it("handleFetchWorkableDrawings fetches all workable drawings assigned to user based of a specific package", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/drawings/workable?package_ids=2`)
        .replyOnce(200, testDrawings[1]);

      await store.dispatch(handleFetchWorkableDrawings(null, [2])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_DRAWINGS"),
          receiveSucceeded("WORKABLE_DRAWINGS", testDrawings[1]),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testDrawings[1]);

        store.clearActions();
      });
    });

    it("handleFetchWorkableStages returns all workable stages assigned to user", async () => {
      httpMock
        .onPost(`${process.env.REACT_APP_API}/work-stages/workable`)
        .replyOnce(200, testStages)
        .onPost(`${process.env.REACT_APP_API}/work-stages/workable`)
        .replyOnce(404, testError("stages"));

      await store.dispatch(handleFetchWorkableStages()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_STAGES"),
          receiveSucceeded("WORKABLE_STAGES", testStages),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testStages);

        store.clearActions();
      });
    });

    it("handleFetchWorkableStages returns all workable stages assigned to user based off specific job", async () => {
      httpMock
        .onPost(`${process.env.REACT_APP_API}/work-stages/workable`)
        .replyOnce(200, testStages)
        .onPost(`${process.env.REACT_APP_API}/work-stages/workable`)
        .replyOnce(404, testError("stages"));

      await store.dispatch(handleFetchWorkableStages([1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_STAGES"),
          receiveSucceeded("WORKABLE_STAGES", testStages),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testStages);

        store.clearActions();
      });

      return store.dispatch(handleFetchWorkableStages()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_STAGES"),
          receiveFailed("WORKABLE_STAGES", testError("stages")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchWorkableStages returns all workable stages assigned to user based off specific package", async () => {
      httpMock
        .onPost(`${process.env.REACT_APP_API}/work-stages/workable`)
        .replyOnce(200, testStages[1]);

      await store.dispatch(handleFetchWorkableStages(null, [2])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_STAGES"),
          receiveSucceeded("WORKABLE_STAGES", testStages[1]),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testStages[1]);

        store.clearActions();
      });
    });

    it("handleFetchWorkableStages returns all workable stages assigned to user based off specific drawings", async () => {
      httpMock
        .onPost(`${process.env.REACT_APP_API}/work-stages/workable`)
        .replyOnce(200, testStages[1]);

      await store
        .dispatch(handleFetchWorkableStages(null, null, [1]))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("WORKABLE_STAGES"),
            receiveSucceeded("WORKABLE_STAGES", testStages[1]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testStages[1]);

          store.clearActions();
        });
    });

    it("handleFetchDrawingById returns the correct drawing based off ID", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/drawings/1`)
        .replyOnce(200, [testDrawings[0]])
        .onGet(`${process.env.REACT_APP_API}/drawings/10`)
        .replyOnce(404, testError("drawings"));

      await store.dispatch(handleFetchDrawingById(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("DRAWING_BY_ID"),
          receiveSucceeded("DRAWING_BY_ID"),
        ];

        expect(receivedActions).toEqual(expectedActions);

        store.clearActions();
      });

      return store.dispatch(handleFetchDrawingById(10)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("DRAWING_BY_ID"),
          receiveFailed("DRAWING_BY_ID", testError("drawings")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleReceiveLeftMenuWidth returns correct menu width", () => {
      store.dispatch(handleReceiveLeftMenuWidth(500));

      const receivedActions = store.getActions();
      const expectedActions = [receiveLeftMenuWidth(500)];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
    it("handleReceiveRightMenuWidth returns correct menu width", () => {
      store.dispatch(handleReceiveRightMenuWidth(500));

      const receivedActions = store.getActions();
      const expectedActions = [receiveRightMenuWidth(500)];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
    it("should fetch the user's saved table filters", async () => {
      const testReturn = [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }];

      httpMock
        .onGet("table-filters?area=myWork&viewing_all=1&grouping=1&stage_id=0")
        .replyOnce(200, testReturn);

      let response = await await store.dispatch(
        handleFetchWorkableTableFilters(1, 1)
      );

      const receivedActions = store.getActions();
      const expectedActions = [
        receiveStarted("MY_WORK_TABLE_FILTERS"),
        receiveSucceeded("MY_WORK_TABLE_FILTERS", testReturn),
      ];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });

    it("should clear the my work table filters in redux", () => {
      store.dispatch(handleClearWorkableTableFilters);

      let receivedActions = store.getActions(),
        expectedActions = [receiveSucceeded("MY_WORK_TABLE_FILTERS", null)];

      expect(receivedActions).toEqual(expectedActions);
    });
  });

  describe("handleSaveWorkableTableFilters", () => {
    let store, httpMock;
    const testReturn = [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }];

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({
        profileData: {
          systemSettings: {
            time_display: "yyyy-MM-DD",
            timezone: "America/Chicago",
          },
          username: "<EMAIL>",
          userId: 1,
        },
      });
    });

    it("should save the user's workable table filters", async () => {
      httpMock.onPost("table-filters").replyOnce(200, testReturn);

      let response = await store.dispatch(
        handleSaveWorkableTableFilters(testReturn.slice(2), 1)
      );

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveSucceeded("MY_WORK_TABLE_FILTERS", testReturn),
        ];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);
      expect(httpMock.history.post.length).toBe(1);
      expect(httpMock.history.post[0].data).toEqual(
        JSON.stringify({
          area: "myWork",
          viewing_all: 1,
          grouping: 1,
          data: [{ id: 3 }, { id: 4 }],
        })
      );

      store.clearActions();
    });
  });
});
