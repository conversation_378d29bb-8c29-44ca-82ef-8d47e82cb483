@import "../../styles/colors.scss";
@import "../../styles/sizes.scss";

.left-menu-wrapper,
.right-menu-wrapper {
  position: absolute;
  top: 0;
  height: calc(100vh - 100px);
  max-height: calc(100vh - 100px);
  z-index: 1;
  box-sizing: border-box;

  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;

  @supports (-webkit-touch-callout: none) {
    /* CSS specific to iOS devices */
    height: calc(100vh - #{$headerFooter} - #{$iosAddressBar});
  }
}

.left-menu-wrapper {
  left: 0;
}

.right-menu-wrapper {
  right: 0;
}

.draggable-icon {
  position: absolute;
  top: calc(50% - 25px);
  background-color: #3d444a;
  border-radius: 5px;
  height: 50px;
  width: 6px;
  z-index: 19;
  cursor: pointer;
  box-shadow: 1px 1px 2px #222;
}
