// NPM COMPONENT IMPORTS
import React, { useState, useEffect, useRef, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";

// STYLES IMPORTS
import "./stylesMenuWrapper.scss";

// REDUX IMPORTS
import {
  handleReceiveLeftMenuWidth,
  handleReceiveRightMenuWidth,
} from "../myWorkActions";

const MenuWrapper = ({ children, type, pdfInstance = null }) => {
  const [isDragging, setDragging] = useState(false);
  const [screenWidth, setScreenWidth] = useState(null);
  const [dragIconPosition, setDragIconPosition] = useState({});
  const [isOpen, toggleOpen] = useState(true);
  const navExpandedRef = useRef(null);
  const screenWidthRef = useRef(null);
  const rightMenuWidthRef = useRef(null);

  const { navExpanded } = useSelector((state) => state.generalData);
  const { leftMenuWidth, rightMenuWidth } = useSelector(
    (state) => state.myWorkData
  );

  const handleWindowResize = () => {
    // calc current available window space
    let currentWidth =
      navExpandedRef && navExpandedRef.current
        ? window.window.innerWidth - 55 - 200
        : window.window.innerWidth - 55;
    // if we have less space than before, check the panels still fit
    if (
      !screenWidth ||
      !screenWidth.current ||
      currentWidth < screenWidth.current
    ) {
      if (currentWidth > 1000) {
        // resize them to be side by side to avoid overlap from the shrink
        dispatch(handleReceiveLeftMenuWidth(500));
        dispatch(handleReceiveRightMenuWidth(500));
      } else if (currentWidth > 580) {
        // if no space for both expanded, just keep the right one expanded
        dispatch(handleReceiveLeftMenuWidth(20));
        dispatch(handleReceiveRightMenuWidth(500));
      } else {
        // if no space for either, collapse both
        dispatch(handleReceiveLeftMenuWidth(20));
        dispatch(handleReceiveRightMenuWidth(20));
      }
    }
    navExpandedRef.current
      ? setScreenWidth(window.window.innerWidth - 55 - 200)
      : setScreenWidth(window.window.innerWidth - 55);
  };

  useEffect(() => {
    window.addEventListener("resize", handleWindowResize);

    return () => {
      window.removeEventListener("resize", handleWindowResize);
    };
  }, []);

  useEffect(() => {
    rightMenuWidthRef.current = rightMenuWidth;
  }, [rightMenuWidth]);

  useEffect(() => {
    navExpanded
      ? setScreenWidth(window.window.innerWidth - 55 - 200)
      : setScreenWidth(window.window.innerWidth - 55);
    navExpandedRef.current = navExpanded;
    handleWindowResize();
  }, [navExpanded]);

  useEffect(() => {
    screenWidthRef.current = screenWidth;
    if (!screenWidth) return;
    type === "LEFT"
      ? setDragIconPosition({ right: "2px" })
      : setDragIconPosition({ left: "2px" });
  }, [screenWidth, type]);

  useEffect(() => {
    if (type === "LEFT") {
      leftMenuWidth > 20 ? toggleOpen(true) : toggleOpen(false);
    } else {
      rightMenuWidth > 20 ? toggleOpen(true) : toggleOpen(false);
    }
  }, [leftMenuWidth, rightMenuWidth, type]);

  const dispatch = useDispatch();

  useEffect(() => {
    if (!isDragging) return;
    if (leftMenuWidth + rightMenuWidth > screenWidth) {
      const resizeAmount = leftMenuWidth + rightMenuWidth - screenWidth;
      type === "LEFT"
        ? dispatch(handleReceiveRightMenuWidth(rightMenuWidth - resizeAmount))
        : dispatch(handleReceiveLeftMenuWidth(leftMenuWidth - resizeAmount));
    }
  }, [isDragging, leftMenuWidth, rightMenuWidth, dispatch, screenWidth, type]);

  // MOUSE EVENT handling
  // need to grab pdf viewer by class because pdfInstance may still be null
  const onMouseDown = () => {
    setDragging(true);
    const modelInstance = document.getElementById("work-forge-viewer-iframe");
    const pdfViewerInstance = document.getElementsByClassName(
      "pdf-viewer-wrapper"
    );
    document.addEventListener("mousemove", onMouseMove);
    document.addEventListener("mouseup", onMouseUp);

    // this prevents iframe interactions when dragging menu
    if (modelInstance) {
      modelInstance.style.pointerEvents = "none";

      modelInstance.addEventListener("mousemove", onMouseMove);
      modelInstance.addEventListener("mouseup", onMouseUp);
    } else if (pdfViewerInstance?.length) {
      pdfViewerInstance[0].style.pointerEvents = "none";

      pdfViewerInstance[0].addEventListener("mousemove", onMouseMove);
      pdfViewerInstance[0].addEventListener("mouseup", onMouseUp);
    }
  };

  const onMouseUp = () => {
    setDragging(false);
    const modelInstance = document.getElementById("work-forge-viewer-iframe");
    const pdfViewerInstance = document.getElementsByClassName(
      "pdf-viewer-wrapper"
    );
    document.removeEventListener("mousemove", onMouseMove);
    document.removeEventListener("mouseup", onMouseUp);

    // this re-enables iframe interactions after dragging menu
    if (modelInstance) {
      modelInstance.style.pointerEvents = "auto";

      modelInstance.removeEventListener("mousemove", onMouseMove);
      modelInstance.removeEventListener("mouseup", onMouseUp);
    } else if (pdfViewerInstance) {
      pdfViewerInstance[0].style.pointerEvents = "auto";

      pdfViewerInstance[0].removeEventListener("mousemove", onMouseMove);
      pdfViewerInstance[0].removeEventListener("mouseup", onMouseUp);
    }
  };

  const onMouseMove = useCallback(
    (e) => {
      e.preventDefault();
      let mousePosition = navExpanded ? e.x - 255 : e.x - 55;

      const atLeftEdge = mousePosition < 20;
      const atRightEdge = mousePosition > screenWidth - 20;
      if (type === "LEFT") {
        if (atLeftEdge) mousePosition = 20;
        else if (atRightEdge) mousePosition = screenWidth - 20;
      } else {
        if (atLeftEdge) mousePosition = 20;
        else if (atRightEdge) mousePosition = screenWidth - 20;
      }

      type === "LEFT"
        ? dispatch(handleReceiveLeftMenuWidth(mousePosition))
        : dispatch(handleReceiveRightMenuWidth(screenWidth - mousePosition));
    },
    [dispatch, type, screenWidth]
  );

  const handleDragIconClick = () => {
    if (isOpen) {
      type === "LEFT"
        ? dispatch(handleReceiveLeftMenuWidth(20))
        : dispatch(handleReceiveRightMenuWidth(20));
      toggleOpen(false);
    } else {
      // if opening a panel
      let currentWidth =
        navExpandedRef && navExpandedRef.current
          ? window.window.innerWidth - 55 - 200
          : window.window.innerWidth - 55;
      // if no room to fully expand tapped panel, expand such that in a 500px window
      // with an expanded sidebar, there is room for the other collapsed panel, 245 - 80
      let paneWidth = currentWidth >= 580 ? 500 : 165;
      if (type === "LEFT") {
        // if other side is expanded but no room for both expanded, collapse the other side
        if (currentWidth <= 1000 && rightMenuWidth >= 500) {
          dispatch(handleReceiveRightMenuWidth(20));
        }
        dispatch(handleReceiveLeftMenuWidth(paneWidth));
      } else {
        if (currentWidth <= 1000 && leftMenuWidth >= 500) {
          dispatch(handleReceiveLeftMenuWidth(20));
        }
        dispatch(handleReceiveRightMenuWidth(paneWidth));
      }
      toggleOpen(true);
    }
  };

  return (
    <div
      style={{ width: `${type === "LEFT" ? leftMenuWidth : rightMenuWidth}px` }}
      className={`${type.toLowerCase()}-menu-wrapper`}
    >
      {children}
      <div
        onClick={handleDragIconClick}
        style={dragIconPosition}
        onMouseDown={onMouseDown}
        className="draggable-icon"
      />
    </div>
  );
};

export default MenuWrapper;
