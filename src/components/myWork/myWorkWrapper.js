// REACT-REDUX
import React from "react";
import { useSelector } from "react-redux";

// COMPONENT IMPORTS
import MyWork from "./MyWork";
import MyWorkCustomColumns from "./MyWorkCustomColumns";

const MyWorkWrapper = () => {
  const { features } = useSelector((state) => state.profileData);
  const CUSTOM_COLUMNS_FEATURE_ID = 53;
  const hasCustomColumnFeature = features?.includes(CUSTOM_COLUMNS_FEATURE_ID);

  return hasCustomColumnFeature ? <MyWorkCustomColumns /> : <MyWork />;
};

export default MyWorkWrapper;
