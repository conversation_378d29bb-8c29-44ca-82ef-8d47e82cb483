const initialState = {
  leftMenuWidth: 500,
  rightMenuWidth: 500,
  isLoading: false,
  error: null,
  workableJobs: [],
  workablePackages: [],
  workableDrawings: [],
  workableStages: [],
  assignedDrawingsColumnState: null,
  itemsMenuColumnState: null,
  myWorkTableFilters: null,
  myWorkViewingAll: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_LEFT_MENU_WIDTH":
      return { ...state, leftMenuWidth: payload };
    case "RECEIVE_RIGHT_MENU_WIDTH":
      return { ...state, rightMenuWidth: payload };
    case "RECEIVE_WORKABLE_JOBS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_WORKABLE_JOBS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, workableJobs: payload };
    case "RECEIVE_WORKABLE_JOBS_FAILED":
      return { ...state, isLoading: false, workableJobs: [], error: payload };
    case "RECEIVE_WORKABLE_PACKAGES_STARTED":
      return { ...state, isLoading: true, workablePackages: [], error: null };
    case "RECEIVE_WORKABLE_PACKAGES_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        workablePackages: payload,
      };
    case "RECEIVE_WORKABLE_PACKAGES_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_WORKABLE_DRAWINGS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_WORKABLE_DRAWINGS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        workableDrawings: payload,
      };
    case "RECEIVE_WORKABLE_DRAWINGS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        workableDrawings: [],
      };
    case "RECEIVE_WORKABLE_STAGES_STARTED":
      return { ...state, isLoading: true, workableStages: [], error: null };
    case "RECEIVE_WORKABLE_STAGES_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        workableStages: payload,
      };
    case "RECEIVE_WORKABLE_STAGES_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_ASSIGNED_DRAWINGS_COLUMN_STATE_STARTED":
      return { ...state, assignedDrawingsColumnState: payload };
    case "RECEIVE_ASSIGNED_DRAWINGS_COLUMN_STATE_SUCCEEDED":
      return { ...state, assignedDrawingsColumnState: payload };
    case "RECEIVE_ASSIGNED_DRAWINGS_COLUMN_STATE_FAILED":
      return { ...state, assignedDrawingsColumnState: [] };
    case "RECEIVE_MY_WORK_COLUMN_STATE_STARTED":
      return { ...state, itemsMenuColumnState: null };
    case "RECEIVE_MY_WORK_ITEMS_COLUMN_STATE_SUCCEEDED":
      return { ...state, itemsMenuColumnState: payload };
    case "RECEIVE_MY_WORK_ITEMS_COLUMN_STATE_FAILED":
      return { ...state, itemsMenuColumnState: [] };
    case "RECEIVE_DRAWING_BY_ID_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_DRAWING_BY_ID_SUCCEEDED":
      return { ...state, isLoading: false };
    case "RECEIVE_DRAWING_BY_ID_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_MY_WORK_TABLE_FILTERS_STARTED":
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case "RECEIVE_MY_WORK_TABLE_FILTERS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        myWorkTableFilters: payload,
      };
    case "RECEIVE_MY_WORK_TABLE_FILTERS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        myWorkTableFilters: [],
      };
    case "RECEIVE_MY_WORK_VIEWING_ALL_SUCCEEDED":
      return { ...state, isLoading: false, myWorkViewingAll: payload };
    default:
      return state;
  }
}
