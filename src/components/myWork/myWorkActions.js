import {
  fetchWorkableJobs,
  fetchWorkablePackages,
  fetchWorkableDrawings,
  fetchWorkableStages,
  fetchDrawingById,
  fetchTableFilters,
  saveTableFilters,
} from "../../_services";
import store from "../../redux/store";

export const receiveLeftMenuWidth = (width) => ({
  type: "RECEIVE_LEFT_MENU_WIDTH",
  payload: width,
});
export const receiveRightMenuWidth = (width) => ({
  type: "RECEIVE_RIGHT_MENU_WIDTH",
  payload: width,
});
export const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const handleReceiveLeftMenuWidth = (width) => (dispatch) =>
  dispatch(receiveLeftMenuWidth(width));

export const handleReceiveRightMenuWidth = (width) => (dispatch) =>
  dispatch(receiveRightMenuWidth(width));

export const handleFetchWorkablePackages = (jobIds, stageIds) => (dispatch) => {
  const type = "WORKABLE_PACKAGES";
  dispatch(receiveStarted(type));
  return fetchWorkablePackages(jobIds, stageIds).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchWorkableDrawings = (jobIds, packageIds, stageIds) => (
  dispatch
) => {
  const type = "WORKABLE_DRAWINGS";
  dispatch(receiveStarted(type));
  return fetchWorkableDrawings(jobIds, packageIds, stageIds).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchDrawingById = (id) => (dispatch) => {
  const type = "DRAWING_BY_ID";
  dispatch(receiveStarted(type));
  return fetchDrawingById(id).then((res) => {
    if (res.error) return dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type));
    return res;
  });
};

export const handleFetchWorkableTableFilters = (
  viewingAll,
  groupingType,
  stageId
) => (dispatch) => {
  const type = "MY_WORK_TABLE_FILTERS";
  const area = "myWork";

  dispatch(receiveStarted(type));
  return fetchTableFilters(area, viewingAll, groupingType, stageId).then(
    (res) => {
      if (res.error) dispatch(receiveFailed(type, res));
      else dispatch(receiveSucceeded(type, res));

      return res;
    }
  );
};

export const handleSaveWorkableTableFilters = (
  filters,
  viewingAll,
  grouping,
  stage_id,
  drawing_id,
  selectAll
) => (dispatch) => {
  const type = "MY_WORK_TABLE_FILTERS";
  const area = "myWork";

  return saveTableFilters(
    area,
    filters,
    viewingAll,
    grouping,
    stage_id,
    drawing_id,
    selectAll
  ).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleClearWorkableTableFilters = (dispatch) => {
  dispatch(receiveSucceeded("MY_WORK_TABLE_FILTERS", null));
};

// The following is to help suppress duplicate requests to handleFetchWorkableStages
let hasWorkableStagesBeenCalled = false;
let lastParams = {
  jobIds: undefined,
  packageIds: undefined,
  drawingIds: undefined,
};

// Helper function for deep equality check on arrays
const areArraysEqual = (arr1, arr2) => {
  if (arr1 === arr2) return true;
  if (!Array.isArray(arr1) || !Array.isArray(arr2)) return false;
  if (arr1.length !== arr2.length) return false;
  // Create sorted copies for comparison
  const sorted1 = [...arr1].sort();
  const sorted2 = [...arr2].sort();
  for (let i = 0; i < sorted1.length; i++) {
    if (sorted1[i] !== sorted2[i]) return false;
  }
  return true;
};

// Helper function to compare all three params
const areParamsEqual = (a, b) => {
  return (
    areArraysEqual(a.jobIds, b.jobIds) &&
    areArraysEqual(a.packageIds, b.packageIds) &&
    areArraysEqual(a.drawingIds, b.drawingIds)
  );
};

export const handleFetchWorkableStages = (jobIds, packageIds, drawingIds) => (
  dispatch
) => {
  const currentParams = { jobIds, packageIds, drawingIds };

  if (
    hasWorkableStagesBeenCalled &&
    areParamsEqual(currentParams, lastParams)
  ) {
    const currentStages = store.getState().myWorkData.workableStages;
    return Promise.resolve(currentStages);
  }
  hasWorkableStagesBeenCalled = true;
  lastParams = currentParams;

  const type = "WORKABLE_STAGES";
  dispatch(receiveStarted(type));
  return fetchWorkableStages(jobIds, packageIds, drawingIds).then((res) => {
    if (res.error) return dispatch(receiveFailed(type, res));
    dispatch(receiveSucceeded(type, res));
    return res;
  });
};

// The following is to help suppress duplicate requests to handleFetchWorkableJobs
let lastStageIds = undefined;
let hasWorkableJobsBeenCalled = false;

const areStageIdsEqual = (a, b) => {
  if (a === b) return true;
  if (!a || !b) return false;
  if (Array.isArray(a) && Array.isArray(b)) {
    return a.length === b.length && a[0] === b[0];
  }
  return false;
};

export const handleFetchWorkableJobs = (stageIds) => (dispatch) => {
  // if stageIds are equal to the last request, just return the current workableJobs state
  if (hasWorkableJobsBeenCalled && areStageIdsEqual(stageIds, lastStageIds)) {
    const currentJobs = store.getState().myWorkData.workableJobs;
    return Promise.resolve(currentJobs);
  }
  lastStageIds = stageIds;
  hasWorkableJobsBeenCalled = true;

  const type = "WORKABLE_JOBS";
  dispatch(receiveStarted(type));
  return fetchWorkableJobs(stageIds).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));
    return res;
  });
};
