import React, { useState, useEffect, useRef, useMemo } from "react";
import { HiOutlineSearch } from "react-icons/hi";
import { FaPen } from "react-icons/fa";
import { BsFillGrid1X2Fill } from "react-icons/bs";
import { BiRefresh } from "react-icons/bi";
import { logger } from "./../../../utils/_dataDogUtils";

// REDUX IMPORTS
import {
  handleFetchWorkableJobs,
  handleFetchWorkablePackages,
} from "../myWorkActions";
import { useDispatch, useSelector } from "react-redux";
// COMPONENT IMPORTS
import InlineFilter from "../../reusable/inlineFilter/InlineFilter";
import DownloadMAJsButton from "../../reusable/downloadMAJsButton/DownloadMAJsButton";
import AgTable from "../../reusable/agTable/AgTable";
import InfiniteScrollTable from "../../reusable/infiniteScrollTable/InfiniteScrollTable";

// CONSTANT IMPORTS
import EditingTable from "./editingTable/EditingTable";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../hooks/useOutsideClick";
import usePrevious from "../../../hooks/usePrevious";

// STYLES IMPORTS
import "./stylesAssignedDrawings.scss";

const AssignedDrawings = ({
  setSelectedDrawingId,
  setSelectedDrawingIds,
  selectedDrawingIds,
  selectedDrawings,
  selectedJobs,
  setSelectedJobs,
  selectedPackages,
  setSelectedPackages,
  setSelectedStages,
  selectedStage,
  gridOptionsApi,
  gridOptions,
  permissionLockedColumns,
  isEditing,
  toggleEditing,
  displayedDrawings,
  refreshDrawingsStages,
  searchInput,
  setSearchInput,
  tableCount,
  showTable,
  groupingType,
  tableViewSettingsLoaded,
  setJobsSelectAll,
  setPackagesSelectAll,
  jobsFilterSet,
  packagesFilterSet,
  localStoragePopulated,
  isViewingAllItems,
  selectedDrawingsLoaded,
  allColumnsHidden,
  savedStageFilter,
  stageFilterSet,
  hasCustomColumnFeature,
}) => {
  const { on } = useSelector((state) => state.nestingData);
  const [gridEditOptionsApi, setGridEditOptionsApi] = useState(null);
  const [isToolbarVisible, toggleToolbar] = useState(false);
  const [showEditColumnPicker, toggleEditColumnPicker] = useState(false);
  const [showJobContainerPopup, toggleJobContainerPopup] = useState(false);
  const [filteredJobContainer, setFilteredJobContainer] = useState([]);

  const dispatch = useDispatch();
  const editPopupRef = useRef(null);
  const searchInputRef = useRef();

  useOutsideClick(editPopupRef, () => toggleEditColumnPicker(false));

  const {
    leftMenuWidth,
    workableJobs,
    workablePackages,
    isLoading,
  } = useSelector((state) => state.myWorkData);
  const { userId, userInfo } = useSelector((state) => state.profileData);

  const prevSelectedJobs = usePrevious(selectedJobs);
  const prevSelectedPackages = usePrevious(selectedPackages);
  const prevSelectedStage = usePrevious(selectedStage);

  useEffect(() => {
    if (jobsFilterSet) {
      const stageToUse =
        savedStageFilter && !stageFilterSet
          ? [savedStageFilter]
          : selectedStage
          ? [selectedStage.id]
          : null;
      // check if we just de-selected the only job in filter - clear out selectedPackages
      if (
        prevSelectedJobs &&
        prevSelectedJobs.length === 1 &&
        selectedJobs.length === 0
      ) {
        setSelectedPackages([]);
        dispatch(handleFetchWorkablePackages(null, stageToUse));
      } else {
        dispatch(
          handleFetchWorkablePackages(
            selectedJobs.length > 0 ? selectedJobs.map((job) => job.id) : null,
            stageToUse
          )
        ).then((res) => {
          if (!res.error && selectedPackages.length) {
            setSelectedPackages(
              selectedPackages.filter((p) => res.find((o) => o.id === p.id))
            );
          }
        });
      }
    }
  }, [selectedJobs, jobsFilterSet]);

  // fetch drawings on job / package changes
  useEffect(() => {
    if (!jobsFilterSet || !packagesFilterSet) return;
    // empty selected drawings
    if (
      !selectedJobs &&
      !selectedJobs.length &&
      !selectedPackages &&
      !selectedPackages.length &&
      !selectedStage
    ) {
      toggleEditing(null);
    }
    const selectedPackageJobIds = selectedPackages.map((p) => p.job_id);
    const selectedJobIds = selectedJobs.map((j) => j.id);
    let updatedSelectedPackageIds;
    // check if we need to filter out selected packages
    if (
      selectedJobIds.length &&
      !selectedPackageJobIds.every((id) => selectedJobIds.includes(id))
    ) {
      const f_packages = selectedPackages.filter((p) =>
        selectedJobIds.includes(p.job_id)
      );
      updatedSelectedPackageIds = f_packages.map((p) => p.id);
    } else updatedSelectedPackageIds = selectedPackages.map((p) => p.id);

    if (
      !tableViewSettingsLoaded ||
      !localStoragePopulated ||
      isViewingAllItems === null ||
      (JSON.stringify((selectedPackages || []).map((p) => p.id).sort()) ===
        JSON.stringify((prevSelectedPackages || []).map((p) => p.id).sort()) &&
        JSON.stringify((selectedJobs || []).map((j) => j.id).sort()) ===
          JSON.stringify((prevSelectedJobs || []).map((j) => j.id).sort()) &&
        (selectedStage || {}).id === (prevSelectedStage || {}).id)
    )
      return;

    const stageToSend =
      savedStageFilter && !stageFilterSet
        ? [savedStageFilter]
        : selectedStage
        ? [selectedStage.id]
        : null;

    refreshDrawingsStages(
      selectedJobs.length ? selectedJobs.map((j) => j.id) : null,
      updatedSelectedPackageIds || null,
      stageToSend,
      isViewingAllItems ? [] : selectedDrawingIds,
      groupingType
    ).then((res) => {
      if (!Array.isArray(res) || !res.length) return;
      if (!res[0].error && selectedDrawingIds.length) {
        logger.info("My Work Filters Changed", {
          user_id: userId,
          username: userInfo.username,
          name: "Drawings Filter",
          selection: selectedDrawingIds.filter((di) => {
            // res[0] contains the drawing
            return res[0].find((o) => {
              return o.id === di;
            });
          }),
        });

        if (!selectedDrawingsLoaded) return;

        setSelectedDrawingIds(
          selectedDrawingIds.filter((di) => {
            // res[0] contains the drawing
            return res[0].find((o) => {
              return o.id === di;
            });
          })
        );
      }
    });
  }, [
    jobsFilterSet,
    packagesFilterSet,
    selectedJobs,
    selectedPackages,
    on,
    selectedStage,
    tableViewSettingsLoaded,
    groupingType,
    localStoragePopulated,
    isViewingAllItems,
    selectedDrawingsLoaded,
  ]);

  useEffect(() => {
    if (!gridOptionsApi || isEditing) return;
    gridOptionsApi.setQuickFilter(searchInput);
  }, [searchInput, gridOptionsApi, isEditing]);
  useEffect(() => {
    if (!gridEditOptionsApi || !isEditing) return;
    gridEditOptionsApi.setQuickFilter(searchInput);
  }, [searchInput, gridEditOptionsApi, isEditing]);

  const refreshWorkable = () => {
    dispatch(
      handleFetchWorkableJobs(selectedStage ? [selectedStage.id] : null)
    ).then((res) => {
      let filteredSelectedJobs = [];
      let filteredSelectedPkgs = [];
      if (!res.error) {
        const refreshedJobIds = res.map((j) => j.id);
        filteredSelectedJobs = selectedJobs.filter((j) =>
          refreshedJobIds.includes(j.id)
        );
        setSelectedJobs(filteredSelectedJobs);
      }

      dispatch(
        handleFetchWorkablePackages(
          filteredSelectedJobs.length &&
            filteredSelectedJobs.map((job) => job.id),
          selectedStage ? [selectedStage.id] : null
        )
      ).then((res) => {
        if (!res.error) {
          const refreshedPackageIds = res.map((p) => p.id);
          filteredSelectedPkgs = selectedPackages.filter((p) =>
            refreshedPackageIds.includes(p.id)
          );
          if (selectedPackages.length) {
            setSelectedPackages(filteredSelectedPkgs);
          }
        }
        tableViewSettingsLoaded && refreshDrawings();
      });
    });
  };

  const selectedFilterIds = useMemo(() => {
    const selectedJobIds =
      selectedJobs && selectedJobs.length && selectedJobs.map((j) => j.id);
    const selectedPackageIds =
      selectedJobs &&
      selectedPackages.length &&
      selectedPackages.map((p) => p.id);
    let selectedStageIds;
    if (selectedStage) selectedStageIds = [selectedStage.id];

    return [
      selectedJobIds || null,
      selectedPackageIds || null,
      selectedStageIds || null,
    ];
  }, [selectedJobs, selectedPackages, selectedStage]);

  const refreshDrawings = () => {
    refreshDrawingsStages(
      ...selectedFilterIds,
      selectedDrawingIds,
      groupingType
    );
  };

  const toggleColumnToolbar = () => {
    if (!gridOptionsApi) return;

    if (isToolbarVisible) {
      gridOptionsApi.closeToolPanel();
      gridOptionsApi.setSideBarVisible(false);
      toggleToolbar(false);
    } else {
      gridOptionsApi.setSideBarVisible(true);
      gridOptionsApi.setSideBarPosition("left");
      gridOptionsApi.openToolPanel("columns");
      toggleToolbar(true);
    }
  };

  const EditColumnPicker = () => {
    const handleClick = (type) => {
      toggleEditColumnPicker(false);
      toggleEditing(type);
    };

    return (
      <div ref={editPopupRef} className="column-picker-wrapper">
        <p>Select the column you want to edit</p>
        <div className="button-wrapper">
          <button onClick={handleContainerEditClick}>Container</button>
          <button onClick={() => handleClick("LAYDOWN_LOCATION")}>
            Laydown Location
          </button>
        </div>
      </div>
    );
  };

  const jobsByPackages = useMemo(() => {
    if (!selectedPackages.length || !workableJobs || !workableJobs.length)
      return [];

    let jobs = [];
    selectedPackages.forEach((p) => {
      let job = workableJobs.find((job) => job.id === p.job_id);
      if (job) jobs.push(job);
    });

    return [...new Set(jobs)];
  }, [selectedPackages, workableJobs]);

  const JobContainerSelector = () => (
    <div className="job-container-selector">
      <p>Select a job for which to edit containers</p>
      <ul>
        {selectedJobs.length > 1
          ? selectedJobs.map((job) => (
              <li
                key={job.id}
                onClick={() => {
                  toggleEditing("CONTAINER");
                  setFilteredJobContainer([job]);
                  toggleJobContainerPopup(false);
                }}
              >
                {job.job_name}
              </li>
            ))
          : jobsByPackages && jobsByPackages.length > 0
          ? jobsByPackages.map((job) => (
              <li
                key={job.id}
                onClick={() => {
                  toggleEditing("CONTAINER");
                  setFilteredJobContainer([job]);
                  toggleJobContainerPopup(false);
                }}
              >
                {job.job_name}
              </li>
            ))
          : workableJobs.map((job) => (
              <li
                key={job.id}
                onClick={() => {
                  toggleEditing("CONTAINER");
                  setFilteredJobContainer([job]);
                  toggleJobContainerPopup(false);
                }}
              >
                {job.job_name}
              </li>
            ))}
      </ul>
      <button onClick={() => toggleJobContainerPopup(false)}>Cancel</button>
    </div>
  );

  const handleContainerEditClick = () => {
    toggleEditColumnPicker(false);
    if (selectedJobs.length === 1) {
      toggleEditing("CONTAINER");
      setFilteredJobContainer(selectedJobs);
    } else if (selectedJobs.length > 1) {
      setFilteredJobContainer(selectedJobs);
      toggleJobContainerPopup(true);
    } else if (jobsByPackages && jobsByPackages.length) {
      if (jobsByPackages.length === 1) {
        toggleEditing("CONTAINER");
        setFilteredJobContainer(jobsByPackages);
      } else {
        setFilteredJobContainer(jobsByPackages);
        toggleJobContainerPopup(true);
      }
    } else {
      setFilteredJobContainer(workableJobs);
      toggleJobContainerPopup(true);
    }
  };

  const handleEditClick = () => {
    if (!selectedJobs.length && !selectedPackages.length && !selectedStage)
      return toggleEditing(null);
    return isEditing
      ? toggleEditing(null)
      : toggleEditColumnPicker(!showEditColumnPicker);
  };

  return (
    <div
      hidden={leftMenuWidth <= 20}
      className={
        leftMenuWidth <= 20
          ? "assigned-drawings-wrapper menu-closed"
          : "assigned-drawings-wrapper"
      }
    >
      <h4>
        <DownloadMAJsButton
          selectedDrawings={selectedDrawings}
          className="my-work-download-majs"
        />{" "}
        Assigned Drawings
      </h4>
      <div className="filters-wrapper">
        {leftMenuWidth > 80 && (
          <>
            <InlineFilter
              nameKey="job_name"
              idKey="job_number"
              type="Jobs"
              isMulti={true}
              list={workableJobs}
              selected={selectedJobs}
              handleParentSelect={(jobs) => {
                logger.info("My Work Filters Changed", {
                  user_id: userId,
                  username: userInfo.username,
                  name: "Jobs Filter",
                  selection: jobs,
                  select_all: 0,
                });
                setSelectedJobs(jobs);
                setSelectedStages([]);
                setSelectedDrawingIds([]);
                setSelectedDrawingId(null);
              }}
              isDisabled={on}
              toggleAllSelections={(bool) => {
                logger.info("My Work Filters Changed", {
                  user_id: userId,
                  username: userInfo.username,
                  name: "Jobs Filter",
                  select_all: bool ? 1 : 0,
                });
                setJobsSelectAll(bool ? 1 : 0);
              }}
            />
            <InlineFilter
              nameKey="package_name"
              idKey="id"
              type="Packages"
              isMulti={true}
              list={workablePackages}
              selected={selectedPackages}
              handleParentSelect={(pkgs) => {
                logger.info("My Work Filters Changed", {
                  user_id: userId,
                  username: userInfo.username,
                  name: "Packages Filter",
                  selection: pkgs,
                  select_all: 0,
                });
                setSelectedPackages(pkgs);
                setSelectedStages([]);
                setSelectedDrawingIds([]);
                setSelectedDrawingId(null);
              }}
              isDisabled={on}
              toggleAllSelections={(bool) => {
                logger.info("My Work Filters Changed", {
                  user_id: userId,
                  username: userInfo.username,
                  name: "Packages Filter",
                  select_all: bool ? 1 : 0,
                });
                setPackagesSelectAll(bool ? 1 : 0);
              }}
            />
          </>
        )}
      </div>
      <div className="grid-action-bar-wrapper">
        <p className="table-count">{`${tableCount || "0"} drawings`}</p>
        <input
          onChange={(e) => setSearchInput(e.target.value)}
          ref={searchInputRef}
          type="text"
        />
        <HiOutlineSearch
          onClick={() => searchInputRef.current.focus()}
          className="search"
        />
        <span className="edit-dropdown-wrapper">
          <FaPen
            onClick={handleEditClick}
            className={isEditing || showEditColumnPicker ? "selected-edit" : ""}
          />
          {showEditColumnPicker && <EditColumnPicker />}
        </span>
        <button
          onClick={toggleColumnToolbar}
          className={`columns-btn ${allColumnsHidden ? "column-warn" : ""}`}
          disabled={isEditing}
        >
          <BsFillGrid1X2Fill className="columns" />
        </button>
        {/* <RiBarcodeLine className="scan" /> */}
        <BiRefresh onClick={refreshWorkable} className="refresh" />
      </div>
      <div className="menu-table-wrapper">
        {isEditing && (
          <EditingTable
            gridEditOptionsApi={gridEditOptionsApi}
            setGridEditOptionsApi={setGridEditOptionsApi}
            toggleEditing={toggleEditing}
            type={isEditing}
            selectedJobs={selectedJobs}
            selectedPackages={selectedPackages}
            drawings={displayedDrawings}
            refreshDrawings={refreshDrawings}
            showJobContainerPopup={showJobContainerPopup}
            toggleJobContainerPopup={toggleJobContainerPopup}
            filteredJobContainer={filteredJobContainer}
            setFilteredJobContainer={setFilteredJobContainer}
            jobsByPackages={jobsByPackages}
            workableJobs={workableJobs}
            columns={permissionLockedColumns}
            selectedStage={selectedStage}
          />
        )}
        {showTable ? (
          hasCustomColumnFeature ? (
            <InfiniteScrollTable gridOptions={gridOptions} />
          ) : (
            <AgTable gridOptions={gridOptions} />
          )
        ) : undefined}
        {!showTable && !isLoading && !isEditing && (
          <div className="no-drawings-message">
            <span>
              <p>Add a job or package filter</p>
            </span>
          </div>
        )}
      </div>
      {showJobContainerPopup && <JobContainerSelector />}
    </div>
  );
};

export default AssignedDrawings;
