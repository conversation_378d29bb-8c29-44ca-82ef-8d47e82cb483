export const jobData = [
  {
    id: 1,
    job_title: "Ante Bibendum Inc.",
    job_number: "1829",
  },
  {
    id: 2,
    job_title: "Dignissim Lacus Corporation",
    job_number: "8178",
  },
  {
    id: 3,
    job_title: "Velit Consulting",
    job_number: "163",
  },
  {
    id: 4,
    job_title: "Adipiscing Mauris Incorporated",
    job_number: "17",
  },
  {
    id: 5,
    job_title: "Duis Corporation",
    job_number: "16771",
  },
  {
    id: 6,
    job_title: "Lorem Ipsum Dolor LLP",
    job_number: "",
  },
  {
    id: 7,
    job_title: "Ante Bibendum Inc.",
    job_number: null,
    target_date: 0,
    cost_time: 0,
  },
  {
    id: 8,
    job_title: "Another Job",
    job_number: 1000,
  },
];

export const packageData = [
  {
    id: 1,
    package_name: "Ante Bibendum Inc.",
    number: "16200828-3059",
  },
  {
    id: 2,
    package_name: "Dignissim Lacus Corporation",
    number: "16890821-8178",
  },
  {
    id: 3,
    package_name: "<PERSON>elit Consulting",
    number: "16340716-0443",
  },
  {
    id: 4,
    package_name: "Adipiscing Mauris Incorporated",
    number: "16221006-8017",
  },
  {
    id: 5,
    package_name: "Duis Corporation",
    number: "16501127-1771",
  },
  {
    id: 6,
    package_name: "Lorem Ipsum Dolor LLP",
    number: "16670111-3406",
  },
  {
    id: 7,
    package_name: "Ante Bibendum Inc.",
    number: null,
  },
];

export const testDrawings = [
  {
    id: 1,
    name: "Drawing 1",
    percent_complete: 80.2,
    package_id: 10,
    package_name: "Package 1",
    package_area: "Madison Square Garden",
    job_title: "Job 1",
    job_number: 1000,
    container_name: "Container 1",
    laydown_location: "Laydown 1",
    priority: 3,
  },
  {
    id: 2,
    name: "Drawing 2",
    percent_complete: 80.2,
    package_id: 10,
    package_name: "Package 1",
    package_area: "Madison Square Garden",
    job_title: "Job 1",
    job_number: 1000,
    container_name: "Container 1",
    laydown_location: "Laydown 1",
    priority: 109,
  },
  {
    id: 3,
    name: "Drawing 3",
    percent_complete: 90.0,
    package_id: 111,
    package_name: "Package 1",
    package_area: "Area",
    job_title: "Job 1",
    job_number: 1000,
    container_name: "Container 1",
    laydown_location: "Laydown 1",
    priority: 100,
  },
  {
    id: 4,
    name: "Drawing 4",
    percent_complete: 90.1,
    package_id: 100,
    package_name: "Another Area",
    package_area: "Madison Square Garden",
    job_title: "Job 1",
    job_number: 1000,
    container_name: "Container 1",
    laydown_location: "Laydown 1",
    priority: 1000,
  },
  {
    id: 5,
    name: "Drawing 5",
    percent_complete: 90.7,
    package_id: 10,
    package_name: "Package 1",
    package_area: "Madison Square Garden",
    job_title: "Job 1",
    job_number: 1000,
    container_name: "Container 1",
    laydown_location: "Laydown 1",
    priority: 10,
  },
  {
    id: 6,
    name: "Drawing 6",
    percent_complete: 90.7,
    package_id: 10,
    package_name: "Package 1",
    package_area: "Madison Square Garden",
    job_title: "Job 1",
    job_number: 1000,
    container_name: "Container 1",
    laydown_location: "Laydown 1",
    priority: 103,
  },
];
