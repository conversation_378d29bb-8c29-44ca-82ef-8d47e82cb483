@import "../../styles/colors.scss";
@import "../../styles/sizes.scss";

div.assigned-drawings-wrapper {
  background-color: $bgDark;
  height: calc(100vh - #{$headerFooter});
  @supports (-webkit-touch-callout: none) {
    height: calc(100vh - #{$headerFooter} - #{$iosAddressBar});
  }

  display: flex;
  flex-direction: column;

  & h4 {
    color: $lighterSlate;
    text-align: center;
    font-size: 0.9rem;
    margin: 0;
    padding-top: 8px;
    white-space: nowrap;
    overflow: hidden;
    font-weight: normal;

    position: relative;
  }

  & > h4 > button.my-work-download-majs {
    position: absolute;
    left: 0;
    top: 0;

    font-size: 0.8rem;
    padding: 0 10px;
    margin: 0;
    height: 25px;
    background-color: $fabProBlue;
    color: #fff;
  }

  & div.filters-wrapper {
    margin-top: 20px;
    padding-left: 8px;
  }
}

div.menu-closed {
  opacity: 0;
  display: none;
}

div.grid-action-bar-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin: 15px 10px 5px;
  overflow: hidden;

  & button.columns-btn {
    background-color: transparent;
    outline: none;
    border: none;
    color: $blue;
    cursor: pointer;

    &:focus {
      border: none;
      outline: none;
    }

    &:disabled {
      color: darken($blue, 20%);
    }
  }

  & p.table-count {
    color: $textGrey;
    font-size: 0.7rem;
    width: 85px;
    margin-right: 5px;
  }

  & input[type="text"] {
    background-color: transparent;
    width: 20%;
    min-width: 60px;
    max-width: 200px;
    border-radius: 3px;
    font-size: 0.8rem;
    color: white;
    outline: none;
    border: none;
    padding-left: 5px;
    height: 20px;
    flex: 1;

    &::placeholder {
      color: #ccc;
      font-size: 0.65rem;
    }
    &:focus {
      border: none;
    }
  }

  // EDITING POPUPS
  & span.edit-dropdown-wrapper {
    position: relative;
    height: 16px;

    div.column-picker-wrapper {
      position: fixed;
      display: flex;
      top: 25%;
      // left: 18.5%;
      left: 15%;
      flex-direction: column;
      background-color: white;
      height: 110px;
      width: 170px;
      z-index: 1000;
      border-radius: 3px;

      & p {
        font-size: 0.8rem;
        padding: 8px 5px;
        margin: 0;
      }

      & .button-wrapper {
        display: flex;
        flex: 1;
        flex-direction: column;
        margin-top: 5px;
        align-items: stretch;

        & button {
          flex: 1;
          font-size: 0.9rem;
          height: 28px;
          outline: none;
          border: none;
          color: $blue;
          background-color: white;
          border-top: 1px solid #eee;
          cursor: pointer;

          &:last-of-type {
            border-bottom-left-radius: 3px;
            border-bottom-right-radius: 3px;
          }

          &:hover {
            background-color: #eee;
          }
        }
      }
    }
  }

  & .selected-edit {
    color: #fac439;
  }

  & svg {
    color: $blue;
    margin: 0 14px;
    min-width: 22px;
    cursor: pointer;
  }
  & svg.search {
    font-size: 1.1rem;
  }
  & svg.scan,
  svg.refresh {
    font-size: 1.3rem;
  }
}

div.job-container-selector {
  min-height: 120px;
  max-height: 240px;
  width: 280px;
  background-color: black;
  border-radius: 3px;
  position: fixed;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  top: 25%;
  left: 15%;
  padding: 5px 10px;
  color: white;

  & p {
    font-size: 0.9rem;
    text-align: center;
    padding: 0;
  }

  & ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
    background-color: #ccc;
    border-radius: 5px;
    height: auto;
    max-height: 130px !important;
    overflow-y: auto;
    overflow: auto;
    color: black;
    & li {
      font-size: 0.8rem;
      padding: 8px;
      cursor: pointer;
    }
  }

  & button {
    color: $red;
    border: none;
    background-color: transparent;
    font-size: 0.9rem;
    margin: 15px 0 10px;
    cursor: pointer;
    font-weight: bold;
  }
}

div.menu-table-wrapper {
  flex-grow: 1;

  // height
  & > .custom-ag-styles.ag-theme-balham-dark {
    // 24 for title, 90 for filters, 55 for grid action bar
    height: calc(100vh - #{$headerFooter} - 24px - 90px - 56px);
    @supports (-webkit-touch-callout: none) {
      height: calc(
        100vh - #{$headerFooter} - #{$iosAddressBar} - 24px - 90px - 56px
      );
    }
  }
  // AG-GRID TABLE STYLES
  & .custom-ag-styles.ag-theme-balham-dark {
    width: 100%;

    & div.ag-row {
      min-height: 60px !important;
    }

    & .matching-value {
      color: $blue !important;
    }

    & .ag-side-buttons {
      display: none;
    }
    & .ag-tool-panel-wrapper {
      background-color: transparent;
      width: 0;
      border-left: none;
    }
    & .ag-tool-panel-horizontal-resize {
      display: none;
    }
    & .ag-column-select-list .ag-focus-managed {
      background-color: $bgDark;
    }
    & .ag-column-panel-column-select {
      position: fixed;
      left: 320px;
      max-height: 320px;
      border: 1px solid $blue;
      background-color: $bgDark;
      font-size: 0.9rem;
      height: auto;
      z-index: 1;
    }

    & .ag-body-viewport {
      overflow-y: scroll !important;

      &::-webkit-scrollbar {
        display: none;
      }
    }
    .ag-cell {
      white-space: normal;
      min-height: 60px !important;
    }
    // & .ag-body-horizontal-scroll {
    //   display: none;
    // }
    // & .ag-body-horizontal-scroll-viewport {
    //   &::-webkit-scrollbar {
    //     display: none;
    //   }
    // }

    .ag-center-cols-viewport {
      background-color: $bgDark;
    }
    // FOOTER
    .ag-paging-panel {
      background-color: $bgDark;
    }

    & div.ag-header {
      background-color: #20232a;
      border-top: none;
      border-right: none;

      & .ag-header-row {
        & .ag-header-cell-text {
          color: $textLight;
          font-size: 0.65rem;
          font-weight: normal;
        }
      }

      // hides column separator
      & .ag-header-cell::after {
        display: none;
      }
    }

    div.ag-row.--custom-grid-even {
      background-color: $rowEven;
    }
    div.ag-row.--custom-grid-odd {
      background-color: $bgDark;
    }
    div.ag-row.--custom-selected-drawing {
      background-color: $rowGreen !important;
      border: 1px solid $rowYellowOutline !important;
    }
    div.ag-row.--custom-selected-drawings,
    div.ag-row-selected {
      background-color: $rowGreen !important;
    }
  }

  & div.no-drawings-message {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: white;
    font-size: 0.8rem;
    letter-spacing: 1px;
  }
}
