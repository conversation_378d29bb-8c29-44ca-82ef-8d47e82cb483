import { assignedDrawingsColumnDefs } from "./assignedDrawingsConstants";

describe("Assigned Drawings", () => {
  describe("Column Defs", () => {
    const defaultHeaders = [
      "Progress",
      "Priority",
      "Drawing",
      "Package",
      "Pkg ID",
      "Package Area",
      "Job",
      "Job #",
      "Container Name",
      "Laydown Location",
    ];

    let populatedColumns;

    beforeEach(() => {
      populatedColumns = assignedDrawingsColumnDefs(null, false, null);
    });

    it("Headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);

      expect(columnHeaders).toEqual(defaultHeaders);
    });

    describe("PROGRESS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Progress");
      });

      const params = {
        data: {
          percent_complete: 50,
        },
      };
      const badParams = {
        data: {
          percent_complete: null,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(50);
      });

      it("valueGetter", () => {
        expect(column.valueFormatter(params)).toEqual("50%");
        expect(column.valueFormatter(badParams)).toEqual("0%");
      });
    });

    describe("PRIORITY", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Priority");
      });

      let arr = [
        {
          data: {
            id: 1,
            priority: 3,
          },
        },
        {
          data: {
            id: 2,
            priority: 2,
          },
        },
        {
          data: {
            id: 3,
            priority: 10,
          },
        },
      ];
      const params = {
        api: {
          forEachNode: (callback) => {
            for (let i = 0; i < arr.length; i++) {
              callback(arr[i]);
            }
          },
        },
        node: {
          gridApi: {
            forEachNode: (callback) => {
              for (let i = 0; i < arr.length; i++) {
                callback(arr[i]);
              }
            },
          },
        },
        data: {
          id: 1,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(2);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(2);
      });
    });

    describe("DRAWING", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Drawing");
      });

      const params = {
        data: {
          name: "test drawing",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test drawing");
      });
    });

    describe("PACKAGE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Package");
      });

      const params = {
        data: {
          package_name: "test package",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test package");
      });
    });

    describe("PKG ID", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Pkg ID");
      });

      const params = {
        data: {
          package_id: 1,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(1);
      });
    });

    describe("PACKAGE AREA", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Package Area");
      });

      const params = {
        data: {
          package_area: "test area",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test area");
      });
    });

    describe("JOB", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Job");
      });

      const params = {
        data: {
          job_title: "test job",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test job");
      });
    });

    describe("CONTAINER NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Container Name"
        );
      });

      const params = {
        data: {
          container_name: "test container",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test container");
      });
    });

    describe("LAYDOWN LOCATION", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Laydown Location"
        );
      });

      const params = {
        data: {
          laydown_location: "test laydown",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test laydown");
      });
    });
  });
});
