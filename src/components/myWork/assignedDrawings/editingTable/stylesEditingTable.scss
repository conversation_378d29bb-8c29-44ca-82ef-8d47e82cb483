@import "../../../styles/colors.scss";
@import "../../../styles/sizes.scss";

div.edit-wrapper {
  height: 220px;
  width: 100%;
  background-color: black;
  color: white;
  overflow: hidden;

  p {
    text-align: center;
    font-size: 0.8rem;
    padding-top: 5px;
    max-width: 600px;
    margin-top: 0;
  }

  & .small {
    width: 250px;
  }

  & .edit-buttons-wrapper {
    margin-top: auto;
    display: flex;
    justify-content: space-between;
    width: 450px;

    & button {
      background-color: transparent;
      border: none;
      font-size: 0.8rem;
      color: $textGrey;
      cursor: pointer;
    }
    & button:disabled {
      color: #999 !important;
      cursor: default !important;
    }
    & button.warning {
      color: #9d5262;
    }
    & button.save {
      color: $blue;
    }
  }
}
div.container-form {
  display: grid;
  grid-template-columns: 140px 160px 160px 140px;
  grid-template-rows: repeat(3, 50px);
  grid-gap: 5px 20px;
  overflow: hidden;
  padding: 0 10px;

  & .inline-selectable-filter-wrapper {
    margin-left: 12px;
    & .inline-filter-button {
      // width: 50px;
      align-items: center;
      height: 32px;
      display: flex;
      justify-content: space-between;
      font-size: 0.9rem;
      & svg {
        margin-top: 3px;
      }
    }
    & .selected-item {
      margin-top: 3px;
    }
    & .selected-item p {
      font-size: 0.7rem;
      padding-top: 0;
    }
  }

  & span.laydown-location {
    grid-row: 3/3;
    grid-column: 3/3;
  }

  & span.job-filter {
    grid-row: 1/3;
    grid-column: 1;
  }

  & span.desc-input {
    grid-column: 3/3;
    grid-row: 2/3;
  }

  & input[type="text"] {
    background-color: #263137;
    color: white;
    border: none;
    height: 26px;
    margin-left: 3px;
    padding-left: 5px;
    width: 90%;

    &:focus {
      border: none;
      outline: none;
    }
  }

  ul.list-selector {
    background-color: $bgDark;
    list-style-type: none;
    margin: 0;
    padding: 0;
    max-height: 100px;
    overflow: auto;

    & li {
      display: flex;
      align-items: center;
      padding-left: 5px;
      font-size: 0.8rem;
      cursor: pointer;
      white-space: nowrap;
      overflow: hidden;
      height: 28px;
    }

    & .selected-item {
      background-color: $blue;
    }
  }

  & .container-list {
    grid-row: 1 / 3;
    grid-column: 2 / 3;
  }

  & span.container-toggle {
    grid-row: 3 / 4;
    grid-column: 1 / 3;
    width: 200px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 8px;

    & label {
      margin-left: 0;
    }
    & .toggle-text {
      font-size: 0.7rem;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
  }

  & label {
    font-size: 0.75rem;
    color: $textGrey;
    margin-left: 8px;
  }
}
div.laydown-form {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 5px 0 8px;
  & label {
    color: $textGrey;
    font-size: 0.8rem;
    display: block;
    margin: 3px 0 5px 5px;
  }
  & input[type="text"] {
    background-color: #263137;
    color: white;
    border: none;
    height: 26px;
    margin-left: 3px;
    padding-left: 5px;

    &:focus {
      border: none;
      outline: none;
    }
  }
}

div.edit-table-wrapper {
  flex: 1;

  .custom-ag-styles.ag-theme-balham-dark {
    height: calc(100vh - #{$headerFooter} - 390px);
    @supports (-webkit-touch-callout: none) {
      height: calc(100vh - #{$headerFooter} - #{$iosAddressBar} - 390px);
    }

    & .ag-body-viewport {
      overflow-y: auto !important;
      background-color: $bgDark;
    }
    & .ag-center-cols-viewport {
      & .ag-center-cols-container {
      }
    }
    & div.ag-row-selected {
      background-color: $darkBlue !important;
    }

    & .ag-cell {
      white-space: normal;
    }
  }
}
