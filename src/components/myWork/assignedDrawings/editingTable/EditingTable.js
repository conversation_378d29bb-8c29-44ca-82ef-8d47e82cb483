// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useMemo, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import Switch from "react-switch";

// REDUX IMPORTS
import {
  handleUpdateDrawings,
  handleFetchContainers,
  handleUpdateContainers,
  handleCreateContainer,
  handleDeleteContainer,
} from "../../../drawings/drawingsActions";

// CONSTANT IMPORTS
import { frameworkComponents } from "../assignedDrawingsConstants";

// HELPER FUNCTION IMPORTS
// import { permissionLock, onDisplayedColumnsChanged } from "../../../../_utils";

// COMPONENT IMPORTS
import AgTable from "../../../reusable/agTable/AgTable";

// STYLES IMPORTS
import "./stylesEditingTable.scss";

const EditingTable = ({
  drawings,
  type,
  selectedJobs,
  selectedPackages,
  toggleEditing,
  refreshDrawings,
  toggleJobContainerPopup,
  filteredJobContainer,
  setFilteredJobContainer,
  jobsByPackages,
  workableJobs,
  gridEditOptionsApi,
  setGridEditOptionsApi,
  columns,
  selectedStage,
}) => {
  const [drawingsToEdit, setDrawingsToEdit] = useState([]);
  const [editedValues, setEditedValues] = useState({});
  const [selectedContainer, setSelectedContainer] = useState(null);
  const [displayedDrawings, setDisplayedDrawings] = useState(null);
  const [displayedContainers, setDisplayedContainers] = useState(null);
  const [viewAll, toggleAll] = useState(false);

  const dispatch = useDispatch();

  const { containers, isLoading } = useSelector((state) => state.drawingsData);

  const selectedContainerRef = useRef(null);

  useEffect(() => {
    setDisplayedContainers(containers);
  }, [containers]);

  useEffect(() => {
    if (!filteredJobContainer[0]) return;
    const selectedJobIds = selectedJobs.map((j) => j.id);
    if (!selectedJobIds.includes(filteredJobContainer[0].id))
      setDisplayedContainers([]);
  }, [selectedJobs]);

  // clear out container list if de-selected package is attached to filtered job
  useEffect(() => {
    if (!selectedPackages || !filteredJobContainer.length) return;
    const jobIdsOfPackages = selectedPackages.map((p) => p.job_id);
    if (!jobIdsOfPackages.includes(filteredJobContainer[0].id)) {
      setDisplayedContainers([]);
    }
  }, [selectedPackages]);

  useEffect(() => {
    if (type === "CONTAINER" && filteredJobContainer.length) {
      dispatch(handleFetchContainers([filteredJobContainer[0].id]));
    }
  }, [type, filteredJobContainer]);

  useEffect(() => {
    if (selectedJobs.length === 1) {
      setFilteredJobContainer([selectedJobs[0]]);
    }
  }, [selectedJobs, selectedPackages, setFilteredJobContainer]);

  useEffect(() => {
    setEditedValues({});
  }, [filteredJobContainer]);

  // drawings are in dep array for data refrash
  const f_drawings = useMemo(() => {
    if (type === "CONTAINER") {
      if (!filteredJobContainer || !filteredJobContainer.length)
        return drawings;

      return drawings.filter(
        (drawing) => drawing.job_id === filteredJobContainer[0].id
      );
    } else return drawings;
  }, [filteredJobContainer, drawings]);

  useEffect(() => {
    if (!f_drawings) return;

    if (selectedContainer) {
      toggleAll(true);

      setEditedValues({
        name: selectedContainer.name,
        description: selectedContainer.description,
        laydown_location: selectedContainer.laydown_location,
      });
    } else {
      setEditedValues({});
    }
  }, [selectedContainer]);

  useEffect(() => {
    if (type === "LAYDOWN_LOCATION") return setDisplayedDrawings(drawings);
    if (!f_drawings) return;

    if (!selectedJobs.length && !selectedPackages.length)
      return setDisplayedDrawings([]);

    // All and no container selected
    if (viewAll && !selectedContainer) {
      return setDisplayedDrawings(f_drawings);
    }
    // dont need to check if viewing all because will always be true when container is selected
    else if (selectedContainer) {
      setDisplayedDrawings(
        f_drawings.filter(
          (d) => !d.container_id || d.container_id === selectedContainer.id
        )
      );
    }
    // return drawings not in a container
    else {
      setDisplayedDrawings(f_drawings.filter((d) => !d.container_id));
    }
  }, [viewAll, f_drawings, selectedContainer, selectedJobs, selectedPackages]);

  useEffect(() => {
    if (!gridEditOptionsApi) return;

    gridEditOptionsApi.setRowData(displayedDrawings);
  }, [gridEditOptionsApi, displayedDrawings]);

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    event.persist();
    setEditedValues({ ...editedValues, [name]: value });
  };

  // Populate laydown_location / container if only 1 selected drawing
  useEffect(() => {
    if (type === "CONTAINER") {
      const selectedContainers = drawingsToEdit.map((d) => d.container_id);
      if (!selectedContainers.length) return;
      for (let i = 0; i <= selectedContainers.length; i++) {
        // check if valid container
        if (selectedContainers[i]) {
          const container = containers.find(
            (c) => c.id === selectedContainers[i]
          );
          setSelectedContainer(container);
        }
      }
      return;
    }
    // editing laydown
    else if (!drawingsToEdit || !drawingsToEdit.length)
      return setEditedValues({});
    if (drawingsToEdit.length > 1)
      return setEditedValues({ laydown_location: "" });

    drawingsToEdit[0].laydown_location &&
      setEditedValues({ laydown_location: drawingsToEdit[0].laydown_location });
  }, [type, drawingsToEdit]);

  const handleSaveEditedRows = () => {
    if (type === "CONTAINER") {
      dispatch(
        handleUpdateContainers([selectedContainer.id], editedValues)
      ).then(() => {
        handleCleanup();
        refreshDrawings();
        dispatch(handleFetchContainers([filteredJobContainer[0].id]));
      });
    } else {
      dispatch(
        handleUpdateDrawings(
          drawingsToEdit.map((drawing) => drawing.id),
          [editedValues]
        )
      ).then(() => {
        handleCleanup();
        refreshDrawings();
      });
    }
  };

  const handleCreateNewContainer = () => {
    const selectedStageId = selectedStage ? selectedStage.id : null;
    const selectedDrawingIds =
      drawingsToEdit && drawingsToEdit.length
        ? drawingsToEdit.map((d) => d.id).join(",")
        : null;
    dispatch(
      handleCreateContainer(
        filteredJobContainer[0].id,
        selectedDrawingIds,
        selectedStageId,
        "work-items",
        editedValues
      )
    ).then((res) => {
      if (editedValues.laydown_location) {
        const freshContainer = res.find((c) => c.name === editedValues.name);
        freshContainer &&
          dispatch(
            handleUpdateContainers([freshContainer.id], {
              laydown_location: editedValues.laydown_location,
            })
          ).then(() => {
            dispatch(handleFetchContainers([filteredJobContainer[0].id]));
            handleCleanup();
            refreshDrawings();
            setEditedValues({});
            return;
          });
      }
      handleCleanup();
      refreshDrawings();
      setEditedValues({});
    });
  };

  const handleCleanup = () => {
    setDrawingsToEdit([]);
    clearEditSelections();
    toggleJobContainerPopup(false);
  };

  const redrawRows = (params) => params.api.redrawRows();
  const onGridReady = (params) => {
    setGridEditOptionsApi(params.api);
    params.api.setSideBarVisible(false);
    params.api.setSortModel([
      {
        colId: "priority",
        sort: "asc",
      },
    ]);
  };
  const onSelectionChanged = (params) => {
    const rows = params.api.getSelectedRows();
    setDrawingsToEdit(rows);
  };

  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };

  const isRowSelectable = (rowNode) => {
    if (type === "LAYDOWN_LOCATION") {
      return !rowNode.data.container_id;
    } else {
      return true;
    }
  };

  const gridOptions = {
    // FOR REACT OPTIMIZATION
    reactNext: true,
    getRowNodeId: (data) => data.id,
    immutableData: true,

    rowSelection: "multiple",
    rowClassRules,
    defaultColDef: {
      wrapText: true,
    },
    columnDefs: columns,
    rowData: displayedDrawings || [],
    onGridReady,
    onSelectionChanged,

    isRowSelectable,
    onSortChanged: redrawRows,
    frameworkComponents,
    pagination: false,
  };

  const clearEditSelections = () => {
    setDrawingsToEdit([]);
    // setEditedValues({});
    // if (selectedContainer) {
    //   selectedContainerRef.current = null;
    //   setSelectedContainer(null)};
    gridEditOptionsApi.deselectAll();
  };

  const handleContainerSelect = (container) => {
    if (!selectedContainer) {
      selectedContainerRef.current = container;
      return setSelectedContainer(container);
    }

    if (selectedContainer.id === container.id) {
      selectedContainerRef.current = null;
      setSelectedContainer(null);
    } else {
      selectedContainerRef.current = container;
      setSelectedContainer(container);
    }
  };

  const handleJobFilterSelection = (job) => {
    if (selectedJobs.length === 1) return;
    setSelectedContainer(null);
    setFilteredJobContainer([job]);
  };

  const handleContainerDelete = () => {
    dispatch(
      handleDeleteContainer(
        selectedContainer.id.toString(),
        selectedStage ? selectedStage.id.toString() : null
      )
    ).then(() => {
      handleCleanup();
      refreshDrawings();
      setEditedValues({});
      setSelectedContainer(null);
      dispatch(handleFetchContainers([filteredJobContainer[0].id]));
    });
  };

  const addRemoveContainer = (action) => {
    const { id } = selectedContainer;
    const stage_id = selectedStage ? selectedStage.id : null;
    let bodyData;

    if (action === "add") {
      bodyData = [{ container_id: id }, { area: "work-items" }, { stage_id }];
    } else {
      bodyData = {
        area: "work-items",
        stage_id,
      };
    }

    dispatch(
      handleUpdateDrawings(
        drawingsToEdit.map((d) => d.id),
        bodyData,
        action
      )
    ).then(() => {
      handleCleanup();
      refreshDrawings();
    });
  };

  const selectableJobs = useMemo(() => {
    if (selectedJobs && selectedJobs.length) return selectedJobs;
    if (jobsByPackages && jobsByPackages.length) return jobsByPackages;
    if (workableJobs && workableJobs.length) return workableJobs;
    else return [];
  }, [selectedJobs, jobsByPackages]);

  const disableUpdate = useMemo(() => {
    if (
      type === "CONTAINER" &&
      !selectedContainer &&
      editedValues &&
      editedValues.name
    ) {
      return false;
    }
    if (!editedValues || !drawingsToEdit || !drawingsToEdit.length) return true;

    if (type === "LAYDOWN_LOCATION") {
      const editedKeyArr = Object.keys(editedValues);
      const editedValueArr = Object.values(editedValues);

      if (
        drawingsToEdit.length === 1 &&
        editedKeyArr.includes("laydown_location")
      ) {
        return editedValueArr[0] === drawingsToEdit[0].laydown_location
          ? true
          : false;
      } else {
        return editedKeyArr.includes("laydown_location") ? false : true;
      }
    } else return editedValues && !editedValues.container_name;
  }, [editedValues, drawingsToEdit, type]);

  const EditActions = () => {
    if (selectedContainer)
      return (
        <div className="edit-buttons-wrapper">
          <button onClick={clearEditSelections} className="warning">
            Clear Selection
          </button>
          <button onClick={handleSaveEditedRows} className="save">
            Update
          </button>
          <button
            className={drawingsToEdit && drawingsToEdit.length ? "save" : ""}
            disabled={
              !drawingsToEdit ||
              !drawingsToEdit.length ||
              !drawingsToEdit.every(
                (d) => d.container_id !== selectedContainer.id
              )
            }
            onClick={() => addRemoveContainer("add")}
          >
            Add
          </button>
          <button
            className={drawingsToEdit && drawingsToEdit.length ? "save" : ""}
            onClick={() => addRemoveContainer("remove")}
            disabled={
              !drawingsToEdit ||
              !drawingsToEdit.length ||
              !drawingsToEdit.every(
                (d) => d.container_id === selectedContainer.id
              )
            }
          >
            Remove
          </button>
          <button onClick={handleContainerDelete} className="warning">
            Delete
          </button>
          <button
            onClick={() => {
              clearEditSelections();
              toggleEditing(false);
            }}
            className="warning"
          >
            Exit
          </button>
        </div>
      );
    else
      return (
        <div className="edit-buttons-wrapper small">
          <button onClick={clearEditSelections} className="warning">
            Clear Selection
          </button>
          <button
            // ROUTE NEEDS UPDATED
            disabled={disableUpdate}
            onClick={
              type === "CONTAINER"
                ? handleCreateNewContainer
                : handleSaveEditedRows
            }
            className="save"
          >
            {`${type === "CONTAINER" ? "Create" : "Update"}`}
          </button>
          <button
            onClick={() => {
              clearEditSelections();
              toggleEditing(false);
            }}
            className="warning"
          >
            Exit
          </button>
        </div>
      );
  };

  const ListSelector = ({ list, nameKey, selectedItem, setSelectedItem }) => (
    <ul className="list-selector">
      {list &&
        list.length > 0 &&
        list.map((item) => (
          <li
            key={item.id}
            onClick={() => setSelectedItem(item)}
            className={
              selectedItem && item.id === selectedItem.id ? "selected-item" : ""
            }
          >
            {item[nameKey]}
          </li>
        ))}
    </ul>
  );

  return (
    <>
      {type === "CONTAINER" && (
        <div className="edit-wrapper container-edit">
          <p>Edit Container</p>
          <div className="container-form">
            <div className="container-list">
              <label>Container List</label>
              {containers && containers.length > 0 && !isLoading && (
                <ListSelector
                  list={displayedContainers}
                  nameKey="name"
                  selectedItem={selectedContainer}
                  setSelectedItem={handleContainerSelect}
                />
              )}
            </div>
            <span>
              <label>Container Name</label>
              <input
                name="name"
                onChange={(e) => handleInputChange(e)}
                value={(editedValues && editedValues.name) || ""}
                type="text"
              />
            </span>
            <span className="laydown-location">
              <label>Laydown Location</label>
              <input
                onChange={(e) => handleInputChange(e)}
                value={(editedValues && editedValues.laydown_location) || ""}
                name="laydown_location"
                type="text"
              />
            </span>
            <span className="job-filter">
              <label>Job Filter</label>
              {selectableJobs && selectableJobs.length > 0 && !isLoading && (
                <ListSelector
                  list={selectableJobs}
                  nameKey="job_name"
                  selectedItem={filteredJobContainer[0]}
                  setSelectedItem={handleJobFilterSelection}
                />
              )}
            </span>
            <span className="desc-input">
              <label>Container Description</label>
              <input
                name="description"
                onChange={(e) => handleInputChange(e)}
                value={(editedValues && editedValues.description) || ""}
                type="text"
              />
            </span>
            <span className="container-toggle">
              <label>All</label>
              <Switch
                offColor="#253137"
                onColor="#2196f3"
                onChange={() => toggleAll(!viewAll)}
                checked={!viewAll}
                height={18}
                width={40}
                handleDiameter={24}
                boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                checkedIcon={false}
                uncheckedIcon={false}
                disabled={!!selectedContainer}
              />
              <label>Not in Container</label>
            </span>
          </div>
          <EditActions />
        </div>
      )}
      {type === "LAYDOWN_LOCATION" && (
        <div className="edit-wrapper laydown-form">
          <span>
            <label>Laydown Location</label>
            <input
              onChange={(e) => handleInputChange(e)}
              value={(editedValues && editedValues.laydown_location) || ""}
              name="laydown_location"
              type="text"
            />
          </span>
          <EditActions />
        </div>
      )}
      <div className="edit-table-wrapper">
        <AgTable gridOptions={gridOptions} />
      </div>
    </>
  );
};

export default EditingTable;
