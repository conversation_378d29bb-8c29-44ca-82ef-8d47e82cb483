// NPM PACKAGE IMPORTS
import moment from "moment";

// REDUX IMPORTS
import store from "../../../redux/store";

// COMPONENT IMPORTS
import PercentCompleteCellRenderer from "../../reusable/frameworkComponents/PercentCompleteCellRenderer";

// HELPER FUNCTION IMPORTS
import {
  transformPriorityToRank,
  naturalSort,
  generateTime,
} from "../../../_utils";

// EXPORTS
export const assignedDrawingsColumnDefs = (
  isEditing,
  savedColumnState,
  drawingCustomColumns
) => {
  const checkboxColumn = {
    headerName: "",
    headerCheckboxSelection: true,
    headerCheckboxSelectionFilteredOnly: true,
    width: 40,
    minWidth: 40,
    checkboxSelection: true,
    suppressMenu: true,
    colId: "checkbox",
    lockPosition: true,
    pinned: "left",
  };

  const { systemSettings } = store.getState().profileData;
  let dateFormatting = systemSettings && systemSettings.date_display;

  const dataColumns = [
    {
      headerName: "Progress",
      field: "percent_complete",
      valueGetter: (params) => {
        if (!params.data) return;
        const { percent_complete } = params.data;
        if (!percent_complete) return 0;
        return Math.floor(percent_complete);
      },
      valueFormatter: (params) => {
        if (!params.data) return;
        const { percent_complete } = params.data;
        if (!percent_complete) return "0%";
        return `${Math.floor(percent_complete)}%`;
      },
      getQuickFilterText: (params) => params.data.percent_complete,
      minWidth: 80,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "percent_complete",
    },
    {
      headerName: "Priority",
      field: "priority",
      valueGetter: (params) => {
        let allValues = [];
        let value;

        if (params.api) {
          params.api.forEachNode((rowNode) => {
            allValues.push(rowNode.data);
          });
          const ranks = transformPriorityToRank(allValues);
          ranks.forEach((rank, index) => {
            if (rank?.id === params.data?.id) value = ++index;
          });
        }
        return value;
      },
      getQuickFilterText: (params) => {
        let allValues = [];
        let value;
        if (params.api) {
          params.node.gridApi.forEachNode((rowNode) => {
            allValues.push(rowNode.data);
          });
          const ranks = transformPriorityToRank(allValues);
          for (let i = 0; i <= ranks.length; i++) {
            if (ranks[i] && ranks[i].id === params.data.id) {
              value = ++i;
              break;
            }
          }
        }
        return value;
      },
      minWidth: 80,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "priority",
    },
    {
      headerName: "Drawing",
      field: "name",
      getQuickFilterText: (params) => params.data.name,
      minWidth: 120,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "name",
      cellClass: "custom-wrap",
      autoHeight: true,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Package",
      field: "package_name",
      getQuickFilterText: (params) => params.data.package_name,
      minWidth: 120,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_name",
      autoHeight: true,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Pkg ID",
      field: "package_id",
      getQuickFilterText: (params) => params.data.package_id,
      minWidth: 100,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_id",
    },
    {
      headerName: "Package Area",
      field: "package_area",
      getQuickFilterText: (params) => params.data.package_area,
      minWidth: 120,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_area",
      autoHeight: true,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Job",
      field: "job_title",
      getQuickFilterText: (params) => params.data.job_title,
      minWidth: 120,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "job_title",
      autoHeight: true,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Job #",
      field: "job_number",
      getQuickFilterText: (params) => params.data.job_number,
      minWidth: 80,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "job_number",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Container Name",
      field: "container_name",
      getQuickFilterText: (params) => params.data.container_name,
      minWidth: 120,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "container_name",
      autoHeight: true,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Laydown Location",
      field: "laydown_location",
      getQuickFilterText: (params) => params.data.laydown_location,
      minWidth: 120,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "laydown_location",
      autoHeight: true,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
  ];

  if (drawingCustomColumns) {
    for (let column of drawingCustomColumns) {
      let columnDef = {
        headerName: column.display_name,
        field: column.normal_name,
        autoHeight: true,
        minWidth: column.data_type === "string" ? 120 : 100,
        editable: false,
        filter: false,
        menuTabs: [],
        filterParams: {
          buttons: ["reset"],
        },
        colId: column.normal_name,
      };
      if (column.data_type === "date") {
        columnDef.filterParams = {
          buttons: ["reset"],
          comparator: (filterLocalDateAtMidnight, cellValue) => {
            const cellDate = cellValue
              ? typeof cellValue === "number"
                ? new Date(generateTime(cellValue * 1000, false, true, "-"))
                : new Date(cellValue)
              : "-";

            return cellDate < filterLocalDateAtMidnight
              ? -1
              : cellDate > filterLocalDateAtMidnight
              ? 1
              : 0;
          },
        };
        columnDef.getQuickFilterText = (params) => {
          let value;
          const date = params.data?.[column.normal_name]
            ? new Date(params.data?.[column.normal_name])
            : null;

          const timezone = Intl.DateTimeFormat().resolvedOptions().timezone;
          if (!date) value = "N/A";
          else value = moment.tz(date, timezone).format(dateFormatting);
          return value;
        };
        columnDef.valueFormatter = (params) => {
          return params.value
            ? typeof params.value === "number"
              ? generateTime(params.value * 1000, false, true, "-")
              : params.value
            : "";
        };
      }
      dataColumns.push(columnDef);
    }
  }

  if (savedColumnState && savedColumnState.length) {
    let result = [];

    for (let i = 0; i < dataColumns.length; i++) {
      let savedDef = savedColumnState.find(
        (c) => c.header_name === dataColumns[i].headerName
      );

      if (savedDef) {
        result.push({
          ...dataColumns[i],
          pinned: savedDef.pinned,
          hide: savedDef.visible ? false : true,
        });
      } else result.push(dataColumns[i]);
    }

    result = result.sort((a, b) => {
      if (a.position === b.position) {
        if (a.headerName.toLowerCase() > b.headerName.toLowerCase()) return 1;
        else return -1;
      } else return a.position - b.position;
    });

    if (isEditing) {
      result.unshift(checkboxColumn);
    }
    return result;
  }
  return isEditing ? [checkboxColumn, ...dataColumns] : [...dataColumns];
};

export const frameworkComponents = {
  percentCompleteCellRenderer: PercentCompleteCellRenderer,
};
