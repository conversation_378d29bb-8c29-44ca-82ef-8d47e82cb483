import React, { useState, useEffect, useMemo, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import {
  handleSetPageTitle,
  handleFetchColumnState,
  handleFetchSortState,
  handleSaveSortState,
} from "../../redux/generalActions";
import {
  handleFetchWorkableJobs,
  handleFetchWorkableStages,
  handleFetchWorkableDrawings,
  handleFetchWorkableTableFilters,
  handleClearWorkableTableFilters,
  handleSaveWorkableTableFilters,
} from "../myWork/myWorkActions";
import { handleFetchWorkableItems } from "../items/itemsActions";
import { notify } from "../reusable/alertPopup/alertPopupActions";
import {
  handleUpdateTableViewSettings,
  handleFetchMyWorkTableViewSettings,
} from "../profile/profileActions";
import { handleFetchAllTimers } from "../timers/timersActions";
import { handleFetchCustomColumns } from "../customColumns/customColumnsActions";
import {
  fetchCustomColumnDataMultiItem,
  fetchCustomColumnDataPerItem,
} from "../../_services";

// COMPONENT IMPORTS
import MenuWrapper from "./menuWrapper/MenuWrapper";
import AssignedDrawings from "./assignedDrawings/AssignedDrawings";
import StagesTimersStack from "./stagesTimersStack/StagesTimersStack";
import DrawingViewer from "./drawingViewer/DrawingViewer";
import Countdown from "../reusable/countdown/Countdown";

// CONSTANT IMPORTS
import {
  assignedDrawingsColumnDefs,
  frameworkComponents,
} from "./assignedDrawings/assignedDrawingsConstants";

// HELPER FUNCTION IMPORTS
import { permissionLock, onDisplayedColumnsChanged } from "../../_utils";
import usePrevious from "../../hooks/usePrevious";
import { sortAndFilter, associateCustomColumnData } from "../../utils/agTable";

// STYLES IMPORTS
import "./stylesMyWork.scss";
import "../styles/tables.scss";
import {
  handleFetchDrawingFiles,
  handleFetchPackageFile,
} from "../files/filesActions";

let jobsToSelect, packagesToSelect;

const ActiveTimerSliver = ({ handleClick, activeTimer }) => {
  if (
    ["loading", "unloading", "receiving"].includes(activeTimer[0].item_type)
  ) {
    return (
      <div className="active-timer-button">
        <a href={`${process.env.REACT_APP_FABPRO}/shipping/bill_of_lading.php`}>
          Active Work
        </a>
      </div>
    );
  }
  if (activeTimer[0].type === "generic") {
    return (
      <div className="active-timer-button">
        <a href={`/generic-time/`}>Active Work</a>
      </div>
    );
  }

  return (
    <div className="active-timer-button">
      <button onClick={handleClick}>Active Work</button>
    </div>
  );
};

const MyWorkCustomColumns = () => {
  const { on } = useSelector((state) => state.nestingData);
  const [selectedDrawingId, setSelectedDrawingId] = useState(null);
  const [selectedDrawingIds, setSelectedDrawingIds] = useState([]);
  const [selectedDrawings, setSelectedDrawings] = useState([]);
  const [selectedJobs, setSelectedJobs] = useState([]);
  const [selectedPackages, setSelectedPackages] = useState([]);
  const [selectedStage, setSelectedStage] = useState(null);
  const [selectedStages, setSelectedStages] = useState([]);
  const [seconds, setSeconds] = useState(120);
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [rightPanel, setRightPanel] = useState(true);
  // for draggy fix over viewer
  const [pdfInstance, setPdfInstance] = useState(null);
  const [isEditing, toggleEditing] = useState(false);
  const [isViewingAllItems, toggleViewingAllItems] = useState(null);
  const [displayedDrawings, setDisplayedDrawings] = useState([]);
  const [showCountdown, setShowCountdown] = useState(false);
  const [tableCount, setTableCount] = useState(null);
  const [searchInput, setSearchInput] = useState("");
  const [groupingType, setGroupingType] = useState(null);
  // used for shift select (will always be the last row selected)
  const [selectedRowNode, setSelectedRowNode] = useState(null);
  const [tableViewSettingsLoaded, toggleTableViewSettingsLoaded] = useState(
    false
  );
  const [selectedDrawingsLoaded, toggleSelectedDrawingsLoaded] = useState(
    false
  );
  const [ignorePopulatedState, toggleIgnorePopulatedState] = useState(false);
  //  following 5 variables used for left menu filters
  const [jobsFilterSet, toggleJobsFilterSet] = useState(false);
  const [packagesFilterSet, togglePackagesFilterSet] = useState(false);
  const [allFiltersSet, toggleAllFiltersSet] = useState(false);
  const [jobsSelectAll, setJobsSelectAll] = useState(0);
  const [packagesSelectAll, setPackagesSelectAll] = useState(0);
  const [localStoragePopulated, toggleLocalStoragePopulated] = useState(false);
  const [allDrawingColumnsHidden, toggleDrawingsColumnsHidden] = useState(
    false
  );
  const [savedStageFilter, setSavedStageFilter] = useState(null);
  const [stageFilterSet, toggleStageFilterSet] = useState(false);
  const [savedJobsFilter, setSavedJobsFilter] = useState(null);
  const [savedPackagesFilter, setSavedPackagesFilter] = useState(null);
  const [maxGroupQuantity, setMaxGroupQuantity] = useState("1");
  // used to handle group/clear button state in AdvancedGroupingFilter
  const [isAdvancedGroupingGrouped, toggleAdvancedGroupingGrouped] = useState(
    false
  );
  const [file, setFile] = useState(null);

  const isNesting = useRef(null);
  const selectedDrawingIdsRef = useRef(null);
  const selectedDrawingIdRef = useRef(null);
  const activeItemsRef = useRef(null);
  const prevSelectedRowNodeRef = useRef(null);
  const shiftSelectRef = useRef(null);
  const gridOptionsApiRef = useRef(null);
  const displayedDrawingsRef = useRef(null);

  const {
    workableDrawings,
    workableStages,
    assignedDrawingsColumnState,
    leftMenuWidth,
    workableJobs,
    workablePackages,
    isLoading: workDataLoading,
    myWorkTableFilters,
    myWorkViewingAll,
  } = useSelector((state) => state.myWorkData);
  const { customColumns } = useSelector((state) => state.customColumns);
  const drawingCustomColumns = customColumns?.filter(
    (col) => col.table_target === "drawings"
  );
  const { permissions, features } = useSelector((state) => state.profileData);
  const { activeTimer } = useSelector((state) => state.timerData);
  const { sortStateLeft } = useSelector((state) => state.generalData);
  const { packageFiles, drawingFiles } = useSelector(
    (state) => state.filesData
  );

  const dispatch = useDispatch();

  // THESE RUN ONCE
  useEffect(() => {
    dispatch(handleSetPageTitle());
    dispatch(handleFetchColumnState("ASSIGNED_DRAWINGS"));
    dispatch(handleFetchSortState("ASSIGNED_DRAWINGS", null, 0, "LEFT"));
    dispatch(handleFetchAllTimers("running"));
    dispatch(handleFetchCustomColumns);
    return () => {
      dispatch(handleClearWorkableTableFilters);
    };
  }, []);

  // INITAL SETTING OF selectedStage IF SAVED SELECTED STAGE
  useEffect(() => {
    if (
      workableStages &&
      workableStages.length &&
      savedStageFilter &&
      !stageFilterSet
    ) {
      if (savedStageFilter > 0) {
        if (workableStages || !workableStages.length) {
          const stage = workableStages.find((ws) => ws.id === savedStageFilter);
          setSelectedStage(stage);

          if (!stage?.id) return;

          populateLocalStorageState(stage?.id);
        }
      }
      toggleStageFilterSet(true);
    }
    const stageToSend = savedStageFilter ? [savedStageFilter] : null;
    dispatch(handleFetchWorkableJobs(stageToSend));
  }, [workableStages, savedStageFilter]);

  useEffect(() => {
    // used to determine if selected stage saved in DB is still workable
    dispatch(handleFetchWorkableStages());
  }, [dispatch]);

  // FETCH initial table filters and view settings
  useEffect(async () => {
    if ((myWorkTableFilters && myWorkViewingAll) || !workableStages.length)
      return;

    let savedStageFilter;
    dispatch(handleFetchMyWorkTableViewSettings).then((res) => {
      savedStageFilter = res[0].my_work_stage_id || 0;
      const workableStageIds = workableStages?.map((s) => s.id);
      // check if saved stage in DB is still a workable stage, if not clear it
      if (workableStageIds.includes(parseInt(savedStageFilter))) {
        setSavedStageFilter(savedStageFilter);
      } else {
        // clear saved stage in DB
        handleUpdateTableViewSettings(
          null,
          res?.my_work_viewing_all || 0,
          null,
          1
        );
      }
      if (savedStageFilter === 0) {
        setGroupingType(groupingType ? groupingType : "Ungrouped");
      }
      dispatch(
        handleFetchWorkableTableFilters(
          res[0].my_work_viewing_all,
          // always pass 1 for grouping state since it's now being tracked in localStorage
          1,
          savedStageFilter
        )
      ).then((res) => {
        if (!res.error) {
          const savedJobsFilter = res.find((r) => r.filter_type === "jobs");
          if (savedJobsFilter) setSavedJobsFilter(savedJobsFilter);

          const savedPackagesFilter = res.find(
            (r) => r.filter_type === "packages"
          );
          if (savedPackagesFilter) setSavedPackagesFilter(savedPackagesFilter);
        } else {
          // update displayed stages to be all workableStages since we have no job or package filters
          toggleJobsFilterSet(true);
          togglePackagesFilterSet(true);
        }
      });

      toggleViewingAllItems(res[0].my_work_viewing_all ? true : false);
      toggleTableViewSettingsLoaded(true);
      if (res.error) toggleAllFiltersSet(true);
    });
  }, [dispatch, workableStages]);

  // SAVE filter state
  useEffect(() => {
    if (!myWorkTableFilters || !allFiltersSet || !selectedDrawingsLoaded)
      return;

    let filters = [];

    const filterObj = (type, items, ids, selectAll = 0) => ({
      filter_type: type,

      item_ids:
        selectAll === 1
          ? ""
          : ids
          ? ids.join(",")
          : items.map((i) => i.id).join(","),
      select_all: selectAll,
    });

    if (selectedJobs && selectedJobs.length)
      filters.push(filterObj("jobs", selectedJobs, null, jobsSelectAll));
    if (selectedPackages && selectedPackages.length)
      filters.push(
        filterObj("packages", selectedPackages, null, packagesSelectAll)
      );
    if (selectedDrawingIds && selectedDrawingIds.length)
      filters.push(filterObj("drawings", null, selectedDrawingIds));

    // check to not make the call if empty filters
    if (!filters.length && !myWorkTableFilters.length) return;

    // if filters is empty but table filters is not,
    // the post will return a 404 but not update myWorkTableFilters until page reload
    const drawingId =
      selectedJobs.length || selectedPackages.length || selectedStage
        ? selectedDrawingId
        : null;

    dispatch(
      handleSaveWorkableTableFilters(
        filters,
        isViewingAllItems ? 1 : 0,
        // grouping state that is now tracked in localStorage so always passing in 1
        1,
        selectedStage ? selectedStage.id : 0,
        drawingId
      )
    );
  }, [
    selectedJobs,
    selectedPackages,
    selectedDrawingId,
    selectedDrawingIds,
    selectedDrawingsLoaded,
    displayedDrawings,
    isViewingAllItems,
    selectedStage,
    tableViewSettingsLoaded,
  ]);
  const applyTableFilters = async () => {
    const jobsFilter = myWorkTableFilters.find((f) => f.filter_type === "jobs");
    const packagesFilter = myWorkTableFilters.find(
      (f) => f.filter_type === "packages"
    );

    let localJobsFilterSet = false,
      localPackagesFilterSet = false;

    if (!jobsFilterSet) {
      let workableJobsToUse =
        workableJobs && workableJobs.length === 0
          ? await dispatch(handleFetchWorkableJobs(null))
          : workableJobs;

      if (workableJobsToUse?.error) workableJobsToUse = [];

      if (workableJobsToUse && workableJobsToUse.length && jobsFilter) {
        jobsToSelect =
          jobsFilter.select_all === 1
            ? workableJobsToUse
            : workableJobsToUse.filter((j) => {
                let pattern = new RegExp(`(^|,)${j.id}($|,)`);
                return pattern.test(jobsFilter.item_ids);
              });
        setSelectedJobs(jobsToSelect);
        setJobsSelectAll(jobsFilter.select_all);
      }
      localJobsFilterSet = true;
      toggleJobsFilterSet(true);
    }

    if (packagesFilter) {
      if (
        workablePackages &&
        workablePackages.length &&
        !packagesFilterSet &&
        (!jobsFilter || jobsFilterSet)
      ) {
        packagesToSelect =
          packagesFilter.select_all === 1
            ? workablePackages
            : workablePackages.filter((p) => {
                let pattern = new RegExp(`(^|,)${p.id}($|,)`);
                return (
                  pattern.test(packagesFilter.item_ids) &&
                  (jobsToSelect
                    ? jobsToSelect.find((j) => j.id === p.job_id)
                    : true)
                );
              });
        setSelectedPackages(packagesToSelect);
        setPackagesSelectAll(packagesFilter.select_all);
        localPackagesFilterSet = true;
        togglePackagesFilterSet(true);
      }
    } else {
      localPackagesFilterSet = true;
      togglePackagesFilterSet(true);
    }

    if (!ignorePopulatedState) {
      toggleViewingAllItems(myWorkViewingAll === 1 ? true : false);
      toggleIgnorePopulatedState(false);
    }

    if (
      (!jobsFilter || jobsFilterSet || localJobsFilterSet) &&
      (!packagesFilter || packagesFilterSet || localPackagesFilterSet) &&
      !allFiltersSet
    ) {
      toggleAllFiltersSet(true);
    }
  };

  // POPULATE table state
  useEffect(() => {
    if (
      !myWorkTableFilters ||
      !tableViewSettingsLoaded ||
      allFiltersSet ||
      workDataLoading ||
      (workableJobs && workableJobs.length === 0)
    )
      return;

    applyTableFilters();
  }, [
    myWorkTableFilters,
    workableJobs,
    workablePackages,
    jobsFilterSet,
    packagesFilterSet,
    allFiltersSet,
    tableViewSettingsLoaded,
    workDataLoading,
  ]);

  useEffect(() => {
    if (
      !myWorkTableFilters ||
      !tableViewSettingsLoaded ||
      selectedDrawingsLoaded ||
      workDataLoading ||
      !workableDrawings ||
      !workableDrawings.length ||
      !gridOptionsApi
    )
      return;

    const drawingsFilter = myWorkTableFilters.find(
      (f) => f.filter_type === "drawings"
    );

    const displayedDrawingIds = workableDrawings.map((d) => d.id);

    // populate selected drawing ids
    if (drawingsFilter) {
      const drawingIdsToBeSelected = drawingsFilter.item_ids
        .split(",")
        .map((id) => parseInt(id));

      if (
        drawingIdsToBeSelected.every((id) => displayedDrawingIds.includes(id))
      ) {
        const previousDrawingId =
          myWorkTableFilters[0].last_selected_drawing_id;

        if (previousDrawingId) {
          if (displayedDrawingIds.includes(previousDrawingId)) {
            setDisplayedPdf(
              workableDrawings.find((d) => d.id === previousDrawingId)
            );
            setSelectedDrawingId(previousDrawingId);
          }
        }
        setSelectedDrawingIds(drawingIdsToBeSelected);
        gridOptionsApi.redrawRows();
      }
    }

    toggleSelectedDrawingsLoaded(true);
  }, [
    myWorkTableFilters,
    allFiltersSet,
    tableViewSettingsLoaded,
    workableDrawings,
    workDataLoading,
    gridOptionsApi,
  ]);

  const selectedStageRef = useRef(null);

  useEffect(() => {
    if (!allFiltersSet) return;

    selectedStageRef.current = selectedStage;
    dispatch(
      handleUpdateTableViewSettings(
        null,
        isViewingAllItems ? 1 : 0,
        selectedStage ? selectedStage.id : null,
        // not populating grouping state via DB anymore so always passing in 1 for group state
        1
      )
    );
  }, [
    selectedStage,
    isViewingAllItems,
    tableViewSettingsLoaded,
    allFiltersSet,
  ]);

  // if displayedDrawings changes, remove selections no longer displayed
  useEffect(() => {
    displayedDrawingsRef.current = displayedDrawings;
    if (displayedDrawings.length && selectedDrawingIds.length) {
      let newSelectedIds = [];
      const displayedIds = displayedDrawings.map((d) => d.id);
      selectedDrawingIds.forEach((id) => {
        if (displayedIds.includes(id)) newSelectedIds.push(id);
      });

      if (
        newSelectedIds.length !== selectedDrawingIds.length ||
        !newSelectedIds.every((id) => selectedDrawingIds.includes(id))
      ) {
        setSelectedDrawingIds(newSelectedIds);
      }
      if (selectedDrawingId && !newSelectedIds.includes(selectedDrawingId)) {
        setSelectedDrawingId(null);
      }
    }
    if (gridOptionsApiRef.current) gridOptionsApi.purgeInfiniteCache();
  }, [displayedDrawings, gridOptionsApi]);

  useEffect(() => {
    if (!workableDrawings || !tableViewSettingsLoaded) return;

    if (!selectedJobs.length && !selectedPackages.length) {
      return selectedStage
        ? setDisplayedDrawings(workableDrawings)
        : setDisplayedDrawings([]);
    }

    let f_drawings = [];
    if (selectedPackages.length) {
      const packageIds = selectedPackages.map((p) => p.id);
      f_drawings = workableDrawings.filter((d) =>
        packageIds.includes(d.package_id)
      );
    } else if (selectedJobs.length) {
      const jobIds = selectedJobs.map((j) => j.id);
      f_drawings = workableDrawings.filter((d) => jobIds.includes(d.job_id));
    }

    setDisplayedDrawings(f_drawings);
  }, [workableDrawings, selectedJobs, selectedPackages, selectedStage]);

  const prevLeftMenuWidth = usePrevious(leftMenuWidth);
  const prevSelectedRowNode = usePrevious(selectedRowNode);

  useEffect(() => {
    prevSelectedRowNodeRef.current = prevSelectedRowNode;
  }, [prevSelectedRowNode]);

  useEffect(() => {
    if (!gridOptionsApi) return;

    if (prevLeftMenuWidth <= 20) gridOptionsApi.resetRowHeights();
  }, [leftMenuWidth]);

  useEffect(() => {
    gridOptionsApiRef.current = gridOptionsApi;
  }, [gridOptionsApi]);

  useEffect(() => {
    if (workableDrawings) {
      if (selectedDrawingIds?.length)
        setSelectedDrawings(
          workableDrawings.filter((d) => selectedDrawingIds.includes(d.id))
        );
      else setSelectedDrawings([]);
    }
  }, [workableDrawings, selectedDrawingIds]);

  const handleGroupingUpdate = (newGroupingState) => {
    setGroupingType(newGroupingState);

    const groupId =
      newGroupingState === "Ungrouped"
        ? 0
        : newGroupingState === "Grouped"
        ? 1
        : newGroupingState === "Group By Drawing"
        ? 2
        : 3; // advanced grouping

    let groupingByStageState = JSON.parse(
      localStorage.getItem("stageGrouping")
    );

    const newStageObj = {
      stageId: selectedStage.id,
      groupId: groupId,
      maxGroupQuantity: "1",
    };

    if (!groupingByStageState) {
      localStorage.setItem("stageGrouping", JSON.stringify([newStageObj]));
    } else {
      const currentSavedStageGrouping = groupingByStageState.find(
        (stageObj) => stageObj.stageId === selectedStage.id
      );

      // TODO ! if we want to always keep maxGroupQuantity remove groupId === 3 condition
      if (groupId === 3 && currentSavedStageGrouping) {
        newStageObj.maxGroupQuantity =
          currentSavedStageGrouping?.maxGroupQuantity || "";
      }

      groupingByStageState = groupingByStageState.filter(
        (stageObj) => stageObj.stageId !== selectedStage.id
      );

      groupingByStageState.push(newStageObj);
      localStorage.setItem(
        "stageGrouping",
        JSON.stringify(groupingByStageState)
      );
    }
  };

  const viewDrawingWithoutTimer = useMemo(() => {
    if (permissions && permissions.length) {
      return permissionLock([{ permissions: [298] }]);
    } else return null;
  }, [permissions]);

  useEffect(() => {
    if (viewDrawingWithoutTimer && !activeTimer) {
      setSeconds(120);
      setShowCountdown(true);
    } else {
      setShowCountdown(false);
    }
  }, [activeTimer, viewDrawingWithoutTimer]);

  useEffect(() => {
    if (showCountdown) setSeconds(120);
  }, [selectedDrawingId, showCountdown]);

  useEffect(() => {
    isNesting.current = on;
  }, [on]);

  useEffect(() => {
    selectedDrawingIdsRef.current = selectedDrawingIds;
  }, [selectedDrawingIds]);

  const cancelNestFirstMes = () => {
    dispatch(
      notify({
        id: Date.now(),
        type: "ERROR",
        message: "To adjust filters, first cancel your nest",
      })
    );
  };

  const refreshDrawingsStages = (
    jobsIds,
    packageIds,
    stageIds,
    drawingIds,
    groupingType
  ) => {
    let promiseArr = [];

    if (
      (jobsIds && jobsIds.length) ||
      (packageIds && packageIds.length) ||
      (stageIds && stageIds.length)
    );
    promiseArr.push(
      dispatch(handleFetchWorkableDrawings(jobsIds, packageIds, stageIds))
    );
    if (
      selectedStage &&
      selectedStage.id &&
      allFiltersSet &&
      tableViewSettingsLoaded &&
      groupingType
    ) {
      promiseArr.push(
        dispatch(
          handleFetchWorkableItems(
            jobsIds,
            packageIds,
            isViewingAllItems ? null : selectedDrawingIds,
            [selectedStage.id],
            groupingType,
            maxGroupQuantity || "1"
          )
        )
      );
    } else {
      promiseArr.push(
        dispatch(handleFetchWorkableStages(jobsIds, packageIds, drawingIds))
      );
    }

    return Promise.all(promiseArr);
  };

  // AG-GRID SETUP

  const deselectDrawings = (ids) => {
    if (gridOptionsApi) {
      gridOptionsApi.forEachNode((n) => {
        if (ids.includes(n.data.id)) {
          n.setSelected(false);
          n.data.drawingSelected = false;
        }
      });
    }
  };

  // used for the PDF files that we fetch from AWS S3 service
  const packageFilesRef = useRef(null);
  useEffect(() => {
    packageFilesRef.current = packageFiles;
  }, [packageFiles]);

  const drawingFilesRef = useRef(null);
  useEffect(() => {
    drawingFilesRef.current = drawingFiles;
  }, [drawingFiles]);

  // TODO - Consolidate to a reusable function, duplicated code!
  // common code for setting the package map as the file
  const getPackagePDFs = (packageId, updateFile = false) => {
    if (!packageId) return;
    const files = packageFilesRef?.current;
    if (!files?.hasOwnProperty(packageId)) {
      dispatch(handleFetchPackageFile(packageId)).then((res) => {
        if (res?.[packageId] && updateFile) {
          setFile(res[packageId]);
        }
      });
    } else if (updateFile) {
      // update the file if it already exists and we want to update...
      setFile(files[packageId]);
    }
    // else we exit because we already know the drawing package info
  };

  // TODO - Consolidate to a reusable function, duplicated code!
  const getDrawingPDFs = (id, rowData) => {
    if (!id) return;
    const files = drawingFilesRef?.current;
    if (files?.hasOwnProperty(id) && files[id].original) {
      // if drawing doesn't have original check for map
      if (files?.[id]?.original) {
        setFile(files[id].original);
      }
      // load package PDF and set if original was missing
      if (rowData.has_package_map) {
        getPackagePDFs(rowData.package_id, !files?.[id]?.original);
      }
    } else {
      // Have to blindly fetch PDFs here as we don't have access to has_original, etc.
      dispatch(handleFetchDrawingFiles(id)).then((res) => {
        // if drawing doesn't have original check for map
        if (res?.[id]?.original) {
          setFile(res[id].original);
        }
        // load package PDF and set if original was missing
        getPackagePDFs(rowData.package_id, !res?.[id]?.original);
      });
    }
  };

  const setDisplayedPdf = (rowData) => {
    const id = rowData.id;
    getDrawingPDFs(id, rowData); // fetch and set files...
  };

  const onCellClicked = (params) => {
    if (isNesting.current)
      return dispatch(
        notify({
          id: Date.now(),
          type: "ERROR",
          message: "To adjust filters, first cancel your nest",
        })
      );
    if (!params.data.drawingSelected) {
      params.node.setSelected(true);
      params.data.isSelected = true;
      params.data.drawingSelected = true;
      setDisplayedPdf(params.data);
    } else {
      params.node.setSelected(false);
      params.data.drawingSelected = false;
      params.data.isSelected = false;
    }
    setSelectedRowNode(params.node);
    onRowSelected(params);
  };

  useEffect(() => {
    selectedDrawingIdRef.current = selectedDrawingId;
  }, [selectedDrawingId]);

  const onRowSelected = (params) => {
    if (!params.api) return;

    const usedShiftSelect = params.event.shiftKey;
    if (params.event.shiftKey) shiftSelectRef.current = true;

    let selectedRows = selectedDrawingIdsRef.current;
    // this value won't be correct if user is deselecting but we don't care
    // it's only used to toggle all/selected tab on adding to selection
    const selectionType = selectedDrawingIdsRef.current.includes(params.data.id)
      ? "RIGHT"
      : "LEFT";

    if (isNesting.current && selectionType === "LEFT") {
      params.node.setSelected(false);
      params.data.drawingSelected = false;
      return cancelNestFirstMes();
    }

    if (
      selectedDrawingIdsRef.current.includes(params.node.id) &&
      (isNesting.current ||
        (activeItemsRef.current &&
          activeItemsRef.current
            .map((t) => t.drawing_id)
            .includes(params.node.id)))
    ) {
      params.node.setSelected(
        selectedDrawingIdsRef.current.includes(params.data.id)
      );
      params.data.drawingSelected = selectedDrawingIdsRef.current.includes(
        params.data.id
      );
      if (isNesting.current && selectionType === "LEFT") cancelNestFirstMes();
      return;
    }
    if (selectedDrawingIdsRef.current.includes(params.data.id)) {
      selectedRows = selectedRows.filter((id) => id !== params.data.id);
    } else selectedRows.push(params.data.id);
    setSelectedDrawingIds(selectedRows);
    dispatch(
      handleFetchWorkableItems(
        selectedJobs.map((j) => j?.id),
        selectedPackages.map((p) => p?.id),
        isViewingAllItems ? null : selectedRows,
        [selectedStageRef?.current?.id],
        selectedStageRef?.current?.groupable ? groupingType : "Ungrouped",
        maxGroupQuantity || "1"
      )
    ).then((res) => {
      if (
        (res.error || !res || !res.length) &&
        allFiltersSet &&
        tableViewSettingsLoaded
      ) {
        // toggle to show all items within assigned drawings displayed
        toggleIgnorePopulatedState(true);
        toggleViewingAllItems(true);
      }
    });
    if (!params.data.drawingSelected) {
      // check if the the selectedDrawing was deselected - if so clear state/context
      if (params.node.data.isSelected) {
        let forgeUrn = params.node.data.forge_urn;
        let hasForgeUrn = false;
        let newSelection = null;
        // if forge drawing, search for drawings in selection with same urn
        if (forgeUrn) {
          params.api.forEachNode((n) => {
            if (
              !newSelection &&
              selectedRows.includes(n.data.id) &&
              n.data.forge_urn === forgeUrn &&
              n.data.id !== params.node.data.id
            ) {
              hasForgeUrn = true;
              newSelection = n.data.id;
              return;
            }
          });
        }
        if (hasForgeUrn) {
          // select next forge drawing if possible
          params.api.forEachNode((n) => {
            if (n.data.id === newSelection) {
              n.data.isSelected = true;
            } else if (n.data.isSelected) {
              n.data.isSelected = false;
            }
          });
          setSelectedDrawingId(newSelection);
        } else {
          params.api.forEachNode((n) => {
            if (n.data.isSelected) {
              n.data.isSelected = false;
            }
          });
          setSelectedDrawingId(null);
          // no drawing selected, go to all
          toggleViewingAllItems(true);
        }
      } else if (params.node.id === selectedDrawingIdRef.current)
        setSelectedDrawingId(null);
    } else {
      // set new selection as selectedDrawingId
      params.api.forEachNode((n) => {
        if (params.data.id === n.data.id) {
          n.setSelected(true);
          n.data.isSelected = true;
          n.data.drawingSelected = true;
        } else if (n.data.isSelected) {
          n.data.isSelected = false;
        }
      });
      setSelectedDrawingId(params.data.id);
      // if first drawing selected from left panel, toggle to selected
      if (
        selectionType === "LEFT" &&
        selectedDrawingIdsRef.current.length === 1
      )
        toggleViewingAllItems(false);
    }
  };

  // update rows on data refetch/selection change to set border
  useEffect(() => {
    if (gridOptionsApi) {
      let temp = [];
      displayedDrawings.forEach((n) => {
        if (n.id === selectedDrawingId) {
          n.isSelected = true;
          n.drawingSelected = true;
          // } else if (n.isSelected) {
        } else {
          n.isSelected = false;
          if (selectedDrawingIds.includes(n.id)) {
            n.drawingSelected = true;
          }
        }
        temp.push(n);
      });

      gridOptionsApi.resetRowHeights();
      gridOptionsApi.redrawRows();
    }
  }, [
    gridOptionsApi,
    displayedDrawings,
    selectedDrawingId,
    selectedDrawingIds,
  ]);

  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
    "--custom-selected-drawing": (params) => {
      if (params.node.data?.isSelected) {
        return true;
      } else return false;
    },
  };

  const getRowStyle = (params) => {
    if (params.data?.isSelected)
      return { background: "#16603d", border: "1px solid #d6993a" };
    if (params.data?.drawingSelected) return { background: "#16603d" };
    else if (params.node.childIndex % 2 === 1) return { background: "#20232a" };
    else if (params.node.childIndex % 2 === 0) return { background: "#253137" };
  };

  const onSortChanged = (params) => {
    params.api.resetRowHeights();
    params.api.redrawRows();
    if (params.columnApi.getAllColumns()) {
      const sortedColumn = params.columnApi.getAllColumns().find((c) => c.sort);

      dispatch(
        handleSaveSortState(
          sortedColumn ? sortedColumn.colId : null,
          sortedColumn ? sortedColumn.sort : null,
          "ASSIGNED_DRAWINGS"
        )
      );
    }
  };

  const onInfiniteGridReady = (params) => {
    setGridOptionsApi(params.api);
    // hide the columns button along table
    params.api.setSideBarVisible(false);
    params.api.setSortModel([
      {
        colId: sortStateLeft
          ? sortStateLeft.sorting_column_name || "priority"
          : "priority",
        sort: sortStateLeft ? sortStateLeft.sorting_method || "asc" : "asc",
      },
    ]);

    const dataSource = {
      rowCount: undefined,
      getRows: async (params) => {
        const dataAfterSortAndFilter = sortAndFilter(
          displayedDrawingsRef.current,
          params.sortModel,
          params.filterModel
        );

        const rowsThisPage = dataAfterSortAndFilter.slice(
          params.startRow,
          params.endRow
        );
        let lastRow = -1;

        if (dataAfterSortAndFilter.length <= params.endRow) {
          lastRow = dataAfterSortAndFilter.length;
        }

        let rowsWithCustomData = [];

        // Fetch the custom column data for all the rows on this page as a hashmap
        // and add that data to each row
        const rowIds = rowsThisPage.map((x) => x.id);

        // only fetch custom column data if table data has populated
        if (rowIds?.length) {
          const customColumnData = await fetchCustomColumnDataMultiItem(
            "drawings",
            rowIds
          );

          for (let row of rowsThisPage) {
            if (row.id in customColumnData) {
              const formattedCustomColumnData = associateCustomColumnData(
                customColumnData[row.id],
                drawingCustomColumns
              );
              rowsWithCustomData.push({ ...formattedCustomColumnData, ...row });
            } else {
              rowsWithCustomData.push({ ...row });
            }
          }
        }

        // pass updated data with custom column data into grid
        params.successCallback(rowsWithCustomData, lastRow);
      },
    };
    params.api.setDatasource(dataSource);
  };

  const permissionLockedColumns = useMemo(() => {
    if (assignedDrawingsColumnState) {
      return permissionLock(
        assignedDrawingsColumnDefs(
          isEditing,
          assignedDrawingsColumnState,
          drawingCustomColumns
        )
      );
    }
  }, [assignedDrawingsColumnState, isEditing, drawingCustomColumns]);

  const prevColumnState = usePrevious(assignedDrawingsColumnState);
  const prevGridOptionsApi = usePrevious(gridOptionsApi);

  useEffect(() => {
    if (
      !gridOptionsApi ||
      !permissionLockedColumns ||
      (JSON.stringify(assignedDrawingsColumnState) ===
        JSON.stringify(prevColumnState) &&
        gridOptionsApi === prevGridOptionsApi)
    )
      return;

    gridOptionsApi.setColumnDefs(permissionLockedColumns);
  }, [
    gridOptionsApi,
    permissionLockedColumns,
    assignedDrawingsColumnState,
    prevColumnState,
    prevGridOptionsApi,
  ]);

  // SUPER IMPORTANT: to have table not clear selection on stage entry,
  // make the workableDrawings length check be an "or" selected stage
  const showDrawingsTable = useMemo(() => {
    // make sure jobs or packages are selected and we have workable drawings
    return (
      (!!(selectedStage && selectedStage.id) ||
        selectedJobs.length > 0 ||
        selectedPackages.length > 0) &&
      !isEditing
    );
  }, [selectedJobs, selectedPackages, selectedStage, isEditing]);

  const onFilterChanged = (params) => {
    setTableCount(params.api.getDisplayedRowCount());
  };

  useEffect(() => {
    if (!searchInput && showDrawingsTable) {
      setTableCount(displayedDrawings.length || 0);
    } else if (!showDrawingsTable) setTableCount(0);
  }, [displayedDrawings, showDrawingsTable]);

  // handle groupingType state population from localStorage
  useEffect(() => {
    if (!selectedStage) {
      toggleLocalStoragePopulated(true);
      setGroupingType(null);
      setMaxGroupQuantity("1");
      return;
    }
    dispatch(handleFetchColumnState("MY_WORK_ITEMS", [selectedStage.id]));

    populateLocalStorageState(selectedStage?.id);

    return () => {
      toggleLocalStoragePopulated(false);
    };
  }, [selectedStage]);

  const populateLocalStorageState = (currentStageId) => {
    const stageGrouping = localStorage.getItem("stageGrouping");

    if (!stageGrouping) {
      setGroupingType("Ungrouped");
      setMaxGroupQuantity("1");
      return;
    }

    let savedGroupingState = JSON.parse(localStorage.getItem("stageGrouping"));

    let currentSavedStageGrouping = savedGroupingState.find(
      (stageObj) => stageObj.stageId === currentStageId
    );

    let savedGroupingType = currentSavedStageGrouping?.groupId;
    savedGroupingType =
      savedGroupingType === 0
        ? "Ungrouped"
        : savedGroupingType === 1
        ? "Grouped"
        : savedGroupingType === 2
        ? "Grouped By Drawing"
        : // only set to advanced grouping if the feature is ON
        savedGroupingType === 3 && features?.includes(51)
        ? "Advanced Grouping"
        : "Ungrouped";

    setGroupingType(savedGroupingType);

    const savedMaxGroupQuantity = features?.includes(51)
      ? currentSavedStageGrouping?.maxGroupQuantity ?? null
      : null;
    // only need to set maxGroupQuantity if it isn't 1, since that's default state
    if (savedMaxGroupQuantity && savedMaxGroupQuantity !== "1") {
      setMaxGroupQuantity(currentSavedStageGrouping?.maxGroupQuantity);
      toggleAdvancedGroupingGrouped(true);
    }

    toggleLocalStoragePopulated(true);
  };

  const handleColumnChange = (params) => {
    const allCols = params.columnApi.getAllGridColumns();
    const displayedCols = params.columnApi.getAllDisplayedColumns();
    // to avoid icon flashing red in-between renders, only toggle true if the grid has columns
    allCols?.length &&
      toggleDrawingsColumnsHidden(displayedCols?.length ? false : true);
    onDisplayedColumnsChanged("ASSIGNED_DRAWINGS", params);
  };

  const infiniteGridOptions = {
    reactNext: true,
    getRowNodeId: (data) => data.id,
    immutableData: true,
    onDisplayedColumnsChanged: handleColumnChange,
    onCellClicked,
    rowSelection: "multiple",
    suppressRowClickSelection: true,
    getRowStyle,
    columnDefs: permissionLockedColumns,
    onGridReady: onInfiniteGridReady,
    onSortChanged,
    frameworkComponents,
    pagination: false,
    defaultColDef: {
      wrapText: true,
      cellClass: "custom-wrap",
    },
    suppressContextMenu: true,
    suppressTouch: true,
    rowModelType: "infinite",
    // display lines per page
    cacheBlockSize: 30,

    // how many rows to seek ahead when unknown data size.
    cacheOverflowSize: 0,

    // how many concurrent data requests are allowed.
    maxConcurrentDatasourceRequests: 1,

    // how many rows to initially allow scrolling to in the grid.
    infiniteInitialRowCount: 100,

    // how many pages to hold in the cache.
    maxBlocksInCache: 2,

    blockLoadDebounceMillis: 200,
  };

  const handleActiveTimerClick = () => {
    const activeStage = workableStages.find(
      (s) => s.id === activeTimer[0].stage_id
    );
    setSelectedStage(activeStage);
    // if we have timer with drawing id, go to selected
    if (activeTimer[0].drawing_id) toggleViewingAllItems(false);
  };

  return (
    <div className="my-work-container">
      {activeTimer && !activeTimer.error && (
        <ActiveTimerSliver
          activeTimer={activeTimer}
          handleClick={handleActiveTimerClick}
        />
      )}
      <DrawingViewer
        selectedDrawingId={selectedDrawingId}
        setSelectedDrawingId={setSelectedDrawingId}
        setSelectedDrawingIds={setSelectedDrawingIds}
        selectedDrawingIds={selectedDrawingIds}
        selectedJobs={selectedJobs}
        selectedPackages={selectedPackages}
        selectedStages={selectedStages}
        rightPanel={rightPanel}
        setRightPanel={setRightPanel}
        setPdfInstance={setPdfInstance}
        gridOptionsApi={gridOptionsApi}
        gridOptions={infiniteGridOptions}
        displayedDrawings={displayedDrawings}
        file={file}
        setFile={setFile}
      />
      <MenuWrapper type="LEFT" pdfInstance={pdfInstance}>
        <AssignedDrawings
          selectedDrawingIds={selectedDrawingIds}
          selectedDrawings={selectedDrawings}
          setSelectedDrawingId={setSelectedDrawingId}
          setSelectedDrawingIds={setSelectedDrawingIds}
          selectedJobs={selectedJobs}
          setSelectedJobs={setSelectedJobs}
          selectedPackages={selectedPackages}
          setSelectedPackages={setSelectedPackages}
          selectedStages={selectedStages}
          setSelectedStages={setSelectedStages}
          selectedStage={selectedStage}
          gridOptionsApi={gridOptionsApi}
          gridOptions={infiniteGridOptions}
          permissionLockedColumns={permissionLockedColumns}
          isEditing={isEditing}
          toggleEditing={toggleEditing}
          displayedDrawings={displayedDrawings}
          refreshDrawingsStages={refreshDrawingsStages}
          searchInput={searchInput}
          setSearchInput={setSearchInput}
          tableCount={tableCount}
          showTable={showDrawingsTable}
          groupingType={groupingType}
          tableViewSettingsLoaded={tableViewSettingsLoaded}
          setJobsSelectAll={setJobsSelectAll}
          setPackagesSelectAll={setPackagesSelectAll}
          ignorePopulatedState={ignorePopulatedState}
          jobsFilterSet={jobsFilterSet}
          packagesFilterSet={packagesFilterSet}
          localStoragePopulated={localStoragePopulated}
          isViewingAllItems={isViewingAllItems}
          selectedDrawingsLoaded={selectedDrawingsLoaded}
          allColumnsHidden={allDrawingColumnsHidden}
          savedStageFilter={savedStageFilter}
          stageFilterSet={stageFilterSet}
          hasCustomColumnFeature={true}
        />
      </MenuWrapper>
      {rightPanel && (
        <MenuWrapper type="RIGHT" pdfInstance={pdfInstance}>
          <StagesTimersStack
            selectedDrawingId={selectedDrawingId}
            selectedDrawingIds={selectedDrawingIds}
            setSelectedDrawingId={setSelectedDrawingId}
            setSelectedDrawingIds={setSelectedDrawingIds}
            selectedJobs={selectedJobs}
            setSelectedJobs={setSelectedJobs}
            setSelectedPackages={setSelectedPackages}
            selectedPackages={selectedPackages}
            selectedStage={selectedStage}
            selectedStages={selectedStages}
            setSelectedStages={setSelectedStages}
            setSelectedStage={setSelectedStage}
            setDisplayedPdf={setDisplayedPdf}
            viewDrawingWithoutTimer={
              viewDrawingWithoutTimer && !viewDrawingWithoutTimer.length > 0
            }
            activeItemsRef={activeItemsRef}
            deselectDrawings={deselectDrawings}
            isViewingAllItems={isViewingAllItems}
            toggleViewingAllItems={toggleViewingAllItems}
            groupingType={groupingType}
            setGroupingType={handleGroupingUpdate}
            tableViewSettingsLoaded={tableViewSettingsLoaded}
            allFiltersSet={allFiltersSet}
            toggleIgnorePopulatedState={toggleIgnorePopulatedState}
            selectedDrawingsLoaded={selectedDrawingsLoaded}
            savedJobsFilter={savedJobsFilter}
            jobsFilterSet={jobsFilterSet}
            savedPackagesFilter={savedPackagesFilter}
            packagesFilterSet={packagesFilterSet}
            maxGroupQuantity={maxGroupQuantity}
            setMaxGroupQuantity={setMaxGroupQuantity}
            isAdvancedGroupingGrouped={isAdvancedGroupingGrouped}
            toggleAdvancedGroupingGrouped={toggleAdvancedGroupingGrouped}
          />
        </MenuWrapper>
      )}
      {showCountdown &&
      !viewDrawingWithoutTimer.length > 0 &&
      selectedDrawingId ? (
        <Countdown
          onTimerEnd={() => {
            setSelectedDrawingId(null);
            // no drawing selected, go to all
            toggleViewingAllItems(true);
            setSelectedStage(null);
          }}
          seconds={seconds}
          setSeconds={setSeconds}
        />
      ) : (
        <></>
      )}
    </div>
  );
};

export default MyWorkCustomColumns;
