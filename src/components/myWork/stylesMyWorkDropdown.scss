@import "../styles/colors.scss";
.mywork-dropdown-wrapper {
  max-width: 400px;
  border: 1px solid $blue;
  max-height: 200px; // seems to only work right at this height
  min-height: 0px;
  width: 100%;
  position: absolute;
  margin-left: 16px;
  z-index: 50;
  cursor: pointer;

  overflow-y: scroll;
  scrollbar-width: none; /* Firefox 64 */
  -ms-overflow-style: none; /* Microsoft Edge */
  &::-webkit-scrollbar {
    display: none;
  }

  .mywork-dropdown {
    display: flex;
    flex-direction: column;
    color: $textLight;
    background-color: $rowEven;

    .selected {
      background-color: $blue;
    }

    ul {
      list-style-type: none;
      margin: 0;
      padding: 0;

      li {
        padding: 10px;
      }
    }
  }
}
