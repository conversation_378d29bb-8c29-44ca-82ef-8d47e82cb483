import React, { useState, useRef } from "react";
import { FiChevronDown } from "react-icons/fi";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../hooks/useOutsideClick";

// COMPONENT IMPORTS
import MyWorkDropdown from "./MyWorkDropdown";

// STYLES IMPORTS
import "./stylesMyWorkDropdownButton.scss";

const MyWorkDropdownButton = ({
  itemList,
  selectedItem,
  updateSelectedItem,
  title,
  disabled,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const editPopupRef = useRef(null);
  useOutsideClick(editPopupRef, () => setIsOpen(false));

  return (
    <div
      ref={editPopupRef}
      onClick={() => {
        if (!disabled) setIsOpen(!isOpen);
      }}
    >
      <div className={"mywork-dropdown-button"}>
        <span>{selectedItem ? selectedItem : title}</span>
        <FiChevronDown />
      </div>
      {isOpen ? (
        <MyWorkDropdown
          itemList={itemList}
          selectedItem={selectedItem}
          updateSelectedItem={updateSelectedItem}
        />
      ) : undefined}
    </div>
  );
};

export default MyWorkDropdownButton;
