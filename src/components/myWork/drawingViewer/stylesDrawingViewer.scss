@import "../../styles/colors.scss";
@import "../../styles/sizes.scss";

div.drawing-viewer-wrapper {
  height: calc(100vh - #{$headerFooter});
  width: calc(100vw - 55px);
  z-index: -1;

  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

div.layout-body.narrow div.drawing-viewer-wrapper {
  width: calc(100vw - #{$navExpanded});
}

div.viewer-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  background-color: #999;
  font-weight: bold;
  width: calc(100% - 50px);
}

.work.pdf-viewer-wrapper {
  grid-template-columns: unset;
  height: calc(100vh - #{$headerFooter});
  width: calc(100% - 120px);
  margin: 0 34px;

  & div.viewer {
    height: inherit;
  }
}

// tabs and pdf viewer
#drawing-view-tabs ~ div.drawing-viewer-wrapper {
  height: calc(100vh - 53px - #{$headerFooter});
}
#drawing-view-tabs ~ .work.pdf-viewer-wrapper {
  height: calc(100vh - 53px - #{$headerFooter});
}
