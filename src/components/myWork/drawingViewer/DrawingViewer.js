import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import PSPDFKitViewer from "../../reusable/pdfViewer/PSPDFKitViewer";
import ForgeViewer from "./ForgeViewer";

// REDUX IMPORTS
import { handleFetchDrawingById } from "../myWorkActions";

// COMPONENT IMPORTS
import Tabs from "../../reusable/tabs/Tabs";

// STYLES IMPORTS
import "./stylesDrawingViewer.scss";
import "./stylesForgeViewer.scss";

const DrawingViewer = ({
  selectedDrawingId,
  selectedDrawingIds,
  setSelectedDrawingId,
  rightPanel,
  setRightPanel,
  setPdfInstance,
  displayedDrawings,
  file,
  setFile,
}) => {
  const dispatch = useDispatch();

  const DRAWING_TAB = "PDF";
  const PACKAGE_MAP_TAB = "Package Map";
  const FORGE_TAB = "3D Model";
  const [drawingTabs, setDrawingTabs] = useState([]);
  const [selectedDrawingTab, setSelectedDrawingTab] = useState(DRAWING_TAB);
  const [loadedDrawing, setLoadedDrawing] = useState(null);

  const { token, userInfo, permissions, userId, systemSettings } = useSelector(
    (state) => state.profileData
  );
  const { navExpanded } = useSelector((state) => state.generalData);
  const { leftMenuWidth, rightMenuWidth } = useSelector(
    (state) => state.myWorkData
  );
  const { drawingFiles, packageFiles } = useSelector(
    (state) => state.filesData
  );

  // get valid list of tabs for drawing
  const getDisplayOptions = (allDrawings, drawingId) => {
    let drawing = allDrawings.find((d) => d.id === drawingId);
    let options = [];
    if (!drawing) {
      return { options, drawing: {} };
    }

    const { forge_urn } = drawing;
    const selectedDrawing = displayedDrawings.find(
      (d) => d.id === selectedDrawingId
    );
    const { has_original, has_annotated, has_package_map } = selectedDrawing;

    if (has_original || has_annotated) {
      options.push(DRAWING_TAB);
    }
    if (forge_urn) {
      options.push(FORGE_TAB);
    }
    if (has_package_map) {
      options.push(PACKAGE_MAP_TAB);
    }
    return {
      options,
      drawing,
    };
  };

  // TODO - does this need updated
  const refreshDrawingPdf = useCallback(async () => {
    return dispatch(handleFetchDrawingById(selectedDrawingId)).then((res) => {
      if (!res.error && res.length) {
        let index = displayedDrawings
          .map((d) => d.id)
          .indexOf(selectedDrawingId);
        displayedDrawings[index] = res[0];
      }
    });
  }, [selectedDrawingId, displayedDrawings]);

  // use drawing in state for display to not accidentally clear when displayedDrawings get refetched
  useEffect(() => {
    if (displayedDrawings && selectedDrawingId) {
      let index = displayedDrawings.map((d) => d.id).indexOf(selectedDrawingId);
      if (index > -1) {
        setLoadedDrawing(displayedDrawings[index]);
      }
    } else {
      setLoadedDrawing(null);
    }
  }, [displayedDrawings, selectedDrawingId]);

  useEffect(() => {
    if (
      !rightPanel &&
      (!selectedDrawingId || selectedDrawingTab !== FORGE_TAB)
    ) {
      setRightPanel(true);
    }
    if (displayedDrawings.map((d) => d.id).includes(selectedDrawingId)) {
      let { options, drawing } = getDisplayOptions(
        displayedDrawings,
        selectedDrawingId
      );
      if (!options.length) return setDrawingTabs([]);

      setDrawingTabs(options);

      if (!options.includes(selectedDrawingTab)) {
        setSelectedDrawingTab(options.length ? options[0] : "");
      } else {
        if (selectedDrawingTab === DRAWING_TAB) {
          setFile(drawingFiles[selectedDrawingId]?.original || null);
        } else if (selectedDrawingTab === PACKAGE_MAP_TAB) {
          setFile(packageFiles[drawing.package_id] || null);
        }
      }
    } else {
      setDrawingTabs([]);
    }
  }, [selectedDrawingId, selectedDrawingIds, packageFiles, drawingFiles]);

  useEffect(() => {
    let { options, drawing } = getDisplayOptions(
      displayedDrawings,
      selectedDrawingId
    );

    if (!rightPanel && (!options || selectedDrawingTab !== FORGE_TAB)) {
      setRightPanel(true);
    }
    if (!options.length) return;

    if (options.includes(selectedDrawingTab)) {
      if (selectedDrawingTab === DRAWING_TAB) {
        setFile(drawingFiles[selectedDrawingId]?.original || null);
      } else if (selectedDrawingTab === PACKAGE_MAP_TAB) {
        setFile(packageFiles[drawing.package_id] || null);
      }
    }
  }, [selectedDrawingTab]);

  const forgeModelId = useMemo(() => {
    if (displayedDrawings) {
      let drawing = displayedDrawings.find((d) => d.id === selectedDrawingId);
      if (drawing && drawing.forge_model_id) return drawing.forge_model_id;
    }

    return 0;
  }, [displayedDrawings, selectedDrawingId]);

  return (
    <div
      className="drawing-viewer-wrapper"
      style={
        selectedDrawingTab === DRAWING_TAB ||
        selectedDrawingTab === PACKAGE_MAP_TAB
          ? {
              width: `calc(100vw - ${leftMenuWidth}px - ${rightMenuWidth}px`,
              marginLeft: `${leftMenuWidth}px`,
            }
          : {}
      }
    >
      {drawingTabs.length > 0 ? (
        <div
          id="drawing-view-tabs"
          style={{
            width: `calc(100vw - 55px - ${leftMenuWidth}px - ${rightMenuWidth}px - ${
              navExpanded ? "200px" : "0px"
            }`,
            marginLeft:
              selectedDrawingTab === FORGE_TAB
                ? `${leftMenuWidth}px`
                : undefined,
          }}
        >
          <Tabs
            options={drawingTabs}
            currentTabIndex={drawingTabs.indexOf(selectedDrawingTab)}
            onValueChanged={(item) => setSelectedDrawingTab(item)}
          />
        </div>
      ) : undefined}
      {loadedDrawing ? (
        (selectedDrawingTab === DRAWING_TAB ||
          selectedDrawingTab === PACKAGE_MAP_TAB) &&
        drawingTabs.includes(selectedDrawingTab) &&
        selectedDrawingId ? (
          <div
            className={`work pdf-viewer-wrapper fullscreen" ${
              navExpanded ? "narrow" : ""
            }`}
          >
            <div className="viewer">
              <PSPDFKitViewer
                selectedItem={loadedDrawing}
                itemId={selectedDrawingId}
                itemType={"DRAWING"}
                file={file}
                refresh={refreshDrawingPdf}
                area="WORK-VIEWER"
                token={token}
                userInfo={userInfo}
                hasPermission={permissions?.includes(111)}
                isArchived={loadedDrawing.archived ? true : false}
                storeInstanceOutside={setPdfInstance}
                showAnnotations={selectedDrawingTab === DRAWING_TAB}
                // TODO - update this for package map
                fileType={
                  selectedDrawingTab === DRAWING_TAB ? "ANNOTATED" : "ORIGINAL"
                }
              />
            </div>
          </div>
        ) : (
          selectedDrawingTab === FORGE_TAB &&
          drawingTabs.includes(selectedDrawingTab) && (
            <div className={"work-forge-viewer"}>
              <ForgeViewer
                appType="myWork"
                token={token}
                userId={userId}
                systemSettings={systemSettings}
                permissions={permissions}
                origin="app"
                navExpanded={navExpanded}
                setRightPanel={setRightPanel}
                selectedDrawingId={selectedDrawingId}
                setSelectedDrawingId={setSelectedDrawingId}
                displayedDrawings={displayedDrawings}
                forgeModelId={forgeModelId}
              />
            </div>
          )
        )
      ) : (
        <div className="drawing-viewer-wrapper viewer-empty">
          <p>No selected drawing</p>
        </div>
      )}
    </div>
  );
};

export default DrawingViewer;
