// NPM PACKAGE IMPORTS
import React, { useCallback, useEffect, useState } from "react";
import jwt from "jsonwebtoken";
import PropTypes from "prop-types";

// HELPER FUNCTION IMPORTS
import usePrevious from "../../../hooks/usePrevious";

const ForgeViewer = ({
  appType = "myWork",
  token,
  userId,
  systemSettings,
  permissions,
  navExpanded = false,
  setRightPanel = (f) => f,
  selectedDrawingId = 0,
  setSelectedDrawingId = (f) => f,
  displayedDrawings = [],
  origin = "web",
  forgeModelId = 0,
}) => {
  const [isLoading, setIsLoading] = useState(true); // default to true to not try and access vars/fns while loading
  const [selectionOutsideMarkups, setSelectionOutsideMarkups] = useState([]);
  const [key, setKey] = useState(1);

  const previousSelectedDrawing = usePrevious(
    displayedDrawings?.find((d) => d.id === selectedDrawingId) ?? {}
  );

  let zohoContactId = 1;
  let systemFeatures = [];
  if (token) {
    let tokenData = jwt.decode(token).data;
    zohoContactId = tokenData.zohoContactId;
    systemFeatures = tokenData.features;
  }

  // init forge viewer
  const onIframeLoaded = useCallback(() => {
    const forgeViewerIframe = document.getElementById(
      "work-forge-viewer-iframe"
    );
    if (!forgeViewerIframe?.contentWindow) return;

    // pass in only values we want to set, defaults has fallbacks
    forgeViewerIframe.contentWindow.postMessage(
      {
        function: `
      createScripts({
        jwt: '${token}',
        api_path: '${process.env.REACT_APP_API}',
        drawing: ${JSON.stringify(
          displayedDrawings?.find((d) => d.id === selectedDrawingId) ?? {}
        )},
        spoolId: ${JSON.stringify(selectedDrawingId)},
        itemIds: ${JSON.stringify(displayedDrawings?.map((o) => o.id) ?? [])},
        spoolIds: [${JSON.stringify(selectedDrawingId)}],
        appType: '${appType}',
        env: '${process.env.REACT_APP_ENVIRONMENT}',
        origin: '${origin}',
        systemFeatures: ${JSON.stringify(systemFeatures)},
        userId: ${JSON.stringify(userId)},
        permissions: ${JSON.stringify(permissions)},
        fabPath: '${process.env.REACT_APP_FABPRO}',
        zoho_account_id: ${JSON.stringify(systemSettings.zoho_account_id)},
        zohoContactId: ${JSON.stringify(zohoContactId)},
        churn_app_key: '${process.env.REACT_APP_CHURN_APP_KEY}',
        forgeModelId: ${forgeModelId},
        userBehaviorTrackingToken: '${
          process.env.REACT_APP_USER_BEHAVIOR_TRACKING_TOKEN
        }',
      });
    `,
      },
      process.env.REACT_APP_FABPRO
    );
  }, [selectedDrawingId]);

  const messageCallback = useCallback(
    (e) => {
      if (typeof e.data === "string") {
        if (e.data === "initial load complete") {
          if (isLoading) setIsLoading(false);
        } else if (e.data === "markup_opened") {
          setRightPanel(false);
        } else if (e.data === "markup_closed") {
          setRightPanel(true);
        } else {
          setIsLoading(false);
        }
      }
    },
    [selectedDrawingId, isLoading]
  );

  useEffect(() => {
    setRightPanel(true);

    const forgeViewerIframe = document.getElementById(
      "work-forge-viewer-iframe"
    );

    return () => {
      forgeViewerIframe?.contentWindow?.postMessage({
        function: `if (viewer) {
          viewer.finish();
          viewer = null;
        }`,
      });
    };
  }, []);

  useEffect(() => {
    window.addEventListener("message", messageCallback, true);

    return () => window.removeEventListener("message", messageCallback, true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedDrawingId, isLoading]);

  useEffect(() => {
    const forgeViewerIframe = document.getElementById(
      "work-forge-viewer-iframe"
    );

    // refresh model on selection change
    if (selectedDrawingId && !isLoading && forgeViewerIframe) {
      forgeViewerIframe.contentWindow.postMessage(
        {
          function: `
        defaults.drawing = ${JSON.stringify(
          displayedDrawings?.find((d) => d.id === selectedDrawingId)
        )};
        defaults.spoolId = defaults.drawing.id;
        defaults.spoolIds = [defaults.drawing.id];
        viewer?.loadNewSheet(defaults.drawing.name);
      `,
        },
        process.env.REACT_APP_FABPRO
      );
    }
  }, [selectedDrawingId]);

  // update model by refreshing iframe if urn changes
  useEffect(() => {
    let spool =
      displayedDrawings?.find((d) => d.id === selectedDrawingId) ?? {};

    if (
      spool &&
      previousSelectedDrawing &&
      spool.forge_urn !== previousSelectedDrawing.forge_urn
    ) {
      setKey(key + 1);
    }
  }, [displayedDrawings, selectedDrawingId, key]);

  return (
    <iframe
      src={`${process.env.REACT_APP_FABPRO}/js/Forge/core.html`}
      title="work-forge-viewer-iframe"
      name="work-forge-viewer-iframe"
      id="work-forge-viewer-iframe"
      className={`${navExpanded ? "narrow" : ""}`}
      onLoad={onIframeLoaded}
      key={key}
    ></iframe>
  );
};

export default ForgeViewer;

ForgeViewer.propTypes = {
  // GENERAL
  appType: PropTypes.oneOf(["fab", "field", "myWork"]),
  token: PropTypes.string, // REQUIRED
  userId: PropTypes.number, // REQUIRED
  systemSettings: PropTypes.shape({
    // REQUIRED
    zoho_account_id: PropTypes.string,
  }),
  permissions: PropTypes.array, // REQUIRED
  origin: PropTypes.oneOf(["web", "app", "field"]),

  spool: PropTypes.shape({
    type: PropTypes.string,
    id: PropTypes.number,
    drawing_id: PropTypes.number,
    name: PropTypes.string,
    drawing_name: PropTypes.string,
    forge_urn: PropTypes.string,
    model_name: PropTypes.string,
    job_id: PropTypes.number,
    package_id: PropTypes.number,
  }),
  navExpanded: PropTypes.bool,

  setRightPanel: PropTypes.func,
  selectedDrawingId: PropTypes.number,
  setSelectedDrawingId: PropTypes.func,
  displayedDrawings: PropTypes.arrayOf(PropTypes.object),
};
