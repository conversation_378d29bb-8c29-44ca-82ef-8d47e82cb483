import React from "react";

// STYLES IMPORTS
import "./stylesMyWorkDropdown.scss";

const MyWorkDropdown = ({ itemList, selectedItem, updateSelectedItem }) => {
  return (
    <div className={"mywork-dropdown-wrapper"}>
      <div className={"mywork-dropdown"}>
        <ul>
          {itemList.map((i, index) => (
            <li
              className={selectedItem === i ? "selected" : ""}
              key={index}
              onClick={() => updateSelectedItem(i)}
            >
              {i}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default MyWorkDropdown;
