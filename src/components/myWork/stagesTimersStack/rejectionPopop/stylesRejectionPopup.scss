@import "../../../styles/colors.scss";

// div.stages-timer-wrapper {
div.rejection-popup-wrapper {
  width: 230px;
  min-height: 220px;
  position: fixed;
  top: 300px;
  right: 10%;
  background-color: black;
  padding: 15px 10px 10px;
  border-radius: 5px;

  & h4 {
    color: white !important;
    font-size: 1.1rem;
    padding-top: 0;
  }

  & select {
    border: none;
    background-color: transparent;
    color: $blue;
    margin-top: 5px;
    cursor: pointer;
    height: 28px;
    font-size: 1rem;

    &:focus {
      outline: none;
      border: none;
    }
  }

  span.rejection-type-selector {
    display: flex;
    flex-direction: column;
    margin-top: 10px;

    & label {
      color: $textGrey;
      font-size: 0.7rem;
    }
  }

  & p {
    color: $textGrey;
    font-size: 0.7rem;
    text-align: center;
    margin-top: 20px;
    padding: 0;
    margin-bottom: 0;
  }

  & p.scrap-warning {
    color: $red;
    text-align: left;
  }

  & .rejection-form {
    width: 95%;
    margin: 0 auto;
    & input {
      background-color: $inputDark;
      border: none;
      height: 32px;
      width: 215px;
      margin: 5px 0;
      box-sizing: border-box;
      color: white;

      &:disabled {
        background-color: darken($rowEven, 8%);
        color: darken(white, 60%);
      }

      &:focus {
        border: none;
        outline: none;
      }
    }
  }

  & div.button-row {
    margin-top: 35px;
    display: flex;
    justify-content: space-evenly;

    & button {
      border: none;
      background-color: transparent;
      color: $blue;
      font-size: 1rem;
      cursor: pointer;

      &:disabled {
        color: $textGrey;
      }
    }
    & button.cancel {
      color: $red;
    }
  }

  & input[type="text"] {
    padding-left: 5px;
  }
}
