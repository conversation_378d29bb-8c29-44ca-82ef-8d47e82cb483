// NPM PACKAGE IMPORTS
import React, { useState, useRef, useMemo, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import {
  handleFetchSentBackStages,
  handleRejectItems,
} from "../../../items/itemsActions";
import {
  handleStartTimerv2,
  handleAddItemsToTimerv2,
} from "../../../timers/timersActions";
import { showErrorModal } from "../../../reusable/generalModal/generalModalActions";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../../hooks/useOutsideClick";

// STYLES IMPORTS
import "./stylesRejectionPopup.scss";

const RejectionPopup = ({
  itemToReject,
  isGrouped,
  closePopup,
  rejectionCategories,
  selectedStage,
  toggleRejectionPopup,
  refreshTable,
  setSelectedRows,
  gridOptionsApi,
  activeItemsRef,
  groupingType,
}) => {
  const [rejectionCategoryId, setRejectionCategoryId] = useState(null);
  const [rejectionType, setRejectionType] = useState(null);
  const [newStage, setNewStage] = useState(null);
  const [description, setDescription] = useState("");
  const [quantityToReject, setQuantityToReject] = useState(null);

  const dispatch = useDispatch();
  const { shiftNow } = useSelector((state) => state.profileData);
  const { sentBackStages } = useSelector((state) => state.itemsData);
  const { activeTimer } = useSelector((state) => state.timerData);

  const popupRef = useRef(null);
  useOutsideClick(popupRef, () => closePopup());

  useEffect(() => {
    if (!itemToReject) return;
    dispatch(handleFetchSentBackStages(itemToReject.id, selectedStage.id));
    setQuantityToReject(isGrouped ? itemToReject.quantity.toFixed(1) : null);
  }, [itemToReject, selectedStage]);

  const disableSave = useMemo(() => {
    if (isGrouped && !quantityToReject) return true;
    return (
      !rejectionCategoryId ||
      !rejectionType ||
      (rejectionType === "rework" && !newStage)
    );
  }, [rejectionCategoryId, rejectionType, newStage, quantityToReject]);

  const handleRejection = (timerId, rejectionQuantity) => {
    dispatch(
      handleRejectItems(
        itemToReject.id.toString(),
        parseInt(rejectionCategoryId),
        rejectionType.toLowerCase(),
        selectedStage.id,
        parseInt(newStage),
        description,
        timerId,
        isGrouped ? parseFloat(rejectionQuantity) : null
      )
    ).then(() => {
      refreshTable();
    });
    toggleRejectionPopup(false);
    setSelectedRows([]);
  };

  const onSave = () => {
    // If Advanced Grouping, send null as quantity to reject so it rejects whole grouped row
    const updatedQuantityToReject =
      groupingType === "Advanced Grouping" ? null : quantityToReject;

    if (!activeItemsRef.current || !activeItemsRef.current.length) {
      dispatch(
        handleStartTimerv2([itemToReject.id], shiftNow.id, selectedStage.id)
      ).then((res) => {
        if (res && res.error) {
          dispatch(showErrorModal(res.error.message));
          return;
        }

        setTimeout(() => {
          handleRejection(
            res.payload.timer[0].timer_id,
            updatedQuantityToReject
          );
        }, 1000);
      });
    } else if (
      activeItemsRef.current &&
      activeItemsRef.current.length &&
      selectedStage.id === activeItemsRef.current[0].stage_id &&
      !activeItemsRef.current
        .map((i) => i.work_item_id)
        .includes(itemToReject.id)
    ) {
      dispatch(
        handleAddItemsToTimerv2(
          [itemToReject.id],
          shiftNow.id,
          selectedStage.id,
          activeTimer[0].timer_id
        )
      ).then((res) => {
        if (
          res &&
          res.payload &&
          res.payload.error &&
          res.payload.error.message
        ) {
          dispatch(showErrorModal(res.error.message));
          return;
        }

        handleRejection(activeTimer[0].timer_id, updatedQuantityToReject);
      });
    } else {
      handleRejection(activeTimer[0].timer_id, updatedQuantityToReject);
    }
  };

  return (
    <div ref={popupRef} className="rejection-popup-wrapper">
      {itemToReject && (
        <h4>{`Reject Type ${itemToReject.material_name}: ${itemToReject.drawing_name}`}</h4>
      )}

      <span className="rejection-type-selector">
        <label>Category:</label>
        <select onChange={(e) => setRejectionCategoryId(e.target.value)}>
          <option disabled hidden selected>
            Select Rejection Category
          </option>
          {rejectionCategories &&
            rejectionCategories.length &&
            rejectionCategories.map((c) => (
              <option key={c.id} value={c.id}>
                {c.name}
              </option>
            ))}
        </select>
      </span>

      {rejectionCategoryId && (
        <span className="rejection-type-selector">
          <label>Scrap or Rework</label>
          <select onChange={(e) => setRejectionType(e.target.value)}>
            <option disabled hidden selected>
              Select Scrap or Rework
            </option>
            <option value="scrap">Scrap</option>
            {sentBackStages && sentBackStages.length && (
              <option value="rework">Rework</option>
            )}
          </select>
        </span>
      )}

      {rejectionType === "rework" && (
        <span className="rejection-type-selector">
          <label>Send back to stage:</label>
          <select onChange={(e) => setNewStage(e.target.value)}>
            <option disabled hidden selected>
              Select stage to send back to
            </option>
            {sentBackStages &&
              sentBackStages.length &&
              sentBackStages.map((s) => (
                <option value={s.id} key={s.id}>
                  {s.name}
                </option>
              ))}
          </select>
        </span>
      )}

      {rejectionType === "scrap" && (
        <p className="scrap-warning">
          Scrap resets the whole drawing. Does not need to be re-approved.
        </p>
      )}

      <div className="rejection-form">
        {isGrouped ? (
          <p>Enter a quantity, description is optional.</p>
        ) : (
          <p>Description is optional.</p>
        )}
        {isGrouped && (
          <input
            value={quantityToReject}
            onChange={(e) => setQuantityToReject(e.target.value)}
            name="quantity"
            type="number"
            placeholder="quantity"
            disabled={groupingType === "Advanced Grouping"}
          />
        )}
        <input
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Description"
          name="description"
          type="text"
        />
      </div>

      <div className="button-row">
        <button onClick={closePopup} className="cancel" type="button">
          Cancel
        </button>
        <button
          onClick={onSave}
          disabled={disableSave}
          className={disableSave ? "disabled-save" : "save"}
          type="button"
        >
          Save
        </button>
      </div>
    </div>
  );
};

export default RejectionPopup;
