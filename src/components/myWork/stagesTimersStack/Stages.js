import React, { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import { handleFetchWorkableStages } from "../myWorkActions";

// COMPONENT IMPORTS
import InlineFilter from "../../reusable/inlineFilter/InlineFilter";
import Tabs from "./../../reusable/tabs/Tabs";
import SingleCellTable from "./SingleCellTable";

// HELPER FUNCTION IMPORTS
import usePrevious from "../../../hooks/usePrevious";

// STYLES IMPORTS
import "./stylesStages.scss";

const Stages = ({
  selectedDrawingIds,
  selectedJobs,
  selectedPackages,
  selectedStages,
  setSelectedStages,
  isViewingAllItems,
  toggleViewingAllItems,
  setSelectedStage,
  savedJobsFilter,
  savedPackagesFilter,
  allFiltersSet,
}) => {
  const dispatch = useDispatch();
  const { workableStages } = useSelector((state) => state.myWorkData);
  const prevSelected = usePrevious({ isViewingAllItems, selectedDrawingIds });

  useEffect(() => {
    if (allFiltersSet && !savedJobsFilter && !savedPackagesFilter)
      dispatch(handleFetchWorkableStages());
  }, [allFiltersSet]);

  // update on selected drawing id changes if on selected and on and all/selected tab changes
  useEffect(() => {
    if (
      prevSelected &&
      (prevSelected.isViewingAllItems !== isViewingAllItems ||
        (!isViewingAllItems && selectedDrawingIds.length)) &&
      allFiltersSet
    ) {
      let jobIds = selectedJobs.map((j) => j.id),
        packageIds = selectedPackages.map((p) => p.id),
        drawingIds = !isViewingAllItems ? selectedDrawingIds : [];
      dispatch(handleFetchWorkableStages(jobIds, packageIds, drawingIds));
    }
  }, [selectedDrawingIds, isViewingAllItems, allFiltersSet]);

  const f_workableStages = useMemo(() => {
    if (!workableStages) return [];
    return workableStages.filter((s) => s.shipping_block_id === null);
  }, [workableStages]);

  const StageFilter = () => (
    <div className="filters-wrapper">
      <InlineFilter
        nameKey="name"
        type="Stages"
        isMulti={true}
        list={f_workableStages}
        selected={selectedStages}
        handleParentSelect={setSelectedStages}
        noSort
      />
    </div>
  );

  return (
    <div>
      <StageFilter />
      <Tabs
        options={["All", "Selected"]}
        currentTabIndex={
          !isViewingAllItems && selectedDrawingIds.length ? 1 : 0
        }
        isDisabled={!selectedDrawingIds.length}
        onValueChanged={(item, index) =>
          toggleViewingAllItems(index === 1 ? false : true)
        }
      />
      {(selectedStages.length &&
        f_workableStages.filter((s) =>
          selectedStages.map((s) => s.id).includes(s.id)
        ).length) ||
      f_workableStages.length ? (
        <SingleCellTable
          items={
            selectedStages.length
              ? f_workableStages.filter((s) =>
                  selectedStages.map((s) => s.id).includes(s.id)
                )
              : f_workableStages
          }
          noIcon={false}
          onSelect={(stage) => {
            setSelectedStage(stage);
          }}
          rainbow={true}
        />
      ) : (
        <div className="no-stages-message">
          <span>
            <p>No stages with work for current filters</p>
          </span>
        </div>
      )}
    </div>
  );
};

export default Stages;
