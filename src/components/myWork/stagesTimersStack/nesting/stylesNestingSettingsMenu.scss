@import "./../../../styles/colors.scss";
.nesting-settings {
  border: 1px solid $blue;
  height: 300px;
  width: 280px;
  position: absolute;
  z-index: 50;
  color: $textLight;
  background-color: $bgLightDarkGrey;
  text-align: center;
  font-family: sans-serif;
  cursor: default;

  overflow-y: scroll;
  scrollbar-width: none; /* Firefox 64 */
  -ms-overflow-style: none; /* Microsoft Edge */
  &::-webkit-scrollbar {
    display: none;
  }

  .title {
    font-weight: bold;
    font-size: 18px;
    margin: 16px;
  }

  .save {
    color: $blue;
    font-size: 12px;
    padding: 10px;
    width: 100px;
    margin: 10px auto;
    cursor: pointer;
  }

  .mywork-dropdown {
    display: flex;
    flex-direction: column;
    color: $textLight;
    background-color: $rowEven;

    .selected {
      background-color: $blue;
    }

    ul {
      list-style-type: none;
      margin: 0;
      padding: 0;

      li {
        padding: 10px;
      }
    }
  }
}
