import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  FiChevronDown,
  FiChevronRight,
  FiSettings,
  FiSlash,
} from "react-icons/fi";
import { RiBarricadeLine } from "react-icons/ri";
import Switch from "react-switch";

// REDUX IMPORTS
import {
  handleUpdateNestingLength,
  handleUpdateNestedItems,
  handleUpdateSelectedMaterial,
  handleUpdateSelectedSize,
  handleUpdateNestingOff,
  handleUpdateNestingOn,
  handleUpdateNestingOpen,
  handleUpdateAbsLengthInches,
} from "./nestingActions";
import { notify } from "../../../reusable/alertPopup/alertPopupActions";

// COMPONENT IMPORTS
import MyWorkDropdownButton from "../../MyWorkDropdownButton";
import NestingSettingsMenu from "./NestingSettingsMenu";

// HELPER FUNCTION IMPORTS
import { lengthParse, sanitize, nestCuts } from "../../../../_utils";
import useOutsideClick from "../../../../hooks/useOutsideClick";

// STYLES IMPORTS
import "./stylesNestingCollapsibleMenu.scss";

const NestingCollapsibleMenu = ({
  selectedStage,
  activeTimer,
  shouldRenest,
  setShouldRenest,
  isEditing,
  isDisabled,
}) => {
  const dispatch = useDispatch();
  const {
    absLengthInches,
    items,
    length,
    material,
    size,
    on,
    open,
  } = useSelector((state) => state.nestingData);
  const { workableItems } = useSelector((state) => state.itemsData);
  const { permissions } = useSelector((state) => state.profileData);
  const { machines } = useSelector((state) => state.nestingData);

  const inputRef = useRef(null);
  const editPopupRef = useRef(null);
  const [currentMachine, setCurrentMachine] = useState(null);
  const [openSettings, setOpenSettings] = useState(false);
  useOutsideClick(editPopupRef, () => setOpenSettings(false));
  const [materialList, setMaterialList] = useState([]);
  const [sizeList, setSizeList] = useState([]);
  const [displayLength, setDisplayLength] = useState("");
  const [displayParsedLength, setDisplayParsedLength] = useState(false);
  // stage Settings should presist on page reload
  const [stageSettings, setStageSettings] = useStickyState({}, "stageSettings");

  useEffect(() => {
    let machine = machines.find(
      (m) => !m.serial_number && m.work_stage_id === selectedStage.id
    );
    if (machine) setCurrentMachine(machine);
  }, [machines]);

  useEffect(() => {
    dispatch(handleUpdateSelectedMaterial(""));
    dispatch(handleUpdateSelectedSize(""));
    // filter size based on material selected

    setMaterialList([
      ...new Set([
        ...workableItems
          .filter((i) => i.material_name)
          .map((i) => i.material_name),
      ]),
    ]);
    setSizeList([
      ...new Set([
        ...workableItems
          .filter((i) =>
            material ? i.material_name === material && i.size : i.size
          )
          .map((i) => i.size),
      ]),
    ]);
  }, [dispatch]);

  useEffect(() => {
    setMaterialList([
      ...new Set([
        ...workableItems
          .filter((i) => i.material_name && i.is_cut)
          .map((i) => i.material_name)
          .sort((a, b) => (a > b ? 1 : b > a ? -1 : 0)),
      ]),
    ]);
    setSizeList([
      ...new Set([
        ...workableItems
          .filter((i) =>
            material ? i.material_name === material && i.size : i.size
          )
          .map((i) => i.size),
      ]),
    ]);
    if (on && items.length) {
      let currentWorkItemIds = workableItems.map((i) => i.id);
      let currentItem;
      // want to set items as completed if they are no longer workable
      let newItems = items.map((i) => {
        currentItem = workableItems.find((o) => o.id === i.id);
        if (currentItem) {
          return {
            ...currentItem,
            is_completed: i.is_completed,
          };
        }
        return {
          ...i,
          is_completed:
            (i.id > 0 && !currentWorkItemIds.includes(i.id)) || i.is_completed,
        };
      });
      dispatch(handleUpdateNestedItems(newItems));
    }
  }, [workableItems]);

  useEffect(() => {
    setSizeList([
      ...new Set([
        ...workableItems
          .filter((i) =>
            material ? i.material_name === material && i.size : i.size
          )
          .map((i) => i.size),
      ]),
    ]);
  }, [material]);

  useEffect(() => {
    setDisplayLength(lengthParse(length).display);
  }, [length]);

  useEffect(() => {
    if (shouldRenest) {
      if (items.length && items.every((i) => i.is_completed)) {
        dispatch(handleUpdateNestingOff());
      } else {
        setShouldRenest(false);
      }
    }
  }, [shouldRenest, items]);

  useEffect(() => {
    if (!on && shouldRenest) {
      setShouldRenest(false);
      onNest();
    }
  }, [on]);

  const onToggleSettings = (type, e) => {
    if (stageSettings[selectedStage.id]) {
      if (type === "head") {
        setStageSettings({
          ...stageSettings,
          [selectedStage.id]: {
            ...stageSettings[selectedStage.id],
            hasHeadCut: e,
          },
        });
      } else if (type === "tail") {
        setStageSettings({
          ...stageSettings,
          [selectedStage.id]: {
            ...stageSettings[selectedStage.id],
            hasTailCut: e,
          },
        });
      }
    } else {
      if (type === "head") {
        setStageSettings({
          ...stageSettings,
          [selectedStage.id]: {
            stageId: selectedStage.id,
            hasHeadCut: e,
          },
        });
      } else if (type === "tail") {
        setStageSettings({
          ...stageSettings,
          [selectedStage.id]: {
            stageId: selectedStage.id,
            hasTailCut: e,
          },
        });
      }
    }
  };

  const handleMouseOutIn = (mouseIn) => {
    if (!mouseIn && document.activeElement === inputRef.current) {
      setDisplayParsedLength(true);
    } else if (mouseIn && document.activeElement === inputRef.current) {
      setDisplayParsedLength(false);
    }
  };

  // format length input to be "N N N N" for nesting
  const onUpdateLength = (e) => {
    dispatch(handleUpdateAbsLengthInches(lengthParse(e).value));
    dispatch(handleUpdateNestingLength(sanitize(e)));
  };

  const sendNotif = (type, message) => {
    dispatch(
      notify({
        id: Date.now(),
        type: type,
        message: message,
      })
    );
  };

  function useStickyState(defaultValue, key) {
    const [value, setValue] = React.useState(() => {
      const stickyValue = window.localStorage.getItem(key);
      return stickyValue !== null ? JSON.parse(stickyValue) : defaultValue;
    });
    React.useEffect(() => {
      window.localStorage.setItem(key, JSON.stringify(value));
    }, [key, value]);
    return [value, setValue];
  }

  const onNest = () => {
    if (isDisabled) return sendNotif("ERROR", "Unable to nest when grouped.");
    if (on) {
      dispatch(handleUpdateNestingOff());
    } else {
      if (activeTimer && activeTimer.length && activeTimer[0].work_item_id) {
        return sendNotif(
          "ERROR",
          "Before nesting, please stop your current active work."
        );
      }
      if (!material || !size) {
        return sendNotif("ERROR", "Material and size selection are required");
      }

      let DATA = workableItems.filter(
        (wi) => wi.material_name === material && wi.size === size
      );
      let maxLength = absLengthInches;
      // use machine settings if set, otherwise default to 0
      const MACHINE_SETTINGS =
        currentMachine && currentMachine.settings
          ? {
              "head cut": currentMachine.settings["head cut"]?.value ?? 0,
              "tail cut": currentMachine.settings["tail cut"]?.value ?? 0,
              kerf: currentMachine.settings["kerf"]?.value ?? 0,
              "min limit": currentMachine.settings["min limit"]?.value ?? 0,
              "min length": currentMachine.settings["min length"]?.value ?? 0,
            }
          : {
              "head cut": 0,
              "tail cut": 0,
              kerf: 0,
              "min limit": 0,
              "min length": 0,
            };
      let curStageSettings = stageSettings[selectedStage.id] || {};

      let nestedCutList = nestCuts(DATA, {
        shouldCutTail: curStageSettings.hasTailCut,
        shouldCutHead: curStageSettings.hasHeadCut,
        tailCutLength: MACHINE_SETTINGS["tail cut"],
        headCutLength: MACHINE_SETTINGS["head cut"],
        minLimit: MACHINE_SETTINGS["min limit"],
        minLength: MACHINE_SETTINGS["min length"],
        stockLength: maxLength,
        kerf: MACHINE_SETTINGS["kerf"],
      }).map((c) => {
        return { ...c, is_nest: true };
      });

      dispatch(handleUpdateNestedItems(nestedCutList));
      dispatch(handleUpdateNestingOn());
    }
  };

  return (
    <>
      {!isEditing && (
        <div
          className={`nesting-wrapper ${open ? "expanded" : ""} ${
            on ? "on" : ""
          }`}
        >
          <div
            className={`nesting-toggle ${open ? "expanded" : ""} ${
              on ? "on" : ""
            }`}
            onClick={() => {
              if (isDisabled && !open)
                return sendNotif("ERROR", "Unable to nest when grouped.");
              dispatch(handleUpdateNestingOpen());
            }}
          >
            {open ? <FiChevronDown /> : <FiChevronRight />}
            <span>Nesting</span>
          </div>
          {open ? (
            <div>
              <div className={"nesting-switches"}>
                <div>
                  <span>Head Cut</span>
                  <Switch
                    offColor="#253137"
                    onColor="#2196f3"
                    onChange={(e) => onToggleSettings("head", e)}
                    checked={
                      stageSettings[selectedStage.id]
                        ? stageSettings[selectedStage.id].hasHeadCut === true
                        : false
                    }
                    height={18}
                    width={40}
                    handleDiameter={24}
                    boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                    activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                    checkedIcon={false}
                    uncheckedIcon={false}
                    disabled={on}
                  />
                </div>
                <div>
                  <span>Tail Cut</span>
                  <Switch
                    offColor="#253137"
                    onColor="#2196f3"
                    onChange={(e) => onToggleSettings("tail", e)}
                    checked={
                      stageSettings[selectedStage.id]
                        ? stageSettings[selectedStage.id].hasTailCut === true
                        : false
                    }
                    height={18}
                    width={40}
                    handleDiameter={24}
                    boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                    activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                    checkedIcon={false}
                    uncheckedIcon={false}
                    disabled={on}
                  />
                </div>
                {permissions && permissions.includes(300) && (
                  <div
                    ref={editPopupRef}
                    className="settings-toggle"
                    onClick={
                      on || openSettings
                        ? (_) => _
                        : () => setOpenSettings(true)
                    }
                  >
                    <FiSettings size="1.5rem" />
                    {openSettings && (
                      <NestingSettingsMenu
                        selectedStage={selectedStage}
                        onSave={(_) => setOpenSettings(false)}
                        currentMachine={currentMachine}
                        machines={machines}
                      />
                    )}
                  </div>
                )}
              </div>
              <MyWorkDropdownButton
                itemList={materialList}
                selectedItem={material}
                updateSelectedItem={(m) =>
                  dispatch(handleUpdateSelectedMaterial(m))
                }
                title={"MATERIAL"}
                disabled={on}
              />
              <MyWorkDropdownButton
                itemList={sizeList}
                selectedItem={size}
                updateSelectedItem={(s) =>
                  dispatch(handleUpdateSelectedSize(s))
                }
                title={"SIZE"}
                disabled={on}
              />
              <div className={"nest-length"}>
                {length ? (
                  <label htmlFor="length">{`Length - Ex: 2 3 5 8 => 2' 3" 5 / 8`}</label>
                ) : undefined}
                <input
                  ref={inputRef}
                  autoComplete={"off"}
                  type="text"
                  id="length"
                  name="length"
                  placeholder="Length"
                  value={length}
                  onChange={(e) => onUpdateLength(e.target.value)}
                  onMouseOut={(_) => handleMouseOutIn(false)}
                  onMouseEnter={(_) => handleMouseOutIn(true)}
                  disabled={on}
                />
                {displayParsedLength ? (
                  <div className={"parsed-length"}>{displayLength}</div>
                ) : undefined}
              </div>
              <div className={`nest-button ${on ? "on" : ""}`} onClick={onNest}>
                {on ? (
                  <FiSlash size="1.5em" />
                ) : (
                  <RiBarricadeLine size="1.5em" />
                )}
                <span>{on ? "Cancel" : "Nest"}</span>
              </div>
            </div>
          ) : undefined}
        </div>
      )}
    </>
  );
};

export default NestingCollapsibleMenu;
