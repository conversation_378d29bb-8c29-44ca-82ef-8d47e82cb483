import React, { useState, useEffect } from "react";
import { FiMinus, FiPlus } from "react-icons/fi";

// STYLES IMPORTS
import "./stylesValueStepper.scss";

const ValueStepper = ({
  startingValue,
  minValue,
  maxValue,
  stepValue,
  onValueChange,
  label,
}) => {
  const [currentValue, setCurrentValue] = useState(0);

  useEffect(() => {
    setCurrentValue(startingValue);
  }, [startingValue]);

  const changeValue = (isIncrement) => {
    let newValue = isIncrement
      ? currentValue + stepValue
      : currentValue - stepValue;
    if (
      (isIncrement && newValue <= maxValue) ||
      (!isIncrement && newValue >= minValue)
    ) {
      onValueChange(newValue);
    }
  };

  return (
    <div className={`value-stepper`}>
      {label ? <span>{label}</span> : undefined}
      <div>
        <div className={"stepper-icon"} onClick={(_) => changeValue(false)}>
          <FiMinus />
        </div>
        <div className={"stepper-value"}>{currentValue}</div>
        <div className={"stepper-icon"} onClick={(_) => changeValue(true)}>
          <FiPlus />
        </div>
      </div>
    </div>
  );
};

export default ValueStepper;
