const initialState = {
  isLoading: false,
  currentStock: 0,
  absLengthInches: 0,
  items: [],
  length: "",
  material: "",
  size: "",
  on: false,
  open: false,
  machines: [],
};

const SETTINGS = {
  d23: "kerf",
  d24: "head cut",
  d25: "tail cut",
  d10: "max length",
  d11: "min limit",
  no: "min length",
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "UPDATE_CURRENT_STOCK":
      return { ...state, currentStock: payload };
    case "UPDATE_ABS_LENGTH_INCHES":
      return { ...state, absLengthInches: payload };
    case "UPDATE_NESTED_ITEMS":
      return { ...state, items: payload };
    case "UPDATE_NESTING_LENGTH":
      return { ...state, length: payload };
    case "UPDATE_SELECTED_MATERIAL":
      return { ...state, material: payload === state.material ? "" : payload };
    case "UPDATE_SELECTED_SIZE":
      return { ...state, size: payload === state.size ? "" : payload };
    case "UPDATE_NESTING_OPEN":
      return { ...state, open: !state.open };
    case "UPDATE_NESTING_ON":
      return { ...state, on: true };
    case "UPDATE_NESTING_OFF":
      return { ...state, items: [], on: false };
    case "RECEIVE_CUTTING_MACHINES_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_CUTTING_MACHINES_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        machines: payload,
      };
    case "RECEIVE_CUTTING_MACHINES_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_CUTTING_MACHINE_SETTINGS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_CUTTING_MACHINE_SETTINGS_SUCCEEDED":
      // map settings to its machine
      let newMachines = state.machines.map((m) => {
        return { ...m, settings: {} };
      });
      let index = -1;
      payload.forEach((s) => {
        index = newMachines.findIndex((m) => m.id === s.machine_id);
        newMachines[index].settings[SETTINGS[s.setting]] = s;
      });
      return {
        ...state,
        isLoading: false,
        error: null,
        machines: newMachines,
      };
    case "UPDATE_CUTTING_MACHINES_STARTED":
      return { ...state, isLoading: true, error: null };
    case "UPDATE_CUTTING_MACHINES_SUCCEEDED":
      return state;
    case "UPDATE_CUTTING_MACHINES_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "UPDATE_CUTTING_MACHINE_SETTINGS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "UPDATE_CUTTING_MACHINE_SETTINGS_SUCCEEDED":
      return { ...state, isLoading: false };
    case "UPDATE_CUTTING_MACHINE_SETTINGS_FAILED":
      return { ...state, isLoading: false, error: payload };
    default:
      return state;
  }
}
