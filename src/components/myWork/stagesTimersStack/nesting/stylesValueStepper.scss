@import "../../../styles/colors.scss";

.value-stepper {
  display: flex;
  margin: 16px;
  align-items: center;
  font-size: 12px;

  & > span {
    margin-right: auto;
    color: $textGreyAlt;
  }

  & > div {
    display: flex;
    width: 130px;
    margin-left: auto;
  }

  .stepper-icon {
    border: 1px solid $blue;
    background-color: $blue;
    padding: 5px 8px;
    border-radius: 0 5px 5px 0;
    cursor: pointer;

    &:first-child {
      border-radius: 5px 0 0 5px;
    }
  }

  .stepper-value {
    border: 1px solid $blue;
    padding-top: 5px;
    width: 110px;
  }
}
