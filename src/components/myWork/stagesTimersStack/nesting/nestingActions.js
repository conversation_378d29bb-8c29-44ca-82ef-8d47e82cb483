import {
  fetchAllCuttingMachines,
  fetchAllCuttingMachineSettings,
  createCuttingMachine,
  createUpdateCuttingMachineSettings,
} from "../../../../_services";
import { trackMixPanelEvent } from "../../../../utils/_mixPanelUtils";

export const updateCurrentStock = (stock) => ({
  type: "UPDATE_CURRENT_STOCK",
  payload: stock,
});
export const updateAbsLengthInches = (absLengthInches) => ({
  type: "UPDATE_ABS_LENGTH_INCHES",
  payload: absLengthInches,
});
export const updateNestedItems = (items) => ({
  type: "UPDATE_NESTED_ITEMS",
  payload: items,
});
export const updateNestingLength = (length) => ({
  type: "UPDATE_NESTING_LENGTH",
  payload: length,
});
export const updateSelectedMaterial = (material) => ({
  type: "UPDATE_SELECTED_MATERIAL",
  payload: material,
});
export const updateSelectedSize = (size) => ({
  type: "UPDATE_SELECTED_SIZE",
  payload: size,
});
export const updateNestingOpen = () => ({
  type: "UPDATE_NESTING_OPEN",
});
export const updateNestingOn = () => ({
  type: "UPDATE_NESTING_ON",
});
export const updateNestingOff = () => ({
  type: "UPDATE_NESTING_OFF",
});
export const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});
export const updateStarted = (type) => ({ type: `UPDATE_${type}_STARTED` });
export const updateSucceeded = (type, payload) => ({
  type: `UPDATE_${type}_SUCCEEDED`,
  payload,
});
export const updateFailed = (type, error) => ({
  type: `UPDATE_${type}_FAILED`,
  payload: error,
});

export const handleFetchAllCuttingMachines = () => (dispatch) => {
  const type = "CUTTING_MACHINES";
  dispatch(receiveStarted(type));
  return fetchAllCuttingMachines().then((res) => {
    if (res.error) {
      dispatch(receiveFailed(type, res));
    } else {
      dispatch(receiveSucceeded(type, res));
      dispatch(handleFetchAllCuttingMachineSettings(res.map((m) => m.id)));
    }
    return res;
  });
};

export const handleFetchAllCuttingMachineSettings = (machineIds) => (
  dispatch
) => {
  const type = "CUTTING_MACHINE_SETTINGS";
  dispatch(receiveStarted(type));
  return fetchAllCuttingMachineSettings(machineIds).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleCreateCuttingMachine = (machineData) => (dispatch) => {
  const type = "CUTTING_MACHINES";
  dispatch(updateStarted(type));
  return createCuttingMachine(machineData).then((res) => {
    if (res.error) dispatch(updateFailed(type, res));
    else {
      dispatch(updateSucceeded(type, res));
      dispatch(
        handleCreateUpdateCuttingMachineSettings(
          { id: res.machine_id },
          false,
          true,
          false,
          () => dispatch(handleFetchAllCuttingMachines())
        )
      );
    }
    return res;
  });
};

export const handleCreateUpdateCuttingMachineSettings = (
  machine,
  settings,
  useDefault,
  isNew,
  callback
) => (dispatch) => {
  const type = "CUTTING_MACHINE_SETTINGS";
  dispatch(updateStarted(type));
  return createUpdateCuttingMachineSettings(
    machine,
    settings,
    useDefault,
    isNew
  ).then((res) => {
    if (res.error) {
      dispatch(updateFailed(type, res));
    } else {
      dispatch(updateSucceeded(type, res));
      if (callback && typeof callback === "function") callback();
    }

    return res;
  });
};

export const handleUpdateCurrentStock = (stock) => (dispatch) =>
  dispatch(updateCurrentStock(stock));

export const handleUpdateAbsLengthInches = (absLengthInches) => (dispatch) =>
  dispatch(updateAbsLengthInches(absLengthInches));

export const handleUpdateNestedItems = (items) => (dispatch) =>
  dispatch(updateNestedItems(items));

export const handleUpdateNestingLength = (length) => (dispatch) =>
  dispatch(updateNestingLength(length));

export const handleUpdateSelectedMaterial = (material) => (dispatch) =>
  dispatch(updateSelectedMaterial(material));

export const handleUpdateSelectedSize = (size) => (dispatch) =>
  dispatch(updateSelectedSize(size));

export const handleUpdateNestingOpen = () => (dispatch) =>
  dispatch(updateNestingOpen());

export const handleUpdateNestingOn = () => (dispatch) => {
  dispatch(updateNestingOn());
  trackMixPanelEvent("Nest Created", 1, "Nests");
};

export const handleUpdateNestingOff = () => (dispatch) =>
  dispatch(updateNestingOff());
