import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import {
  handleCreateUpdateCuttingMachineSettings,
  handleFetchAllCuttingMachineSettings,
} from "./nestingActions";

// COMPONENT IMPORTS
import ValueStepper from "./ValueStepper";

// STYLES IMPORTS
import "./stylesNestingSettingsMenu.scss";

const NestingSettingsMenu = ({
  selectedStage,
  onSave,
  currentMachine,
  machines,
}) => {
  const dispatch = useDispatch();
  const [currentHead, setCurrentHead] = useState(0);
  const [currentTail, setCurrentTail] = useState(0);
  const [currentMinLimit, setCurrentMinLimit] = useState(0);
  const [currentMinLength, setCurrentMinLength] = useState(0);
  const [currentKerf, setCurrentKerf] = useState(0);

  useEffect(() => {
    if (currentMachine && currentMachine.settings) {
      setCurrentHead(currentMachine.settings["head cut"].value);
      setCurrentTail(currentMachine.settings["tail cut"].value);
      setCurrentMinLimit(currentMachine.settings["min limit"].value);
      setCurrentMinLength(currentMachine.settings["min length"]?.value ?? 0);
      setCurrentKerf(currentMachine.settings["kerf"].value);
    } else {
      //set to 0s if machine is not set
      setCurrentHead(0);
      setCurrentTail(0);
      setCurrentMinLimit(0);
      setCurrentMinLength(0);
      setCurrentKerf(0);
    }
  }, [currentMachine]);

  const saveNewSettings = () => {
    if (!currentMachine) return;

    let changedSettings = [];
    if (currentHead !== currentMachine.settings["head cut"].value)
      changedSettings.push({ type: "head cut", value: currentHead });
    if (currentTail !== currentMachine.settings["tail cut"].value)
      changedSettings.push({ type: "tail cut", value: currentTail });
    if (currentMinLimit !== currentMachine.settings["min limit"].value)
      changedSettings.push({ type: "min limit", value: currentMinLimit });
    if (currentMinLength !== currentMachine.settings["min length"]?.value ?? 0)
      changedSettings.push({ type: "min length", value: currentMinLength });
    if (currentKerf !== currentMachine.settings["kerf"].value)
      changedSettings.push({ type: "kerf", value: currentKerf });

    if (changedSettings.length > 0) {
      dispatch(
        handleCreateUpdateCuttingMachineSettings(
          currentMachine,
          changedSettings,
          false,
          false,
          (_) =>
            dispatch(
              handleFetchAllCuttingMachineSettings(machines.map((m) => m.id))
            )
        )
      );
      onSave();
    }
  };

  return (
    <div className={`nesting-settings`}>
      <div className="title">
        Settings for {currentMachine ? currentMachine.name : selectedStage.name}
      </div>
      <ValueStepper
        label={"Head Cut:"}
        startingValue={currentHead}
        minValue={0}
        maxValue={100}
        stepValue={0.5}
        onValueChange={setCurrentHead}
      />
      <ValueStepper
        label={"Tail Cut:"}
        startingValue={currentTail}
        minValue={0}
        maxValue={100}
        stepValue={0.5}
        onValueChange={setCurrentTail}
      />
      <ValueStepper
        label={"Min Limit:"}
        startingValue={currentMinLimit}
        minValue={-50}
        maxValue={100}
        stepValue={0.03125}
        onValueChange={setCurrentMinLimit}
      />
      <ValueStepper
        label={"Min Length:"}
        startingValue={currentMinLength}
        minValue={0}
        maxValue={100}
        stepValue={0.03125}
        onValueChange={setCurrentMinLength}
      />
      <ValueStepper
        label={"Kerf:"}
        startingValue={currentKerf}
        minValue={0}
        maxValue={1000}
        stepValue={0.03125}
        onValueChange={setCurrentKerf}
      />
      <div className="save" onClick={saveNewSettings}>
        Save Changes
      </div>
    </div>
  );
};

export default NestingSettingsMenu;
