@import "../../../styles/colors.scss";

.nesting-wrapper {
  flex-direction: column;
  padding: 16px;
  background-color: $bgDark;
  color: $textLight;

  & > div {
    background-color: inherit;
  }

  &.expanded {
    height: 260px;
    background-color: $bgLightDarkGrey;
  }
  &.on {
    color: $bgDarkGrey;
    background-color: $nestingYellow;
  }

  .nest-length {
    color: inherit;
    background-color: inherit;
    display: flex;
    flex-direction: column;
    padding: 16px;

    label {
      color: $textGrey;
      font-size: 0.8em;
      margin-bottom: 5px;
    }

    input[type="text"] {
      color: $textLight;
      background-color: inherit;
      border: none;
      border-bottom: 1px solid $textGrey;

      &:focus {
        outline: none;
      }
    }
    .parsed-length {
      color: inherit;
      background-color: inherit;
      position: fixed;
      margin-top: 20px;
      font-size: 0.9em;
      font-family: sans-serif;
    }
  }
}

.nesting-toggle {
  color: $textGrey;
  flex: 1;
  align-items: center;
  flex-direction: row;
  cursor: pointer;
  height: 18px;

  span {
    padding-left: 5px;
  }
  &.expanded {
    padding-bottom: 16px;
  }
}

.nesting-switches {
  display: flex;

  > div {
    display: inline-flex;
    align-items: center;
    margin: 0px 16px;

    > span {
      color: $textLight;
      padding: 5px 10px 5px 0px;
    }
  }
}

.settings-toggle {
  color: $blue;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.nest-button {
  color: $blue;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 200px;
  margin-left: auto;
  margin-right: auto;
  cursor: pointer;

  span {
    font-size: 1.5em;
    padding-left: 5px;
  }
}

.on {
  color: $bgDarkGrey;
  background-color: $nestingYellow;

  span {
    color: $bgDarkGrey;
  }

  input[type="text"] {
    color: $bgDarkGrey;
    font-weight: bold;
  }
}
