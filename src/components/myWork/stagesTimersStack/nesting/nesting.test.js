// NPM PACKAGE IMPORTS
import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockAdapter from "axios-mock-adapter";
import thunk from "redux-thunk";

// REDUX IMPORTS
import {
  // ACTION CREATORS
  updateCurrentStock,
  updateAbsLengthInches,
  updateNestedItems,
  updateNestingLength,
  updateSelectedMaterial,
  updateSelectedSize,
  updateNestingOpen,
  updateNestingOn,
  updateNestingOff,
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  updateStarted,
  updateSucceeded,
  updateFailed,
  // ACTION HANDLERS
  handleFetchAllCuttingMachines,
  handleFetchAllCuttingMachineSettings,
  handleCreateCuttingMachine,
  handleCreateUpdateCuttingMachineSettings,
  handleUpdateCurrentStock,
  handleUpdateAbsLengthInches,
  handleUpdateNestedItems,
  handleUpdateNestingLength,
  handleUpdateSelectedMaterial,
  handleUpdateSelectedSize,
  handleUpdateNestingOpen,
  handleUpdateNestingOn,
  handleUpdateNestingOff,
} from "./nestingActions";

const serverError = {
  error: { status: 500, message: "Error completing action" },
};

describe("NESTING", () => {
  describe("action handlers should perform the necessary functions", () => {
    let store;
    let httpMock;

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    it("handleFetchAllCuttingMachines fetches all machines that are not deleted", async () => {
      const testItems = [
        {
          id: 1,
          name: "Cut Table",
          serial_number: "",
          description: "",
          type_id: null,
          type_name: null,
          stage_id: null, // should deprecate soon, from v2
          work_stage_id: 14,
          created_by: 158,
          created_on: 1598442727,
        },
        {
          id: 2,
          name: "rg2",
          serial_number: "**********",
          description: "",
          type_id: 12,
          type_name: "RazorGage",
          stage_id: 2, // should deprecate soon, from v2
          work_stage_id: 2,
          created_by: 158,
          created_on: 1598432727,
        },
      ];
      const testError = {
        error: { status: 404, message: "TigerStop not found." },
      };

      httpMock
        .onGet(`${process.env.REACT_APP_API}/tigerstop/machines`)
        .replyOnce(200, testItems)
        .onGet(`${process.env.REACT_APP_API}/tigerstop/machines`)
        .replyOnce(404, testError);

      await store.dispatch(handleFetchAllCuttingMachines()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("CUTTING_MACHINES"),
          receiveSucceeded("CUTTING_MACHINES", testItems),
          receiveStarted("CUTTING_MACHINE_SETTINGS"),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(testItems[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchAllCuttingMachines()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("CUTTING_MACHINES"),
          receiveFailed("CUTTING_MACHINE_SETTINGS", serverError),
          receiveFailed("CUTTING_MACHINES", testError),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchAllCuttingMachineSettings fetches all machine settings for provided machine ids", async () => {
      const testItems = [
        {
          id: 1,
          setting: "d10",
          value: 250,
          description: "max length",
          machine_id: 1,
          user_editable: 1,
          password_protected: 1,
        },
        {
          id: 2,
          setting: "d10",
          value: 120,
          description: "max length",
          machine_id: 2,
          user_editable: 1,
          password_protected: 1,
        },
      ];
      const testError = {
        error: { status: 404, message: "Settings not found." },
      };

      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/tigerstop/settings?machine_ids=1,2`
        )
        .replyOnce(200, testItems)
        .onGet(
          `${process.env.REACT_APP_API}/tigerstop/settings?machine_ids=1,2`
        )
        .replyOnce(404, testError);

      await store
        .dispatch(handleFetchAllCuttingMachineSettings([1, 2]))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("CUTTING_MACHINE_SETTINGS"),
            receiveSucceeded("CUTTING_MACHINE_SETTINGS", testItems),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload[0]).toEqual(testItems[0]);
          store.clearActions();
        });

      return store
        .dispatch(handleFetchAllCuttingMachineSettings([1, 2]))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("CUTTING_MACHINE_SETTINGS"),
            receiveFailed("CUTTING_MACHINE_SETTINGS", testError),
          ];

          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleCreateCuttingMachine creates a new cutting machine and settings for it", async () => {
      const testItems = {
        stageId: 1,
        type: null,
        description: "",
        serialNumber: "",
        name: '"Cut Table',
      };
      const testBody = {
        name: '"Cut Table',
        serial_number: "",
        description: "",
        type: null,
        stage_id: 1,
        created_by: null,
      };
      const testError = {
        error: { status: 404, message: "SQL Error:" },
      };

      httpMock
        .onPost(`${process.env.REACT_APP_API}/tigerstop/machines`)
        .replyOnce(200, testItems)
        .onPost(`${process.env.REACT_APP_API}/tigerstop/machines`)
        .replyOnce(404, testError);

      await store.dispatch(handleCreateCuttingMachine(testItems)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          updateStarted("CUTTING_MACHINES"),
          updateSucceeded("CUTTING_MACHINES", testItems),
          updateStarted("CUTTING_MACHINE_SETTINGS"),
        ];

        expect(httpMock.history.post[0].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(testItems[0]);
        store.clearActions();
      });

      return store.dispatch(handleCreateCuttingMachine(testItems)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          updateStarted("CUTTING_MACHINES"),
          updateFailed("CUTTING_MACHINE_SETTINGS", serverError),
          updateFailed("CUTTING_MACHINES", testError),
        ];

        expect(httpMock.history.post[2].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    const successMessage = {
      message: "success",
      setting_ids: [1],
    };
    it("handleCreateUpdateCuttingMachineSettings create default settings for machine", async () => {
      // pass machine object, no settings, use defaults true, not a new mapping and no callback
      const testItems = [{ id: 1 }, false, true, false, false];
      const testBody = {
        machine_id: 1,
        settings: [
          {
            setting: "d10",
            value: 0,
            description: "max length",
            user_editable: 1,
            password_protected: 1,
          },
          {
            setting: "d11",
            value: 0,
            description: "min limit",
            user_editable: 1,
            password_protected: 1,
          },
          {
            setting: "no",
            value: 0,
            description: "min length",
            user_editable: 1,
            password_protected: 0,
          },
          {
            setting: "d23",
            value: 0,
            description: "kerf",
            user_editable: 1,
            password_protected: 1,
          },
          {
            setting: "d24",
            value: 0,
            description: "head cut",
            user_editable: 1,
            password_protected: 1,
          },
          {
            setting: "d25",
            value: 0,
            description: "tail cut",
            user_editable: 1,
            password_protected: 1,
          },
        ],
      };
      const testError = {
        error: { status: 404, message: "Error:" },
      };

      httpMock
        .onPost(`${process.env.REACT_APP_API}/tigerstop/settings`)
        .replyOnce(200, successMessage)
        .onPost(`${process.env.REACT_APP_API}/tigerstop/settings`)
        .replyOnce(404, testError);

      await store
        .dispatch(handleCreateUpdateCuttingMachineSettings(...testItems))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("CUTTING_MACHINE_SETTINGS"),
            updateSucceeded("CUTTING_MACHINE_SETTINGS", successMessage),
          ];

          expect(httpMock.history.post[0].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual({
            message: "success",
            setting_ids: [1],
          });
          store.clearActions();
        });

      return store
        .dispatch(handleCreateUpdateCuttingMachineSettings(...testItems))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("CUTTING_MACHINE_SETTINGS"),
            updateFailed("CUTTING_MACHINE_SETTINGS", testError),
          ];

          expect(httpMock.history.post[1].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleCreateUpdateCuttingMachineSettings updates settings for machine", async () => {
      const testItems = [
        {
          // machine object with settings
          id: 1,
          settings: {
            "head cut": {
              setting: "d24",
              description: "head cut",
              user_editable: 1,
              password_protected: 1,
              value: 3.5,
            },
          },
        },
        [{ type: "head cut", value: 2.0 }], // new values for settings to be saved
        false, // don't use default settings
        false, // not a new mapping
        false, // no callback
      ];
      const testBody = {
        machine_id: 1,
        settings: [
          {
            setting: "d24",
            description: "head cut",
            user_editable: 1,
            password_protected: 1,
            value: 2.0,
          },
        ],
      };
      const testError = {
        error: { status: 404, message: "Error:" },
      };

      httpMock
        .onPost(`${process.env.REACT_APP_API}/tigerstop/settings`)
        .replyOnce(200, successMessage)
        .onPost(`${process.env.REACT_APP_API}/tigerstop/settings`)
        .replyOnce(404, testError);

      await store
        .dispatch(handleCreateUpdateCuttingMachineSettings(...testItems))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("CUTTING_MACHINE_SETTINGS"),
            updateSucceeded("CUTTING_MACHINE_SETTINGS", successMessage),
          ];

          expect(httpMock.history.post[0].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(successMessage);
          store.clearActions();
        });

      return store
        .dispatch(handleCreateUpdateCuttingMachineSettings(...testItems))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("CUTTING_MACHINE_SETTINGS"),
            updateFailed("CUTTING_MACHINE_SETTINGS", testError),
          ];

          expect(httpMock.history.post[0].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleCreateUpdateCuttingMachineSettings create settings for new machine", async () => {
      const testItems = [
        { id: 1 }, // just machine object
        [
          // new settings array
          {
            type: "head cut",
            value: 2.0,
            command: "d24",
          },
        ],
        false, // don't use default settings
        true, // is a new mapping
        false, // no callback
      ];
      const testBody = {
        machine_id: 1,
        settings: [
          {
            setting: "d24",
            description: "head cut",
            user_editable: 1,
            password_protected: 1,
            value: 2.0,
          },
        ],
      };
      const testError = {
        error: { status: 404, message: "Error:" },
      };

      httpMock
        .onPost(`${process.env.REACT_APP_API}/tigerstop/settings`)
        .replyOnce(200, successMessage)
        .onPost(`${process.env.REACT_APP_API}/tigerstop/settings`)
        .replyOnce(404, testError);

      await store
        .dispatch(handleCreateUpdateCuttingMachineSettings(...testItems))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("CUTTING_MACHINE_SETTINGS"),
            updateSucceeded("CUTTING_MACHINE_SETTINGS", successMessage),
          ];

          expect(httpMock.history.post[0].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(successMessage);
          store.clearActions();
        });

      return store
        .dispatch(handleCreateUpdateCuttingMachineSettings(...testItems))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("CUTTING_MACHINE_SETTINGS"),
            updateFailed("CUTTING_MACHINE_SETTINGS", testError),
          ];

          expect(httpMock.history.post[1].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleUpdateCurrentStock returns correct stock", () => {
      store.dispatch(handleUpdateCurrentStock(240));

      const receivedActions = store.getActions();
      const expectedActions = [updateCurrentStock(240)];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
    it("handleUpdateAbsLengthInches returns correct abs length inches", () => {
      store.dispatch(handleUpdateAbsLengthInches(50));

      const receivedActions = store.getActions();
      const expectedActions = [updateAbsLengthInches(50)];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
    it("handleUpdateNestedItems returns correct nested items array", () => {
      const nestedItems = [
        {
          id: 5169,
          job_id: 901,
          job_name: "AMC Bridge (DO NOT DELETE)",
          job_number: "AMCB-001",
          package_id: 1696,
          package_name: "MH3D 2",
          package_number: null,
          drawing_id: 98720,
          drawing_name: "EA001",
          tag_number: "15",
          end_prep_1: "ectest",
          end_prep_2: null,
          end_prep_3: null,
          end_prep_4: null,
          length: {
            decimal: 0.4375,
            display: '0 7/16"',
          },
          stock_length: 4.442161083,
          identifier: null,
          measurement_area: null,
          height: 0.25,
          width: null,
          thickness: 0.000233333,
          paint_spec: null,
          texture: null,
          fixture_type: null,
          gauge: 0.000233333,
          weight: 59.669998169,
          hanger_size: null,
          product_code: null,
          insulation: null,
          insulation_area: null,
          insulation_gauge: null,
          insulation_specification: null,
          joining_procedure_name: null,
          material_name: "GalvSqr",
          service_name: "EA-2",
          service_color_name: null,
          status: "Spooled",
          size: '84"x24"',
          area: null,
          random_length: 0,
          rod_size: null,
          support_rod_length: null,
          support_rod_length_2: null,
          laydown_location_name: null,
          liner_spec: null,
          heat_number: "85",
          joint_heat_numbers: "[]",
          is_cut: 1,
          filler_metal: null,
          vendor: null,
        },
      ];
      store.dispatch(handleUpdateNestedItems(nestedItems));

      const receivedActions = store.getActions();
      const expectedActions = [updateNestedItems(nestedItems)];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
    it("handleUpdateNestingLength returns correct nesting length", () => {
      store.dispatch(handleUpdateNestingLength("10 2"));

      const receivedActions = store.getActions();
      const expectedActions = [updateNestingLength("10 2")];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
    it("handleUpdateSelectedMaterial returns correct material", () => {
      store.dispatch(handleUpdateSelectedMaterial("Copper"));

      const receivedActions = store.getActions();
      const expectedActions = [updateSelectedMaterial("Copper")];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
    it("handleUpdateSelectedSize returns correct size", () => {
      store.dispatch(handleUpdateSelectedSize("3/4"));

      const receivedActions = store.getActions();
      const expectedActions = [updateSelectedSize("3/4")];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
    it("handleUpdateNestingOpen toggles nesting area open", () => {
      store.dispatch(handleUpdateNestingOpen());

      const receivedActions = store.getActions();
      const expectedActions = [updateNestingOpen()];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
    it("handleUpdateNestingOn toggles nesting on", () => {
      store.dispatch(handleUpdateNestingOn());

      const receivedActions = store.getActions();
      const expectedActions = [updateNestingOn()];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
    it("handleUpdateNestingOff toggles nesting off", () => {
      store.dispatch(handleUpdateNestingOff());

      const receivedActions = store.getActions();
      const expectedActions = [updateNestingOff()];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });
});
