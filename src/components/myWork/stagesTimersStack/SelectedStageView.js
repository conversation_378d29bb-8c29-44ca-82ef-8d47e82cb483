// NPM PACKAGE IMPORTS
import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
} from "react";
import { useSelector, useDispatch } from "react-redux";
import { HiOutlineSearch } from "react-icons/hi";
import { AiOutlineLeft } from "react-icons/ai";
import { MdDone, MdPause } from "react-icons/md";
import { BiRefresh } from "react-icons/bi";
import { BsFillGrid1X2Fill } from "react-icons/bs";
import { FaPen, FaThumbsDown, FaExclamationCircle } from "react-icons/fa";
import "moment-timezone";
import CreateDropdown from "./createDropdown/CreateDropdown";
import AdvancedGroupingFilter from "./advancedGroupingFilter/AdvancedGroupingFilter";

// REDUX IMPORTS
import {
  handleFetchWorkableItems,
  handleResetItemsReducer,
  handleFetchRejectionCategories,
} from "../../items/itemsActions";
import {
  handleFetchWorkStageColumns,
  handleFetchWorkStageGroupableColumns,
  handleResetFlowsReducer,
} from "../../flows/flowsActions";
import {
  handleFetchWorkableJobs,
  handleFetchWorkablePackages,
  handleFetchWorkableDrawings,
} from "../myWorkActions";
import {
  handleStartTimerv2,
  handleStopTimer,
  handleAddItemsToTimerv2,
  handleRemoveItemsFromTimer,
  handleFetchActiveTimer,
  handleFetchAllTimers,
} from "../../timers/timersActions";
import {
  handleFetchAllCuttingMachines,
  handleCreateCuttingMachine,
} from "./nesting/nestingActions";
import { notify } from "../../reusable/alertPopup/alertPopupActions";
import { showErrorModal } from "../../reusable/generalModal/generalModalActions";
import {
  handleSaveSortState,
  handleFetchSortState,
} from "../../../redux/generalActions";

// COMPONENT IMPORTS
import AgTable from "../../reusable/agTable/AgTable";
import Tabs from "../../reusable/tabs/Tabs";
import NestingCollapsibleMenu from "./nesting/NestingCollapsibleMenu";
import EditView from "./EditView";
import EditingTable from "./EditingTable";
import GroupedQuantityEdit from "./groupedQuantityEdit/GroupedQuantityEdit";
import InfiniteScrollTable from "../../reusable/infiniteScrollTable/InfiniteScrollTable";

// CONSTANTS IMPORTS
import { selectedStageViewColumnDefs } from "./selectedStageViewConstants";

// HELPER FUNCTION IMPORTS
import { onDisplayedColumnsChanged } from "../../../_utils";
import usePrevious from "../../../hooks/usePrevious";
import {
  sortAndFilter,
  associateCustomColumnData,
} from "../../../utils/agTable";
import { fetchCustomColumnDataMultiItem } from "../../../_services";

// STYLE IMPORTS
import { rainbowHash } from "../../styles/colors";
import "./stylesSelectedStageView.scss";
import useOutsideClick from "../../../hooks/useOutsideClick";

const CompleteTimerButton = ({ handleClick }) => (
  <div onClick={handleClick} className="floating-button complete-timer-button">
    <button>
      <MdDone />
    </button>
  </div>
);
const PauseTimerButton = ({ handleClick }) => (
  <div onClick={handleClick} className="floating-button pause-timer-button">
    <button>
      <MdPause />
    </button>
  </div>
);

// var to ignore ag grid selection while updating nest
// to not trigger row selection
let gridIgnoreSelection = false;

const CUSTOM_COLUMNS_FEATURE_ID = 53;

const SelectedStageView = ({
  selectedStage,
  setSelectedStage,
  selectedDrawingIds,
  selectedDrawingId,
  setSelectedDrawingId,
  setSelectedDrawingIds,
  selectedJobs,
  setSelectedJobs,
  setSelectedPackages,
  selectedPackages,
  setDisplayedPdf,
  activeItemsRef,
  deselectDrawings,
  groupingType,
  setGroupingType,
  tableViewSettingsLoaded,
  allFiltersSet,
  toggleIgnorePopulatedState,
  isViewingAllItems,
  toggleViewingAllItems,
  selectedDrawingsLoaded,
  savedJobsFilter,
  jobsFilterSet,
  savedPackagesFilter,
  packagesFilterSet,
  maxGroupQuantity,
  setMaxGroupQuantity,
  isAdvancedGroupingGrouped,
  toggleAdvancedGroupingGrouped,
}) => {
  const {
    items,
    material,
    size,
    on,
    machines,
    isLoading: nestLoading,
    open,
  } = useSelector((state) => state.nestingData);
  const { showModal, modalType, modalMessage } = useSelector(
    (state) => state.modalData
  );
  const [isGrouped, toggleGrouped] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [editGridOptionsApi, setEditGridOptionsApi] = useState(null);
  const [isToolbarVisible, toggleToolbar] = useState(false);
  const [itemsInTimer, setItemsInTimer] = useState([]);
  const [completingTimers, toggleCompletingTimers] = useState(false);
  const [timerPaused, toggleTimerPaused] = useState(false);
  const [shouldRenest, setShouldRenest] = useState(false);
  const [isEditing, toggleEditing] = useState(false);
  const [editType, setEditType] = useState("");
  const [selectedEditRows, setSelectedEditRows] = useState([]);
  const [showEditTypeModal, toggleEditTypeModal] = useState(false);
  const [showQuantityModal, toggleQuantityModal] = useState(false);
  const [tableCount, setTableCount] = useState(null);
  const [rendering, toggleRendering] = useState(false);
  // will always be last selected row whether it was selected or deselected
  const [selectedRowNode, setSelectedRowNode] = useState(null);
  const [allItemsColumnsHidden, toggleItemsColumnsHidden] = useState(false);
  const [groupingOptions, setGroupingOptions] = useState([
    "Ungrouped",
    "Grouped By Drawing",
    "Grouped",
  ]);

  const dispatch = useDispatch();
  const { workableItems, isLoading, rejectionCategories } = useSelector(
    (state) => state.itemsData
  );
  const { workStageColumns, workStageGroupableColumns } = useSelector(
    (state) => state.flowsData
  );
  const { rightMenuWidth, itemsMenuColumnState } = useSelector(
    (state) => state.myWorkData
  );
  const { shiftNow, permissions, features, userId } = useSelector(
    (state) => state.profileData
  );
  const { activeTimer, allTimers } = useSelector((state) => state.timerData);
  const [
    currentStageTimersWorkItemIds,
    setCurrentStageTimersWorkItemIds,
  ] = useState([]);
  const { sortStateRight } = useSelector((state) => state.generalData);

  const inputRef = useRef(null);
  const shiftRef = useRef(null);
  const completingTimersRef = useRef(null);
  const timerPausedRef = useRef(null);
  const editTypeRef = useRef(null);
  const isGroupedRef = useRef(null);
  const workableItemsRef = useRef(null);
  const editWrapperRef = useRef(null);
  const selectedDrawingIdsRef = useRef([]);
  const selectedStageRef = useRef(null);
  const initialColumnRender = useRef(null);
  const workStageColumnsRef = useRef(null);
  const prevSelectedRowNodeRef = useRef(null);
  const gridOptionsApiRef = useRef(null);
  const isLoadingRef = useRef(null);
  const [highlightRefreshButton, setHighlightRefreshButton] = useState(false);

  useOutsideClick(editWrapperRef, () => toggleEditTypeModal(false));

  const hasCustomColumnFeature = features?.includes(CUSTOM_COLUMNS_FEATURE_ID);

  const customColumns = workStageColumns?.filter((col) => col.is_custom);

  useEffect(() => {
    if (!features?.length || groupingOptions.includes("Advanced Grouping"))
      return;

    if (features?.includes(51)) {
      setGroupingOptions((options) => [...options, "Advanced Grouping"]);
    }
  }, [features]);

  const prevSelectedStage = usePrevious(selectedStage);
  useEffect(() => {
    if (selectedStage?.id !== prevSelectedStage?.id) {
      dispatch(
        handleFetchWorkStageColumns(
          [selectedStage.id],
          hasCustomColumnFeature ? true : false
        )
      );
      dispatch(handleFetchWorkStageGroupableColumns(selectedStage.id, true));
      dispatch(handleFetchRejectionCategories);
      selectedStageRef.current = selectedStage;
      if (selectedStage.nestable) dispatch(handleFetchAllCuttingMachines());
      refreshData();
    }
  }, [prevSelectedStage, selectedStage]);

  useEffect(() => {
    let machine = machines.find(
      (m) => !m.serial_number && m.work_stage_id === selectedStage.id
    );
    if (
      selectedStage.nestable &&
      !machine &&
      !nestLoading &&
      !isLoading &&
      workableItems.length
    ) {
      let settings = {
        stageId: selectedStage.id,
        type: null,
        description: "",
        serialNumber: "",
        name: selectedStage.name,
      };
      dispatch(handleCreateCuttingMachine(settings));
    }
  }, [machines]);

  let debouncehandleFetchWorkableItems;
  useEffect(() => {
    if (
      !tableViewSettingsLoaded ||
      !allFiltersSet ||
      !selectedStage ||
      !selectedDrawingsLoaded ||
      !groupingType
    )
      return;

    debouncehandleFetchWorkableItems = setTimeout(() => {
      dispatch(
        handleFetchWorkableItems(
          selectedJobs.map((j) => j.id),
          selectedPackages.map((p) => p.id),
          isViewingAllItems ? null : selectedDrawingIds,
          [selectedStage.id],
          groupingType,
          maxGroupQuantity || "1"
        )
      );
    }, 300); // 300ms delay

    // Cleanup timeout on dependency change
    return () => clearTimeout(debouncehandleFetchWorkableItems);
  }, [
    selectedJobs,
    selectedPackages,
    selectedStage,
    isViewingAllItems,
    groupingType,
    tableViewSettingsLoaded,
    allFiltersSet,
  ]);

  const prevSelectedDrawingIds = usePrevious(selectedDrawingIds);
  const prevRightMenuWidth = usePrevious(rightMenuWidth);
  const prevSelectedRowNode = usePrevious(selectedRowNode);

  useEffect(() => {
    prevSelectedRowNodeRef.current = prevSelectedRowNode;
  }, [prevSelectedRowNode]);

  useEffect(() => {
    isLoadingRef.current = isLoading;
  }, [isLoading]);

  useEffect(() => {
    if (!gridOptionsApi) return;

    if (prevRightMenuWidth <= 20) {
      gridOptionsApi.resetRowHeights();
    }
  }, [rightMenuWidth]);

  useEffect(() => {
    selectedDrawingIdsRef.current = selectedDrawingIds || [];
    // leave if on all tab or no selected drawings
    if (
      !selectedDrawingIds.length ||
      isViewingAllItems ||
      !tableViewSettingsLoaded ||
      !selectedDrawingsLoaded
    )
      return;
    // Prevent infinite loop when multiple drawings are selected
    if (
      JSON.stringify(selectedDrawingIds.sort()) !==
      JSON.stringify(prevSelectedDrawingIds && prevSelectedDrawingIds.sort())
    ) {
      dispatch(
        handleFetchWorkableItems(
          selectedJobs.map((j) => j.id),
          selectedPackages.map((p) => p.id),
          isViewingAllItems ? null : selectedDrawingIds,
          [selectedStage.id],
          selectedStage.groupable ? groupingType : "Ungrouped",
          maxGroupQuantity || "1"
        )
      );
    }
  }, [selectedDrawingIds, isViewingAllItems, tableViewSettingsLoaded]);

  useEffect(() => {
    dispatch(
      handleFetchSortState(
        "MY_WORK_ITEMS",
        groupingType === "Ungrouped" || !selectedStage.groupable
          ? 1
          : groupingType === "Grouped By Drawing"
          ? 3
          : 2,
        selectedStage.id.toString(),
        "RIGHT"
      )
    );

    toggleRendering(true);
    if (groupingType !== "Ungrouped" && selectedStage.groupable) {
      isGroupedRef.current = true;
      toggleGrouped(true);
    } else {
      isGroupedRef.current = false;
      toggleGrouped(false);
    }
    setTimeout(() => toggleRendering(false), 50);
  }, [groupingType, selectedStage]);

  useEffect(() => {
    if (isEditing && editGridOptionsApi) {
      setGridOptionsApi(null);
    }
  }, [editGridOptionsApi, isEditing]);

  useEffect(() => {
    if (gridOptionsApi && !isEditing)
      gridOptionsApi.setQuickFilter(searchValue);
    else if (editGridOptionsApi && isEditing)
      editGridOptionsApi.setQuickFilter(searchValue);
  }, [searchValue, gridOptionsApi, isEditing, editGridOptionsApi]);

  const refreshTable = (callback) => {
    setHighlightRefreshButton(false);
    dispatch(
      handleFetchWorkableItems(
        selectedJobs.map((j) => j.id),
        selectedPackages.map((p) => p.id),
        isViewingAllItems ? null : selectedDrawingIds,
        [selectedStage.id],
        selectedStage.groupable ? groupingType : "Ungrouped",
        maxGroupQuantity || "1",
        callback
      )
    ).then((res) => {
      if (
        (res.error || !res || !res.length) &&
        allFiltersSet &&
        tableViewSettingsLoaded
      ) {
        // toggle to show all items within assigned drawings displayed
        toggleIgnorePopulatedState(true);
        toggleViewingAllItems(true);
      }
    });
    dispatch(handleFetchAllTimers("running"));
    dispatch(handleFetchActiveTimer);
  };

  const f_workStageColumns = useMemo(() => {
    let result = {};

    for (let column of workStageColumns) {
      if (!result[column.id] && column.visible_in_work_table === 1) {
        result[column.id] = column;
      }
    }

    return Object.values(result);
  }, [workStageColumns]);

  const initialColumns = useMemo(() => {
    if (!itemsMenuColumnState || !itemsMenuColumnState.length) return [];

    initialColumnRender.current = true;
    if (sortStateRight) {
      let colDefs = selectedStageViewColumnDefs(
        itemsMenuColumnState,
        f_workStageColumns,
        workStageGroupableColumns || [],
        isEditing,
        selectedStage.groupable ? groupingType : "Ungrouped",
        sortStateRight
      );
      return colDefs;
    } else return null;
  }, [itemsMenuColumnState, sortStateRight]);

  const refreshColumns = () => {
    if (initialColumnRender.current === false) return;
    if (sortStateRight) {
      let colDefs = selectedStageViewColumnDefs(
        itemsMenuColumnState,
        f_workStageColumns,
        workStageGroupableColumns || [],
        isEditing,
        selectedStage.groupable ? groupingType : "Ungrouped",
        sortStateRight
      );

      if (isEditing && editGridOptionsApi) {
        editGridOptionsApi.setColumnDefs(colDefs);
      } else if (!isEditing && gridOptionsApi) {
        gridOptionsApi.setColumnDefs(colDefs);
      }
    }
  };

  useEffect(() => {
    refreshColumns();
  }, [
    isEditing,
    groupingType,
    f_workStageColumns,
    gridOptionsApi,
    editGridOptionsApi,
    sortStateRight,
  ]);

  const refreshData = (clearStage = false) => {
    dispatch(
      handleFetchWorkableJobs(
        !clearStage && selectedStage ? [selectedStage.id] : null
      )
    ).then((res) => {
      let filteredSelectedJobs = [];
      let filteredSelectedPkgs = [];
      if (savedJobsFilter && !jobsFilterSet) {
        filteredSelectedJobs = savedJobsFilter.item_ids.split(",");
      } else if (!res.error) {
        const refreshedJobIds = res.map((j) => j.id);
        for (let j of selectedJobs) {
          if (refreshedJobIds.includes(j.id)) filteredSelectedJobs.push(j.id);
        }
      }
      dispatch(
        handleFetchWorkablePackages(
          filteredSelectedJobs.length > 0 ? filteredSelectedJobs : null,
          !clearStage && selectedStage ? [selectedStage.id] : null
        )
      ).then((res) => {
        if (savedPackagesFilter && !packagesFilterSet) {
          filteredSelectedPkgs = savedPackagesFilter.item_ids.split(",");
        } else if (!res.error) {
          const refreshedPackageIds = res.map((p) => p.id);
          for (let p of selectedPackages) {
            if (refreshedPackageIds.includes(p.id))
              filteredSelectedPkgs.push(p.id);
          }
        }
        dispatch(
          handleFetchWorkableDrawings(
            filteredSelectedJobs.length ? filteredSelectedJobs : null,
            filteredSelectedPkgs.length ? filteredSelectedPkgs : null,
            !clearStage && selectedStage ? [selectedStage.id] : null
          )
        ).then((res) => {
          if (
            (res.error || !res || !res.length) &&
            allFiltersSet &&
            tableViewSettingsLoaded
          ) {
            setSelectedJobs(null);
            setSelectedPackages(null);
          }
        });
      });
    });
  };

  const sendBack = useCallback(() => {
    dispatch(handleResetFlowsReducer());
    dispatch(handleResetItemsReducer);

    setSelectedStage(null);
    refreshData(true);
  }, []);

  const cancelNestFirstMes = () => {
    setNotif("ERROR", "To adjust filters, first cancel your nest");
  };

  const setNotif = (type, message) => {
    dispatch(
      notify({
        id: Date.now(),
        type: type,
        message: message,
      })
    );
  };

  const toggleColumnToolbar = () => {
    if (editGridOptionsApi && isEditing) {
      if (isToolbarVisible) {
        editGridOptionsApi.closeToolPanel();
        editGridOptionsApi.setSideBarVisible(false);
        toggleToolbar(false);
      } else {
        editGridOptionsApi.setSideBarVisible(true);
        editGridOptionsApi.setSideBarPosition("left");
        editGridOptionsApi.openToolPanel("columns");
        toggleToolbar(true);
      }
      return;
    }
    if (gridOptionsApi && !isEditing) {
      if (isToolbarVisible) {
        gridOptionsApi.closeToolPanel();
        gridOptionsApi.setSideBarVisible(false);
        toggleToolbar(false);
      } else {
        gridOptionsApi.setSideBarVisible(true);
        gridOptionsApi.setSideBarPosition("left");
        gridOptionsApi.openToolPanel("columns");
        toggleToolbar(true);
      }
    }
  };

  const isStageWithActiveTimer = useMemo(() => {
    if (activeTimer && activeTimer.error) return;
    if (
      activeTimer &&
      activeTimer.length &&
      activeTimer[0].stage_id === selectedStage.id
    )
      return true;
    else return false;
  }, [activeTimer, selectedStage]);

  // if activerTimer add items to state
  useEffect(() => {
    if (!gridOptionsApi) return;
    if (
      activeTimer &&
      !activeTimer.error &&
      activeTimer.length &&
      activeTimer[0].stage_id !== selectedStage.id
    ) {
      setItemsInTimer([]);
      activeItemsRef.current = [];
      return;
    }
    if (!activeTimer || activeTimer.error) {
      setItemsInTimer([]);
      activeItemsRef.current = [];
    } else if (!activeTimer[0].work_item_id) {
      setItemsInTimer([]);
      activeItemsRef.current = [];
    } else {
      setItemsInTimer(activeTimer);
      activeItemsRef.current = activeTimer;
    }
  }, [activeTimer, gridOptionsApi, selectedStage]);

  useEffect(() => {
    const activeStageTimers = [];
    allTimers?.map((timer) => {
      if (timer.stage_id === selectedStage.id && timer.user_id != userId) {
        activeStageTimers.push(timer.work_item_id);
      }
    });
    setCurrentStageTimersWorkItemIds(activeStageTimers);
  }, [allTimers, selectedStage]);

  // add in custom isSelected param to item object
  useEffect(() => {
    workableItemsRef.current =
      selectedStage.nestable && on ? items : workableItems;

    if (
      (!gridOptionsApi && !isEditing) ||
      (!editGridOptionsApi && isEditing) ||
      !workableItems ||
      !selectedDrawingsLoaded ||
      (activeTimer && !activeTimer.error && !isStageWithActiveTimer)
    )
      return;
    let temp = [];
    let itemsToShow = selectedStage.nestable && on ? items : workableItems;

    itemsToShow
      .filter((i) => {
        return selectedStage.nestable && !on
          ? (!material || (material && i.material_name === material)) &&
              (!size || (size && i.size === size))
          : i;
      })
      .forEach((n) => {
        // clear all isSelected if no activeTimer to remove background
        if (!activeTimer) {
          n.isSelected = false;
        } else if (activeTimer.map((i) => i.work_item_id).includes(n.id)) {
          n.isSelected = true;
          // if timers not in selection, add them to selection
          if (
            !activeTimer
              .map((i) => i.drawing_id)
              .every((i) => selectedDrawingIdsRef.current.includes(i))
          ) {
            const drawingsWithTimer = activeTimer.map((i) => i.drawing_id);
            setSelectedDrawingIds([
              ...new Set([...drawingsWithTimer, n.drawing_id]),
            ]);
            setSelectedDrawingId(n.drawing_id);
          }
        } else if (
          isGrouped &&
          typeof n.id === "string" && // account for grouped id string
          n.id.split(",").includes(`${activeTimer[0].work_item_id}`)
        ) {
          n.isSelected = true;
          if (
            !activeTimer
              .map((i) => i.drawing_id)
              .every((i) => selectedDrawingIdsRef.current.includes(i))
          ) {
            const drawingsWithTimer = activeTimer.map((i) => i.drawing_id);
            setSelectedDrawingIds([
              ...new Set([...drawingsWithTimer, n.drawing_id]),
            ]);
            setSelectedDrawingId(n.drawing_id);
          }
        } else if (
          isGrouped &&
          typeof n.id === "string" && // account for grouped id string
          activeTimer.some((i) => n.id.split(",").includes(`${i.work_item_id}`))
        ) {
          n.isSelected = true;
        } else n.isSelected = false;

        // used to determine if row show have golden border
        if (n.drawing_id === selectedDrawingId) n.isDrawingSelected = true;
        else n.isDrawingSelected = false;

        //used to determine if the row should be disabled due to an active timer
        if (currentStageTimersWorkItemIds.includes(n.id)) {
          n.isDisabled = true;
        } else if (
          isGrouped &&
          typeof n.id === "string" && // account for grouped id string
          currentStageTimersWorkItemIds.some((i) =>
            n.id.split(",").includes(`${i}`)
          )
        ) {
          n.isDisabled = true;
        } else n.isDisabled = false;
        temp.push(n);
      });

    // TODO - may need some extra logic related to editing here

    // if infiniteTable is displayed we want to clear out the infinite cache
    if (hasCustomColumnFeature) {
      if (gridOptionsApi && groupingType === "Ungrouped") {
        return gridOptionsApi.refreshInfiniteCache();
      } else if (editGridOptionsApi && isEditing) {
        return editGridOptionsApi.purgeInfiniteCache();
      }
    }

    if (editGridOptionsApi && isEditing) {
      editGridOptionsApi.setRowData(temp);
      editGridOptionsApi.redrawRows();
      editGridOptionsApi.resetRowHeights();
    } else if (gridOptionsApi && !isEditing) {
      gridOptionsApi.setRowData(temp);
      gridOptionsApi.redrawRows();
      gridOptionsApi.resetRowHeights();
    }
  }, [
    workableItems,
    activeTimer,
    gridOptionsApi,
    editGridOptionsApi,
    selectedDrawingId,
    selectedDrawingIdsRef,
    isEditing,
    isGrouped,
    material,
    size,
    items,
    on,
    groupingType,
    selectedDrawingsLoaded,
  ]);

  // remove drawings from selection once all items associated to them are completed
  const removeSelectionForCompletedItems = (drawingIds, completedIds) => {
    // keep separate drawings that had timers and were selected from timer free ones
    const drawingIdsToCheck = selectedDrawingIdsRef.current.filter((id) =>
      drawingIds.includes(id)
    );
    const drawingIdsToKeep = selectedDrawingIdsRef.current.filter(
      (id) => !drawingIdsToCheck.includes(id)
    );
    let temp = {}; // drawing id to item id mapping

    // store drawing to item mapping if drawing id matches
    workableItems.forEach((i) => {
      if (drawingIdsToCheck.includes(i.drawing_id)) {
        if (temp[i.drawing_id]) {
          temp[i.drawing_id].push(i.id);
        } else {
          temp[i.drawing_id] = [i.id];
        }
      }
    });
    // filter out completed items from list, if drawing still has items left, remove it
    // we don't want to use it for the remove from selected drawing ids logic
    for (let key in temp) {
      temp[key] = temp[key].filter((id) => !completedIds.includes(id));
      if (temp[key].length > 0) {
        delete temp[key];
      }
    }
    // finally update the selected drawing ids and the selected drawing id
    // don't forget to add back the selected drawings that weren't in timers
    let newDrawingSelection = [
      ...drawingIdsToKeep,
      ...drawingIdsToCheck.filter((id) => !Object.keys(temp).includes(`${id}`)),
    ];

    // check all old selection is in new selection
    if (
      selectedDrawingIdsRef.current &&
      !selectedDrawingIdsRef.current.every((id) =>
        newDrawingSelection.includes(id)
      )
    ) {
      setSelectedDrawingIds(newDrawingSelection);
      deselectDrawings(Object.keys(temp).map((i) => Number.parseInt(i)));
    }

    if (Object.keys(temp).includes(`${selectedDrawingId}`)) {
      setSelectedDrawingId(null);
      // no drawing selected, go to all
      toggleViewingAllItems(true);
    }
  };

  // used for onRowSelected
  useEffect(() => {
    shiftRef.current = shiftNow;
    completingTimersRef.current = !!completingTimers;
    timerPausedRef.current = !!timerPaused;
  }, [shiftNow, completingTimers, timerPaused]);

  useEffect(() => {
    // allow selection if done renesting
    if (!shouldRenest && gridIgnoreSelection) gridIgnoreSelection = false;
  }, [shouldRenest]);

  useEffect(() => {
    workStageColumnsRef.current = workStageColumns || [];
  }, [workStageColumns]);

  useEffect(() => {
    gridOptionsApiRef.current = gridOptionsApi;
  }, [gridOptionsApi]);

  // shared logic for shift and long press
  const onShiftLongPress = (row, api, startIndex, endIndex) => {
    // apply same logic as for shift select
    // KEEP! if the row being selected isn't within workableItems then dont do anything
    if (
      !isGroupedRef.current &&
      (!workableItemsRef.current ||
        !workableItemsRef.current.map((i) => i.id).includes(row.id) ||
        isLoadingRef.current)
    ) {
      return;
    }

    if (
      !shiftRef.current ||
      completingTimersRef.current ||
      gridIgnoreSelection // ignore if updating nest
    ) {
      if (!shiftRef.current) {
        let tableSelectedRows = api.getSelectedRows();
        if (tableSelectedRows.length) {
          api.deselectAll();
          setNotif("ERROR", "You must be in a shift to start work");
        }
      }
      completingTimersRef.current = false;
      timerPausedRef.current = false;
      return;
    }
    if (
      activeItemsRef.current &&
      activeItemsRef.current.length &&
      activeItemsRef.current[0].stage_id !== selectedStageRef.current.id
    ) {
      return setNotif(
        "ERROR",
        `Failed to start work. Timer already running for user.`
      );
    }
    if (row.id === -2) {
      return setNotif(
        "WARN",
        "Tail cut is only displayed for information, it is generated by completing all nested cuts before it"
      );
    } else if (row.data.is_completed) {
      return setNotif(
        "ERROR",
        `Unable to start work, item is already complete`
      );
    }

    const itemsAlreadyInTimer = activeItemsRef.current
      ? activeItemsRef.current.map((i) => i.work_item_id)
      : [];

    let selectionType;

    if (isGroupedRef.current && prevSelectedRowNodeRef.current.id) {
      const previousId = prevSelectedRowNodeRef.current.id + "";
      selectionType = itemsAlreadyInTimer.includes(
        parseInt(previousId.split(",")[0])
      )
        ? "SELECT"
        : "DESELECT";
    } else
      selectionType = itemsAlreadyInTimer.includes(
        prevSelectedRowNodeRef.current.id
      )
        ? "SELECT"
        : "DESELECT";

    let itemIdsToAddRemove = [];
    let drawingIdsToAddRemove = [];

    api.forEachNode((rowNode) => {
      // check if row is in between start and end
      if (rowNode.rowIndex >= startIndex && rowNode.rowIndex <= endIndex) {
        itemIdsToAddRemove.push(rowNode.id);
        rowNode.data.drawing_id &&
          drawingIdsToAddRemove.push(rowNode.data.drawing_id);
      }
    });

    if (selectionType === "SELECT") {
      // add items if a timer is already running
      dispatch(
        handleAddItemsToTimerv2(
          itemIdsToAddRemove,
          shiftRef.current.id,
          selectedStage.id,
          activeItemsRef.current[0].timer_id
        )
      ).then((res) => {
        handleError(res);
        if (!res.error) setHighlightRefreshButton(false);
      });

      setSelectedDrawingId(row.data.drawing_id);
      setSelectedDrawingIds([
        ...new Set([
          ...selectedDrawingIdsRef.current,
          ...drawingIdsToAddRemove,
        ]),
      ]);
    } else if (
      activeItemsRef.current !== undefined &&
      activeItemsRef.current.length > 0
    ) {
      dispatch(
        handleRemoveItemsFromTimer(
          activeItemsRef.current[0].timer_id,
          itemIdsToAddRemove
        )
      );
    }
  };

  const onLongPress = (event) => {
    // remove listener to not later have duplicates
    document.removeEventListener("touchend", onLongPress);

    if (!gridOptionsApiRef.current) return;

    // grab the row index and parse to int, includes header and start from 1
    let rowIndex = Number.parseInt(
      (((event || {}).target || {}).parentElement || {}).ariaRowIndex || "0"
    );

    // remove 1 for header and 1 to start from 0
    if (!rowIndex || rowIndex < 2) return;
    rowIndex = rowIndex - 2;
    let row = {};
    let startIndex, endIndex;

    if (rowIndex < prevSelectedRowNodeRef.current.rowIndex) {
      startIndex = rowIndex;
      endIndex = prevSelectedRowNodeRef.current.rowIndex;
    } else {
      startIndex = prevSelectedRowNodeRef.current.rowIndex;
      endIndex = rowIndex;
    }

    gridOptionsApiRef.current.forEachNode((rowNode) => {
      // grab final row
      if (rowNode.rowIndex === endIndex) {
        row = rowNode;
      }
    });

    onShiftLongPress(row, gridOptionsApiRef.current, startIndex, endIndex);
  };

  const onCellMouseDown = () => {
    let isTouchDevice = "ontouchstart" in document.documentElement;
    if (isTouchDevice) document.addEventListener("touchend", onLongPress);
  };

  const onCellClicked = (params) => {
    // make sure user has "ACccess Shop Timer" permission
    if (!permissions.includes(35) || editTypeRef.current) return;
    if (params.node.data.isDisabled) return;
    if (!params.node.selected) params.node.setSelected(true);
    else params.node.setSelected(false);

    setSelectedRowNode(params.node);
    onRowSelected(params);
  };
  const onRowSelected = (params) => {
    // KEEP! if the row being selected isn't within workableItems then dont do anything
    if (
      !isGroupedRef.current &&
      (!workableItemsRef.current ||
        !workableItemsRef.current
          .map((i) => i.id)
          .includes(parseInt(params.node.id)) ||
        isLoadingRef.current)
    ) {
      return;
    }

    if (
      !shiftRef.current ||
      completingTimersRef.current ||
      gridIgnoreSelection // ignore if updating nest
    ) {
      if (!shiftRef.current) {
        params.api.deselectAll();
        setNotif("ERROR", "You must be in a shift to start work");
      }
      completingTimersRef.current = false;
      timerPausedRef.current = false;
      return;
    }
    if (
      activeItemsRef.current &&
      activeItemsRef.current.length &&
      activeItemsRef.current[0].stage_id !== selectedStageRef.current.id
    ) {
      return setNotif(
        "ERROR",
        `Failed to start work. Timer already running for user.`
      );
    }
    if (params.node.id === -2) {
      return setNotif(
        "WARN",
        "Tail cut is only displayed for information, it is generated by completing all nested cuts before it"
      );
    } else if (params.node.data.is_completed) {
      return setNotif(
        "ERROR",
        `Unable to start work, item is already complete`
      );
    }

    const usedShiftSelect = params.event.shiftKey;

    if (usedShiftSelect && prevSelectedRowNodeRef.current) {
      let startIndex, endIndex;

      if (params.node.rowIndex < prevSelectedRowNodeRef.current.rowIndex) {
        startIndex = params.node.rowIndex;
        endIndex = prevSelectedRowNodeRef.current.rowIndex;
      } else {
        startIndex = prevSelectedRowNodeRef.current.rowIndex;
        endIndex = params.node.rowIndex;
      }

      onShiftLongPress(params.node, params.api, startIndex, endIndex);
      return;
    }

    if (params.node.data.drawing_id && params.node.data.drawing_id) {
      setSelectedDrawingId(params.node.data.drawing_id);
      setDisplayedPdf({ ...params.node.data, id: params.node.data.drawing_id });
      setSelectedDrawingIds([
        ...new Set([
          ...selectedDrawingIdsRef.current,
          params.node.data.drawing_id,
        ]),
      ]);
    }

    // START check if item isn't already on the timer and its the first
    if (
      !params.node.data.isSelected &&
      (!activeItemsRef.current || !activeItemsRef.current.length) &&
      (params.node.id > -2 || typeof params.node.id === "string")
    ) {
      // check if head cut with -id then only start parent timer
      dispatch(
        handleStartTimerv2(
          params.node.id === -1 ? [] : [params.node.id],
          shiftRef.current.id,
          selectedStage.id
        )
      ).then((res) => {
        handleError(res);
        if (!res.error) setHighlightRefreshButton(false);
      });
    }

    // REMOVE ITEM
    else if (
      params.node.data.isSelected &&
      activeItemsRef.current &&
      (params.node.id > 0 || typeof params.node.id === "string")
    ) {
      dispatch(
        handleRemoveItemsFromTimer(activeItemsRef.current[0].timer_id, [
          params.node.id,
        ])
      );
    }

    // ADD ITEM
    else if (activeItemsRef.current && activeItemsRef.current.length) {
      if (params.node.id > 0 || typeof params.node.id === "string") {
        dispatch(
          handleAddItemsToTimerv2(
            [params.node.data.id],
            shiftRef.current.id,
            selectedStage.id,
            activeItemsRef.current[0].timer_id
          )
        ).then((res) => {
          handleError(res);
          if (!res.error) setHighlightRefreshButton(false);
        });
      }
    }
    if (params.node.id === -1) {
      // set head to complete and redraw rows to show in case nothing else changed
      params.node.data.is_completed = true;
      params.api.redrawRows();
    }
  };

  /* start error modal handling */
  useEffect(() => {
    // highlight the error button if we have a refresh error ( can come from multiple pages)
    if (
      modalType === "error" &&
      modalMessage &&
      modalMessage?.includes("refresh")
    )
      setHighlightRefreshButton(true);
  }, [showModal]);

  const handleError = (res) => {
    // do not show if there is no error message
    if (res && res.error && res.error.message)
      dispatch(showErrorModal(res.error.message));
  };
  /* end error modal handling */

  const rowClassRules = {
    "--custom-selected-drawing": (params) => {
      if (params.node.data.isDrawingSelected) {
        return true;
      } else return false;
    },
  };

  const onSortChanged = (params) => {
    params.api.redrawRows();
    const sortedColumn = params.columnApi.getAllColumns().find((c) => c.sort);

    dispatch(
      handleSaveSortState(
        sortedColumn ? sortedColumn.colId : null,
        sortedColumn ? sortedColumn.sort : null,
        "MY_WORK_ITEMS",
        groupingType === "Ungrouped" || !selectedStage.groupable
          ? 1
          : groupingType === "Grouped By Drawing"
          ? 3
          : 2,
        selectedStage.id.toString()
      )
    );
  };

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
    // hide the columns button along table
    params.api.setSideBarVisible(false);
    if (sortStateRight && !sortStateRight.sorting_column_name) {
      params.api.setSortModel([
        {
          colId: "drawing_priority",
          sort: "asc",
        },
      ]);
    }
  };
  const onFilterChanged = (params) => {
    if (hasCustomColumnFeature && groupingType === "Ungrouped") return;

    setTableCount(params.api.getDisplayedRowCount());
  };

  useEffect(() => {
    if (!searchValue) {
      setTableCount(workableItems.length);
    }
  }, [workableItems]);

  const handleColumnChange = (params) => {
    const allCols = params.columnApi.getAllGridColumns();
    const displayedCols = params.columnApi.getAllDisplayedColumns();
    // to avoid icon flashing red in-between renders, only toggle true if the grid has columns
    allCols?.length &&
      toggleItemsColumnsHidden(displayedCols?.length ? false : true);
    onDisplayedColumnsChanged(
      "MY_WORK_ITEMS",
      params,
      [selectedStage.id],
      null,
      isGroupedRef.current
    );
  };

  const getRowStyle = (params) => {
    // set the border and selected background here only if custom column feature is present - otherwise border will be set using rowClassRules as normal
    if (
      params.data?.isDrawingSelected &&
      params.data?.isSelected &&
      hasCustomColumnFeature
    ) {
      return {
        background: "#2196f3 !important",
        border: "1px solid #d6993a",
      };
    }
    if (params.data?.is_completed) {
      return { background: "#9b9ea4 !important" };
    } else if (params.data?.isSelected) {
      return { background: "#2196f3 !important" };
    } else if (params.data?.isDisabled) {
      return {
        background:
          "repeating-linear-gradient(30deg, #7F7F7F, #7F7F7F 5px, #1976d2 5px, #1976d2 10px)",
        cursor: "no-drop",
      };
    } else if (params.data?.rejected === 1) {
      return { background: "#b50751 !important" };
    } else if (
      params.data?.identifier === 1 &&
      workStageColumnsRef.current &&
      workStageColumnsRef.current
        .map((c) => c.normal_name)
        .includes("identifier")
    ) {
      const column = workStageColumnsRef.current.find(
        (c) => c.normal_name === "identifier"
      );
      if (column && column.color && JSON.parse(column.color)) {
        let colorParsed = JSON.parse(column.color);
        return {
          background: `rgb(${colorParsed.R}, ${colorParsed.G}, ${colorParsed.B}) !important`,
        };
      }
      // Border is only applied here if using infinte row model. If using client model it will be added via rowClassRules
    } else if (params.data?.isDrawingSelected && hasCustomColumnFeature) {
      return { border: "1px solid #d6993a" };
    } else if (params.node?.childIndex % 2 === 1) {
      return { background: "#20232a !important" };
    } else if (params.node?.childIndex % 2 === 0) {
      return { background: "#253137 !important" };
    }
  };

  const gridOptions = {
    // FOR REACT OPTIMIZATION
    reactNext: true,
    getRowNodeId: (data) => data.id,
    immutableData: true,
    // onSelectionChanged,
    // onRowSelected,
    onFilterChanged,
    onCellClicked,
    onCellMouseDown,
    onDisplayedColumnsChanged: handleColumnChange,
    rowSelection: "multiple",
    suppressRowClickSelection: true,
    rowClassRules,
    getRowStyle,
    rowHeight: 60,
    defaultColDef: {
      wrapText: true,
    },
    columnDefs: initialColumns,
    rowData: workableItems,
    onGridReady,
    onSortChanged,
    pagination: false,
    suppressContextMenu: true,
    suppressTouch: true,
  };

  const onInfiniteGridReady = (params) => {
    setGridOptionsApi(params.api);

    const dataSource = {
      rowCount: undefined,
      getRows: async (params) => {
        const dataAfterSortAndFilter = sortAndFilter(
          workableItemsRef.current,
          params.sortModel,
          params.filterModel
        );

        setTableCount(dataAfterSortAndFilter?.length);

        const rowsThisPage = dataAfterSortAndFilter.slice(
          params.startRow,
          params.endRow
        );
        let lastRow = dataAfterSortAndFilter?.length;

        let rowsWithCustomData = [];

        const rowIdsThisPage = rowsThisPage.map((row) => row.id);
        // fetch custom column data for items currently in view
        // not using a dispatch because don't need to save state in redux currently
        const customColumnData = await fetchCustomColumnDataMultiItem(
          "items",
          rowIdsThisPage
        );

        for (let row of rowsThisPage) {
          if (row.id in customColumnData) {
            const formattedCustomColumnData = associateCustomColumnData(
              customColumnData[row.id],
              customColumns
            );

            rowsWithCustomData.push({ ...formattedCustomColumnData, ...row });
          } else {
            rowsWithCustomData.push({ ...row });
          }
        }

        // pass updated data with custom column data into grid
        params.successCallback(rowsWithCustomData, lastRow);
      },
    };

    params.api.setDatasource(dataSource);
  };

  const infiniteGridOptions = {
    rowModelType: "infinite",
    onGridReady: onInfiniteGridReady,

    // display lines per page
    cacheBlockSize: 30,

    // how many rows to seek ahead when unknown data size.
    cacheOverflowSize: 0,

    // how many concurrent data requests are allowed.
    maxConcurrentDatasourceRequests: 1,

    // how many rows to initially allow scrolling to in the grid.
    infiniteInitialRowCount: 100,

    // how many pages to hold in the cache.
    maxBlocksInCache: 2,

    blockLoadDebounceMillis: 200,

    onFilterChanged,
    onCellClicked,
    onCellMouseDown,
    onDisplayedColumnsChanged: handleColumnChange,
    rowSelection: "multiple",
    // suppressRowClickSelection: true,
    // rowClassRules,
    getRowStyle,
    columnDefs: initialColumns,
    rowHeight: 60,
    defaultColDef: {
      wrapText: true,
    },
    onSortChanged,
    pagination: false,
    suppressContextMenu: true,
    suppressTouch: true,
    getRowNodeId: (data) => data.id,
  };

  const handleCompleteTimer = () => {
    gridIgnoreSelection = true;
    toggleCompletingTimers(true);

    let drawingIds = activeTimer.map((t) => t.drawing_id);
    let completedIds = activeTimer.map((t) => t.work_item_id);
    dispatch(
      handleStopTimer(activeTimer[0].timer_id, 2, (_) => {
        toggleCompletingTimers(false);
        removeSelectionForCompletedItems(drawingIds, completedIds);
        if (gridOptionsApi) gridOptionsApi.deselectAll();
        refreshData();
        refreshTable((_) =>
          on ? setShouldRenest(true) : (gridIgnoreSelection = false)
        );
      })
    );
  };

  const handlePauseTimer = () => {
    gridIgnoreSelection = true;
    toggleTimerPaused(true);
    dispatch(
      handleStopTimer(activeTimer[0].timer_id, 1, (_) => {
        if (gridOptionsApi) gridOptionsApi.deselectAll();
        refreshData();
        refreshTable((_) => (gridIgnoreSelection = false));
      })
    );
  };

  const showTimerButtons = useMemo(() => {
    if (!selectedStage || !gridOptionsApi || !activeTimer || activeTimer.error)
      return false;

    return selectedStage.id === activeTimer[0].stage_id ? true : false;
  }, [selectedStage, activeTimer, gridOptionsApi]);

  const handleEditClick = () => {
    // currently not able to edit items when advanced grouping
    if (groupingType === "Advanced Grouping") return;

    if (
      isGrouped &&
      groupingType !== "Grouped By Drawing" &&
      activeTimer &&
      activeTimer.length
    ) {
      return isEditing
        ? handleEditRejectionClick("EDIT")
        : toggleEditTypeModal(true);
    } else {
      handleEditRejectionClick("EDIT");
    }
  };

  const handleEditRejectionClick = (type) => {
    if (groupingType === "Grouped By Drawing") return;
    if (isEditing) {
      if (editGridOptionsApi) {
        editGridOptionsApi.deselectAll();
        setEditGridOptionsApi(null);
      }
      setEditType("");
      editTypeRef.current = "";
    } else if (type === "reject") {
      if (!shiftNow)
        return setNotif("ERROR", "Must be in a shift to make rejections.");
      if (
        activeTimer &&
        activeTimer.length &&
        activeTimer[0].stage_id !== selectedStage.id
      )
        return setNotif(
          "ERROR",
          "Must be in stage with active timer to make rejections."
        );

      setEditType("REJECT");
      editTypeRef.current = "REJECT";
    } else {
      setEditType("EDIT");
      editTypeRef.current = "EDIT";
    }
    toggleEditing(!isEditing);
  };

  const handleQuantityClick = () => {
    if (!activeTimer || !activeTimer.length) {
      toggleEditTypeModal(false);
      return setNotif("ERROR", "Must have an active timer to edit quantity.");
    }
    setEditType("QUANTITY");
    editTypeRef.current = "QUANTITY";
    toggleEditing(true);
    toggleEditTypeModal(false);
  };

  const cancelEditing = () => {
    toggleEditing(false);
    editTypeRef.current = null;
    setEditType(null);
  };

  const handleEditRowSelection = (api) => {
    setSelectedEditRows(api ? api.getSelectedRows() : []);
  };

  const onClearTableSelection = (exitEdit) => {
    if (!editGridOptionsApi || !isEditing) return;
    editGridOptionsApi.deselectAll();
    handleEditRowSelection(editGridOptionsApi);

    if (exitEdit) setTimeout((_) => handleEditRejectionClick(""), 160);
  };

  const editableColumns = useMemo(() => {
    if (isGrouped) {
      return (workStageGroupableColumns || []).filter(
        (c) => c.editable_my_work && c.editable
      );
    } else
      return workStageColumns.filter((c) => c.editable_my_work && c.editable);
  }, [isGrouped, workStageGroupableColumns, workStageColumns]);

  const EditTypeSelector = (selectedStage) => (
    <div ref={editWrapperRef} className="edit-type-picker">
      <h4>{selectedStage.name}</h4>
      <p>Select the column you want to edit:</p>
      <div className="button-wrapper">
        <button onClick={handleQuantityClick}>Quantity</button>
        <button
          onClick={() => {
            toggleEditTypeModal(false);
            handleEditRejectionClick("EDIT");
          }}
        >
          Edit
        </button>
      </div>
    </div>
  );

  useEffect(() => {
    if (
      isEditing &&
      editType === "QUANTITY" &&
      selectedEditRows &&
      selectedEditRows.length
    ) {
      toggleQuantityModal(true);
    } else if (showQuantityModal) toggleQuantityModal(false);
  }, [editType, isEditing, selectedEditRows]);

  const handleAdvancedGrouping = () => {
    saveMaxGroupQuantityToLocalStorage(maxGroupQuantity);
    toggleAdvancedGroupingGrouped(true);

    dispatch(
      handleFetchWorkableItems(
        selectedJobs.map((j) => j.id),
        selectedPackages.map((p) => p.id),
        isViewingAllItems ? null : selectedDrawingIds,
        [selectedStage.id],
        groupingType,
        maxGroupQuantity
      )
    );
  };

  const handleClearAdvancedGrouping = () => {
    setMaxGroupQuantity("1");
    saveMaxGroupQuantityToLocalStorage("1");
    toggleAdvancedGroupingGrouped(false);

    dispatch(
      handleFetchWorkableItems(
        selectedJobs.map((j) => j.id),
        selectedPackages.map((p) => p.id),
        isViewingAllItems ? null : selectedDrawingIds,
        [selectedStage.id],
        groupingType,
        "1" // default max group quantity state
      )
    );
  };

  const handleMaxGroupQuantityInput = (quantity) => {
    setMaxGroupQuantity(quantity);
  };

  const saveMaxGroupQuantityToLocalStorage = (quantity) => {
    let groupingByStageState = JSON.parse(
      localStorage.getItem("stageGrouping")
    );

    const currentStageState = groupingByStageState?.find(
      (stageObj) => stageObj.stageId === selectedStage.id
    );

    currentStageState.maxGroupQuantity = quantity;

    localStorage.setItem("stageGrouping", JSON.stringify(groupingByStageState));
  };

  return (
    <div className="selected-stage-view">
      <div
        className="heading-wrapper"
        style={{ backgroundColor: rainbowHash(selectedStage.id) }}
      >
        <AiOutlineLeft onClick={on ? cancelNestFirstMes : sendBack} />
        <span onClick={on ? cancelNestFirstMes : sendBack}>Fab</span>
        <h2>{selectedStage.name}</h2>
        {!isEditing && (
          <Tabs
            options={["All", "Selected"]}
            currentTabIndex={
              !isViewingAllItems &&
              selectedDrawingIds.length &&
              workableItems.filter((i) =>
                selectedStage.nestable
                  ? (!material || (material && i.material_name === material)) &&
                    (!size || (size && i.size === size)) &&
                    selectedDrawingIds.includes(i.drawing_id)
                  : i && selectedDrawingIds.includes(i.drawing_id)
              ).length
                ? 1
                : 0
            }
            isDisabled={
              !selectedDrawingIds.length ||
              on ||
              !workableItems.filter((i) =>
                selectedStage.nestable
                  ? (!material || (material && i.material_name === material)) &&
                    (!size || (size && i.size === size)) &&
                    selectedDrawingIds.includes(i.drawing_id)
                  : i && selectedDrawingIds.includes(i.drawing_id)
              ).length
            }
            onValueChanged={(item, index) =>
              toggleViewingAllItems(index === 1 ? false : true)
            }
          />
        )}
      </div>

      {selectedStage.nestable ? (
        <NestingCollapsibleMenu
          selectedStage={selectedStage}
          activeTimer={activeTimer}
          shouldRenest={shouldRenest}
          setShouldRenest={setShouldRenest}
          isEditing={isEditing}
          isDisabled={isGrouped}
        />
      ) : undefined}
      {selectedStage.groupable === 1 && (
        <div className="grouping-wrapper">
          <CreateDropdown
            list={groupingOptions}
            selected={groupingType}
            setSelected={setGroupingType}
            disabled={
              !!activeTimer ||
              on ||
              isEditing ||
              selectedStage.grouped === 0 ||
              !workStageGroupableColumns ||
              !workStageGroupableColumns.length
            }
          />
        </div>
      )}
      {groupingType === "Advanced Grouping" ? (
        <AdvancedGroupingFilter
          setMaxGroupQuantity={handleMaxGroupQuantityInput}
          maxGroupQuantity={maxGroupQuantity}
          handleGroupCallback={handleAdvancedGrouping}
          handleClearCallback={handleClearAdvancedGrouping}
          isGrouped={isAdvancedGroupingGrouped}
        />
      ) : undefined}
      {isEditing && (editType === "EDIT" || editType === "CUSTOM") && (
        <EditView
          startingValue=""
          editableColumns={editableColumns}
          selectedRows={selectedEditRows}
          onClearTableSelection={onClearTableSelection}
          refreshTable={refreshTable}
          setNotif={setNotif}
          isGrouped={isGrouped}
          groupingType={groupingType}
          editableCustomColumns={customColumns?.filter(
            (cc) => cc.editable_my_work && cc.editable
          )}
          setEditType={setEditType}
          gridOptionsApi={editGridOptionsApi}
        />
      )}
      <>
        <div className="table-toolbar">
          <p className="table-count">{`${tableCount || "0"} items`}</p>
          <input
            ref={inputRef}
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
          />
          <div className="tool-wrapper">
            <HiOutlineSearch
              onClick={() => inputRef.current.focus()}
              className="search"
            />
            {}
            {groupingType !== "Grouped By Drawing" && (
              <>
                {groupingType !== "Advanced Grouping" && (
                  <FaPen
                    className={
                      editType === "EDIT" || editType === "QUANTITY"
                        ? "selected-edit"
                        : ""
                    }
                    onClick={handleEditClick}
                  />
                )}
                <FaThumbsDown
                  onClick={(_) => handleEditRejectionClick("reject")}
                  className={
                    editType === "REJECT" ? "selected rejection" : "rejection"
                  }
                />
              </>
            )}
            <button
              onClick={toggleColumnToolbar}
              className={`columns-btn ${
                allItemsColumnsHidden ? "column-warn" : ""
              }`}
              disabled={isEditing || open}
            >
              <BsFillGrid1X2Fill className="columns" />
            </button>
            <div id="refresh-items-table-container">
              <BiRefresh
                id="refresh-items-table-button"
                className={`refresh ${
                  highlightRefreshButton ? "icon-highlight" : ""
                }`}
                onClick={refreshTable}
              />
              {highlightRefreshButton && (
                <FaExclamationCircle
                  id="refresh-items-table-notification-icon"
                  className="notification-icon"
                />
              )}
            </div>
          </div>
        </div>
        <div className="table-wrapper" style={{ width: rightMenuWidth }}>
          {!rendering && sortStateRight ? (
            workableItems.length ? (
              initialColumns ? (
                !isEditing ? (
                  groupingType === "Ungrouped" &&
                  hasCustomColumnFeature &&
                  !isLoadingRef.current ? (
                    <InfiniteScrollTable
                      overrideCheckboxSelectColumn={false}
                      gridOptions={infiniteGridOptions}
                    />
                  ) : (
                    <AgTable gridOptions={gridOptions} />
                  )
                ) : (
                  <EditingTable
                    columns={initialColumns}
                    editType={editType}
                    tableData={workableItems}
                    isGrouped={isGrouped}
                    selectedRows={selectedEditRows}
                    setSelectedRows={setSelectedEditRows}
                    handleRowSelection={handleEditRowSelection}
                    gridOptionsApi={editGridOptionsApi}
                    setGridOptionsApi={setEditGridOptionsApi}
                    rejectionCategories={rejectionCategories}
                    selectedStage={selectedStage}
                    refreshTable={refreshTable}
                    activeItemsRef={activeItemsRef}
                    toggleEditing={toggleEditing}
                    editTypeRef={editTypeRef}
                    workStageColumns={workStageColumnsRef.current || []}
                    groupingType={groupingType}
                    isInfiniteGrid={hasCustomColumnFeature}
                    customColumns={customColumns}
                  />
                )
              ) : (
                <p>No columns selected for stage.</p>
              )
            ) : (
              <p>No items for selected stage and/or drawings.</p>
            )
          ) : (
            <></>
          )}
        </div>
        {showEditTypeModal && (
          <EditTypeSelector selectedStage={selectedStage} />
        )}
        {showQuantityModal && (
          <GroupedQuantityEdit
            selectedStage={selectedStage}
            toggleModal={toggleQuantityModal}
            selectedRow={selectedEditRows[0]}
            refreshTable={refreshTable}
            notify={setNotif}
            cancelEditing={cancelEditing}
            setSelectedEditRows={setSelectedEditRows}
          />
        )}
      </>
      {showTimerButtons && itemsInTimer.length && (
        <CompleteTimerButton handleClick={handleCompleteTimer} />
      )}
      {showTimerButtons && <PauseTimerButton handleClick={handlePauseTimer} />}
    </div>
  );
};

export default SelectedStageView;
