import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";

import Stages from "./Stages";
import SelectedStageView from "./SelectedStageView";
import Countdown from "../../reusable/countdown/Countdown";

// STYLES IMPORTS
import "./stylesStagesTimersStack.scss";

const StagesTimersStack = ({
  selectedDrawingId,
  setSelectedDrawingId,
  setSelectedDrawingIds,
  selectedDrawingIds,
  selectedJobs,
  selectedPackages,
  selectedStage,
  selectedStages,
  setSelectedStages,
  setSelectedStage,
  setDisplayedPdf,
  viewDrawingWithoutTimer,
  activeItemsRef,
  deselectDrawings,
  allOrSelected,
  setAllOrSelected,
  groupingType,
  setGroupingType,
  tableViewSettingsLoaded,
  allFiltersSet,
  selectedStageSet,
  toggleIgnorePopulatedState,
  isViewingAllItems,
  toggleViewingAllItems,
  selectedDrawingsLoaded,
  savedJobsFilter,
  jobsFilterSet,
  savedPackagesFilter,
  packagesFilterSet,
  maxGroupQuantity,
  setMaxGroupQuantity,
  isAdvancedGroupingGrouped,
  toggleAdvancedGroupingGrouped,
}) => {
  const { rightMenuWidth } = useSelector((state) => state.myWorkData);
  const { activeTimer } = useSelector((state) => state.timerData);
  const [showCountdown, setShowCountdown] = useState(false);
  const [seconds, setSeconds] = useState(120);

  useEffect(() => {
    if (viewDrawingWithoutTimer && !activeTimer && selectedDrawingId) {
      setSeconds(120);
      setShowCountdown(true);
    } else {
      setShowCountdown(false);
    }
  }, [activeTimer, viewDrawingWithoutTimer, selectedDrawingId]);

  useEffect(() => {
    setSeconds(120);
  }, [selectedStage]);

  return (
    <div
      hidden={rightMenuWidth <= 20}
      className={
        rightMenuWidth <= 20
          ? "stages-timers-wrapper menu-closed"
          : "stages-timers-wrapper"
      }
    >
      {selectedStage ? (
        <>
          <SelectedStageView
            selectedStage={selectedStage}
            setSelectedStage={setSelectedStage}
            allOrSelected={allOrSelected}
            setAllOrSelected={setAllOrSelected}
            selectedDrawingIds={selectedDrawingIds}
            selectedDrawingId={selectedDrawingId}
            selectedJobs={selectedJobs}
            selectedPackages={selectedPackages}
            setSelectedDrawingId={setSelectedDrawingId}
            setSelectedDrawingIds={setSelectedDrawingIds}
            setDisplayedPdf={setDisplayedPdf}
            activeItemsRef={activeItemsRef}
            deselectDrawings={deselectDrawings}
            groupingType={groupingType}
            setGroupingType={setGroupingType}
            tableViewSettingsLoaded={tableViewSettingsLoaded}
            allFiltersSet={allFiltersSet}
            selectedStageSet={selectedStageSet}
            toggleIgnorePopulatedState={toggleIgnorePopulatedState}
            toggleViewingAllItems={toggleViewingAllItems}
            isViewingAllItems={isViewingAllItems}
            selectedDrawingsLoaded={selectedDrawingsLoaded}
            savedJobsFilter={savedJobsFilter}
            jobsFilterSet={jobsFilterSet}
            savedPackagesFilter={savedPackagesFilter}
            packagesFilterSet={packagesFilterSet}
            maxGroupQuantity={maxGroupQuantity}
            setMaxGroupQuantity={setMaxGroupQuantity}
            isAdvancedGroupingGrouped={isAdvancedGroupingGrouped}
            toggleAdvancedGroupingGrouped={toggleAdvancedGroupingGrouped}
          />
          {showCountdown ? (
            <Countdown
              onTimerEnd={() => {
                setSelectedStage(null);
              }}
              seconds={seconds}
              setSeconds={setSeconds}
              showAt={60}
            />
          ) : (
            <></>
          )}
        </>
      ) : (
        <>
          <h4>Fab</h4>
          <Stages
            selectedDrawingId={selectedDrawingId}
            selectedDrawingIds={selectedDrawingIds}
            selectedJobs={selectedJobs}
            selectedPackages={selectedPackages}
            selectedStage={selectedStage}
            selectedStages={selectedStages}
            setSelectedStages={setSelectedStages}
            isViewingAllItems={isViewingAllItems}
            toggleViewingAllItems={toggleViewingAllItems}
            setSelectedStage={setSelectedStage}
            savedJobsFilter={savedJobsFilter}
            savedPackagesFilter={savedPackagesFilter}
            allFiltersSet={allFiltersSet}
          />
        </>
      )}
    </div>
  );
};

export default StagesTimersStack;
