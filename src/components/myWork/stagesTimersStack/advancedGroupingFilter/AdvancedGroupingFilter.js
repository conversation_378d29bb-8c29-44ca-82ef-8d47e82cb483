// NPM PACKAGE IMPORTS
import { useMemo } from "react";
import Button from "msuite_storybook/dist/button/Button";
import { useSelector } from "react-redux";

// STYLE IMPORTS
import "./stylesAdvancedGroupingFilter.scss";

const AdvancedGroupingFilter = ({
  maxGroupQuantity,
  setMaxGroupQuantity,
  handleGroupCallback,
  handleClearCallback,
  isGrouped,
}) => {
  const { activeTimer } = useSelector((state) => state.timerData);

  const handleGroup = () => {
    handleGroupCallback(maxGroupQuantity);
  };

  const handleClear = () => {
    handleClearCallback(maxGroupQuantity);
  };

  const isGroupButtonDisabled = useMemo(() => {
    const parsedMaxGroupQuantity = parseInt(maxGroupQuantity);
    const isInitialQuantity = (parsedMaxGroupQuantity === 1) & !isGrouped; // this is just when it is cleared/1 on load
    if (
      !parsedMaxGroupQuantity ||
      parsedMaxGroupQuantity < 1 ||
      isInitialQuantity ||
      activeTimer
    )
      return true;
    else return false;
  }, [maxGroupQuantity, activeTimer, isGrouped]);

  const handleMaxGroupQuantityChange = (value) => {
    setMaxGroupQuantity(value.replace(/[^0-9]/g, ""));
  };

  return (
    <div className="advanced-grouping-filter">
      <label>Max Group Quantity:</label>
      <input
        id="quantity"
        name="quantity"
        type="number"
        value={maxGroupQuantity}
        disabled={activeTimer?.length}
        onKeyDown={(e) => e.key === "." && e.preventDefault()}
        onChange={(e) => handleMaxGroupQuantityChange(e.target.value)}
      />
      <div className="buttons">
        <Button disabled={isGroupButtonDisabled} onClick={handleGroup}>
          Group
        </Button>
        <Button disabled={!isGrouped || activeTimer} onClick={handleClear}>
          Clear
        </Button>
      </div>
    </div>
  );
};

export default AdvancedGroupingFilter;
