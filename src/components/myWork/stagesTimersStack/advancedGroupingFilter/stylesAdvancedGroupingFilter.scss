@import "../../../styles/colors.scss";

div.advanced-grouping-filter {
  margin: 0 30px 0;
  padding: 0 16px 0 16px;
  flex-wrap: nowrap;
  display: flex;
  align-items: baseline;

  & label {
    color: $textLight;
    white-space: nowrap;
  }

  // hide the button on number inputs
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  & input {
    margin: 20px 0 0 10px;
    width: 80px;
    color: $textLight;
    background-color: inherit;
    border: none;
    border-bottom: 1px solid $textGrey;

    &:focus {
      outline: none;
    }
    &:disabled {
      border-bottom: 1px solid darken($textGrey, 20%);
      color: darken($textLight, 20%);
    }
  }

  & div.buttons {
    display: inline-flex;
    justify-content: space-between;
    gap: 5px;
    width: 180px;
    margin-left: 40px;

    & button {
      background-color: inherit;
      height: 36px;
      padding: 0 20px;
      font-size: 0.9rem;
      border: 1px solid $fabProBlue;
      color: $fabProBlue;
    }
  }
}
