@import "../../styles/colors.scss";

.single-cell-table {
  color: white;
  overflow-y: auto;
  // 100 - header/footer, 25 - title, 35 - stage filter, 20 - all/selected tabs, 20 - table padding, 12 - unaccounted for
  height: calc(100vh - 212px);

  .single-cell-row {
    padding: 10px 10px 10px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
  }
}
