import React from "react";

import { FiChevronRight } from "react-icons/fi";

// STYLES IMPORTS
import "./stylesSingleCellTable.scss";
import { rainbowHash } from "./../../../components/styles/colors";

const SingleCellTable = ({ items, noIcon, onSelect, rainbow }) => {
  return (
    <div className="single-cell-table">
      {items &&
        items.length > 0 &&
        items.map((item) => (
          <div
            key={item.id}
            className="single-cell-row"
            onClick={() => {
              onSelect(item);
            }}
            style={{
              backgroundColor: rainbow ? rainbowHash(item.id) : undefined,
            }}
          >
            {item.name}
            {!noIcon ? <FiChevronRight /> : undefined}
          </div>
        ))}
    </div>
  );
};

export default SingleCellTable;
