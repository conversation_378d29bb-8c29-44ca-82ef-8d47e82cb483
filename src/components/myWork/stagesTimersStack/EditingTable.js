// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useRef } from "react";
import { useSelector } from "react-redux";

// COMPONENT IMPORTS
import RejectionPopup from "./rejectionPopop/RejectionPopup";
import AgTable from "../../reusable/agTable/AgTable";

// HELPER FUNCTION IMPORTS
import {
  sortAndFilter,
  associateCustomColumnData,
} from "../../../utils/agTable";
import { fetchCustomColumnDataMultiItem } from "../../../_services";

// STYLES IMPORTS
import "./stylesSelectedStageView.scss";
import usePrevious from "../../../hooks/usePrevious";

const EditingTable = ({
  columns,
  editType,
  tableData,
  selectedRows,
  setSelectedRows,
  handleRowSelection,
  gridOptionsApi,
  setGridOptionsApi,
  rejectionCategories,
  selectedStage,
  refreshTable,
  activeItemsRef,
  isGrouped,
  toggleEditing,
  editTypeRef,
  workStageColumns,
  groupingType,
  isInfiniteGrid,
  customColumns,
}) => {
  const [showRejectionPopup, toggleRejectionPopup] = useState(false);

  const { activeTimer } = useSelector((state) => state.timerData);

  useEffect(() => {
    if (selectedRows.length && editType === "REJECT") {
      toggleRejectionPopup(true);
    }
  }, [editType, selectedRows]);

  // need to check if the timer gets paused/complete while in quantity mode
  useEffect(() => {
    if ((!activeTimer || !activeTimer.length) && editType === "QUANTITY") {
      toggleEditing(false);
      editTypeRef.current = null;
    }
  }, [activeTimer]);

  const onRowSelected = (params) => {
    handleRowSelection(params.api, params.node);
  };

  const handleCancelRejection = () => {
    toggleRejectionPopup(false);
    if (gridOptionsApi) gridOptionsApi.deselectAll();
  };

  const rowClassRules = {
    "--custom-selected-drawing": (params) => {
      if (params.node?.data?.isDrawingSelected) {
        return true;
      } else return false;
    },
  };

  const onSortChanged = (params) => params.api.redrawRows();
  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
    // hide columns button along table
    params.api.setSideBarVisible(false);
    params.api.setSortModel([
      {
        colId: "drawing_priority",
        sort: "asc",
      },
    ]);
  };

  const prevTableData = usePrevious(tableData);
  useEffect(() => {
    if (
      !gridOptionsApi ||
      JSON.stringify(prevTableData) === JSON.stringify(tableData)
    )
      return;

    tableDataRef.current = tableData;
    if (isInfiniteGrid) {
      gridOptionsApi.purgeInfiniteCache();
    } else {
      gridOptionsApi.setRowData(tableData);
    }
  }, [tableData, gridOptionsApi]);

  const isRowSelectable = (rowNode) => {
    if (rowNode.data?.id === -1 || rowNode.data?.id === -2) return false;
    if (editTypeRef.current === "QUANTITY") {
      return rowNode.data.isSelected;
    } else return true;
  };

  const gridOptions = {
    // FOR REACT OPTIMIZATION
    reactNext: true,
    getRowNodeId: (data) => data.id,
    immutableData: true,

    onRowSelected,
    isRowSelectable,
    rowSelection:
      editType === "REJECT" || editType === "CUSTOM" || isGrouped
        ? "single"
        : "multiple",
    rowClassRules,
    getRowStyle: (params) => {
      if (params.data.is_completed) {
        return { background: "#9b9ea4 !important" };
      } else if (params.data.isSelected) {
        return { background: "#2196f3 !important" };
      } else if (params.data.rejected === 1) {
        return { background: "#b50751 !important" };
      } else if (
        params.data.identifier === 1 &&
        workStageColumns &&
        workStageColumns.map((c) => c.normal_name).includes("identifier")
      ) {
        const column = workStageColumns.find(
          (c) => c.normal_name === "identifier"
        );
        if (column && column.color && JSON.parse(column.color)) {
          let colorParsed = JSON.parse(column.color);
          return {
            background: `rgb(${colorParsed.R}, ${colorParsed.G}, ${colorParsed.B}) !important`,
          };
        }
      } else if (params.node.childIndex % 2 === 1) {
        return { background: "#20232a !important" };
      } else if (params.node.childIndex % 2 === 0) {
        return { background: "#253137 !important" };
      }
    },
    defaultColDef: {
      wrapText: true,
    },
    rowHeight: 60,
    columnDefs: columns,
    rowData: tableData,
    onGridReady,
    onSortChanged,
    pagination: false,
  };

  const onInfiniteGridReady = (params) => {
    setGridOptionsApi(params.api);

    const datasource = {
      rowCount: tableDataRef.current?.length,
      getRows: async (params) => {
        const dataAfterSortAndFilter = sortAndFilter(
          tableDataRef.current,
          params.sortModel,
          params.filterModel
        );

        const rowsThisPage = dataAfterSortAndFilter.slice(
          params.startRow,
          params.endRow
        );
        let lastRow = dataAfterSortAndFilter?.length;

        let rowsWithCustomData = [];

        const rowIdsThisPage = rowsThisPage.map((row) => row.id);

        // fetch custom column data for current item
        // not using a dispatch because don't need to save state in redux currently
        const customColumnData = await fetchCustomColumnDataMultiItem(
          "items",
          rowIdsThisPage
        );

        for (let row of rowsThisPage) {
          if (row.id in customColumnData) {
            const formattedCustomColumnData = associateCustomColumnData(
              customColumnData[row.id],
              customColumns
            );

            rowsWithCustomData.push({ ...formattedCustomColumnData, ...row });
          } else {
            rowsWithCustomData.push({ ...row });
          }
        }

        params.successCallback(rowsWithCustomData, lastRow);
      },
    };

    params.api.setDatasource(datasource);
  };

  const tableDataRef = useRef(tableData);

  const gridOptionsInfinite = {
    rowModelType: "infinite",

    // display lines per page
    cacheBlockSize: 10,

    // how many rows to seek ahead when unknown data size.
    cacheOverflowSize: 2,

    // how many concurrent data requests are allowed.
    maxConcurrentDatasourceRequests: 1,

    // how many pages to hold in the cache.
    maxBlocksInCache: 10,

    blockLoadDebounceMillis: 200,

    getRowNodeId: (data) => data.id,
    onRowSelected,
    isRowSelectable,
    rowSelection: editType === "REJECT" || isGrouped ? "single" : "multiple",
    rowClassRules,
    getRowStyle: (params) => {
      if (params.data?.is_completed) {
        return { background: "#9b9ea4 !important" };
      } else if (params.data?.isSelected) {
        return { background: "#2196f3 !important" };
      } else if (params.data?.rejected === 1) {
        return { background: "#b50751 !important" };
      } else if (
        params.data?.identifier === 1 &&
        workStageColumns &&
        workStageColumns.map((c) => c.normal_name).includes("identifier")
      ) {
        const column = workStageColumns.find(
          (c) => c.normal_name === "identifier"
        );
        if (column && column.color && JSON.parse(column.color)) {
          let colorParsed = JSON.parse(column.color);
          return {
            background: `rgb(${colorParsed.R}, ${colorParsed.G}, ${colorParsed.B}) !important`,
          };
        }
      } else if (params.node?.childIndex % 2 === 1) {
        return { background: "#20232a !important" };
      } else if (params.node?.childIndex % 2 === 0) {
        return { background: "#253137 !important" };
      }
    },
    defaultColDef: {
      wrapText: true,
    },
    rowHeight: 60,
    columnDefs: columns,
    rowData: tableData,
    onGridReady: onInfiniteGridReady,
    onSortChanged,
    pagination: false,
  };

  return (
    <>
      <AgTable
        gridOptions={isInfiniteGrid ? gridOptionsInfinite : gridOptions}
      />
      {showRejectionPopup && selectedRows && selectedRows.length && (
        <RejectionPopup
          closePopup={handleCancelRejection}
          isGrouped={isGrouped}
          itemToReject={selectedRows[0]}
          rejectionCategories={rejectionCategories}
          selectedStage={selectedStage}
          toggleRejectionPopup={toggleRejectionPopup}
          refreshTable={refreshTable}
          setSelectedRows={setSelectedRows}
          gridOptionsApi={gridOptionsApi}
          activeItemsRef={activeItemsRef}
          groupingType={groupingType}
        />
      )}
    </>
  );
};

export default EditingTable;
