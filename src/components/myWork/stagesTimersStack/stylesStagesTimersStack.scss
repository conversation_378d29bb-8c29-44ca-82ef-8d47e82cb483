@import "../../styles/colors.scss";
@import "../../styles/sizes.scss";

div.stages-timers-wrapper {
  background-color: $bgDark;
  height: calc(100vh - #{$headerFooter});

  @supports (-webkit-touch-callout: none) {
    /* CSS specific to iOS devices */
    height: calc(100vh - #{$headerFooter} - #{$iosAddressBar});
  }
  position: relative;
  font-size: 0.9rem;

  & h4 {
    color: $lighterSlate;
    text-align: center;
    margin: 0;
    padding-top: 8px;
    font-weight: normal;
  }

  & div.countdown-timer {
    margin-bottom: 16px;
  }
}

div.menu-closed {
  opacity: 0;
}
