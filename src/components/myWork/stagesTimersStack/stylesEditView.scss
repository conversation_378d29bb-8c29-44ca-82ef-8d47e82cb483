@import "../../styles/colors.scss";

.edit-view {
  height: 230px;
  background-color: $bgLightDark<PERSON>rey;

  .row {
    display: flex;
    flex-direction: column;
    height: 178px;
    padding-top: 10px;
    margin: 0 5px;

    & .dropdown-btn {
      width: 300px;
    }

    .col-label {
      padding: 3px 0 0 0;
      font-size: 0.8rem;
      color: white;
      margin-bottom: 5px;
    }

    .col-selection {
      padding: 5px;
      color: $textLight;
      margin-bottom: 35px;

      .col-list-wrapper {
        margin-left: 8px;
      }
    }

    .col-list-wrapper {
      width: 250px;
      margin-bottom: 40px;
      margin: 0 0 40px 0;

      &.wide {
        width: 410px;
      }
    }

    .edit-field {
      margin: 5px 10px 0 5px;
      height: 80px;
      display: flex;
      align-items: flex-start;
      width: 100%;
      min-width: 600px;
      max-width: 800px;

      & .value-wrapper {
        width: 300px;
      }

      input {
        padding: 5px 0 5px 5px;
        min-width: 250px;
        width: 300px;
        max-width: 400px;
        background-color: $rowEven;
        border: none;
        color: $textLight;
        font-size: 16px;
        height: 25px;
        &:focus {
          outline: none;
        }
      }

      & ::placeholder {
        color: $textGrey;
      }
    }

    & .spaced {
      margin-left: 250px;
    }

    & .quantity-input {
      margin: 5px 0 0 40px;

      & label {
        font-size: 0.8rem;
        color: $textLight;
        display: block;
        margin-bottom: 3px;
      }
      & input[type="number"] {
        padding: 5px 0 5px 5px;
        background-color: $rowEven;
        width: 100px;
        border: none;
        color: $textLight;
        font-size: 16px;
        &:focus {
          outline: none;
        }
        &:disabled {
          background-color: darken($rowEven, 8%);
          color: darken($textLight, 60%);
        }
      }
    }
  }

  .button-bar {
    display: flex;
    color: $blue;
    font-size: 1em;
    max-width: 500px;
    margin-top: 20px;

    .clear-selection {
      margin: 0 20px 0 24px;
      color: $buttonRed;
      cursor: pointer;
    }
    .save {
      margin: 0 0 0 auto;
      cursor: pointer;
    }
    .exit {
      margin: 0 40px 0 80px;
      color: $buttonRed;
      cursor: pointer;
    }
  }
}
