@import "../../../styles/colors.scss";

.disabled-wrapper {
  cursor: default;
  pointer-events: none;
  opacity: 0.5;
}

div.dropdown-wrapper {
  width: 300px;
  position: absolute;

  & div.dropdown-btn {
    display: inline-flex;
    justify-content: space-between;
    align-items: center;
    color: $blue;
    cursor: pointer;
    height: 30px;
    min-width: 120px;

    & svg {
      color: $blue;
      margin-left: 20px;
    }
  }

  & .empty {
    color: $textGrey !important;
  }

  & div.list-wrapper {
    position: relative;
    top: 10px;
    width: 300px;
    max-height: 150px;
    background-color: $bgDark;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    z-index: 50;
    border: 1px solid $blue;

    & p {
      color: $textLight;
      padding-left: 10px;
    }

    & div.search-wrapper {
      // border: 1px solid $blue;

      & input {
        width: 300px;
        padding: 3px 5px;
        height: 36px;
        margin: 0;
        box-sizing: border-box;
      }
    }

    & span {
      color: white;
      min-height: 25px;
      padding: 8px 5px;
      width: 300px;
      // margin: 8px 5px;
      font-size: 0.9rem;
      cursor: pointer;
      display: flex;
      align-items: center;
    }

    & .selected {
      background-color: $blue;
    }
  }
}
