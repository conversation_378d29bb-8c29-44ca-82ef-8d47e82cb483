// NPM PACKAGE IMPORTS
import React, { useState, useRef, useEffect, useMemo } from "react";
import { FaChevronDown } from "react-icons/fa";

// HELPER IMPORTS
import useOutsideClick from "../../../../hooks/useOutsideClick";

// STYLES IMPORTS
import "./stylesCreateDropdown.scss";

const CreateDropdown = ({
  list,
  selected,
  setSelected,
  placeholder = "Select column then row(s) to update",
  isSearchable,
  handleNewValue,
  selectedItems,
  type = "BASIC",
  selectedColumn,
  disabled,
}) => {
  const [showList, toggleList] = useState(false);
  const [searchInput, setSearchInput] = useState("");
  const [displayedOptions, setDisplayedOptions] = useState([]);

  const dropdownRef = useRef(null);

  useOutsideClick(dropdownRef, () => toggleList(false));

  useEffect(() => {
    if (!selectedItems?.length) setSearchInput("");
  }, [selectedItems]);

  const onEnter = (e) => {
    if (e.key === "Enter") {
      // clear search if not a match for existing mat and user closed dropdown
      if (
        selectedColumn === "Material Name" &&
        (!searchInput || !displayedOptions.includes(searchInput))
      ) {
        return toggleList(false);
      }
      handleNewValue(searchInput ? searchInput : null);
      toggleList(false);
    }
  };

  useEffect(() => {
    // clear search if not a match for existing mat and user closed dropdown
    if (
      !showList &&
      selectedColumn === "Material Name" &&
      (!searchInput || !displayedOptions.includes(searchInput))
    ) {
      setSearchInput("");
    }
  }, [showList]);

  const handleSelection = (item) => {
    setSelected(item);
    toggleList(false);
    setSearchInput("");
  };

  useEffect(() => {
    if (!searchInput || !searchInput.length || searchInput === "")
      setDisplayedOptions(list);
    else {
      const f_displayedOptions = list.filter((i) =>
        i.toLowerCase().includes(searchInput.toLowerCase())
      );
      setDisplayedOptions(f_displayedOptions);
    }
  }, [searchInput, list]);

  const displayedSelectedValue = useMemo(() => {
    if (type === "BASIC") return selected || null;
    else if (selectedItems && selectedItems.length) return selected || "";
    else return null;
  }, [selectedItems, selected]);

  const checkSelection = (value) => {
    if (type === "CREATE") {
      if (selectedColumn.includes("Heat Number")) {
        return (
          value ===
          `${selectedItems[0].heat_number} [${selectedItems[0].material_name}]`
        );
      }
      return value === selected;
    }
    if (value === displayedSelectedValue) return true;
    else return false;
  };

  return (
    <div
      className={
        disabled ? "disabled-wrapper dropdown-wrapper" : "dropdown-wrapper"
      }
    >
      <div
        onClick={() => toggleList(!showList)}
        className={
          displayedSelectedValue ? "dropdown-btn" : "empty dropdown-btn"
        }
      >
        <span>
          {displayedSelectedValue !== null
            ? displayedSelectedValue
            : placeholder}
        </span>
        <FaChevronDown />
      </div>

      {showList && (
        <div ref={dropdownRef} className="list-wrapper">
          {isSearchable && (
            <div className="search-wrapper">
              <input
                onKeyPress={(e) => onEnter(e)}
                placeholder={`Search ${type === "CREATE" ? "or Create" : ""}`}
                type="text"
                onChange={(e) => setSearchInput(e.target.value)}
              />
            </div>
          )}
          {displayedOptions && displayedOptions.length ? (
            displayedOptions.map((i, index) => (
              <span
                className={
                  (type !== "CREATE" &&
                    checkSelection(i) &&
                    displayedSelectedValue !== "") ||
                  (type === "CREATE" &&
                    displayedSelectedValue !== "" &&
                    checkSelection(i))
                    ? "selected"
                    : ""
                }
                key={index}
                onClick={() => handleSelection(i)}
              >
                {i}
              </span>
            ))
          ) : (
            <p>
              {!selectedItems || !selectedItems.length
                ? "Select a row in table"
                : "No options"}
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default CreateDropdown;
