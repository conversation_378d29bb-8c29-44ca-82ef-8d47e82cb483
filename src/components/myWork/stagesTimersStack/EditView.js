import React, { useState, useEffect, useRef, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import ReactDOM from "react-dom";

// REDUX IMPORTS
import {
  handleFetchJoiningProcedures,
  handleFetchMaterialTypes,
  handleUpdateJointHeatNumber,
  handleUpdateItems,
  handleUpdateGroupedItems,
  handleCreateLaydownLocation,
} from "./../../items/itemsActions";
import { handleFetchItemHeatNumbers } from "./../../drawings/drawingsActions";
import { handleFetchLaydownLocations } from "../../shipping/shippingActions";
import { handleUpdateManyCustomColumnData } from "../../customColumns/customColumnsActions";

// COMPONENT IMPORTS
import CreateDropdown from "./createDropdown/CreateDropdown";
import ConfirmationModal from "../../reusable/confirmationModal/ConfirmationModal";

// UTILS IMPORTS
import {
  fractionalRegex,
  multidimensionRegex,
  convertFracToDec,
  multidimensionOnlyX,
} from "../../../_utils";

// STYLES IMPORTS
import "./stylesEditView.scss";

const dropdownColumns = [
  "Material Name",
  "Joining Procedure",
  "Laydown Location",
  "Heat Number",
  "Joint Heat Number 1",
  "Joint Heat Number 2",
];
const fractionalColumns = ["Length"];

const EditView = ({
  startingValue, //updates as user selects rows in items table
  editableColumns, //columns we can edit
  selectedRows,
  onClearTableSelection,
  refreshTable,
  setNotif,
  isGrouped,
  groupingType,
  editableCustomColumns,
  gridOptionsApi,
}) => {
  const liRefs = useRef([]);
  const [currentValue, setCurrentValue] = useState(null);
  const [fullValue, setFullValue] = useState("");
  const [editOptions, setEditOptions] = useState([]);
  const [filteredEditOptions, setFilteredEditOptions] = useState([]);
  const [columns, setColumns] = useState([]);
  const [columnNames, setColumnNames] = useState([]);
  const [selectedColumn, setSelectedColumn] = useState("");
  const [updatedQuantity, setUpdatedQuantity] = useState(null);
  const [isNewValue, toggleNewValue] = useState(false);
  const [showConfirmation, toggleConfirmation] = useState(false);

  const dispatch = useDispatch();
  const { materialTypes, joiningProcedures } = useSelector(
    (state) => state.itemsData
  );
  const { heatNumbers } = useSelector((state) => state.drawingsData);
  const { laydown_locations } = useSelector((state) => state.shippingData);

  const showHeatNumberDropdown = useMemo(() => {
    if (
      !selectedRows ||
      !selectedRows.length ||
      selectedRows[0].size === null ||
      selectedRows[0].material_name === null ||
      !selectedRows.every(
        (r) =>
          r.size === selectedRows[0].size &&
          r.material_name === selectedRows[0].material_name
      )
    )
      return false;
    else return true;
  }, [selectedRows, selectedColumn]);

  const showDropdownValues = useMemo(() => {
    if (dropdownColumns.includes(selectedColumn)) {
      if (
        (selectedColumn === "Heat Number" ||
          selectedColumn.includes("Joint Heat Number")) &&
        showHeatNumberDropdown
      ) {
        return true;
      } else if (
        (selectedColumn === "Heat Number" ||
          selectedColumn.includes("Joint Heat Number")) &&
        !showHeatNumberDropdown
      ) {
        return false;
      } else return true;
    } else return false;
  }, [selectedColumn, showHeatNumberDropdown]);

  useEffect(() => {
    if (!selectedRows || !selectedRows.length) return;
    setCurrentValue(startingValue);
  }, [startingValue, selectedRows]);

  // split out joint heat number into 2 columns
  useEffect(() => {
    let visibleCols = editableColumns.filter((c) => c.visible_in_work_table);
    let jointHeaNumberIndex = visibleCols
      .map((vc) => vc.display_name)
      .indexOf("Joint Heat Number");
    // if we have joint heat number col, split it into two editable columns
    if (jointHeaNumberIndex > -1) {
      let base = visibleCols.splice(jointHeaNumberIndex, 1);
      visibleCols.push(
        {
          ...base[0],
          display_name: `${base[0].display_name} 1`,
        },
        {
          ...base[0],
          display_name: `${base[0].display_name} 2`,
        }
      );
    }

    setColumns(visibleCols);
    setColumnNames(visibleCols.map((c) => c.display_name));
  }, [editableColumns]);

  useEffect(() => {
    let finalOptions = [];
    // format options to strings for display
    if (selectedColumn === "Material Name")
      finalOptions = (materialTypes || []).map((i) => i.name);
    else if (selectedColumn === "Joining Procedure")
      finalOptions = (joiningProcedures || []).map((i) => i.name);
    else if (selectedColumn === "Laydown Location")
      finalOptions = (laydown_locations || []).map((ll) => ll.name);
    else if (
      selectedColumn === "Heat Number" &&
      selectedRows &&
      selectedRows.length
    ) {
      finalOptions = (heatNumbers || []).map(
        (hn) => `${hn.heat_number} [${selectedRows[0].material_name}]`
      );
    } else if (selectedColumn.includes("Joint Heat Number"))
      finalOptions = (heatNumbers || []).map(
        (hn) => `${hn.heat_number} [${hn.material_name}]`
      );

    setEditOptions(finalOptions);
    setFilteredEditOptions(finalOptions);
    // trigger scroll after rerender
    if (selectedColumn.includes("Joint Heat Number")) {
      let heatNumberObj =
        (heatNumbers || []).find(
          (hn) => `${hn.heat_number} [${hn.material_name}]` === fullValue
        ) || {};
      // set a small timeout for the list to refresh and recreate the liRefs again before scrolling
      setTimeout(
        (_) =>
          scrollToView(
            finalOptions,
            `${heatNumberObj.heat_number} [${heatNumberObj.material_name}]`
          ),
        160
      );
    } else setTimeout((_) => scrollToView(finalOptions, currentValue), 160);
  }, [materialTypes, joiningProcedures, heatNumbers, laydown_locations]);

  const changeSelectedColumn = (i) => {
    setSelectedColumn(i);
    setEditOptions([]);
    setFilteredEditOptions([]);
    fetchEditOptions(i);

    let value = "";
    let shouldShow = true;
    if (selectedRows.length) {
      if (i.includes("Joint Heat Number")) {
        let nameArr = i.split(" ");
        let position = Number.parseInt(nameArr[nameArr.length - 1]);
        value =
          (
            JSON.parse(selectedRows[0].joint_heat_numbers).find(
              (i) => i.position === position
            ) || {}
          ).heat_number || "";
        shouldShow = selectedRows.every(
          (r) =>
            ((
              JSON.parse(r.joint_heat_numbers).find(
                (i) => i.position === position
              ) || {}
            ).heat_number || "") === value
        );
      } else {
        value =
          selectedRows[0][columns[columnNames.indexOf(i)].normal_name] || "";
        shouldShow = selectedRows.every(
          (r) =>
            (r[columns[columnNames.indexOf(i)].normal_name] || "") === value
        );
      }
      if (fractionalColumns.includes(i)) {
        const displayValue = value.display;
        return setCurrentValue(shouldShow ? displayValue : "");
      }
      setCurrentValue(shouldShow ? value : "");
      if (i.includes("Joint Heat Number")) {
        if (!shouldShow || value.trim() === "") {
          setFullValue("");
        } else if (shouldShow && value && selectedRows.length) {
          let heatNumberObjArr =
            heatNumbers && heatNumbers.filter((hn) => hn.heat_number === value);
          if (heatNumberObjArr && heatNumberObjArr.length === 1)
            setFullValue(`${value} [${heatNumberObjArr[0].material_name}]`);
          else setFullValue(`${value} [${selectedRows[0].material_name}]`);
        }
      } else setFullValue("");
    }
  };

  const changeValue = (newValue, isTextEntry) => {
    let val = "";
    let newFilteredOptions = editOptions;
    let heatNumberObj = {};

    if (currentValue !== newValue) {
      if (selectedColumn.includes("Joint Heat Number") && !isTextEntry) {
        heatNumberObj =
          (heatNumbers || []).find(
            (hn) =>
              `${hn.heat_number} [${
                selectedRows.length ? selectedRows[0].material_name : ""
              }]` === newValue
          ) || {};
        val = heatNumberObj.heat_number || "";
        if (val.trim() === "") setFullValue("");
        else setFullValue(newValue);
      } else val = newValue;
      if (isTextEntry) {
        if (selectedColumn.includes("Joint Heat Number")) {
          if (val.trim() === "") {
            setFullValue("");
          } else if (val && selectedRows.length) {
            let heatNumberObjArr = heatNumbers.filter(
              (hn) => hn.heat_number === val
            );
            if (heatNumberObjArr.length === 1)
              setFullValue(`${val} [${heatNumberObjArr[0].material_name}]`);
            else setFullValue(`${val} [${selectedRows[0].material_name}]`);
          }
        }
        let index = editOptions
          .map((e) => e.toLowerCase())
          .indexOf(val.toLowerCase());
        if (index > -1) val = editOptions[index];
        newFilteredOptions = editOptions.filter((e) =>
          e.toLowerCase().includes(val.toLowerCase().trim())
        );
        setFilteredEditOptions(newFilteredOptions);
      } else if (filteredEditOptions.length < editOptions.length) {
        setFilteredEditOptions(newFilteredOptions);
      }
    }
    setCurrentValue(val);
    // set a small timeout for the list to refresh and recreate the liRefs again before scrolling
    if (selectedColumn.includes("Joint Heat Number") && !isTextEntry) {
      setTimeout(
        (_) =>
          scrollToView(
            newFilteredOptions,
            `${heatNumberObj.heat_number} [${heatNumberObj.material_name}]`
          ),
        160
      );
    } else {
      setTimeout((_) => scrollToView(newFilteredOptions, val), 160);
    }
  };

  useEffect(() => {
    let value = "";
    let material = "";
    let shouldShow = true;

    // populate text field and load appropriate dropdowns based on column
    if (selectedRows.length) {
      if (selectedColumn.includes("Joint Heat Number")) {
        let nameArr = selectedColumn.split(" ");
        let position = Number.parseInt(nameArr[nameArr.length - 1]);
        value =
          (
            JSON.parse(selectedRows[0].joint_heat_numbers).find(
              (i) => i.position === position
            ) || {}
          ).heat_number || "";
        material = selectedRows[0].material_name || "";
        shouldShow = selectedRows.every(
          (r) =>
            ((
              JSON.parse(r.joint_heat_numbers).find(
                (i) => i.position === position
              ) || {}
            ).heat_number || "") === value
        );
      } else {
        value =
          selectedRows[0][
            (columns[columnNames.indexOf(selectedColumn)] || {}).normal_name
          ] || "";
        material = selectedRows[0].material_name || "";
        shouldShow = selectedRows.every(
          (r) =>
            (r[
              (columns[columnNames.indexOf(selectedColumn)] || {}).normal_name
            ] || "") === value
        );
      }
      if (!shouldShow || (typeof value === "string" && value.trim() === ""))
        setFullValue("");
      else if (
        selectedColumn.includes("Joint Heat Number") ||
        selectedColumn === "Heat Number"
      ) {
        setFullValue(`${value} [${material}]`);
        setCurrentValue(value);
      } else if (fractionalColumns.includes(selectedColumn)) {
        let displayValue = value.display;
        setCurrentValue(shouldShow ? displayValue : "");
      } else {
        setCurrentValue(shouldShow ? value : "");
      }
    } else clearSelection(true);

    fetchEditOptions(selectedColumn);
  }, [selectedRows]);

  const fetchEditOptions = (columnName) => {
    if (selectedRows.length) {
      if (columnName === "Material Name") dispatch(handleFetchMaterialTypes());
      else if (columnName === "Joining Procedure")
        dispatch(handleFetchJoiningProcedures);
      else if (columnName === "Laydown Location")
        dispatch(handleFetchLaydownLocations());
      else if (
        columnName === "Heat Number" ||
        columnName.includes("Joint Heat Number")
      ) {
        if (!selectedRows || !selectedRows.length) {
          return setNotif("WARN", "No rows selected");
        }

        if (selectedRows[0].size === null) {
          return setNotif("WARN", "No size is present, please add a size");
        }

        if (selectedRows[0].material_name === null) {
          return setNotif("WARN", "Material name is null");
        }

        if (
          !selectedRows.every(
            (r) =>
              r.size === selectedRows[0].size &&
              r.material_name === selectedRows[0].material_name
          )
        ) {
          return setNotif("WARN", "No heat numbers");
        }

        dispatch(
          handleFetchItemHeatNumbers(
            columnName === "Heat Number" ? "" : "JOINT",
            [...new Set([...selectedRows.map((r) => r.job_id)])],
            selectedRows[0].size,
            selectedRows[0].material_name,
            selectedRows.map((r) => r.drawing_id)
          )
        );
      }
    } else clearSelection(true);
  };

  const scrollToView = (newFilteredOptions, val) => {
    if (newFilteredOptions.length) {
      let index = newFilteredOptions.indexOf(val);
      if (index > -1 && index < liRefs.current.length) {
        let listItem = ReactDOM.findDOMNode(liRefs.current[index]);
        if (listItem) listItem.scrollIntoView();
      }
    }
  };

  // TEST THIS!!!!!
  const formatCustomColumnDataForUpdate = (
    customColumnId,
    parentIds,
    newValue
  ) => {
    let result = [];
    for (let id of parentIds) {
      result.push({
        custom_columns_id: customColumnId,
        parent_id: parseInt(id),
        data: newValue,
      });
    }

    return result;
  };

  const handleSave = () => {
    // check if selected column is custom
    const customColumn = editableCustomColumns?.find(
      (cc) => cc.display_name === selectedColumn
    );

    if (customColumn) {
      if (!selectedRows?.length) return setNotif("WARN", "No rows selected");

      const updateData = formatCustomColumnDataForUpdate(
        customColumn?.id,
        selectedRows?.map((r) => r.id),
        currentValue
      );
      dispatch(handleUpdateManyCustomColumnData("items", updateData));

      gridOptionsApi.purgeInfiniteCache();

      return clearSelection();
    }

    if (selectedColumn.includes("Joint Heat Number")) {
      let columnArr = selectedColumn.split(" ");
      let dropdownOption = (heatNumbers || []).find(
        (hn) => `${hn.heat_number} [${hn.material_name}]` === currentValue
      );

      // only send heat number and not the material name for heat numbers
      let newValue = dropdownOption ? dropdownOption.heat_number : currentValue;
      let position = Number.parseInt(columnArr[columnArr.length - 1]);

      if (
        selectedRows.every(
          (r) =>
            JSON.parse(r.joint_heat_numbers) &&
            JSON.parse(r.joint_heat_numbers).find(
              (hn) => hn.position === position
            ) &&
            JSON.parse(r.joint_heat_numbers).find(
              (hn) => hn.position === position
            ).heat_number === newValue
        )
      ) {
        return setNotif("WARN", "No changes were made to update");
      }

      const updatedJointHeatNumber = {
        work_item_ids: selectedRows.map((i) => i.id),
        position: position,
        heat_number: newValue ? newValue : null,
      };

      clearSelection(false, () =>
        dispatch(
          handleUpdateJointHeatNumber(
            updatedJointHeatNumber,
            {
              isWorkable: true,
            },
            refreshTable
          )
        )
      );
    } else {
      let workItemIds = selectedRows.map((i) => i.id);
      let updatedValue = [];
      if (selectedColumn === "Heat Number") {
        if (currentValue && currentValue.trim() === "") return clearSelection;

        let dropdownOption = (heatNumbers || []).find(
          (hn) =>
            `${hn.heat_number} [${
              selectedRows.length ? selectedRows[0].material_name : ""
            }]` === currentValue
        );

        // only send heat number and not the material name for heat numbers
        let newValue = dropdownOption
          ? dropdownOption.heat_number
          : currentValue;

        if (
          (newValue === "" &&
            selectedRows.every(
              (r) => r.heat_number && r.heat_number === newValue
            )) ||
          selectedRows.every((r) => r.heat_number && r.heat_number === newValue)
        ) {
          return setNotif("WARN", "No changes were made to update");
        }

        updatedValue.push({ heat_number: newValue ? newValue : null });
      } else if (selectedColumn === "Joint Heat Number") {
        // only send heat number and not the material name for heat numbers
        let dropdownOption = (heatNumbers || []).find(
          (hn) => `${hn.heat_number} [${hn.material_name}]` === currentValue
        );
        let newValue = dropdownOption
          ? dropdownOption.heat_number
          : currentValue;
        if (
          newValue === "" ||
          selectedRows.every((r) => r.heat_number === newValue)
        ) {
          return setNotif("WARN", "No changes were made to update");
        }
        updatedValue.push({
          heat_number: newValue ? newValue : null,
        });
      } else if (
        fractionalColumns.includes(selectedColumn) ||
        selectedColumn === "Size"
      ) {
        let column = columns[columnNames.indexOf(selectedColumn)];
        if (selectedColumn === "Size") {
          // multidimensionalRegex
          if (
            !multidimensionRegex.test(currentValue) ||
            !multidimensionOnlyX.test(currentValue)
          )
            return setNotif("WARN", `Please enter multidimensional format.`);
        } else if (!fractionalRegex.test(currentValue) && currentValue)
          return setNotif("WARN", `Please enter correct fractional format.`);

        if (selectedColumn === "Size") {
          updatedValue.push({ [column.normal_name]: currentValue || null });
        } else
          updatedValue.push({
            [column.normal_name]: currentValue
              ? convertFracToDec(currentValue)
              : null,
          });
      } else {
        let column = columns[columnNames.indexOf(selectedColumn)];
        if (
          (selectedRows.every((r) => r[column.normal_name] === null) &&
            currentValue === "") ||
          selectedRows.every((r) => r[column.normal_name] === currentValue)
        ) {
          return setNotif("WARN", "No changes were made to update");
        }

        // material handling is the same for grouped and ungrouped
        let updatedMaterial;
        if (column.normal_name === "material_name") {
          updatedMaterial = materialTypes.find((m) => m.name === currentValue);

          // should never be undefined as user can only select from existing, but just in case
          updatedValue.push({ material_type_id: updatedMaterial?.id });
          setNotif(
            "WARN",
            "If selected material does not match the conditions for this stage, the item may no longer be available at this stage."
          );
        }

        if (isGrouped) {
          if (column.normal_name === "joining_procedure_name") {
            const updatedJoiningProcedure = joiningProcedures.find(
              (jp) => jp.name === currentValue
            );
            if (!updatedJoiningProcedure)
              return setNotif("ERROR", `Couldn't update joining_procedure`);

            updatedValue.push({
              joining_procedure_id: updatedJoiningProcedure.id,
            });
          } else if (column.normal_name !== "material_name") {
            updatedValue.push({
              [column.normal_name]: currentValue ? currentValue : null,
            });
          }
        } else if (column.normal_name === "laydown_location_name") {
          if (!currentValue) updatedValue.push({ laydown_location_id: null });
          else {
            updatedValue.push({
              laydown_location_id:
                laydown_locations.find(
                  (ll) => ll.name === currentValue.toString().trim()
                )?.id || null,
            });
          }
        } else if (column.normal_name !== "material_name") {
          updatedValue.push({
            [column.normal_name]: currentValue ? currentValue : null,
          });
        }
      }

      if (isGrouped) {
        const quantity = selectedRows.reduce((acc, curr) => {
          if (!curr.quantity) return 0;
          return acc + curr.quantity;
        }, 0);

        clearSelection(false, () =>
          dispatch(
            handleUpdateGroupedItems(
              workItemIds.toString(),
              updatedValue[0],
              // If Advanced Grouping, pass in 0 as quantity to update all items in group
              groupingType === "Advanced Grouping"
                ? 0
                : parseFloat(updatedQuantity) || quantity,
              refreshTable
            )
          )
        );
        setUpdatedQuantity(null);
      } else {
        if (selectedColumn === "Laydown Location") {
          clearSelection(false, () =>
            dispatch(
              handleCreateLaydownLocation(
                selectedRows.map((r) => r.id),
                currentValue
              )
            ).then((res) => {
              if (!res.error) refreshTable();
            })
          );
        } else
          clearSelection(false, () =>
            dispatch(
              handleUpdateItems(
                workItemIds,
                updatedValue,
                { isWorkable: true },
                refreshTable
              )
            )
          );
      }
    }
    toggleNewValue(false);
  };

  const onSave = () => {
    if (isNewValue && selectedColumn !== "Heat Number" && currentValue) {
      return toggleConfirmation(true);
    }
    handleSave();
  };

  const clearSelection = (onlyLocal = false, callback, exit) => {
    setCurrentValue(null);
    setFullValue("");
    setEditOptions([]);
    setFilteredEditOptions([]);
    if (!onlyLocal) onClearTableSelection(exit);

    if (callback && typeof callback === "function") callback();
  };

  const handleNewValue = (newValue) => {
    setCurrentValue(newValue);
    if (newValue === "") return;
    toggleNewValue(true);
  };

  const displayedOptions = filteredEditOptions.map((i, index) => (
    <li
      ref={(el) => {
        if (index === 0) return (liRefs.current = [el]);
        else return (liRefs.current = [...liRefs.current, el]);
      }}
      className={
        selectedColumn.includes("Joint Heat Number")
          ? i === fullValue && currentValue
            ? "selected"
            : ""
          : currentValue === i
          ? "selected"
          : ""
      }
      key={index}
      onClick={() => changeValue(i)}
    >
      {i}
    </li>
  ));

  return (
    <div className={`edit-view`}>
      <div className={"row"}>
        <div className={"col-selection"}>
          <div className={"col-label"}>Column to Update</div>
          <CreateDropdown
            list={columnNames}
            selected={selectedColumn}
            setSelected={changeSelectedColumn}
            placeholder="Column To Update"
            type="BASIC"
          />
        </div>
        <div className={"edit-field"}>
          <div className="value-wrapper">
            <div className="col-label">{selectedColumn || "Value"}</div>
            {showDropdownValues ? (
              <CreateDropdown
                list={filteredEditOptions}
                selected={currentValue !== "" ? currentValue : null}
                fullSelected={fullValue}
                setSelected={setCurrentValue}
                isSearchable={true}
                handleNewValue={handleNewValue}
                selectedItems={selectedRows}
                // can't create new materials in /my-work
                type={selectedColumn === "Material Name" ? "BASIC" : "CREATE"}
                selectedColumn={selectedColumn}
              />
            ) : (
              <>
                <input
                  autoComplete={"off"}
                  type={selectedColumn !== "Quantity" ? "text" : "number"}
                  id="value"
                  name="value"
                  value={currentValue || ""}
                  onChange={(e) => changeValue(e.target.value, true)}
                  placeholder="Select Column then row(s) to update"
                />
                {!dropdownColumns.includes(selectedColumn) && (
                  <div className={"col-list-wrapper wide"}>
                    <div className={"col-list"}>
                      <ul>{displayedOptions}</ul>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
          {isGrouped && (
            <div
              className={
                dropdownColumns.includes(selectedColumn)
                  ? "spaced quantity-input"
                  : "quantity-input"
              }
            >
              <label>Quantity to update</label>
              <input
                value={
                  updatedQuantity !== null
                    ? updatedQuantity
                    : selectedRows.length && selectedRows[0].quantity
                    ? selectedRows[0].quantity.toFixed(1)
                    : ""
                }
                type="number"
                onChange={(e) => setUpdatedQuantity(e.target.value)}
                disabled={groupingType === "Advanced Grouping"}
              />
            </div>
          )}
        </div>
      </div>
      <div className={"button-bar"}>
        <div
          className={"clear-selection"}
          onClick={(_) => clearSelection(false)}
        >
          Clear Selection
        </div>
        <div className={"save"} onClick={onSave}>
          Save
        </div>
        <div
          className={"exit"}
          onClick={(_) => clearSelection(false, false, true)}
        >
          Exit
        </div>
      </div>
      {showConfirmation && (
        <ConfirmationModal
          showModal={showConfirmation}
          handleClick={() => {
            toggleConfirmation(false);
            handleSave();
          }}
          action="SAVE"
          item={selectedColumn}
          toggleModal={toggleConfirmation}
          message={`You have entered a ${selectedColumn} that isn't in the list. Do you wish to add the ${selectedColumn}: ${currentValue}? `}
          submitText="Save"
        />
      )}
    </div>
  );
};

export default EditView;
