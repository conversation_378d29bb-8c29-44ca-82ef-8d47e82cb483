import { selectedStageViewColumnDefs } from "./selectedStageViewConstants";

describe("Selected Stage View", () => {
  const sortState = { sorting_column_name: "id", sorting_method: "asc" };

  const workStageColumns = [
    {
      id: 1,
      display_name: "Job Number",
      name: "<PERSON><PERSON><PERSON><PERSON>",
      normal_name: "job_number",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Jobs",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 2,
      display_name: "Job Name",
      name: "<PERSON><PERSON><PERSON><PERSON>",
      normal_name: "job_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Jobs",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
    {
      id: 3,
      display_name: "Package ID",
      name: "id",
      normal_name: "package_id",
      data_type: "integer",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Packages",
      editable: 0,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 4,
      display_name: "Package Name",
      name: "PackageName",
      normal_name: "package_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Packages",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
    {
      id: 5,
      display_name: "Package Number",
      name: "number",
      normal_name: "package_number",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Packages",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 6,
      display_name: "Package Due Date",
      name: "dueDate",
      normal_name: "package_due_date",
      data_type: "date",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Packages",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 8,
      display_name: "Package Area",
      name: "area",
      normal_name: "package_area",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Packages",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 9,
      display_name: "Drawing Name",
      name: "SpoolName",
      normal_name: "drawing_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Spools",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
    {
      id: 10,
      display_name: "Drawing Due Date",
      name: "dueDate",
      normal_name: "drawing_due_date",
      data_type: "date",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Spools",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 11,
      display_name: "Drawing Priority",
      name: "priority",
      normal_name: "drawing_priority",
      data_type: "integer",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Spools",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 12,
      display_name: "Drawing Area",
      name: "area",
      normal_name: "drawing_area",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Spools",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 14,
      display_name: "Tag Number",
      name: "tag_number",
      normal_name: "tag_number",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 15,
      display_name: "End Prep 1",
      name: "end_prep_1",
      normal_name: "end_prep_1",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 16,
      display_name: "End Prep 2",
      name: "end_prep_2",
      normal_name: "end_prep_2",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 17,
      display_name: "End Prep 3",
      name: "end_prep_3",
      normal_name: "end_prep_3",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 18,
      display_name: "End Prep 4",
      name: "end_prep_4",
      normal_name: "end_prep_4",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 19,
      display_name: "Length",
      name: "rounded_cut_length",
      normal_name: "length",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
    {
      id: 20,
      display_name: "Stock Length",
      name: "stock_length",
      normal_name: "stock_length",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 22,
      display_name: "Height",
      name: "height",
      normal_name: "height",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 23,
      display_name: "Width",
      name: "width",
      normal_name: "width",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 24,
      display_name: "Thickness",
      name: "thickness",
      normal_name: "thickness",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 25,
      display_name: "Paint Spec",
      name: "paint_spec",
      normal_name: "paint_spec",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 26,
      display_name: "Texture",
      name: "texture",
      normal_name: "texture",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 27,
      display_name: "Fixture",
      name: "fixture_type",
      normal_name: "fixture_type",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 28,
      display_name: "Gauge",
      name: "gauge",
      normal_name: "gauge",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 29,
      display_name: "Weight",
      name: "weight",
      normal_name: "weight",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 30,
      display_name: "Hanger Size",
      name: "hanger_size",
      normal_name: "hanger_size",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 31,
      display_name: "Product Code",
      name: "product_code",
      normal_name: "product_code",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 32,
      display_name: "Insulation",
      name: "insulation",
      normal_name: "insulation",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 33,
      display_name: "Insulation Area",
      name: "insulation_area",
      normal_name: "insulation_area",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 34,
      display_name: "Insulation Spec",
      name: "insulation_specification",
      normal_name: "insulation_specification",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 35,
      display_name: "Insulation Gauge",
      name: "insulation_gauge",
      normal_name: "insulation_gauge",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 36,
      display_name: "Joining Procedure",
      name: "name",
      normal_name: "joining_procedure_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "joining_procedures",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
    {
      id: 37,
      display_name: "Material Name",
      name: "name",
      normal_name: "material_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "material_types",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
    {
      id: 38,
      display_name: "Service",
      name: "service_name",
      normal_name: "service_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 39,
      display_name: "Service Color",
      name: "service_color_name",
      normal_name: "service_color_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 41,
      display_name: "Size",
      name: "size",
      normal_name: "size",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
    {
      id: 42,
      display_name: "Area",
      name: "area",
      normal_name: "area",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 43,
      display_name: "Random Length",
      name: "random_length",
      normal_name: "random_length",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 44,
      display_name: "Rod Size",
      name: "rod_size",
      normal_name: "rod_size",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 45,
      display_name: "Support Rod Length 1",
      name: "support_rod_length",
      normal_name: "support_rod_length",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 46,
      display_name: "Laydown Location",
      name: "name",
      normal_name: "laydown_location_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "laydown_locations",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 47,
      display_name: "Liner Spec",
      name: "liner_spec",
      normal_name: "liner_spec",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 48,
      display_name: "Heat Number",
      name: "heat_number",
      normal_name: "heat_number",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
    {
      id: 49,
      display_name: "Joint Heat Number",
      name: "heat_number",
      normal_name: "heat_number",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "joint_heat_numbers",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 50,
      display_name: "Container",
      name: "name",
      normal_name: "container_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "containers",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 51,
      display_name: "Filler Metal",
      name: "filler_metal",
      normal_name: "filler_metal",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 52,
      display_name: "Vendor",
      name: "vendor",
      normal_name: "vendor",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 53,
      display_name: "Measurement Area",
      name: "measurement_area",
      normal_name: "measurement_area",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 54,
      display_name: "Support Rod Length 2",
      name: "support_rod_length_2",
      normal_name: "support_rod_length_2",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
  ];
  const workStageGroupableColumns = [
    {
      id: 2,
      display_name: "Job Name",
      name: "JobTitle",
      normal_name: "job_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Jobs",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
    {
      id: 4,
      display_name: "Package Name",
      name: "PackageName",
      normal_name: "package_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Packages",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
    {
      id: 9,
      display_name: "Drawing Name",
      name: "SpoolName",
      normal_name: "drawing_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Spools",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
    {
      id: 19,
      display_name: "Length",
      name: "rounded_cut_length",
      normal_name: "length",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
    {
      id: 37,
      display_name: "Material Name",
      name: "name",
      normal_name: "material_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "material_types",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
    {
      id: 41,
      display_name: "Size",
      name: "size",
      normal_name: "size",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
    {
      id: 48,
      display_name: "Heat Number",
      name: "heat_number",
      normal_name: "heat_number",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
    {
      id: 36,
      display_name: "Joining Procedure",
      name: "name",
      normal_name: "joining_procedure_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "joining_procedures",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
  ];

  describe("Ungrouped Column Defs", () => {
    const defaultHeaders = [
      "Area",
      "Container",
      "Drawing Area",
      "Drawing Due Date",
      "Drawing Name",
      "Drawing Priority",
      "End Prep 1",
      "End Prep 2",
      "End Prep 3",
      "End Prep 4",
      "Filler Metal",
      "Fixture",
      "Gauge",
      "Hanger Size",
      "Heat Number",
      "Height",
      "Insulation",
      "Insulation Area",
      "Insulation Gauge",
      "Insulation Spec",
      "Job Name",
      "Job Number",
      "Joining Procedure",
      "Joint Heat Number 1",
      "Joint Heat Number 2",
      "Laydown Location",
      "Length",
      "Liner Spec",
      "Material Name",
      "Measurement Area",
      "Package Area",
      "Package Due Date",
      "Package ID",
      "Package Name",
      "Package Number",
      "Paint Spec",
      "Product Code",
      "Random Length",
      "Rod Size",
      "Service",
      "Service Color",
      "Size",
      "Stock Length",
      "Support Rod Length 1",
      "Support Rod Length 2",
      "Tag Number",
      "Texture",
      "Thickness",
      "Vendor",
      "Weight",
      "Width",
    ];

    let populatedColumns;

    beforeEach(() => {
      populatedColumns = selectedStageViewColumnDefs(
        null,
        workStageColumns,
        null,
        false,
        "Ungrouped",
        sortState
      );
    });

    it("Headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);

      expect(columnHeaders).toEqual(defaultHeaders);
    });

    describe("JOB NUMBER", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Job Number");
      });

      const params = {
        data: {
          job_number: "1",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("1");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("1");
      });
    });

    describe("JOB NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Job Name");
      });

      const params = {
        data: {
          job_name: "test job",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test job");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test job");
      });
    });

    describe("PACKAGE ID", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Package ID");
      });

      const params = {
        data: {
          package_id: 1,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(1);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(1);
      });
    });

    describe("PACKAGE NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Package Name");
      });

      const params = {
        data: {
          package_name: "test package",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test package");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test package");
      });
    });

    describe("PACKAGE NUMBER", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Package Number"
        );
      });

      const params = {
        data: {
          package_number: "1",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("1");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("1");
      });
    });

    describe("PACKAGE DUE DATE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Package Due Date"
        );
      });

      const params = {
        data: {
          package_due_date: "2020-09-17T05:00:00.000Z",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(
          "2020-09-17T05:00:00Z"
        );
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("2020-09-17T05:00:00.000Z");
      });

      it("valueFormatter", () => {
        expect(column.valueFormatter(params)).toEqual("2020-09-17T05:00:00Z");
      });
    });

    describe("PACKAGE AREA", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Package Area");
      });

      const params = {
        data: {
          package_area: "test area",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test area");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test area");
      });
    });

    describe("DRAWING NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Drawing Name");
      });

      const params = {
        data: {
          drawing_name: "test drawing",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test drawing");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test drawing");
      });
    });

    describe("DRAWING DUE DATE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Drawing Due Date"
        );
      });

      const params = {
        data: {
          drawing_due_date: "2020-09-17T05:00:00.000Z",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(
          "2020-09-17T05:00:00Z"
        );
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("2020-09-17T05:00:00.000Z");
      });

      it("valueFormatter", () => {
        expect(column.valueFormatter(params)).toEqual("2020-09-17T05:00:00Z");
      });
    });

    describe("DRAWING AREA", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Drawing Area");
      });

      const params = {
        data: {
          drawing_area: "test area",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test area");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test area");
      });
    });

    describe("TAG NUMBER", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Tag Number");
      });

      const params = {
        data: {
          tag_number: "1",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("1");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("1");
      });
    });

    describe("END PREP 1", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "End Prep 1");
      });

      const params = {
        data: {
          end_prep_1: "test prep",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test prep");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test prep");
      });
    });

    describe("END PREP 2", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "End Prep 2");
      });

      const params = {
        data: {
          end_prep_2: "test prep",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test prep");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test prep");
      });
    });

    describe("END PREP 3", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "End Prep 3");
      });

      const params = {
        data: {
          end_prep_3: "test prep",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test prep");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test prep");
      });
    });

    describe("END PREP 4", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "End Prep 4");
      });

      const params = {
        data: {
          end_prep_4: "test prep",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test prep");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test prep");
      });
    });

    describe("LENGTH", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Length");
      });

      const params = {
        data: {
          length: {
            decimal: 3.5,
            display: `3 1/2"`,
          },
        },
      };
      const multiDimensionParams = {
        data: {
          length: {
            decimal: 16.069433212,
            display: `1'-4"`,
          },
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(`3 1/2"`);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(`3 1/2"`);
      });
      it("valueGetter", () => {
        expect(column.valueGetter(multiDimensionParams)).toEqual(`1'-4"`);
      });
      it("comparator", () => {
        const values = [
          {
            data: {
              length: {
                decimal: 2,
                display: `2"`,
              },
            },
          },
          {
            data: {
              length: {
                decimal: 7,
                display: `7"`,
              },
            },
          },
          {
            data: {
              length: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
        ];

        expect(
          values.sort((a, b) => column.comparator(null, null, a, b))
        ).toEqual([
          {
            data: {
              length: {
                decimal: 2,
                display: `2"`,
              },
            },
          },
          {
            data: {
              length: {
                decimal: 7,
                display: `7"`,
              },
            },
          },
          {
            data: {
              length: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
        ]);
      });
    });

    describe("STOCK LENGTH", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Stock Length");
      });

      const params = {
        data: {
          stock_length: 2,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(2);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(2);
      });
    });

    describe("HEIGHT", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Height");
      });

      const params = {
        data: {
          height: 3,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(3);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(3);
      });
    });

    describe("WIDTH", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Width");
      });

      const params = {
        data: {
          width: 10,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(10);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(10);
      });
    });

    describe("THICKNESS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Thickness");
      });

      const params = {
        data: {
          thickness: 3,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(3);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(3);
      });
    });

    describe("PAINT SPEC", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Paint Spec");
      });

      const params = {
        data: {
          paint_spec: "test spec",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test spec");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test spec");
      });
    });

    describe("TEXTURE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Texture");
      });

      const params = {
        data: {
          texture: "test texture",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test texture");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test texture");
      });
    });

    describe("FIXTURE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Fixture");
      });

      const params = {
        data: {
          fixture_type: "test fixture",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test fixture");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test fixture");
      });
    });

    describe("GAUGE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Gauge");
      });

      const params = {
        data: {
          gauge: 1,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(1);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(1);
      });
    });

    describe("WEIGHT", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Weight");
      });

      const params = {
        data: {
          weight: 20,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(20);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(20);
      });
    });

    describe("HANGER SIZE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Hanger Size");
      });

      const params = {
        data: {
          hanger_size: 100,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(100);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(100);
      });
    });

    describe("PRODUCT CODE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Product Code");
      });

      const params = {
        data: {
          product_code: "12345",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("12345");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("12345");
      });
    });

    describe("INSULATION", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Insulation");
      });

      const params = {
        data: {
          insulation: "test insulation",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test insulation");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test insulation");
      });
    });

    describe("INSULATION AREA", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Insulation Area"
        );
      });

      const params = {
        data: {
          insulation_area: 5,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(5);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(5);
      });
    });

    describe("INSULATION SPEC", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Insulation Spec"
        );
      });

      const params = {
        data: {
          insulation_specification: "test spec",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test spec");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test spec");
      });
    });

    describe("INSULATION GAUGE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Insulation Gauge"
        );
      });

      const params = {
        data: {
          insulation_gauge: "1",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("1");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("1");
      });
    });

    describe("JOINING PROCEDURE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Joining Procedure"
        );
      });

      const params = {
        data: {
          joining_procedure_name: "test jp",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test jp");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test jp");
      });
    });

    describe("MATERIAL NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Material Name");
      });

      const params = {
        data: {
          material_name: "test mat",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test mat");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test mat");
      });
    });

    describe("SERVICE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Service");
      });

      const params = {
        data: {
          service_name: "test service",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test service");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test service");
      });
    });

    describe("SERVICE COLOR", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Service Color");
      });

      const params = {
        data: {
          service_color_name: "red",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("red");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("red");
      });
    });

    describe("SIZE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Size");
      });

      const params = {
        data: {
          size: "10",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("10");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("10");
      });
      it("comparator", () => {
        const values = [`1/2"`, `1/2"x3/4"`, `54"x36"`];

        expect(values.sort(column.comparator)).toEqual([
          `1/2"`,
          `1/2"x3/4"`,
          `54"x36"`,
        ]);
      });
    });

    describe("AREA", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Area");
      });

      const params = {
        data: {
          area: "test area",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test area");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test area");
      });
    });

    describe("RANDOM LENGTH", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Random Length");
      });

      const params = {
        data: {
          random_length: 10,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(10);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(10);
      });
    });

    describe("ROD SIZE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Rod Size");
      });

      const params = {
        data: {
          rod_size: 10,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(10);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(10);
      });
    });

    describe("SUPPORT ROD LENGTH 1", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Support Rod Length 1"
        );
      });

      const params = {
        data: {
          support_rod_length: 20,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(20);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(20);
      });
    });

    describe("SUPPORT ROD LENGTH 2", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Support Rod Length 2"
        );
      });

      const params = {
        data: {
          support_rod_length_2: 20,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(20);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(20);
      });
    });

    describe("LAYDOWN LOCATION", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Laydown Location"
        );
      });

      const params = {
        data: {
          laydown_location_name: "test laydown",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test laydown");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test laydown");
      });
    });

    describe("LINER SPEC", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Liner Spec");
      });

      const params = {
        data: {
          liner_spec: "test liner",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test liner");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test liner");
      });
    });

    describe("HEAT NUMBER", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Heat Number");
      });

      const params = {
        data: {
          heat_number: "test hn",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test hn");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test hn");
      });
    });

    describe("CONTAINER", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Container");
      });

      const params = {
        data: {
          container_name: "test container",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test container");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test container");
      });
    });

    describe("FILLER METAL", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Filler Metal");
      });

      const params = {
        data: {
          filler_metal: "test metal",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test metal");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test metal");
      });
    });

    describe("VENDOR", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Vendor");
      });

      const params = {
        data: {
          vendor: "test vendor",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test vendor");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test vendor");
      });
    });

    describe("MEASUREMENT AREA", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Measurement Area"
        );
      });

      const params = {
        data: {
          measurement_area: 30,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(30);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(30);
      });
    });

    describe("JOINT HEAT NUMBER 1", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Joint Heat Number 1"
        );
      });

      const params = {
        data: {
          joint_heat_numbers:
            '[{"position": 1, "heat_number": "test hn 1"},{"position": 2, "heat_number": "test hn 2"}]',
        },
      };
      const badParams = {
        data: {
          joint_heat_numbers: "[]",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test hn 1");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test hn 1");
        expect(column.valueGetter(badParams)).toEqual("");
      });
    });

    describe("JOINT HEAT NUMBER 2", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Joint Heat Number 2"
        );
      });

      const params = {
        data: {
          joint_heat_numbers:
            '[{"position": 1, "heat_number": "test hn 1"},{"position": 2, "heat_number": "test hn 2"}]',
        },
      };
      const badParams = {
        data: {
          joint_heat_numbers: "[]",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test hn 2");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test hn 2");
        expect(column.valueGetter(badParams)).toEqual("");
      });
    });

    describe("DRAWING PRIORITY", () => {
      let column;

      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Drawing Priority"
        );
      });

      let arr = [
        {
          data: {
            drawing_id: 1,
            drawing_priority: 3,
          },
        },
        {
          data: {
            drawing_id: 2,
            drawing_priority: 2,
          },
        },
        {
          data: {
            drawing_id: 3,
            drawing_priority: 10,
          },
        },
      ];

      const params = {
        api: {
          forEachNode: (callback) => {
            for (let i = 0; i < arr.length; i++) {
              callback(arr[i]);
            }
          },
        },
        node: {
          gridApi: {
            forEachNode: (callback) => {
              for (let i = 0; i < arr.length; i++) {
                callback(arr[i]);
              }
            },
          },
        },
        data: {
          drawing_id: 1,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(2);
      });

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(2);
      });
    });
  });

  describe("Grouped Column Defs", () => {
    const defaultHeaders = [
      "Quantity",
      "Job Name",
      "Package Name",
      "Drawing Name",
      "Length",
      "Material Name",
      "Size",
      "Heat Number",
      "Joining Procedure",
    ];

    let populatedColumns;

    beforeEach(() => {
      populatedColumns = selectedStageViewColumnDefs(
        null,
        workStageColumns,
        workStageGroupableColumns,
        false,
        "Grouped",
        sortState
      );
    });

    it("Headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);

      expect(columnHeaders).toEqual(defaultHeaders);
    });
  });

  describe("Grouped By Drawing Column Defs", () => {
    const defaultHeaders = ["Drawing Name", "Quantity"];

    let populatedColumns;

    beforeEach(() => {
      populatedColumns = selectedStageViewColumnDefs(
        null,
        workStageColumns,
        workStageGroupableColumns,
        false,
        "Grouped By Drawing",
        sortState
      );
    });

    it("Headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);

      expect(columnHeaders).toEqual(defaultHeaders);
    });
  });
});
