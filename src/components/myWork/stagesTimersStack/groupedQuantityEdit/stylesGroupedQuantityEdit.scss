@import "../../../styles/colors.scss";

div.quantity-selector-popup {
  position: fixed;
  height: 140px;
  width: 220px;
  top: 40%;
  right: 5%;
  transform: translate(-25%, 8%);
  background-color: black;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 5px 10px;
  color: white;

  & h4 {
    color: white !important;
    padding-top: 15px !important;
  }

  & p {
    color: white;
    font-size: 0.7rem;
    text-align: center;
    margin-top: 10px;
    padding: 0;
    margin-bottom: -5px;
  }

  & input[type="number"] {
    background-color: #263137;
    color: white;
    border: none;
    height: 32px;
    padding-left: 5px;
    width: 100%;
    box-sizing: border-box;

    &:focus {
      border: none;
      outline: none;
    }
  }

  & div.button-row {
    // margin-top: 35px;
    display: flex;
    justify-content: space-evenly;

    & button {
      border: none;
      background-color: transparent;
      color: $blue;
      font-size: 1rem;
      cursor: pointer;
      margin-bottom: 5px;

      &:disabled {
        color: $textGrey;
      }
    }
    & button.cancel {
      color: $red;
    }
  }
}
