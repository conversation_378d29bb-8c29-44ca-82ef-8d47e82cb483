// NPM PACKAGE IMPORTS
import React, { useState, useRef, useMemo, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import { handleRemoveItemsFromTimer } from "../../../timers/timersActions";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../../hooks/useOutsideClick";

// STYLES IMPORTS
import "./stylesGroupedQuantityEdit.scss";

const GroupedQuantityEdit = ({
  selectedStage,
  toggleModal,
  selectedRow,
  refreshTable,
  notify,
  cancelEditing,
  setSelectedEditRows,
}) => {
  const [editQuantity, setEditQuantity] = useState("");
  const [validQuantity, toggleValidQuantity] = useState(false);

  const { activeTimer } = useSelector((state) => state.timerData);

  const dispatch = useDispatch();

  const currentQuantity = useMemo(() => {
    return selectedRow.quantity.toFixed(1);
  }, []);

  const isLength = useMemo(() => {
    return selectedRow.is_cut || false;
  }, []);

  const wrapperRef = useRef(null);

  useOutsideClick(wrapperRef, () => toggleModal(false));

  const handleCancel = () => {
    toggleModal(false);
    setEditQuantity("");
  };

  const handleQuantityUpdate = () => {
    toggleModal(false);
    dispatch(
      handleRemoveItemsFromTimer(
        activeTimer[0].timer_id,
        selectedRow.id,
        isLength ? parseFloat(editQuantity) : parseInt(editQuantity)
      )
    ).then((res) => {
      if (res.timer && !res.timer[0].work_item_id) {
        notify("ERROR", "No items fit updated quantity.");
      }
      cancelEditing();
      setSelectedEditRows([]);
      refreshTable();
    });
  };

  useEffect(() => {
    if (!editQuantity || editQuantity === "") toggleValidQuantity(false);
    else if (editQuantity >= parseFloat(currentQuantity) || editQuantity <= 0)
      toggleValidQuantity(false);
    else toggleValidQuantity(true);
  }, [editQuantity]);

  return (
    <div ref={wrapperRef} className="quantity-selector-popup">
      <h4>{`${selectedStage.name}: Edit Quantity`}</h4>
      <p>{`Current quantity: ${currentQuantity || 0} ${
        isLength ? "inches" : "items"
      }`}</p>
      <input
        type="number"
        onChange={(e) => setEditQuantity(e.target.value)}
        value={editQuantity}
      />
      <div className="button-row">
        <button onClick={handleCancel} className="cancel">
          Cancel
        </button>
        <button onClick={handleQuantityUpdate} disabled={!validQuantity}>
          Save
        </button>
      </div>
    </div>
  );
};

export default GroupedQuantityEdit;
