// NPM PACKAGE IMPORTS
import moment from "moment";
import "moment-timezone";

// REDUX IMPORTS
import store from "../../../redux/store";
import { notify } from "../../reusable/alertPopup/alertPopupActions";

// CONSTANTS
import {
  objectColumns,
  transformDrawingPriorityToRank,
  generateTime,
  naturalSort,
  convertFracToDec,
  fractionalRegex,
  objectColumnDefs,
} from "../../../_utils";

const buildGroupableColumns = (
  workStageColumns,
  sortState,
  groupingType,
  isEditing
) => {
  if (!workStageColumns?.length) return [];

  let builtColumns = [];

  workStageColumns.forEach((ws) => {
    let columnDef = {
      headerName: ws.display_name,
      valueGetter: (params) =>
        objectColumns.includes(ws.normal_name)
          ? params.data?.[ws.normal_name].display
          : params.data?.[ws.normal_name],
      cellClass: "wrap-text",
      minWidth: 120,
      getQuickFilterText: (params) =>
        objectColumns.includes(ws.normal_name)
          ? params.data?.[ws.normal_name].display
          : params.data?.[ws.normal_name],
      menuTabs: ["filterMenuTab"],
      filterParams: {
        buttons: ["reset"],
        suppressAndOrCondition: true,
      },
      colId: ws.normal_name,
      autoHeight: true,
      sort:
        sortState.sorting_column_name === ws.normal_name
          ? sortState.sorting_method
          : null,
    };

    if (ws.data_type === "integer" || ws.data_type === "date")
      columnDef.filter = "agNumberColumnFilter";
    else if (ws.data_type === "string") columnDef.filter = "agTextColumnFilter";
    else if (ws.data_type === "date") {
      columnDef.filter = "agDateColumnFilter";
      columnDef.filterParams = {
        buttons: ["reset"],
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = cellValue
            ? typeof cellValue === "number"
              ? new Date(generateTime(cellValue * 1000, false, true, "-"))
              : new Date(cellValue)
            : "-";

          return cellDate < filterLocalDateAtMidnight
            ? -1
            : cellDate > filterLocalDateAtMidnight
            ? 1
            : 0;
        },
      };
    }
    if (objectColumns.includes(ws.normal_name)) {
      columnDef.comparator = objectColumnDefs.comparator(ws.normal_name);
    }
    builtColumns.push(columnDef);
  });

  if (groupingType === "Advanced Grouping") {
    // TODO - add individual length columnDef
    builtColumns.unshift({
      headerName: "Individual Length",
      field: "individual_length",
      valueGetter: (params) => params.data?.individual_length?.display,
      cellClass: "wrap-text",
      minWidth: 150,
      getQuickFilterText: (params) => params.data?.individual_length?.display,
      menuTabs: ["filterMenuTab"],
      filterParams: {
        buttons: ["reset"],
      },
      filter: "agTextColumnFilter",
      colId: "individual_length",
      sort:
        sortState.sorting_column_name === "individual_length"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB, nodeA, nodeB) => {
        return nodeA.data?.individual_length?.decimal >
          nodeB.data?.individual_length?.decimal
          ? 1
          : -1;
      },
    });
  }

  builtColumns.unshift({
    headerName: "Quantity",
    field: "quantity",
    valueFormatter: (params) => {
      return params.value || "-";
    },
    width: 100,
    minWidth: 80,
    menuTabs: ["filterMenuTab"],
    filterParams: {
      buttons: ["reset"],
    },
    filter: "agNumberColumnFilter",
    colId: "quantity",
    pinned: "left",
  });

  if (isEditing) {
    builtColumns.unshift({
      headerName: "",
      headerCheckboxSelection: false,
      width: 40,
      minWidth: 40,
      checkboxSelection: true,
      suppressMenu: true,
      pinned: "left",
      suppressColumnsToolPanel: true,
      colId: "checkbox",
    });
  }

  return builtColumns;
};

export const selectedStageViewColumnDefs = (
  savedColumnState,
  workStageColumns,
  workStageGroupableColumns,
  isEditing,
  groupingType,
  sortState
) => {
  const { systemSettings } = store.getState().profileData;
  let dateFormatting = systemSettings && systemSettings.date_display;

  let columns = [];

  if (workStageColumns && workStageColumns.length) {
    if (groupingType === "Ungrouped") {
      for (let i = 0; i < workStageColumns.length; i++) {
        let col = workStageColumns[i];

        if (
          col.display_name.includes("Joint Heat Number") ||
          col.display_name === "Drawing Priority"
        )
          continue;

        let columnDef = {
          autoHeight: true,
          suppressSizeToFit: true,
          headerName: col.display_name,
          valueGetter: (params) =>
            // updated because if value is missing in dataset then the display is not available
            params.data?.[col.normal_name] &&
            objectColumns.includes(col.normal_name)
              ? params.data?.[col.normal_name]?.display
              : params.data?.[col.normal_name],
          cellClass: "wrap-text",
          minWidth: 150,
          getQuickFilterText: (params) =>
            // updated because if value is missing in dataset then the display is not available
            params.data?.[col.normal_name] &&
            objectColumns.includes(col.normal_name)
              ? params.data[col.normal_name].display
              : params.data[col.normal_name],
          menuTabs: ["filterMenuTab"],
          filterParams: {
            buttons: ["reset"],
            suppressAndOrCondition: true,
          },
          filter: "agTextColumnFilter",
          colId: col.normal_name,
          sortable: col.is_custom ? false : true,
          sort:
            sortState.sorting_column_name === col.normal_name
              ? sortState.sorting_method
              : null,
        };

        if (col.normal_name === "length") {
          columnDef.valueParser = (params) => {
            if (params.newValue === "" || params.newValue === undefined)
              return params.oldValue === null
                ? params.oldValue && params.oldValue.decimal
                : params.oldValue.decimal === null
                ? { decimal: null, display: null }
                : { decimal: 0, display: "" };
            if (params.oldValue === convertFracToDec(params.newValue))
              return params.oldValue;

            if (!fractionalRegex.test(params.newValue)) {
              store.dispatch(
                notify({
                  id: Date.now(),
                  type: "ERROR",
                  message: `Value not saved due to invalid format. Please provide a valid fraction (ex. 2'-1/2")`,
                })
              );
              return params.oldValue;
            }

            return {
              decimal: convertFracToDec(params.newValue),
              display: params.newValue,
            };
          };
        }

        if (col.data_type === "integer") {
          columnDef.filter = "agNumberColumnFilter";
        }

        if (col.data_type === "string") {
          columnDef.filter = "agTextColumnFilter";
          columnDef.comparator = (valueA, valueB) => {
            return valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1;
          };
        }

        if (objectColumns.includes(col.normal_name)) {
          columnDef.comparator = objectColumnDefs.comparator(col.normal_name);
        }

        if (col.normal_name === "size") {
          columnDef.comparator = (valueA, valueB) => {
            if (!valueA) return -1;
            if (!valueB) return 1;
            return valueA.localeCompare(valueB, undefined, {
              numeric: true,
              sensitivity: "base",
            });
          };
        }

        if (col.data_type === "date") {
          columnDef.filter = "agDateColumnFilter";
          columnDef.filterParams = {
            buttons: ["reset"],
            comparator: (filterLocalDateAtMidnight, cellValue) => {
              const cellDate = cellValue
                ? typeof cellValue === "number"
                  ? new Date(generateTime(cellValue * 1000, false, true, "-"))
                  : new Date(cellValue)
                : "-";

              return cellDate < filterLocalDateAtMidnight
                ? -1
                : cellDate > filterLocalDateAtMidnight
                ? 1
                : 0;
            },
          };
          columnDef.getQuickFilterText = (params) => {
            let value;
            const date = params.data?.[col.normal_name]
              ? new Date(params.data[col.normal_name])
              : null;

            const timezone = Intl.DateTimeFormat().resolvedOptions().timezone;
            if (!date) value = "";
            else value = moment.tz(date, timezone).format(dateFormatting);
            return value;
          };
          columnDef.valueFormatter = (params) => {
            let value;
            const date = params.data?.[col.normal_name]
              ? new Date(params.data[col.normal_name])
              : null;

            const timezone = Intl.DateTimeFormat().resolvedOptions().timezone;
            if (!date) value = "";
            else value = moment.tz(date, timezone).format(dateFormatting);

            return value;
          };
        }

        if (!col.comparator && col.filter === "agTextColumnFilter") {
          col.comparator = (valueA, valueB) =>
            valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1;
        }

        // add in text wrap cell style
        if (col.normal_name === "product_code") {
          columnDef.cellClass = "custom-wrap";
        }

        if (col.is_custom) {
          columnDef.filter = false;
          columnDef.menuTabs = [];
        }

        columns.push(columnDef);
      }
      if (
        workStageColumns.find((col) => col.display_name === "Joint Heat Number")
      ) {
        for (let i = 0; i < 2; i++) {
          const columnDef = {
            suppressSizeToFit: true,
            headerName: `Joint Heat Number ${i + 1}`,
            field: "heat_number",
            cellClass: "wrap-text",
            minWidth: 150,
            valueGetter: (params) => {
              if (!params.data) return "";
              const { joint_heat_numbers } = params.data;
              let value;
              if (!joint_heat_numbers || !JSON.parse(joint_heat_numbers).length)
                value = "";
              else {
                JSON.parse(joint_heat_numbers).forEach((hn) => {
                  if (hn.position === i + 1) value = hn.heat_number;
                });
              }
              return value;
            },
            getQuickFilterText: (params) => {
              if (!params.data) return;
              const { joint_heat_numbers } = params.data;
              if (!JSON.parse(joint_heat_numbers).length) return;
              if (
                JSON.parse(joint_heat_numbers)[i] &&
                JSON.parse(joint_heat_numbers)[i].heat_number
              )
                return JSON.parse(joint_heat_numbers)[i].heat_number;
            },
            menuTabs: ["filterMenuTab"],
            filter: "agTextColumnFilter",
            filterParams: {
              buttons: ["reset"],
              suppressAndOrCondition: true,
            },
            colId: `joint_heat_number_${i + 1}`,
            sort:
              sortState.sorting_column_name === `joint_heat_number_${i + 1}`
                ? sortState.sorting_method
                : null,
          };

          columns.push(columnDef);
        }
      }
      if (
        workStageColumns
          .map((col) => col.display_name)
          .includes("Drawing Priority")
      ) {
        const columnDef = {
          autoHeight: true,
          suppressSizeToFit: true,
          headerName: "Drawing Priority",
          field: "drawing_priority",
          cellClass: "wrap-text",
          getQuickFilterText: (params) => {
            let allValues = [];
            let value;
            params.node.gridApi.forEachNode((rowNode) => {
              allValues.push(rowNode.data);
            });
            if (params.api) {
              const ranks = transformDrawingPriorityToRank(allValues);
              for (let i = 0; i <= ranks.length; i++) {
                if (ranks[i] && ranks[i].id === params.data?.drawing_id) {
                  value = ranks[i].drawing_priority;
                  break;
                }
              }
            }
            return value;
          },
          minWidth: 150,
          // TODO - add filtering back once this is fixed
          filter: false,
          menuTabs: [],
          // filter: "agNumberColumnFilter",
          // filterParams: {
          //   buttons: ["reset"],
          // },
          // menuTabs: ["filterMenuTab"],
          colId: "drawing_priority",
          sortable: true,
          sort:
            sortState.sorting_column_name === "drawing_priority"
              ? sortState.sorting_method
              : null,
        };
        columns.push(columnDef);
      }

      if (savedColumnState && savedColumnState.length) {
        let result = [];

        columns.forEach((c) => {
          let savedDef = savedColumnState.find(
            (sc) => sc.header_name === c.headerName
          );

          if (savedDef) {
            result.push({
              ...c,
              pinned: savedDef.pinned,
              hide: savedDef.visible === 1 ? false : true,
              position: savedDef.position,
            });
          } else result.push(c);
        });

        result = result
          .sort((a, b) => {
            if (a.position === b.position) {
              if (a.headerName.toLowerCase() > b.headerName.toLowerCase())
                return 1;
              else return -1;
            } else return a.position - b.position;
          })
          .map((col) => {
            if (col.position !== undefined) delete col.position;
            return col;
          });

        if (isEditing) {
          result.unshift({
            headerName: "",
            headerCheckboxSelection: false,
            width: 40,
            minWidth: 40,
            checkboxSelection: true,
            suppressMenu: true,
            pinned: "left",
            suppressColumnsToolPanel: true,
          });
        }

        return result;
      } else {
        let result = [];

        columns.forEach((c) => {
          let defaultPosition = workStageColumns.find(
            (sc) =>
              sc.display_name === c.headerName ||
              (sc.display_name === "Joint Heat Number" &&
                c.headerName.includes("Joint Heat Number"))
          );

          if (defaultPosition) {
            result.push({
              ...c,
              position: defaultPosition.position,
            });
          } else result.push(c);
        });

        result = result
          .sort((a, b) => {
            if (a.position === b.position) {
              if (a.headerName.toLowerCase() > b.headerName.toLowerCase())
                return 1;
              else return -1;
            } else return a.position - b.position;
          })
          .map((col) => {
            if (col.position !== undefined) delete col.position;
            return col;
          });

        if (isEditing) {
          result.unshift({
            headerName: "",
            headerCheckboxSelection: false,
            width: 40,
            minWidth: 40,
            checkboxSelection: true,
            suppressMenu: true,
            pinned: "left",
            suppressColumnsToolPanel: true,
          });
        }

        return result;
      }
    } else if (groupingType === "Grouped") {
      if (workStageGroupableColumns && workStageGroupableColumns.length) {
        // TODO use buildGroupableColumns here
        workStageGroupableColumns.forEach((ws) => {
          let columnDef = {
            headerName: ws.display_name,
            valueGetter: (params) =>
              params.data
                ? params?.data[ws.normal_name] &&
                  objectColumns.includes(ws.normal_name)
                  ? params.data[ws.normal_name].display
                  : params.data[ws.normal_name]
                : null,
            // updated because if value is missing in dataset then the display is not available
            cellClass: "wrap-text",
            minWidth: 120,
            getQuickFilterText: (params) =>
              // updated because if value is missing in dataset then the display is not available
              params.data[ws.normal_name] &&
              objectColumns.includes(ws.normal_name)
                ? params.data[ws.normal_name].display
                : params.data[ws.normal_name],
            menuTabs: ["filterMenuTab"],
            filterParams: {
              buttons: ["reset"],
            },
            colId: ws.normal_name,
            autoHeight: true,
            sort:
              sortState.sorting_column_name === ws.normal_name
                ? sortState.sorting_method
                : null,
          };

          if (ws.data_type === "integer" || ws.data_type === "date")
            columnDef.filter = "agNumberColumnFilter";
          else if (ws.data_type === "string")
            columnDef.filter = "agTextColumnFilter";
          else if (ws.data_type === "date") {
            columnDef.filter = "agDateColumnFilter";
            columnDef.filterParams = {
              buttons: ["reset"],
              comparator: (filterLocalDateAtMidnight, cellValue) => {
                const cellDate = cellValue
                  ? typeof cellValue === "number"
                    ? new Date(generateTime(cellValue * 1000, false, true, "-"))
                    : new Date(cellValue)
                  : "-";

                return cellDate < filterLocalDateAtMidnight
                  ? -1
                  : cellDate > filterLocalDateAtMidnight
                  ? 1
                  : 0;
              },
            };
          }
          if (objectColumns.includes(ws.normal_name)) {
            columnDef.comparator = objectColumnDefs.comparator(ws.normal_name);
          }
          columns.push(columnDef);
        });
        columns.unshift({
          headerName: "Quantity",
          field: "quantity",
          valueFormatter: (params) => {
            return params.value || "-";
          },
          width: 100,
          minWidth: 80,
          menuTabs: ["filterMenuTab"],
          filterParams: {
            buttons: ["reset"],
          },
          filter: "agNumberColumnFilter",
          colId: "quantity",
          pinned: "left",
        });

        if (isEditing) {
          columns.unshift({
            headerName: "",
            headerCheckboxSelection: false,
            width: 40,
            minWidth: 40,
            checkboxSelection: true,
            suppressMenu: true,
            pinned: "left",
            suppressColumnsToolPanel: true,
            colId: "checkbox",
          });
        }
      }
      return columns;
    } else if (groupingType === "Advanced Grouping") {
      // build groupable columns and filter out Tag Number column from results if Advanced Grouping
      const result = buildGroupableColumns(
        workStageGroupableColumns,
        sortState,
        groupingType,
        isEditing
      );

      return result;
    }
  }
  if (groupingType === "Grouped By Drawing") {
    columns = [
      {
        headerName: "Drawing Name",
        field: "drawing_name",
        getQuickFilterText: (params) => params.data.drawing_name,
        minWidth: 120,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "drawing_name",
        autoHeight: true,
        sort:
          sortState.sorting_column_name === "drawing_name"
            ? sortState.sorting_method
            : null,
        comparator: (valueA, valueB) =>
          valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
      },
      {
        headerName: "Quantity",
        field: "quantity",
        valueFormatter: (params) => {
          return params.value || "-";
        },
        width: 100,
        minWidth: 80,
        autoHeight: true,
        menuTabs: ["filterMenuTab"],
        filterParams: {
          buttons: ["reset"],
        },
        filter: "agNumberColumnFilter",
        colId: "quantity",
        sort:
          sortState.sorting_column_name === "quantity"
            ? sortState.sorting_method
            : null,
      },
    ];
  }

  return columns;
};
