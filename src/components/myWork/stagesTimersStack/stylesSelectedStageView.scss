@import "../../styles/colors.scss";
@import "../../styles/sizes.scss";

div.floating-button {
  position: absolute;
  z-index: 50;

  & button {
    position: fixed;
    right: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e51f5f;
    height: 40px;
    width: 40px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    -webkit-box-shadow: 5px 5px 15px 5px rgba(0, 0, 0, 0.5);
    box-shadow: 5px 5px 15px 5px rgba(0, 0, 0, 0.5);

    & svg {
      color: white;
    }
  }
}

div.complete-timer-button {
  bottom: 90px;
  & svg {
    font-size: 1.5rem;
  }

  & button {
    &:focus {
      border: none;
      outline: none;
    }
  }
}
div.pause-timer-button {
  bottom: 150px;

  & button {
    width: 35px;
    height: 35px;
    right: 63px;

    &:focus {
      border: none;
      outline: none;
    }
  }
  & svg {
    font-size: 1.2rem;
  }
}

div.selected-stage-view {
  & > div.loader-wrapper {
    height: calc(100vh - 210px);
  }
}

div.heading-wrapper {
  display: grid;
  grid-template-rows: repeat(2, 30px);
  grid-template-columns: repeat(2, 20px) 1fr 40px;
  align-items: center;
  color: #fff;

  & > svg,
  & > span {
    font-size: 0.8rem;
    cursor: pointer;
  }

  & > svg {
    justify-self: end;
  }

  & > h2 {
    margin: 0;
    padding: 0;
    text-align: center;
    font-size: 0.9rem;
    font-weight: 400;
  }

  & > div.inline-tabs {
    grid-row: 2/-1;
    grid-column: 1/-1;
  }
}

div.grouping-wrapper {
  width: 120px;
  height: 30px;

  padding: 5px 5px 5px 16px;
}

div.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;

  padding: 5px 10px;
  height: 30px;

  & .selected-edit {
    color: #fac439;
  }

  & p.table-count {
    color: $textGrey;
    font-size: 0.7rem;
    width: 85px;
    margin-right: 5px;
  }

  & > input {
    height: 20px;
    font-size: 0.7rem;
    background-color: transparent;
    color: #fff;
    border: none;
    padding: 0;
    width: 180px;

    &::placeholder {
      color: #ddd;
    }

    &:focus {
      outline: none;
    }
  }

  & > div.tool-wrapper {
    color: $fabProBlue;

    display: flex;
    column-gap: 20px;
    align-items: center;
    margin-right: 30px;

    & button.columns-btn {
      background-color: transparent;
      outline: none;
      border: none;
      color: $blue;
      cursor: pointer;

      &:focus {
        border: none;
        outline: none;
      }

      &:disabled {
        color: darken($blue, 20%);
      }
    }

    & .search {
      font-size: 1.1rem;
    }
    & .edit,
    .columns {
      font-size: 1rem;
    }
    & .rejection {
      font-size: 1.1rem;
    }

    & > svg {
      cursor: pointer;
      margin: 0 8px;

      &:not(.selected-edit):hover {
        color: lighten($fabProBlue, 10%);
      }
    }

    & > svg.edit-disabled {
      color: darken($fabProBlue, 20%);
      cursor: default;
      &:hover {
        color: darken($fabProBlue, 20%);
      }
    }

    & .selected {
      color: $rowYellowOutline !important;
    }

    & div#refresh-items-table-container {
      display: flex;
      column-gap: 0px;

      & svg.refresh {
        cursor: pointer;
        font-size: 1.7rem;
        margin-top: -0.2rem;
        &:hover {
          color: lighten($fabProBlue, 10%);
        }
      }

      // used to highlight the icon-highlight button (only refresh currently)
      & svg.icon-highlight {
        color: $yellow;
        animation: 1s alternate-reverse infinite bounce;
        z-index: 99;

        &:hover {
          color: lighten($fabProBlue, 10%);
        }
      }

      // used to animate the icon-highlight class (only refresh currently)
      @keyframes bounce {
        from {
          transform: translateY(-6px);
        }
        to {
          transform: translateY(0);
        }
      }

      // used to hover over the highlighted button (only refresh currently)
      & svg.notification-icon {
        position: relative;
        top: -5px;
        left: 0.7rem;
        color: $red;
        border: 1px solid #000;
        background-color: #333;
        border-radius: 50%;
        margin-left: calc(-1em - 2px);
      }
    }
  }
}

div.edit-type-picker {
  height: 110px;
  width: 170px;
  position: fixed;
  top: 25%;
  right: 8%;
  background-color: white;
  border-radius: 3px;

  & p {
    font-size: 0.8rem;
    padding: 8px 5px;
    margin: 0;
  }

  & .button-wrapper {
    display: flex;
    flex: 1;
    flex-direction: column;
    margin-top: 5px;
    align-items: stretch;

    & button {
      font-size: 0.9rem;
      height: 28px;
      outline: none;
      border: none;
      color: $blue;
      background-color: white;
      border-top: 1px solid #eee;
      cursor: pointer;
      // font-weight: bold;

      &:last-of-type {
        border-bottom-left-radius: 3px;
        border-bottom-right-radius: 3px;
      }

      &:hover {
        background-color: #eee;
      }
    }
  }
}

div.table-wrapper {
  height: calc(100vh - 210px);

  @supports (-webkit-touch-callout: none) {
    height: calc(100vh - 200px - #{$iosAddressBar});
  }
  flex-grow: 1;

  & > p {
    text-align: center;
    color: #fff;
    font-size: 0.8rem;
  }

  // AG-GRID TABLE STYLES

  & .custom-ag-styles.ag-theme-balham-dark {
    height: 100%;
    width: 100%;

    & .ag-side-buttons {
      display: none;
    }
    & .ag-tool-panel-wrapper {
      background-color: transparent;
      width: 0;
    }
    & .ag-column-select-list .ag-focus-managed {
      background-color: $bgDark;
    }
    & .ag-column-panel-column-select {
      position: fixed;
      right: 20px;
      max-height: 320px;
      border: 1px solid $blue;
      background-color: $bgDark;
      font-size: 0.9rem;
      z-index: 1;
    }

    .ag-center-cols-container .ag-center-cols-viewport {
      background-color: $bgDark;
    }
    // FOOTER
    .ag-paging-panel {
      background-color: $bgDark;
    }

    & div.ag-header {
      background-color: #20232a;
      border-top: none;
      border-right: none;

      & .ag-header-row {
        & .ag-header-cell-text {
          color: $textLight;
          font-size: 0.65rem;
          font-weight: normal;
        }
      }

      // hides column separator
      & .ag-header-cell::after {
        display: none;
      }
    }
    & .ag-body-viewport {
      overflow-y: scroll !important;
      background-color: $bgDark;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .ag-cell {
      white-space: normal;
      min-height: 60px !important;
    }

    div.ag-row.--custom-grid-even {
      background-color: $rowEven !important;
    }
    div.ag-row.--custom-grid-odd {
      background-color: $bgDark !important;
    }
    div.ag-row.--custom-selected-drawing {
      border: 1px solid $rowYellowOutline !important;
    }
    div.ag-row-selected {
      background-color: transparent !important;
    }
    div.ag-row.--custom-rejected-row-even {
      background-color: $redWashDark !important;
    }
    div.ag-row.--custom-rejected-row-odd {
      background-color: $redWash !important;
    }
    div.ag-row.--custom-active-timer-item {
      background-color: $blue !important;
    }
    div.ag-row.--custom-completed-row {
      background-color: $textGrey !important;
    }
    div.ag-row.--custom-row-to-edit {
      background-color: $blue !important;
    }
  }
  &
    .ag-theme-balham-dark
    .ag-cell.ag-cell-last-left-pinned:not(.ag-cell-range-right):not(.ag-cell-range-single-cell) {
    border: none;
  }
}

// make height of table smaller if it is nestable
// stage has nesting but not grouping
.nesting-wrapper ~ div.table-wrapper {
  height: calc(100vh - 260px);
  @supports (-webkit-touch-callout: none) {
    height: calc(100vh - 260px - #{$iosAddressBar});
  }
}
// stage has nesting expanded but not grouping
.nesting-wrapper.expanded ~ div.table-wrapper {
  height: calc(100vh - 502px);
  @supports (-webkit-touch-callout: none) {
    height: calc(100vh - 502px - #{$iosAddressBar});
  }
}

// stage has nesting expanded and grouping
.nesting-wrapper.expanded ~ .grouping-wrapper ~ div.table-wrapper {
  height: calc(100vh - 542px);
  @supports (-webkit-touch-callout: none) {
    height: calc(100vh - 542px - #{$iosAddressBar});
  }
}
// // stage has grouping but no nesting
.grouping-wrapper ~ :not(.nesting-wrapper) ~ div.table-wrapper {
  @supports (-webkit-touch-callout: none) {
    height: calc(100vh - 250px - #{$iosAddressBar});
  }
}

// stage has nesting and grouping
.nesting-wrapper ~ .grouping-wrapper ~ div.table-wrapper {
  height: calc(100vh - 300px);
  @supports (-webkit-touch-callout: none) {
    height: calc(100vh - 300px - #{$iosAddressBar});
  }
}

// stage has advanced grouping selected in grouping dropdown with nesting
.nesting-wrapper ~ .advanced-grouping-filter ~ div.table-wrapper {
  height: calc(100vh - 345px);
  @supports (-webkit-touch-callout: none) {
    height: calc(100vh - 345px - #{$iosAddressBar});
  }
}
// stage has advanced grouping selected in grouping dropdown without nesting
// CANT GET THIS TO WORK - when nesting is displayed this style still gets applied
// :not(.nesting-wrapper) ~ .advanced-grouping-filter ~ div.table-wrapper {
//   height: calc(100vh - 315px);
// }

// stage has advanced grouping selected and has edit view displayed
.advanced-grouping-filter ~ .edit-view ~ div.table-wrapper {
  height: calc(100vh - 525px);
  @supports (-webkit-touch-callout: none) {
    height: calc(100vh - 525px - #{$iosAddressBar});
  }
}

// adjust height if in "edit" mode
.edit-view ~ div.table-wrapper {
  height: calc(100vh - 440px);
  @supports (-webkit-touch-callout: none) {
    height: calc(100vh - 440px - #{$iosAddressBar});
  }
}

// editing, NOT nestable, NOT groupable
.edit-view
  ~ :not(.nesting-wrapper)
  ~ :not(.grouping-wrapper)
  ~ div.table-wrapper {
  height: calc(100vh - 480px);
  @supports (-webkit-touch-callout: none) {
    height: calc(100vh - 480px - #{$iosAddressBar});
  }
}

// editing, NOT nestable, groupable
.edit-view ~ :not(.nesting-wrapper) ~ .grouping-wrapper ~ div.table-wrapper {
  height: calc(100vh - 440px);
  @supports (-webkit-touch-callout: none) {
    height: calc(100vh - 440px - #{$iosAddressBar});
  }
}

.grouping-wrapper ~ .edit-view ~ div.table-wrapper {
  height: calc(100vh - 480px);
  @supports (-webkit-touch-callout: none) {
    height: calc(100vh - 480px - #{$iosAddressBar});
  }
}

.grouping-wrapper ~ div.table-wrapper {
  height: calc(100vh - 250px);
  @supports (-webkit-touch-callout: none) {
    height: calc(100vh - 250px - #{$iosAddressBar});
  }
}

.grouping-wrapper ~ .advanced-grouping-filter ~ div.table-wrapper {
  height: calc(100vh - 297px);
  @supports (-webkit-touch-callout: none) {
    height: calc(100vh - 297px - #{$iosAddressBar});
  }
}

.nesting-wrapper
  ~ .grouping-wrapper
  ~ .advanced-grouping-filter
  ~ div.table-wrapper {
  height: calc(100vh - 347px);
  @supports (-webkit-touch-callout: none) {
    height: calc(100vh - 347px - #{$iosAddressBar});
  }
}
