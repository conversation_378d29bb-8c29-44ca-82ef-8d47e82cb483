@import "../styles/colors.scss";

// div.my-work-container {
//   height: calc(100vh - 100px);
//   width: calc(100vw - 55px);
// }

div.active-timer-button {
  position: relative;
  bottom: 0;
  z-index: 50;

  & button,
  a {
    position: fixed;
    height: 22px;
    width: 120px;
    bottom: 180px;
    right: -49px;
    background-color: #227211;
    transform: rotate(270deg);
    border: none;
    color: white;
    font-weight: bold;
    letter-spacing: 1.2px;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    cursor: pointer;

    &:focus {
      border: none;
      outline: none;
    }
  }

  & a {
    font-size: 0.9rem;
    text-decoration: none;
    text-align: center;
    padding-top: 3px;
  }
}

button.column-warn {
  & svg {
    color: $red;
  }
}
