export const notificationsColumnDefs = [
  {
    headerName: "Name",
    field: "display_name",
    getQuickFilterText: (params) => params.data.display_name,
    filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["reset"],
    },
    menuTabs: ["filterMenuTab"],
    autoHeight: true,
    colId: "display_name",
  },
  {
    headerName: "Description",
    field: "description",
    getQuickFilterText: (params) => params.data.description,
    filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["reset"],
    },
    width: 400,
    menuTabs: ["filterMenuTab"],
    autoHeight: true,
    colId: "description",
  },
  {
    headerName: "Email",
    field: "email_subscribed",
    valueGetter: (params) => (params.data.email_subscribed ? true : false),
    cellRenderer: "emailCellRenderer",
    cellRendererParams: (params) => {
      return {
        rowHeight: params.node.rowHeight,
        even: params.node.childIndex % 2 === 0,
        isDisabled: params.data.email === 0 ? true : false,
        value: params.value ? true : false,
        setValue: params.setValue,
      };
    },
    getQuickFilterText: (params) => params.data.email_subscribed,
    autoHeight: true,
    colId: "email",
  },
];
