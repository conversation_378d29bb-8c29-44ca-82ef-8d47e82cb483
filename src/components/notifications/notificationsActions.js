import { fetchNotifications } from "../../_services";

export const receiveStarted = (type) => ({
  type: `RECEIVE_${type}_STARTED`,
});
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const handleFetchNotifications = (dispatch) => {
  const type = "NOTIFICATIONS";
  dispatch(receiveStarted(type));

  return fetchNotifications().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};
