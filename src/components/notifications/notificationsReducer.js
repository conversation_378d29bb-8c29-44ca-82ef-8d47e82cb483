const initialState = {
  notifications: null,
  isLoading: false,
  error: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_NOTIFICATIONS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_NOTIFICATIONS_SUCCEEDED":
      return { ...state, isLoading: false, notifications: payload };
    case "RECEIVE_NOTIFICATIONS_FAILED":
      return { ...state, isLoading: false, error: payload };
    default:
      return state;
  }
}
