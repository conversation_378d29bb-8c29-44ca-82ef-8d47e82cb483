// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import {
  handleFetchUserNotifications,
  handleUpdateNotificationSubscriptions,
} from "../profile/profileActions";

// COMPONENT IMPORTS
import AgTable from "../reusable/agTable/AgTable";
import TableContainer from "../reusable/tableContainer/TableContainer";
import ToggleCellEditor from "../reusable/frameworkComponents/ToggleCellEditor";

// CONSTANT IMPORTS
import { notificationsColumnDefs } from "./notificationsConstants";

// STYLES IMPORTS
import "./stylesNotifications.scss";

const Notifications = () => {
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [selectedTab, setSelectedTab] = useState("STATUS");
  const [searchInput, setSearchInput] = useState("");

  const dispatch = useDispatch();

  const { userNotifications: notifications } = useSelector(
    (state) => state.profileData
  );

  useEffect(() => {
    dispatch(handleFetchUserNotifications);
  }, []);

  const displayedNotifications = useMemo(() => {
    if (!notifications?.length) return [];

    return notifications.filter(
      (n) => n.group_name === selectedTab.toLowerCase()
    );
  }, [notifications, selectedTab]);

  useEffect(() => {
    if (!displayedNotifications || !gridOptionsApi) return;

    gridOptionsApi.setRowData(displayedNotifications);
  }, [displayedNotifications, gridOptionsApi]);

  useEffect(() => {
    if (!gridOptionsApi) return;

    gridOptionsApi.setQuickFilter(searchInput);
    gridOptionsApi.redrawRows();
  }, [searchInput, gridOptionsApi]);

  const handleTabSelection = (tabName) => {
    setSelectedTab(tabName.toUpperCase());
  };

  const onCellValueChanged = (params) => {
    if (
      (!params.newValue && !params.oldValue) ||
      params.newValue === params.oldValue
    )
      return;

    if (params.colDef.field === "email_subscribed") {
      const action = params.newValue ? "assign" : "unassign";
      dispatch(
        handleUpdateNotificationSubscriptions(action, params.data.id.toString())
      );
    }
  };

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
  };

  const onSortChanged = (params) => {
    params.api.redrawRows();
  };

  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };

  const gridOptions = {
    rowData: displayedNotifications,
    columnDefs: notificationsColumnDefs,
    defaultColDef: {
      wrapText: true,
      cellClass: ["no-border", "custom-wrap"],
    },
    frameworkComponents: {
      emailCellRenderer: ToggleCellEditor,
    },
    rowClassRules,
    onSortChanged,
    onGridReady,
    onCellValueChanged: onCellValueChanged,
    sideBar: false,
    suppressRowClickSelection: true,
    getRowNodeId: (data) => data.id,
  };

  const tabs = [
    { name: "Status", selected: selectedTab === "STATUS" },
    { name: "Issues", selected: selectedTab === "ISSUES" },
    { name: "Other", selected: selectedTab === "OTHER" },
  ];

  return (
    <div className="notifications-wrapper">
      <h3>Notifications</h3>
      <TableContainer
        tabs={tabs}
        handleToggle={handleTabSelection}
        searchInput={searchInput}
        setSearchInput={setSearchInput}
        selectedRows={[]}
        showExport={false}
      >
        {displayedNotifications && <AgTable gridOptions={gridOptions} />}
      </TableContainer>
    </div>
  );
};

export default Notifications;
