// NPM PACKAGE IMPORTS
import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockAdapter from "axios-mock-adapter";
import thunk from "redux-thunk";

// REDUX IMPORTS
import {
  // ACTION CREATORS
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  updateSucceeded,
  archiveStarted,
  archiveEnded,
  deleteStarted,
  deleteEnded,

  // ACTION HANDLERS
  handleFetchJobs,
  handleFetchJobById,
  handleUpdateJobs,
  handleArchiveJob,
  handleDeleteJob,
  updateStarted,
  handleClearJobsState,
  handleCreateJob,
  handleFetchForgeModelsByJob,
} from "./jobsActions";

import {
  handleFetchColumnState,
  receiveColumnStateStarted,
  receiveColumnStateSucceeded,
} from "../../redux/generalActions";
import { jobsColumnDefs } from "./jobsConstants";

describe("JOBS", () => {
  const testError = {
    error: { status: 404, message: "No Jobs Found" },
  };

  describe("Action handlers should perform the necessary functions", () => {
    let store;
    let httpMock;

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    const testJobs = [
      {
        id: 1,
        job_name: "REVIT test",
        job_number: "0306159-1",
        archived: 0,
        deleted: 0,
      },
      {
        id: 2,
        job_name: "Zach Modelo Testing",
        job_number: "ZMT-001",
        archived: 0,
        deleted: 0,
      },
    ];
    const testArchiveJob = [
      {
        id: 1,
        job_name: "REVIT test",
        job_number: "0306159-1",
        archived: 1,
        deleted: 0,
      },
    ];
    const testDeletedJob = [
      {
        id: 1,
        job_name: "REVIT test",
        job_number: "0306159-1",
        archived: 0,
        deleted: 1,
      },
    ];
    const testSavedColumnState = [
      {
        header_name: "Job #",
        pinned: null,
        visible: 1,
        position: 0,
      },
      {
        header_name: "Manage",
        pinned: "right",
        visible: 1,
        position: 1,
      },
    ];

    it("handleFetchJobs", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/jobs?is_active=1&user_id=1&with_assigned_count=1`
        )
        .replyOnce(200, testJobs)
        .onGet(
          `${process.env.REACT_APP_API}/jobs?is_active=1&user_id=1&with_assigned_count=1`
        )
        .replyOnce(404, testError);

      await store.dispatch(handleFetchJobs(false, null, 1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("JOBS"),
          receiveSucceeded("JOBS", testJobs),
        ];
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testJobs);

        store.clearActions();
      });
      return store.dispatch(handleFetchJobs(false, null, 1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("JOBS"),
          receiveFailed("JOBS", testError),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });
    it("handleFetchJobById: Fetches Jobs by id", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/jobs/1?app_type=fab`)
        .replyOnce(200, testJobs);

      await store.dispatch(handleFetchJobById(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [receiveSucceeded("SPECIFIC_JOB", testJobs[0])];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload).toEqual(testJobs[0]);
        store.clearActions();
      });
    });
    it("handleUpdateJobs: Update Jobs by id", async () => {
      const updatedJobData = { job_name: "updated Job" };
      const updatedJob = [
        {
          id: 1,
          job_name: "updated Job",
          job_number: "0306159-1",
          archived: 0,
          deleted: 0,
        },
      ];
      httpMock
        .onPut(`${process.env.REACT_APP_API}/jobs/1`)
        .reply(200, updatedJob);

      await store.dispatch(handleUpdateJobs(1, updatedJobData)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          updateStarted("JOBS"),
          updateSucceeded("JOBS", updatedJob),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(updatedJob);

        store.clearActions();
      });
    });
    it("handleArchiveJobs", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/jobs/archive?ids=1}`)
        .replyOnce(200, testArchiveJob);

      await store.dispatch(handleArchiveJob(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [archiveStarted("JOBS"), archiveEnded("JOBS")];

        expect(receivedActions).toEqual(expectedActions);

        store.clearActions();
      });
    });
    it("handleDeleteJobs", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/jobs/delete`)
        .replyOnce(200, testDeletedJob);

      await store.dispatch(handleDeleteJob(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [deleteStarted("JOBS"), deleteEnded("JOBS")];

        expect(receivedActions).toEqual(expectedActions);
        store.clearActions();
      });
    });
    it("handleFetchColumnState: Fetches Job Column State", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/ag-table/column-state?app_type=fab&table=jobs`
        )
        .replyOnce(200, testSavedColumnState);

      await store.dispatch(handleFetchColumnState("JOBS")).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveColumnStateStarted("JOBS"),
          receiveColumnStateSucceeded("JOBS", testSavedColumnState),
        ];
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testSavedColumnState);
        store.clearActions();
      });
    });
    it("handleClearJobsState", () => {
      store.dispatch(handleClearJobsState);

      const receivedActions = store.getActions();
      const expectedActions = [receiveSucceeded("CLEAR_JOBS_STATE")];

      expect(receivedActions).toEqual(expectedActions);
      store.clearActions();
    });
    it("handleCreateJob", async () => {
      httpMock
        .onPost(`${process.env.REACT_APP_API}/jobs`)
        .replyOnce(200, [{ id: 1 }])
        .onPost(`${process.env.REACT_APP_API}/jobs`)
        .replyOnce(400, testError);

      await store
        .dispatch(handleCreateJob({ job_name: "Test", job_number: "123456" }))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [receiveSucceeded("SPECIFIC_JOB", { id: 1 })];

          expect(receivedActions).toEqual(expectedActions);
          store.clearActions();
        });

      return store
        .dispatch(handleCreateJob({ job_name: "Test", job_number: "123456" }))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveFailed("SPECIFIC_JOB", testError.error),
          ];

          expect(receivedActions).toEqual(expectedActions);
          store.clearActions();
        });
    });
    it("handleFetchForgeModelsByJob: Fetches Jobs by id", async () => {
      const testModels = [{ id: 1 }, { id: 2 }, { id: 3 }];
      const type = "FORGE_MODELS_BY_JOB";

      httpMock
        .onGet(`${process.env.REACT_APP_API}/forgev2?job_id=1`)
        .replyOnce(200, testModels)
        .onGet(`${process.env.REACT_APP_API}/forgev2?job_id=1`)
        .replyOnce(404, testModels);

      await store.dispatch(handleFetchForgeModelsByJob(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(type),
          receiveSucceeded(type, testModels),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testModels);
        store.clearActions();
      });

      await store.dispatch(handleFetchForgeModelsByJob(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [receiveStarted(type), receiveFailed(type)];

        expect(receivedActions).toEqual(expectedActions);
        store.clearActions();
      });
    });
  });

  describe("Jobs Column Defs", () => {
    const defaultHeaders = [
      "",
      "% Complete",
      "Budget Hours",
      "Drawings",
      "Due Date",
      "Est. Hours +/-",
      "FP Budget",
      "Hours Left",
      "Job #",
      "Job ID",
      "Job Name",
      "Manage",
      "Packages",
      "Total Hours",
      "Work Items",
    ];
    const testJobs = [
      {
        id: 32,
        job_name: "REVIT test",
        job_number: "0306159-1",
        target_date: "2030-10-12T05:00:00.000Z",
        unix_target_date: 1918008000,
        description: null,
        address: null,
        city: null,
        state: null,
        zip: null,
        archived: 0,
        latitude: null,
        longitude: null,
        budget_hours: 1500,
        fp_budget: 652.6275,
        percent_complete: 50,
        est_time_left: 652.6275,
        hours_ahead_behind: 0,
        heat_numbers_required: 0,
        hold_points_required: 0,
        active: 1,
        archived_on: null,
        archived_by: null,
        created_by: 1,
        deleted: 0,
        deleted_by: null,
        deleted_on: null,
        cad_status_flow_id: null,
        prod_time: 0,
        cost_time: 0,
        package_count: 8,
        drawings_count: 95,
        work_item_count: 519,
        assigned_drawings_count: 95,
        assigned_packages_count: 8,
        assigned_work_item_count: 519,
      },
      {
        id: 33,
        job_name: "REVIT test 2",
        job_number: "0306159-11",
        target_date: "2030-10-12T05:00:00.000Z",
        unix_target_date: 1918008000,
        description: null,
        address: null,
        city: null,
        state: null,
        zip: null,
        archived: 0,
        latitude: null,
        longitude: null,
        budget_hours: 1500,
        fp_budget: 652.6275,
        percent_complete: 70,
        est_time_left: 652.6275,
        hours_ahead_behind: 0,
        heat_numbers_required: 0,
        hold_points_required: 0,
        active: 1,
        archived_on: null,
        archived_by: null,
        created_by: 1,
        deleted: 0,
        deleted_by: null,
        deleted_on: null,
        cad_status_flow_id: null,
        prod_time: 0,
        cost_time: 0,
        package_count: 8,
        drawings_count: 95,
        work_item_count: 519,
        assigned_drawings_count: 95,
        assigned_packages_count: 8,
        assigned_work_item_count: 519,
      },
    ];
    const sortState = { sorting_column_name: "id", sorting_method: "asc" };
    const testStore = {
      getState: () => ({ profileData: testStore.profileData }),
      profileData: {
        systemSettings: {
          date_display: "MM-DD-YYYY",
          timezone: "America/Chicago",
        },
        permissions: [279, 280, 281],
      },
    };

    const moreInfoClick = jest.fn();
    const toggleMoreInfo = jest.fn();
    const togglePDFViewer = jest.fn();

    let populatedColumns;

    beforeEach(() => {
      populatedColumns = jobsColumnDefs(
        testJobs,
        moreInfoClick,
        toggleMoreInfo,
        togglePDFViewer,
        sortState,
        testStore
      );
    });

    it("Jobs headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);
      expect(columnHeaders).toEqual(defaultHeaders);
    });

    describe("JOB NUMBER", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Job #");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            job_number: 1,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(1);
      });
      it("valueParser", () => {
        const newValueParams = {
          oldValue: "oldValue",
          newValue: "newValue",
          data: {
            job_number: "1",
            id: 1,
          },
        };
        const oldValueParams = {
          oldValue: "oldValue",
          newValue: "",
          data: {
            job_number: "2",
            id: 1,
          },
        };
        expect(column.valueParser(oldValueParams)).toEqual("oldValue");
        expect(column.valueParser(newValueParams)).toEqual("newValue");
      });
    });

    describe("JOB NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Job Name");
      });

      it("valueParser", () => {
        const newValueParams = {
          oldValue: "oldValue",
          newValue: "newValue",
          data: {
            name: "test",
            id: 1,
          },
        };
        const oldValueParams = {
          oldValue: "oldValue",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };
        expect(column.valueParser(oldValueParams)).toEqual("oldValue");
        expect(column.valueParser(newValueParams)).toEqual("newValue");
      });
      it("valueFormatter", () => {
        expect(column.valueFormatter()).toEqual({ wizardPermission: true });
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            job_name: "test job",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test job");
      });
    });

    describe("PACKAGES", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Packages");
      });
      it("valueGetter", () => {
        const params = {
          data: {
            assigned_packages_count: 1,
          },
        };
        const noCountParams = {
          data: {},
        };
        expect(column.valueGetter(params)).toEqual(1);
        expect(column.valueGetter(noCountParams)).toEqual(0);
      });
      it("Comparator", () => {
        expect([20, 2, 67, 38, 8].sort(column.comparator)).toEqual([
          2,
          8,
          20,
          38,
          67,
        ]);
      });
    });

    describe("DRAWINGS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Drawings");
      });
      it("valueGetter", () => {
        const params = {
          data: {
            assigned_drawings_count: 1,
          },
        };
        const noCountParams = {
          data: {},
        };
        expect(column.valueGetter(params)).toEqual(1);
        expect(column.valueGetter(noCountParams)).toEqual(0);
      });
      it("Comparator", () => {
        expect([200, 100, 2, 34].sort(column.comparator)).toEqual([
          2,
          34,
          100,
          200,
        ]);
      });
    });

    describe("WORK ITEMS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Work Items");
      });
      it("valueGetter", () => {
        const params = {
          data: {
            assigned_work_item_count: 1,
          },
        };
        const noCountParams = {
          data: {},
        };
        expect(column.valueGetter(params)).toEqual(1);
        expect(column.valueGetter(noCountParams)).toEqual(0);
      });
      it("Comparator", () => {
        expect([1, 2, 3, 4, 6, 5].sort(column.comparator)).toEqual([
          1,
          2,
          3,
          4,
          5,
          6,
        ]);
      });
    });

    describe("DUE DATE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Due Date");
      });

      it("valueParser", () => {
        const params = {
          oldValue: 1634533200,
          newValue: "10-20-2021",
        };
        const badParams = {
          // oldValue: 1589500875,
          // HERE
          oldValue: "",
          newValue: "",
        };
        expect(column.valueParser(params)).toEqual(1634706000);
        expect(column.valueParser(badParams)).toEqual(badParams.oldValue);
      });
      it("valueFormatter", () => {
        const params = {
          value: 1621018878,
        };
        const badParams = {
          value: "",
        };
        expect(column.valueFormatter(params)).toEqual("05-14-2021");
        expect(column.valueFormatter(badParams)).toEqual("-");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          value: "1634706000",
        };
        expect(column.getQuickFilterText(params)).toEqual("1634706000");
      });
    });

    describe("PERCENT COMPLETE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "% Complete");
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            percent_complete: 15,
          },
        };
        const emptyParams = {
          data: {},
        };
        expect(column.valueFormatter(params)).toEqual(15);
        expect(column.valueFormatter(emptyParams)).toEqual("0%");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            percent_complete: 15,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(15);
      });
    });

    describe("BUDGET HOURS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Budget Hours");
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            budget_hours: 2.45999,
          },
        };
        const emptyParams = {
          data: {},
        };
        expect(column.valueFormatter(params)).toEqual("2.46");
        expect(column.valueFormatter(emptyParams)).toEqual("0.00");
      });
      it("valueParser", () => {
        const params = {
          oldValue: 10,
          newValue: 100,
        };
        const badParams = {
          oldValue: 10,
          newValue: "",
        };
        expect(column.valueParser(params)).toEqual(100);
        expect(column.valueParser(badParams)).toEqual(badParams.oldValue);
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            budget_hours: 15,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(15);
      });
    });

    describe("FP BUDGET", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "FP Budget");
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            fp_budget: 2.45999,
          },
        };
        const emptyParams = {
          data: {},
        };
        expect(column.valueFormatter(params)).toEqual("2.5");
        expect(column.valueFormatter(emptyParams)).toEqual("0.0");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            fp_budget: 15,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(15);
      });
    });

    describe("HOURS LEFT", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Hours Left");
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            est_time_left: 2.45999,
          },
        };
        const emptyParams = {
          data: {},
        };
        expect(column.valueFormatter(params)).toEqual("2.5");
        expect(column.valueFormatter(emptyParams)).toEqual("0.0");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            est_time_left: 15,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(15);
      });
    });
    describe("EST. HOURS +/-", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Est. Hours +/-"
        );
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            hours_ahead_behind: 2.45999,
          },
        };
        const emptyParams = {
          data: {},
        };
        expect(column.valueFormatter(params)).toEqual("2.5");
        expect(column.valueFormatter(emptyParams)).toEqual(0);
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            hours_ahead_behind: 15,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(15);
      });
    });
    describe("TOTAL HOURS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Total Hours");
      });
      const params = {
        data: {
          cost_time: 280,
        },
      };
      it("valueFormatter", () => {
        expect(column.valueFormatter(params)).toEqual("280.0");
      });
      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(280);
      });
    });
  });
});
