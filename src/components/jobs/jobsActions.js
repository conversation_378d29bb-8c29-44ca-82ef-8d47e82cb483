import {
  fetchJobs,
  fetchLocations,
  editWork,
  deleteJob,
  archiveJob,
  fetchJob<PERSON>yId,
  fetchForgeModelsByJob,
  createJob,
} from "../../_services";
import store from "../../redux/store";

export const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});
export const updateStarted = (type) => ({ type: `UPDATE_${type}_STARTED` });
export const updateSucceeded = (type, payload) => ({
  type: `UPDATE_${type}_SUCCEEDED`,
  payload,
});
export const updateFailed = (type, error) => ({
  type: `UPDATE_${type}_FAILED`,
  payload: error,
});
export const deleteStarted = (type) => ({ type: `DELETE_${type}_STARTED` });
export const deleteEnded = (type) => ({ type: `DELETE_${type}_ENDED` });
export const archiveStarted = (type) => ({ type: `ARCHIVE_${type}_STARTED` });
export const archiveEnded = (type) => ({ type: `ARCHIVE_${type}_ENDED` });

export const handleClearJobsSavedColumnState = (dispatch) => {
  dispatch(receiveSucceeded("JOBS_CLEAR_COLUMN_STATE"));
};

export const handleClearJobsState = (dispatch) => {
  dispatch(receiveSucceeded("CLEAR_JOBS_STATE"));
};

export const handleFetchJobs = (all, workable, testUserId) => (dispatch) => {
  dispatch(receiveStarted("JOBS"));
  return fetchJobs(all, workable, testUserId).then(async (res) => {
    if (res.error) {
      dispatch(receiveFailed("JOBS", res));
    } else {
      dispatch(receiveSucceeded("JOBS", res));
    }
    return res;
  });
};

// unused right now, not in API yet
export const handleFetchJobLocations = (dispatch) => {
  dispatch(receiveStarted("LOCATIONS"));
  return fetchLocations().then((res) => {
    if (res.error) return dispatch(receiveFailed("LOCATIONS", res));
    return dispatch(receiveSucceeded("LOCATIONS", res));
  });
};

export const handleUpdateJobs = (idsToUpdate, updatedData, parentData) => (
  dispatch
) => {
  dispatch(updateStarted("JOBS"));
  return editWork(idsToUpdate, updatedData, "jobs").then((res) => {
    if (res.error) dispatch(updateFailed("JOBS", res));
    else dispatch(updateSucceeded("JOBS", res));

    if (parentData) {
      return fetchJobs(...parentData).then((res) => {
        dispatch(receiveStarted("JOBS"));
        if (res.error) dispatch(receiveFailed("JOBS", res));
        else dispatch(receiveSucceeded("JOBS", res));

        return res;
      });
    }

    return res;
  });
};

export const handleArchiveJob = (jobId) => (dispatch) => {
  dispatch(archiveStarted("JOBS"));
  return archiveJob(jobId).then((res) => {
    dispatch(archiveEnded("JOBS"));
    return res;
  });
};

export const handleDeleteJob = (jobId) => (dispatch) => {
  dispatch(deleteStarted("JOBS"));
  return deleteJob(jobId).then((res) => {
    dispatch(deleteEnded("JOBS"));
    return res;
  });
};

export const handleFetchJobById = (jobId) => (dispatch) => {
  const type = "SPECIFIC_JOB";
  return fetchJobById(jobId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res[0]));

    return res[0];
  });
};

export const handleFetchForgeModelsByJob = (id) => (dispatch) => {
  const type = "FORGE_MODELS_BY_JOB";

  dispatch(receiveStarted(type));
  return fetchForgeModelsByJob(id).then((res) => {
    if (res.error) dispatch(receiveFailed(type));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleCreateJob = (jobInfo) => (dispatch) => {
  const type = "SPECIFIC_JOB";

  return createJob(jobInfo).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res[0]));

    return res[0];
  });
};

export const handleResetJobsList = (dispatch) => {
  const type = "RESET_JOBS_LIST";
  dispatch(receiveSucceeded(type));
};

export const handleSetJobsList = (jobs = []) => (dispatch) => {
  const type = "SET_JOBS_LIST";
  dispatch(receiveSucceeded(type, jobs));
};

// this is used to handle inline edits and properly update the cached state
// Need to update both jobs and storedListOfJobs in order to avoid any stale cached data
export const handleUpdateJobsNoRefresh = (updatedJobs) => (dispatch) => {
  const type1 = "JOBS";
  const type2 = "SET_JOBS_LIST";

  const { jobs, storedListOfJobs } = store.getState().jobsData;
  for (let updatedJob of updatedJobs) {
    const updatedIndex1 = jobs?.findIndex((job) => job.id === updatedJob.id);
    const updatedIndex2 = storedListOfJobs?.findIndex(
      (job) => job.id === updatedJob.id
    );
    if (updatedIndex1 !== -1 && updatedIndex2 !== -1) {
      jobs[updatedIndex1] = updatedJob;
      storedListOfJobs[updatedIndex2] = updatedJob;
    }
  }

  dispatch(receiveSucceeded(type1, jobs));
  dispatch(receiveSucceeded(type2, storedListOfJobs));
};
