// NPM PACKAGE IMPORTS
import React from "react";

// REDUX IMPORTS
import store from "../../redux/store";
import { notify } from "../reusable/alertPopup/alertPopupActions";

// HELPER FUNCTION IMPORTS
import { generateTime, naturalSort, decimalRegex } from "../../_utils";

// COMPONENT IMPORTS
import DueDateCellRenderer from "../reusable/frameworkComponents/DueDateCellRenderer";
import EstHoursCellRenderer from "../reusable/frameworkComponents/EstHoursCellRenderer";
import PercentCompleteCellRenderer from "../reusable/frameworkComponents/PercentCompleteCellRenderer";
import TimerButtonCellRenderer from "../reusable/frameworkComponents/TimerButtonCellRenderer";
import DropdownCellRenderer from "../reusable/frameworkComponents/DropdownCellRenderer";
import PriorityDropdownCellRenderer from "../reusable/frameworkComponents/PriorityDropdownCellRenderer";
import MoreInfoCellRenderer from "../reusable/frameworkComponents/MoreInfoCellRenderer";
import NumberInputCellRenderer from "../reusable/frameworkComponents/NumberInputCellRenderer";
import TextDateCellRenderer from "../reusable/frameworkComponents/TextDateCellRenderer";
import JointHeatNumberCellRenderer from "../reusable/frameworkComponents/JointHeatNumberCellRenderer";
import TextInputCellRenderer from "../reusable/frameworkComponents/TextInputCellRenderer";
import ChildCellRenderer from "../reusable/frameworkComponents/ChildCellRenderer";
import OnHoldCellRenderer from "../reusable/frameworkComponents/OnHoldCellRenderer";
import JobNameCellRenderer from "../reusable/frameworkComponents/JobNameCellRenderer";
import PackageNameCellRenderer from "../reusable/frameworkComponents/PackageNameCellRenderer";
import DropdownEditorRenderer from "../reusable/frameworkComponents/DropdownEditorRenderer";
import DueDateEditorRenderer from "../reusable/frameworkComponents/DueDateEditorRenderer";
import DropdownInputEditorRenderer from "../reusable/frameworkComponents/DropdownInputEditorRenderer";
import FractionalEditorRenderer from "../reusable/frameworkComponents/FractionalEditorRenderer";
import ItemStatusCellRenderer from "../reusable/frameworkComponents/ItemStatusCellRenderer";
import DrawingNameCellRenderer from "../reusable/frameworkComponents/DrawingNameCellRenderer";

// STYLE IMPORTS
import "../styles/tables.scss";

// EXPORTS
export const jobsColumnDefs = (
  savedColumnState,
  moreInfoClick,
  toggleMoreInfo,
  handleChildSelection,
  sortState,
  testStore = null,
  customColumns
) => {
  const { systemSettings, permissions } = (
    testStore || store
  ).getState().profileData;
  let dateFormatting = systemSettings && systemSettings.date_display;
  const defaultDefs = [
    {
      headerName: "Job #",
      field: "job_number",
      minWidth: 100,
      flex: 1,
      getQuickFilterText: (params) => params.data?.job_number,
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue;

        return params.newValue;
      },
      editable: true,
      autoHeight: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "job_number",
      sort:
        sortState.sorting_column_name === "job_number"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Job Name",
      field: "job_name",
      valueFormatter: (params) => ({
        wizardPermission:
          permissions &&
          (permissions.includes(279) ||
            permissions.includes(280) ||
            permissions.includes(281)),
      }),
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue;

        return params.newValue;
      },
      getQuickFilterText: (params) => params.data?.job_name,
      cellRenderer: "jobNameCellRenderer",
      editable: true,
      minWidth: 140,
      flex: 1,
      autoHeight: true,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      cellClass: "custom-wrap",
      menuTabs: ["filterMenuTab"],
      colId: "job_name",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
      sort:
        sortState.sorting_column_name === "job_name"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Job ID",
      field: "id",
      minWidth: 100,
      flex: 1,
      autoHeight: true,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "id",
      sort:
        sortState.sorting_column_name === "id"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Packages",
      valueGetter: (params) =>
        params.data?.assigned_packages_count
          ? params.data?.assigned_packages_count
          : params.data?.package_count || 0,
      cellRendererParams: {
        handleChildSelection,
        type: "PACKAGES",
      },
      cellRenderer: "childCellRenderer",
      minWidth: 100,
      flex: 1,
      autoHeight: true,
      permissions: [37],
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      comparator: (valueA, valueB) => valueA - valueB,
      colId: "packages",
      sort:
        sortState.sorting_column_name === "packages"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Drawings",
      minWidth: 100,
      flex: 1,
      autoHeight: true,
      permissions: [51],
      valueGetter: (params) =>
        params.data?.assigned_drawings_count
          ? params.data?.assigned_drawings_count
          : params.data?.drawings_count || 0,
      cellRendererParams: {
        handleChildSelection,
        type: "DRAWINGS",
      },
      cellRenderer: "childCellRenderer",
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      comparator: (valueA, valueB) => valueA - valueB,
      colId: "drawings",
      sort:
        sortState.sorting_column_name === "drawings"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Work Items",
      valueGetter: (params) =>
        params.data?.assigned_work_item_count
          ? params.data?.assigned_work_item_count
          : params.data?.work_item_count,
      minWidth: 100,
      flex: 1,
      autoHeight: true,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      comparator: (valueA, valueB) => valueA - valueB,
      colId: "work_items",
      sort:
        sortState.sorting_column_name === "work_items"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Due Date",
      field: "unix_target_date",
      valueFormatter: (params) => {
        return params.value
          ? typeof params.value === "number"
            ? generateTime(params.value * 1000, false, true, "-", testStore)
            : params.value
          : "-";
      },
      valueParser: (params) => {
        if (params.newValue === 0) return 0;
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue;

        const defaultRegex = new RegExp("\\d{2}-\\d{2}-\\d{4}");
        const dateFormattingRegex = dateFormatting
          ? /-/.test(dateFormatting)
            ? new RegExp(
                dateFormatting
                  .split("-")
                  .map((part) => `\\d{${part.length}}`)
                  .join("\\-")
              )
            : /\//.test(dateFormatting)
            ? new RegExp(
                dateFormatting
                  .split("/")
                  .map((part) => `\\d{${part.length}}`)
                  .join("\\/")
              )
            : defaultRegex
          : defaultRegex;

        if (!dateFormattingRegex.test(params.newValue)) {
          return params.oldValue;
        } else {
          return Math.floor(new Date(params.newValue).getTime() / 1000);
        }
      },
      getQuickFilterText: (params) => params.colDef.valueFormatter(params),
      filter: "agDateColumnFilter",
      cellEditor: "dueDateEditorRenderer",
      editable: true,
      minWidth: 100,
      flex: 1,
      autoHeight: true,
      filterParams: {
        buttons: ["reset"],
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = cellValue
            ? typeof cellValue === "number"
              ? new Date(
                  generateTime(cellValue * 1000, false, true, "-", testStore)
                )
              : new Date(cellValue)
            : "-";

          return cellDate < filterLocalDateAtMidnight
            ? -1
            : cellDate > filterLocalDateAtMidnight
            ? 1
            : 0;
        },
      },
      menuTabs: ["filterMenuTab"],
      colId: "unix_target_date",
      sort:
        sortState.sorting_column_name === "unix_target_date"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "% Complete",
      field: "percent_complete",
      // @ToDo: Fix rounding...
      valueGetter: (params) => {
        if (!params.data || !params.data.percent_complete) return 0;
        const { percent_complete } = params.data;
        return Math.floor(percent_complete.toFixed(0));
      },
      valueFormatter: (params) => {
        if (!params.data || !params.data.percent_complete) return 0;
        const { percent_complete } = params.data;
        return Math.floor(percent_complete.toFixed(0));
      },
      getQuickFilterText: (params) => params.data?.percent_complete,
      cellRenderer: "percentCompleteCellRenderer",
      minWidth: 120,
      flex: 1,
      autoHeight: true,
      permissions: [63],
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "percent_complete",
      sort:
        sortState.sorting_column_name === "percent_complete"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Budget Hours",
      field: "budget_hours",
      valueFormatter: (params) => {
        if (!params.data) return;
        const { budget_hours } = params.data;
        const value = budget_hours
          ? `${parseFloat(budget_hours).toFixed(2)}`
          : "0.00";
        return value;
      },
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue;

        if (!/^\d{0,}\.?\d{1,}$/.test(params.newValue)) return params.oldValue;

        return params.newValue;
      },
      getQuickFilterText: (params) => params.data?.budget_hours,
      editable: true,
      minWidth: 100,
      flex: 1,
      autoHeight: true,
      permissions: [66],
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "budget_hours",
      sort:
        sortState.sorting_column_name === "budget_hours"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "FP Budget",
      field: "fp_budget",
      valueFormatter: (params) => {
        if (!params.data) return;
        const { fp_budget } = params.data;
        const value = fp_budget ? `${fp_budget.toFixed(1)}` : "0.0";
        return value;
      },
      getQuickFilterText: (params) => params.data?.fp_budget,
      minWidth: 100,
      flex: 1,
      autoHeight: true,
      permissions: [66],
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "fp_budget",
      sort:
        sortState.sorting_column_name === "fp_budget"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Hours Left",
      field: "est_time_left",
      valueFormatter: (params) => {
        if (!params.data) return;
        const { est_time_left } = params.data;
        const value = est_time_left ? `${est_time_left.toFixed(1)}` : "0.0";
        return value;
      },
      getQuickFilterText: (params) => params.data?.est_time_left,
      minWidth: 100,
      flex: 1,
      autoHeight: true,
      permissions: [66],
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "est_time_left",
      sort:
        sortState.sorting_column_name === "est_time_left"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Est. Hours +/-",
      field: "hours_ahead_behind",
      valueFormatter: (params) => {
        if (!params.data) return;
        const { hours_ahead_behind } = params.data;
        return hours_ahead_behind ? hours_ahead_behind.toFixed(1) : 0;
      },
      getQuickFilterText: (params) => params.data?.hours_ahead_behind,
      cellRenderer: "estHoursCellRenderer",
      minWidth: 100,
      flex: 1,
      autoHeight: true,
      permissions: [69],
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "hours_ahead_behind",
      sort:
        sortState.sorting_column_name === "hours_ahead_behind"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) => (valueA || 0) - (valueB || 0),
    },
    {
      headerName: "Total Hours",
      field: "cost_time",
      valueFormatter: (params) => {
        if (!params.data) return;
        const { cost_time } = params.data;
        return cost_time ? cost_time.toFixed(1) : 0;
      },
      getQuickFilterText: (params) => params.data?.cost_time,
      minWidth: 100,
      flex: 1,
      autoHeight: true,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "cost_time",
      sort:
        sortState.sorting_column_name === "cost_time"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Manage",
      sortable: false,
      suppressMenu: true,
      valueFormatter: (params) => {
        return {
          moreInfoClick,
          toggleMoreInfo,
        };
      },
      cellRenderer: "moreInfoCellRenderer",
      width: 60,
      minWidth: 60,
      suppressSizeToFit: true,
      pinned: "right",
      lockVisible: true,
      suppressMovable: true,
      suppressColumnsToolPanel: true,
      colId: "manage",
    },
  ];

  if (customColumns?.length) {
    for (let column of customColumns) {
      const columnDef = {
        autoHeight: true,
        // suppressSizeToFit: true,
        headerName: column.display_name,
        field: column.normal_name,
        valueParser: (params) => {
          if (params.newValue === "" || params.newValue === undefined)
            return params.oldValue === "" || params.oldValue === undefined
              ? params.oldValue
              : null;

          if (column.data_type === "decimal") {
            if (!decimalRegex.test(params.newValue)) {
              (testStore || store).dispatch(
                notify({
                  id: Date.now(),
                  type: "ERROR",
                  message: "Value must be a number",
                })
              );
              return params.oldValue;
            }
          }
          return params.newValue;
        },
        cellClass: "wrap-text",
        menuTabs: [],
        minWidth: 120,
        flex: 1,
        editable: true,
        sortable: false,
        colId: column.normal_name,
      };

      if (column.data_type === "date") {
        columnDef.filter = "agDateColumnFilter";
        columnDef.filterParams = {
          buttons: ["reset"],
          comparator: (filterLocalDateAtMidnight, cellValue) => {
            const cellDate = cellValue
              ? typeof cellValue === "number"
                ? new Date(generateTime(cellValue * 1000, false, true, "-"))
                : new Date(cellValue)
              : "-";

            return cellDate < filterLocalDateAtMidnight
              ? -1
              : cellDate > filterLocalDateAtMidnight
              ? 1
              : 0;
          },
        };
        columnDef.valueFormatter = (params) => {
          return params.value
            ? typeof params.value === "number"
              ? generateTime(params.value * 1000, false, true, "-", testStore)
              : params.value
            : "";
        };

        if (column.editable) {
          columnDef.cellEditor = "dueDateEditorRenderer";
          columnDef.valueParser = (params) => {
            if (params.newValue === "" || params.newValue === undefined)
              return params.oldValue;
            if (params.newValue === 0) return 0;

            const defaultRegex = new RegExp("\\d{2}-\\d{2}-\\d{4}");
            const dateFormattingRegex = dateFormatting
              ? /-/.test(dateFormatting)
                ? new RegExp(
                    dateFormatting
                      .split("-")
                      .map((part) => `\\d{${part.length}}`)
                      .join("\\-")
                  )
                : /\//.test(dateFormatting)
                ? new RegExp(
                    dateFormatting
                      .split("/")
                      .map((part) => `\\d{${part.length}}`)
                      .join("\\/")
                  )
                : defaultRegex
              : defaultRegex;
            if (!dateFormattingRegex.test(params.newValue)) {
              return params.oldValue;
            } else return params.newValue; //new Date(params.newValue);
          };
        }
      }

      defaultDefs.push(columnDef);
    }
  }

  if (savedColumnState && savedColumnState.length) {
    let result = [];

    for (let i = 0; i < defaultDefs.length; i++) {
      let savedDef = savedColumnState.find(
        (c) => c.header_name === defaultDefs[i].headerName
      );

      if (savedDef) {
        result.push({
          ...defaultDefs[i],
          pinned: savedDef.pinned,
          hide: savedDef.visible ? false : true,
          position: savedDef.position,
        });
      } else result.push(defaultDefs[i]);
    }

    result = result
      .sort((a, b) => {
        if (a.position === b.position) {
          if (a.headerName.toLowerCase() > b.headerName.toLowerCase()) return 1;
          else return -1;
        } else return a.position - b.position;
      })
      .map((col) => {
        if (col.position !== undefined) delete col.position;
        return col;
      });

    result.unshift({
      headerName: "",
      headerCheckboxSelection: customColumns?.length ? false : true,
      headerCheckboxSelectionFilteredOnly: true,
      width: 40,
      minWidth: 40,
      maxWidth: 40,
      checkboxSelection: true,
      suppressMovable: true,
      lockVisible: true,
      suppressMenu: true,
      pinned: "left",
      suppressColumnsToolPanel: true,
      colId: "checkbox",
    });

    return result;
  } else {
    defaultDefs.unshift({
      headerName: "",
      headerCheckboxSelection: customColumns?.length ? false : true,
      headerCheckboxSelectionFilteredOnly: true,
      width: 40,
      minWidth: 40,
      maxWidth: 40,
      checkboxSelection: true,
      suppressMovable: true,
      lockVisible: true,
      suppressMenu: true,
      pinned: "left",
      suppressColumnsToolPanel: true,
      colId: "checkbox",
    });

    return defaultDefs;
  }
};

export const frameworkComponents = (setStatusTrackerPopup) => ({
  dueDateCellRenderer: DueDateCellRenderer,
  estHoursCellRenderer: EstHoursCellRenderer,
  percentCompleteCellRenderer: (params) => (
    <PercentCompleteCellRenderer
      params={params}
      setStatusTrackerPopup={setStatusTrackerPopup}
    />
  ),
  timerButtonCellRenderer: TimerButtonCellRenderer,
  dropdownCellRenderer: DropdownCellRenderer,
  priorityDropdownCellRenderer: PriorityDropdownCellRenderer,
  textInputCellRenderer: TextInputCellRenderer,
  moreInfoCellRenderer: MoreInfoCellRenderer,
  numberInputCellRenderer: NumberInputCellRenderer,
  textDateCellRenderer: TextDateCellRenderer,
  jointHeatNumberCellRenderer: JointHeatNumberCellRenderer,
  childCellRenderer: ChildCellRenderer,
  onHoldCellRenderer: OnHoldCellRenderer,
  jobNameCellRenderer: JobNameCellRenderer,
  packageNameCellRenderer: PackageNameCellRenderer,
  dropdownEditorRenderer: DropdownEditorRenderer,
  dueDateEditorRenderer: DueDateEditorRenderer,
  dropdownInputEditorRenderer: DropdownInputEditorRenderer,
  fractionalEditorRenderer: FractionalEditorRenderer,
  itemStatusCellRenderer: (params) => (
    <ItemStatusCellRenderer
      params={params}
      setStatusTrackerPopup={setStatusTrackerPopup}
    />
  ),
  drawingNameCellRenderer: DrawingNameCellRenderer,
});

export const generateTabs = (group, clickHandlers, selected) => {
  switch (group) {
    case "JOBS":
      return [{ name: "Existing Jobs" }];
    case "EXISTING_JOBS":
      return [
        { name: "My Work", onClick: clickHandlers[0], selected: !selected },
        {
          name: "All Work",
          onClick: clickHandlers[1],
          selected: selected,
          permissions: [287],
        }, // 287
      ];
    default:
      return [];
  }
};
