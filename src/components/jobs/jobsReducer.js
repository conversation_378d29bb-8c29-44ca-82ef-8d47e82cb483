const initialState = {
  isLoading: false,
  isPUTLoading: false,
  error: null,
  jobs: null,
  allJobs: [],
  jobLocations: [],
  statusTracker: [],
  savedColumnState: null,
  specificJob: null,
  forgeModelsByJob: [],
  storedListOfJobs: [],
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_CLEAR_JOBS_STATE_SUCCEEDED":
      return { ...initialState };
    case "RECEIVE_JOBS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_JOBS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, jobs: payload };
    case "RECEIVE_JOBS_FAILED":
      return { ...state, isLoading: false, error: payload, jobs: null };
    case "RECEIVE_ALL_JOBS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_ALL_JOBS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, allJobs: payload };
    case "RECEIVE_ALL_JOBS_FAILED":
      return { ...state, isLoading: false, error: payload, allJobs: [] };
    case "UPDATE_JOBS_STARTED":
      return { ...state, isPUTLoading: true, error: null };
    case "UPDATE_JOBS_FAILED":
      return { ...state, isPUTLoading: false, error: payload };
    case "UPDATE_JOBS_SUCCEEDED":
      return { ...state, isPUTLoading: false, error: null };
    case "RECEIVE_LOCATIONS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_LOCATIONS_SUCCEEDED":
      return { ...state, isLoading: false, jobLocations: payload, error: null };
    case "RECEIVE_LOCATIONS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "DELETE_JOBS_STARTED":
      return { ...state, isLoading: true };
    case "DELETE_JOBS_ENDED":
      return { ...state, isLoading: false };
    case "ARCHIVE_JOBS_STARTED":
      return { ...state, isLoading: true };
    case "ARCHIVE_JOBS_ENDED":
      return { ...state, isLoading: false };
    case "RECEIVE_JOBS_COLUMN_STATE_STARTED":
      return { ...state, savedColumnState: null };
    case "RECEIVE_JOBS_COLUMN_STATE_SUCCEEDED":
      return { ...state, savedColumnState: payload };
    case "RECEIVE_JOBS_COLUMN_STATE_FAILED":
      return { ...state, savedColumnState: [] };
    case "RECEIVE_SPECIFIC_JOB_SUCCEEDED":
      return { ...state, specificJob: payload };
    case "RECEIVE_SPECIFIC_JOB_FAILED":
      return { ...state, specificJob: null };
    case "RECEIVE_FORGE_MODELS_BY_JOB_STARTED":
      return { ...state, forgeModelsByJob: [] };
    case "RECEIVE_FORGE_MODELS_BY_JOB_SUCCEEDED":
      return { ...state, forgeModelsByJob: payload };
    case "RECEIVE_FORGE_MODELS_BY_JOB_FAILED":
      return { ...state, forgeModelsByJob: [] };
    case "RECEIVE_JOBS_CLEAR_COLUMN_STATE_SUCCEEDED":
      return { ...state, savedColumnState: null };
    case "RECEIVE_SET_JOBS_LIST_SUCCEEDED":
      return { ...state, storedListOfJobs: payload };
    case "RECEIVE_RESET_JOBS_LIST_SUCCEEDED":
      return { ...state, jobs: state.storedListOfJobs };
    default:
      return state;
  }
}
