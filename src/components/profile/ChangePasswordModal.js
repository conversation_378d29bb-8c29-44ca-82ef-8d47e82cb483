// NPM PACKAGE IMPORTS
import React, { useState, useMemo, useEffect } from "react";
import { useDispatch } from "react-redux";
import Button from "msuite_storybook/dist/button/Button";
import Input from "msuite_storybook/dist/input/Input";
import { FaInfoCircle } from "react-icons/fa";

// REDUX IMPORTS
import { notify } from "../reusable/alertPopup/alertPopupActions";

// COMPONENT IMPORTS
import Modal from "../reusable/modal/Modal";
import PasswordTooltip from "./PasswordTooltip";

// HELPER FUNCTION IMPORTS
import { confirmPassword, validatePassword } from "../../_utils";

const ChangePasswordModal = ({
  showModal,
  toggleModal,
  onSubmit,
  username,
}) => {
  const [newPassword, setNewPassword] = useState("");
  const [newPasswordRepeated, setNewPasswordRepeated] = useState("");
  const [oldPassword, setOldPassword] = useState("");
  const [isPasswordMatch, togglePasswordMatch] = useState(true);
  const [showTooltip, toggleTooltip] = useState(false);

  const dispatch = useDispatch();

  useEffect(() => {
    if (!newPassword || !newPasswordRepeated) return togglePasswordMatch(true);

    togglePasswordMatch(
      newPassword.trim() === newPasswordRepeated.trim() ? true : false
    );
  }, [newPassword, newPasswordRepeated]);

  const passwordReqNotification = () => {
    return dispatch(
      notify({
        id: Date.now(),
        type: "ERROR",
        message: "Password does not meet requirements.",
      })
    );
  };

  const onNewPasswordBlur = () => {
    if (!newPassword) return;

    if (!validatePassword(newPassword)) {
      passwordReqNotification();
    }
  };

  const disableSubmit = useMemo(() => {
    if (!newPassword.length || !newPasswordRepeated.length) return true;
    return !oldPassword || !isPasswordMatch ? true : false;
  }, [isPasswordMatch, oldPassword, newPassword, newPasswordRepeated]);

  const handleSubmit = async () => {
    const passwordCheck = await confirmPassword(username, oldPassword);

    if (!passwordCheck) {
      dispatch(
        notify({
          id: Date.now(),
          type: "ERROR",
          message: "Password is incorrect. Please try again.",
        })
      );
      setNewPassword("");
      setNewPasswordRepeated("");
      setOldPassword("");

      return;
    }

    if (!validatePassword(newPassword)) {
      dispatch(
        notify({
          id: Date.now(),
          type: "ERROR",
          message: "Password does not meet requirements.",
        })
      );

      setNewPassword("");
      setNewPasswordRepeated("");

      return;
    }

    onSubmit(newPassword);
  };

  return (
    <Modal
      showModal={showModal}
      toggleModal={toggleModal}
      onSubmit={onSubmit}
      title="Change Password"
      onClose={() => toggleModal(false)}
      suppressForm={true}
    >
      <div className="change-password-wrapper">
        <label className="label">
          <span>Old Password:</span>
          <Input
            placeholder="Old Password"
            value={oldPassword}
            onChange={(e) => setOldPassword(e.target.value)}
            type="password"
          />
        </label>
        <div className="input-button-wrapper">
          <label className="label">
            <span>New Password:</span>
            <Input
              placeholder="New Password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              type="password"
              onBlur={onNewPasswordBlur}
            />
          </label>
          <FaInfoCircle onClick={() => toggleTooltip(!showTooltip)} />
        </div>
        <label className="label">
          <span></span>
          <Input
            placeholder="Repeat Password"
            value={newPasswordRepeated}
            onChange={(e) => setNewPasswordRepeated(e.target.value)}
            type="password"
          />
        </label>
        <div className="error-message-wrapper">
          {!isPasswordMatch && (
            <p className="error">* Passwords must be matching.</p>
          )}
        </div>
        <div className="button-row">
          <Button className="cancel" onClick={() => toggleModal(false)}>
            Cancel
          </Button>
          <Button disabled={disableSubmit} onClick={handleSubmit}>
            Submit
          </Button>
        </div>
      </div>
      {showTooltip && <PasswordTooltip />}
    </Modal>
  );
};

export default ChangePasswordModal;
