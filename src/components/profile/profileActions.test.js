// NPM PACKAGE IMPORTS
import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockAdapter from "axios-mock-adapter";
import thunk from "redux-thunk";

// REDUX IMPORTS
import {
  // ACTION CREATORS
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  updateStarted,
  updateSucceeded,
  updateFailed,
  // ACTION HANDLERS
  handleReceiveUserPrefs,
  handleReceiveToken,
  handleReceiveUserId,
  handleReceivePermissions,
  handleReceiveRole,
  handleCheckRegularShift,
  handleCheckPartnerShifts,
  handleReceiveUserSettings,
  handleFetchUserInfo,
  handleFetchShiftNow,
  handleClockIn,
  handleExtendShift,
  handleFetchLastShift,
  handleFetchPredefinedShifts,
  handleClockIntoShift,
  handleFetchFutureShift,
  handleAcknowledgeShiftEnd,
  handleFetchTableFilters,
  handleSaveTableFilters,
  handleReceiveFeatures,
  handleCheckReleasesPageNotification,
  handleFetchViewingAll,
  handleFetchMyWorkTableViewSettings,
  handleUpdateTableViewSettings,
  handleClearTableFilters,
  handleFetchSystemSettings,
  handleFetchPackagesPendingApprovalViewingAll,
  handleFetchDrawingsPendingApprovalViewingAll,
  handleFetchUserSettings,
  handleFetchUserNotifications,
  handleUpdateNotificationSubscriptions,
  handleFetchProducts,
} from "./profileActions";

describe("PROFILE", () => {
  let store, httpMock;

  const testError = (mes) => ({
    error: { status: 404, message: mes },
  });

  const testStore = {
    getState: () => ({ profileData: testStore.profileData }),
    profileData: {
      userId: 1,
      userInfo: {
        username: "<EMAIL>",
      },
    },
  };

  const testReturn = [
    { id: 1, item_ids_array: [] },
    { id: 2, item_ids_array: [] },
    { id: 3, item_ids_array: [] },
    { id: 4, item_ids_array: [] },
  ];

  const testNotifications = [
    {
      id: 1,
      name: "test",
      description: "desc",
      display_name: "Test",
      email: 1,
      permission_id: 1,
      email_subscribed: 0,
    },
  ];

  beforeEach(() => {
    httpMock = new MockAdapter(axios);
    const mockStore = configureMockStore([thunk]);
    store = mockStore({
      profileData: {
        systemSettings: {
          time_display: "yyyy-MM-DD",
          timezone: "America/Chicago",
        },
        username: "<EMAIL>",
        userId: 1,
      },
    });
  });

  describe("handleReceiveUserPrefs", () => {
    it("should store the user preferences if they exist", () => {
      store.dispatch(handleReceiveUserPrefs({}));

      let receivedActions = store.getActions(),
        expectedActions = [];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      store.dispatch(handleReceiveUserPrefs({ jobsColumnState: testReturn }));

      receivedActions = store.getActions();
      expectedActions = [receiveSucceeded("USER_PREFS_JOBS", testReturn)];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleReceiveToken", () => {
    it("should store the token", () => {
      store.dispatch(handleReceiveToken("8675309"));

      let receivedActions = store.getActions(),
        expectedActions = [receiveSucceeded("TOKEN", "8675309")];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleReceiveUserId", () => {
    it("should store the user id", () => {
      store.dispatch(handleReceiveUserId(15));

      let receivedActions = store.getActions(),
        expectedActions = [receiveSucceeded("USER_ID", 15)];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleReceivePermissions", () => {
    it("should store the user permissions", () => {
      store.dispatch(handleReceivePermissions(testReturn));

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveSucceeded("CURRENT_USER_PERMISSIONS", testReturn),
        ];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleReceiveRole", () => {
    it("should store the user's role", () => {
      store.dispatch(handleReceiveRole(15));

      let receivedActions = store.getActions(),
        expectedActions = [receiveSucceeded("ROLE", 15)];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleCheckRegularShift", () => {
    it("should check for, and store, the user's current shift", async () => {
      httpMock
        .onGet("self/shiftNow")
        .replyOnce(200, { shift_end: 21600 })
        .onGet("self/shiftNow")
        .replyOnce(404, testError("No shift"));

      let response = await store.dispatch(handleCheckRegularShift);

      let receivedActions = store.getActions();
      let expectedActions = [receiveSucceeded("SHIFT_END", 21600000)];

      expect(response).toEqual({ shift_end: 21600 });
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleCheckRegularShift);

      receivedActions = store.getActions();
      expectedActions = [receiveSucceeded("SHIFT_END", testError("No shift"))];

      expect(response).toEqual(testError("No shift"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleCheckPartnerShifts", () => {
    it("should check for, and store, the user's partner shifts", async () => {
      httpMock
        .onGet("self/partners/shifts")
        .replyOnce(200, testReturn)
        .onGet("self/partners/shifts")
        .replyOnce(404, testError("No shift"));

      let response = await store.dispatch(handleCheckPartnerShifts);

      let receivedActions = store.getActions(),
        expectedActions = [receiveSucceeded("PARTNER_SHIFTS", testReturn)];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleCheckPartnerShifts);

      receivedActions = store.getActions();
      expectedActions = [
        receiveSucceeded("PARTNER_SHIFTS", testError("No shift")),
      ];

      expect(response).toEqual(testError("No shift"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleReceiveUserSettings", () => {
    it("should store the user's settings", () => {
      store.dispatch(handleReceiveUserSettings({ some_setting: true }));

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveSucceeded("USER_SETTINGS", { some_setting: true }),
        ];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleFetchSystemSettings", () => {
    it("should fetch, and store, the company system settings", async () => {
      httpMock
        .onGet("system-settings")
        .replyOnce(200, [{ some_setting: true }]);

      await store.dispatch(handleFetchSystemSettings(1, true));

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveSucceeded("SYSTEM_SETTINGS", { some_setting: true }),
        ];

      expect(receivedActions).toEqual(expectedActions);
    });
  });

  describe("handleFetchUserInfo", () => {
    it("should check for, and store, the user's partner shifts", async () => {
      httpMock
        .onGet("users/15")
        .replyOnce(200, testReturn)
        .onGet("users/15")
        .replyOnce(404, testError("No user"));

      let response = await store.dispatch(handleFetchUserInfo(15));

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveStarted("USER_INFO"),
          receiveSucceeded("USER_INFO", testReturn[0]),
        ];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleFetchUserInfo(15));

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted("USER_INFO"),
        receiveFailed("USER_INFO", testError("No user")),
      ];

      expect(response).toEqual(testError("No user"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleFetchShiftNow", () => {
    it("should fetch the user's current shift", async () => {
      httpMock
        .onGet("self/shiftNow")
        .replyOnce(200, testReturn)
        .onGet("self/shiftNow")
        .replyOnce(404, testError("No shift"));

      let response = await store.dispatch(handleFetchShiftNow);

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveStarted("SHIFT_NOW"),
          receiveSucceeded("SHIFT_NOW", testReturn),
        ];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleFetchShiftNow);

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted("SHIFT_NOW"),
        receiveFailed("SHIFT_NOW", testError("No shift")),
      ];

      expect(response).toEqual(testError("No shift"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleClockIn", () => {
    it("should clock the user into a custom or predefined shift", async () => {
      httpMock
        .onPost("self/shifts")
        .replyOnce(200, testReturn)
        .onPost("self/shifts")
        .replyOnce(200, testReturn)
        .onPost("self/shifts")
        .replyOnce(200, testReturn)
        .onPost("self/shifts")
        .replyOnce(404, testError("No shift"));
      const testWindow = {
          ChurnZero: {
            push: (arr) => testWindow.ChurnZero.data.push(arr),
            data: [],
          },
        },
        churnZeroData = (shiftType) => [
          "trackEvent",
          "FP Shift Created",
          shiftType,
          1,
          {
            Product: "FabPro",
            SubGroup: "Shifts",
            Version: process.env.REACT_APP_ENVIRONMENT,
            UserName: testStore.getState().profileData.userInfo.username,
            UserId: testStore.getState().profileData.userId,
          },
        ];

      let response = await store.dispatch(
        handleClockIn(null, testWindow, testStore)
      );

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveStarted("SHIFT_NOW"),
          receiveSucceeded("SHIFT_NOW", testReturn[0]),
        ];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);
      expect(httpMock.history.post.length).toBe(1);
      expect(httpMock.history.post[0].data).toEqual(
        JSON.stringify({ method: "clockIn" })
      );
      expect(testWindow.ChurnZero.data).toEqual([churnZeroData("Custom")]);

      testWindow.ChurnZero.data = [];
      store.clearActions();

      response = await store.dispatch(
        handleClockIn(
          { id: 5, start_time: 100.55, end_time: 1000.23 },
          testWindow,
          testStore
        )
      );

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted("SHIFT_NOW"),
        receiveSucceeded("SHIFT_NOW", testReturn[0]),
      ];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);
      expect(httpMock.history.post.length).toBe(2);
      expect(httpMock.history.post[1].data).toEqual(
        JSON.stringify({ shift_type_id: 5, shift_start: 100, shift_end: 1000 })
      );
      expect(testWindow.ChurnZero.data).toEqual([churnZeroData("Predefined")]);

      testWindow.ChurnZero.data = [];
      store.clearActions();

      response = await store.dispatch(
        handleClockIn(
          {
            id: 5,
            start_time: Math.trunc(new Date("2100-01-01").valueOf() / 1000),
            end_time: 1000.23,
          },
          testWindow,
          testStore
        )
      );

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted("SHIFT_NOW"),
        receiveSucceeded("SHIFT_NOW", testReturn[0]),
        receiveSucceeded("FUTURE_SHIFT", testReturn[0]),
      ];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);
      expect(httpMock.history.post.length).toBe(3);
      expect(httpMock.history.post[2].data).toEqual(
        JSON.stringify({
          shift_type_id: 5,
          shift_start: Math.trunc(new Date("2100-01-01").valueOf() / 1000),
          shift_end: 1000,
        })
      );
      expect(testWindow.ChurnZero.data).toEqual([churnZeroData("Predefined")]);

      testWindow.ChurnZero.data = [];
      store.clearActions();

      response = await store.dispatch(
        handleClockIn(
          { id: 5, start_time: 100.55, end_time: 1000.23 },
          testWindow,
          testStore
        )
      );

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted("SHIFT_NOW"),
        receiveFailed("SET_SHIFT_NOW", testError("No shift")),
      ];

      expect(response).toEqual(testError("No shift"));
      expect(receivedActions).toEqual(expectedActions);
      expect(testWindow.ChurnZero.data).toEqual([]);

      store.clearActions();
    });
  });

  describe("handleExtendShift", () => {
    it("should extend the user's shift", async () => {
      const specialTestReturn = [{ shift_start: 100, expected_duration: 500 }];

      httpMock
        .onPut("self/shifts/1")
        .replyOnce(200, specialTestReturn)
        .onPut("self/shifts/1")
        .replyOnce(404, testError("No shift"));

      let response = await store.dispatch(handleExtendShift(1, 5000));

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveStarted("SHIFT_NOW"),
          receiveSucceeded("SHIFT_NOW", specialTestReturn[0]),
          receiveSucceeded("SHIFT_END", 600000),
        ];

      expect(response).toEqual(specialTestReturn);
      expect(receivedActions).toEqual(expectedActions);
      expect(httpMock.history.put.length).toBe(1);
      expect(httpMock.history.put[0].data).toEqual(
        JSON.stringify({ shift_end: 5 })
      );
      store.clearActions();

      response = await store.dispatch(handleExtendShift(1, 5000));

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted("SHIFT_NOW"),
        receiveFailed("SET_SHIFT_NOW", testError("No shift")),
      ];

      expect(response).toEqual(testError("No shift"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleFetchLastShift", () => {
    it("should fetch the user's most recent shift", async () => {
      httpMock
        .onGet("shifts?last_shift=true")
        .replyOnce(200, testReturn)
        .onGet("shifts?last_shift=true")
        .replyOnce(404, testError("No shift"));

      let response = await store.dispatch(handleFetchLastShift);

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveStarted("LAST_SHIFT"),
          receiveSucceeded("LAST_SHIFT", testReturn[0]),
        ];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleFetchLastShift);

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted("LAST_SHIFT"),
        receiveFailed("LAST_SHIFT", testError("No shift")),
      ];

      expect(response).toEqual(testError("No shift"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleFetchPredefinedShifts", () => {
    it("should fetch the list of predefined shifts", async () => {
      httpMock
        .onGet(
          "predefined-shifts?status=available&statusTime=" +
            Math.trunc(Date.now() / 1000)
        )
        .replyOnce(200, testReturn)
        .onGet(
          "predefined-shifts?status=available&statusTime=" +
            Math.trunc(Date.now() / 1000)
        )
        .replyOnce(404, testError("No shift"));

      let response = await store.dispatch(handleFetchPredefinedShifts);

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveStarted("PREDEFINED_SHIFTS"),
          receiveSucceeded("PREDEFINED_SHIFTS", testReturn),
        ];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleFetchPredefinedShifts);

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted("PREDEFINED_SHIFTS"),
        receiveFailed("PREDEFINED_SHIFTS", testError("No shift")),
      ];

      expect(response).toEqual(testError("No shift"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleClockIntoShift", () => {
    it("should clock the user into a specific shift", async () => {
      const specialTestReturn = [{ shift_start: 100, shift_end: 500 }];

      httpMock
        .onPut("self/shifts/5")
        .replyOnce(200, specialTestReturn)
        .onPut("self/shifts/5")
        .replyOnce(200, specialTestReturn)
        .onPut("self/shifts/5")
        .replyOnce(404, testError("No shift"));

      let response = await store.dispatch(
        handleClockIntoShift(
          {
            id: 5,
            start_time: 100.55,
            end_time: 1000.23,
            comment: "some comment",
          },
          store
        )
      );

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveStarted("SHIFT_NOW"),
          receiveSucceeded("SHIFT_END", 500000),
          receiveSucceeded("SHIFT_NOW", specialTestReturn[0]),
        ];

      expect(response).toEqual(specialTestReturn);
      expect(receivedActions).toEqual(expectedActions);
      expect(httpMock.history.put.length).toBe(1);
      expect(httpMock.history.put[0].data).toEqual(
        JSON.stringify({
          shift_start: 100,
          shift_end: 1000,
          comment: "some comment",
        })
      );

      store.clearActions();

      response = await store.dispatch(
        handleClockIntoShift({ id: 5, start_time: 100.55 }, store)
      );

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted("SHIFT_NOW"),
        receiveSucceeded("SHIFT_END", 500000),
        receiveSucceeded("SHIFT_NOW", specialTestReturn[0]),
      ];

      expect(response).toEqual(specialTestReturn);
      expect(receivedActions).toEqual(expectedActions);
      expect(httpMock.history.put.length).toBe(2);
      expect(httpMock.history.put[1].data).toEqual(
        JSON.stringify({ shift_start: 100 })
      );

      store.clearActions();

      response = await store.dispatch(
        handleClockIntoShift(
          { id: 5, start_time: 100.55, end_time: 1000.23 },
          store
        )
      );

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted("SHIFT_NOW"),
        receiveFailed("SET_SHIFT_NOW", testError("No shift")),
      ];

      expect(response).toEqual(testError("No shift"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleFetchFutureShift", () => {
    it("should fetch the user's future shift", async () => {
      const specialTestReturn = [
        { shift_start: Math.trunc(new Date("2100-01-01").valueOf() / 1000) },
        { shift_start: Math.trunc(new Date("2000-01-01").valueOf() / 1000) },
      ];

      httpMock
        .onGet("self/shifts")
        .replyOnce(200, specialTestReturn)
        .onGet("self/shifts")
        .replyOnce(200, [])
        .onGet("self/shifts")
        .replyOnce(404, testError("No shift"));

      let response = await store.dispatch(handleFetchFutureShift(store));

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveStarted("FUTURE_SHIFT"),
          receiveSucceeded("FUTURE_SHIFT", specialTestReturn[0]),
        ];

      expect(response).toEqual(specialTestReturn);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleFetchFutureShift(store));

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted("FUTURE_SHIFT"),
        receiveSucceeded("FUTURE_SHIFT", null),
      ];

      expect(response).toEqual([]);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleFetchFutureShift(store));

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted("FUTURE_SHIFT"),
        receiveFailed("FUTURE_SHIFT", testError("No shift")),
      ];

      expect(response).toEqual(testError("No shift"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleAcknowledgeShiftEnd", () => {
    it("should mark the user's shift ending alert as warned", async () => {
      httpMock.onPut("self/shifts/5").replyOnce(200, testReturn);

      let response = await store.dispatch(handleAcknowledgeShiftEnd(5));

      let receivedActions = store.getActions(),
        expectedActions = [];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);
      expect(httpMock.history.put.length).toBe(1);
      expect(httpMock.history.put[0].data).toEqual(
        JSON.stringify({ warned: 1 })
      );

      store.clearActions();
    });
  });

  describe("handleFetchTableFilters", () => {
    it("should fetch the user's saved table filters", async () => {
      httpMock
        .onGet("table-filters?area=jobs&viewing_all=0&grouping=1&stage_id=0")
        .replyOnce(200, testReturn.slice(0, 2))
        .onGet("table-filters?area=jobs&viewing_all=1&grouping=1&stage_id=0")
        .replyOnce(200, testReturn)
        .onGet("table-filters?area=myWork&viewing_all=1&grouping=1&stage_id=0")
        .replyOnce(200, testReturn)
        .onGet("table-filters?area=jobs&viewing_all=1&grouping=1&stage_id=0")
        .replyOnce(404, testError("No filters"));

      let response = await store.dispatch(handleFetchTableFilters("jobs", 0));

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveStarted("TABLE_FILTERS"),
          receiveSucceeded("TABLE_FILTERS", testReturn.slice(0, 2)),
        ];
      expect(response).toEqual(testReturn.slice(0, 2));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleFetchTableFilters("jobs", 1));

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted("TABLE_FILTERS"),
        receiveSucceeded("TABLE_FILTERS", testReturn),
      ];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleFetchTableFilters("jobs", 1));

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted("TABLE_FILTERS"),
        receiveFailed("TABLE_FILTERS", testError("No filters")),
      ];

      expect(response).toEqual(testError("No filters"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleSaveTableFilters", () => {
    it("should save the user's table filters", async () => {
      httpMock.onPost("table-filters").replyOnce(200, testReturn);

      let response = await store.dispatch(
        handleSaveTableFilters("jobs", testReturn.slice(2), 1)
      );

      let receivedActions = store.getActions(),
        expectedActions = [];

      expect(response).toEqual(undefined);
      expect(receivedActions).toEqual(expectedActions);
      expect(httpMock.history.post.length).toBe(1);
      expect(httpMock.history.post[0].data).toEqual(
        JSON.stringify({
          area: "jobs",
          viewing_all: 1,
          grouping: 1,
          data: testReturn.slice(2),
        })
      );

      store.clearActions();
    });
  });

  describe("handleReceiveFeatures", () => {
    it("should store the user's accessible features", () => {
      store.dispatch(handleReceiveFeatures(testReturn));

      let receivedActions = store.getActions(),
        expectedActions = [receiveSucceeded("FEATURES", testReturn)];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleCheckReleasesPageNotification", () => {
    it("should check for a releases page update", async () => {
      httpMock
        .onGet("self/releases-page-notification")
        .replyOnce(200, testReturn)
        .onGet("self/releases-page-notification")
        .replyOnce(404, testError("No update"));

      let response = await store.dispatch(handleCheckReleasesPageNotification);

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveSucceeded("RELEASES_PAGE_NOTIFICATION", testReturn),
        ];

      expect(response).toEqual(testReturn);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleCheckReleasesPageNotification);

      receivedActions = store.getActions();
      expectedActions = [
        receiveFailed("RELEASES_PAGE_NOTIFICATION", testError("No update")),
      ];

      expect(response).toEqual(testError("No update"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleFetchViewingAll", () => {
    it("should fetch the user's viewing_all status", async () => {
      const specialTestReturn = [{ viewing_all: true }];

      httpMock
        .onGet("self/table-view-settings")
        .replyOnce(200, specialTestReturn)
        .onGet("self/table-view-settings")
        .replyOnce(404, testError("Not found"));

      let response = await store.dispatch(handleFetchViewingAll);

      let receivedActions = store.getActions(),
        expectedActions = [receiveSucceeded("VIEWING_ALL", true)];

      expect(response).toEqual(specialTestReturn);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleFetchViewingAll);

      receivedActions = store.getActions();
      expectedActions = [];

      expect(response).toEqual(testError("Not found"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleFetchMyWorkTableViewSettings", () => {
    it("should fetch the user's table view settings for my work", async () => {
      const userSettingsReturn = {
        my_work_stage_id: 0,
        grouping_type_id: 1,
        my_work_viewing_all: 1,
      };

      httpMock
        .onGet(`self/table-view-settings`)
        .replyOnce(200, [userSettingsReturn]);

      let response = await store.dispatch(handleFetchMyWorkTableViewSettings);

      let receivedActions = store.getActions();
      let expectedActions = [
        receiveSucceeded("MY_WORK_VIEWING_ALL", 1),
        receiveSucceeded("MY_WORK_STAGE_ID", 0),
        receiveSucceeded("MY_WORK_GROUPING_TYPE", 1),
      ];

      expect(response).toEqual([userSettingsReturn]);
      expect(receivedActions).toEqual(expectedActions);
    });
  });

  describe("handleFetchPackagesPendingApprovalViewingAll", () => {
    it("should fetch the user's packages_pending_approval_viewing_all status", async () => {
      const specialTestReturn = [{ packages_pending_approval_viewing_all: 1 }];

      httpMock
        .onGet("self/table-view-settings")
        .replyOnce(200, specialTestReturn)
        .onGet("self/table-view-settings")
        .replyOnce(404, testError("Not found"));

      let response = await store.dispatch(
        handleFetchPackagesPendingApprovalViewingAll
      );

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveSucceeded("PACKAGES_PENDING_APPROVAL_VIEWING_ALL", 1),
        ];

      expect(response).toEqual(specialTestReturn);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(
        handleFetchPackagesPendingApprovalViewingAll
      );

      receivedActions = store.getActions();
      expectedActions = [];

      expect(response).toEqual(testError("Not found"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleFetchDrawingsPendingApprovalViewingAll", () => {
    it("should fetch the user's drawings_pending_approval_viewing_all status", async () => {
      const specialTestReturn = [{ drawings_pending_approval_viewing_all: 1 }];

      httpMock
        .onGet("self/table-view-settings")
        .replyOnce(200, specialTestReturn)
        .onGet("self/table-view-settings")
        .replyOnce(404, testError("Not found"));

      let response = await store.dispatch(
        handleFetchDrawingsPendingApprovalViewingAll
      );

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveSucceeded("DRAWINGS_PENDING_APPROVAL_VIEWING_ALL", 1),
        ];

      expect(response).toEqual(specialTestReturn);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(
        handleFetchDrawingsPendingApprovalViewingAll
      );

      receivedActions = store.getActions();
      expectedActions = [];

      expect(response).toEqual(testError("Not found"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  describe("handleUpdateTableViewSettings", () => {
    const userSettingsReturn = {
      my_work_stage_id: 0,
      grouping_type_id: 1,
      my_work_viewing_all: 1,
      packages_pending_approval_viewing_all: 1,
      drawings_pending_approval_viewing_all: 1,
    };
    it("should update viewing_all used in jobs", async () => {
      httpMock
        .onPut("/self/table-view-settings")
        .replyOnce(200, userSettingsReturn);

      await store.dispatch(handleUpdateTableViewSettings(1));

      let receivedActions = store.getActions();
      let expectedActions = [
        receiveStarted("TABLE_VIEW_SETTINGS"),
        receiveSucceeded("VIEWING_ALL", 1),
      ];

      expect(receivedActions).toEqual(expectedActions);
      expect(receivedActions[1].payload).toEqual(1);
    });
    it("should take in my work table settings to update in DB", async () => {
      // this action only updates the viewing_all state in redux but still takes in my work params related table-view-settings to update in DB

      httpMock
        .onPut("/self/table-view-settings")
        .replyOnce(200, userSettingsReturn);

      await store.dispatch(handleUpdateTableViewSettings(1, 1, 0, 1));

      let receivedActions = store.getActions();
      let expectedActions = [
        receiveStarted("TABLE_VIEW_SETTINGS"),
        receiveSucceeded("VIEWING_ALL", 1),
        receiveSucceeded("MY_WORK_VIEWING_ALL", 1),
        receiveSucceeded("MY_WORK_STAGE_ID", 0),
        receiveSucceeded("MY_WORK_GROUPING_TYPE", 1),
      ];

      expect(receivedActions).toEqual(expectedActions);
      expect(receivedActions[1].payload).toEqual(1);
      expect(receivedActions[2].payload).toEqual(1);
      expect(receivedActions[3].payload).toEqual(0);
      expect(receivedActions[4].payload).toEqual(1);
    });
    it("should update viewing_all used in packages pending approval", async () => {
      httpMock
        .onPut("/self/table-view-settings")
        .replyOnce(200, userSettingsReturn);

      await store.dispatch(
        handleUpdateTableViewSettings(null, null, null, null, 1)
      );

      let receivedActions = store.getActions();
      let expectedActions = [
        receiveStarted("TABLE_VIEW_SETTINGS"),
        receiveSucceeded("PACKAGES_PENDING_APPROVAL_VIEWING_ALL", 1),
      ];

      expect(receivedActions).toEqual(expectedActions);
      expect(receivedActions[1].payload).toEqual(1);
    });
  });

  describe("handleClearTableFilters", () => {
    it("should clear the table filters in redux", () => {
      store.dispatch(handleClearTableFilters());

      let receivedActions = store.getActions(),
        expectedActions = [receiveSucceeded("TABLE_FILTERS", null)];

      expect(receivedActions).toEqual(expectedActions);
    });
  });

  describe("handleFetchUserSettings", () => {
    const testUserSettings = [
      {
        home_page: "jobs",
        terms_viewed_on: 0,
        terms_version: 0,
        expand_menu: 0,
        release_page_view: "",
      },
    ];
    it("should return user settings", async () => {
      httpMock.onGet("/self/settings").replyOnce(200, testUserSettings);

      await store.dispatch(handleFetchUserSettings);

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveStarted("USER_SETTINGS"),
          receiveSucceeded("USER_SETTINGS", testUserSettings[0]),
        ];

      expect(receivedActions).toEqual(expectedActions);
    });
  });

  describe("handleFetchUserNotifications", () => {
    const actionType = "USER_NOTIFICATIONS";

    it("should return notifications with extra email_subscribed column", async () => {
      httpMock.onGet("/self/notifications").replyOnce(200, testNotifications);

      await store.dispatch(handleFetchUserNotifications);

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveStarted(actionType),
          receiveSucceeded(actionType, testNotifications),
        ];

      expect(receivedActions).toEqual(expectedActions);
    });

    it("should return error if user is not found", async () => {
      httpMock
        .onGet("/self/notifications")
        .replyOnce(404, testError("No notifications to view"));

      await store.dispatch(handleFetchUserNotifications);

      let receivedActions = store.getActions(),
        expectedActions = [
          receiveStarted(actionType),
          receiveFailed(actionType, testError("No notifications to view")),
        ];

      expect(receivedActions).toEqual(expectedActions);
    });
  });

  describe("handleUpdateNotificationSubscriptions", () => {
    const actionType = "NOTIFICATION_SUBSCRIPTIONS";
    const updatedNotifications = [
      { ...testNotifications[0], email_subscribed: 1 },
    ];

    it("should return updated notifications when assigning", async () => {
      httpMock
        .onPut("/self/notifications")
        .replyOnce(200, updatedNotifications);

      const response = await store.dispatch(
        handleUpdateNotificationSubscriptions("assign", "1")
      );

      let receivedActions = store.getActions(),
        expectedActions = [
          updateStarted(actionType),
          updateSucceeded(actionType, updatedNotifications),
        ];

      expect(receivedActions).toEqual(expectedActions);
      expect(response[0].email_subscribed).toEqual(1);
    });

    it("should return updated notifications when un-assigning", async () => {
      httpMock.onPut("/self/notifications").replyOnce(200, testNotifications);

      const response = await store.dispatch(
        handleUpdateNotificationSubscriptions("unassign", "1")
      );

      let receivedActions = store.getActions(),
        expectedActions = [
          updateStarted(actionType),
          updateSucceeded(actionType, testNotifications),
        ];

      expect(receivedActions).toEqual(expectedActions);
      expect(response[0].email_subscribed).toEqual(0);
    });

    it("should return error if no notifications are found", async () => {
      const message = "No valid notification(s) provided";

      httpMock.onPut("/self/notifications").replyOnce(400, testError(message));

      await store.dispatch(
        handleUpdateNotificationSubscriptions("unassign", "1000")
      );

      let receivedActions = store.getActions(),
        expectedActions = [
          updateStarted(actionType),
          updateFailed(actionType, testError(message)),
        ];

      expect(receivedActions).toEqual(expectedActions);
      store.clearActions();
    });
  });

  describe("PRODUCTS", () => {
    const type = "PRODUCTS";
    const testProducts = [
      { id: 1, name: "Hangerworks" },
      { id: 2, name: "BIMPro" },
    ];

    it("should return all active products", async () => {
      httpMock.onGet(`/products`).replyOnce(200, testProducts);

      const response = await store.dispatch(handleFetchProducts);
      let receivedActions = store.getActions();
      let expectedActions = [
        receiveStarted(type),
        receiveSucceeded(type, testProducts),
      ];

      expect(response).toEqual(testProducts);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  // describe("handleUpdateViewingAll", () => {
  //   it("should update the user's viewing_all status", async () => {
  //     httpMock
  //       .onPut("self/table-view-settings")
  //       .replyOnce(200, testReturn)
  //       .onPut("self/table-view-settings")
  //       .replyOnce(404, testError("Not found"));

  //     let response = await store.dispatch(handleUpdateViewingAll(1));

  //     let receivedActions = store.getActions(),
  //       expectedActions = [receiveSucceeded("VIEWING_ALL", 1)];

  //     expect(response).toEqual(testReturn);
  //     expect(receivedActions).toEqual(expectedActions);
  //     expect(httpMock.history.put.length).toBe(1);
  //     expect(httpMock.history.put[0].data).toEqual(
  //       JSON.stringify({ viewing_all: 1 })
  //     );

  //     store.clearActions();

  //     response = await store.dispatch(handleUpdateViewingAll(1));

  //     (receivedActions = store.getActions()), (expectedActions = []);

  //     expect(response).toEqual(testError("Not found"));
  //     expect(receivedActions).toEqual(expectedActions);

  //     store.clearActions();
  //   });
  // });
});
