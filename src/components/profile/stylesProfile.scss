@import "../styles/colors.scss";

div.profile-wrapper {
  height: calc(100vh - 60px - 50px - 33px);
  width: calc(100vw - 55px);
  box-sizing: border-box;
  overflow: auto;
  padding: 0 20px;

  & h3 {
    color: white;
  }

  & label {
    font-size: 0.8rem;
    display: flex;
    flex-direction: column;

    & span {
      margin-bottom: 5px;
    }
  }

  & input,
  select {
    height: 34px;
    font-size: 0.8rem;
  }
}

div.profile-form-wrapper {
  margin-top: 40px;

  p {
    font-size: 1.3rem;
    color: white;
  }
}

div.profile-form {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  max-width: 700px;

  & label.profile-label {
    color: white;
    font-size: 0.8rem;
    display: flex;
    flex-direction: column;

    & span {
      margin-bottom: 5px;
    }
  }

  & input,
  select {
    // height: 34px;
    // font-size: 0.8rem;
    width: 200px;
  }

  & label.profile-label button,
  label.profile-label a {
    background-color: $fabProBlue;
    color: white;
    height: 34px;
    font-size: 0.8rem;
    width: 200px;
    padding: 0;
  }

  & label.profile-label a {
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    border-radius: 3px;
  }
}

div.profile-photo-wrapper {
  margin-top: 50px;
  display: flex;
  gap: 30px;
  height: 150px;

  & img {
    border-radius: 50%;
    height: 150px;
    width: 150px;
  }

  & p {
    font-size: 1rem;
    display: flex;
    align-items: center;
  }

  input[type="file"] {
    display: none;
  }
  label[for="upload"] {
    cursor: pointer;
    height: 150px;
    width: 150px;
    border-radius: 50%;
    border: 3px solid white;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }
}

div.icon-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;

  & span {
    border-radius: 50%;
    height: 35px;
    width: 35px;
    background-color: #111;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  & span svg {
    color: $fabProBlue;
    height: 20px;

    & .disabled {
      opacity: 0.7;
      cursor: default;
    }
  }
}

div.button-row {
  display: flex;
  justify-content: space-around;
  padding: 10px 0;

  & button {
    height: 34px;
    font-size: 0.9rem;
    width: 120px;
    padding: 0;
    color: white;
    background-color: $fabProBlue;
    box-shadow: none;
  }

  & button.cancel {
    background-color: white;
    color: black;
  }
}

div.change-password-wrapper {
  width: 530px;
  padding: 30px 20px 0;

  & input {
    width: 500px;
  }

  & div.input-button-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;

    & svg {
      margin-top: 8px;
      font-size: 1.3rem;
      color: $fabProBlue;
      cursor: pointer;
    }
  }

  & div.button-row {
    justify-content: flex-end;
    gap: 10px;
    margin-top: 10px;

    & .cancel {
      border: 1px solid #ccc;
      background-color: #f9f9f9;
    }
  }

  & div.error-message-wrapper {
    height: 20px;
  }

  & p.error {
    color: $red;
    font-size: 0.8rem;
    font-weight: bold;
    margin: 0;
  }
}

div.password-tooltip {
  position: absolute;
  left: 102%;
  top: 20%;
  width: 250px;
  height: 200px;
  background-color: white;
  border-radius: 10px;
  padding: 5px;

  @media (max-width: 1200px) {
    top: 102%;
    left: 30%;
  }

  & p {
    font-size: 0.9rem;
    font-weight: bold;
  }

  & ul {
    padding-left: 20px;

    & li {
      font-size: 0.8rem;
      color: black;
      margin: 3px 3px 8px;
    }
  }
}
