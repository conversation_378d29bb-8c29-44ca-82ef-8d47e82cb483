// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";

import Input from "msuite_storybook/dist/input/Input";
import Select from "msuite_storybook/dist/select/Select";
import Toggle from "msuite_storybook/dist/toggle/Toggle";
import Button from "msuite_storybook/dist/button/Button";

import {
  FaTrashAlt,
  //FaPen,
  FaUpload,
} from "react-icons/fa";

// COMPONENT IMPORTS
import ConfirmationModal from "../reusable/confirmationModal/ConfirmationModal";
// import ImageCropper from "./ImageCropper";
import ChangePasswordModal from "./ChangePasswordModal";

// REDUX IMPORTS
import {
  handleFetchHomePageOptions,
  handleUpdateUserSettings,
  handleFetchUserSettings,
  handleUpdateProfilePhoto,
  handleUpdatePassword,
} from "./profileActions";
import { handleUpdateUserInfo } from "../users/usersActions";

// HELPER FUNCTION IMPORTS
// import { b64toBlob } from "../../_utils";
import { trackMixPanelEvent } from "../../utils/_mixPanelUtils";

// STYLE IMPORTS
import "./stylesProfile.scss";

const Profile = () => {
  const [username, setUsername] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [homePageId, setHomePageId] = useState(null);
  const [expandMenu, toggleExpandMenu] = useState(false);
  const [profilePhoto, setProfilePhoto] = useState(null);
  const [showDeleteConfirmation, toggleDeleteConfirmation] = useState(false);
  // disabled when moving to s3 file storage due to issues - see ImageCropper component for commentary
  // const [showCropper, toggleCropper] = useState(false);
  const [photoToUpload, setPhotoToUpload] = useState(null);
  const [displayedImageUrl, setDisplayedImageUrl] = useState(null);
  const [showPasswordModal, togglePasswordModal] = useState(false);

  const {
    userId,
    userInfo,
    userSettings,
    homePageOptions,
    isLoading,
  } = useSelector((state) => state.profileData);

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(handleFetchHomePageOptions);
    dispatch(handleFetchUserSettings);
  }, [dispatch]);

  useEffect(() => {
    if (!userInfo) return;

    const {
      username,
      first_name,
      last_name,
      email,
      phone_number,
      profile_photo,
    } = userInfo;
    username && setUsername(username);
    first_name && setFirstName(first_name);
    last_name && setLastName(last_name);
    setEmail(email || "");
    setPhone(phone_number || "");
    setProfilePhoto(profile_photo || null);
  }, [userInfo]);

  useEffect(() => {
    if (!userSettings || isLoading) return;

    const { expand_menu, home_page } = userSettings;
    expand_menu && toggleExpandMenu(expand_menu ? true : false);

    home_page &&
      homePageOptions?.length &&
      setHomePageId(
        homePageOptions.find((o) => o.url === home_page).id || null
      );
  }, [userSettings, homePageOptions, isLoading]);

  // displayed image logic
  useEffect(() => {
    if (isLoading) return;

    const showPhotoToUpload = photoToUpload?.file;
    if (!profilePhoto && !showPhotoToUpload) return setDisplayedImageUrl(null);

    setDisplayedImageUrl(
      showPhotoToUpload ? URL.createObjectURL(photoToUpload.file) : profilePhoto
    );
  }, [profilePhoto, photoToUpload, setDisplayedImageUrl, isLoading]);

  const displayedHomePageOptions = useMemo(() => {
    if (!homePageOptions) return [];

    return homePageOptions.map((o) => ({
      id: o.id,
      value: o.id,
      display: o.display_name,
    }));
  }, [homePageOptions]);

  // used in onUserInfoBlur to determine if values have changed
  const currentUserInfo = useMemo(() => {
    if (!userInfo || Object.keys(userInfo).length === 0) return null;

    const { username, first_name, last_name, email, phone_number } = userInfo;

    return {
      username,
      firstName: first_name,
      lastName: last_name,
      email: email || "",
      phone: phone_number || "",
    };
  }, [userInfo]);

  const onUserInfoBlur = () => {
    let newData = {
      username: username.trim(),
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      email: email && email.trim(),
      phone: phone && phone.trim(),
    };

    if (JSON.stringify(currentUserInfo) === JSON.stringify(newData)) return;

    dispatch(handleUpdateUserInfo(userId, newData));
  };

  const onHomePageBlur = () => {
    // check if current home page is different
    const newHomePage = homePageOptions.find(
      (o) => o.id === parseInt(homePageId)
    );
    if (userSettings.home_page === newHomePage?.url || !newHomePage) return;

    dispatch(handleUpdateUserSettings(null, homePageId));
    trackMixPanelEvent(
      `Profile Settings | ${newHomePage?.display_name}`,
      0,
      "Profile Settings",
      Date.now(),
      `Home Page Settings Updated to ${newHomePage?.display_name}`
    );
  };

  const handleExpandMenuToggle = () => {
    // whatever expandMenu is currently, we want to send the opposite since it was toggled
    const result = expandMenu ? 0 : 1;
    dispatch(handleUpdateUserSettings(result));
    toggleExpandMenu(expandMenu ? false : true);
  };

  // const handlePhotoCrop = async (url) => {
  //   const blob = await b64toBlob(url);
  //   const file = new File([blob], url, { type: blob.type });

  //   setPhotoToUpload({
  //     file: file,
  //   });
  // };

  const handleDelete = () => {
    if (photoToUpload?.file) {
      return setDisplayedImageUrl(profilePhoto ?? null);
    }

    toggleDeleteConfirmation(true);
  };

  const handleDeleteConfirmation = () => {
    toggleDeleteConfirmation(false);
    dispatch(handleUpdateProfilePhoto());
  };

  const handlePhotoUpload = () => {
    setPhotoToUpload(null);
    dispatch(handleUpdateProfilePhoto(photoToUpload?.file));
  };

  const handlePasswordSubmit = (password) => {
    dispatch(handleUpdatePassword(password));
    togglePasswordModal(false);
  };

  return (
    <div className="profile-wrapper">
      <h3>Profile</h3>
      <div className="profile-form-wrapper">
        <p>User Information</p>
        <div className="profile-form">
          <label className="profile-label">
            <span>User Name:</span>
            <Input
              placeholder="Username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              onBlur={onUserInfoBlur}
            />
          </label>

          <label className="profile-label">
            <span>First Name:</span>
            <Input
              placeholder="First Name"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              onBlur={onUserInfoBlur}
            />
          </label>

          <label className="profile-label">
            <span>Last Name:</span>
            <Input
              placeholder="Last Name"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              onBlur={onUserInfoBlur}
            />
          </label>

          <label className="profile-label">
            <span>Email:</span>
            <Input
              placeholder="Email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              onBlur={onUserInfoBlur}
            />
          </label>

          <label className="profile-label">
            <span>Phone Number:</span>
            <Input
              placeholder="Phone Number"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              onBlur={onUserInfoBlur}
            />
          </label>

          <label className="profile-label">
            <span>Home Page:</span>
            <Select
              options={displayedHomePageOptions}
              placeholder="Home Page"
              value={homePageId}
              onInput={(e) => setHomePageId(e.target.value)}
              onBlur={onHomePageBlur}
            />
          </label>

          <label className="profile-label">
            <span>Change Password</span>
            <Button onClick={() => togglePasswordModal(true)}>
              Change Password
            </Button>
          </label>

          <label className="profile-label">
            <span>Choose Notifications</span>
            <a href={`/notifications/`}>Choose Notifications</a>
          </label>

          <label className="profile-label">
            <span>Expand Menu:</span>
            <Toggle
              name="expand-menu-toggle"
              text={["yes", "no"]}
              defaultChecked={expandMenu}
              onToggleChanged={handleExpandMenuToggle}
            />
          </label>
        </div>
        <div className="profile-photo-wrapper">
          {displayedImageUrl ? (
            <img alt="Profile" src={displayedImageUrl} />
          ) : (
            <>
              <input
                onChange={(e) => {
                  e.persist();
                  setPhotoToUpload({
                    file: e.target.files[0],
                  });
                }}
                accept="image/*"
                type="file"
                id="upload"
              />
              <label htmlFor="upload">Upload Image</label>
            </>
          )}
          {displayedImageUrl && (
            <div className="icon-wrapper">
              <span>
                <FaTrashAlt onClick={handleDelete} />
              </span>
              {/* <span>
                <FaPen onClick={() => toggleCropper(true)} />
              </span> */}
              <span>
                <FaUpload onClick={handlePhotoUpload} />
              </span>
            </div>
          )}
        </div>
      </div>

      {showDeleteConfirmation && (
        <ConfirmationModal
          showModal={showDeleteConfirmation}
          handleClick={handleDeleteConfirmation}
          action="DELETE"
          toggleModal={toggleDeleteConfirmation}
          item="profile picture"
        />
      )}
      {/* {showCropper && (
        <ImageCropper
          onSubmit={handlePhotoCrop}
          imageURL={displayedImageUrl}
          showModal={showCropper}
          toggleModal={toggleCropper}
        />
      )} */}
      {showPasswordModal && (
        <ChangePasswordModal
          showModal={showPasswordModal}
          toggleModal={togglePasswordModal}
          onSubmit={handlePasswordSubmit}
          username={username}
        />
      )}
    </div>
  );
};

export default Profile;
