const initialState = {
  token: null,
  userId: null,
  userPrefs: {},
  userInfo: {},
  permissions: null,
  role: null,
  shiftEnd: null,
  partnerShifts: null,
  userSettings: null,
  systemSettings: null,
  zohoContactId: 1,
  features: null,
  shiftNow: null,
  lastShift: null,
  predefinedShifts: [],
  futureShift: null,
  tableFilters: null,
  releasesPageNotification: false,
  viewingAll: null,
  myWorkStageId: null,
  myWorkGroupingType: null,
  packagesPendingApprovalViewingAll: null,
  isLoading: false,
  error: null,
  homePageOptions: null,
  userNotifications: null,
  products: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_TOKEN_SUCCEEDED":
      return { ...state, token: payload };
    case "RECEIVE_USER_ID_SUCCEEDED":
      return { ...state, userId: payload };
    case "RECEIVE_USER_PREFS_JOBS_SUCCEEDED":
      return {
        ...state,
        userPrefs: { ...state.userPrefs, jobsColumnState: payload },
      };
    case "RECEIVE_USER_INFO_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_USER_INFO_SUCCEEDED":
      return { ...state, isLoading: false, error: null, userInfo: payload };
    case "RECEIVE_USER_INFO_FAILED":
      return { ...state, isLoading: false, error: payload, userInfo: null };
    case "RECEIVE_CURRENT_USER_PERMISSIONS_SUCCEEDED":
      return {
        ...state,
        permissions: payload,
      };
    case "RECEIVE_ROLE_SUCCEEDED":
      return { ...state, role: payload };
    case "RECEIVE_SHIFT_END_SUCCEEDED":
      return { ...state, shiftEnd: payload };
    case "RECEIVE_PARTNER_SHIFTS_SUCCEEDED":
      return { ...state, partnerShifts: payload };
    case "RECEIVE_USER_SETTINGS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_USER_SETTINGS_SUCCEEDED":
      return { ...state, isLoading: false, userSettings: payload };
    case "RECEIVE_USER_SETTINGS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_SYSTEM_SETTINGS_SUCCEEDED":
      return { ...state, systemSettings: payload };
    case "RECEIVE_ZOHO_CONTACT_ID_SUCCEEDED":
      return { ...state, zohoContactId: payload };
    case "RECEIVE_FEATURES_SUCCEEDED":
      return { ...state, features: payload };
    case "RECEIVE_SHIFT_NOW_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_SHIFT_NOW_SUCCEEDED":
      return { ...state, isLoading: false, error: null, shiftNow: payload };
    case "RECEIVE_SHIFT_NOW_FAILED":
      return { ...state, isLoading: false, error: payload, shiftNow: null };
    case "RECEIVE_SET_SHIFT_NOW_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_LAST_SHIFT_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_LAST_SHIFT_SUCCEEDED":
      return { ...state, isLoading: false, error: null, lastShift: payload };
    case "RECEIVE_LAST_SHIFT_FAILED":
      return { ...state, isLoading: false, error: payload, lastShift: null };
    case "RECEIVE_PREDEFINED_SHIFTS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_PREDEFINED_SHIFTS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        predefinedShifts: payload,
      };
    case "RECEIVE_PREDEFINED_SHIFTS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        predefinedShifts: [],
      };
    case "RECEIVE_FUTURE_SHIFT_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_FUTURE_SHIFT_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        futureShift: payload,
      };
    case "RECEIVE_FUTURE_SHIFT_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        futureShift: null,
      };
    case "RECEIVE_TABLE_FILTERS_STARTED":
      return {
        ...state,
        isLoading: true,
        error: null,
        tableFilters: null,
      };
    case "RECEIVE_TABLE_FILTERS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        tableFilters: payload,
      };
    case "RECEIVE_TABLE_FILTERS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        tableFilters: [],
      };
    case "RECEIVE_MY_WORK_TABLE_FILTERS_STARTED":
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case "RECEIVE_RELEASES_PAGE_NOTIFICATION_SUCCEEDED":
      return { ...state, releasesPageNotification: true };
    case "RECEIVE_RELEASES_PAGE_NOTIFICATION_FAILED":
      return { ...state, releasesPageNotification: false };
    case "RECEIVE_TABLE_VIEW_SETTINGS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_TABLE_VIEW_SETTINGS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_VIEWING_ALL_SUCCEEDED":
      return { ...state, isLoading: false, viewingAll: payload };
    case "RECEIVE_MY_WORK_STAGE_ID_SUCCEEDED":
      return { ...state, isLoading: false, myWorkStageId: payload };
    case "RECEIVE_MY_WORK_GROUPING_TYPE_SUCCEEDED":
      return { ...state, isLoading: false, myWorkGroupingType: payload };
    case "RECEIVE_PACKAGES_PENDING_APPROVAL_VIEWING_ALL_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        packagesPendingApprovalViewingAll: payload,
      };
    case "RECEIVE_HOME_PAGE_OPTIONS_SUCCEEDED":
      return { ...state, homePageOptions: payload };
    case "RECEIVE_USER_NOTIFICATIONS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_USER_NOTIFICATIONS_SUCCEEDED":
      return { ...state, isLoading: false, userNotifications: payload };
    case "RECEIVE_USER_NOTIFICATIONS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "UPDATE_NOTIFICATION_SUBSCRIPTIONS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "UPDATE_NOTIFICATION_SUBSCRIPTIONS_SUCCEEDED":
      return { ...state, isLoading: false, userNotifications: payload };
    case "UPDATE_NOTIFICATION_SUBSCRIPTIONS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_PRODUCTS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_PRODUCTS_SUCCEEDED":
      return { ...state, isLoading: false, products: payload };
    case "RECEIVE_PRODUCTS_FAILED":
      return { ...state, isLoading: false, error: payload };
    default:
      return state;
  }
}
