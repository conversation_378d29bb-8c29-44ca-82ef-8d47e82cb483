import {
  checkRegularShift,
  checkPartnerShifts,
  fetchUserInformation,
  fetchShiftNow,
  createShiftNow,
  endShiftNow,
  extendShift,
  fetchLastShift,
  fetchPredefinedShifts,
  clockIntoShift,
  fetchFutureShift,
  acknowledgeShiftEnd,
  fetchTableFilters,
  saveTableFilters,
  checkReleasesPageNotification,
  updateTableViewSettings,
  fetchTableViewSettings,
  fetchSystemSettings,
  fetchHomePageOptions,
  fetchUserSettings,
  updateUserSettings,
  updateProfilePhoto,
  deleteProfilePhoto,
  fetchUserNotifications,
  updateNotificationSubscriptions,
  updatePassword,
  fetchProducts,
} from "../../_services";
import { generateTime } from "../../_utils";
import { trackMixPanelEvent } from "../../utils/_mixPanelUtils";
import store from "../../redux/store";

export const receiveStarted = (type) => ({
  type: `RECEIVE_${type}_STARTED`,
});
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const updateStarted = (type) => ({ type: `UPDATE_${type}_STARTED` });
export const updateSucceeded = (type, payload) => ({
  type: `UPDATE_${type}_SUCCEEDED`,
  payload,
});
export const updateFailed = (type, error) => ({
  type: `UPDATE_${type}_FAILED`,
  payload: error,
});

export const handleReceiveUserPrefs = ({ jobsColumnState }) => (dispatch) => {
  if (jobsColumnState)
    return dispatch(receiveSucceeded("USER_PREFS_JOBS", jobsColumnState));
};
export const handleReceiveToken = (token) => (dispatch) =>
  dispatch(receiveSucceeded("TOKEN", token));
export const handleReceiveUserId = (userId) => (dispatch) =>
  dispatch(receiveSucceeded("USER_ID", userId));
export const handleReceivePermissions = (permissions) => (dispatch) => {
  dispatch(receiveSucceeded("CURRENT_USER_PERMISSIONS", permissions));
};
export const handleReceiveZohoContactId = (zohoContactId) => (dispatch) =>
  dispatch(receiveSucceeded("ZOHO_CONTACT_ID", zohoContactId));
export const handleReceiveRole = (roleID) => (dispatch) =>
  dispatch(receiveSucceeded("ROLE", roleID));
export const handleCheckRegularShift = (dispatch) => {
  return checkRegularShift().then((res) => {
    if (res.error && res.error.status && res.error.status === 404)
      dispatch(receiveSucceeded("SHIFT_END", res));
    else if (!res.error)
      dispatch(receiveSucceeded("SHIFT_END", res.shift_end * 1000));

    return res;
  });
};
export const handleCheckPartnerShifts = (dispatch) => {
  return checkPartnerShifts().then((res) => {
    dispatch(receiveSucceeded("PARTNER_SHIFTS", res));
    return res;
  });
};
export const handleReceiveUserSettings = (userSettings) => (dispatch) =>
  dispatch(receiveSucceeded("USER_SETTINGS", userSettings));
export const handleFetchUserInfo = (id) => (dispatch) => {
  dispatch(receiveStarted("USER_INFO"));
  return fetchUserInformation().then((res) => {
    if (res.error) dispatch(receiveFailed("USER_INFO", res));
    else dispatch(receiveSucceeded("USER_INFO", res));

    return res;
  });
};

export const handleFetchShiftNow = (dispatch) => {
  const type = "SHIFT_NOW";

  dispatch(receiveStarted(type));
  return fetchShiftNow().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};
export const handleClockIn = (
  shift = null,
  testWindow = null,
  testStore = null
) => (dispatch) => {
  dispatch(receiveStarted("SHIFT_NOW"));

  const mixPanelEventStart = Date.now();

  return createShiftNow(shift).then((res) => {
    if (res.error) dispatch(receiveFailed("SET_SHIFT_NOW", res));
    else {
      const { username } = (testStore || store).getState().profileData.userInfo;
      const { userId } = (testStore || store).getState().profileData;
      let shiftType = shift && shift.id ? "Predefined" : "Custom";
      (testWindow || window).ChurnZero.push([
        "trackEvent",
        "FP Shift Created",
        shiftType,
        1,
        {
          Product: "FabPro",
          SubGroup: "Shifts",
          Version: process.env.REACT_APP_ENVIRONMENT,
          UserName: username,
          UserId: userId,
        },
      ]);
      trackMixPanelEvent(
        "Shift Started",
        res.length,
        "Shifts",
        mixPanelEventStart,
        shiftType
      );

      dispatch(receiveSucceeded("SHIFT_NOW", res[0]));
    }

    if (
      shift &&
      generateTime(
        shift.start_time * 1000,
        false,
        true,
        null,
        testStore,
        true
      ) > Date.now()
    )
      dispatch(receiveSucceeded("FUTURE_SHIFT", res[0]));

    return res;
  });
};
export const handleClockOut = (shiftId) => (dispatch) => {
  // dont need to track duration here
  trackMixPanelEvent("Shift Ended", 1, "Shifts");

  return endShiftNow(shiftId);
};

export const handleExtendShift = (shiftId, endTime) => (dispatch) => {
  dispatch(receiveStarted("SHIFT_NOW"));

  return extendShift(shiftId, endTime).then((res) => {
    if (res.error) dispatch(receiveFailed("SET_SHIFT_NOW", res));
    else {
      dispatch(receiveSucceeded("SHIFT_NOW", res[0]));
      dispatch(
        receiveSucceeded(
          "SHIFT_END",
          (res[0].shift_start + res[0].expected_duration) * 1000
        )
      );
    }

    return res;
  });
};

export const handleFetchLastShift = (dispatch) => {
  dispatch(receiveStarted("LAST_SHIFT"));
  return fetchLastShift().then((res) => {
    if (res.error) dispatch(receiveFailed("LAST_SHIFT", res));
    else dispatch(receiveSucceeded("LAST_SHIFT", res[0]));

    return res;
  });
};

export const handleFetchPredefinedShifts = (dispatch) => {
  const type = "PREDEFINED_SHIFTS";

  dispatch(receiveStarted(type));
  return fetchPredefinedShifts().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleClockIntoShift = (shift, testStore = null) => (dispatch) => {
  dispatch(receiveStarted("SHIFT_NOW"));
  return clockIntoShift(shift).then((res) => {
    if (res.error) dispatch(receiveFailed("SET_SHIFT_NOW", res));
    else if (
      generateTime(res[0].shift_start, false, true, null, testStore, true) <=
      Date.now()
    ) {
      dispatch(receiveSucceeded("SHIFT_END", res[0].shift_end * 1000));
      dispatch(receiveSucceeded("SHIFT_NOW", res[0]));
    }

    return res;
  });
};

export const handleFetchFutureShift = (testStore = null) => (dispatch) => {
  dispatch(receiveStarted("FUTURE_SHIFT"));
  return fetchFutureShift().then((res) => {
    if (res.error) dispatch(receiveFailed("FUTURE_SHIFT", res));
    else {
      const futureShift = res.find(
        (s) =>
          generateTime(
            s.shift_start * 1000,
            false,
            true,
            null,
            testStore,
            true
          ) -
            Date.now() >=
          3600000
      );

      dispatch(receiveSucceeded("FUTURE_SHIFT", futureShift || null));
    }

    return res;
  });
};

export const handleAcknowledgeShiftEnd = (shiftId) => (dispatch) => {
  return acknowledgeShiftEnd(shiftId);
};

export const handleClearTableFilters = (type = "") => (dispatch) => {
  dispatch(receiveSucceeded(`${type ? type + "_" : ""}TABLE_FILTERS`, null));
};

export const handleFetchTableFilters = (
  area,
  viewingAll,
  groupingType,
  stageId
) => (dispatch) => {
  let type = "TABLE_FILTERS";

  dispatch(receiveStarted(type));
  return fetchTableFilters(area, viewingAll, groupingType, stageId).then(
    (res) => {
      let tableFiltersToSave = res;
      if (res.error) dispatch(receiveFailed(type, res));
      else {
        tableFiltersToSave = tableFiltersToSave.map((p) => ({
          ...p,
          item_ids_array:
            p.item_ids?.split(",").map((n) => Number.parseInt(n)) ?? [],
        }));

        dispatch(receiveSucceeded(type, tableFiltersToSave));
      }

      return tableFiltersToSave;
    }
  );
};

export const handleSaveTableFilters = (
  area,
  filters,
  viewingAll,
  grouping,
  stage_id,
  drawing_id,
  selectAll
) => (dispatch) => {
  let type = "TABLE_FILTERS";

  const tableFiltersToSave = filters.map((f) => {
    delete f.item_ids_array;
    return f;
  });

  // dispatch(receiveStarted(type));
  return saveTableFilters(
    area,
    tableFiltersToSave,
    viewingAll,
    grouping,
    stage_id,
    drawing_id,
    selectAll
  ).then((res) => {
    if (area !== "myWork") return;
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleReceiveFeatures = (features) => (dispatch) =>
  dispatch(receiveSucceeded("FEATURES", features));

export const handleCheckReleasesPageNotification = (dispatch) => {
  const type = "RELEASES_PAGE_NOTIFICATION";

  return checkReleasesPageNotification().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchViewingAll = (dispatch) => {
  const type = "VIEWING_ALL";
  return fetchTableViewSettings().then((res) => {
    if (!res.error)
      dispatch(receiveSucceeded(type, res[0] ? res[0].viewing_all : false));

    return res;
  });
};

export const handleFetchMyWorkTableViewSettings = (dispatch) => {
  return fetchTableViewSettings().then((res) => {
    if (!res.error) {
      dispatch(
        receiveSucceeded(
          "MY_WORK_VIEWING_ALL",
          res[0] ? res[0].my_work_viewing_all : null
        )
      );
      dispatch(
        receiveSucceeded(
          "MY_WORK_STAGE_ID",
          res[0] ? res[0].my_work_stage_id : null
        )
      );
      dispatch(
        receiveSucceeded(
          "MY_WORK_GROUPING_TYPE",
          res[0] ? res[0].grouping_type_id : null
        )
      );
    }

    return res;
  });
};

export const handleFetchPackagesPendingApprovalViewingAll = (dispatch) => {
  const type = "PACKAGES_PENDING_APPROVAL_VIEWING_ALL";
  return fetchTableViewSettings().then((res) => {
    if (!res.error)
      dispatch(
        receiveSucceeded(
          type,
          res[0] ? res[0].packages_pending_approval_viewing_all : false
        )
      );

    return res;
  });
};

export const handleFetchDrawingsPendingApprovalViewingAll = (dispatch) => {
  const type = "DRAWINGS_PENDING_APPROVAL_VIEWING_ALL";
  return fetchTableViewSettings().then((res) => {
    if (!res.error)
      dispatch(
        receiveSucceeded(
          type,
          res[0] ? res[0].drawings_pending_approval_viewing_all : false
        )
      );

    return res;
  });
};

export const handleUpdateTableViewSettings = (
  viewingAll = null,
  myWorkViewingAll = null,
  myWorkStageId = null,
  myWorkGroupingType = null,
  packagesPendingApprovalViewingAll = null,
  drawingsPendingApprovalViewingAll = null
) => (dispatch) => {
  return updateTableViewSettings(
    viewingAll,
    myWorkViewingAll,
    myWorkStageId,
    myWorkGroupingType,
    packagesPendingApprovalViewingAll,
    drawingsPendingApprovalViewingAll
  ).then((res) => {
    dispatch(receiveStarted("TABLE_VIEW_SETTINGS"));
    if (!res.error) {
      viewingAll !== null &&
        dispatch(receiveSucceeded("VIEWING_ALL", viewingAll));
      myWorkViewingAll !== null &&
        dispatch(receiveSucceeded("MY_WORK_VIEWING_ALL", myWorkViewingAll));
      myWorkStageId !== null &&
        dispatch(receiveSucceeded("MY_WORK_STAGE_ID", myWorkStageId));
      myWorkGroupingType !== null &&
        dispatch(receiveSucceeded("MY_WORK_GROUPING_TYPE", myWorkGroupingType));
      packagesPendingApprovalViewingAll !== null &&
        dispatch(
          receiveSucceeded(
            "PACKAGES_PENDING_APPROVAL_VIEWING_ALL",
            packagesPendingApprovalViewingAll
          )
        );
    } else {
      dispatch(receiveFailed("TABLE_VIEW_SETTINGS"));
    }
  });
};

export const handleFetchSystemSettings = (zohoContactId, isTest) => (
  dispatch
) => {
  return fetchSystemSettings().then((res) => {
    if (!res.error) {
      if (!isTest) {
        window.ChurnZero.push([
          "setContact",
          res[0].zoho_account_id,
          zohoContactId,
        ]);
      }
      dispatch(receiveSucceeded("SYSTEM_SETTINGS", res[0]));
    }
  });
};

export const handleFetchHomePageOptions = (dispatch) => {
  return fetchHomePageOptions().then((res) => {
    if (!res.error) {
      dispatch(receiveSucceeded("HOME_PAGE_OPTIONS", res));
    }
  });
};

export const handleFetchUserSettings = (dispatch) => {
  dispatch(receiveStarted("USER_SETTINGS"));
  return fetchUserSettings().then((res) => {
    if (res.error) dispatch(receiveFailed("USER_SETTINGS", res));
    else dispatch(receiveSucceeded("USER_SETTINGS", res[0]));

    return res;
  });
};

export const handleUpdateUserSettings = (expandMenu, homePageId) => (
  dispatch
) => {
  return updateUserSettings(expandMenu, homePageId).then((res) => {
    return fetchUserSettings().then((res) => {
      if (!res.error) dispatch(receiveSucceeded("USER_SETTINGS", res[0]));

      return res;
    });
  });
};

export const handleUpdateProfilePhoto = (file) => (dispatch) => {
  dispatch(receiveStarted("USER_INFO"));
  // delete photo
  if (!file) {
    return deleteProfilePhoto().then((res) => {
      if (!res.error) dispatch(receiveSucceeded("USER_INFO", res[0]));
      else dispatch(receiveFailed("USER_INFO", res));

      return res;
    });
  }

  // upload
  return updateProfilePhoto(file).then((res) => {
    if (!res.error) dispatch(receiveSucceeded("USER_INFO", res[0]));
    else dispatch(receiveFailed("USER_INFO", res));

    return res;
  });
};

export const handleFetchUserNotifications = (dispatch) => {
  const type = "USER_NOTIFICATIONS";
  dispatch(receiveStarted(type));

  return fetchUserNotifications().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleUpdateNotificationSubscriptions = (
  action,
  notificationIds
) => (dispatch) => {
  const type = "NOTIFICATION_SUBSCRIPTIONS";
  dispatch(updateStarted(type));

  return updateNotificationSubscriptions(action, notificationIds).then(
    (res) => {
      if (res.error) dispatch(updateFailed(type, res));
      else dispatch(updateSucceeded(type, res));

      return res;
    }
  );
};

export const handleUpdatePassword = (password) => (dispatch) => {
  const type = "PASSWORD";
  dispatch(updateStarted(type));

  return updatePassword(password).then((res) => {
    if (res.error) dispatch(updateFailed(type, res));
    else dispatch(updateSucceeded(type, res));
  });
};

export const handleFetchProducts = (dispatch) => {
  const type = "PRODUCTS";
  dispatch(receiveStarted(type));

  return fetchProducts().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};
