// NPM PACKAGE IMPORTS
import React, { useState, useRef } from "react";
import Button from "msuite_storybook/dist/button/Button";
import Cropper from "react-cropper";
import "cropperjs/dist/cropper.css";

// COMPONENT IMPORTS
import Modal from "../reusable/modal/Modal";

const ImageCropper = ({ imageURL, showModal, toggleModal, onSubmit }) => {
  const [croppedImg, setCroppedImg] = useState("");

  const cropperRef = useRef(null);

  const onCrop = () => {
    const imageElement = cropperRef?.current;
    const cropper = imageElement?.cropper;
    // ToDo: This library does not support when files are hosted
    // outside of our app... need a app that supports CORS properly...
    // https://github.com/fengyuanchen/cropperjs/issues/413
    setCroppedImg(cropper.getCroppedCanvas().toDataURL());
  };

  const handleSubmit = () => {
    onSubmit && typeof onSubmit === "function" && onSubmit(croppedImg);
    toggleModal(false);
  };

  return (
    <Modal
      showModal={showModal}
      onClose={toggleModal}
      onSubmit={onSubmit}
      dark={true}
    >
      <Cropper
        src={imageURL}
        style={{ height: "70vh", width: "70vw" }}
        crop={onCrop}
        ref={cropperRef}
        minCropBoxHeight={100}
        minCanvasWidth={100}
        initialAspectRatio={16 / 9}
        viewMode={1}
        responsive={true}
        autoCropArea={true}
        aspectRatio={4 / 3}
        checkOrientation={false}
        checkCrossOrigin={false}
      />
      <div className="button-row">
        <Button className="cancel" onClick={() => toggleModal(false)}>
          Cancel
        </Button>
        <Button onClick={handleSubmit}>Submit</Button>
      </div>
    </Modal>
  );
};

export default ImageCropper;
