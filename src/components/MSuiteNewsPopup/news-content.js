import React from "react";

/**
 * Just add more news content in here with a start date and it will automatically work
 */
const newsContent = [
  {
    name: "cloud-spooling",
    startDate: new Date("10/01/2024").getTime(),
    endDate: new Date("01/01/2025").getTime(),
    content: (
      <>
        <p>
          <strong>Introducing Cloud Spooling</strong> - the revolutionary new
          way to create and view Spool Drawings without needing to be a Revit
          power user or have a Revit license. With Cloud Spooling, your VDC
          teams can focus on more important detailing tasks, while reducing the
          strain a Revit model puts on your system. In addition, access your BIM
          Models anytime, anywhere with MSUITE's 3D Model Viewer, and enjoy
          interactive 3D Spool Sheets and 3D Model viewer options. Try Cloud
          Spooling for free with our upcoming trial period, starting on
          10.17.2024 and ending on 12.31.2024.{" "}
        </p>
        <p>
          Visit{" "}
          <a
            href="https://support.msuite.com/portal/en/kb/articles/cloud-spooling-in-fab-4-6-2024"
            target="_blank"
          >
            https://support.msuite.com/portal/en/kb/articles/cloud-spooling-in-fab-4-6-2024
          </a>{" "}
          for more information on how to use Cloud Spooling and take advantage
          of all its benefits. New users to the Knowledge Base will need to
          create an account to access the articles.
        </p>
      </>
    ),
  },
];

export default newsContent;
