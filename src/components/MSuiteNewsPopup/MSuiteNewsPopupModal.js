import Modal from "../reusable/modal/Modal";
import React, { useState } from "react";
import styled from "styled-components";

const ModalBody = styled.div`
  text-align: left;
  margin: 20px;
  max-width: 700px;
`;

export const MSuiteNewsPopupModal = ({ children, onDismiss }) => {
  const [visible, setVisible] = useState(true);

  if (!visible) return null;

  const onClosHandler = () => {
    setVisible(false);
    return onDismiss();
  };

  return (
    <Modal suppressForm>
      <div class="general-modal-content-wrapper">
        <p className="sub-modalTitle">MSuite News</p>
        <ModalBody>{children}</ModalBody>
      </div>
      <footer>
        <span>
          <button onClick={onClosHandler}>Close</button>
        </span>
      </footer>
    </Modal>
  );
};
