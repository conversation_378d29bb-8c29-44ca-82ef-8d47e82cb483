import React, { useRef, useState } from "react";
import { MSuiteNewsPopupModal } from "./MSuiteNewsPopupModal";
import { useLocalStorageForUser } from "../../hooks/useLocalStorageForUser";
import newsContent from "./news-content";
import useMount from "../../hooks/useMount";

/**
 * Component for showing a one time news popup.
 * Uses the news content from news-content.js file, add new items in there.
 * Stores view history for user in local storage so user only sees the news once.
 */
export const MSuiteNewsPopup = () => {
  const [viewHistory, setViewHistory] = useLocalStorageForUser(
    "msuite-news-popup-history",
    {}
  );

  const currentNewsItem = useRef(null);

  const now = new Date().getTime();

  // Just look for new news on the first mount
  useMount(() => {
    for (const item of newsContent) {
      if (item.startDate > now) continue;
      else if (item.endDate < now) continue;
      else if (item?.name in viewHistory) continue;
      else {
        currentNewsItem.current = item;
        break;
      }
    }
  });

  const onDismissHandler = () => {
    setViewHistory((prevState) => {
      return {
        ...prevState,
        [currentNewsItem.current?.name]: now,
      };
    });
    currentNewsItem.current = null;
  };

  if (!currentNewsItem.current) return null;

  return (
    <MSuiteNewsPopupModal onDismiss={onDismissHandler}>
      {currentNewsItem.current?.content}
    </MSuiteNewsPopupModal>
  );
};
