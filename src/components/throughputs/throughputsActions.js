import {
  fetchThroughputs,
  fetchColumnsForConditions,
  fetchThroughputColumns,
  updateThroughputColumns,
  updateThroughputs,
} from "../../_services";

const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const handleFetchThroughputs = (flowId, stageId) => (dispatch) => {
  const type = "THROUGHPUTS";

  dispatch(receiveStarted(type));
  return fetchThroughputs(flowId, stageId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchColumnsForConditions = (stageId) => (dispatch) => {
  const type = "COLUMNS_FOR_CONDITIONS";
  return fetchColumnsForConditions(stageId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else
      dispatch(
        receiveSucceeded(
          type,
          stageId ? (res[0] ? res[0].work_item_columns : []) : res
        )
      );

    return res;
  });
};

export const handleFetchThroughputColumns = (stageId) => (dispatch) => {
  const type = "THROUGHPUT_COLUMNS";

  return fetchThroughputColumns(stageId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleUpdateThroughputColumns = (stageId, workItemColumnIds) => (
  dispatch
) => {
  return updateThroughputColumns(stageId, workItemColumnIds);
};

export const handleUpdateThroughputs = (ids, staticCost, dynamicStartDate) => (
  dispatch
) => {
  return updateThroughputs(ids, staticCost, dynamicStartDate);
};

export const resetReducer = (dispatch) => {
  dispatch(receiveSucceeded("THROUGHPUT_MATERIALS", []));
  dispatch(receiveSucceeded("THROUGHPUTS", null));
  dispatch(receiveSucceeded("COLUMNS_FOR_CONDITIONS", null));
  dispatch(receiveSucceeded("THROUGHPUT_COLUMNS", null));
};
