@import "../styles/colors.scss";
@import "../styles/sizes.scss";

div.throughputs {
  display: grid;
  grid-template-columns: 0px 200px 200px 200px 1fr 120px;
  column-gap: 10px;
  padding: 5px 0 0;
  align-items: end;

  & > label.filter-by {
    color: $lighterSlate;
    font-size: 0.9rem;
    width: 200px;
    grid-column: 2/3;

    display: grid;
    row-gap: 5px;

    &:first-of-type {
      grid-column: 2/3;
    }

    &:last-of-type {
      grid-column: 3/4;
    }

    & > div {
      height: 30px;
      width: 200px;

      & > div {
        height: 30px;

        & > select {
          height: 30px;
          font-size: 1rem;
        }
      }
    }
  }

  & > div.search-wrapper {
    display: grid;
    grid-template-columns: 30px 1fr;
    justify-items: center;
    align-items: center;

    grid-column: 4/5;
    border-radius: 3px;
    background-color: $fabProBlue;

    & > svg {
      color: #fff;
      border: 1px solid #333;
      border-right: 1px solid transparent;
      padding: 6px;
      border-top-left-radius: 3px;
      border-bottom-left-radius: 3px;
    }

    & > div {
      height: 30px;

      & > input {
        height: 30px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        font-size: 1rem;
        border-color: #333;
      }
    }
  }

  & > div.columns-wrapper {
    grid-column: 5/6;

    & > div.selectable-filter-wrapper {
      & > div.filter-button {
        height: 30px;

        & > p {
          font-size: 1rem;
        }
      }
    }

    & > p {
      height: 30px;
      font-size: 1rem;
      padding: 0;
      margin: 0;
      line-height: 30px;
      color: $lighterSlate;
    }
  }

  & > div.table-wrapper {
    grid-column: 1/-1;

    & > div.ag-theme-balham-dark.custom-ag-styles {
      height: calc(100vh - 200px);

      @supports (-webkit-touch-callout: none) {
        height: calc(100vh - #{$headerFooter} - #{$iosAddressBar} - 90px);
      }

      & div.ag-cell {
        min-height: 30px;
      }
    }
  }

  & > div.mass-update-button {
    grid-column: 6/-1;

    & > button {
      width: 110px;
      padding: 0;
      font-size: 0.8rem;
      height: 30px;
      background-color: transparent;
      color: $fabProBlue;
      border: 1px solid $fabProBlue;

      display: flex;
      align-items: center;
      justify-content: center;
      column-gap: 5px;
    }

    & > button:not(:disabled):hover {
      background-color: lighten($darkGrey, 10%);
      cursor: pointer;
    }
  }
}

div.mass-update-wrapper {
  display: grid;
  grid-template-rows: 30px 1fr;

  width: 400px;
  background-color: #fff;

  & > h2.title {
    font-size: 0.8rem;
    background-color: $fabProBlue;
    color: #fff;
    margin: 0;
    padding: 0 10px;
    line-height: 30px;
  }

  & > div.content {
    padding: 10px;

    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 10px;
    justify-items: center;
  }

  & > div.content label {
    grid-column: 1/-1;
    padding-bottom: 10px;
  }

  & > div.content label > div {
    height: 30px;
  }

  & > div.content label > div > input {
    height: 30px;
  }

  & > div.content button {
    padding: 0 10px;
    font-size: 0.8rem;
    height: 30px;
    width: 100px;
    border: 1px solid #333;

    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  & > div.content button.cancel {
    background-color: #fff;
    color: #333;

    &:hover {
      background-color: darken(#fff, 10%);
    }
  }

  & > div.content button.submit {
    background-color: $green;
    color: #fff;

    &:hover {
      background-color: darken($green, 10%);
    }
  }
}
