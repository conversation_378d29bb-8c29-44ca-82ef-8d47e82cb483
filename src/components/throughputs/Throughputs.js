// NPM PACKAGE IMPORTS
import React, { useEffect, useMemo, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import Select from "msuite_storybook/dist/select/Select";
import Input from "msuite_storybook/dist/input/Input";
import Button from "msuite_storybook/dist/button/Button";
import Modal from "msuite_storybook/dist/modal/Modal";
import { BiSearch } from "react-icons/bi";
import { FaArrowRight } from "react-icons/fa";
import { FiLayers } from "react-icons/fi";

// REDUX IMPORTS
import { handleSetPageTitle } from "../../redux/generalActions";
import { handleFetchWorkStages, handleFetchFlows } from "../flows/flowsActions";
import {
  handleFetchThroughputs,
  handleFetchColumnsForConditions,
  handleFetchThroughputColumns,
  handleUpdateThroughputs,
  resetReducer,
} from "./throughputsActions";

// COMPONENT IMPORTS
import AgTable from "../reusable/agTable/AgTable";
import TextInputCellRenderer from "../reusable/frameworkComponents/TextInputCellRenderer";
import DueDateEditorRenderer from "../reusable/frameworkComponents/DueDateEditorRenderer";

// HELPER FUNCTION IMPORTS
import { rowClassRules, onSortChanged } from "../../_utils";

// TRANSLATIONS IMPORTS
import useTranslations from "../../hooks/useTranslations";
import throughputsTranslations from "./throughputsTranslations.json";

// CONSTANTS IMPORTS
import { columnDefs } from "./throughputsConstants";

// STYLE IMPORTS
import "./stylesThroughputs.scss";
import "../styles/tables.scss";

const Throughputs = () => {
  const [selectedFlow, setSelectedFlow] = useState(null);
  const [selectedStage, setSelectedStage] = useState(null);
  const [searchValue, setSearchValue] = useState("");
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [selectedRows, setSelectedRows] = useState([]);
  const [massUpdate, toggleMassUpdate] = useState(false);
  const [newStaticCost, setNewStaticCost] = useState(0);
  const [displayedThroughputs, setDisplayedThroughputs] = useState(null);
  const [throughputsRefreshTimer, toggleThroughputsRefreshTimer] = useState(
    false
  );

  const translate = useTranslations(throughputsTranslations);
  const dispatch = useDispatch();

  const { flows, workStages } = useSelector((state) => state.flowsData);
  const { throughputs, throughputColumns } = useSelector(
    (state) => state.throughputsData
  );
  const { permissions, userSettings } = useSelector(
    (state) => state.profileData
  );

  useEffect(() => {
    if (!permissions.length) return;

    // redirect to homepage if user doesn't have permission to view settings
    if (!permissions.includes(62)) {
      const homePage = userSettings?.home_page || "jobs";
      window.location.assign(`${process.env.REACT_APP_FABPRO}/${homePage}`);
    }
  }, [permissions, userSettings]);

  useEffect(() => {
    if (throughputs) setDisplayedThroughputs(throughputs);
  }, [throughputs]);

  useEffect(() => {
    if (!selectedStage || !selectedFlow) return;

    const id = JSON.parse(selectedStage).id;
    const timer = setTimeout(() => {
      dispatch(handleFetchThroughputs(selectedFlow, id));
      toggleThroughputsRefreshTimer(false);
    }, 120000);

    if (!throughputsRefreshTimer) return clearTimeout(timer);
  }, [throughputsRefreshTimer, selectedFlow, selectedStage, dispatch]);

  useEffect(() => {
    dispatch(handleSetPageTitle(translate("Throughputs")));
    dispatch(handleFetchFlows());

    return () => dispatch(resetReducer);
  }, []);

  useEffect(() => {
    if (selectedFlow) {
      dispatch(handleFetchWorkStages(false, null, null, null, selectedFlow));
      toggleThroughputsRefreshTimer(false);
    }
  }, [selectedFlow]);

  useEffect(() => {
    if (selectedStage) {
      const id = JSON.parse(selectedStage).id;
      dispatch(handleFetchThroughputs(selectedFlow, id));
      dispatch(handleFetchThroughputColumns(id));
      dispatch(handleFetchColumnsForConditions(id));
      toggleThroughputsRefreshTimer(false);
    }
  }, [selectedStage]);

  useEffect(() => {
    if (gridOptionsApi && throughputColumns) {
      gridOptionsApi.setColumnDefs(columnDefs(throughputColumns));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [throughputColumns, gridOptionsApi]);

  useEffect(() => {
    if (!gridOptionsApi) return;

    if (displayedThroughputs) gridOptionsApi.setRowData(displayedThroughputs);
    else gridOptionsApi.setRowData([]);
  }, [gridOptionsApi, displayedThroughputs]);

  useEffect(() => {
    if (gridOptionsApi) {
      gridOptionsApi.setQuickFilter(searchValue);
    }
  }, [gridOptionsApi, searchValue]);

  const performMassUpdate = useCallback(() => {
    dispatch(
      handleUpdateThroughputs([selectedRows.map((r) => r.id)], newStaticCost)
    ).then((res) => {
      if (!res.error) {
        selectedRows.forEach((r) => {
          const node = gridOptionsApi.getRowNode(r.id);
          if (node) {
            node.setData({ ...r, static: newStaticCost });
          }
        });
      }
    });
  }, [newStaticCost, selectedRows]);

  const displayedFlows = useMemo(() => {
    return flows
      .map((f) => ({ id: f.id, value: f.id, display: f.name }))
      .sort((a, b) =>
        a.display.toUpperCase() > b.display.toUpperCase() ? 1 : -1
      );
  }, [flows]);

  const displayedStages = useMemo(() => {
    return workStages
      .map((ws) => ({
        id: ws.id,
        value: JSON.stringify(ws),
        display: `${ws.name} - ${ws.metric_work_item_column_name}`,
      }))
      .sort((a, b) =>
        a.display.toUpperCase() > b.display.toUpperCase() ? 1 : -1
      );
  }, [workStages]);

  const onCellValueChanged = (params) => {
    let staticCost = null,
      dynamicStartDate = null;

    if (params.colDef.headerName === "Dynamic Start Date")
      dynamicStartDate = params.newValue;
    if (/Static Cost/i.test(params.colDef.headerName))
      staticCost = params.newValue;

    dispatch(
      handleUpdateThroughputs([params.data.id], staticCost, dynamicStartDate)
    );
  };

  const onSelectionChanged = (params) => {
    let rows = params.api.getSelectedRows();
    return setSelectedRows(rows);
  };

  const gridOptions = {
    rowData: displayedThroughputs,
    columnDefs: columnDefs(throughputColumns),
    rowClassRules,
    onSortChanged,
    onGridReady: (params) => setGridOptionsApi(params.api),
    frameworkComponents: {
      textInputCellRenderer: TextInputCellRenderer,
      dueDateEditorRenderer: DueDateEditorRenderer,
    },
    defaultColDef: {
      cellClass: "custom-wrap",
      minWidth: 120,
      wrapText: true,
    },
    suppressRowClickSelection: true,
    onCellValueChanged,
    onSelectionChanged,
    getRowNodeId: (data) => data.id,
  };

  return (
    <div className="throughputs">
      <label className="filter-by">
        {translate("Filter by Flow:")}
        <Select
          options={displayedFlows}
          value={selectedFlow}
          onInput={(e) => {
            setSelectedFlow(e.target.value);
            setDisplayedThroughputs(null);
            setSelectedStage(null);
          }}
          placeholder={translate("-- Select a Flow --")}
        />
      </label>
      <label className="filter-by">
        {selectedFlow ? (
          <>
            {translate("Filter by Stage:")}
            <Select
              options={displayedStages}
              value={selectedStage}
              onInput={(e) => {
                setSelectedStage(e.target.value);
              }}
              placeholder={translate("-- Select a Stage --")}
            />
          </>
        ) : (
          <></>
        )}
      </label>
      <div className="search-wrapper">
        <BiSearch />
        <Input
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          placeholder={translate("Search")}
        />
      </div>
      <div className="mass-update-button">
        <Button
          disabled={!selectedRows.length}
          onClick={() => toggleMassUpdate(true)}
        >
          <FiLayers />
          {translate("Mass Update")}
        </Button>
      </div>
      <div className="table-wrapper">
        {throughputColumns && throughputColumns.length && throughputs ? (
          <AgTable gridOptions={gridOptions} />
        ) : (
          <></>
        )}
      </div>
      {massUpdate && (
        <Modal open={massUpdate} handleClose={() => toggleMassUpdate(false)}>
          <div className="mass-update-wrapper">
            <h2 className="title">Mass Update</h2>
            <div className="content">
              <label>
                New Static Cost
                <Input
                  type="number"
                  min="0"
                  onChange={(e) => setNewStaticCost(parseFloat(e.target.value))}
                  value={newStaticCost}
                />
              </label>
              <Button
                className="cancel"
                onClick={() => toggleMassUpdate(false)}
              >
                Cancel
              </Button>
              <Button
                className="submit"
                onClick={() => {
                  performMassUpdate();
                  toggleMassUpdate(false);
                }}
                disabled={isNaN(newStaticCost)}
              >
                Continue <FaArrowRight />
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default Throughputs;
