export const columnDefs = (columns) => {
  const objectColumns = [
    "length",
    "bend_deduct",
    "bend_dim_a",
    "bend_dim_b",
    "bend_dim_c",
    "bend_dim_d",
    "bend_dim_e",
    "bend_dim_f",
    "bend_dim_g",
    "bend_mark_1",
    "bend_mark_2",
  ];

  const decimalColumns = [
    "stock_length",
    "height",
    "width",
    "thickness",
    "gauge",
    "hanger_size",
    "insulation_area",
    "random_length",
    "rod_size",
    "support_rod_length",
    "support_rod_length_2",
    "identifier",
    "weight",
    "bend_angle_1",
    "bend_angle_2",
  ];

  return [
    {
      headerName: "",
      headerCheckboxSelection: true,
      headerCheckboxSelectionFilteredOnly: true,
      width: 40,
      maxWidth: 40,
      minWidth: 40,
      checkboxSelection: true,
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      pinned: "left",
      colId: "checkbox",
    },
    ...(columns || []).map((c) => ({
      headerName: c.display_name,
      field:
        c.normal_name === "material_name"
          ? "material_type_name"
          : c.normal_name,
      valueGetter: (params) => {
        if (objectColumns.includes(c.normal_name)) {
          return params.data[c.normal_name].display;
        } else if (c.normal_name === "is_cut") {
          return params.data[c.normal_name] === 1 ? "Yes" : "No";
        } else if (c.normal_name === "material_name") {
          return params.data.material_type_name;
        } else {
          return params.data[c.normal_name];
        }
      },
      getQuickFilterText: (params) => params.colDef.valueGetter(params),
      comparator: (valueA, valueB, nodeA, nodeB, isInverted) => {
        if (c.normal_name === "size" || c.normal_name === "is_cut") {
          if (!valueA) return -1;
          if (!valueB) return 1;
          return valueA.localeCompare(valueB, undefined, {
            numeric: true,
            sensitivity: "base",
          });
        }
        return objectColumns.includes(c.normal_name)
          ? nodeA.data[c.normal_name].decimal -
              nodeB.data[c.normal_name].decimal
          : decimalColumns.includes(c.normal_name)
          ? valueA - valueB
          : (valueA || "").toLowerCase() > (valueB || "").toLowerCase()
          ? 1
          : -1;
      },
      filter: decimalColumns.includes(c.normal_name)
        ? "agNumberColumnFilter"
        : "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      autoHeight: true,
      minWidth: 120,
      maxWidth: 140,
      colId: c.normal_name,
    })),
    {
      headerName: "Hours Done",
      field: "total_time",
      valueFormatter: (params) =>
        params.value ? (params.value / 3600).toFixed(1) : "",
      getQuickFilterText: (params) => params.colDef.valueFormatter(params),
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      minWidth: 120,
      maxWidth: 140,
      colId: "total_time",
    },
    {
      headerName: "Static Cost (units/hr)",
      field: "static",
      valueFormatter: (params) => params.value || "-",
      getQuickFilterText: (params) => params.colDef.valueFormatter(params),
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        if (/^\d{0,}((\.\d{1,})|$)/.test(params.newValue))
          return params.newValue;
        else return params.oldValue;
      },
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      editable: true,
      minWidth: 120,
      maxWidth: 140,
      colId: "static",
    },
    {
      headerName: "Dynamic Cost (units/hr)",
      field: "dynamic",
      valueFormatter: (params) =>
        params.value ? params.value.toFixed(2) : "-",
      getQuickFilterText: (params) => params.colDef.valueFormatter(params),
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      minWidth: 120,
      maxWidth: 140,
      colId: "dynamic",
    },
    // {
    //   headerName: "Dynamic Start Date",
    //   field: "dynamic_start_date",
    //   valueFormatter: params =>
    //     params.value
    //       ? typeof params.value === "number"
    //         ? generateTime(params.value * 1000)
    //         : params.value
    //       : "-",
    //   valueParser: params => {
    //     if (params.newValue === "" || params.newValue === undefined)
    //       return null;

    //     const defaultRegex = new RegExp("\\d{2}-\\d{2}-\\d{4}");
    //     const dateFormattingRegex = dateFormatting
    //       ? /\-/.test(dateFormatting)
    //         ? new RegExp(
    //             dateFormatting
    //               .split("-")
    //               .map(part => `\\d{${part.length}}`)
    //               .join("\\-")
    //           )
    //         : /\//.test(dateFormatting)
    //         ? new RegExp(
    //             dateFormatting
    //               .split("/")
    //               .map(part => `\\d{${part.length}}`)
    //               .join("\\/")
    //           )
    //         : defaultRegex
    //       : defaultRegex;

    //     if (!dateFormattingRegex.test(params.newValue)) {
    //       return params.oldValue;
    //     } else return Math.floor(new Date(params.newValue).getTime() / 1000);
    //   },
    //   getQuickFilterText: params => params.colDef.valueFormatter(params),
    //   filter: "agTextColumnFilter",
    //   cellEditor: "dueDateEditorRenderer",
    //   menuTabs: ["filterMenuTab"],
    //   editable: true,
    //   minWidth: 120,
    //   maxWidth: 140
    // },
    {
      headerName: "Throughput Type",
      field: "dynamic_flag",
      valueFormatter: (params) => (params.value === 1 ? "Dynamic" : "Static"),
      getQuickFilterText: (params) => params.colDef.valueFormatter(params),
      filter: "agSetColumnFilter",
      filterParams: {
        suppressMiniFilter: true,
        valueFormatter: (params) =>
          parseInt(params.value) === 1 ? "Dynamic" : "Static",
      },
      menuTabs: ["filterMenuTab"],
      minWidth: 120,
      maxWidth: 140,
      colId: "dynamic_flag",
    },
  ];
};
