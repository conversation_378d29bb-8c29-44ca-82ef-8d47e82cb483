import { columnDefs } from "./throughputsConstants";

describe("Throughputs", () => {
  const defaultHeaders = [
    "",
    "End Prep 1",
    "Length",
    "Height",
    "Width",
    "Thickness",
    "Paint Spec",
    "Texture",
    "Fixture",
    "Gauge",
    "Weight",
    "Hanger Size",
    "Insulation",
    "Insulation Area",
    "Insulation Spec",
    "Insulation Gauge",
    "Joining Procedure",
    "Material Name",
    "Service",
    "Size",
    "Area",
    "Random Length",
    "Rod Size",
    "Support Rod Length 1",
    "Support Rod Length 2",
    "Liner Spec",
    "Is Cut",
    "Hours Done",
    "Static Cost (units/hr)",
    "Dynamic Cost (units/hr)",
    "Throughput Type",
  ];

  const testColumns = [
    {
      id: 15,
      display_name: "End Prep 1",
      name: "end_prep_1",
      normal_name: "end_prep_1",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 19,
      display_name: "Length",
      name: "rounded_cut_length",
      normal_name: "length",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 1,
      group_by: 0,
    },
    {
      id: 22,
      display_name: "Height",
      name: "height",
      normal_name: "height",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 23,
      display_name: "Width",
      name: "width",
      normal_name: "width",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 24,
      display_name: "Thickness",
      name: "thickness",
      normal_name: "thickness",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 25,
      display_name: "Paint Spec",
      name: "paint_spec",
      normal_name: "paint_spec",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 26,
      display_name: "Texture",
      name: "texture",
      normal_name: "texture",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 27,
      display_name: "Fixture",
      name: "fixture_type",
      normal_name: "fixture_type",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 28,
      display_name: "Gauge",
      name: "gauge",
      normal_name: "gauge",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 29,
      display_name: "Weight",
      name: "weight",
      normal_name: "weight",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 30,
      display_name: "Hanger Size",
      name: "hanger_size",
      normal_name: "hanger_size",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 32,
      display_name: "Insulation",
      name: "insulation",
      normal_name: "insulation",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 33,
      display_name: "Insulation Area",
      name: "insulation_area",
      normal_name: "insulation_area",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 34,
      display_name: "Insulation Spec",
      name: "insulation_specification",
      normal_name: "insulation_specification",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 35,
      display_name: "Insulation Gauge",
      name: "insulation_gauge",
      normal_name: "insulation_gauge",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 36,
      display_name: "Joining Procedure",
      name: "name",
      normal_name: "joining_procedure_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "joining_procedures",
      editable: 1,
      groupable: 1,
      group_by: 0,
    },
    {
      id: 37,
      display_name: "Material Name",
      name: "name",
      normal_name: "material_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "material_types",
      editable: 1,
      groupable: 1,
      group_by: 1,
    },
    {
      id: 38,
      display_name: "Service",
      name: "service_name",
      normal_name: "service_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 41,
      display_name: "Size",
      name: "size",
      normal_name: "size",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 1,
      group_by: 0,
    },
    {
      id: 42,
      display_name: "Area",
      name: "area",
      normal_name: "area",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 43,
      display_name: "Random Length",
      name: "random_length",
      normal_name: "random_length",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 44,
      display_name: "Rod Size",
      name: "rod_size",
      normal_name: "rod_size",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 45,
      display_name: "Support Rod Length 1",
      name: "support_rod_length",
      normal_name: "support_rod_length",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 46,
      display_name: "Support Rod Length 2",
      name: "support_rod_length_2",
      normal_name: "support_rod_length_2",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 47,
      display_name: "Liner Spec",
      name: "liner_spec",
      normal_name: "liner_spec",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
      group_by: 0,
    },
    {
      id: 54,
      display_name: "Is Cut",
      name: "is_cut",
      normal_name: "is_cut",
      data_type: "integer",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "material_types",
      editable: 0,
      groupable: 1,
      group_by: 0,
    },
  ];

  describe("Column Defs", () => {
    let populatedColumns;

    beforeEach(() => {
      populatedColumns = columnDefs(testColumns);
    });

    it("Items headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);

      expect(columnHeaders).toEqual(defaultHeaders);
    });

    describe("END PREP 1", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "End Prep 1");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            end_prep_1: "test prep",
          },
          value: "test prep",
        };
        expect(column.valueGetter(params)).toEqual("test prep");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            end_prep_1: "test prep",
          },
          value: "test prep",
        };
        expect(column.getQuickFilterText(params)).toEqual("test prep");
      });
      it("comparator", () => {
        const values = ["test prep 1", "test prep 2", "prep test 1"];
        expect(values.sort(column.comparator)).toEqual([
          "prep test 1",
          "test prep 1",
          "test prep 2",
        ]);
      });
    });

    describe("LENGTH", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Length");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            length: {
              decimal: 10,
              display: "10",
            },
          },
          value: {
            decimal: 10,
            display: "10",
          },
        };
        expect(column.valueGetter(params)).toEqual("10");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            length: {
              decimal: 10,
              display: "10",
            },
          },
          value: {
            decimal: 10,
            display: "10",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("10");
      });
      it("Comparator", () => {
        const values = [
          {
            data: {
              length: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              length: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
          {
            data: {
              length: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
        ];
        expect(
          values.sort((a, b) => column.comparator(null, null, a, b))
        ).toEqual([
          {
            data: {
              length: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              length: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
          {
            data: {
              length: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
        ]);
      });
    });

    describe("WIDTH", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Width");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            width: 12,
          },
          value: 12,
        };
        expect(column.valueGetter(params)).toEqual(12);
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            width: 12,
          },
          value: 12,
        };
        expect(column.getQuickFilterText(params)).toEqual(12);
      });
      it("comparator", () => {
        const values = [10, 10.5, 8, 12];
        expect(values.sort(column.comparator)).toEqual([8, 10, 10.5, 12]);
      });
    });

    describe("THICKNESS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Thickness");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            thickness: 12,
          },
          value: 12,
        };
        expect(column.valueGetter(params)).toEqual(12);
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            thickness: 12,
          },
          value: 12,
        };
        expect(column.getQuickFilterText(params)).toEqual(12);
      });
      it("comparator", () => {
        const values = [10, 10.5, 8, 12];
        expect(values.sort(column.comparator)).toEqual([8, 10, 10.5, 12]);
      });
    });

    describe("PAINT SPEC", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Paint Spec");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            paint_spec: "test spec",
          },
          value: "test spec",
        };
        expect(column.valueGetter(params)).toEqual("test spec");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            paint_spec: "test spec",
          },
          value: "test spec",
        };
        expect(column.getQuickFilterText(params)).toEqual("test spec");
      });
      it("comparator", () => {
        const values = ["test spec", "aaaa", "zzzz"];
        expect(values.sort(column.comparator)).toEqual([
          "aaaa",
          "test spec",
          "zzzz",
        ]);
      });
    });

    describe("TEXTURE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Texture");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            texture: "test texture",
          },
          value: "test texture",
        };
        expect(column.valueGetter(params)).toEqual("test texture");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            texture: "test texture",
          },
          value: "test texture",
        };
        expect(column.getQuickFilterText(params)).toEqual("test texture");
      });
      it("comparator", () => {
        const values = ["test texture", "aaaa", "zzzz"];
        expect(values.sort(column.comparator)).toEqual([
          "aaaa",
          "test texture",
          "zzzz",
        ]);
      });
    });

    describe("FIXTURE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Fixture");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            fixture_type: "test fixture",
          },
          value: "test fixture",
        };
        expect(column.valueGetter(params)).toEqual("test fixture");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            fixture_type: "test fixture",
          },
          value: "test fixture",
        };
        expect(column.getQuickFilterText(params)).toEqual("test fixture");
      });
      it("comparator", () => {
        const values = ["test fixture", "aaaa", "zzzz"];
        expect(values.sort(column.comparator)).toEqual([
          "aaaa",
          "test fixture",
          "zzzz",
        ]);
      });
    });

    describe("GAUGE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Gauge");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            gauge: 12,
          },
          value: 12,
        };
        expect(column.valueGetter(params)).toEqual(12);
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            gauge: 12,
          },
          value: 12,
        };
        expect(column.getQuickFilterText(params)).toEqual(12);
      });
      it("comparator", () => {
        const values = [10, 10.5, 8, 12];
        expect(values.sort(column.comparator)).toEqual([8, 10, 10.5, 12]);
      });
    });

    describe("WEIGHT", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Weight");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            weight: 12,
          },
          value: 12,
        };
        expect(column.valueGetter(params)).toEqual(12);
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            weight: 12,
          },
          value: 12,
        };
        expect(column.getQuickFilterText(params)).toEqual(12);
      });
      it("comparator", () => {
        const values = [10, 10.5, 8, 12];
        expect(values.sort(column.comparator)).toEqual([8, 10, 10.5, 12]);
      });
    });

    describe("HANGER SIZE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Hanger Size");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            hanger_size: 12,
          },
          value: 12,
        };
        expect(column.valueGetter(params)).toEqual(12);
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            hanger_size: 12,
          },
          value: 12,
        };
        expect(column.getQuickFilterText(params)).toEqual(12);
      });
      it("comparator", () => {
        const values = [10, 10.5, 8, 12];
        expect(values.sort(column.comparator)).toEqual([8, 10, 10.5, 12]);
      });
    });

    describe("INSULATION", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Insulation");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            insulation: "test insulation",
          },
          value: "test insulation",
        };
        expect(column.valueGetter(params)).toEqual("test insulation");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            insulation: "test insulation",
          },
          value: "test insulation",
        };
        expect(column.getQuickFilterText(params)).toEqual("test insulation");
      });
      it("comparator", () => {
        const values = ["test insulation", "aaaa", "zzzz"];
        expect(values.sort(column.comparator)).toEqual([
          "aaaa",
          "test insulation",
          "zzzz",
        ]);
      });
    });

    describe("INSULATION AREA", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Insulation Area"
        );
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            insulation_area: 12,
          },
          value: 12,
        };
        expect(column.valueGetter(params)).toEqual(12);
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            insulation_area: 12,
          },
          value: 12,
        };
        expect(column.getQuickFilterText(params)).toEqual(12);
      });
      it("comparator", () => {
        const values = [10, 10.5, 8, 12];
        expect(values.sort(column.comparator)).toEqual([8, 10, 10.5, 12]);
      });
    });

    describe("INSULATION SPEC", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Insulation Spec"
        );
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            insulation_specification: "test insulation",
          },
          value: "test insulation",
        };
        expect(column.valueGetter(params)).toEqual("test insulation");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            insulation_specification: "test insulation",
          },
          value: "test insulation",
        };
        expect(column.getQuickFilterText(params)).toEqual("test insulation");
      });
      it("comparator", () => {
        const values = ["test insulation", "aaaa", "zzzz"];
        expect(values.sort(column.comparator)).toEqual([
          "aaaa",
          "test insulation",
          "zzzz",
        ]);
      });
    });

    describe("INSULATION GAUGE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Insulation Gauge"
        );
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            insulation_gauge: "test insulation",
          },
          value: "test insulation",
        };
        expect(column.valueGetter(params)).toEqual("test insulation");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            insulation_gauge: "test insulation",
          },
          value: "test insulation",
        };
        expect(column.getQuickFilterText(params)).toEqual("test insulation");
      });
      it("comparator", () => {
        const values = ["test insulation", "aaaa", "zzzz"];
        expect(values.sort(column.comparator)).toEqual([
          "aaaa",
          "test insulation",
          "zzzz",
        ]);
      });
    });

    describe("JOINING PROCEDURE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Joining Procedure"
        );
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            joining_procedure_name: "test jp",
          },
          value: "test jp",
        };
        expect(column.valueGetter(params)).toEqual("test jp");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            joining_procedure_name: "test jp",
          },
          value: "test jp",
        };
        expect(column.getQuickFilterText(params)).toEqual("test jp");
      });
      it("comparator", () => {
        const values = ["test jp", "aaaa", "zzzz"];
        expect(values.sort(column.comparator)).toEqual([
          "aaaa",
          "test jp",
          "zzzz",
        ]);
      });
    });

    describe("MATERIAL NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Material Name");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            material_type_name: "test mat",
          },
          value: "test mat",
        };
        expect(column.valueGetter(params)).toEqual("test mat");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            material_type_name: "test mat",
          },
          value: "test mat",
        };
        expect(column.getQuickFilterText(params)).toEqual("test mat");
      });
      it("comparator", () => {
        const values = ["test mat", "aaaa", "zzzz"];
        expect(values.sort(column.comparator)).toEqual([
          "aaaa",
          "test mat",
          "zzzz",
        ]);
      });
    });

    describe("SERVICE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Service");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            service_name: "test",
          },
          value: "test",
        };
        expect(column.valueGetter(params)).toEqual("test");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            service_name: "test",
          },
          value: "test",
        };
        expect(column.getQuickFilterText(params)).toEqual("test");
      });
      it("comparator", () => {
        const values = ["test", "aaaa", "zzzz"];
        expect(values.sort(column.comparator)).toEqual([
          "aaaa",
          "test",
          "zzzz",
        ]);
      });
    });

    describe("SIZE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Size");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            size: `12 1/2"`,
          },
          value: `12 1/2"`,
        };
        expect(column.valueGetter(params)).toEqual(`12 1/2"`);
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            size: `12 1/2"`,
          },
          value: `12 1/2"`,
        };
        expect(column.getQuickFilterText(params)).toEqual(`12 1/2"`);
      });
      it("comparator", () => {
        const values = [`12 1/2"`, "10", "1"];
        expect(values.sort(column.comparator)).toEqual(["1", "10", `12 1/2"`]);
      });
    });

    describe("AREA", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Area");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            area: "test",
          },
          value: "test",
        };
        expect(column.valueGetter(params)).toEqual("test");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            area: "test",
          },
          value: "test",
        };
        expect(column.getQuickFilterText(params)).toEqual("test");
      });
      it("comparator", () => {
        const values = ["test", "aaaa", "zzzz"];
        expect(values.sort(column.comparator)).toEqual([
          "aaaa",
          "test",
          "zzzz",
        ]);
      });
    });

    describe("RANDOM LENGTH", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Random Length");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            random_length: 11,
          },
          value: 11,
        };
        expect(column.valueGetter(params)).toEqual(11);
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            random_length: 11,
          },
          value: 11,
        };
        expect(column.getQuickFilterText(params)).toEqual(11);
      });
      it("comparator", () => {
        const values = [11, 10, 1];
        expect(values.sort(column.comparator)).toEqual([1, 10, 11]);
      });
    });

    describe("ROD SIZE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Rod Size");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            rod_size: 11,
          },
          value: 11,
        };
        expect(column.valueGetter(params)).toEqual(11);
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            rod_size: 11,
          },
          value: 11,
        };
        expect(column.getQuickFilterText(params)).toEqual(11);
      });
      it("comparator", () => {
        const values = [11, 10, 1];
        expect(values.sort(column.comparator)).toEqual([1, 10, 11]);
      });
    });

    describe("SUPPORT ROD LENGTH 1", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Support Rod Length 1"
        );
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            support_rod_length: 11,
          },
          value: 11,
        };
        expect(column.valueGetter(params)).toEqual(11);
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            support_rod_length: 11,
          },
          value: 11,
        };
        expect(column.getQuickFilterText(params)).toEqual(11);
      });
      it("comparator", () => {
        const values = [11, 10, 1];
        expect(values.sort(column.comparator)).toEqual([1, 10, 11]);
      });
    });

    describe("SUPPORT ROD LENGTH 2", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Support Rod Length 2"
        );
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            support_rod_length_2: 11,
          },
          value: 11,
        };
        expect(column.valueGetter(params)).toEqual(11);
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            support_rod_length_2: 11,
          },
          value: 11,
        };
        expect(column.getQuickFilterText(params)).toEqual(11);
      });
      it("comparator", () => {
        const values = [11, 10, 1];
        expect(values.sort(column.comparator)).toEqual([1, 10, 11]);
      });
    });

    describe("LINER SPEC", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Liner Spec");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            liner_spec: "test",
          },
          value: "test",
        };
        expect(column.valueGetter(params)).toEqual("test");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            liner_spec: "test",
          },
          value: "test",
        };
        expect(column.getQuickFilterText(params)).toEqual("test");
      });
      it("comparator", () => {
        const values = ["test", "aaaa", "zzzz"];
        expect(values.sort(column.comparator)).toEqual([
          "aaaa",
          "test",
          "zzzz",
        ]);
      });
    });

    describe("IS CUT", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Is Cut");
      });

      it("valueGetter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            is_cut: 1,
          },
          value: 1,
        };
        expect(column.valueGetter(params)).toEqual("Yes");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            is_cut: 1,
          },
          value: 1,
        };
        expect(column.getQuickFilterText(params)).toEqual("Yes");
      });
      it("comparator", () => {
        const values = [1, 0, 0];
        expect(values.sort(column.comparator)).toEqual([0, 0, 1]);
      });
    });

    describe("HOURS DONE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Hours Done");
      });

      it("valueFormatter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            hours_done: 3600,
          },
          value: 3600,
        };
        expect(column.valueFormatter(params)).toEqual("1.0");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            hours_done: 3600,
          },
          value: 3600,
        };
        expect(column.getQuickFilterText(params)).toEqual("1.0");
      });
    });

    describe("STATIC COST", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Static Cost (units/hr)"
        );
      });
      it("valueFormatter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            static_cost: 100,
          },
          value: 100,
        };
        expect(column.valueFormatter(params)).toEqual(100);
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            static_cost: 100,
          },
          value: 100,
        };
        expect(column.getQuickFilterText(params)).toEqual(100);
      });
      it("valueParser", () => {
        const params = {
          oldValue: 100,
          newValue: 200,
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: 100,
          newValue: "hello",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual(200);
        expect(column.valueParser(badParams)).toEqual(100);
      });
    });

    describe("DYNAMIC COST", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Dynamic Cost (units/hr)"
        );
      });

      it("valueFormatter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            dynamic_cost: 100,
          },
          value: 100,
        };
        expect(column.valueFormatter(params)).toEqual("100.00");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            dynamic_cost: 100,
          },
          value: 100,
        };
        expect(column.getQuickFilterText(params)).toEqual("100.00");
      });
    });

    describe("THROUGHPUT TYPE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Throughput Type"
        );
      });

      it("valueFormatter", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            throughput_type: 1,
          },
          value: 1,
        };
        expect(column.valueFormatter(params)).toEqual("Dynamic");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          data: {
            throughput_type: 1,
          },
          value: 0,
        };
        expect(column.getQuickFilterText(params)).toEqual("Static");
      });
    });
  });
});
