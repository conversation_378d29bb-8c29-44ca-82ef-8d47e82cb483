const initialState = {
  isLoading: false,
  error: null,
  materials: [],
  throughputs: null,
  columnsForConditions: null,
  throughputColumns: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_THROUGHPUT_MATERIALS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_THROUGHPUT_MATERIALS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, materials: payload };
    case "RECEIVE_THROUGHPUT_MATERIALS_FAILED":
      return { ...state, isLoading: false, error: payload, materials: [] };
    case "RECEIVE_THROUGHPUTS_STARTED":
      return { ...state, isLoading: true, error: null, throughputs: [] };
    case "RECEIVE_THROUGHPUTS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, throughputs: payload };
    case "RECEIVE_THROUGHPUTS_FAILED":
      return { ...state, isLoading: false, error: payload, throughputs: [] };
    case "RECEIVE_COLUMNS_FOR_CONDITIONS_SUCCEEDED":
      return { ...state, error: null, columnsForConditions: payload };
    case "RECEIVE_COLUMNS_FOR_CONDITIONS_FAILED":
      return { ...state, error: payload, columnsForConditions: null };
    case "RECEIVE_THROUGHPUT_COLUMNS_SUCCEEDED":
      return { ...state, error: null, throughputColumns: payload };
    case "RECEIVE_THROUGHPUT_COLUMNS_FAILED":
      return { ...state, error: payload, throughputColumns: null };
    default:
      return state;
  }
}
