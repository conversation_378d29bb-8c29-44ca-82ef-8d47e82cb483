@import "../../styles/colors.scss";

div.email-info-modal {
  background-color: #fff;
  width: 600px;
  padding: 15px;

  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 15px;
  row-gap: 15px;

  & > h2,
  & > p,
  & > div {
    grid-column: 1/-1;
  }

  & > h2 {
    font-weight: normal;
    margin: 0;
  }

  & > p {
    margin: 0;
  }

  & > p.email-info-no-recipients {
    color: $fieldProOrange;
    font-weight: 600;
    font-size: 0.8rem;
  }

  & > div {
    height: 60px;
    width: 100%;

    & > textarea {
      height: 60px;
      font-size: 1rem;
    }
  }

  & > button {
    width: 150px;
    height: 40px;
    padding: 0;
    font-size: 0.8rem;
    justify-self: center;
    border: 1px solid #333;
  }

  & > button.cancel {
    color: #333;
    background-color: #fff;

    &:hover {
      background-color: darken(#fff, 10%);
    }
  }

  & > button.submit {
    color: #fff;
    background-color: $fabProBlue;

    &:hover {
      background-color: darken($fabProBlue, 10%);
    }
  }
}
