// NPM PACKAGE IMPORTS
import React from "react";
import Button from "msuite_storybook/dist/button/Button";
import Modal from "msuite_storybook/dist/modal/Modal";
import Input from "msuite_storybook/dist/input/Input";

// STYLE IMPORTS
import "./stylesEmailInfoModal.scss";

const EmailInfoModal = ({
  showEmailInfo,
  toggleShowEmailInfo,
  emails,
  setEmails,
  emailSubject,
  setEmailSubject,
  emailBody,
  setEmailBody,
  sendBackOrResubmitSelected,
  resubmitting,
  drawingsTable,
}) => (
  <Modal open={showEmailInfo} handleClose={() => toggleShowEmailInfo(false)}>
    <div className="email-info-modal">
      <h2 className="email-info-title">
        {resubmitting
          ? "Resubmit for Approval"
          : `Send Back ${drawingsTable ? "Drawing" : "Package"}(s)`}
      </h2>
      <p>Comma separated emails</p>
      <Input
        value={emails}
        onChange={(e) => setEmails(e.target.value)}
        placeholder="Recipients (comma separated)"
        className="email-info-emails"
        as="textarea"
      />
      <p className="email-info-no-recipients">
        {!emails.trim()
          ? "There are currently no recipients added. Notification emails will only be sent to the users who are assigned to these drawings and have opted to be alerted to any sent back drawings."
          : ""}
      </p>
      <Input
        value={emailSubject}
        onChange={(e) => setEmailSubject(e.target.value)}
        placeholder="Subject"
        className="email-info-subject"
        as="textarea"
      />
      <Input
        value={emailBody}
        onChange={(e) => setEmailBody(e.target.value)}
        placeholder={`Reason for ${
          resubmitting ? "resubmitting" : "sending back"
        }`}
        className="email-info-body"
        as="textarea"
      />
      <Button className="cancel" onClick={() => toggleShowEmailInfo(false)}>
        Cancel
      </Button>
      <Button
        className="submit"
        onClick={() => sendBackOrResubmitSelected(true)}
      >
        Submit
      </Button>
    </div>
  </Modal>
);

export default EmailInfoModal;
