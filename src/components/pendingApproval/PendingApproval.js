// NPM PACKAGE IMPORTS
import React, {
  useEffect,
  useState,
  useCallback,
  useMemo,
  useRef,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { FaThList } from "react-icons/fa";
import Button from "msuite_storybook/dist/button/Button";
import Input from "msuite_storybook/dist/input/Input";
import moment from "moment";
import "moment-timezone";

// COMPONENT IMPORTS
import AgTable from "../reusable/agTable/AgTable";
import ChildCellRenderer from "../reusable/frameworkComponents/ChildCellRenderer";
import ResetNeededConfirmationModal from "./resetNeededConfirmationModal/ResetNeededConfirmationModal";
import DueDateEditorRenderer from "../reusable/frameworkComponents/DueDateEditorRenderer";
import WorkflowModal from "../reusable/workflowModal/WorkflowModal";
import MoreInfoDropdown from "../reusable/moreInfoDropdown/MoreInfoDropdown";
import MoreInfoCellRenderer from "../reusable/frameworkComponents/MoreInfoCellRenderer";
import PDFModal from "../reusable/pdfModal";
import ForgeModelSelector from "../reusable/forgeModelSelector/ForgeModelSelector";
import ForgeModal from "../reusable/forge/ForgeModal";
import DrawingsWithoutItemsConfirmationModal from "./drawingsWithoutItemsConfirmationModal/DrawingsWithoutItemsConfirmationModal";
import EmailInfoModal from "./emailInfoModal/EmailInfoModal";
import AllInOneModal from "./allInOneModal/AllInOneModal";
import AssignmentsModal from "../reusable/assignmentsModal/AssignmentsModal";
import SubmitRevisionModal from "../reusable/submitRevisionModal/SubmitRevisionModal";
import DrawingNameCellRenderer from "../reusable/frameworkComponents/DrawingNameCellRenderer";
import BreadCrumbs from "../reusable/breadcrumbs/Breadcrumbs";
import ConfirmationModal from "../reusable/confirmationModal/ConfirmationModal";
import ManageMAJModal from "../reusable/manageMAJModal/ManageMAJModal";

// REDUX IMPORTS
import {
  handleSetPageTitle,
  handleFetchColumnState,
  handleSaveSortState,
  handleFetchSortState,
} from "../../redux/generalActions";
import {
  handleApprovePackagesPendingApproval,
  handleClearPackagesPendingApprovalState,
  handleFetchPackagesPendingApproval,
  handleSendBackPackagesPendingApproval,
  handleFetchPackagesSentBack,
  handleResubmitPackagesSentBack,
  handleFetchApprovalsPending,
} from "./packages/packagesPendingApprovalActions";
import {
  handleApproveDrawingsPendingApproval,
  handleClearDrawingsPendingApprovalState,
  handleFetchDrawingsPendingApproval,
  handleSendBackDrawingsPendingApproval,
  handleFetchDrawingsSentBack,
  handleResubmitDrawingsSentBack,
} from "./drawings/drawingsPendingApprovalActions";
import {
  handleDeleteDrawings,
  handleFetchDrawings,
  handleFetchDrawingsById,
} from "../drawings/drawingsActions";
import { handleUpdatePackages } from "../packages/packagesActions";
import { handleChangeItemWorkflow } from "../flows/flowsActions";
import {
  handleFetchPackagesPendingApprovalViewingAll,
  handleFetchDrawingsPendingApprovalViewingAll,
  handleUpdateTableViewSettings,
} from "../profile/profileActions";

// COMPONENT IMPORTS
import JobNameCellRenderer from "../reusable/frameworkComponents/JobNameCellRenderer";
import PackageNameCellRenderer from "../reusable/frameworkComponents/PackageNameCellRenderer";
import DownloadMAJsButton from "../reusable/downloadMAJsButton/DownloadMAJsButton";

// CONSTANTS IMPORTS
import { columnDefs as packagesColumnDefs } from "./packages/packagesPendingApprovalConstants";
import { columnDefs as drawingsColumnDefs } from "./drawings/drawingsPendingApprovalConstants";

// HELPER FUNCTION IMPORTS
import { onDisplayedColumnsChanged } from "../../_utils";

// STYLE IMPORTS
import "./stylesPendingApproval.scss";
import {
  handleFetchDrawingFiles,
  handleFetchPackageFile,
} from "../files/filesActions";

const PendingApproval = () => {
  const [currentTable, setCurrentTable] = useState(null);
  const [drawingsTable, setDrawingsTable] = useState(false);
  const [viewAll, toggleViewAll] = useState(false);
  const [viewSentBack, toggleViewSentBack] = useState(false);
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [selectedRows, setSelectedRows] = useState([]);
  const [searchInput, setSearchInput] = useState("");
  const [resetNeeded, setResetNeeded] = useState(null);
  const [showResetNeeded, toggleShowResetNeeded] = useState(false);
  const [drawingsWithoutItems, setDrawingsWithoutItems] = useState(null);
  const [showDrawingsWithoutItems, toggleShowDrawingsWithoutItems] = useState(
    false
  );
  const [showEmailInfo, toggleShowEmailInfo] = useState(false);
  const [emails, setEmails] = useState("");
  const [emailSubject, setEmailSubject] = useState("");
  const [emailBody, setEmailBody] = useState("");
  const [showWorkflowModal, toggleWorkflowModal] = useState(false);
  const [rowInfo, setRowInfo] = useState(null);
  const [showMoreInfo, toggleMoreInfo] = useState(false);
  const [moreInfoLocation, setMoreInfoLocation] = useState(null);
  const [file, setFile] = useState(null);
  const [pdfViewer, togglePDFViewer] = useState(false);
  const [forgeViewer, toggleForgeViewer] = useState(false);
  const [forgeModelSelector, toggleForgeModelSelector] = useState(false);
  const [packageBeingViewed, setPackageBeingViewed] = useState(null);
  const [current, setCurrent] = useState(null);
  const [currentRow, setCurrentRow] = useState({});
  const [currentRowOrder, setCurrentRowOrder] = useState(null);
  const [allInOne, toggleAllInOne] = useState(false);
  const [showAssignments, toggleAssignments] = useState(false);
  const [showSubmitRevision, toggleSubmitRevision] = useState(false);
  const [actionToConfirm, setActionToConfirm] = useState(null);
  const [showManageMAJModal, toggleManageMAJModal] = useState(false);

  const dispatch = useDispatch();
  const {
    packagesPendingApproval,
    savedColumnState: packagesSavedColumnState,
    packagesSentBack,
  } = useSelector((state) => state.packagesPendingApprovalData);
  const {
    drawingsPendingApproval,
    savedColumnState: drawingsSavedColumnState,
    drawingsSentBack,
  } = useSelector((state) => state.drawingsPendingApprovalData);
  const { flows } = useSelector((state) => state.flowsData);
  const { sortState } = useSelector((state) => state.generalData);
  const { permissions } = useSelector((state) => state.profileData);
  const { packageFiles, drawingFiles } = useSelector(
    (state) => state.filesData
  );

  useEffect(() => {
    let params = new URLSearchParams(window.location.search);
    const currentTab = params.get("tab"); // "incoming" or "sent-back"
    let initialCurrentTable = "";
    let pageTitle = "";

    if (currentTab === "sent-back") {
      // sent back
      toggleViewSentBack(true);
      if (/^\/drawings-pending-approval($|\/)/.test(window.location.pathname)) {
        initialCurrentTable = "DRAWINGS_SENT_BACK";
        pageTitle = "Pending Approvals by Drawing";

        const packageMatch = /(?!(\?|^|&))package_id=\d{1,}(?=(&|$))/.exec(
          window.location.search
        );

        const packageId = packageMatch
          ? parseInt(packageMatch[0].split("=")[1])
          : null;

        setPackageBeingViewed(packageId);
      } else {
        initialCurrentTable = "PACKAGES_SENT_BACK";
        pageTitle = "Pending Approvals by Package";
      }
    } else {
      toggleViewSentBack(false);
      if (/^\/drawings-pending-approval($|\/)/.test(window.location.pathname)) {
        initialCurrentTable = "DRAWINGS_PENDING_APPROVAL";
        pageTitle = "Pending Approvals by Drawing";

        const packageMatch = /(?!(\?|^|&))package_id=\d{1,}(?=(&|$))/.exec(
          window.location.search
        );

        const packageId = packageMatch
          ? parseInt(packageMatch[0].split("=")[1])
          : null;

        setPackageBeingViewed(packageId);
      } else {
        initialCurrentTable = "PACKAGES_PENDING_APPROVAL";
        pageTitle = "Pending Approvals by Package";
      }
    }
    setCurrentTable(initialCurrentTable);
    dispatch(handleSetPageTitle(pageTitle));

    return () => {
      dispatch(handleClearPackagesPendingApprovalState);
      dispatch(handleClearDrawingsPendingApprovalState);
    };
  }, []);

  useEffect(() => {
    if (!currentTable) return;

    const initialDrawingsTable = /^DRAWINGS/.test(currentTable);
    setDrawingsTable(initialDrawingsTable);

    if (["PACKAGES_SENT_BACK", "DRAWINGS_SENT_BACK"].includes(currentTable)) {
      dispatch(
        initialDrawingsTable
          ? handleFetchDrawingsPendingApprovalViewingAll
          : handleFetchPackagesPendingApprovalViewingAll
      ).then((res) => {
        let savedValue =
          res.error || !res[0]
            ? true
            : !!(initialDrawingsTable
                ? res[0].drawings_pending_approval_viewing_all
                : res[0].packages_pending_approval_viewing_all);

        if (!res.error && res[0]) toggleViewAll(savedValue);

        dispatch(
          initialDrawingsTable
            ? handleFetchDrawingsSentBack(packageBeingViewed, !savedValue)
            : handleFetchPackagesSentBack(!savedValue)
        );
      });
      dispatch(handleFetchColumnState(currentTable));
      dispatch(handleFetchSortState(currentTable));
    } else {
      dispatch(
        initialDrawingsTable
          ? handleFetchDrawingsPendingApprovalViewingAll
          : handleFetchPackagesPendingApprovalViewingAll
      ).then((res) => {
        let savedValue =
          res.error || !res[0]
            ? true
            : !!(initialDrawingsTable
                ? res[0].drawings_pending_approval_viewing_all
                : res[0].packages_pending_approval_viewing_all);

        if (!res.error && res[0]) toggleViewAll(savedValue);

        dispatch(
          initialDrawingsTable
            ? handleFetchDrawingsPendingApproval(
                packageBeingViewed,
                !savedValue
              )
            : handleFetchPackagesPendingApproval(!savedValue)
        );
      });
      dispatch(handleFetchColumnState(currentTable));
      dispatch(handleFetchSortState(currentTable));
    }

    setSelectedRows([]);
  }, [currentTable]);

  useEffect(() => {
    if (
      (drawingsTable ? drawingsPendingApproval : packagesPendingApproval) &&
      gridOptionsApi &&
      !viewSentBack
    ) {
      gridOptionsApi.setRowData(
        drawingsTable ? drawingsPendingApproval : packagesPendingApproval
      );

      if (drawingsTable && drawingsPendingApproval?.length > 0) {
        dispatch(
          handleFetchDrawingFiles(
            drawingsPendingApproval?.map((d) => d.id)?.join(",")
          )
        );
        setCurrentRowOrder(drawingsPendingApproval);
      }
    }
  }, [packagesPendingApproval, drawingsPendingApproval, gridOptionsApi]);

  useEffect(() => {
    if (
      (drawingsTable ? drawingsSentBack : packagesSentBack) &&
      gridOptionsApi &&
      viewSentBack
    ) {
      gridOptionsApi.setRowData(
        drawingsTable ? drawingsSentBack : packagesSentBack
      );

      if (drawingsTable && drawingsPendingApproval?.length > 0) {
        dispatch(
          handleFetchDrawingFiles(drawingsSentBack?.map((d) => d.id)?.join(","))
        );
        setCurrentRowOrder(drawingsSentBack);
      }
    }
  }, [packagesSentBack, drawingsSentBack, gridOptionsApi]);

  const viewMyPendingApprovals = () => {
    if (!viewAll) return;

    toggleViewAll(false);
    dispatch(
      handleUpdateTableViewSettings(
        null,
        null,
        null,
        null,
        drawingsTable ? null : 0,
        drawingsTable ? 0 : null
      )
    );
    dispatch(
      drawingsTable
        ? (viewSentBack
            ? handleFetchDrawingsSentBack
            : handleFetchDrawingsPendingApproval)(packageBeingViewed, true)
        : (viewSentBack
            ? handleFetchPackagesSentBack
            : handleFetchPackagesPendingApproval)(true)
    );
  };

  const viewAllPendingApprovals = () => {
    if (viewAll) return;

    toggleViewAll(true);
    dispatch(
      handleUpdateTableViewSettings(
        null,
        null,
        null,
        null,
        drawingsTable ? null : 1,
        drawingsTable ? 1 : null
      )
    );
    dispatch(
      drawingsTable
        ? (viewSentBack
            ? handleFetchDrawingsSentBack
            : handleFetchDrawingsPendingApproval)(packageBeingViewed, false)
        : (viewSentBack
            ? handleFetchPackagesSentBack
            : handleFetchPackagesPendingApproval)(false)
    );
  };

  const updateTabSearchParam = (newTab) => {
    const url = new URL(window.location);
    url.searchParams.set("tab", newTab);
    window.history.pushState(null, "", url.toString());
  };

  // used for the PDF files that we fetch from AWS S3 service
  const packageFilesRef = useRef(null);
  useEffect(() => {
    packageFilesRef.current = packageFiles;
  }, [packageFiles]);

  const drawingFilesRef = useRef(null);
  useEffect(() => {
    drawingFilesRef.current = drawingFiles;
  }, [drawingFiles]);

  // TODO - Consolidate to a reusable function, duplicated code!
  // common code for setting the package map as the file
  const getPackagePDFs = (packageId, updateFile = false) => {
    if (!packageId) return;
    const files = packageFilesRef?.current;
    if (!files?.hasOwnProperty(packageId)) {
      dispatch(handleFetchPackageFile(packageId)).then((res) => {
        if (res?.[packageId] && updateFile) {
          setFile(res[packageId]);
        }
      });
    } else if (updateFile) {
      // update the file if it already exists and we want to update...
      setFile(files[packageId]);
    }
    // else we exit because we already know the drawing package info
  };

  // TODO - Consolidate to a reusable function, duplicated code!
  const getDrawingPDFs = (id, rowData) => {
    if (!id) return;
    // if the file has no files to fetch, return (kept separate for logic simplicity)
    if (!(rowData.has_original ?? 0) && !(rowData.has_package_map ?? 0)) return;
    const files = drawingFilesRef?.current;
    if (files?.hasOwnProperty(id)) {
      // if drawing doesn't have original check for map
      if (files?.[id]?.original) {
        setFile(files[id].original);
      }
      // load package PDF and set if original was missing
      if (rowData.has_package_map) {
        getPackagePDFs(rowData.package_id, !files?.[id]?.original);
      }
    } else if (rowData?.has_original) {
      dispatch(handleFetchDrawingFiles(id)).then((res) => {
        // if drawing doesn't have original check for map
        if (res?.[id]?.original) {
          setFile(res[id].original);
        }
        // load package PDF and set if original was missing
        if (rowData.has_package_map) {
          getPackagePDFs(rowData.package_id, !res?.[id]?.original);
        }
      });
    } else if (rowData?.has_package_map) {
      // load package PDF and set
      getPackagePDFs(rowData.package_id, true);
    } // else do nothing... nothing to load
  };

  const setDisplayedPdf = (rowData) => {
    if (/DRAWINGS/.test(currentTable)) {
      const id = rowData.id;
      getDrawingPDFs(id, rowData);
    } else if (/PACKAGES/.test(currentTable) && rowData?.has_package_map) {
      getPackagePDFs(rowData.package_id, true);
    } else {
      // do nothing
    }
  };

  const viewIncomingApprovals = () => {
    if (!viewSentBack) return;

    // update url query string
    updateTabSearchParam("incoming");

    const newCurrentTable = drawingsTable
      ? "DRAWINGS_PENDING_APPROVAL"
      : "PACKAGES_PENDING_APPROVAL";
    setCurrentTable(newCurrentTable);
    toggleViewSentBack(false);
    dispatch(
      drawingsTable
        ? handleFetchDrawingsPendingApprovalViewingAll
        : handleFetchPackagesPendingApprovalViewingAll
    ).then((res) => {
      let savedValue =
        res.error || !res[0]
          ? true
          : !!(drawingsTable
              ? res[0].drawings_pending_approval_viewing_all
              : res[0].packages_pending_approval_viewing_all);

      if (!res.error && res[0]) toggleViewAll(savedValue);

      dispatch(
        drawingsTable
          ? handleFetchDrawingsPendingApproval(packageBeingViewed, !savedValue)
          : handleFetchPackagesPendingApproval(!savedValue)
      );
    });
    dispatch(handleFetchColumnState(newCurrentTable)).then((columnStateRes) => {
      dispatch(handleFetchSortState(newCurrentTable)).then((sortStateRes) => {
        if (gridOptionsApi) {
          const newColumnDefs = drawingsTable
            ? drawingsColumnDefs(
                moreInfoClick,
                toggleMoreInfo,
                () => togglePDFViewer(true),
                columnStateRes.error ? [] : columnStateRes,
                sortStateRes.error ? [] : sortStateRes[0],
                permissions,
                setDisplayedPdf
              )
            : packagesColumnDefs(
                handleDrawingCountClick,
                moreInfoClick,
                toggleMoreInfo,
                columnStateRes.error ? [] : columnStateRes,
                sortStateRes.error ? [] : sortStateRes[0],
                permissions
              );
          gridOptionsApi.setColumnDefs(newColumnDefs);
        }
      });
    });
  };

  const viewSentBackApprovals = () => {
    if (viewSentBack) return;

    updateTabSearchParam("sent-back");

    const newCurrentTable = drawingsTable
      ? "DRAWINGS_SENT_BACK"
      : "PACKAGES_SENT_BACK";
    setCurrentTable(newCurrentTable);
    toggleViewSentBack(true);
    dispatch(
      drawingsTable
        ? handleFetchDrawingsPendingApprovalViewingAll
        : handleFetchPackagesPendingApprovalViewingAll
    ).then((res) => {
      let savedValue =
        res.error || !res[0]
          ? true
          : !!(drawingsTable
              ? res[0].drawings_pending_approval_viewing_all
              : res[0].packages_pending_approval_viewing_all);

      if (!res.error && res[0]) toggleViewAll(savedValue);

      dispatch(
        drawingsTable
          ? handleFetchDrawingsSentBack(packageBeingViewed, !savedValue)
          : handleFetchPackagesSentBack(!savedValue)
      );
    });
    dispatch(handleFetchColumnState(newCurrentTable)).then((columnStateRes) => {
      dispatch(handleFetchSortState(newCurrentTable)).then((sortStateRes) => {
        if (gridOptionsApi) {
          const newColumnDefs = drawingsTable
            ? drawingsColumnDefs(
                moreInfoClick,
                toggleMoreInfo,
                () => togglePDFViewer(true),
                columnStateRes.error ? [] : columnStateRes,
                sortStateRes.error ? [] : sortStateRes[0],
                permissions,
                setDisplayedPdf
              )
            : packagesColumnDefs(
                handleDrawingCountClick,
                moreInfoClick,
                toggleMoreInfo,
                columnStateRes.error ? [] : columnStateRes,
                sortStateRes.error ? [] : sortStateRes[0],
                permissions
              );
          gridOptionsApi.setColumnDefs(newColumnDefs);
        }
      });
    });
  };

  const postPendingApprovalAction = useCallback(() => {
    if (currentRowOrder) {
      if (currentRowOrder.length - 1 > 0) {
        if (currentRowOrder[current + 1]) {
          setCurrentRow(currentRowOrder[current + 1]);
          setSelectedRows([currentRowOrder[current + 1]]);
          setRowInfo(currentRowOrder[current + 1]);
          setDisplayedPdf(currentRowOrder[current + 1]);
        } else {
          setCurrentRow(currentRowOrder[current - 1]);
          setSelectedRows([currentRowOrder[current - 1]]);
          setRowInfo(currentRowOrder[current - 1]);
          setDisplayedPdf(currentRowOrder[current - 1]);
          setCurrent(current - 1);
        }
      } else {
        toggleAllInOne();
      }
    }
  }, [rowInfo, currentRowOrder, current]);

  const approveSelected = async (
    confirmed = false,
    resetDrawings = false,
    resetNeededConfirmed = false,
    drawingsWithoutItemsConfirmed = false
  ) => {
    if (resetNeeded && !resetNeededConfirmed) {
      toggleShowResetNeeded(true);
      return;
    }

    if (!drawingsWithoutItemsConfirmed) {
      const drawingsRes = await dispatch(
        drawingsTable
          ? handleFetchDrawingsById(
              allInOne
                ? currentRow.id.toString()
                : selectedRows.map((r) => r.id).join(",")
            )
          : handleFetchDrawings(
              selectedRows.map((r) => r.package_id),
              true,
              null
            )
      );

      if (!drawingsRes.error) {
        const itemlessDrawings = drawingsRes.filter(
          (d) => d.work_item_count === null
        );

        if (itemlessDrawings.length) {
          setDrawingsWithoutItems(itemlessDrawings);
          toggleShowDrawingsWithoutItems(true);
          return;
        }
      }
    }

    if (
      confirmed ||
      ((!resetNeeded || resetNeededConfirmed) &&
        (!drawingsWithoutItems || drawingsWithoutItemsConfirmed))
    ) {
      dispatch(
        (drawingsTable
          ? handleApproveDrawingsPendingApproval
          : handleApprovePackagesPendingApproval)(
          allInOne
            ? [currentRow.id]
            : selectedRows.map(
                drawingsTable ? (r) => r.id : (r) => r.package_id
              ),
          resetDrawings
        )
      ).then((res) => {
        if (!res.error) {
          if (drawingsTable) {
            if (allInOne) postPendingApprovalAction();
            else
              setCurrentRowOrder(
                currentRowOrder.filter((r) => r.id !== rowInfo.id)
              );
          }
          setSelectedRows([]);
          toggleShowDrawingsWithoutItems(false);
          setDrawingsWithoutItems(null);
          toggleShowResetNeeded(false);
          setResetNeeded(null);

          if (gridOptionsApi) {
            gridOptionsApi.deselectAll();
          }

          if (/^[1-9]/.test(res.failed)) {
            dispatch(
              drawingsTable
                ? handleFetchDrawingsPendingApproval(
                    packageBeingViewed,
                    !viewAll
                  )
                : handleFetchPackagesPendingApproval(!viewAll)
            );
          }
        }

        // check pending approval from API and toggle pending approval icon
        dispatch(handleFetchApprovalsPending());
      });
    }
  };

  const sendBackOrResubmitSelected = (resubmit = false, confirmed = false) => {
    if (confirmed) {
      let emailInfo = {};

      if (emails.trim()) Object.assign(emailInfo, { email_recipients: emails });
      if (emailSubject.trim())
        Object.assign(emailInfo, { email_subject: emailSubject });
      if (emailBody.trim()) Object.assign(emailInfo, { email_body: emailBody });

      return dispatch(
        (resubmit
          ? drawingsTable
            ? handleResubmitDrawingsSentBack
            : handleResubmitPackagesSentBack
          : drawingsTable
          ? handleSendBackDrawingsPendingApproval
          : handleSendBackPackagesPendingApproval)(
          allInOne
            ? [currentRow.id]
            : selectedRows.map(
                drawingsTable ? (r) => r.id : (r) => r.package_id
              ),
          emailInfo
        )
      ).then((res) => {
        if (!res.error && res.state === "success") {
          if (drawingsTable) {
            if (allInOne) postPendingApprovalAction();
            else
              setCurrentRowOrder(
                currentRowOrder.filter((r) => r.id !== rowInfo.id)
              );
          }
          setEmails("");
          setEmailSubject("");
          setEmailBody("");
          toggleShowEmailInfo(false);
          setSelectedRows([]);
          if (gridOptionsApi) {
            gridOptionsApi.deselectAll();
          }

          // check pending approval from API and toggle pending approval icon
          dispatch(handleFetchApprovalsPending());
        }
      });
    } else {
      let allEmails = [];
      selectedRows.forEach((row) => {
        if (drawingsTable) {
          allEmails.push(row.user_email);
        } else {
          // emails could contain multiple so need to split before adding to array
          const splitEmails = row.emails?.split(",");
          splitEmails && allEmails.push(...splitEmails);
        }
      });

      let uniqueEmails = [...new Set(allEmails.filter((e) => e))];
      setEmails(uniqueEmails.join(","));
      toggleShowEmailInfo(true);
    }
  };

  const searchTable = (e) => {
    e.persist();

    setSearchInput(e.target.value);
    if (gridOptionsApi) {
      gridOptionsApi.setQuickFilter(e.target.value);
    }
  };

  const changeWorkflowSubmit = useCallback(
    (newWorkflow) => {
      dispatch(
        handleChangeItemWorkflow(
          drawingsTable ? rowInfo.id : rowInfo.package_id,
          drawingsTable ? "drawing" : "package",
          newWorkflow
        )
      ).then((res) => {
        if (!res.error) {
          const newRowInfo = { ...rowInfo, flow_id: newWorkflow };
          const existingFlow = flows.find((f) => f.id === newWorkflow);

          if (existingFlow) {
            newRowInfo.flow_name = flows.find((f) => f.id === newWorkflow).name;
          }

          const currentNode = gridOptionsApi.getRowNode(
            drawingsTable ? newRowInfo.id : newRowInfo.package_id
          );
          if (currentNode) {
            currentNode.setData(newRowInfo);
            gridOptionsApi.refreshCells({
              rowNodes: [currentNode],
              force: true,
            });
          }
          setRowInfo(newRowInfo);
          toggleWorkflowModal(false);
        }
      });
    },
    [rowInfo, flows]
  );

  const handleDrawingCountClick = (type, rowInfo) => {
    const tab = viewSentBack ? "sent-back" : "incoming";
    window.location.assign(
      `${process.env.REACT_APP_FABPRO}/drawings-pending-approval/?package_id=${rowInfo.package_id}&tab=${tab}`
    );
  };

  const moreInfoClick = (event, toggleMoreInfo, rowData, params) => {
    event.persist();
    setMoreInfoLocation({ x: event.clientX, y: event.clientY });
    setRowInfo(rowData);
    if (drawingsTable) setCurrent(params.node.childIndex);
    setDisplayedPdf(rowData);
    toggleMoreInfo(true);
  };

  const handleClosePDFViewer = () => {
    setRowInfo(null);
    togglePDFViewer(false);
  };

  const updateRow = (updatedInfo) => {
    const newRowInfo = {
      ...rowInfo,
      ...updatedInfo,
      annotated_url: null,
    };
    setRowInfo(newRowInfo);
    setCurrentRowOrder(
      currentRowOrder.map((r) => (r.id === newRowInfo.id ? newRowInfo : r))
    );
    setSelectedRows(
      selectedRows.map((r) => (r.id === newRowInfo.id ? newRowInfo : r))
    );
    setDisplayedPdf(updatedInfo);

    if (gridOptionsApi) {
      const rowNode = gridOptionsApi.getRowNode(newRowInfo.id);
      if (rowNode) rowNode.setData(newRowInfo);
    }
  };

  const refreshPdfModal = () => {
    dispatch(handleFetchDrawingsById(rowInfo.id.toString())).then((res) => {
      if (!res.error) {
        const drawing = res[0];
        if (drawing) {
          setTimeout(() => {
            setCurrentRow(drawing);
            setCurrentRowOrder(
              currentRowOrder.map((r) =>
                r.id === drawing.id
                  ? { ...r, has_annotated: drawing.has_annotated }
                  : r
              )
            );
            setRowInfo({ ...rowInfo, has_annotated: drawing.has_annotated });
          }, 1000);
          const rowNode = gridOptionsApi.getRowNode(drawing.id);
          rowNode.setData({ ...rowInfo, has_annotated: drawing.has_annotated });
          gridOptionsApi.redrawRows({ rowNodes: [rowNode] });
        }
      }
    });
  };

  const handleDeleteConfirmation = useCallback(() => {
    dispatch(handleDeleteDrawings(actionToConfirm.id)).then(() => {
      setCurrentRowOrder(
        currentRowOrder.filter((r) => r.id !== actionToConfirm.id)
      );
      if (selectedRows?.length) {
        setSelectedRows(
          selectedRows.filter((r) => r.id !== actionToConfirm.id)
        );
      }
      if (gridOptionsApi) {
        const rowNode = gridOptionsApi.getRowNode(actionToConfirm.id);
        if (rowNode) {
          gridOptionsApi.applyTransaction({ remove: [rowNode] });
        }
      }
      setActionToConfirm(null);
    });
  }, [gridOptionsApi, actionToConfirm]);

  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);

    if (drawingsTable) {
      const result = [];
      params.api.forEachNodeAfterFilterAndSort((n) => {
        result.push(n.data);
      });
      setCurrentRowOrder(result);
    }
  };

  const onSortChanged = (params) => {
    params.api.redrawRows();

    if (drawingsTable) {
      const result = [];
      params.api.forEachNodeAfterFilterAndSort((n) => {
        result.push(n.data);
      });
      setCurrentRowOrder(result);
    }

    const sortedColumn = params.columnApi.getAllColumns().find((c) => c.sort);
    dispatch(
      handleSaveSortState(
        sortedColumn ? sortedColumn.colId : null,
        sortedColumn ? sortedColumn.sort : null,
        currentTable
      )
    );
  };

  const onSelectionChanged = (params) => {
    let rows = params.api.getSelectedRows();
    let resetNeededRows = rows.filter((r) => r.reset_needed);

    if (resetNeededRows.length) setResetNeeded(resetNeededRows);
    else setResetNeeded(null);

    setDrawingsWithoutItems(null);

    return setSelectedRows(rows);
  };

  const onCellValueChanged = (params) => {
    if (params.colDef.field === "due_date") {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const utcOffset = moment.tz(timezone).utcOffset() * 60;
      if (params.newValue + utcOffset === params.oldValue) return;
    }
    dispatch(
      handleUpdatePackages([params.data.package_id], {
        [params.colDef.field]: params.newValue,
      })
    ).then((res) => {
      if (!res.error) {
        const rowNode = params.api.getRowNode(params.data.package_id);
        rowNode.setData({ ...rowNode.data, due_date: res[0].due_date_unix });
      }
    });
  };
  const onFilterChanged = (params) => {
    if (drawingsTable) {
      const result = [];
      params.api.forEachNodeAfterFilterAndSort((n) => {
        result.push(n.data);
      });
      setCurrentRowOrder(result);
    }
  };

  const gridOptions = {
    rowData: [],
    columnDefs:
      (drawingsTable ? drawingsSavedColumnState : packagesSavedColumnState) &&
      sortState
        ? drawingsTable
          ? drawingsColumnDefs(
              moreInfoClick,
              toggleMoreInfo,
              () => togglePDFViewer(true),
              drawingsTable
                ? drawingsSavedColumnState
                : packagesSavedColumnState,
              sortState,
              permissions,
              setDisplayedPdf
            )
          : packagesColumnDefs(
              handleDrawingCountClick,
              moreInfoClick,
              toggleMoreInfo,
              drawingsTable
                ? drawingsSavedColumnState
                : packagesSavedColumnState,
              sortState,
              permissions
            )
        : [],
    rowClassRules,
    onSortChanged,
    onFilterChanged,
    onGridReady,
    onDisplayedColumnsChanged: (params) =>
      onDisplayedColumnsChanged(currentTable, params),
    frameworkComponents: {
      childCellRenderer: ChildCellRenderer,
      dueDateEditorRenderer: DueDateEditorRenderer,
      moreInfoCellRenderer: MoreInfoCellRenderer,
      drawingNameCellRenderer: DrawingNameCellRenderer,
      jobNameCellRenderer: JobNameCellRenderer,
      packageNameCellRenderer: PackageNameCellRenderer,
    },
    defaultColDef: {
      cellClass: "custom-wrap",
      minWidth: 120,
      wrapText: true,
    },
    suppressRowClickSelection: true,
    onSelectionChanged,
    onCellValueChanged,
    getRowNodeId: (data) => (drawingsTable ? data.id : data.package_id),
  };

  const breadcrumbs = useMemo(() => {
    return [
      {
        step: 1,
        display: "Packages",
        path: `/packages-pending-approval/?tab=${
          viewSentBack ? "sent-back" : "incoming"
        }`,
        value: viewSentBack
          ? "PACKAGES_SENT_BACK"
          : "PACKAGES_PENDING_APPROVAL",
        isSelected: viewSentBack
          ? currentTable === "PACKAGES_SENT_BACK"
          : currentTable === "PACKAGES_PENDING_APPROVAL",
      },
      {
        step: 2,
        display: "Drawings",
        path: `/drawings-pending-approval/?tab=${
          viewSentBack ? "sent-back" : "incoming"
        }`,
        value: viewSentBack
          ? "DRAWINGS_SENT_BACK"
          : "DRAWINGS_PENDING_APPROVAL",
        isSelected: viewSentBack
          ? currentTable === "DRAWINGS_SENT_BACK"
          : currentTable === "DRAWINGS_PENDING_APPROVAL",
      },
    ];
  }, [viewSentBack, currentTable]);

  return (
    <div className="pending-approval">
      {breadcrumbs && <BreadCrumbs crumbs={breadcrumbs} />}
      <div className="pending-approval-my-all-tabs">
        <div
          className={`my-pending-approvals ${viewAll ? "" : "selected"}`}
          onClick={viewMyPendingApprovals}
        >
          My Pending Approvals
        </div>
        <div
          className={`all-pending-approvals ${viewAll ? "selected" : ""}`}
          onClick={viewAllPendingApprovals}
        >
          All Pending Approvals
        </div>
      </div>
      <div className="pending-approval-incoming-sent-back-tabs">
        <div
          className={`incoming-pending-approvals ${
            viewSentBack ? "" : "selected"
          }`}
          // onClick={() => handleTabClick("incoming")}
          onClick={viewIncomingApprovals}
        >
          <FaThList /> <span>Incoming Approvals List</span>
        </div>
        <div
          className={`sent-back-pending-approvals ${
            viewSentBack ? "selected" : ""
          }`}
          // onClick={() => handleTabClick("sent-back")}
          onClick={viewSentBackApprovals}
        >
          <FaThList /> <span>Sent Back List</span>
        </div>
      </div>
      <div className="pending-approval-table">
        <div className="action-container">
          {viewSentBack ? (
            <>
              {permissions?.includes(112) && (
                <Button
                  className="approve-selected"
                  onClick={() => sendBackOrResubmitSelected(true, false)}
                  disabled={!selectedRows.length}
                >
                  Resubmit Selected
                </Button>
              )}
            </>
          ) : (
            <>
              {permissions?.includes(91) && (
                <Button
                  className="approve-selected"
                  onClick={() => approveSelected(false, false, false, false)}
                  disabled={!selectedRows.length}
                >
                  Approve Selected
                </Button>
              )}
              {permissions?.includes(94) && (
                <Button
                  className="send-back-selected"
                  onClick={() => sendBackOrResubmitSelected(false, false)}
                  disabled={!selectedRows.length}
                >
                  Send Back Selected
                </Button>
              )}
            </>
          )}
          <Input
            className="quick-search"
            placeholder="Search"
            value={searchInput}
            onChange={searchTable}
          />
          <DownloadMAJsButton
            selectedDrawings={selectedRows}
            className="pending-download-majs push-right"
          />
        </div>
        <div className="pending-approval-table-container">
          {(drawingsTable
            ? drawingsSavedColumnState
            : packagesSavedColumnState) &&
            sortState && <AgTable gridOptions={gridOptions} />}
        </div>
      </div>
      {allInOne && rowInfo && (
        <AllInOneModal
          allInOne={allInOne}
          toggleAllInOne={() => {
            if (showResetNeeded || showDrawingsWithoutItems || showEmailInfo)
              return;

            toggleAllInOne(false);
          }}
          selectedItem={rowInfo}
          setSelectedItem={setRowInfo}
          file={file}
          setFile={setFile}
          area="VIEWER"
          current={current}
          setCurrent={setCurrent}
          currentRow={currentRow}
          setCurrentRow={setCurrentRow}
          currentRowOrder={currentRowOrder}
          viewSentBack={viewSentBack}
          approveSelected={approveSelected}
          sendBackOrResubmitSelected={sendBackOrResubmitSelected}
          setSelectedRows={setSelectedRows}
          gridOptionsApi={gridOptionsApi}
          setDisplayedPdf={setDisplayedPdf}
        />
      )}
      {showResetNeeded && resetNeeded && (
        <ResetNeededConfirmationModal
          showResetNeeded={showResetNeeded}
          toggleShowResetNeeded={toggleShowResetNeeded}
          resetNeeded={resetNeeded}
          approveSelected={approveSelected}
          drawingsTable={drawingsTable}
          allInOne={allInOne}
        />
      )}
      {showDrawingsWithoutItems && drawingsWithoutItems && (
        <DrawingsWithoutItemsConfirmationModal
          showDrawingsWithoutItems={showDrawingsWithoutItems}
          toggleShowDrawingsWithoutItems={toggleShowDrawingsWithoutItems}
          drawingsWithoutItems={drawingsWithoutItems}
          approveSelected={approveSelected}
          drawingsTable={drawingsTable}
          allInOne={allInOne}
        />
      )}
      {showEmailInfo && (
        <EmailInfoModal
          showEmailInfo={showEmailInfo}
          toggleShowEmailInfo={toggleShowEmailInfo}
          emails={emails}
          setEmails={setEmails}
          emailSubject={emailSubject}
          setEmailSubject={setEmailSubject}
          emailBody={emailBody}
          setEmailBody={setEmailBody}
          sendBackOrResubmitSelected={(confirmation) =>
            sendBackOrResubmitSelected(viewSentBack, confirmation)
          }
          resubmitting={viewSentBack}
          drawingsTable={drawingsTable}
          allInOne={allInOne}
        />
      )}
      {showWorkflowModal && (
        <WorkflowModal
          open={showWorkflowModal}
          handleClose={() => toggleWorkflowModal(false)}
          handleSubmit={changeWorkflowSubmit}
          selectedItem={rowInfo}
        />
      )}
      {showMoreInfo && moreInfoLocation && (
        <MoreInfoDropdown
          moreInfoLocation={moreInfoLocation}
          currentTable={currentTable}
          rowInfo={rowInfo}
          toggleMoreInfo={toggleMoreInfo}
          toggleWorkflowModal={toggleWorkflowModal}
          togglePDFViewer={() => togglePDFViewer(true)}
          toggleForgeViewer={() => toggleForgeViewer(true)}
          toggleForgeModelSelector={() => toggleForgeModelSelector(true)}
          toggleAllInOne={() => {
            setSelectedRows([rowInfo]);
            toggleAllInOne(true);
          }}
          onOptionClick={(action) => {
            switch (action) {
              case "VIEW_ASSIGNMENTS":
                toggleAssignments(true);
                break;
              default:
                return;
            }
          }}
          setSubmitRevisionInfo={() => {
            toggleSubmitRevision(true);
          }}
          toggleConfirmationModal={(action) => {
            setActionToConfirm(action);
            toggleMoreInfo(false);
          }}
          toggleManageMAJModal={() => toggleManageMAJModal(true)}
          setDisplayedPdf={setDisplayedPdf}
        />
      )}
      {actionToConfirm && (
        <ConfirmationModal
          showModal={!!actionToConfirm}
          toggleModal={() => setActionToConfirm(null)}
          action={actionToConfirm.action}
          item={actionToConfirm.item}
          handleClick={handleDeleteConfirmation}
        />
      )}
      {pdfViewer && rowInfo && (
        <PDFModal
          pdfViewer={pdfViewer}
          togglePDFViewer={handleClosePDFViewer}
          selectedItem={rowInfo}
          itemId={drawingsTable ? rowInfo.id : rowInfo.package_id}
          itemType={drawingsTable ? "DRAWING" : "PACKAGE"}
          file={file}
          setFile={setFile}
          area="VIEWER"
          currentRowOrder={drawingsTable ? currentRowOrder : false}
          setCurrentRowOrder={setCurrentRowOrder}
          setSelectedItem={setRowInfo}
          refresh={drawingsTable ? refreshPdfModal : () => {}}
        />
      )}
      {forgeModelSelector && (
        <ForgeModelSelector
          forgeModelSelector={forgeModelSelector}
          toggleForgeModelSelector={toggleForgeModelSelector}
          toggleForgeViewer={toggleForgeViewer}
          rowInfo={rowInfo}
          setRowInfo={setRowInfo}
          currentTable="PACKAGES"
        />
      )}
      {forgeViewer && (
        <ForgeModal
          forgeViewer={forgeViewer}
          toggleForgeViewer={() => {
            if (!drawingsTable) toggleForgeModelSelector(true);
            toggleForgeViewer(false);
          }}
          selectedItem={
            drawingsTable ? rowInfo : { ...rowInfo, id: rowInfo.package_id }
          }
          type={drawingsTable ? "DRAWINGS" : "PACKAGES"}
          sheetCreation
          gridOptionsApi={gridOptionsApi}
          setCurrentRowOrder={setCurrentRowOrder}
          area="VIEWER"
        />
      )}
      {showAssignments && (
        <AssignmentsModal
          open={showAssignments}
          handleClose={() => toggleAssignments(false)}
          selectedItem={
            drawingsTable ? rowInfo : { ...rowInfo, id: rowInfo.package_id }
          }
          currentTable={drawingsTable ? "DRAWINGS" : "PACKAGES"}
        />
      )}
      {showSubmitRevision && (
        <SubmitRevisionModal
          handleClose={() => toggleSubmitRevision(false)}
          submitRevisionInfo={rowInfo}
          updateRow={updateRow}
        />
      )}
      {showManageMAJModal && (
        <ManageMAJModal
          drawingOrPackage={rowInfo}
          showModal={showManageMAJModal}
          handleClose={() => toggleManageMAJModal(false)}
          currentTable={currentTable}
        />
      )}
    </div>
  );
};

export default PendingApproval;
