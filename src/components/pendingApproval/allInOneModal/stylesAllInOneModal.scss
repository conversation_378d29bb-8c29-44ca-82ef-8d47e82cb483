@import "../../styles/colors.scss";

div.all-in-one-wrapper {
  height: calc(100vh - 110px);
  background-color: $darkGrey;
  position: relative;
  transform: translateY(5px);

  display: grid;
  grid-template-columns: 1fr 100px;
  grid-template-rows: 30px 40px 1fr 40px;
  row-gap: 5px;

  & > div.viewer {
    height: calc(100vh - 240px);
  }

  & > div.custom-ag-styles.ag-theme-balham-dark {
    width: 100%;
    height: calc(100vh - 240px);
    padding-left: 10px;
  }

  & > div.pdf-or-items-tabs {
    grid-row: 2/3;
    grid-column: 1/-1;

    display: flex;
    padding-left: 10px;

    & > div {
      padding: 10px;
      color: #fff;
      cursor: pointer;
    }

    & > div.selected {
      background-color: #fff;
      color: #333;
      font-weight: 600;
    }
  }

  & > div.traversal-buttons {
    grid-row: 1/2;
    grid-column: 1/-1;
    padding: 0 10px;

    display: grid;
    column-gap: 10px;
    grid-template-columns: 150px 1fr 150px;

    & > button {
      padding: 0 10px;
      height: 30px;
      font-size: 0.8rem;
      width: 100px;
      background-color: $fabProBlue;
      color: #fff;

      display: flex;
      justify-content: space-around;
      align-items: center;

      &:not(:disabled):hover {
        background-color: darken($fabProBlue, 10%);
      }
    }

    & > button#previous,
    & > div#previous {
      grid-column: 1/2;
      justify-self: start;
    }

    & > button#next,
    & > div#next {
      grid-column: 3/-1;
      justify-self: end;
    }

    & > span {
      grid-column: 2/3;
      font-weight: 600;
      color: #fff;
      text-align: center;
    }
  }

  &.fullscreen {
    width: calc(100vw - 50px);
    transform: translate(30px, 5px);

    &.narrow {
      width: calc(100vw - 250px);
      transform: translate(125px, 5px);
    }
  }

  @media screen and (max-width: 600px) and (orientation: portrait) {
    height: 100vw;
    width: 100vw;
  }

  @media screen and (max-height: 600px) and (orientation: landscape) {
    height: 100vh;
    width: 100vh;
  }

  & section.content-wrapper {
    height: 100%;
    width: 100%;

    & div.error-message {
      height: 100%;
      width: 100%;

      display: grid;
      justify-content: center;
      align-items: center;

      & p {
        margin: 0;
        font-size: 1.2rem;
        color: $fieldProOrange;
      }
    }
  }

  & button.fullscreen-button {
    position: absolute;
    bottom: 15px;
    right: 15px;
    width: 30px;
    height: 30px;
    background-color: $blue;
    color: #fff;
    border: 1px solid $darkGrey;
    border-radius: 3px;
    font-size: 1.2rem;
    cursor: pointer;
    transition: background-color 250ms ease;

    display: grid;
    justify-content: center;
    align-items: center;

    &:hover {
      background-color: darken($blue, 10%);
    }
  }

  & div.file-type {
    padding: 10px;
    height: 200px;

    display: flex;
    flex-direction: column;
    row-gap: 15px;

    & > button {
      width: 100%;
      padding: 0 5px;
      height: 40px;
      background-color: $fabProBlue;
      color: #fff;
      font-size: 0.8rem;

      &:not(:disabled):hover {
        background-color: darken($fabProBlue, 10%);
      }
    }
  }

  & > div.pending-approval-action-buttons {
    padding-left: 10px;
    display: flex;
    column-gap: 10px;

    & > button {
      height: 30px;
      padding: 0 10px;
      font-size: 1rem;
      color: #fff;
    }

    & > button.approve-selected {
      background-color: $green;

      &:not(:disabled):hover {
        background-color: darken($green, 10%);
      }
    }

    & > button.send-back-selected {
      background-color: $redWashDark;

      &:not(:disabled):hover {
        background-color: darken($redWashDark, 10%);
      }
    }

    & > button.manage-selected {
      background-color: $fabProBlue;

      &:not(:disabled):hover {
        background-color: darken($fabProBlue, 10%);
      }
    }
  }
}
