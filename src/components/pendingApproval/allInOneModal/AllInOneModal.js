// NPM PACKAGE IMPORTS
import React, { useState, useEffect, useCallback } from "react";
import PSPDFKitViewer from "../../reusable/pdfViewer/PSPDFKitViewer";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import { useSelector, useDispatch } from "react-redux";
import { IoMdExpand, IoMdContract } from "react-icons/io";
import ForgeViewer from "./../../reusable/forge/ForgeViewer";

// REDUX IMPORTS
import {
  handleFetchItems,
  handleClearItemsState,
} from "../../items/itemsActions";

// COMPONENT IMPORTS
import AgTable from "../../reusable/agTable/AgTable";
import AssignmentsModal from "../../reusable/assignmentsModal/AssignmentsModal";
import SubmitRevisionModal from "../../reusable/submitRevisionModal/SubmitRevisionModal";

// CONSTANTS IMPORTS
import { columnDefs } from "./allInOneModalConstants";

// STYLE IMPORTS
import "./stylesAllInOneModal.scss";
import "./stylesAllInOneModal.css";
import "../../reusable/forge/stylesForgeViewer.scss";
import { FaArrowLeft, FaArrowRight } from "react-icons/fa";

const AllInOneModal = ({
  allInOne,
  toggleAllInOne,
  selectedItem,
  file,
  setFile,
  refresh = (f) => f,
  area,
  current,
  setCurrent,
  currentRow,
  setCurrentRow,
  currentRowOrder,
  viewSentBack,
  setSelectedItem,
  approveSelected,
  sendBackOrResubmitSelected,
  setSelectedRows,
  gridOptionsApi,
  setDisplayedPdf,
}) => {
  const [fullscreen, toggleFullscreen] = useState(false);
  const [showForge, toggleForge] = useState(false);
  const [showAssignments, toggleAssignments] = useState(false);
  const [showSubmitRevision, toggleSubmitRevision] = useState(false);
  const [itemsView, toggleItemsView] = useState(false);
  const [itemsGridOptionsApi, setItemsGridOptionsApi] = useState(null);
  const [pdfViewerLoading, togglePdfViewerLoading] = useState(false);
  const [fileType, setFileType] = useState("ORIGINAL");

  const dispatch = useDispatch();
  const {
    token,
    userInfo,
    permissions,
    userId,
    systemSettings,
    features,
  } = useSelector((state) => state.profileData);
  const { navExpanded } = useSelector((state) => state.generalData);
  const { items } = useSelector((state) => state.itemsData);
  const { packageFiles, drawingFiles } = useSelector(
    (state) => state.filesData
  );

  useEffect(() => {
    return () => dispatch(handleClearItemsState);
  }, []);

  useEffect(() => {
    if (currentRowOrder && current === null) {
      setCurrent(currentRowOrder.findIndex((r) => r.id === selectedItem.id));
    }
  }, [selectedItem.id, currentRowOrder]);

  useEffect(() => {
    if (currentRowOrder && currentRowOrder[current]) {
      setCurrentRow(currentRowOrder[current]);
      setSelectedRows([currentRowOrder[current]]);
      const fileToUse =
        drawingFiles?.[currentRowOrder[current]?.id]?.original ||
        packageFiles?.[currentRowOrder[current]?.package_id] ||
        null;
      setFile(fileToUse);
      setFileType(selectedItem.has_annotated ? "ANNOTATED" : "ORIGINAL");
    } else {
      setCurrentRow(selectedItem);
      setSelectedRows([selectedItem]);
      setFileType(selectedItem.has_annotated ? "ANNOTATED" : "ORIGINAL");
    }
  }, [current]);

  useEffect(() => {
    if (currentRowOrder && currentRowOrder[current]) {
      setCurrentRow(currentRowOrder[current]);
      setSelectedRows([currentRowOrder[current]]);
      const fileToUse =
        drawingFiles?.[currentRowOrder[current]?.id]?.original ||
        packageFiles?.[currentRowOrder[current]?.package_id] ||
        null;
      setFile(fileToUse);
      setFileType(selectedItem.has_annotated ? "ANNOTATED" : "ORIGINAL");
    } else {
      setCurrentRow(selectedItem);
      setSelectedRows([selectedItem]);
      const fileToUse =
        drawingFiles?.[selectedItem?.id]?.original ||
        packageFiles?.[selectedItem?.package_id] ||
        null;
      setFile(fileToUse);
      setFileType(selectedItem.has_annotated ? "ANNOTATED" : "ORIGINAL");
    }
  }, [selectedItem.id]);

  useEffect(() => {
    if (itemsGridOptionsApi) {
      itemsGridOptionsApi.setRowData(items);
    }
  }, [itemsGridOptionsApi, items]);

  useEffect(() => {
    if (itemsView) {
      dispatch(
        handleFetchItems(
          [currentRow.job_id],
          [currentRow.package_id],
          [currentRow.id],
          null,
          true
        )
      );
    }
  }, [currentRow]);

  const handleNext = useCallback(() => {
    if (currentRowOrder) {
      setCurrent(current + 1);
    }
  }, [selectedItem.id, currentRowOrder, current]);

  const handlePrevious = useCallback(() => {
    if (currentRowOrder) {
      setCurrent(current - 1);
    }
  }, [selectedItem.id, currentRowOrder, current]);

  const handleForgeClick = () => {
    setFile(currentRow.forge_urn);
    toggleForge(true);
  };

  const updateRow = (updatedInfo) => {
    const newSelectedItem = {
      ...selectedItem,
      ...updatedInfo,
    };
    setSelectedItem(newSelectedItem);
    setCurrentRow(newSelectedItem);
    currentRowOrder[current] = newSelectedItem;
    setSelectedRows([newSelectedItem]);
    setDisplayedPdf(updatedInfo);

    if (gridOptionsApi) {
      const rowNode = gridOptionsApi.getRowNode(newSelectedItem.id);
      if (rowNode) rowNode.setData(newSelectedItem);
    }
  };

  const switchPdfOrItems = () => {
    const newItemsViewValue = !itemsView;
    if (newItemsViewValue === true) {
      dispatch(
        handleFetchItems(
          [currentRow.job_id],
          [currentRow.package_id],
          [currentRow.id],
          null,
          true
        )
      );
    } else {
      dispatch(handleClearItemsState);
    }

    toggleItemsView(newItemsViewValue);
  };

  const sendToEditItems = () => {
    window.location.assign(
      `${process.env.REACT_APP_FABPRO}/jobs/?job_id=${currentRow.job_id}&package_id=${currentRow.package_id}&drawing_id=${currentRow.id}&view_items=1`
    );
  };

  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };

  const gridOptions = {
    rowData: items,
    columnDefs: columnDefs(features),
    rowClassRules,
    suppressRowClickSelection: true,
    defaultColDef: {
      cellClass: ["no-border", "custom-wrap"],
      wrapText: true,
      autoHeight: true,
      flex: 1,
    },
    pagination: true,
    paginationPageSize: 100,
    getRowNodeId: (data) => data.id,
    onGridReady: (params) => setItemsGridOptionsApi(params.api),
  };

  return (
    <Modal
      open={allInOne}
      handleClose={() => {
        if (showAssignments || showSubmitRevision) return;
        toggleAllInOne();
      }}
    >
      <div
        className={`all-in-one-wrapper ${fullscreen ? "fullscreen" : ""} ${
          navExpanded ? "narrow" : ""
        }`}
      >
        {currentRowOrder && (
          <div className="traversal-buttons">
            {current > 0 ? (
              <Button
                onClick={handlePrevious}
                id="previous"
                disabled={pdfViewerLoading}
              >
                <FaArrowLeft />
                Previous
              </Button>
            ) : (
              <div id="previous"></div>
            )}
            <span>
              {currentRow.job_title} / {currentRow.package_name} /{" "}
              {currentRow.name}
            </span>
            {current < currentRowOrder.length - 1 ? (
              <Button
                onClick={handleNext}
                id="next"
                disabled={pdfViewerLoading}
              >
                Next
                <FaArrowRight />
              </Button>
            ) : (
              <div id="next"></div>
            )}
          </div>
        )}
        <div className="pdf-or-items-tabs">
          <div
            onClick={() => {
              if (itemsView) {
                switchPdfOrItems();
              }
            }}
            className={itemsView ? "" : "selected"}
          >
            Viewer
          </div>
          <div
            onClick={() => {
              if (!itemsView) {
                switchPdfOrItems();
              }
            }}
            className={itemsView ? "selected" : ""}
          >
            Item List
          </div>
        </div>
        {itemsView ? (
          <AgTable gridOptions={gridOptions} />
        ) : !showForge ? (
          <div className="viewer">
            <PSPDFKitViewer
              selectedItem={currentRow}
              itemId={currentRow.id}
              itemType="DRAWING"
              file={file}
              showAnnotations={fileType === "ANNOTATED"}
              area={area}
              token={token}
              userInfo={userInfo}
              refresh={refresh}
              hasPermission={
                !/map\.pdf/.test(file) ? permissions?.includes(111) : false
              }
              isArchived={currentRow.archived ? true : false}
              onLoadStarted={() => togglePdfViewerLoading(true)}
              onLoadSuccess={() => togglePdfViewerLoading(false)}
              onLoadFailed={() => togglePdfViewerLoading(false)}
              fileType={fileType}
            />
            <button
              className="fullscreen-button"
              onClick={() => toggleFullscreen(!fullscreen)}
            >
              {fullscreen ? <IoMdContract /> : <IoMdExpand />}
            </button>
          </div>
        ) : (
          <div className="viewer forge-viewer">
            <ForgeViewer
              appType="fab"
              token={token}
              userId={userId}
              systemSettings={systemSettings}
              permissions={permissions}
              origin="web"
              spool={{ ...currentRow, type: "DRAWINGS" }}
              useModalLayout={true}
              navExpanded={navExpanded}
              area={area}
              userInfo={userInfo}
              refresh={refresh}
            />
            <button
              className="fullscreen-button"
              onClick={() => toggleFullscreen(!fullscreen)}
            >
              {fullscreen ? <IoMdContract /> : <IoMdExpand />}
            </button>
          </div>
        )}
        <div className="file-type">
          {itemsView ? (
            <Button className="edit-items-link" onClick={sendToEditItems}>
              Edit Items
            </Button>
          ) : (
            <>
              {currentRow && currentRow.has_original !== 0 && (
                <Button
                  onClick={() => {
                    setFile(drawingFiles[currentRow.id]?.original);
                    setFileType("ORIGINAL");
                  }}
                  className={
                    fileType === "ORIGINAL" && !/map\.pdf/.test(file)
                      ? "selected"
                      : ""
                  }
                  disabled={fileType === "ORIGINAL" && !/map\.pdf/.test(file)}
                >
                  Original
                </Button>
              )}
              {currentRow && currentRow.has_annotated !== 0 && (
                <Button
                  onClick={() => {
                    setFile(drawingFiles[currentRow.id]?.original);
                    setFileType("ANNOTATED");
                  }}
                  className={
                    fileType === "ANNOTATED" && !/map\.pdf/.test(file)
                      ? "selected"
                      : ""
                  }
                  disabled={fileType === "ANNOTATED" && !/map\.pdf/.test(file)}
                >
                  Annotated
                </Button>
              )}
              {currentRow && currentRow.has_package_map && (
                <Button
                  onClick={() => {
                    const itemId = currentRow?.package_id;
                    setFile(packageFiles[itemId]);
                    toggleForge(false);
                  }}
                  className={/map\.pdf/.test(file) ? "selected" : ""}
                  disabled={/map\.pdf/.test(file)}
                >
                  Package Map
                </Button>
              )}

              {currentRow && currentRow.forge_urn && (
                <Button
                  onClick={handleForgeClick}
                  className={showForge ? "selected" : ""}
                  disabled={showForge}
                >
                  3D Model
                </Button>
              )}
            </>
          )}
        </div>
        <div className="pending-approval-action-buttons">
          {viewSentBack ? (
            <>
              {permissions?.includes(112) && (
                <Button
                  className="approve-selected"
                  onClick={() => sendBackOrResubmitSelected(true, false)}
                >
                  Resubmit Selected
                </Button>
              )}
            </>
          ) : (
            <>
              {permissions?.includes(91) && (
                <Button
                  className="approve-selected"
                  onClick={() => approveSelected(false, false, false, false)}
                >
                  Approve Selected
                </Button>
              )}
              {permissions?.includes(94) && (
                <Button
                  className="send-back-selected"
                  onClick={() => sendBackOrResubmitSelected(false, false)}
                >
                  Send Back Selected
                </Button>
              )}
            </>
          )}
          <Button
            className="manage-selected"
            onClick={() => toggleAssignments(true)}
          >
            Assign Personnel
          </Button>
          <Button
            className="manage-selected"
            onClick={() => toggleSubmitRevision(true)}
          >
            Submit a Revision
          </Button>
        </div>
      </div>
      {showAssignments && (
        <AssignmentsModal
          open={showAssignments}
          handleClose={() => toggleAssignments(false)}
          selectedItem={selectedItem}
          currentTable="DRAWINGS"
        />
      )}
      {showSubmitRevision && (
        <SubmitRevisionModal
          handleClose={() => toggleSubmitRevision(false)}
          submitRevisionInfo={selectedItem}
          updateRow={updateRow}
        />
      )}
    </Modal>
  );
};

export default AllInOneModal;
