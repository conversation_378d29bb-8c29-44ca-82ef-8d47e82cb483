import { naturalSort, objectColumnDefs } from "../../../_utils";

export const columnDefs = (features = []) => {
  let columns = [
    {
      headerName: "Tag #",
      field: "tag_number",
      minWidth: 80,
      width: 100,
      getQuickFilterText: (params) => params.data.tag_number,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "tag_number",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Material",
      field: "material_name",
      lockVisible: true,
      getQuickFilterText: (params) => params.data.material_name,
      minWidth: 120,
      width: 140,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "material_name",
      autoHeight: true,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Joining Procedure",
      field: "joining_procedure_name",
      minWidth: 200,
      width: 200,
      getQuickFilterText: (params) => params.data.joining_procedure_name,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "joining_procedure_name",
      autoHeight: true,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Size",
      field: "size",
      minWidth: 80,
      width: 100,
      comparator: (valueA, valueB) => {
        if (!valueA) return -1;
        if (!valueB) return 1;
        return valueA.localeCompare(valueB, undefined, {
          numeric: true,
          sensitivity: "base",
        });
      },
      getQuickFilterText: (params) => params.data.size,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "size",
    },
    {
      headerName: "Length",
      field: "length",
      minWidth: 80,
      width: 100,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "length",
      filterValueGetter: objectColumnDefs.filterValueGetter("length"),
      valueFormatter: objectColumnDefs.valueFormatter("length"),
      getQuickFilterText: objectColumnDefs.getQuickFilterText("length"),
      comparator: objectColumnDefs.comparator("length"),
    },
    {
      headerName: "End Prep 1",
      field: "end_prep_1",
      minWidth: 120,
      width: 140,
      getQuickFilterText: (params) => params.data.end_prep_1,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "end_prep_1",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "End Prep 2",
      field: "end_prep_2",
      minWidth: 120,
      width: 140,
      getQuickFilterText: (params) => params.data.end_prep_2,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "end_prep_2",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "End Prep 3",
      field: "end_prep_3",
      minWidth: 120,
      width: 140,
      getQuickFilterText: (params) => params.data.end_prep_3,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "end_prep_3",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "End Prep 4",
      field: "end_prep_4",
      minWidth: 120,
      width: 140,
      getQuickFilterText: (params) => params.data.end_prep_4,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "end_prep_4",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
  ];

  if (features.includes(37) || features.includes(38))
    columns.push(
      {
        headerName: "Bend Angle 1",
        field: "bend_angle_1",
        minWidth: 80,
        width: 100,
        getQuickFilterText: (params) => params.data.bend_angle_1,
        filter: "agNumberColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_angle_1",
      },
      {
        headerName: "Bend Angle 2",
        field: "bend_angle_2",
        minWidth: 80,
        width: 100,
        getQuickFilterText: (params) => params.data.bend_angle_2,
        filter: "agNumberColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_angle_2",
      },
      {
        headerName: "Bend Deduct",
        field: "bend_deduct",
        minWidth: 80,
        width: 100,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_deduct",
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_deduct"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_deduct"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_deduct"),
        comparator: objectColumnDefs.comparator("bend_deduct"),
      },
      {
        headerName: "Bend Dim A",
        field: "bend_dim_a",
        minWidth: 80,
        width: 100,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_dim_a",
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_dim_a"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_dim_a"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_dim_a"),
        comparator: objectColumnDefs.comparator("bend_dim_a"),
      },
      {
        headerName: "Bend Dim B",
        field: "bend_dim_b",
        minWidth: 80,
        width: 100,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_dim_b",
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_dim_b"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_dim_b"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_dim_b"),
        comparator: objectColumnDefs.comparator("bend_dim_b"),
      },
      {
        headerName: "Bend Dim C",
        field: "bend_dim_c",
        minWidth: 80,
        width: 100,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_dim_c",
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_dim_c"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_dim_c"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_dim_c"),
        comparator: objectColumnDefs.comparator("bend_dim_c"),
      },
      {
        headerName: "Bend Dim D",
        field: "bend_dim_d",
        minWidth: 80,
        width: 100,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_dim_d",
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_dim_d"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_dim_d"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_dim_d"),
        comparator: objectColumnDefs.comparator("bend_dim_d"),
      },
      {
        headerName: "Bend Dim E",
        field: "bend_dim_e",
        minWidth: 80,
        width: 100,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_dim_e",
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_dim_e"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_dim_e"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_dim_e"),
        comparator: objectColumnDefs.comparator("bend_dim_e"),
      },
      {
        headerName: "Bend Dim F",
        field: "bend_dim_f",
        minWidth: 80,
        width: 100,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_dim_f",
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_dim_f"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_dim_f"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_dim_f"),
        comparator: objectColumnDefs.comparator("bend_dim_f"),
      },
      {
        headerName: "Bend Dim G",
        field: "bend_dim_g",
        minWidth: 80,
        width: 100,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_dim_g",
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_dim_g"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_dim_g"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_dim_g"),
        comparator: objectColumnDefs.comparator("bend_dim_g"),
      },
      {
        headerName: "Bend Mark 1",
        field: "bend_mark_1",
        minWidth: 80,
        width: 100,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_mark_1",
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_mark_1"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_mark_1"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_mark_1"),
        comparator: objectColumnDefs.comparator("bend_mark_1"),
      },
      {
        headerName: "Bend Mark 2",
        field: "bend_mark_2",
        minWidth: 80,
        width: 100,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_mark_2",
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_mark_2"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_mark_2"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_mark_2"),
        comparator: objectColumnDefs.comparator("bend_mark_2"),
      },
      {
        headerName: "Bender Type",
        field: "bend_bender_type",
        minWidth: 80,
        width: 100,
        getQuickFilterText: (params) => params.value,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_bender_type",
      },
      {
        headerName: "Bend Type ID",
        field: "bend_type_id",
        minWidth: 80,
        width: 100,
        getQuickFilterText: (params) => params.data.bend_type_id,
        filter: "agNumberColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_type_id",
      }
    );

  return columns;
};
