import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockAdapter from "axios-mock-adapter";
import thunk from "redux-thunk";

import {
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  handleFetchPackagesPendingApproval,
  handleApprovePackagesPendingApproval,
  handleSendBackPackagesPendingApproval,
  handleFetchPackagesSentBack,
  handleResubmitPackagesSentBack,
} from "./packagesPendingApprovalActions";
import { columnDefs } from "./packagesPendingApprovalConstants";

describe("Packages Pending Approval", () => {
  const testError = (type, message) => ({
    error: {
      status: 400,
      message: `${message ? message : `No ${type} found.`}`,
    },
  });

  describe("action handlers should perform the necessary functions", () => {
    let store;
    let httpMock;

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    const testPackages = [
      {
        user_id: 158,
        created_on: 1595269806,
        package_id: 1700,
        fp_budget: 0,
        package_name: "20+ stages",
        due_date: 1595894400,
        job_number: "231123",
        job_name: "jk test save items 2",
        job_id: 922,
        full_name: "Jessica1 Karpovich",
        flow_name: "EC Basic Flow",
        flow_id: 27,
        flow_active: 0,
        drawing_count: 36,
        emails: "<EMAIL>,<EMAIL>",
        reset_needed: 0,
        approved: 0,
        package_map: "",
        forge_models:
          '[{"forge_model_id": 164, "forge_urn": "dXJuOmFkc2sud2lwcHJvZDpmcy5maWxlOnZmLktHSVlBcnpBU1Q2XzVGeThINGlaU2c_dmVyc2lvbj0xMQ", "model_name": "(Save2)2018 - Demo - Mech Pipe2018.rvt"}]',
      },
      {
        user_id: 119,
        created_on: 1595594998,
        package_id: 1712,
        fp_budget: 0,
        package_name: "P2 - archi ve",
        due_date: 1588204800,
        job_number: "0594-23234",
        job_name: "EC test fab using new sprocs",
        job_id: 925,
        full_name: "Emerald Cheney",
        flow_name: "EC test flow response - 1",
        flow_id: 1,
        flow_active: 0,
        drawing_count: 2,
        emails: "<EMAIL>",
        reset_needed: 0,
        approved: 0,
        package_map: "",
        forge_models: null,
      },
    ];

    it("handleFetchPackagesPendingApproval fetches all packages pending approval or where assigned", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/packages/pending-approval`)
        .replyOnce(200, testPackages)
        .onGet(
          `${process.env.REACT_APP_API}/packages/pending-approval?is_assigned=1`
        )
        .replyOnce(200, testPackages[1])
        .onGet(`${process.env.REACT_APP_API}/packages/pending-approval`)
        .replyOnce(404, testError("packages pending approval"));

      await store
        .dispatch(handleFetchPackagesPendingApproval(false))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("PACKAGES_PENDING_APPROVAL"),
            receiveSucceeded("PACKAGES_PENDING_APPROVAL", testPackages),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testPackages);

          store.clearActions();
        });

      await store
        .dispatch(handleFetchPackagesPendingApproval(true))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("PACKAGES_PENDING_APPROVAL"),
            receiveSucceeded("PACKAGES_PENDING_APPROVAL", testPackages[1]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testPackages[1]);

          store.clearActions();
        });

      return store
        .dispatch(handleFetchPackagesPendingApproval(false))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("PACKAGES_PENDING_APPROVAL"),
            receiveFailed(
              "PACKAGES_PENDING_APPROVAL",
              testError("packages pending approval").error
            ),
          ];

          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleApprovePackagesPendingApproval approves packages pending approval", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/packages/approve-work`)
        .replyOnce(200, { succeeded: "2 packages", failed: "0 packages" })
        .onPut(`${process.env.REACT_APP_API}/packages/approve-work`)
        .replyOnce(200, { succeeded: "0 packages", failed: "2 packages" });

      await store
        .dispatch(handleApprovePackagesPendingApproval([1, 2], false))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveSucceeded("PACKAGES_PENDING_APPROVAL_APPROVED", [1, 2]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[0].payload).toEqual([1, 2]);

          store.clearActions();
        });

      return store
        .dispatch(handleApprovePackagesPendingApproval([1, 2], false))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [];

          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleSendBackPackagesPendingApproval approves packages pending approval", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/packages/send-back`)
        .replyOnce(200, { state: "success" })
        .onPut(`${process.env.REACT_APP_API}/packages/send-back`)
        .replyOnce(200, testError("send back packages"));

      await store
        .dispatch(handleSendBackPackagesPendingApproval([1, 2], {}))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveSucceeded("PACKAGES_PENDING_APPROVAL_SENT_BACK", [1, 2]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[0].payload).toEqual([1, 2]);

          store.clearActions();
        });

      return store
        .dispatch(handleSendBackPackagesPendingApproval([1, 2], {}))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [];

          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleFetchPackagesSentBack fetches all packages sent back or where assigned", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/packages/sent-back`)
        .replyOnce(200, testPackages)
        .onGet(`${process.env.REACT_APP_API}/packages/sent-back?is_assigned=1`)
        .replyOnce(200, testPackages[1])
        .onGet(`${process.env.REACT_APP_API}/packages/sent-back`)
        .replyOnce(404, testError("packages sent back"));

      await store.dispatch(handleFetchPackagesSentBack(false)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("PACKAGES_SENT_BACK"),
          receiveSucceeded("PACKAGES_SENT_BACK", testPackages),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testPackages);

        store.clearActions();
      });

      await store.dispatch(handleFetchPackagesSentBack(true)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("PACKAGES_SENT_BACK"),
          receiveSucceeded("PACKAGES_SENT_BACK", testPackages[1]),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testPackages[1]);

        store.clearActions();
      });

      return store.dispatch(handleFetchPackagesSentBack(false)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("PACKAGES_SENT_BACK"),
          receiveFailed(
            "PACKAGES_SENT_BACK",
            testError("packages sent back").error
          ),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleResubmitPackagesSentBack approves packages pending approval", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/packages/resubmit`)
        .replyOnce(200, { state: "success" })
        .onPut(`${process.env.REACT_APP_API}/packages/resubmit`)
        .replyOnce(200, testError("send back packages"));

      await store
        .dispatch(handleResubmitPackagesSentBack([1, 2], {}))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveSucceeded("PACKAGES_SENT_BACK_RESUBMITTED", [1, 2]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[0].payload).toEqual([1, 2]);

          store.clearActions();
        });

      return store
        .dispatch(handleResubmitPackagesSentBack([1, 2], {}))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [];

          expect(receivedActions).toEqual(expectedActions);
        });
    });
  });
  describe("Packages Pending Approval Column Defs", () => {
    const defaultHeaders = [
      "",
      "Job #",
      "Job Name",
      "Package ID",
      "Package Name",
      "Uploaded By",
      "First Added",
      "Due Date",
      "Estimated Time",
      "Flow",
      "# of Pending Drawings",
      "Manage",
    ];
    const sortState = { sorting_column_name: "id", sorting_method: "asc" };
    const testStore = {
      getState: () => ({ profileData: testStore.profileData }),
      profileData: {
        systemSettings: {
          date_display: "MM-DD-YYYY",
          timezone: "America/Chicago",
        },
        permissions: [279, 280, 281],
      },
    };

    const drawingCountClick = jest.fn();
    const moreInfoClick = jest.fn();
    const toggleMoreInfo = jest.fn();

    let populatedColumns;

    beforeEach(() => {
      populatedColumns = columnDefs(
        drawingCountClick,
        moreInfoClick,
        toggleMoreInfo,
        null,
        sortState,
        testStore
      );
    });

    it("Packages headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);
      expect(columnHeaders).toEqual(defaultHeaders);
    });

    describe("JOB #", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Job #");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            job_number: "123456",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("123456");
      });
    });

    describe("JOB NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Job Name");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            job_name: "test job",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test job");
      });
    });

    describe("PACKAGE NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Package Name");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            package_name: "test job",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test job");
      });
    });

    describe("UPLOADED BY", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Uploaded By");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            full_name: "test user",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test user");
      });
    });

    describe("FIRST ADDED", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "First Added");
      });
      it("valueGetter returns either created_on or drawing_created_on", () => {
        const params1 = {
            data: {
              created_on: 1645651408,
            },
          },
          params2 = {
            data: {
              drawing_created_on: 1645651408,
            },
          };
        expect(column.valueGetter(params1)).toEqual(1645651408);
        expect(column.valueGetter(params2)).toEqual(1645651408);
      });
      it("valueFormatter should display the value in a user friendly format", () => {
        const params = {
          value: 1645651408,
        };
        expect(column.valueFormatter(params)).toEqual("02-23-2022");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          valueFormatted: "02-23-2022",
        };
        expect(column.getQuickFilterText(params)).toEqual("02-23-2022");
      });
    });

    describe("DUE DATE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Due Date");
      });
      it("valueFormatter should display the value in a user friendly format", () => {
        const params = {
          value: 1645651408,
        };
        expect(column.valueFormatter(params)).toEqual("02-23-2022");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          valueFormatted: "02-23-2022",
        };
        expect(column.getQuickFilterText(params)).toEqual("02-23-2022");
      });
    });

    describe("ESTIMATED TIME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Estimated Time"
        );
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          value: 10.22,
        };
        expect(column.getQuickFilterText(params)).toEqual("10.22");
      });
    });

    describe("FLOW", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Flow");
      });
      // it("valueFormatter should return the name of the flow", () => {
      //   const params = {
      //     data: { flow_name: "Test Flow" },
      //   };
      // });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: { flow_name: "Test Flow" },
        };
        expect(column.getQuickFilterText(params)).toEqual("Test Flow");
      });
    });

    describe("# OF PENDING DRAWINGS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "# of Pending Drawings"
        );
      });
      it("valueGetter", () => {
        const params = {
          data: {
            drawing_count: 10,
          },
        };
        const noCountParams = {
          data: {},
        };
        expect(column.valueGetter(params)).toEqual(10);
        expect(column.valueGetter(noCountParams)).toEqual(0);
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            drawing_count: 10,
          },
        };
        const noCountParams = {
          data: {},
        };
        expect(column.getQuickFilterText(params)).toEqual("10 Drawings");
        expect(column.getQuickFilterText(noCountParams)).toEqual("0 Drawings");
      });
    });

    describe("MANAGE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Manage");
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            moreInfoClick,
            toggleMoreInfo,
          },
        };
        expect(column.valueFormatter(params)).toEqual({
          moreInfoClick,
          toggleMoreInfo,
        });
      });
    });
  });
});
