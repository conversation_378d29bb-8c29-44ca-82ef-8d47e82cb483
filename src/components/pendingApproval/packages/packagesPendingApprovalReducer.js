const initialState = {
  isLoading: false,
  error: null,
  packagesPendingApproval: null,
  savedColumnState: null,
  packagesSentBack: null,
  fetchIsApprovalPending: {
    isLoading: false,
    isApprovalPending: false,
    error: null,
  },
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_PACKAGES_PENDING_APPROVAL_STARTED":
      return {
        ...state,
        isLoading: true,
        error: null,
        packagesPendingApproval: null,
      };
    case "RECEIVE_PACKAGES_PENDING_APPROVAL_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        packagesPendingApproval: payload,
      };
    case "RECEIVE_PACKAGES_PENDING_APPROVAL_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        packagesPendingApproval: [],
      };
    case "RECEIVE_PACKAGES_PENDING_APPROVAL_APPROVED_SUCCEEDED":
      return {
        ...state,
        packagesPendingApproval: state.packagesPendingApproval.filter(
          (p) => !payload.includes(p.package_id)
        ),
      };
    case "RECEIVE_PACKAGES_PENDING_APPROVAL_SENT_BACK_SUCCEEDED":
      return {
        ...state,
        packagesPendingApproval: state.packagesPendingApproval.filter(
          (p) => !payload.includes(p.package_id)
        ),
      };
    case "CLEAR_PACKAGES_PENDING_APPROVAL_STATE":
      return initialState;
    case "RECEIVE_PACKAGES_PENDING_APPROVAL_COLUMN_STATE_STARTED":
      return { ...state, savedColumnState: null };
    case "RECEIVE_PACKAGES_PENDING_APPROVAL_COLUMN_STATE_SUCCEEDED":
      return { ...state, savedColumnState: payload };
    case "RECEIVE_PACKAGES_PENDING_APPROVAL_COLUMN_STATE_FAILED":
      return { ...state, savedColumnState: [] };
    case "RECEIVE_PACKAGES_SENT_BACK_STARTED":
      return {
        ...state,
        isLoading: true,
        error: null,
        packagesSentBack: null,
      };
    case "RECEIVE_PACKAGES_SENT_BACK_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        packagesSentBack: payload,
      };
    case "RECEIVE_PACKAGES_SENT_BACK_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        packagesSentBack: [],
      };
    case "RECEIVE_PACKAGES_SENT_BACK_COLUMN_STATE_STARTED":
      return { ...state, savedColumnState: null };
    case "RECEIVE_PACKAGES_SENT_BACK_COLUMN_STATE_SUCCEEDED":
      return { ...state, savedColumnState: payload };
    case "RECEIVE_PACKAGES_SENT_BACK_COLUMN_STATE_FAILED":
      return { ...state, savedColumnState: [] };
    case "RECEIVE_PACKAGES_SENT_BACK_RESUBMITTED_SUCCEEDED":
      return {
        ...state,
        packagesSentBack: state.packagesSentBack.filter(
          (p) => !payload.includes(p.package_id)
        ),
      };
    case "FETCH_PENDING_APPROVAL_REQUEST":
      return {
        ...state,
        fetchIsApprovalPending: {
          isLoading: true,
          isApprovalPending: false,
          error: null,
        },
      };

    case "FETCH_PENDING_APPROVAL_SUCCESS":
      return {
        ...state,
        fetchIsApprovalPending: {
          isLoading: false,
          isApprovalPending: payload,
          error: null,
        },
      };

    case "FETCH_PENDING_APPROVAL_FAILURE":
      return {
        ...state,
        fetchIsApprovalPending: {
          isLoading: false,
          isApprovalPending: false,
          error: payload,
        },
      };

    default:
      return state;
  }
}
