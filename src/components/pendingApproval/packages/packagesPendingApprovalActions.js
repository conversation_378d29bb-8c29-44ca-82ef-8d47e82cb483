import {
  fetchPackagesPendingApproval,
  approvePackagesPendingApproval,
  sendBackPackagesPendingApproval,
  fetchPackagesSentBack,
  resubmitPackagesSentBack,
  fetchIsApprovalExists,
} from "../../../_services";

export const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const handleClearPackagesPendingApprovalState = (dispatch) =>
  dispatch({ type: "CLEAR_PACKAGES_PENDING_APPROVAL_STATE" });

export const handleFetchPackagesPendingApproval = (isAssigned = true) => (
  dispatch
) => {
  const type = "PACKAGES_PENDING_APPROVAL";

  dispatch(receiveStarted(type));
  return fetchPackagesPendingApproval(isAssigned).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleApprovePackagesPendingApproval = (
  packageIds = [],
  resetDrawings = false
) => (dispatch) => {
  const type = "PACKAGES_PENDING_APPROVAL_APPROVED";
  return approvePackagesPendingApproval(packageIds, resetDrawings).then(
    (res) => {
      if (!res.error && !/^[1-9]/.test(res.failed))
        dispatch(receiveSucceeded(type, packageIds));

      return res;
    }
  );
};

export const handleSendBackPackagesPendingApproval = (
  packageIds = [],
  emailInfo = {}
) => (dispatch) => {
  const type = "PACKAGES_PENDING_APPROVAL_SENT_BACK";
  return sendBackPackagesPendingApproval(packageIds, emailInfo).then((res) => {
    if (!res.error && res.state === "success")
      dispatch(receiveSucceeded(type, packageIds));

    return res;
  });
};

export const handleFetchPackagesSentBack = (isAssigned = true) => (
  dispatch
) => {
  const type = "PACKAGES_SENT_BACK";

  dispatch(receiveStarted(type));
  return fetchPackagesSentBack(isAssigned).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleResubmitPackagesSentBack = (
  packageIds = [],
  emailInfo = {}
) => (dispatch) => {
  const type = "PACKAGES_SENT_BACK_RESUBMITTED";
  return resubmitPackagesSentBack(packageIds, emailInfo).then((res) => {
    if (!res.error && res.state === "success")
      dispatch(receiveSucceeded(type, packageIds));

    return res;
  });
};

export const fetchIsPendingApprovalRequest = () => ({
  type: "FETCH_PENDING_APPROVAL_REQUEST",
});

export const fetchIsPendingApprovalSuccess = (payload) => ({
  type: "FETCH_PENDING_APPROVAL_SUCCESS",
  payload,
});

export const fetchIsPendingApprovalFailure = (payload) => ({
  type: "FETCH_PENDING_APPROVAL_FAILURE",
  payload,
});

export const handleFetchApprovalsPending = () => (dispatch) => {
  dispatch(fetchIsPendingApprovalRequest);
  return fetchIsApprovalExists()
    .then((res) => {
      if (res) {
        dispatch(fetchIsPendingApprovalSuccess(res.hasPendingApprovals));
        return res;
      }
    })
    .catch((error) => {
      dispatch(fetchIsPendingApprovalFailure(error?.message));
    });
};
