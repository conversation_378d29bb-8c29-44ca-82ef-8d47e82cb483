// REDUX IMPORTS
import store from "../../../redux/store";

// HELPER FUNCTION IMPORTS
import { generateTime, naturalSort } from "../../../_utils";

// EXPORTS
export const columnDefs = (
  handleDrawingCountClick,
  moreInfoClick,
  toggleMoreInfo,
  savedColumnState,
  sortState,
  permissions,
  testStore = null
) => {
  const { systemSettings } = (testStore || store).getState().profileData;
  let dateFormatting = systemSettings && systemSettings.date_display;

  let defaultDefs = [
    {
      headerName: "",
      headerCheckboxSelection: true,
      headerCheckboxSelectionFilteredOnly: true,
      minWidth: 50,
      width: 50,
      checkboxSelection: true,
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      colId: "checkbox",
    },
    {
      headerName: "Job #",
      field: "job_number",
      minWidth: 80,
      width: 100,
      getQuickFilterText: (params) => params.data.job_number,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "job_number",
      sort:
        sortState.sorting_column_name === "job_number"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Job Name",
      field: "job_name",
      getQuickFilterText: (params) => params.data.job_name,
      valueFormatter: (params) => ({
        wizardPermission:
          permissions &&
          (permissions.includes(279) ||
            permissions.includes(280) ||
            permissions.includes(281)),
      }),
      cellRenderer: "jobNameCellRenderer",
      minWidth: 120,
      width: 140,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "job_name",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "job_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Package ID",
      field: "package_id",
      getQuickFilterText: (params) => params.data.package_id,
      minWidth: 120,
      resizable: true,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_id",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "package_id"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Package Name",
      field: "package_name",
      getQuickFilterText: (params) => params.data.package_name,
      valueFormatter: (params) => ({
        wizardPermission:
          permissions &&
          (permissions.includes(279) ||
            permissions.includes(280) ||
            permissions.includes(281)),
      }),
      cellRenderer: "packageNameCellRenderer",
      minWidth: 120,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_name",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "package_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Uploaded By",
      field: "full_name",
      getQuickFilterText: (params) => params.data.full_name,
      minWidth: 120,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "full_name",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "full_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "First Added",
      valueGetter: (params) =>
        params.data.created_on || params.data.drawing_created_on,
      valueFormatter: (params) => {
        return generateTime(params.value * 1000, false, true, "+");
      },
      getQuickFilterText: (params) => params.valueFormatted,
      minWidth: 120,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "created_on",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "created_on"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Due Date",
      field: "due_date",
      valueFormatter: (params) => {
        return generateTime(params.value * 1000, false, true, "-");
      },
      valueParser: (params) => {
        if (
          params.newValue === "" ||
          params.newValue === undefined ||
          generateTime(params.value * 1000, false, true, "-", testStore) ===
            params.newValue
        )
          return params.oldValue;

        const defaultRegex = new RegExp("\\d{2}-\\d{2}-\\d{4}");
        const dateFormattingRegex = dateFormatting
          ? /-/.test(dateFormatting)
            ? new RegExp(
                dateFormatting
                  .split("-")
                  .map((part) => `\\d{${part.length}}`)
                  .join("\\-")
              )
            : /\//.test(dateFormatting)
            ? new RegExp(
                dateFormatting
                  .split("/")
                  .map((part) => `\\d{${part.length}}`)
                  .join("\\/")
              )
            : defaultRegex
          : defaultRegex;

        if (!dateFormattingRegex.test(params.newValue)) {
          return params.oldValue;
        } else {
          return (
            new Date(
              generateTime(new Date(params.newValue).getTime(), false)
            ).getTime() / 1000
          );
        }
      },
      getQuickFilterText: (params) => params.valueFormatted,
      editable: true,
      cellEditor: "dueDateEditorRenderer",
      minWidth: 120,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = cellValue
            ? typeof cellValue === "number"
              ? new Date(
                  generateTime(cellValue * 1000, false, true, "-", testStore)
                )
              : new Date(cellValue)
            : "-";

          return cellDate < filterLocalDateAtMidnight
            ? -1
            : cellDate > filterLocalDateAtMidnight
            ? 1
            : 0;
        },
      },
      menuTabs: ["filterMenuTab"],
      colId: "due_date",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "due_date"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Estimated Time",
      field: "fp_budget",
      getQuickFilterText: (params) =>
        params.value ? params.value.toString() : "",
      minWidth: 120,
      resizable: true,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "fp_budget",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "fp_budget"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Flow",
      field: "flow_id",
      valueFormatter: (params) => params.data.flow_name,
      getQuickFilterText: (params) => params.data.flow_name,
      minWidth: 120,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "flow_id",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "flow_id"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB, nodeA, nodeB) =>
        nodeA.data.flow_name
          ? nodeB.data.flow_name
            ? naturalSort(nodeA.data.flow_name, nodeB.data.flow_name)
            : -1
          : 1,
    },
    {
      headerName: "# of Pending Drawings",
      valueGetter: (params) => params.data.drawing_count || 0,
      getQuickFilterText: (params) =>
        `${params.data.drawing_count || 0} Drawings`,
      minWidth: 120,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "drawing_count",
      autoHeight: true,
      cellRenderer: "childCellRenderer",
      cellRendererParams: {
        handleChildSelection: handleDrawingCountClick,
        type: "DRAWINGS",
      },
      sort:
        sortState.sorting_column_name === "drawing_count"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB, nodeA, nodeB) => {
        return (
          (nodeA.data.drawing_count || 0) - (nodeB.data.drawing_count || 0)
        );
      },
    },
    {
      headerName: "Manage",
      sortable: false,
      width: 60,
      minWidth: 60,
      valueFormatter: (params) => {
        return {
          moreInfoClick,
          toggleMoreInfo,
        };
      },
      pinned: "right",
      cellRenderer: "moreInfoCellRenderer",
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      lockVisible: true,
      suppressMovable: true,
      colId: "manage",
    },
  ];

  if (savedColumnState && savedColumnState.length) {
    for (let i = 0; i < defaultDefs.length; i++) {
      let savedDef = savedColumnState.find(
        (c) => c.header_name === defaultDefs[i].headerName
      );
      if (savedDef) {
        defaultDefs[i] = {
          ...defaultDefs[i],
          pinned: savedDef.pinned,
          hide: savedDef.visible ? false : true,
          position: savedDef.position,
        };
      }
    }
    return defaultDefs
      .sort((a, b) => {
        if (a.position === b.position) {
          if (a.headerName.toLowerCase() > b.headerName.toLowerCase()) return 1;
          else return -1;
        } else return a.position - b.position;
      })
      .map((col) => {
        if (col.position !== undefined) delete col.position;
        return col;
      });
  } else return defaultDefs;
};
