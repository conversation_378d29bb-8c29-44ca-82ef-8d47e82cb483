@import "../../styles/colors.scss";

div.drawings-without-items-confirmation-modal {
  background-color: #fff;
  width: 600px;
  max-height: calc(100vh - 200px);
  padding: 15px;
  overflow-y: scroll;

  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 15px;
  row-gap: 15px;
  justify-items: center;

  & > h2,
  & > svg,
  & > p {
    grid-column: 1/-1;
  }

  & > svg {
    font-size: 6rem;
    color: $yellow;
  }

  & > h2 {
    font-weight: normal;
    margin: 0;
  }

  & > p > span {
    display: block;
    padding: 5px 15px;

    &.drawing-name {
      display: block;
      text-align: center;
    }
  }

  & > button {
    width: 150px;
    height: 40px;
    padding: 0;
    font-size: 0.8rem;
    background-color: $fieldProOrange;
    color: #fff;

    &:hover {
      background-color: darken($fieldProOrange, 10%);
    }
  }
}
