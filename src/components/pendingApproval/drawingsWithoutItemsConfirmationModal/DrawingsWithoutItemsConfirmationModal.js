// NPM PACKAGE IMPORTS
import React, { useMemo } from "react";
import Button from "msuite_storybook/dist/button/Button";
import Modal from "msuite_storybook/dist/modal/Modal";
import { BsExclamationCircle } from "react-icons/bs";

// STYLE IMPORTS
import "./stylesDrawingsWithoutItemsConfirmationModal.scss";

const DrawingsWithoutItemsConfirmationModal = ({
  showDrawingsWithoutItems,
  toggleShowDrawingsWithoutItems,
  drawingsWithoutItems,
  approveSelected,
  drawingsTable,
}) => {
  const packageList = useMemo(() => {
    const reducedDrawings = drawingsWithoutItems.reduce((acc, curr) => {
      if (!acc[curr.package_id])
        acc[curr.package_id] = {
          package_id: curr.package_id,
          package_name: curr.package_name,
          count: 0,
        };

      acc[curr.package_id].count++;

      return acc;
    }, {});

    return Object.values(reducedDrawings);
  }, [drawingsWithoutItems]);

  return (
    <Modal
      open={showDrawingsWithoutItems}
      handleClose={() => toggleShowDrawingsWithoutItems(false)}
    >
      <div className="drawings-without-items-confirmation-modal">
        <BsExclamationCircle />
        <h2 className="drawings-without-items-count">Are you sure?</h2>
        <p className="drawings-without-items-names">
          {drawingsTable
            ? `The following drawings in package ${packageList[0].package_name} have no items attached.`
            : packageList.map((p) => (
                <span key={p.package_id}>
                  {p.count} drawings in package <b>{p.package_name}</b> have no
                  items.
                </span>
              ))}
          {drawingsTable &&
            drawingsWithoutItems.slice(0, 10).map((d) => (
              <span key={d.id} className="drawing-name">
                <b>{d.name}</b>
              </span>
            ))}
          {drawingsTable && drawingsWithoutItems.length > 10 ? (
            <span className="drawing-name">{`along with ${
              drawingsWithoutItems.length - 10
            } other drawings.`}</span>
          ) : (
            <></>
          )}
        </p>
        <p className="drawings-without-items-disclaimer">
          Drawings that have no items will skip all item based stages.
        </p>
        <Button
          className="cancel"
          onClick={() => toggleShowDrawingsWithoutItems(false)}
        >
          Cancel
        </Button>
        <Button
          className={`submit restart`}
          onClick={() => approveSelected(true, false, true, true)}
        >
          Yes, approve selected
        </Button>
      </div>
    </Modal>
  );
};

export default DrawingsWithoutItemsConfirmationModal;
