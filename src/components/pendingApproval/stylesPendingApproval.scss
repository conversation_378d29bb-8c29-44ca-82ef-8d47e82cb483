@import "../styles/colors.scss";

div.pending-approval {
  display: grid;
  grid-template-rows: 30px 40px 40px 1fr;
  padding-top: 10px;
  overflow-y: scroll;
  height: calc(100vh - 150px);
  padding-left: 20px;
}

div.pending-approval-my-all-tabs,
div.pending-approval-incoming-sent-back-tabs {
  display: flex;
  column-gap: 10px;
  align-items: center;
}

div.pending-approval-my-all-tabs {
  border-bottom: 3px solid $lighterGrey;
}

div.my-pending-approvals,
div.all-pending-approvals,
div.incoming-pending-approvals,
div.sent-back-pending-approvals {
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: #fff;
  padding: 0 10px;
}

div.my-pending-approvals,
div.all-pending-approvals {
  cursor: pointer;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  transition: background-color 300ms ease;

  &.selected {
    background-color: #fff;
    color: #333;
  }

  &:not(.selected):hover {
    background-color: $darkFab;
  }
}

div.incoming-pending-approvals,
div.sent-back-pending-approvals {
  cursor: pointer;
  transition: background-color 300ms ease;

  &.selected {
    background-color: $darkFab;
  }

  &:not(.selected):hover {
    background-color: $darkFab;
  }
}

div.pending-approval-table {
  background-color: $darkFab;
  padding-bottom: 15px;
}

div.pending-approval-table > div.action-container {
  padding: 10px 0;
  margin: 0 auto 10px;
  width: 97.5%;
  display: grid;
  column-gap: 5px;
  border-bottom: 1px solid #fff;
  grid-template-columns: repeat(2, 165px) 250px 1fr;

  & > button {
    height: 30px;
    padding: 0 10px;
    font-size: 1rem;
    color: #fff;
  }

  & > button.approve-selected {
    background-color: $green;
    grid-column: 1/2;

    &:not(:disabled):hover {
      background-color: darken($green, 10%);
    }
  }

  & > button.send-back-selected {
    background-color: $redWashDark;
    grid-column: 2/3;

    &:not(:disabled):hover {
      background-color: darken($redWashDark, 10%);
    }
  }

  & > div {
    height: 30px;
    padding-left: 30px;
    grid-column: 3/4;
  }

  & > div > input {
    height: 30px;
    font-size: 1rem;
  }

  & > button.pending-download-majs {
    background-color: $fabProBlue;
    grid-column: 4/-1;

    &:not(:disabled):hover {
      background-color: darken($fabProBlue, 10%);
    }
  }

  & > button.push-right {
    justify-self: right;
  }
}

div.pending-approval-table
  > div.pending-approval-table-container
  > div.ag-theme-balham-dark.custom-ag-styles {
  width: 97.5%;
  height: calc(100vh - 110px - 230px);
  min-height: 300px;
}
