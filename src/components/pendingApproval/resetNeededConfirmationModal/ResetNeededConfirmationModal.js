// NPM PACKAGE IMPORTS
import React from "react";
import Button from "msuite_storybook/dist/button/Button";
import Modal from "msuite_storybook/dist/modal/Modal";
import { BsExclamationCircle } from "react-icons/bs";

// STYLE IMPORTS
import "./stylesResetNeededConfirmationModal.scss";

const ResetNeededConfirmationModal = ({
  showResetNeeded,
  toggleShowResetNeeded,
  resetNeeded,
  approveSelected,
  drawingsTable,
}) => (
  <Modal
    open={showResetNeeded}
    handleClose={() => toggleShowResetNeeded(false)}
  >
    <div className="reset-needed-confirmation-modal">
      <BsExclamationCircle />
      <h2 className="reset-needed-count">{`${resetNeeded.length} selected ${
        drawingsTable
          ? "drawing(s) are revisions"
          : "package(s) contain revised drawing(s)"
      }.`}</h2>
      <p className="reset-needed-names">
        {resetNeeded.map((r) => (
          <span key={drawingsTable ? r.id : r.package_id}>
            {drawingsTable
              ? `${r.name} has completed work.`
              : `${r.package_name} has drawing(s) with completed work.`}
          </span>
        ))}
      </p>
      <p className="reset-needed-confirmation">
        Would you like to restart these drawings, or keep previously completed
        work intact?
      </p>
      <Button className="cancel" onClick={() => toggleShowResetNeeded(false)}>
        Cancel
      </Button>
      <Button
        className={`submit restart`}
        onClick={() => approveSelected(true, true, true, false)}
      >
        Restart Work
      </Button>
      <Button
        className={`submit leave-work`}
        onClick={() => approveSelected(true, false, true, false)}
      >
        Leave Work Completed
      </Button>
    </div>
  </Modal>
);

export default ResetNeededConfirmationModal;
