import {
  fetchDrawingsPendingApproval,
  approveDrawingsPendingApproval,
  sendBackDrawingsPendingApproval,
  fetchDrawingsSentBack,
  resubmitDrawingsSentBack,
} from "../../../_services";

export const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const handleClearDrawingsPendingApprovalState = (dispatch) =>
  dispatch({ type: "CLEAR_DRAWINGS_PENDING_APPROVAL_STATE" });

export const handleFetchDrawingsPendingApproval = (
  packageId,
  isAssigned = true
) => (dispatch) => {
  const type = "DRAWINGS_PENDING_APPROVAL";

  dispatch(receiveStarted(type));
  return fetchDrawingsPendingApproval(packageId, isAssigned).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleApproveDrawingsPendingApproval = (
  drawingIds = [],
  resetWork = false
) => (dispatch) => {
  const type = "DRAWINGS_PENDING_APPROVAL_APPROVED";
  return approveDrawingsPendingApproval(drawingIds, resetWork).then((res) => {
    if (!res.error)
      dispatch(
        receiveSucceeded(
          type,
          res.map((o) => o.drawing_id)
        )
      );

    return res;
  });
};

export const handleSendBackDrawingsPendingApproval = (
  drawingIds = [],
  emailInfo = {}
) => (dispatch) => {
  const type = "DRAWINGS_PENDING_APPROVAL_SENT_BACK";
  return sendBackDrawingsPendingApproval(drawingIds, emailInfo).then((res) => {
    if (
      !res.error &&
      res.state === "success" &&
      new RegExp(`^${drawingIds.length} `).test(res.message)
    )
      dispatch(receiveSucceeded(type, drawingIds));

    return res;
  });
};

export const handleFetchDrawingsSentBack = (packageId, isAssigned = true) => (
  dispatch
) => {
  const type = "DRAWINGS_SENT_BACK";

  dispatch(receiveStarted(type));
  return fetchDrawingsSentBack(packageId, isAssigned).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleResubmitDrawingsSentBack = (
  drawingIds = [],
  emailInfo = {}
) => (dispatch) => {
  const type = "DRAWINGS_SENT_BACK_RESUBMITTED";
  return resubmitDrawingsSentBack(drawingIds, emailInfo).then((res) => {
    if (
      !res.error &&
      res.state === "success" &&
      new RegExp(`^${drawingIds.length} `).test(res.message)
    )
      dispatch(receiveSucceeded(type, drawingIds));

    return res;
  });
};
