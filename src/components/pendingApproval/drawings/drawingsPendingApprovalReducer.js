import { updateDrawingsHasMaj } from "../../../_utils";

const initialState = {
  isLoading: false,
  error: null,
  drawingsPendingApproval: null,
  savedColumnState: null,
  drawingsSentBack: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_DRAWINGS_PENDING_APPROVAL_STARTED":
      return {
        ...state,
        isLoading: true,
        error: null,
        drawingsPendingApproval: null,
      };
    case "RECEIVE_DRAWINGS_PENDING_APPROVAL_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        drawingsPendingApproval: payload,
      };
    case "RECEIVE_DRAWINGS_PENDING_APPROVAL_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        drawingsPendingApproval: [],
      };
    case "RECEIVE_DRAWINGS_PENDING_APPROVAL_APPROVED_SUCCEEDED":
      return {
        ...state,
        drawingsPendingApproval: state.drawingsPendingApproval.filter(
          (d) => !payload.includes(d.id)
        ),
      };
    case "RECEIVE_DRAWINGS_PENDING_APPROVAL_SENT_BACK_SUCCEEDED":
      return {
        ...state,
        drawingsPendingApproval: state.drawingsPendingApproval.filter(
          (d) => !payload.includes(d.id)
        ),
      };
    case "CLEAR_DRAWINGS_PENDING_APPROVAL_STATE":
      return initialState;
    case "RECEIVE_DRAWINGS_PENDING_APPROVAL_COLUMN_STATE_STARTED":
      return { ...state, savedColumnState: null };
    case "RECEIVE_DRAWINGS_PENDING_APPROVAL_COLUMN_STATE_SUCCEEDED":
      return { ...state, savedColumnState: payload };
    case "RECEIVE_DRAWINGS_PENDING_APPROVAL_COLUMN_STATE_FAILED":
      return { ...state, savedColumnState: [] };
    case "RECEIVE_DRAWINGS_SENT_BACK_STARTED":
      return {
        ...state,
        isLoading: true,
        error: null,
        drawingsSentBack: null,
      };
    case "RECEIVE_DRAWINGS_SENT_BACK_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        drawingsSentBack: payload,
      };
    case "RECEIVE_DRAWINGS_SENT_BACK_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        drawingsSentBack: [],
      };
    case "RECEIVE_DRAWINGS_SENT_BACK_COLUMN_STATE_STARTED":
      return { ...state, savedColumnState: null };
    case "RECEIVE_DRAWINGS_SENT_BACK_COLUMN_STATE_SUCCEEDED":
      return { ...state, savedColumnState: payload };
    case "RECEIVE_DRAWINGS_SENT_BACK_COLUMN_STATE_FAILED":
      return { ...state, savedColumnState: [] };
    case "RECEIVE_DRAWINGS_SENT_BACK_RESUBMITTED_SUCCEEDED":
      return {
        ...state,
        drawingsSentBack: state.drawingsSentBack.filter(
          (p) => !payload.includes(p.id)
        ),
      };
    case "RECEIVE_DRAWINGS_PENDING_MAJS_UPLOADED_SUCCEEDED":
      if (state.drawingsPendingApproval) {
        return {
          ...state,
          drawingsPendingApproval: updateDrawingsHasMaj(
            state.drawingsPendingApproval,
            payload
          ),
        };
      }
      return state;
    case "RECEIVE_DRAWINGS_SENT_BACK_MAJS_UPLOADED_SUCCEEDED":
      if (state.drawingsSentBack) {
        return {
          ...state,
          drawingsSentBack: updateDrawingsHasMaj(
            state.drawingsSentBack,
            payload
          ),
        };
      }
      return state;
    default:
      return state;
  }
}
