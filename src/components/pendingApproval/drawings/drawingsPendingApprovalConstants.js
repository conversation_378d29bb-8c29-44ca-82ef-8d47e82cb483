// HELPER FUNCTION IMPORTS
import { generateTime, naturalSort } from "../../../_utils";

// EXPORTS
export const columnDefs = (
  moreInfoClick,
  toggleMoreInfo,
  togglePDFViewer,
  savedColumnState,
  sortState,
  permissions,
  setDisplayedPdf
) => {
  let defaultDefs = [
    {
      headerName: "",
      headerCheckboxSelection: true,
      headerCheckboxSelectionFilteredOnly: true,
      minWidth: 50,
      width: 50,
      checkboxSelection: true,
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      colId: "checkbox",
    },
    {
      headerName: "Job #",
      field: "job_number",
      minWidth: 80,
      width: 100,
      getQuickFilterText: (params) => params.data.job_number,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "job_number",
      sort:
        sortState.sorting_column_name === "job_number"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Job Title",
      field: "job_title",
      getQuickFilterText: (params) => params.data.job_title,
      valueFormatter: (params) => ({
        wizardPermission:
          permissions &&
          (permissions.includes(279) ||
            permissions.includes(280) ||
            permissions.includes(281)),
      }),
      cellRenderer: "jobNameCellRenderer",
      minWidth: 120,
      width: 140,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "job_title",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "job_title"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Package ID",
      field: "package_id",
      getQuickFilterText: (params) => params.data.package_id,
      minWidth: 120,
      resizable: true,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_id",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "package_id"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Package Name",
      field: "package_name",
      getQuickFilterText: (params) => params.data.package_name,
      valueFormatter: (params) => ({
        wizardPermission:
          permissions &&
          (permissions.includes(279) ||
            permissions.includes(280) ||
            permissions.includes(281)),
      }),
      cellRenderer: "packageNameCellRenderer",
      minWidth: 120,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_name",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "package_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Drawing Name",
      field: "name",
      getQuickFilterText: (params) => params.data.name,
      minWidth: 120,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "drawing_name",
      autoHeight: true,
      cellRenderer: "drawingNameCellRenderer",
      cellRendererParams: (params) => {
        return {
          moreInfoClick: (...args) => moreInfoClick(...args, params),
          togglePDFViewer,
          specialColor: true,
          setDisplayedPdf,
        };
      },
      sort:
        sortState.sorting_column_name === "drawing_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Created By",
      field: "created_by",
      getQuickFilterText: (params) => params.data.created_by,
      minWidth: 120,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "created_by",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "created_by"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Created On",
      field: "created_on",
      valueFormatter: (params) => {
        return generateTime(params.value * 1000, false, true, "+");
      },
      getQuickFilterText: (params) => params.valueFormatted,
      minWidth: 120,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "created_on",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "created_on"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Manage",
      sortable: false,
      width: 60,
      minWidth: 60,
      valueFormatter: (params) => {
        return {
          moreInfoClick: (...args) => moreInfoClick(...args, params),
          toggleMoreInfo,
        };
      },
      pinned: "right",
      cellRenderer: "moreInfoCellRenderer",
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      lockVisible: true,
      suppressMovable: true,
      colId: "manage",
    },
  ];

  if (savedColumnState && savedColumnState.length) {
    for (let i = 0; i < defaultDefs.length; i++) {
      let savedDef = savedColumnState.find(
        (c) => c.header_name === defaultDefs[i].headerName
      );
      if (savedDef) {
        defaultDefs[i] = {
          ...defaultDefs[i],
          pinned: savedDef.pinned,
          hide: savedDef.visible ? false : true,
          position: savedDef.position,
        };
      }
    }
    return defaultDefs
      .sort((a, b) => {
        if (a.position === b.position) {
          if (a.headerName.toLowerCase() > b.headerName.toLowerCase()) return 1;
          else return -1;
        } else return a.position - b.position;
      })
      .map((col) => {
        if (col.position !== undefined) delete col.position;
        return col;
      });
  } else return defaultDefs;
};
