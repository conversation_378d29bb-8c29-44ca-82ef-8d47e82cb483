import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockAdapter from "axios-mock-adapter";
import thunk from "redux-thunk";

import {
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  handleFetchDrawingsPendingApproval,
  handleApproveDrawingsPendingApproval,
  handleSendBackDrawingsPendingApproval,
  handleFetchDrawingsSentBack,
  handleResubmitDrawingsSentBack,
} from "./drawingsPendingApprovalActions";
import { columnDefs } from "./drawingsPendingApprovalConstants";

describe("Drawings Pending Approval", () => {
  const testError = (type, message) => ({
    error: {
      status: 400,
      message: `${message ? message : `No ${type} found.`}`,
    },
  });

  describe("action handlers should perform the necessary functions", () => {
    let store;
    let httpMock;

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    const testDrawings = [
      {
        user_id: 158,
        created_on: 1595269806,
        package_id: 1700,
        fp_budget: 0,
        package_name: "20+ stages",
        due_date: 1595894400,
        job_number: "231123",
        job_name: "jk test save items 2",
        job_id: 922,
        full_name: "Jessica1 Karpovich",
        flow_name: "EC Basic Flow",
        flow_id: 27,
        flow_active: 0,
        drawing_count: 36,
        emails: "<EMAIL>,<EMAIL>",
        reset_needed: 0,
        approved: 0,
        package_map: "",
        forge_models:
          '[{"forge_model_id": 164, "forge_urn": "dXJuOmFkc2sud2lwcHJvZDpmcy5maWxlOnZmLktHSVlBcnpBU1Q2XzVGeThINGlaU2c_dmVyc2lvbj0xMQ", "model_name": "(Save2)2018 - Demo - Mech Pipe2018.rvt"}]',
      },
      {
        user_id: 119,
        created_on: 1595594998,
        package_id: 1712,
        fp_budget: 0,
        package_name: "P2 - archi ve",
        due_date: 1588204800,
        job_number: "0594-23234",
        job_name: "EC test fab using new sprocs",
        job_id: 925,
        full_name: "Emerald Cheney",
        flow_name: "EC test flow response - 1",
        flow_id: 1,
        flow_active: 0,
        drawing_count: 2,
        emails: "<EMAIL>",
        reset_needed: 0,
        approved: 0,
        package_map: "",
        forge_models: null,
      },
    ];

    it("handleFetchDrawingsPendingApproval fetches all drawings pending approval or where assigned", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/drawings/pending-approval?app_type=fab&package_id=32&is_assigned=0`
        )
        .replyOnce(200, testDrawings)
        .onGet(
          `${process.env.REACT_APP_API}/drawings/pending-approval?app_type=fab&package_id=32&is_assigned=1`
        )
        .replyOnce(200, testDrawings[1])
        .onGet(
          `${process.env.REACT_APP_API}/drawings/pending-approval?app_type=fab&package_id=32&is_assigned=0`
        )
        .replyOnce(404, testError("drawings pending approval"));

      await store
        .dispatch(handleFetchDrawingsPendingApproval(32, false))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("DRAWINGS_PENDING_APPROVAL"),
            receiveSucceeded("DRAWINGS_PENDING_APPROVAL", testDrawings),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testDrawings);

          store.clearActions();
        });

      await store
        .dispatch(handleFetchDrawingsPendingApproval(32, true))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("DRAWINGS_PENDING_APPROVAL"),
            receiveSucceeded("DRAWINGS_PENDING_APPROVAL", testDrawings[1]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testDrawings[1]);

          store.clearActions();
        });

      return store
        .dispatch(handleFetchDrawingsPendingApproval(32, false))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("DRAWINGS_PENDING_APPROVAL"),
            receiveFailed(
              "DRAWINGS_PENDING_APPROVAL",
              testError("drawings pending approval").error
            ),
          ];

          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleApproveDrawingsPendingApproval approves drawings pending approval", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/drawings/approve-work`)
        .replyOnce(200, [{ drawing_id: 1 }, { drawing_id: 2 }])
        .onPut(`${process.env.REACT_APP_API}/drawings/approve-work`)
        .replyOnce(400, testError("drawings approve work"));

      await store
        .dispatch(handleApproveDrawingsPendingApproval([1, 2], false))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveSucceeded("DRAWINGS_PENDING_APPROVAL_APPROVED", [1, 2]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[0].payload).toEqual([1, 2]);

          store.clearActions();
        });

      return store
        .dispatch(handleApproveDrawingsPendingApproval([1, 2], false))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [];

          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleSendBackDrawingsPendingApproval approves drawings pending approval", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/drawings/send-back`)
        .replyOnce(200, { state: "success", message: "2 drawings" })
        .onPut(`${process.env.REACT_APP_API}/drawings/send-back`)
        .replyOnce(400, testError("send back drawings"));

      await store
        .dispatch(handleSendBackDrawingsPendingApproval([1, 2], {}))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveSucceeded("DRAWINGS_PENDING_APPROVAL_SENT_BACK", [1, 2]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[0].payload).toEqual([1, 2]);

          store.clearActions();
        });

      return store
        .dispatch(handleSendBackDrawingsPendingApproval([1, 2], {}))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [];

          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleFetchDrawingsSentBack fetches all drawings sent back or where assigned", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/drawings/sent-back-list?app_type=fab&package_id=32&is_assigned=0`
        )
        .replyOnce(200, testDrawings)
        .onGet(
          `${process.env.REACT_APP_API}/drawings/sent-back-list?app_type=fab&package_id=32&is_assigned=1`
        )
        .replyOnce(200, testDrawings[1])
        .onGet(
          `${process.env.REACT_APP_API}/drawings/sent-back-list?app_type=fab&package_id=32&is_assigned=0`
        )
        .replyOnce(404, testError("drawings sent back"));

      await store.dispatch(handleFetchDrawingsSentBack(32, false)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("DRAWINGS_SENT_BACK"),
          receiveSucceeded("DRAWINGS_SENT_BACK", testDrawings),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testDrawings);

        store.clearActions();
      });

      await store.dispatch(handleFetchDrawingsSentBack(32, true)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("DRAWINGS_SENT_BACK"),
          receiveSucceeded("DRAWINGS_SENT_BACK", testDrawings[1]),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testDrawings[1]);

        store.clearActions();
      });

      return store.dispatch(handleFetchDrawingsSentBack(32, false)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("DRAWINGS_SENT_BACK"),
          receiveFailed(
            "DRAWINGS_SENT_BACK",
            testError("drawings sent back").error
          ),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleResubmitDrawingsSentBack approves drawings pending approval", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/drawings/resubmit`)
        .replyOnce(200, { state: "success", message: "2 drawings resubmitted" })
        .onPut(`${process.env.REACT_APP_API}/drawings/resubmit`)
        .replyOnce(400, testError("send back drawings"));

      await store
        .dispatch(handleResubmitDrawingsSentBack([1, 2], {}))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveSucceeded("DRAWINGS_SENT_BACK_RESUBMITTED", [1, 2]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[0].payload).toEqual([1, 2]);

          store.clearActions();
        });

      return store
        .dispatch(handleResubmitDrawingsSentBack([1, 2], {}))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [];

          expect(receivedActions).toEqual(expectedActions);
        });
    });
  });
  describe("Drawings Pending Approval Column Defs", () => {
    const defaultHeaders = [
      "",
      "Job #",
      "Job Title",
      "Package ID",
      "Package Name",
      "Drawing Name",
      "Created By",
      "Created On",
      "Manage",
    ];
    const sortState = { sorting_column_name: "id", sorting_method: "asc" };

    const moreInfoClick = jest.fn();
    const toggleMoreInfo = jest.fn();

    let populatedColumns;

    beforeEach(() => {
      populatedColumns = columnDefs(
        moreInfoClick,
        toggleMoreInfo,
        () => {},
        null,
        sortState
      );
    });

    it("Drawings headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);
      expect(columnHeaders).toEqual(defaultHeaders);
    });

    describe("JOB #", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Job #");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            job_number: "123456",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("123456");
      });
    });

    describe("JOB TITLE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Job Title");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            job_title: "test job",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test job");
      });
    });

    describe("PACKAGE NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Package Name");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            package_name: "test package",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test package");
      });
    });

    describe("DRAWING NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Drawing Name");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            name: "test drawing",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test drawing");
      });
    });

    describe("CREATED BY", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Created By");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            created_by: "test user",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test user");
      });
    });

    describe("CREATED ON", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Created On");
      });
      it("valueFormatter should display the value in a user friendly format", () => {
        const params = {
          value: 1645651408,
        };
        expect(column.valueFormatter(params)).toEqual("02-23-2022");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          valueFormatted: "02-23-2022",
        };
        expect(column.getQuickFilterText(params)).toEqual("02-23-2022");
      });
    });

    describe("MANAGE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Manage");
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            moreInfoClick,
            toggleMoreInfo,
          },
        };
        expect(column.valueFormatter(params)).toHaveProperty("moreInfoClick");
        expect(column.valueFormatter(params)).toHaveProperty(
          "toggleMoreInfo",
          toggleMoreInfo
        );
      });
    });
  });
});
