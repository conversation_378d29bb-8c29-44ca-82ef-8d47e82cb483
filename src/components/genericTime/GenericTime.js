// NPM PACKAGE IMPORTS
import React, {
  useEffect,
  useMemo,
  useState,
  useCallback,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import Button from "msuite_storybook/dist/button/Button";
import { ImArrowUpRight2 } from "react-icons/im";
import { FaPlay, FaPause } from "react-icons/fa";

// REDUX IMPORTS
import { setPageTitle } from "../../redux/generalActions";
import {
  handleFetchJobs,
  handleFetchPackages,
  handleFetchDrawings,
  handleFetchGenericTimeStages,
  handleFetchCostCodes,
} from "./genericTimeActions";
import { handleStartTimer, handleStopTimer } from "./../timers/timersActions";
import { notify } from "../reusable/alertPopup/alertPopupActions";

// COMPONENT IMPORTS
import Filter from "../reusable/filter/Filter";
import ManageSessionModal from "../layout/header/manageSessionModal/ManageSessionModal";
import PDFViewer from "../reusable/pdfViewer";
import DownloadMAJsButton from "../reusable/downloadMAJsButton/DownloadMAJsButton";

// HELPER FUNCTION IMPORTS
import { escapeRegExp } from "../../_utils";
import usePrevious from "../../hooks/usePrevious";

// STYLE IMPORTS
import "./stylesGenericTime.scss";

const GenericTime = () => {
  const [stagesAndTasks, setStagesAndTasks] = useState([]);
  const [file, setFile] = useState(null);
  const [currentDrawing, setCurrentDrawing] = useState(null);
  const [viewerDrawings, setViewerDrawings] = useState([]);
  // SELECTED state
  const [selectedJobs, setSelectedJobs] = useState([]);
  const [selectedPackages, setSelectedPackages] = useState([]);
  const [selectedDrawings, setSelectedDrawings] = useState([]);
  const [selectedStage, setSelectedStage] = useState([]);
  const [selectedCostCodes, setSelectedCostCodes] = useState([]);
  // SEARCH INPUT state
  const [jobsSearchInput, setJobsSearchInput] = useState("");
  const [packagesSearchInput, setPackagesSearchInput] = useState("");
  const [drawingsSearchInput, setDrawingsSearchInput] = useState("");
  const [stagesSearchInput, setStagesSearchInput] = useState("");
  const [costCodeSearchInput, setCostCodeSearchInput] = useState("");
  // SHIFT state
  const [showManageSessionModal, toggleManageSessionModal] = useState(false);
  // FETCH state
  const [dataFetched, setDataFetched] = useState({
    jobs: false,
    packages: false,
    drawings: false,
    stagesAndTasks: false,
    costCodes: false,
    activeTimer: false,
    hasRequiredData: false,
  });

  const dispatch = useDispatch();

  const { jobs, packages, drawings, stages, costCodes } = useSelector(
    (state) => state.genericTimeData
  );
  const { shiftNow, permissions, features } = useSelector(
    (state) => state.profileData
  );
  const { activeTimer } = useSelector((state) => state.timerData);

  const previousSelectedCostCodes = usePrevious(selectedCostCodes);
  const previousTimers = usePrevious(activeTimer);

  useEffect(() => {
    dispatch(setPageTitle("Generic Time"));
    dispatch(handleFetchJobs());
    dispatch(handleFetchCostCodes());
    dispatch(handleFetchGenericTimeStages());
  }, []);

  /* filter refreshes and updates */
  const refreshFilterData = useCallback(
    (type) => {
      if (dataFetched.hasRequiredData && dataFetched.activeTimer) return;

      const jobIds = selectedJobs.map((o) => o.id);
      const packageIds = selectedPackages
        .filter((o) => jobIds.includes(o.job_id))
        .map((o) => o.id);
      const drawingIds = selectedDrawings
        .filter(
          (o) =>
            jobIds.includes(o.job_id) &&
            (!packageIds.length || packageIds.includes(o.package_id))
        )
        .map((o) => o.id);

      if (packageIds.length !== selectedPackages.length) {
        setSelectedPackages(
          selectedPackages.filter((o) => packageIds.includes(o.id))
        );
      }

      if (drawingIds.length !== selectedDrawings.length) {
        setSelectedDrawings(
          selectedDrawings.filter((o) => drawingIds.includes(o.id))
        );
      }

      switch (type) {
        case "jobs":
          if (!selectedJobs.length) {
            dispatch(handleFetchGenericTimeStages());
            dispatch(handleFetchCostCodes());
          } else {
            dispatch(handleFetchPackages(jobIds));

            if (drawingIds.length) {
              dispatch(handleFetchGenericTimeStages(null, null, drawingIds));
              dispatch(handleFetchCostCodes(null, null, drawingIds));
            } else if (packageIds.length) {
              dispatch(handleFetchGenericTimeStages(null, packageIds));
              dispatch(handleFetchCostCodes(null, packageIds));
              dispatch(
                handleFetchDrawings(
                  null,
                  packageIds,
                  selectedStage.length &&
                    selectedStage[0]?.stage_work_level_id !== 3
                    ? [selectedStage[0]?.id]
                    : null
                )
              );
            } else {
              dispatch(handleFetchGenericTimeStages(jobIds));
              dispatch(handleFetchCostCodes(jobIds));
              dispatch(
                handleFetchDrawings(
                  jobIds,
                  null,
                  selectedStage.length &&
                    selectedStage[0]?.stage_work_level_id !== 3
                    ? [selectedStage[0]?.id]
                    : null
                )
              );
            }
          }
          break;
        case "packages":
          if (packageIds.length) {
            dispatch(
              handleFetchDrawings(
                null,
                packageIds,
                selectedStage.length &&
                  selectedStage[0]?.stage_work_level_id !== 3
                  ? [selectedStage[0]?.id]
                  : null
              )
            );
            if (drawingIds.length) {
              dispatch(handleFetchGenericTimeStages(null, null, drawingIds));
              dispatch(handleFetchCostCodes(null, null, drawingIds));
            } else {
              dispatch(handleFetchGenericTimeStages(null, packageIds));
              dispatch(handleFetchCostCodes(null, packageIds));
            }
          } else if (jobIds.length) {
            dispatch(handleFetchGenericTimeStages(jobIds));
            dispatch(handleFetchCostCodes(jobIds));
            dispatch(
              handleFetchDrawings(
                jobIds,
                null,
                selectedStage.length &&
                  selectedStage[0]?.stage_work_level_id !== 3
                  ? [selectedStage[0]?.id]
                  : null
              )
            );
          }
          break;
        case "drawings":
          if (drawingIds.length) {
            dispatch(handleFetchGenericTimeStages(null, null, drawingIds));
            dispatch(handleFetchCostCodes(null, null, drawingIds));
          } else if (packageIds.length) {
            dispatch(handleFetchGenericTimeStages(null, packageIds));
            dispatch(handleFetchCostCodes(null, packageIds));
          } else if (jobIds.length) {
            dispatch(handleFetchGenericTimeStages(jobIds));
            dispatch(handleFetchCostCodes(jobIds));
          }
          break;
        default:
          return;
      }
    },
    [
      selectedJobs,
      selectedPackages,
      selectedDrawings,
      selectedStage,
      selectedCostCodes,
      dataFetched,
    ]
  );

  useEffect(() => {
    if (!selectedJobs?.length) {
      setSelectedPackages([]);
      if (selectedStage?.length && selectedStage[0].stage_work_level_id !== 3)
        setSelectedStage([]);
    }

    refreshFilterData("jobs");
  }, [selectedJobs, selectedStage, dispatch]);

  useEffect(() => {
    if (!selectedCostCodes?.length && previousSelectedCostCodes?.length) {
      dispatch(handleFetchJobs());
    } else {
      dispatch(
        handleFetchJobs(
          selectedCostCodes
            .filter((c) => c.general_code === 0)
            .map((scc) => scc.id)
        )
      );
    }

    refreshFilterData("jobs");
  }, [selectedCostCodes, selectedJobs]);

  useEffect(() => {
    if (!selectedPackages?.length) {
      setSelectedDrawings([]);
    }

    refreshFilterData("packages");
  }, [selectedPackages]);

  useEffect(() => {
    refreshFilterData("drawings");
    filterViewerDrawings(selectedDrawings);
  }, [selectedDrawings]);

  /* Populate filters on page load if user has active timer */
  const updateFiltersWithTimer = useCallback(() => {
    if (!activeTimer || activeTimer[0].type !== "generic") return;

    const jobIds = activeTimer.map((t) => t.job_id);
    const packageIds = activeTimer.map((t) => t.package_id);
    const drawingIds = activeTimer.map((t) => t.drawing_id);
    const stageId = activeTimer[0].stage_id;
    const costCodeId = activeTimer[0].cost_code_id;

    if (jobs && jobIds) {
      const selectedJobs = jobs?.filter((j) => jobIds.includes(j.id));
      selectedJobs && setSelectedJobs(selectedJobs);
    }
    if (packages && packageIds) {
      const selectedPackages = packages?.filter((p) =>
        packageIds.includes(p.id)
      );
      selectedPackages && setSelectedPackages(selectedPackages);
    }
    if (drawings && drawingIds) {
      const selectedDrawings = drawings?.filter((d) =>
        drawingIds.includes(d.id)
      );
      selectedDrawings && setSelectedDrawings(selectedDrawings);
    }
    if (costCodes && costCodeId) {
      const selectedCostCode = costCodes?.find((cc) => cc.id === costCodeId);
      selectedCostCode && setSelectedCostCodes([selectedCostCode]);
    }
    if (stagesAndTasks && stageId) {
      const selectedStage = stagesAndTasks?.find((s) => s.id === stageId);
      selectedStage && setSelectedStage([selectedStage]);
    }
  }, [activeTimer, jobs, packages, drawings, costCodes, stagesAndTasks]);

  // fetch data for active timer
  useEffect(() => {
    if (
      activeTimer &&
      activeTimer.length &&
      !previousTimers &&
      !dataFetched.hasRequiredData
    ) {
      const jobIds = activeTimer.flatMap((f) => (f.job_id ? [f.job_id] : []));
      const packageIds = activeTimer.flatMap((f) =>
        f.package_id ? [f.package_id] : []
      );
      const drawingIds = activeTimer.flatMap((f) =>
        f.drawing_id ? [f.drawing_id] : []
      );

      if (drawingIds.length) {
        dispatch(handleFetchGenericTimeStages(null, null, drawingIds));
        dispatch(handleFetchCostCodes(null, null, drawingIds));
      } else if (packageIds.length) {
        dispatch(handleFetchGenericTimeStages(null, packageIds));
        dispatch(handleFetchCostCodes(null, packageIds));
      } else if (jobIds.length) {
        dispatch(handleFetchGenericTimeStages(jobIds));
        dispatch(handleFetchCostCodes(jobIds));
      }

      if (packageIds.length) {
        dispatch(
          handleFetchDrawings(
            null,
            packageIds,
            selectedStage.length && selectedStage[0]?.stage_work_level_id !== 3
              ? [selectedStage[0]?.id]
              : null
          )
        );
      } else if (jobIds.length) {
        dispatch(
          handleFetchDrawings(
            jobIds,
            null,
            selectedStage.length && selectedStage[0]?.stage_work_level_id !== 3
              ? [selectedStage[0]?.id]
              : null
          )
        );
      }

      if (jobIds.length) {
        dispatch(handleFetchPackages(jobIds));
      }
    } else if (
      (!activeTimer || !activeTimer.length) &&
      dataFetched.activeTimer
    ) {
      setDataFetched({ ...dataFetched, activeTimer: false });
    }
  }, [activeTimer, dataFetched]);

  // set dataFetched.hasRequired data if all timer pieces are fetched
  useEffect(() => {
    if (dataFetched.activeTimer) {
      if (!dataFetched.hasRequiredData) {
        const hasRequiredData =
          (!activeTimer[0].job_id ||
            (activeTimer[0].job_id && dataFetched.jobs)) &&
          (!activeTimer[0].package_id ||
            (activeTimer[0].package_id && dataFetched.packages)) &&
          (!activeTimer[0].drawing_id ||
            (activeTimer[0].drawing_id && dataFetched.drawings)) &&
          (!activeTimer[0].stage_id ||
            (activeTimer[0].stage_id && dataFetched.stagesAndTasks)) &&
          (!activeTimer[0].cost_code_id ||
            (activeTimer[0].cost_code_id && dataFetched.costCodes));

        if (hasRequiredData) {
          setDataFetched({ ...dataFetched, hasRequiredData: true });
          updateFiltersWithTimer();
        }
      } else {
        updateFiltersWithTimer();
      }
    }
  }, [dataFetched]);

  // update data fetched as entities are loaded
  useEffect(() => {
    // make a copy of dataFetched
    let tempDataFetched = JSON.parse(JSON.stringify(dataFetched));
    if (jobs && !tempDataFetched.jobs) tempDataFetched.jobs = true;
    if (packages && !tempDataFetched.packages) tempDataFetched.packages = true;
    if (drawings && !tempDataFetched.drawings) tempDataFetched.drawings = true;
    if (costCodes && !tempDataFetched.costCodes)
      tempDataFetched.costCodes = true;
    if (stagesAndTasks && !tempDataFetched.stagesAndTasks)
      tempDataFetched.stagesAndTasks = true;
    if (activeTimer && !tempDataFetched.activeTimer)
      tempDataFetched.activeTimer = true;

    if (JSON.stringify(tempDataFetched) !== JSON.stringify(dataFetched)) {
      setDataFetched(tempDataFetched);
    }
  }, [
    jobs,
    packages,
    drawings,
    costCodes,
    stagesAndTasks,
    activeTimer,
    dataFetched,
  ]);

  /* displayed data in dropdowns formatting */
  const displayedJobs = useMemo(() => {
    if (jobs && jobs.length) {
      if (jobsSearchInput !== "") {
        const pattern = new RegExp(escapeRegExp(jobsSearchInput), "i");
        return jobs
          .filter((o) => pattern.test(`(${o.job_number}) ${o.job_name}`))
          .sort((a, b) => (a.job_name > b.job_name ? 1 : -1));
      } else {
        return jobs.sort((a, b) => (a.job_name > b.job_name ? 1 : -1));
      }
    } else return [];
  }, [jobs, jobsSearchInput]);

  const displayedPackages = useMemo(() => {
    if (packages && packages.length) {
      if (packagesSearchInput !== "") {
        const pattern = new RegExp(escapeRegExp(packagesSearchInput), "i");
        return packages
          .filter((o) => pattern.test(`(${o.id}) ${o.package_name}`))
          .sort((a, b) => (a.package_name > b.package_name ? 1 : -1));
      } else {
        return packages.sort((a, b) =>
          a.package_name > b.package_name ? 1 : -1
        );
      }
    } else return [];
  }, [packages, packagesSearchInput]);

  const displayedDrawings = useMemo(() => {
    // if no selected jobs, then our drawings are not updated enough to use this...
    if (selectedJobs.length > 0 && drawings && drawings.length) {
      if (drawingsSearchInput !== "") {
        const pattern = new RegExp(escapeRegExp(drawingsSearchInput), "i");
        return drawings
          .filter((o) => pattern.test(o.name))
          .sort((a, b) => (a.name > b.name ? 1 : -1));
      } else {
        return drawings.sort((a, b) => (a.name > b.name ? 1 : -1));
      }
    } else return [];
  }, [drawings, drawingsSearchInput, selectedJobs]);

  const displayedCostCodes = useMemo(() => {
    if (costCodes && costCodes.length) {
      if (costCodeSearchInput !== "") {
        const pattern = new RegExp(escapeRegExp(costCodeSearchInput), "i");
        return costCodes.filter((cc) => pattern.test(cc.name));
      } else {
        return costCodes.sort((a, b) => (a.name > b.name ? 1 : -1));
      }
    } else return [];
  }, [costCodes, costCodeSearchInput]);

  useEffect(() => {
    setStagesAndTasks([...(stages ? stages : [])]);
  }, [stages]);

  const displayedStagesAndTasks = useMemo(() => {
    if (stagesAndTasks && stagesAndTasks.length) {
      if (stagesSearchInput !== "") {
        const pattern = new RegExp(escapeRegExp(stagesSearchInput), "i");
        return [
          ...stagesAndTasks
            .filter(
              (o) =>
                pattern.test(o.name) &&
                o.id !== 0 &&
                o.stage_work_level_id !== 3
            )
            .sort((a, b) => (a.flow_name > b.flow_name ? 1 : -1)),
          // move tasks below stages in list of options
          ...stagesAndTasks
            .filter(
              (o) =>
                pattern.test(o.name) &&
                o.id !== 0 &&
                o.stage_work_level_id === 3
            )
            .map((o) => ({
              ...o,
              flow_id: "Tasks",
            }))
            .sort((a, b) => (a.name > b.name ? 1 : -1)),
        ];
      } else {
        return [
          ...stagesAndTasks
            .filter((o) => o.stage_work_level_id !== 3)
            .sort((a, b) => (a.flow_name > b.flow_name ? 1 : -1)),
          // move tasks below the stages in list of options
          ...stagesAndTasks
            .filter((o) => o.stage_work_level_id === 3)
            .map((o) => ({
              ...o,
              flow_id: "Tasks",
            }))
            .sort((a, b) => (a.name > b.name ? 1 : -1)),
        ];
      }
    } else return [];
  }, [stagesAndTasks, stagesSearchInput]);

  // flow names + Tasks to group stages by in display
  const displayedStageSectionTitles = useMemo(() => {
    let result = [];
    if (displayedStagesAndTasks && displayedStagesAndTasks.length) {
      let mappedArray = displayedStagesAndTasks
        .filter((o) => o.stage_work_level_id !== 3)
        .map((o) => ({
          id: o.flow_id,
          name: o.flow_name,
        }));

      mappedArray.forEach((a) => {
        if (!result.map((o) => o.id).includes(a.id)) result.push(a);
      });

      // add tasks to end of array if any in list
      if (
        displayedStagesAndTasks.filter((o) => o.stage_work_level_id === 3)
          .length
      ) {
        result.push({
          id: "Tasks",
          name: "Tasks",
        });
      }
    }
    return result;
  }, [displayedStagesAndTasks]);

  /* timers! */
  const handleTimerStart = () => {
    if (
      !selectedStage?.length ||
      !(selectedJobs.length || selectedCostCodes.length)
    ) {
      return dispatch(
        notify({
          id: Date.now(),
          type: "ERROR",
          message:
            "You must choose (a job and a task / stage) or (task and cost code) to begin work.",
        })
      );
    }

    const itemType = selectedDrawings.length
      ? "drawing"
      : selectedPackages.length
      ? "package"
      : selectedJobs.length
      ? "job"
      : "costcode";

    const itemIds =
      itemType === "drawing"
        ? selectedDrawings.map((o) => o.id)
        : itemType === "package"
        ? selectedPackages.map((o) => o.id)
        : itemType === "job"
        ? selectedJobs.map((o) => o.id)
        : null;

    dispatch(
      handleStartTimer(
        itemIds,
        shiftNow.id,
        selectedStage[0].id,
        null,
        itemType,
        1,
        selectedCostCodes.length ? selectedCostCodes[0].id : null
      )
    );
  };

  const handleTimerStop = () => {
    dispatch(handleStopTimer(activeTimer[0].timer_id, 1, (_) => _, true));
  };

  useEffect(() => {
    if (!drawings?.length) {
      setFile(null);
      setCurrentDrawing(null);
      setViewerDrawings([]);
      return;
    }

    filterViewerDrawings();
  }, [drawings]);

  const filterViewerDrawings = (drawingsList = drawings) => {
    const filterList = (list) => {
      return (list ?? []).filter(
        (i) =>
          i.has_original != null ||
          i.has_annotated != null ||
          i.forge_urn != null
      );
    };

    // short hand for... drawings && drawings.length && drawings.length > 0
    // use drawing object when they exist and no selected
    let useDrawings =
      (drawings ?? []).length > 0 && !((selectedDrawings ?? []).length > 0);
    let filteredList = filterList(drawingsList ?? []);
    let useFiltered =
      (drawingsList ?? []).length > 0 && (filteredList ?? []).length > 0;

    let currentDrawingToSet = useFiltered
      ? filteredList[0]
      : useDrawings
      ? drawings[0]
      : null;

    const currentDrawings = useFiltered
      ? filteredList
      : useDrawings
      ? drawings
      : [];

    if (!currentDrawings?.length) return;

    // finally set properties
    setViewerDrawings(currentDrawings);
    setCurrentDrawing(currentDrawingToSet);
  };

  const handleAddRemoveUpdate = (action, drawing) => {
    if (action === "UPDATE") {
      let tempDrawings = [...selectedDrawings];
      for (let i = 0; i < tempDrawings.length; i++) {
        if (tempDrawings[i].id === drawing.id) {
          tempDrawings[i] = { ...tempDrawings[i], ...drawing };
          break;
        }
      }
      setSelectedDrawings(tempDrawings);
    } else if (action === "REMOVE") {
      setSelectedDrawings(selectedDrawings.filter((d) => drawing.id !== d.id));
    } else {
      setSelectedDrawings([...selectedDrawings, drawing]);
    }
  };

  const handleStageFilterChange = (selection) => {
    const selectedJobIds = selectedJobs.map((j) => j.id) || null;
    const selectedPackageIds = selectedPackages.map((p) => p.id) || null;

    setSelectedStage(selection);
    dispatch(
      handleFetchDrawings(
        selectedJobIds,
        selectedPackageIds,
        selection?.length && selection[0]?.stage_work_level_id !== 3
          ? [selection[0]?.id]
          : null
      )
    ).then((res) => {
      if (!res || res.error) return setSelectedDrawings([]);

      const availableDrawingIds = res.map((d) => d.id);
      const updatedSelections = selectedDrawings.filter((d) => {
        if (availableDrawingIds.includes(d.id)) return true;
        else return false;
      });
      setSelectedDrawings(updatedSelections);
    });
  };

  return (
    <div className="generic-wrapper">
      <div className="generic-filters-wrapper">
        <div className="filters-list">
          <Filter
            type="Jobs"
            list={displayedJobs || []}
            nameKey="job_name"
            idKey="job_number"
            isMulti
            selected={selectedJobs ?? []}
            handleParentSelect={(jobs) => setSelectedJobs(jobs)}
            setSelected={setSelectedJobs}
            smallView
            setSearchInput={setJobsSearchInput}
            searchInput={jobsSearchInput}
            toggleAllSelections={(f) => f}
            selectAll
            isDisabled={!!activeTimer}
          />
          {selectedJobs && selectedJobs.length > 0 && (
            <Filter
              type="Packages"
              list={displayedPackages || []}
              nameKey="package_name"
              idKey="id"
              isMulti
              selected={selectedPackages}
              handleParentSelect={(packages) => setSelectedPackages(packages)}
              setSelected={setSelectedPackages}
              smallView
              setSearchInput={setPackagesSearchInput}
              searchInput={packagesSearchInput}
              toggleAllSelections={(f) => f}
              selectAll
              isDisabled={!!activeTimer}
            />
          )}
          {((selectedJobs && selectedJobs.length > 0) ||
            (selectedPackages && selectedPackages.length > 0)) && (
            <Filter
              type="Drawings"
              list={displayedDrawings || []}
              nameKey="name"
              isMulti
              selected={selectedDrawings}
              handleParentSelect={() => null}
              setSelected={setSelectedDrawings}
              smallView
              setSearchInput={setDrawingsSearchInput}
              searchInput={drawingsSearchInput}
              toggleAllSelections={(f) => f}
              selectAll
              isDisabled={!!activeTimer}
            />
          )}
          <Filter
            type="Tasks/Stages"
            list={displayedStagesAndTasks || []}
            nameKey="name"
            isMulti={false}
            selected={selectedStage ?? []}
            handleParentSelect={() => null}
            setSelected={(e) => handleStageFilterChange(e)}
            setSearchInput={setStagesSearchInput}
            sectionTitles={displayedStageSectionTitles}
            searchInput={stagesSearchInput}
            sectionKey={"flow_id"}
            isDisabled={!!activeTimer}
          />
          <Filter
            type="Cost Codes"
            list={displayedCostCodes || []}
            nameKey="name"
            isMulti={false}
            selected={selectedCostCodes ?? []}
            handleParentSelect={() => null}
            setSelected={setSelectedCostCodes}
            smallView
            setSearchInput={setCostCodeSearchInput}
            searchInput={costCodeSearchInput}
            isDisabled={!!activeTimer}
          />
        </div>
        {displayedDrawings &&
          displayedDrawings.length > 0 &&
          features?.includes(41) &&
          permissions?.includes(302) && (
            <DownloadMAJsButton
              // current drawing is only applicable if a single is selected, doesn't work for this feature...
              // if we have selected, use those, else use the displayed drawings list...
              selectedDrawings={
                selectedDrawings.length > 0
                  ? selectedDrawings
                  : displayedDrawings
              }
              className="generic-download-majs btn"
              disabled={
                (selectedDrawings.length > 0
                  ? selectedDrawings
                  : displayedDrawings
                ).filter((d) => d.has_maj).length < 1
              }
            />
          )}
        {activeTimer && activeTimer[0].type === "generic" ? (
          <Button className="stop btn" onClick={handleTimerStop}>
            Stop{" "}
            {selectedStage && selectedStage.length ? selectedStage[0].name : ""}{" "}
            <FaPause />
          </Button>
        ) : activeTimer && activeTimer[0].type !== "generic" ? (
          <a
            className="neutral btn"
            href={`${
              activeTimer[0].type === "work"
                ? `${process.env.REACT_APP_FABPRO}/my-work/`
                : `${process.env.REACT_APP_FABPRO}/shipping/bill_of_lading.php`
            }`}
          >
            Work Timer Already Running
          </a>
        ) : shiftNow ? (
          <Button onClick={handleTimerStart} className="start btn">
            Start <FaPlay />{" "}
          </Button>
        ) : (
          <Button
            className="manage btn"
            onClick={() => toggleManageSessionModal(true)}
          >
            Manage Session
          </Button>
        )}
      </div>
      <a href={`${process.env.REACT_APP_FABPRO}/jobs/`} className="btn">
        Looking for something?{" "}
        <span>
          Check assignments and pending approvals. <ImArrowUpRight2 />{" "}
        </span>
      </a>
      {currentDrawing && selectedJobs?.length > 0 && (
        <div className="drawing-viewer">
          <PDFViewer
            selectedItem={currentDrawing}
            itemId={currentDrawing.id}
            itemType="DRAWING"
            file={file}
            setFile={setFile}
            currentRowOrder={viewerDrawings}
            genericTimeArea={true}
            handleAddRemoveUpdate={handleAddRemoveUpdate}
            selectedDrawings={selectedDrawings}
          />
        </div>
      )}
      {showManageSessionModal && (
        <ManageSessionModal
          showModal={showManageSessionModal}
          onClose={() => toggleManageSessionModal(false)}
        />
      )}
    </div>
  );
};

export default GenericTime;
