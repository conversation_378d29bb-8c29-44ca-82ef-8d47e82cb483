import {
  fetchJobs,
  fetchPackages,
  fetchDrawings,
  fetchGenericTimeStages,
  fetchCostCodes,
} from "../../_services";

export const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const handleFetchJobs = (costCodes, testUserId = null) => (dispatch) => {
  const type = "JOBS";

  dispatch(receiveStarted(type));
  return fetchJobs(false, null, testUserId, costCodes).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchPackages = (jobIds, testUserId = null) => (
  dispatch
) => {
  const type = "PACKAGES";

  dispatch(receiveStarted(type));
  return fetchPackages(jobIds, false, null, testUserId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchDrawings = (jobIds, packageIds, stageIds) => (
  dispatch
) => {
  const type = "DRAWINGS";

  dispatch(receiveStarted(type));
  return fetchDrawings(jobIds, packageIds, false, stageIds, true, true).then(
    (res) => {
      if (res.error) dispatch(receiveFailed(type, res.error));
      else dispatch(receiveSucceeded(type, res));

      return res;
    }
  );
};

export const handleFetchGenericTimeStages = (
  jobIds,
  packageIds,
  drawingIds
) => (dispatch) => {
  const type = "STAGES";

  dispatch(receiveStarted(type));
  return fetchGenericTimeStages(jobIds, packageIds, drawingIds).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else {
      dispatch(receiveSucceeded(type, res));
    }

    return res;
  });
};

export const handleFetchCostCodes = (
  jobIds = [],
  packageIds = [],
  drawingIds = []
) => (dispatch) => {
  const type = "COST_CODES";

  dispatch(receiveStarted(type));
  return fetchCostCodes(packageIds, 1, 0, jobIds, 0, drawingIds).then((res) => {
    if (res.error) {
      dispatch(receiveFailed(type, res.error));
      return res;
    }

    dispatch(receiveSucceeded(type, res));
    return res;
  });
};
