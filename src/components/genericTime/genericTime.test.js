import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockA<PERSON>pter from "axios-mock-adapter";
import thunk from "redux-thunk";

import {
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  handleFetchJobs,
  handleFetchPackages,
  handleFetchDrawings,
  handleFetchGenericTimeStages,
  handleFetchCostCodes,
} from "./genericTimeActions";

describe("GenericTime", () => {
  const testError = (type) => ({
    error: { status: 404, message: `No ${type} found.` },
  });
  const expectedErrorPayload = (type) => ({
    status: 404,
    message: `No ${type} found.`,
  });

  describe("action handlers should perform the necessary functions", () => {
    let store;
    let httpMock;

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    const testJobs = [
      {
        id: 1,
        job_name: "test job 1",
        job_number: "1",
        package_count: 1,
      },
      {
        id: 2,
        job_name: "test job 2",
        job_number: "2",
        package_count: 1,
      },
    ];
    const testPackages = [
      {
        id: 1,
        job_id: 1,
        job_name: "test job 1",
        job_number: "1",
        package_name: "test package 1",
        number: "1",
        drawing_count: 1,
      },
      {
        id: 2,
        job_id: 2,
        job_name: "test job 2",
        job_number: "2",
        package_name: "test package 2",
        number: "2",
        drawing_count: 1,
      },
    ];
    const testDrawings = [
      {
        id: 1,
        job_id: 1,
        job_number: "1",
        job_title: "test job 1",
        name: "test drawing 1",
        package_name: "test package 1",
        package_id: 1,
        package_number: "1",
      },
      {
        id: 2,
        job_id: 2,
        job_number: "2",
        job_title: "test job 2",
        name: "test drawing 2",
        package_name: "test package 2",
        package_id: 2,
        package_number: "2",
      },
    ];
    const testStages = [
      {
        id: 1,
        name: "test stage 1",
        stage_work_level_id: 1,
        stage_code_id: 1,
      },
      {
        id: 2,
        name: "test stage 2",
        stage_work_level_id: 2,
        stage_code_id: 2,
      },
      {
        id: 3,
        name: "test task 1",
        stage_work_level_id: 3,
        stage_code_id: null,
      },
    ];
    const testCostCodes = [
      {
        id: 1,
        name: "test cost code 1",
        active: 1,
        general_code: 1,
      },
      {
        id: 2,
        name: "test cost code 2",
        active: 1,
        general_code: 1,
      },
    ];
    const testMixedCostCodes = [
      {
        id: 1,
        name: "test cost code 1",
        active: 1,
        general_code: 0,
      },
      {
        id: 2,
        name: "test cost code 2",
        active: 1,
        general_code: 1,
      },
    ];

    it("handleFetchJobs fetches all jobs assigned to user", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/jobs?is_active=1&user_id=1&with_assigned_count=1`
        )
        .replyOnce(200, testJobs)
        .onGet(
          `${process.env.REACT_APP_API}/jobs?is_active=1&user_id=1&with_assigned_count=1`
        )
        .replyOnce(404, testError("jobs"));

      await store.dispatch(handleFetchJobs(null, 1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("JOBS"),
          receiveSucceeded("JOBS", testJobs),
        ];
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testJobs);

        store.clearActions();
      });

      return store.dispatch(handleFetchJobs(null, 1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("JOBS"),
          receiveFailed("JOBS", expectedErrorPayload("jobs")),
        ];
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchJobs fetches all jobs assigned to user with given cost code id", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/jobs?is_active=1&user_id=1&with_assigned_count=1&cost_codes=1`
        )
        .replyOnce(200, testJobs)
        .onGet(
          `${process.env.REACT_APP_API}/jobs?is_active=1&user_id=1&with_assigned_count=1&cost_codes=1`
        )
        .replyOnce(404, testError("jobs"));

      await store.dispatch(handleFetchJobs([1], 1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("JOBS"),
          receiveSucceeded("JOBS", testJobs),
        ];
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testJobs);

        store.clearActions();
      });

      return store.dispatch(handleFetchJobs([1], 1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("JOBS"),
          receiveFailed("JOBS", expectedErrorPayload("jobs")),
        ];
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchPackages fetches all packages assigned to user by job id", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/packages?is_active=1&job_ids=1&user_id=1&with_assigned_counts=1`
        )
        .replyOnce(200, testPackages)
        .onGet(
          `${process.env.REACT_APP_API}/packages?is_active=1&job_ids=1&user_id=1&with_assigned_counts=1`
        )
        .replyOnce(404, testError("packages"));

      await store.dispatch(handleFetchPackages([1], 1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("PACKAGES"),
          receiveSucceeded("PACKAGES", testPackages),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testPackages);

        store.clearActions();
      });

      return store.dispatch(handleFetchPackages([1], 1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("PACKAGES"),
          receiveFailed("PACKAGES", expectedErrorPayload("packages")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchDrawings fetches all drawings assigned to user for given job ids", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/drawings/simplified?app_type=fab&is_active=1&job_ids=1&user_ids=1&forge_info=1`
        )
        .replyOnce(200, testDrawings)
        .onGet(
          `${process.env.REACT_APP_API}/drawings/simplified?app_type=fab&is_active=1&job_ids=1&user_ids=1&forge_info=1`
        )
        .replyOnce(404, testError("drawings"));

      await store.dispatch(handleFetchDrawings([1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("DRAWINGS"),
          receiveSucceeded("DRAWINGS", testDrawings),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testDrawings);

        store.clearActions();
      });

      return store.dispatch(handleFetchDrawings([1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("DRAWINGS"),
          receiveFailed("DRAWINGS", expectedErrorPayload("drawings")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchDrawings fetches all drawings assigned to user for given package ids", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/drawings/simplified?app_type=fab&is_active=1&package_ids=1&user_ids=1&forge_info=1`
        )
        .replyOnce(200, testDrawings)
        .onGet(
          `${process.env.REACT_APP_API}/drawings/simplified?app_type=fab&is_active=1&package_ids=1&user_ids=1&forge_info=1`
        )
        .replyOnce(404, testError("drawings"));

      await store.dispatch(handleFetchDrawings(null, [1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("DRAWINGS"),
          receiveSucceeded("DRAWINGS", testDrawings),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testDrawings);

        store.clearActions();
      });

      return store.dispatch(handleFetchDrawings(null, [1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("DRAWINGS"),
          receiveFailed("DRAWINGS", expectedErrorPayload("drawings")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchGenericTimeStages returns all stages for gtime for drawing ids", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/work-stages/gtime?drawing_ids=1`)
        .replyOnce(200, testStages)
        .onGet(`${process.env.REACT_APP_API}/work-stages/gtime?drawing_ids=1`)
        .replyOnce(404, testError("stages"));

      await store
        .dispatch(handleFetchGenericTimeStages(null, null, [1]))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("STAGES"),
            receiveSucceeded("STAGES", testStages),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testStages);

          store.clearActions();
        });

      return store
        .dispatch(handleFetchGenericTimeStages(null, null, [1]))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("STAGES"),
            receiveFailed("STAGES", expectedErrorPayload("stages")),
          ];

          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleFetchGenericTimeStages returns all stages for gtime for package ids", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/work-stages/gtime?package_ids=1`)
        .replyOnce(200, testStages)
        .onGet(`${process.env.REACT_APP_API}/work-stages/gtime?package_ids=1`)
        .replyOnce(404, testError("stages"));

      await store.dispatch(handleFetchGenericTimeStages(null, [1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("STAGES"),
          receiveSucceeded("STAGES", testStages),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testStages);

        store.clearActions();
      });

      return store
        .dispatch(handleFetchGenericTimeStages(null, [1]))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("STAGES"),
            receiveFailed("STAGES", expectedErrorPayload("stages")),
          ];

          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleFetchGenericTimeStages returns all stages for gtime for job ids", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/work-stages/gtime?job_ids=1`)
        .replyOnce(200, testStages)
        .onGet(`${process.env.REACT_APP_API}/work-stages/gtime?job_ids=1`)
        .replyOnce(404, testError("stages"));

      await store.dispatch(handleFetchGenericTimeStages([1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("STAGES"),
          receiveSucceeded("STAGES", testStages),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testStages);

        store.clearActions();
      });

      return store.dispatch(handleFetchGenericTimeStages([1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("STAGES"),
          receiveFailed("STAGES", expectedErrorPayload("stages")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchCostCodes returns all cost codes", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/cost-codes?is_active=1&include_general=1&split_out=0&only_general=0`
        )
        .replyOnce(200, testCostCodes)
        .onGet(
          `${process.env.REACT_APP_API}/cost-codes?is_active=1&include_general=1&split_out=0&only_general=0`
        )
        .replyOnce(404, testError("cost_codes"));

      await store.dispatch(handleFetchCostCodes()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("COST_CODES"),
          receiveSucceeded("COST_CODES", testCostCodes),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testCostCodes);

        store.clearActions();
      });

      return store.dispatch(handleFetchCostCodes()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("COST_CODES"),
          receiveFailed("COST_CODES", expectedErrorPayload("cost_codes")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchCostCodes returns all cost codes for drawing ids", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/cost-codes?is_active=1&include_general=1&split_out=0&only_general=0&drawing_ids=1`
        )
        .replyOnce(200, testMixedCostCodes)
        .onGet(
          `${process.env.REACT_APP_API}/cost-codes?is_active=1&include_general=1&split_out=0&only_general=0&drawing_ids=1`
        )
        .replyOnce(404, testError("cost_codes"));

      await store.dispatch(handleFetchCostCodes(null, null, [1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("COST_CODES"),
          receiveSucceeded("COST_CODES", testMixedCostCodes),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testMixedCostCodes);

        store.clearActions();
      });

      return store.dispatch(handleFetchCostCodes(null, null, [1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("COST_CODES"),
          receiveFailed("COST_CODES", expectedErrorPayload("cost_codes")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchCostCodes returns all cost codes for package ids", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/cost-codes?is_active=1&include_general=1&split_out=0&only_general=0&package_ids=1`
        )
        .replyOnce(200, testMixedCostCodes)
        .onGet(
          `${process.env.REACT_APP_API}/cost-codes?is_active=1&include_general=1&split_out=0&only_general=0&package_ids=1`
        )
        .replyOnce(404, testError("cost_codes"));

      await store.dispatch(handleFetchCostCodes(null, [1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("COST_CODES"),
          receiveSucceeded("COST_CODES", testMixedCostCodes),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testMixedCostCodes);

        store.clearActions();
      });

      return store.dispatch(handleFetchCostCodes(null, [1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("COST_CODES"),
          receiveFailed("COST_CODES", expectedErrorPayload("cost_codes")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchCostCodes returns all cost codes for job ids", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/cost-codes?is_active=1&include_general=1&split_out=0&only_general=0&job_ids=1`
        )
        .replyOnce(200, testMixedCostCodes)
        .onGet(
          `${process.env.REACT_APP_API}/cost-codes?is_active=1&include_general=1&split_out=0&only_general=0&job_ids=1`
        )
        .replyOnce(404, testError("cost_codes"));

      await store.dispatch(handleFetchCostCodes([1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("COST_CODES"),
          receiveSucceeded("COST_CODES", testMixedCostCodes),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testMixedCostCodes);

        store.clearActions();
      });

      return store.dispatch(handleFetchCostCodes([1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("COST_CODES"),
          receiveFailed("COST_CODES", expectedErrorPayload("cost_codes")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });
  });
});
