@import "../styles/colors.scss";
@import "../styles/sizes.scss";

div.generic-wrapper {
  height: calc(100vh - 60px - 50px - 33px);
  width: calc(100vw - 55px);
  padding: 0 20px;
  box-sizing: border-box;
  overflow: auto;

  & .btn {
    background-color: white;
    font-size: 0.8rem;
    display: block;
    margin: 0 auto;
    height: 32px;
    padding: 8px 12px;
  }

  & a {
    width: 430px;
    border-radius: 3px;
    color: black;
    text-decoration: none;
    box-sizing: border-box;
  }

  & a.neutral {
    display: inline;
    background-color: $fabProBlue;
    color: white;
  }

  & a span {
    font-weight: 600;
  }
}

div.layout-body.narrow div.generic-wrapper {
  width: calc(100vw - #{$navExpanded});
}

div.generic-filters-wrapper {
  height: 25vh;
  min-height: 200px;
  width: 100%;

  & button.generic-download-majs {
    margin-right: 5px;
    display: inline;
    background-color: $fabProBlue;
    color: white;

    &:not(:disabled):hover {
      background-color: darken($fabProBlue, 10%);
    }
  }

  & button.manage,
  button.generic-download-majs {
    color: white;
    background-color: $fabProBlue;
    display: inline;
  }

  & button.start {
    margin: 0;
    background-color: $green;
    color: white;
    display: flex;
    align-items: center;
    gap: 5px;
  }

  & button.stop {
    background-color: $orange;
    color: white;
    display: flex;
    align-items: center;
    gap: 5px;
    margin: 0;
  }

  & div.filters-list {
    display: flex;
    margin-bottom: 100px;
    flex-wrap: wrap;
  }
}

div.selectable-filter-wrapper {
  margin-right: 30px;

  & div.selected-item p {
    max-width: 100px;
  }
}

div.drawing-viewer {
  & div.pdf-viewer-wrapper {
    width: 100%;
    height: 100%;
  }
}
