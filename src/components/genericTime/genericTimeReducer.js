const initialState = {
  jobs: null,
  packages: null,
  drawings: null,
  stages: null,
  costCodes: null,
  isLoading: false,
  error: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_JOBS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_JOBS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, jobs: payload };
    case "RECEIVE_JOBS_FAILED":
      return { ...state, isLoading: false, error: payload, jobs: [] };
    case "RECEIVE_PACKAGES_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_PACKAGES_SUCCEEDED":
      return { ...state, isLoading: false, error: null, packages: payload };
    case "RECEIVE_PACKAGES_FAILED":
      return { ...state, isLoading: false, error: payload, packages: [] };
    case "RECEIVE_DRAWINGS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_DRAWINGS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, drawings: payload };
    case "RECEIVE_DRAWINGS_FAILED":
      return { ...state, isLoading: false, error: payload, drawings: [] };
    case "RECEIVE_STAGES_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_STAGES_SUCCEEDED":
      return { ...state, isLoading: false, error: null, stages: payload };
    case "RECEIVE_STAGES_FAILED":
      return { ...state, isLoading: false, error: payload, stages: [] };
    case "RECEIVE_COST_CODES_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_COST_CODES_SUCCEEDED":
      return { ...state, isLoading: false, error: null, costCodes: payload };
    case "RECEIVE_COST_CODES_FAILED":
      return { ...state, isLoading: false, error: payload, costCodes: [] };
    default:
      return state;
  }
}
