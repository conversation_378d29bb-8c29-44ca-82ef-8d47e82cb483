// NPM PACKAGE IMPORTS
import React, { useEffect, useRef } from "react";
import { BrowserRouter, Route } from "react-router-dom";
import { Provider } from "react-redux";
import { HTML5Backend } from "react-dnd-html5-backend";
import { DndProvider } from "react-dnd";

// REDUX IMPORTS
import store from "../redux/store";

// COMPONENT IMPORTS
import AlertPopupContainer from "./reusable/alertPopup/AlertPopupContainer";
import GeneralSpinnerContainer from "./reusable/generalSpinner/GeneralSpinnerContainer";
import GeneralModalContainer from "./reusable/generalModal/GeneralModalContainer";
import Header from "./layout/header/Header";
import Nav from "./layout/nav/Nav";
import Body from "./layout/body/Body";
import Footer from "./layout/footer/Footer";
import WorkTable from "./workTable/WorkTable";
import Credits from "./legalDocuments/credits/Credits";
import TermsOfService from "./legalDocuments/termsOfService/TermsOfService";
// import ManpowerHome from "./manpower/Manpower";
import Login from "./login/Login";
import Flows from "./flows/Flows";
import Wizard from "./wizard";
import MyWorkWrapper from "./myWork/myWorkWrapper";
import Throughputs from "./throughputs";
import GenericTime from "./genericTime/GenericTime";
import PendingApproval from "./pendingApproval/PendingApproval";
import Profile from "./profile/Profile";
import Notifications from "./notifications/Notifications";
import Users from "./users/Users";
import AutodeskAccountSettings from "./autodeskAccountSettings/AutodeskAccountSettings";
import { AuthProvider } from "./auth/AuthContext";
import ManageAssignments from "./manageAssignments/ManageAssignments";
import CustomColumns from "./customColumns/CustomColumns";
// import PowerbiAnalyticsContainer from "./powerbiAnalytics/PowerbiAnalyticsContainer";
import DashboardAnalytics from "./dashboardAnalytics";
import AxiosHookTestUI from "./axiosTest/AxiosHookTestUI";

import theme from "../theme/theme";
import { ThemeProvider } from "@mui/material/styles";

// STYLE IMPORTS
import "./styles/app.scss";

// DATA TRACKING
import { initialize, register } from "@sbd-ctg/user-behavior-tracking";
import { setUpListeners } from "./../utils/_userControlUtils";

//initialize user behavior tracking
const userBehaviorToken = `${process.env.REACT_APP_USER_BEHAVIOR_TRACKING_TOKEN}`;
initialize(userBehaviorToken);

//TODO: register global props
register({
  product: "msuite fab",
  environment: process.env.REACT_APP_ENVIRONMENT,
  version: "2.5",
  application: "Web",
});

// EXPORTS
function App() {
  // load Churn
  const calledOnce = useRef(false);
  useEffect(() => {
    if (calledOnce.current) return;

    const churnEndpoint = process.env.REACT_APP_CHURN_ENDPOINT;
    (function (a, b, c, d) {
      var cz = a.createElement(b);
      cz.type = c;
      cz.async = true;
      cz.src = d;
      var s = a.getElementsByTagName(b)[0];
      s.parentNode.insertBefore(cz, s);
    })(document, "script", "text/javascript", churnEndpoint);

    calledOnce.current = true;
  }, []);

  const fixUrlTrailingSlash = () => {
    if (
      // IF ON LOCALHOST, REMOVE TRAILING SLASH
      window.location.hostname === "localhost" &&
      /^\/.*\/$/.test(window.location.pathname)
    ) {
      window.location.replace(
        `${window.location.protocol}//${
          window.location.host
        }${window.location.pathname.replace(/\/$/, "")}${
          window.location.search
        }`
      );
    } else if (
      // IF ON SERVER, ADD TRAILING SLASH
      window.location.hostname !== "localhost" &&
      !/^\/.*\/$/.test(window.location.pathname)
    ) {
      window.location.replace(
        `${window.location.protocol}//${window.location.host}${window.location.pathname}/${window.location.search}`
      );
    }
  };
  fixUrlTrailingSlash();

  // CREATE locationchange EVENT FOR url CHANGES
  // READ MORE HERE ---> https://dirask.com/posts/JavaScript-on-location-changed-event-on-url-changed-event-DKeyZj
  (function () {
    var pushState = window.history.pushState;
    var replaceState = window.history.replaceState;

    window.history.pushState = function () {
      pushState.apply(window.history, arguments);
      window.dispatchEvent(new Event("pushstate"));
      window.dispatchEvent(new Event("locationchange"));
    };

    window.history.replaceState = function () {
      replaceState.apply(window.history, arguments);
      window.dispatchEvent(new Event("replacestate"));
      window.dispatchEvent(new Event("locationchange"));
    };

    window.addEventListener("popstate", function () {
      window.dispatchEvent(new Event("locationchange"));
    });
  })();

  window.addEventListener("locationchange", function () {
    // FIRES IF url CHANGES
    fixUrlTrailingSlash();
  });

  setUpListeners();

  return (
    <BrowserRouter>
      <Provider store={store}>
        <ThemeProvider theme={theme}>
          <AuthProvider>
            <DndProvider backend={HTML5Backend}>
              <AlertPopupContainer />
              <GeneralSpinnerContainer />
              <GeneralModalContainer />
              <Header />
              <Nav />
              <Route path="/login" component={Login} />
              <Body>
                <Route path="/jobs" component={WorkTable} />
                {/* <Route path="/manpower" exact component={ManpowerHome} /> */}
                <Route path="/credits" component={Credits} />
                <Route path="/terms-of-service" component={TermsOfService} />
                <Route path="/settings/flows" component={Flows} />
                <Route path="/settings/throughputs" component={Throughputs} />
                <Route path="/new-job" component={Wizard} />
                <Route path="/new-costcodes" component={Wizard} />
                <Route path="/new-packages" component={Wizard} />
                <Route path="/new-drawings" component={Wizard} />
                <Route path="/new-items" component={Wizard} />
                <Route path="/my-work" component={MyWorkWrapper} />
                <Route
                  path="/packages-pending-approval"
                  component={PendingApproval}
                />
                <Route
                  path="/drawings-pending-approval"
                  component={PendingApproval}
                />
                <Route path="/generic-time" component={GenericTime} />
                <Route path="/profile" component={Profile} />
                <Route path="/notifications" component={Notifications} />
                <Route path="/users" component={Users} />
                <Route
                  path="/autodesk-account-settings"
                  component={AutodeskAccountSettings}
                />
                <Route
                  path="/manpower/assignments"
                  component={ManageAssignments}
                />
                <Route
                  path="/settings/custom-columns"
                  component={CustomColumns}
                />
                {/* <Route
                  path="/dashboard-analytics"
                  component={PowerbiAnalyticsContainer}
                /> */}
                <Route
                  path="/dashboard-analytics"
                  component={DashboardAnalytics}
                />
                <Route path="/test-axios" component={AxiosHookTestUI} />
              </Body>
              <Footer />
            </DndProvider>
          </AuthProvider>
        </ThemeProvider>
      </Provider>
    </BrowserRouter>
  );
}

export default App;
