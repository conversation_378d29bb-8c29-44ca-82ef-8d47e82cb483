import {
  fetchDrawings,
  archiveDrawing,
  deleteDrawing,
  updateDrawingPriorities,
  updateContainers,
  editWork,
  fetchContainers,
  createContainer,
  fetchStatusTrackerStages,
  fetchPendingDrawingsExist,
  fetchItemHeatNumbers,
  deleteContainer,
  duplicateDrawing,
  createDrawingsViaPdfs,
  submitRevisedPdf,
  fetchDrawingById,
  moveDrawingsBetweenPackages,
} from "../../_services";
import { trackMixPanelEvent } from "../../utils/_mixPanelUtils";

import store from "../../redux/store";

export const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});
export const updateStarted = (type) => ({ type: `UPDATE_${type}_STARTED` });
export const updateSucceeded = (type, payload) => ({
  type: `UPDATE_${type}_SUCCEEDED`,
  payload,
});
export const updateFailed = (type, error) => ({
  type: `UPDATE_${type}_FAILED`,
  payload: error,
});
export const deleteStarted = (type) => ({ type: `DELETE_${type}_STARTED` });
export const deleteEnded = (type) => ({ type: `DELETE_${type}_ENDED` });
export const archiveStarted = (type) => ({ type: `ARCHIVE_${type}_STARTED` });
export const archiveEnded = (type) => ({ type: `ARCHIVE_${type}_ENDED` });
export const createStarted = (type) => ({ type: `CREATE_${type}_STARTED` });
export const createSucceeded = (type) => ({
  type: `CREATE_${type}_SUCCEEDED`,
});
export const createFailed = (type, error) => ({
  type: `CREATE_${type}_FAILED`,
  payload: error,
});
export const replaceSucceeded = (type, payload) => ({
  type: `REPLACE_${type}_SUCCEEDED`,
  payload,
});

export const handleClearDrawingsSavedColumnState = (dispatch) => {
  dispatch(receiveSucceeded("DRAWINGS_CLEAR_COLUMN_STATE"));
};

export const handleFetchDrawings = (packageIds, all, stages) => (dispatch) => {
  dispatch(receiveStarted("DRAWINGS"));
  return fetchDrawings(null, packageIds, all, stages).then((res) => {
    if (res.error) {
      dispatch(receiveFailed("DRAWINGS", res));
      return res;
    }
    dispatch(receiveSucceeded("DRAWINGS", res));
    return res;
  });
};
export const handleFetchAllDrawings = () => (dispatch) => {
  dispatch(receiveStarted("ALL_DRAWINGS"));
  return fetchDrawings(null, null, true).then((res) => {
    if (res.error) return dispatch(receiveFailed("ALL_DRAWINGS", res));
    return dispatch(receiveSucceeded("ALL_DRAWINGS", res));
  });
};
export const handleUpdateDrawings = (
  idsToUpdate,
  updatedData,
  callAction,
  parentData
) => (dispatch) => {
  dispatch(updateStarted("DRAWINGS"));
  return editWork(idsToUpdate, updatedData, "drawings", callAction).then(
    (res) => {
      if (res.error) dispatch(updateFailed("DRAWINGS", res));
      else dispatch(updateSucceeded("DRAWINGS", res));
      if (parentData) {
        return fetchDrawings(null, ...parentData).then((res) => {
          dispatch(receiveStarted("DRAWINGS"));
          if (res.error) dispatch(receiveFailed("DRAWINGS", res));
          else dispatch(receiveSucceeded("DRAWINGS", res));

          return res;
        });
      }

      return res;
    }
  );
};

export const handleUpdateDrawingPriorities = (
  updatedRows,
  selectedPackageIds,
  showAllWork = false
) => (dispatch) => {
  dispatch(updateStarted("PRIORITIES"));
  return updateDrawingPriorities(updatedRows).then((res) => {
    if (res.error) dispatch(updateFailed("PRIORITIES", res));
    else {
      dispatch(updateSucceeded("PRIORITIES"));
      return fetchDrawings(null, selectedPackageIds, showAllWork).then(
        (res) => {
          dispatch(receiveStarted("DRAWINGS"));
          if (res.error) dispatch(receiveFailed("DRAWINGS", res));
          else dispatch(receiveSucceeded("DRAWINGS", res));

          return res;
        }
      );
    }

    return res;
  });
};

export const handleFetchContainers = (jobIds) => (dispatch) => {
  dispatch(receiveStarted("CONTAINERS"));
  return fetchContainers(jobIds).then((res) => {
    if (res.error) return dispatch(receiveFailed("CONTAINERS", res));
    return dispatch(receiveSucceeded("CONTAINERS", res));
  });
};

export const handleUpdateContainers = (ids, data, parentData) => (dispatch) => {
  dispatch(updateStarted("CONTAINERS"));
  return updateContainers(ids, data).then((res) => {
    if (res.error) return dispatch(updateFailed("CONTAINERS", res));
    dispatch(updateSucceeded("CONTAINERS", res));

    if (parentData) {
      return fetchDrawings(null, ...parentData).then((res) => {
        dispatch(receiveStarted("DRAWINGS"));
        if (res.error) dispatch(receiveFailed("DRAWINGS", res));
        else dispatch(receiveSucceeded("DRAWINGS", res));

        return res;
      });
    }
  });
};

export const handleCreateContainer = (
  jobId,
  drawingIds,
  stage,
  area,
  containerData
) => (dispatch) => {
  const type = "CONTAINER";
  dispatch(createStarted(type));
  return createContainer(jobId, drawingIds, stage, area, containerData).then(
    (res) => {
      if (res.error) return dispatch(createFailed(type, res));
      dispatch(createSucceeded(type));
      return fetchContainers([jobId]).then((res) => {
        dispatch(receiveStarted("CONTAINERS"));
        if (res.error) dispatch(receiveFailed("CONTAINERS", res));
        dispatch(receiveSucceeded("CONTAINERS", res));
        return res;
      });
    }
  );
};

export const handleArchiveDrawing = (drawingId) => (dispatch) => {
  dispatch(archiveStarted("DRAWING"));
  dispatch(updateStarted("DRAWINGS"));
  return archiveDrawing(drawingId).then((res) => {
    dispatch(archiveEnded("DRAWING"));
    dispatch(updateSucceeded("DRAWINGS"));
    return res;
  });
};

export const handleDeleteDrawings = (drawingIds) => (dispatch) => {
  dispatch(deleteStarted("DRAWING"));
  dispatch(updateStarted("DRAWINGS"));
  return deleteDrawing(drawingIds).then((res) => {
    dispatch(deleteEnded("DRAWING"));
    dispatch(updateSucceeded("DRAWINGS"));
    return res;
  });
};

export const handleFetchStatusTrackerStages = (drawingId) => (dispatch) => {
  const type = "DRAWINGS_STATUS_TRACKER";
  return fetchStatusTrackerStages("drawings", drawingId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));
    return res;
  });
};

export const handleFetchPendingDrawingsExist = () => (dispatch) => {
  const type = "DRAWINGS_PENDING_APPROVAL_FLAG";

  return fetchPendingDrawingsExist().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchItemHeatNumbers = (
  columnType,
  jobIds,
  size,
  materialType,
  drawingIds
) => (dispatch) => {
  const type = "HEAT_NUMBERS";

  dispatch(receiveStarted(type));
  return fetchItemHeatNumbers(
    columnType,
    jobIds,
    size,
    materialType,
    drawingIds
  ).then((res) => {
    if (res.error) return dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleDeleteContainer = (containerId, stageId) => (dispatch) => {
  return deleteContainer(containerId, stageId);
};

export const handleDuplicateDrawing = (drawingId, qty = 1) => (dispatch) => {
  const type = "WIZARD_DRAWINGS_ADDITIONAL";

  dispatch(receiveStarted(type));
  return duplicateDrawing(drawingId, qty).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleCreateDrawingsViaPdfs = (pkgId, files) => (dispatch) => {
  const type = "WIZARD_DRAWINGS_ADDITIONAL";

  dispatch(receiveStarted(type));
  return createDrawingsViaPdfs(pkgId, files).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleSubmitRevisedPdf = (drawingId, file) => (dispatch) => {
  if (window && window.ChurnZero) {
    const { username } = store.getState().profileData.userInfo;
    const { userId } = store.getState().profileData;

    window.ChurnZero.push([
      "trackEvent",
      "FP Revised PDF Uploaded",
      `Drawing ID: ${drawingId}, Revised PDF`,
      1,
      {
        Product: "FabPro",
        SubGroup: "Drawings",
        Version: process.env.REACT_APP_ENVIRONMENT,
        UserName: username,
        UserId: userId,
      },
    ]);
  }
  trackMixPanelEvent("Submit Revised PDF", 1, "Drawings");

  return submitRevisedPdf(drawingId, file);
};

export const handleFetchDrawingsById = (ids = "") => (dispatch) => {
  return fetchDrawingById(ids);
};

export const handleMoveDrawingsBetweenPackages = (
  drawingIds,
  targetPackageId
) => (dispatch) => {
  return moveDrawingsBetweenPackages(drawingIds, targetPackageId).then(
    (res) => {
      return res;
    }
  );
};

export const handleResetDrawingsList = (dispatch) => {
  const type = "RESET_DRAWINGS_LIST";
  dispatch(receiveSucceeded(type));
};

export const handleSetDrawingsList = (packages = []) => (dispatch) => {
  const type = "SET_DRAWINGS_LIST";
  dispatch(receiveSucceeded(type, packages));
};

// this is used to handle inline edits and properly update the cached state
// Need to update both drawings and storedListOfDrawings in order to avoid any stale cached data
export const handleUpdateDrawingsNoRefresh = (updatedDrawings) => (
  dispatch
) => {
  const type1 = "DRAWINGS";
  const type2 = "SET_DRAWINGS_LIST";

  const { drawings, storedListOfDrawings } = store.getState().drawingsData;
  for (let updatedDrawing of updatedDrawings) {
    const updatedIndex1 = drawings?.findIndex(
      (d) => d.id === updatedDrawing.id
    );
    const updatedIndex2 = storedListOfDrawings?.findIndex(
      (d) => d.id === updatedDrawing.id
    );
    if (updatedIndex1 !== -1 && updatedIndex2 !== -1) {
      drawings[updatedIndex1] = updatedDrawing;
      storedListOfDrawings[updatedIndex2] = updatedDrawing;
    }
  }
  dispatch(receiveSucceeded(type1, drawings));
  dispatch(receiveSucceeded(type2, storedListOfDrawings));
};
