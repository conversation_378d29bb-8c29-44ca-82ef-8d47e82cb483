// NPM PACKAGE IMPORTS
import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockAdapter from "axios-mock-adapter";
import thunk from "redux-thunk";
import { drawingsColumnDefs } from "./drawingsConstants";
import {
  handleFetchAllDrawings,
  handleFetchDrawings,
  handleUpdateDrawings,
  handleUpdateDrawingPriorities,
  handleFetchContainers,
  handleUpdateContainers,
  handleCreateContainer,
  handleArchiveDrawing,
  handleDeleteDrawings,
  handleFetchStatusTrackerStages,
  handleFetchPendingDrawingsExist,
  handleFetchItemHeatNumbers,
  handleDeleteContainer,
  handleDuplicateDrawing,
  handleCreateDrawingsViaPdfs,
  handleSubmitRevisedPdf,
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  updateStarted,
  updateSucceeded,
  updateFailed,
  deleteStarted,
  deleteEnded,
  archiveStarted,
  archiveEnded,
  createStarted,
  createSucceeded,
  createFailed,
} from "./drawingsActions";

describe("Drawings", () => {
  const testError = (type) => ({
    error: { status: 404, message: `No ${type} found.` },
  });
  const fab_base_url = "https://dev-fab.msuite.com";
  const testDrawings = [
    {
      id: 105534,
      name: "Example_Drawing_2",
      cost_time: 0,
      est_time_left: 0,
      work_flow_id: 23,
      drawing_area: null,
      fp_budget: 0,
      hours_ahead_behind: 0,
      prod_time: 0,
      laydown_location: null,
      job_id: 1139,
      job_number: "45994",
      job_title: "2.5 TEST",
      package_name: "2.5 TEST PACKAGE 2",
      package_area: null,
      package_id: 2204,
      package_number: null,
      priority: 5560,
      generic: 0,
      job_generic: 0,
      package_generic: 0,
      pending_approval: 0,
      percent_complete: 0,
      on_hold: 0,
      archived: 0,
      archived_by: null,
      stage_name: "Pending Approval",
      container_id: null,
      container_name: null,
      container_description: null,
      container_locked: 0,
      due_date: 1614920400,
      has_original: 1,
      has_annotated: 0,
      package_map: `${fab_base_url}/fabrications/fabpro1/1139/2204/package_maps/48/map.pdf`,
      has_isogrid: 0,
      drawing_status: "In Fabrication",
      work_item_count: 0,
      forge_urn: null,
      fab_completed_on: null,
    },
    {
      id: 105535,
      name: "Example_drawing_1",
      cost_time: 0,
      est_time_left: 0,
      work_flow_id: 23,
      drawing_area: null,
      fp_budget: 0,
      hours_ahead_behind: 0,
      prod_time: 0,
      laydown_location: null,
      job_id: 1139,
      job_number: "45994",
      job_title: "2.5 TEST",
      package_name: "2.5 TEST PACKAGE 2",
      package_area: null,
      package_id: 2204,
      package_number: null,
      priority: 5561,
      generic: 0,
      job_generic: 0,
      package_generic: 0,
      pending_approval: 0,
      percent_complete: 0,
      on_hold: 0,
      archived: 0,
      archived_by: null,
      stage_name: "Pending Approval",
      container_id: null,
      container_name: null,
      container_description: null,
      container_locked: 0,
      due_date: 1614920400,
      has_original: 1,
      has_annotated: 0,
      package_map: `${fab_base_url}/fabrications/fabpro1/1139/2204/package_maps/48/map.pdf`,
      has_isogrid: 0,
      drawing_status: "In Fabrication",
      work_item_count: 0,
      forge_urn: null,
      fab_completed_on: null,
    },
  ];
  const testContainers = [
    {
      id: 1,
      name: "web cont",
      description: "",
      job_id: 2,
      job_name: "JK_mgCount ",
      job_number: "324",
      laydown_location: null,
      created_by: 158,
      created_on: "2019-02-27T23:52:33.000Z",
      deleted_by: null,
      deleted_on: null,
      deleted: 0,
      locked: 0,
      location_name: null,
      location_id: null,
      drawing_ids: null,
      on_hold: 0,
      loaded_date: null,
      unavailable: 1,
    },
    {
      id: 4,
      name: "ADD MAT",
      description: "",
      job_id: 3,
      job_name: "Revit 02",
      job_number: "01",
      laydown_location: "123",
      created_by: 120,
      created_on: "2019-02-28T01:27:43.000Z",
      deleted_by: null,
      deleted_on: null,
      deleted: 0,
      locked: 0,
      location_name: null,
      location_id: null,
      drawing_ids: null,
      on_hold: 0,
      loaded_date: null,
      unavailable: 1,
    },
  ];

  describe("action handlers should perform the necessary functions", () => {
    let store;
    let httpMock;

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    it("handleFetchAllDrawings fetches all drawings", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/drawings/simplified?app_type=fab&is_active=1&forge_info=1`
        )
        .replyOnce(200, testDrawings)
        .onGet(
          `${process.env.REACT_APP_API}/drawings/simplified?app_type=fab&is_active=1&forge_info=1`
        )
        .replyOnce(404, testError("drawings"));

      await store.dispatch(handleFetchAllDrawings()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("ALL_DRAWINGS"),
          receiveSucceeded("ALL_DRAWINGS", testDrawings),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testDrawings);

        store.clearActions();
      });

      return store.dispatch(handleFetchAllDrawings()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("ALL_DRAWINGS"),
          receiveFailed("ALL_DRAWINGS", testError("drawings")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });
    it("handleFetchDrawings fetches drawings for user", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/drawings/simplified?app_type=fab&is_active=1&user_ids=1&forge_info=1`
        )
        .replyOnce(200, testDrawings)
        .onGet(
          `${process.env.REACT_APP_API}/drawings/simplified?app_type=fab&is_active=1&user_ids=1&forge_info=1`
        )
        .replyOnce(404, testError("drawings"));

      await store.dispatch(handleFetchDrawings()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("DRAWINGS"),
          receiveSucceeded("DRAWINGS", testDrawings),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testDrawings);

        store.clearActions();
      });

      return store.dispatch(handleFetchDrawings()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("DRAWINGS"),
          receiveFailed("DRAWINGS", testError("drawings")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });
    it("handleFetchDrawings fetches drawings in package for user", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/drawings/simplified?app_type=fab&is_active=1&package_ids=2204&user_ids=1&forge_info=1`
        )
        .replyOnce(200, testDrawings)
        .onGet(
          `${process.env.REACT_APP_API}/drawings/simplified?app_type=fab&is_active=1&package_ids=2204&user_ids=1&forge_info=1`
        )
        .replyOnce(404, testError("drawings"));

      await store.dispatch(handleFetchDrawings([2204])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("DRAWINGS"),
          receiveSucceeded("DRAWINGS", testDrawings),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testDrawings);

        store.clearActions();
      });

      return store.dispatch(handleFetchDrawings([2204])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("DRAWINGS"),
          receiveFailed("DRAWINGS", testError("drawings")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });
    it("handleFetchContainers fetches containers for user", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/containers?is_active=1&locked=0&app_type=fab`
        )
        .replyOnce(200, testContainers)
        .onGet(
          `${process.env.REACT_APP_API}/containers?is_active=1&locked=0&app_type=fab`
        )
        .replyOnce(404, testError("containers"));

      await store.dispatch(handleFetchContainers()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("CONTAINERS"),
          receiveSucceeded("CONTAINERS", testContainers),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testContainers);

        store.clearActions();
      });

      return store.dispatch(handleFetchContainers()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("CONTAINERS"),
          receiveFailed("CONTAINERS", testError("containers")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });
    it("handleFetchStatusTrackerStages fetches status tracker on drawing", async () => {
      const testStatusTracker = [
        {
          stage_id: 54,
          stage_name: "Material Handling",
          total_items: 51,
          completed_items: 0,
        },
        {
          stage_id: 53,
          stage_name: "Materials Prep",
          total_items: 51,
          completed_items: 0,
        },
      ];
      httpMock
        .onGet(`${process.env.REACT_APP_API}/drawings/status-tracker/264`)
        .replyOnce(200, testStatusTracker)
        .onGet(`${process.env.REACT_APP_API}/drawings/status-tracker/264`)
        .replyOnce(404, testError("drawings status tracker"));

      await store.dispatch(handleFetchStatusTrackerStages(264)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveSucceeded("DRAWINGS_STATUS_TRACKER", testStatusTracker),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload).toEqual(testStatusTracker);

        store.clearActions();
      });

      return store.dispatch(handleFetchStatusTrackerStages(264)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveFailed("DRAWINGS_STATUS_TRACKER", {
            status: 404,
            message: "No drawings status tracker found.",
          }),
        ];
        expect(receivedActions).toEqual(expectedActions);
      });
    });
    it("handleFetchPendingDrawingsExist returns if there are drawings in pending approval", async () => {
      const testPendingApprovalDrawingResponse = {
        has_pending_drawings: 1,
      };
      httpMock
        .onGet(`${process.env.REACT_APP_API}/v2/drawings/pending?app_type=fab`)
        .replyOnce(200, testPendingApprovalDrawingResponse);

      return store.dispatch(handleFetchPendingDrawingsExist()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveSucceeded(
            "DRAWINGS_PENDING_APPROVAL_FLAG",
            testPendingApprovalDrawingResponse
          ),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload).toEqual(
          testPendingApprovalDrawingResponse
        );

        store.clearActions();
      });
    });
    it("handleFetchItemHeatNumbers if JOINT is itemType - fetches joint heat numbers with drawing id", async () => {
      const testHeatNumber = [
        {
          heat_number: "123",
          material_name: "copper",
        },
      ];
      httpMock
        .onGet(`${process.env.REACT_APP_API}/items/heat-numbers?drawing_ids=1`)
        .replyOnce(200, testHeatNumber)
        .onGet(
          `${process.env.REACT_APP_API}/items/heat-numbers?size=84"x24"&material_type_name=copper&job_ids=1`
        )
        .replyOnce(404, testError("HEAT_NUMBERS"));

      await store
        .dispatch(
          handleFetchItemHeatNumbers("JOINT", [1], '84"x24"', "copper", [1])
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("HEAT_NUMBERS"),
            receiveSucceeded("HEAT_NUMBERS", testHeatNumber),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testHeatNumber);

          store.clearActions();
        });

      return store
        .dispatch(
          handleFetchItemHeatNumbers("OTHER", [1], '84"x24"', "copper", [1])
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("HEAT_NUMBERS"),
            receiveFailed("HEAT_NUMBERS", testError("HEAT_NUMBERS")),
          ];
          expect(receivedActions).toEqual(expectedActions);
        });
    });
    it("handleFetchItemHeatNumbers fetches heat numbers with job id if JOINT isn't passed", async () => {
      const testHeatNumber = [
        {
          heat_number: "123",
          material_name: "copper",
        },
      ];
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/items/heat-numbers?size=84"x24"&material_type_name=copper&job_ids=1`
        )
        .replyOnce(200, testHeatNumber)
        .onGet(
          `${process.env.REACT_APP_API}/items/heat-numbers?size=84"x24"&material_type_name=copper&job_ids=1`
        )
        .replyOnce(404, testError("HEAT_NUMBERS"));

      await store
        .dispatch(handleFetchItemHeatNumbers("", [1], '84"x24"', "copper", [1]))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("HEAT_NUMBERS"),
            receiveSucceeded("HEAT_NUMBERS", testHeatNumber),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testHeatNumber);

          store.clearActions();
        });

      return store
        .dispatch(handleFetchItemHeatNumbers("", [1], '84"x24"', "copper", [1]))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("HEAT_NUMBERS"),
            receiveFailed("HEAT_NUMBERS", testError("HEAT_NUMBERS")),
          ];
          expect(receivedActions).toEqual(expectedActions);
        });
    });
    it("handleUpdateDrawings: Update Drawings by id", async () => {
      const updatedDrawingData = { drawing_name: "updated drawing" };
      const updatedDrawing = [
        {
          id: 1,
          drawing_name: "updated drawing",
          drawing_number: "0306159-1",
          archived: 0,
          deleted: 0,
        },
      ];
      const updateBody = {
        callAction: "update",
        callParams: { drawing_name: "updated drawing" },
      };

      httpMock
        .onPut(`${process.env.REACT_APP_API}/drawings/1`)
        .replyOnce(200, updatedDrawing)
        .onPut(`${process.env.REACT_APP_API}/drawings/1`)
        .replyOnce(404, testError("DRAWINGS"));

      await store
        .dispatch(handleUpdateDrawings(1, updatedDrawingData))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("DRAWINGS"),
            updateSucceeded("DRAWINGS", updatedDrawing),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(updatedDrawing);
          expect(httpMock.history.put[0].data).toEqual(
            JSON.stringify(updateBody)
          );
          store.clearActions();
        });
      return store
        .dispatch(handleUpdateDrawings(1, updatedDrawingData))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("DRAWINGS"),
            updateFailed("DRAWINGS", testError("DRAWINGS")),
          ];
          expect(receivedActions).toEqual(expectedActions);
        });
    });
    it("handleUpdateDrawingPriorities: Update Drawing Priorities, then get drawings for that package id", async () => {
      const updatedPriority = [
        {
          id: 1,
          new_priority: 2,
          old_priority: 3,
        },
      ];
      httpMock
        .onPut(`${process.env.REACT_APP_API}/drawings/priorities`)
        .replyOnce(200, updatedPriority)
        .onGet(
          `${process.env.REACT_APP_API}/drawings/simplified?app_type=fab&is_active=1&package_ids=1&user_ids=1&forge_info=1`
        )
        .replyOnce(200, testDrawings)
        .onPut(`${process.env.REACT_APP_API}/drawings/priorities`)
        .replyOnce(404, testError("PRIORITIES"));

      await store
        .dispatch(handleUpdateDrawingPriorities(updatedPriority, [1]))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("PRIORITIES"),
            updateSucceeded("PRIORITIES"),
            receiveStarted("DRAWINGS"),
            receiveSucceeded("DRAWINGS", testDrawings),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[3].payload).toEqual(testDrawings);

          store.clearActions();
        });
      return store
        .dispatch(handleUpdateDrawingPriorities(updatedPriority, [1]))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("PRIORITIES"),
            updateFailed("PRIORITIES", testError("PRIORITIES")),
          ];
          expect(receivedActions).toEqual(expectedActions);
        });
    });
    it("handleUpdateContainers: Update Containers", async () => {
      const testBody = {
        name: "test container",
        description: "test",
        laydown_location: "test laydown location",
      };

      httpMock
        .onPut(`${process.env.REACT_APP_API}/containers/1`)
        .replyOnce(200, testContainers)
        .onPut(`${process.env.REACT_APP_API}/containers/1`)
        .replyOnce(404, testError("CONTAINERS"));

      await store.dispatch(handleUpdateContainers([1], testBody)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          updateStarted("CONTAINERS"),
          updateSucceeded("CONTAINERS", testContainers),
        ];
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testContainers);
        expect(httpMock.history.put[0].data).toEqual(JSON.stringify(testBody));

        store.clearActions();
      });
      return store.dispatch(handleUpdateContainers([1], testBody)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          updateStarted("CONTAINERS"),
          updateFailed("CONTAINERS", testError("CONTAINERS")),
        ];
        expect(receivedActions).toEqual(expectedActions);
      });
    });
    it("handleCreateContainer: creates container, then fetches new container list", async () => {
      const containerData = {
        name: "test container",
        description: "test",
        laydown_location: "test laydown location",
      };
      const testBody = {
        name: "test container",
        description: "test",
        job_id: 1,
        drawing_ids: [1, 2],
        area: "1st Floor",
        stage_id: 1,
        laydown_location: "test laydown location",
      };

      httpMock
        .onPost(`${process.env.REACT_APP_API}/containers`)
        .replyOnce(200, testBody)
        .onGet(
          `${process.env.REACT_APP_API}/containers?is_active=1&locked=0&app_type=fab&job_ids=1`
        )
        .replyOnce(200, testContainers)
        .onPost(`${process.env.REACT_APP_API}/containers`)
        .replyOnce(404, testError("CONTAINER"));

      await store
        .dispatch(
          handleCreateContainer(1, [1, 2], 1, "1st Floor", containerData)
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            createStarted("CONTAINER"),
            createSucceeded("CONTAINER", testBody),
            receiveStarted("CONTAINERS"),
            receiveSucceeded("CONTAINERS", testContainers),
          ];
          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[3].payload).toEqual(testContainers);
          expect(httpMock.history.post[0].data).toEqual(
            JSON.stringify(testBody)
          );

          store.clearActions();
        });
      return store
        .dispatch(
          handleCreateContainer(1, [1, 2], 1, "1st Floor", containerData)
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            createStarted("CONTAINER"),
            createFailed("CONTAINER", testError("CONTAINER")),
          ];
          expect(receivedActions).toEqual(expectedActions);
        });
    });
    it("handleArchiveDrawing: archives drawing", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/drawings/archive/1`)
        .replyOnce(200);

      await store.dispatch(handleArchiveDrawing(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          archiveStarted("DRAWING"),
          updateStarted("DRAWINGS"),
          archiveEnded("DRAWING"),
          updateSucceeded("DRAWINGS"),
        ];
        expect(receivedActions).toEqual(expectedActions);
        store.clearActions();
      });
    });
    it("handleDeleteDrawings: deletes drawings", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/drawings/delete/1`)
        .replyOnce(200);

      await store.dispatch(handleDeleteDrawings(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          deleteStarted("DRAWING"),
          updateStarted("DRAWINGS"),
          deleteEnded("DRAWING"),
          updateSucceeded("DRAWINGS"),
        ];
        expect(receivedActions).toEqual(expectedActions);
        store.clearActions();
      });
    });
    it("handleDeleteContainer: delete container", async () => {
      httpMock
        .onDelete(`${process.env.REACT_APP_API}/containers/1-1`)
        .replyOnce(200, "Successfully deleted container.");

      await store.dispatch(handleDeleteContainer(1, 1)).then((res) => {
        expect(res).toEqual("Successfully deleted container.");
      });
    });
    it("handleDuplicateDrawing: duplicates drawings", async () => {
      const type = "WIZARD_DRAWINGS_ADDITIONAL";
      const responseArr = [
        { id: 2 },
        { id: 3 },
        { id: 4 },
        { id: 5 },
        { id: 6 },
      ];

      httpMock
        .onPost(`${process.env.REACT_APP_API}/drawings/duplicate/1`)
        .replyOnce(200, responseArr[0])
        .onPost(`${process.env.REACT_APP_API}/drawings/duplicate/1`)
        .replyOnce(200, responseArr)
        .onPost(`${process.env.REACT_APP_API}/drawings/duplicate/1`)
        .replyOnce(400, testError("drawings"));

      await store.dispatch(handleDuplicateDrawing(1, 1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(type),
          receiveSucceeded(type, responseArr[0]),
        ];
        expect(receivedActions).toEqual(expectedActions);
        store.clearActions();
      });

      await store.dispatch(handleDuplicateDrawing(1, 5)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(type),
          receiveSucceeded(type, responseArr),
        ];
        expect(receivedActions).toEqual(expectedActions);
        store.clearActions();
      });

      await store.dispatch(handleDuplicateDrawing(1, 1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(type),
          receiveFailed(type, testError("drawings").error),
        ];
        expect(receivedActions).toEqual(expectedActions);
        store.clearActions();
      });
    });
    it("handleCreateDrawingsViaPdfs: creates drawings with pdfs", async () => {
      const type = "WIZARD_DRAWINGS_ADDITIONAL";
      const responseArr = [
        { id: 2 },
        { id: 3 },
        { id: 4 },
        { id: 5 },
        { id: 6 },
      ];

      httpMock
        .onPost(`${process.env.REACT_APP_API}/drawings`)
        .replyOnce(200, responseArr)
        .onPost(`${process.env.REACT_APP_API}/drawings`)
        .replyOnce(400, testError("drawings"));

      await store.dispatch(handleCreateDrawingsViaPdfs(1, [])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(type),
          receiveSucceeded(type, responseArr),
        ];
        expect(receivedActions).toEqual(expectedActions);
        store.clearActions();
      });

      await store.dispatch(handleCreateDrawingsViaPdfs(1, [])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(type),
          receiveFailed(type, testError("drawings").error),
        ];
        expect(receivedActions).toEqual(expectedActions);
        store.clearActions();
      });
    });
    it("handleSubmitRevisedPdf: update drawing pdf", async () => {
      const response = [{ id: 1 }];

      httpMock
        .onPut(`${process.env.REACT_APP_API}/drawings/upload-revision/1`)
        .replyOnce(200, response)
        .onPut(`${process.env.REACT_APP_API}/drawings/upload-revision/1`)
        .replyOnce(400, testError("drawings"));

      await store.dispatch(handleSubmitRevisedPdf(1, {})).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [];
        expect(receivedActions).toEqual(expectedActions);
        store.clearActions();
      });

      await store.dispatch(handleSubmitRevisedPdf(1, {})).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [];
        expect(receivedActions).toEqual(expectedActions);
        store.clearActions();
      });
    });
  });
  // Drawings Column Defs
  describe("Drawings Column Defs", () => {
    const defaultHeaders = [
      "",
      "",
      "% Complete",
      "Area",
      "Budget Hours",
      "Container",
      "Drawing ID",
      "Drawing Name",
      "Due Date",
      "Est. Hours +/-",
      "Fab Completed On",
      "FP Budget",
      "Hours Left",
      "Job Name",
      "Laydown Location",
      "Manage",
      "Package Name",
      "Priority",
      "Status",
      "Total Hours",
      "Work Flow",
      "Work Items",
    ];
    const sortState = { sorting_column_name: "id", sorting_method: "asc" };
    const testStore = {
      getState: () => ({ profileData: testStore.profileData }),
      dispatch: (f) => f,
      profileData: {
        systemSettings: {
          date_display: "MM-DD-YYYY",
          timezone: "America/Chicago",
        },
        permissions: [279, 280, 281, 51],
      },
    };
    const containers = testContainers;
    const moreInfoClick = jest.fn();
    const toggleMoreInfo = jest.fn();
    const togglePDFViewer = jest.fn();
    const setDisplayedPdf = jest.fn();

    let populatedColumns;

    beforeEach(() => {
      populatedColumns = drawingsColumnDefs(
        testDrawings,
        containers,
        moreInfoClick,
        toggleMoreInfo,
        togglePDFViewer,
        sortState,
        [],
        testStore,
        setDisplayedPdf
      );
    });

    it("Drawings Headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);
      expect(columnHeaders).toEqual(defaultHeaders);
    });

    describe("DRAWING ID", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Drawing ID");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            id: 1,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(1);
      });
    });

    describe("DRAWING NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Drawing Name");
      });
      it("Value Parser", () => {
        const newValueParams = {
          oldValue: "oldValue",
          newValue: "newValue",
          data: {
            name: "test",
            id: 1,
          },
        };
        const oldValueParams = {
          oldValue: "oldValue",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };
        expect(column.valueParser(oldValueParams)).toEqual("oldValue");
        expect(column.valueParser(newValueParams)).toEqual("newValue");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            name: "test",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test");
      });
    });

    describe("JOB NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Job Name");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            job_title: "test job",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test job");
      });
      it("valueFormatter Job Name", () => {
        expect(column.valueFormatter()).toEqual({ wizardPermission: true });
      });
    });

    describe("PACKAGE NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Package Name");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            package_name: "test package",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test package");
      });
      it("valueFormatter Package Name", () => {
        expect(column.valueFormatter()).toEqual({ wizardPermission: true });
      });
    });

    describe("CONTAINERS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Container");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            container_name: "test container",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test container");
      });
      it("valueParser", () => {
        const newValueParams = {
          oldValue: "oldValue",
          newValue: "newValue",
          data: {
            name: "test",
            id: 1,
          },
        };
        const oldValueParams = {
          oldValue: "oldValue",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };
        expect(column.valueParser(oldValueParams)).toEqual(null);
        expect(column.valueParser(newValueParams)).toEqual("newValue");
      });
      it("cellEditorParams ", () => {
        const params = {
          data: {
            job_id: 2,
          },
        };
        expect(column.cellEditorParams(params)).toEqual({
          values: ["web cont"],
        });
      });
    });

    describe("AREA", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Area");
      });
      it("valueParser", () => {
        const newValueParams = {
          oldValue: "oldValue",
          newValue: "newValue",
          data: {
            name: "test",
            id: 1,
          },
        };
        const oldValueParams = {
          oldValue: "oldValue",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };
        expect(column.valueParser(oldValueParams)).toEqual(null);
        expect(column.valueParser(newValueParams)).toEqual("newValue");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            drawing_area: "test area",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test area");
      });
    });

    describe("WORK ITEMS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Work Items");
      });
      it("valueGetter", () => {
        const params = {
          data: {
            work_item_count: 1,
          },
        };
        const noCountParams = {
          data: {},
        };
        expect(column.valueGetter(params)).toEqual(1);
        expect(column.valueGetter(noCountParams)).toEqual(0);
      });
      it("Comparator", () => {
        expect([20, 2, 67, 38, 8].sort(column.comparator)).toEqual([
          2,
          8,
          20,
          38,
          67,
        ]);
      });
    });

    describe("STATUS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Status");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            drawing_status: "Pending Approval",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("Pending Approval");
      });
    });

    describe("PRIORITY", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Priority");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            priority: 1,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(1);
      });
    });

    describe("LAYDOWN LOCATION", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Laydown Location"
        );
      });
      it("valueParser", () => {
        const newValueParams = {
          oldValue: "oldValue",
          newValue: "newValue",
          data: {
            name: "test",
            id: 1,
          },
        };
        const oldValueParams = {
          oldValue: "oldValue",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };
        expect(column.valueParser(oldValueParams)).toEqual(null);
        expect(column.valueParser(newValueParams)).toEqual("newValue");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            laydown_location: "test laydown",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test laydown");
      });
    });

    describe("DUE DATE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Due Date");
      });
      it("valueParser", () => {
        const params = {
          oldValue: 1634533200,
          newValue: "10-20-2021",
        };
        const badParams = {
          oldValue: "",
          newValue: "",
        };
        expect(column.valueParser(params)).toEqual(1634706000);
        expect(column.valueParser(badParams)).toEqual("");
      });
      it("valueFormatter", () => {
        const params = {
          value: 1621018878,
        };
        const badParams = {
          value: "",
        };

        expect(column.valueFormatter(params)).toEqual("05-14-2021");
        expect(column.valueFormatter(badParams)).toEqual("-");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          value: "1634706000",
        };
        expect(column.getQuickFilterText(params)).toEqual("1634706000");
      });
    });

    describe("PERCENT COMPLETE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "% Complete");
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            percent_complete: 15,
          },
        };
        const emptyParams = {
          data: {},
        };
        expect(column.valueFormatter(params)).toEqual(15);
        expect(column.valueFormatter(emptyParams)).toEqual("0%");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            percent_complete: 15,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(15);
      });
    });

    describe("BUDGET HOURS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Budget Hours");
      });
      it("valueFormatter", () => {
        const params = {
          value: 2.45999,
        };
        const emptyParams = {};
        expect(column.valueFormatter(params)).toEqual("2.46");
        expect(column.valueFormatter(emptyParams)).toEqual("0.00");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            budget_hours: 15,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(15);
      });
    });
    describe("FP BUDGET", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "FP Budget");
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            fp_budget: 2.45999,
          },
        };
        const emptyParams = {
          data: {},
        };
        expect(column.valueFormatter(params)).toEqual("2.5");
        expect(column.valueFormatter(emptyParams)).toEqual("0.0");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            fp_budget: 15,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(15);
      });
    });

    describe("HOURS LEFT", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Hours Left");
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            est_time_left: 2.45999,
          },
        };
        const emptyParams = {
          data: {},
        };
        expect(column.valueFormatter(params)).toEqual("2.5");
        expect(column.valueFormatter(emptyParams)).toEqual("0.0");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            est_time_left: 15,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(15);
      });
    });

    describe("EST. HOURS +/-", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Est. Hours +/-"
        );
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            hours_ahead_behind: 2.45999,
          },
        };
        const emptyParams = {
          data: {},
        };
        expect(column.valueFormatter(params)).toEqual("2.5");
        expect(column.valueFormatter(emptyParams)).toEqual(0);
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            hours_ahead_behind: 15,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(15);
      });
    });

    describe("TOTAL HOURS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Total Hours");
      });
      const params = {
        data: {
          cost_time: 280,
        },
      };
      it("valueFormatter", () => {
        expect(column.valueFormatter(params)).toEqual("280.0");
      });
      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(280);
      });
    });

    describe("WORK FLOW", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Work Flow");
      });
      const params = {
        data: {
          work_flow_name: "Britton Test",
        },
      };
      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("Britton Test");
      });
    });

    describe("MANAGE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Manage");
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            moreInfoClick,
            toggleMoreInfo,
          },
        };
        expect(column.valueFormatter(params)).toEqual({
          moreInfoClick,
          toggleMoreInfo,
        });
      });
    });
  });
});
