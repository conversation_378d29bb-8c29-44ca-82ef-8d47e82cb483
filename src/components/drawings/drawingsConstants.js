// NPM PACKAGE IMPORTS
import moment from "moment";
import "moment-timezone";

// REDUX IMPORTS
import store from "../../redux/store";
import { notify } from "../reusable/alertPopup/alertPopupActions";

// HELPER FUNCTION IMPORTS
import {
  generateTime,
  formatDate,
  naturalSort,
  decimalRegex,
} from "../../_utils";

// EXPORTS
export const drawingsColumnDefs = (
  savedColumnState,
  containers = [],
  moreInfoClick,
  toggleMoreInfo,
  togglePDFViewer,
  sortState,
  drawingCustomColumns = [],
  testStore = null,
  setDisplayedPdf
) => {
  const { systemSettings, permissions } = (
    testStore || store
  ).getState().profileData;
  let dateFormatting = systemSettings && systemSettings.date_display;

  const defaultDefs = [
    {
      headerName: "Drawing ID",
      field: "id",
      getQuickFilterText: (params) => params.data?.id,
      minWidth: 100,
      width: 120,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "id",
      sortable: true,
      sort:
        sortState.sorting_column_name === "id"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Drawing Name",
      field: "name",
      cellRendererParams: (params) => {
        return {
          moreInfoClick,
          togglePDFViewer,
          setDisplayedPdf,
        };
      },
      valueParser: (params) => {
        if (
          (params.newValue ? params.newValue.trim() : "") === "" ||
          params.newValue === undefined
        ) {
          (testStore || store).dispatch(
            notify({
              id: Date.now(),
              type: "ERROR",
              message: "Drawing name cannot be empty",
            })
          );
          return params.oldValue;
        }

        if (/[^a-z0-9\.\-\_ ]/gi.test(params.newValue)) {
          (testStore || store).dispatch(
            notify({
              id: Date.now(),
              type: "ERROR",
              message:
                "Drawing name can only contain the following characters (a-z A-Z 0-9 . - _)",
            })
          );
          return params.oldValue;
        }

        return params.newValue;
      },
      editable: true,
      getQuickFilterText: (params) => params.data?.name,
      minWidth: 140,
      width: 160,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      cellRenderer: "drawingNameCellRenderer",
      colId: "name",
      autoHeight: true,
      sortable: true,
      sort:
        sortState.sorting_column_name === "name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Job Name",
      field: "job_title",
      cellRenderer: "jobNameCellRenderer",
      valueFormatter: (params) => ({
        wizardPermission:
          permissions &&
          (permissions.includes(279) ||
            permissions.includes(280) ||
            permissions.includes(281)),
      }),
      getQuickFilterText: (params) => params.data?.job_title,
      minWidth: 120,
      width: 140,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "job_title",
      autoHeight: true,
      sortable: true,
      sort:
        sortState.sorting_column_name === "job_title"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Package Name",
      field: "package_name",
      getQuickFilterText: (params) => params.data?.package_name,
      minWidth: 120,
      width: 140,
      resizable: true,
      permissions: [51],
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_name",
      autoHeight: true,
      sortable: true,
      sort:
        sortState.sorting_column_name === "package_name"
          ? sortState.sorting_method
          : null,
      valueFormatter: (params) => ({
        wizardPermission:
          permissions &&
          (permissions.includes(279) ||
            permissions.includes(280) ||
            permissions.includes(281)),
      }),
      cellRenderer: "packageNameCellRenderer",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Container",
      field: "container_name",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      cellEditorParams: (params) => {
        return {
          values: containers
            .filter((c) => c.job_id === params.data?.job_id)
            .map((c) => c.name),
        };
      },
      minWidth: 120,
      width: 140,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      cellEditor: "dropdownEditorRenderer",
      editable: true,
      getQuickFilterText: (params) => params.data?.container_name,
      colId: "container_name",
      autoHeight: true,
      sortable: true,
      sort:
        sortState.sorting_column_name === "container_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Area",
      field: "drawing_area",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      editable: true,
      getQuickFilterText: (params) => params.data?.drawing_area,
      minWidth: 100,
      width: 120,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "drawing_area",
      autoHeight: true,
      sortable: true,
      sort:
        sortState.sorting_column_name === "drawing_area"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Work Items",
      valueGetter: (params) => params.data?.work_item_count,
      minWidth: 100,
      width: 120,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "work_items",
      sortable: true,
      sort:
        sortState.sorting_column_name === "work_items"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Status",
      field: "drawing_status",
      minWidth: 120,
      width: 140,
      getQuickFilterText: (params) => params.data?.drawing_status,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "drawing_status",
      sortable: true,
      sort:
        sortState.sorting_column_name === "drawing_status"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Priority",
      field: "priority",
      minWidth: 100,
      width: 120,
      getQuickFilterText: (params) => params.data?.priority,
      resizable: true,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "priority",
      sortable: true,
      sort:
        sortState.sorting_column_name === "priority"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Laydown Location",
      field: "laydown_location",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      editable: true,
      minWidth: 120,
      width: 140,
      getQuickFilterText: (params) => params.data?.laydown_location,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "laydown_location",
      autoHeight: true,
      sortable: true,
      sort:
        sortState.sorting_column_name === "laydown_location"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Due Date",
      field: "due_date",
      valueFormatter: (params) => {
        return params.value
          ? typeof params.value === "number"
            ? formatDate(params.value)
            : params.value
          : "-";
      },
      editable: true,
      cellEditor: "dueDateEditorRenderer",
      valueParser: (params) => {
        if (
          params.newValue === "" ||
          params.newValue === undefined ||
          formatDate(params.value) === params.newValue // params.value is same as params.oldValue
        )
          return params.oldValue;

        const defaultRegex = new RegExp("\\d{2}-\\d{2}-\\d{4}");
        const dateFormattingRegex = dateFormatting
          ? /-/.test(dateFormatting)
            ? new RegExp(
                dateFormatting
                  .split("-")
                  .map((part) => `\\d{${part.length}}`)
                  .join("\\-")
              )
            : /\//.test(dateFormatting)
            ? new RegExp(
                dateFormatting
                  .split("/")
                  .map((part) => `\\d{${part.length}}`)
                  .join("\\/")
              )
            : defaultRegex
          : defaultRegex;

        if (!dateFormattingRegex.test(params.newValue)) {
          return params.oldValue;
        } else {
          return formatDate(params.newValue, true); // returns unixtime...
        }
      },
      getQuickFilterText: (params) => params.colDef.valueFormatter(params),
      filter: "agDateColumnFilter",
      minWidth: 100,
      width: 120,
      filterParams: {
        buttons: ["reset"],
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = cellValue
            ? typeof cellValue === "number"
              ? new Date(formatDate(cellValue))
              : new Date(cellValue)
            : "-";

          return cellDate < filterLocalDateAtMidnight
            ? -1
            : cellDate > filterLocalDateAtMidnight
            ? 1
            : 0;
        },
      },
      menuTabs: ["filterMenuTab"],
      colId: "due_date",
      sortable: true,
      sort:
        sortState.sorting_column_name === "due_date"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "% Complete",
      field: "percent_complete",
      minWidth: 100,
      width: 120,
      // @ToDo: Fix rounding...
      valueGetter: (params) => {
        if (!params.data) return;
        const { percent_complete } = params.data;
        if (!percent_complete) return 0;
        return Math.floor(percent_complete);
      },
      valueFormatter: (params) => {
        if (!params.data) return;
        const { percent_complete } = params.data;
        if (!percent_complete) return "0%";
        return Math.floor(percent_complete);
      },
      getQuickFilterText: (params) => params.data?.percent_complete,
      cellRenderer: "percentCompleteCellRenderer",
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "percent_complete",
      sortable: true,
      sort:
        sortState.sorting_column_name === "percent_complete"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Budget Hours",
      field: "budget_hours",
      valueFormatter: (params) => {
        if (!params.data) return;
        const value = params.value
          ? `${parseFloat(params.value).toFixed(2)}`
          : "0.00";
        return value;
      },
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue;

        // return old value if new value is not a valid number (decimal included)
        if (!/^\d{0,}\.?\d{1,}$/.test(params.newValue)) return params.oldValue;

        return params.newValue;
      },
      getQuickFilterText: (params) => params.data?.budget_hours,
      editable: true,
      minWidth: 80,
      width: 100,
      permissions: [66],
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "budget_hours",
      sortable: true,
      sort:
        sortState.sorting_column_name === "budget_hours"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "FP Budget",
      field: "fp_budget",
      valueFormatter: (params) => {
        if (!params.data) return;
        const { fp_budget } = params.data;
        const value = fp_budget ? `${fp_budget.toFixed(1)}` : "0.0";
        return value;
      },
      getQuickFilterText: (params) => params.data?.fp_budget,
      minWidth: 80,
      width: 100,
      permissions: [66],
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "fp_budget",
      sortable: true,
      sort:
        sortState.sorting_column_name === "fp_budget"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Fab Completed On",
      field: "fab_completed_on",
      valueFormatter: (params) => {
        return params.value
          ? typeof params.value === "number"
            ? generateTime(params.value * 1000, false, false, null, testStore)
            : params.value
          : "";
      },
      editable: false,
      getQuickFilterText: (params) => params.colDef.valueFormatter(params),
      filter: "agDateColumnFilter",
      minWidth: 100,
      width: 120,
      filterParams: {
        buttons: ["reset"],
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = cellValue
            ? typeof cellValue === "number"
              ? new Date(
                  generateTime(cellValue * 1000, true, true, "-", testStore)
                )
              : new Date(cellValue)
            : "-";

          return cellDate < filterLocalDateAtMidnight
            ? -1
            : cellDate > filterLocalDateAtMidnight
            ? 1
            : 0;
        },
      },
      menuTabs: ["filterMenuTab"],
      sortable: true,
      sort:
        sortState.sorting_column_name === "fab_completed_on"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Hours Left",
      field: "est_time_left",
      valueFormatter: (params) => {
        if (!params.data) return;

        const { est_time_left } = params.data;
        const value = est_time_left ? `${est_time_left.toFixed(1)}` : "0.0";
        return value;
      },
      getQuickFilterText: (params) => params.data?.est_time_left,
      minWidth: 80,
      width: 100,
      permissions: [66],
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "est_time_left",
      sortable: true,
      sort:
        sortState.sorting_column_name === "est_time_left"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Est. Hours +/-",
      field: "hours_ahead_behind",
      valueFormatter: (params) => {
        if (!params.data) return;

        const { hours_ahead_behind } = params.data;
        return hours_ahead_behind ? hours_ahead_behind.toFixed(1) : 0;
      },
      getQuickFilterText: (params) => params.data?.hours_ahead_behind,
      cellRenderer: "estHoursCellRenderer",
      permissions: [71],
      minWidth: 120,
      width: 140,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "hours_ahead_behind",
      sortable: true,
      sort:
        sortState.sorting_column_name === "hours_ahead_behind"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) => (valueA || 0) - (valueB || 0),
    },
    {
      headerName: "Total Hours",
      field: "cost_time",
      valueFormatter: (params) => {
        if (!params.data) return;
        const { cost_time } = params.data;
        return cost_time ? cost_time.toFixed(1) : 0;
      },
      getQuickFilterText: (params) => {
        if (!params.data) return;
        return params.data?.cost_time;
      },
      minWidth: 80,
      width: 100,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "cost_time",
      sortable: true,
      sort:
        sortState.sorting_column_name === "cost_time"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Work Flow",
      field: "work_flow_name",
      getQuickFilterText: (params) => {
        if (!params.data) return;
        return params.data?.work_flow_name;
      },
      minWidth: 80,
      width: 100,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "work_flow_name",
      sortable: true,
      sort:
        sortState.sorting_column_name === "work_flow_name"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Created By",
      field: "created_by_name",
      getQuickFilterText: (params) => params.data?.created_by_name,
      resizable: true,
      minWidth: 120,
      width: 140,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "created_by",
      autoHeight: true,
      sortable: true,
      sort:
        sortState.sorting_column_name === "created_by_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Manage",
      sortable: false,
      minWidth: 60,
      width: 60,
      valueFormatter: (params) => {
        return {
          moreInfoClick,
          toggleMoreInfo,
        };
      },
      pinned: "right",
      cellRenderer: "moreInfoCellRenderer",
      lockVisible: true,
      suppressMovable: true,
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      colId: "manage",
    },
  ];

  if (savedColumnState && savedColumnState.length) {
    let result = [];

    for (let i = 0; i < defaultDefs.length; i++) {
      let savedDef = savedColumnState.find(
        (c) => c.header_name === defaultDefs[i].headerName
      );

      if (savedDef) {
        result.push({
          ...defaultDefs[i],
          pinned: savedDef.pinned,
          hide: savedDef.visible ? false : true,
          position: savedDef.position,
        });
      } else result.push(defaultDefs[i]);
    }

    for (let column of drawingCustomColumns) {
      const columnDef = {
        autoHeight: true,
        suppressSizeToFit: false,
        headerName: column.display_name,
        field: column.normal_name,
        valueParser: (params) => {
          if (params.newValue === "" || params.newValue === undefined)
            return params.oldValue === "" || params.oldValue === undefined
              ? params.oldValue
              : null;

          if (column.data_type === "decimal") {
            if (!decimalRegex.test(params.newValue)) {
              (testStore || store).dispatch(
                notify({
                  id: Date.now(),
                  type: "ERROR",
                  message: "Value must be a number",
                })
              );
              return params.oldValue;
            }
          }
          return params.newValue;
        },
        cellClass: "wrap-text",
        minWidth: 125,
        editable: true,
        sortable: false,
        menuTabs: [],
        colId: column.normal_name,
      };
      if (column.data_type === "date") {
        columnDef.filterParams = {
          comparator: (filterLocalDateAtMidnight, cellValue) => {
            const cellDate = cellValue
              ? typeof cellValue === "number"
                ? new Date(generateTime(cellValue * 1000, false, true, "-"))
                : new Date(cellValue)
              : "-";

            return cellDate < filterLocalDateAtMidnight
              ? -1
              : cellDate > filterLocalDateAtMidnight
              ? 1
              : 0;
          },
        };
        columnDef.getQuickFilterText = (params) => {
          let value;
          const date = params.data?.[column.normal_name]
            ? new Date(params.data?.[column.normal_name])
            : null;

          const timezone = Intl.DateTimeFormat().resolvedOptions().timezone;
          if (!date) value = "N/A";
          else value = moment.tz(date, timezone).format(dateFormatting);
          return value;
        };
        columnDef.valueFormatter = (params) => {
          return params.value
            ? typeof params.value === "number"
              ? generateTime(params.value * 1000, false, true, "-", testStore)
              : params.value
            : "";
        };

        if (column.editable) {
          columnDef.cellEditor = "dueDateEditorRenderer";
          columnDef.valueParser = (params) => {
            if (params.newValue === "" || params.newValue === undefined)
              return params.oldValue;
            if (params.newValue === 0) return 0;

            const defaultRegex = new RegExp("\\d{2}-\\d{2}-\\d{4}");
            const dateFormattingRegex = dateFormatting
              ? /-/.test(dateFormatting)
                ? new RegExp(
                    dateFormatting
                      .split("-")
                      .map((part) => `\\d{${part.length}}`)
                      .join("\\-")
                  )
                : /\//.test(dateFormatting)
                ? new RegExp(
                    dateFormatting
                      .split("/")
                      .map((part) => `\\d{${part.length}}`)
                      .join("\\/")
                  )
                : defaultRegex
              : defaultRegex;
            if (!dateFormattingRegex.test(params.newValue)) {
              return params.oldValue;
            } else return params.newValue; //new Date(params.newValue);
          };
        }
      }
      result.push(columnDef);
    }

    result = result
      .sort((a, b) => {
        if (a.position === b.position) {
          if (a.headerName.toLowerCase() > b.headerName.toLowerCase()) return 1;
          else return -1;
        } else return a.position - b.position;
      })
      .map((col) => {
        if (col.position !== undefined) delete col.position;
        return col;
      });

    result.unshift(
      {
        headerName: "",
        maxWidth: 50,
        width: 50,
        minWidth: 50,
        suppressMenu: true,
        resizable: false,
        sortable: true,
        comparator: (valueA, valueB, nodeA, nodeB) => {
          return nodeA.data.on_hold - nodeB.data.on_hold;
        },
        suppressSizeToFit: true,
        cellClassRules: {
          "cell-yellow": (params) => params.data?.on_hold === 1,
          "cell-green": (params) => params.data?.on_hold === 0,
        },
        suppressColumnsToolPanel: true,
        colId: "on_hold",
        headerClass: "status-indicator-button",
        sort:
          sortState.sorting_column_name === "on_hold"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "",
        minWidth: 50,
        width: 50,
        headerCheckboxSelection: drawingCustomColumns?.length ? false : true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        lockVisible: true,
        suppressMovable: true,
        suppressMenu: true,
        suppressColumnsToolPanel: true,
        colId: "checkbox",
      }
    );

    return result;
  } else {
    defaultDefs.unshift(
      {
        headerName: "",
        maxWidth: 50,
        minWidth: 50,
        width: 50,
        sortable: true,
        comparator: (valueA, valueB, nodeA, nodeB) => {
          return nodeA.data.on_hold - nodeB.data.on_hold;
        },
        suppressMenu: true,
        resizable: false,
        suppressSizeToFit: true,
        cellClassRules: {
          "cell-yellow": (params) => params.data?.on_hold === 1,
          "cell-green": (params) => params.data?.on_hold === 0,
        },
        suppressColumnsToolPanel: true,
        colId: "on_hold",
        headerClass: "status-indicator-button",
        sort:
          sortState.sorting_column_name === "on_hold"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "",
        minWidth: 50,
        width: 50,
        headerCheckboxSelection: drawingCustomColumns?.length ? false : true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        lockVisible: true,
        suppressMovable: true,
        suppressMenu: true,
        suppressColumnsToolPanel: true,
        colId: "checkbox",
      }
    );

    return defaultDefs;
  }
};
