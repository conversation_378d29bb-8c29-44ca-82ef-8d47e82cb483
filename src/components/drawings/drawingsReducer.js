import { updateDrawingsHasMaj } from "../../_utils";

const initialState = {
  isLoading: false,
  error: null,
  drawings: [],
  allDrawings: [],
  containers: null,
  heatNumbers: null,
  statusTracker: [],
  savedColumnState: null,
  drawingsPendingApproval: [],
  drawingsPendingApprovalFlag: false,
  storedListOfDrawings: [],
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_DRAWINGS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_DRAWINGS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, drawings: payload };
    case "RECEIVE_DRAWINGS_FAILED":
      return { ...state, isLoading: false, error: payload, drawings: [] };
    case "RECEIVE_ALL_DRAWINGS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_ALL_DRAWINGS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, allDrawings: payload };
    case "RECEIVE_ALL_DRAWINGS_FAILED":
      return { ...state, isLoading: false, error: payload, allDrawings: [] };
    case "UPDATE_DRAWINGS_STARTED":
      return { ...state, isLoading: true };
    case "UPDATE_DRAWINGS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "UPDATE_DRAWINGS_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "UPDATE_PRIORITIES_STARTED":
      return { ...state, isLoading: true, error: null };
    case "UPDATE_PRIORITIES_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "UPDATE_PRIORITIES_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_CONTAINERS_STARTED":
      return { ...state, error: null };
    case "RECEIVE_CONTAINERS_SUCCEEDED":
      return { ...state, containers: payload, error: null };
    case "RECEIVE_CONTAINERS_FAILED":
      return { ...state, containers: [], error: payload };
    case "UPDATE_CONTAINERS_STARTED":
      return { ...state, isLoading: true };
    case "UPDATE_CONTAINERS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "UPDATE_CONTAINERS_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "CREATE_CONTAINER_STARTED":
      return { ...state, isLoading: true, error: null };
    case "CREATE_CONTAINER_SUCCEEDED":
      return { ...state, isLoading: false, error: null, containers: payload };
    case "CREATE_CONTAINER_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "UPDATE_DRAWING_CONTAINER_STARTED":
      return { ...state, isLoading: true, error: null };
    case "UPDATE_DRAWING_CONTAINER_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "UPDATE_DRAWING_CONTAINER_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_HEAT_NUMBERS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_HEAT_NUMBERS_SUCCEEDED":
      return { ...state, isLoading: false, heatNumbers: payload, error: null };
    case "RECEIVE_HEAT_NUMBERS_FAILED":
      return { ...state, isLoading: false, heatNumbers: [], error: payload };
    case "RECEIVE_DRAWINGS_STATUS_TRACKER_SUCCEEDED":
      return {
        ...state,
        statusTracker: payload,
      };
    case "RECEIVE_DRAWINGS_STATUS_TRACKER_FAILED":
      return { ...state, statusTracker: [] };
    case "RECEIVE_DRAWINGS_COLUMN_STATE_STARTED":
      return { ...state, savedColumnState: null };
    case "RECEIVE_DRAWINGS_COLUMN_STATE_SUCCEEDED":
      return { ...state, savedColumnState: payload };
    case "RECEIVE_DRAWINGS_COLUMN_STATE_FAILED":
      return { ...state, savedColumnState: [] };
    case "RECEIVE_DRAWINGS_PENDING_APPROVAL_SUCCEEDED":
      return {
        ...state,
        drawingsPendingApproval: payload,
      };
    case "RECEIVE_DRAWINGS_PENDING_APPROVAL_FAILED":
      return { ...state, drawingsPendingApproval: [] };
    case "RECEIVE_DRAWINGS_PENDING_APPROVAL_FLAG_SUCCEEDED":
      return {
        ...state,
        drawingsPendingApprovalFlag: payload,
      };
    case "RECEIVE_DRAWINGS_PENDING_APPROVAL_FLAG_FAILED":
      return { ...state, drawingsPendingApprovalFlag: false };
    case "RECEIVE_DRAWINGS_CLEAR_COLUMN_STATE_SUCCEEDED":
      return { ...state, savedColumnState: null };
    case "REPLACE_SINGLE_DRAWING_SUCCEEDED":
      let tempDrawings = [...state.drawings];
      for (let i = 0; i < tempDrawings.length; i++) {
        if (tempDrawings[i].id === payload.id) {
          tempDrawings[i] = { ...tempDrawings[i], ...payload };
          break;
        }
      }
      return { ...state, drawings: tempDrawings };
    case "RECEIVE_DRAWING_MAJS_UPLOADED_SUCCEEDED":
      return {
        ...state,
        drawings: updateDrawingsHasMaj(state.drawings, payload),
      };
    case "RECEIVE_SET_DRAWINGS_LIST_SUCCEEDED":
      return { ...state, storedListOfDrawings: payload };
    case "RECEIVE_RESET_DRAWINGS_LIST_SUCCEEDED":
      return { ...state, drawings: state.storedListOfDrawings };
    default:
      return state;
  }
}
