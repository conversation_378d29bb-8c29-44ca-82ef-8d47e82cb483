// NPM PACKAGE IMPORTS
import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import Button from "msuite_storybook/dist/button/Button";
import Modal from "msuite_storybook/dist/modal/Modal";
import Select from "msuite_storybook/dist/select/Select";
import CSVImport from "msuite_storybook/dist/csvImport/CSVImport";
import { RiUploadCloudLine } from "react-icons/ri";
import Input from "msuite_storybook/dist/input/Input";

// REDUX IMPORTS
import { handleSetPageTitle } from "../../redux/generalActions";
import { handleClearJobsState, handleFetchJobById } from "../jobs/jobsActions";
import { handleFetchMaterialTypes } from "../items/itemsActions";
import {
  handleFetchDrawings,
  handleFetchDrawingsInPackage,
  handleFetchPackageCostCodes,
  handleFetchF<PERSON>,
  handleFetchCostCodes,
  handleUpdateJobCostCodes,
  handleUpdatePackageCostCodes,
  handleCreateCostCodes,
  handleFetchJobSpecificCostCodes,
} from "./wizardActions";
import {
  handleDeleteDrawings,
  handleUpdateDrawings,
  handleCreateDrawingsViaPdfs,
} from "../drawings/drawingsActions";
import { handleCreateItems } from "../items/itemsActions";
import {
  handleFetchPackages,
  handleCreatePackages,
} from "../packages/packagesActions";

// COMPONENT IMPORTS
import Stepper from "./stepper";
import Job from "./job";
import CostCodes from "./costCodes";
import Packages from "./packages";
import Drawings from "./drawings";
import Items from "./items";
import ConfirmationModal from "../reusable/confirmationModal/ConfirmationModal";
import { notify } from "../reusable/alertPopup/alertPopupActions";
import CostCodeUploadModal from "./costCodes/CostCodeUploadModal";
import CostCodeTypesModal from "./costCodes/costCodeTypesModal/CostCodeTypesModal";
import ManageMAJModal from "../reusable/manageMAJModal/ManageMAJModal";
import DownloadMAJsButton from "../reusable/downloadMAJsButton/DownloadMAJsButton";

// HELPER FUNCTION IMPORTS
import { buildCSV, naturalSort } from "../../_utils";
import { trackMixPanelEvent } from "../../utils/_mixPanelUtils";

// CONSTANTS IMPORTS
import {
  headers as packagesHeaders,
  images as packagesImages,
} from "./packages/packagesConstants";

// STYLE IMPORTS
import "./stylesWizard.scss";
import "react-datepicker/dist/react-datepicker.css";

const Wizard = () => {
  const [currentStep, setCurrentStep] = useState(null);
  const [stepTitle, setStepTitle] = useState("");
  const [selectedJob, setSelectedJob] = useState(null);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [selectedRows, setSelectedRows] = useState([]);
  const [showConfirmationModal, toggleConfirmationModal] = useState(false);
  const [drawingsGridOptionsApi, setDrawingsGridOptionsApi] = useState(null);
  const [showUploadDrawings, toggleUploadDrawings] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState(null);
  const [createWorkItems, toggleCreateWorkItems] = useState(false);
  const [defaultMaterial, setDefaultMaterial] = useState(null);
  const [showCSVModal, toggleCSVModal] = useState(false);
  const [csvError, setCSVError] = useState(null);
  const [csvData, setCsvData] = useState(null);
  const [receivedHeaders, setReceivedHeaders] = useState(null);
  const [showCancelConfirmationModal, toggleCancelConfirmationModal] = useState(
    false
  );
  const [showCostCodeUploadModal, toggleCostCodeUploadModal] = useState(false);
  const [showCostCodeTypesModal, toggleCostCodeTypesModal] = useState(false);
  const [invalidPdfNames, setInvalidPdfNames] = useState([]);
  const [showManageMAJModal, toggleManageMAJModal] = useState(false);

  const dispatch = useDispatch();
  const { specificJob } = useSelector((state) => state.jobsData);
  const { materialTypes } = useSelector((state) => state.itemsData);
  const {
    drawings,
    drawingsInPackage,
    flows,
    costCodes,
    jobCostCodes,
  } = useSelector((state) => state.wizardData);
  const { packages } = useSelector((state) => state.packagesData);
  const { permissions, features } = useSelector((state) => state.profileData);

  useEffect(() => {
    const jobIdQuery = window.location.search.match(/JobID=\d+/);
    let jobId = null;

    if (jobIdQuery) {
      jobId = parseInt(jobIdQuery[0].split("=")[1]);

      setSelectedJob(jobId);
      dispatch(handleFetchMaterialTypes());
      dispatch(handleFetchPackages([jobId], true));
    } else {
      dispatch(handleSetPageTitle("New Job"));
    }

    dispatch(handleFetchFlows);
    if (jobId) dispatch(handleFetchJobSpecificCostCodes(jobId));
    dispatch(handleFetchCostCodes(null));

    if (/new-job/.test(window.location.pathname)) {
      setStepTitle("Job Info");
      setCurrentStep(1);
    } else if (/new-costcodes/.test(window.location.pathname)) {
      setStepTitle("Cost Codes");
      setCurrentStep(2);
    } else if (/new-packages/.test(window.location.pathname)) {
      setStepTitle("Packages");
      setCurrentStep(3);
    } else if (/new-drawings/.test(window.location.pathname)) {
      setStepTitle("Drawings");
      setCurrentStep(4);
      if (jobId)
        dispatch(handleFetchDrawings(jobId)).then((res) => {
          if (!res.error) {
            dispatch(
              handleFetchPackageCostCodes(
                Array.from(new Set(res.map((d) => d.package_id)))
              )
            );
          }
        });
    } else if (/new-items/.test(window.location.pathname)) {
      setStepTitle("Items");
      setCurrentStep(5);
    }
  }, []);

  useEffect(() => {
    if (selectedPackage && currentStep === 5)
      dispatch(handleFetchDrawingsInPackage(selectedPackage));
  }, [selectedPackage]);

  useEffect(() => {
    let reactArea;

    switch (currentStep) {
      case 1:
        reactArea = "job";
        break;
      case 2:
        reactArea = "costcodes";
        break;
      case 3:
        reactArea = "packages";
        break;
      case 4:
        reactArea = "drawings";
        break;
      case 5:
        reactArea = "items";
        break;
      default:
        break;
    }

    if (reactArea && !new RegExp(reactArea).test(window.location.pathname)) {
      sendToReact(reactArea);
      return;
    }
  }, [currentStep]);

  useEffect(() => {
    if (selectedJob) {
      dispatch(handleFetchJobById(selectedJob));
    }
  }, [selectedJob]);

  useEffect(() => {
    if (specificJob) {
      dispatch(
        handleSetPageTitle(
          `Job - ${
            specificJob.job_number ? specificJob.job_number + " | " : ""
          }${specificJob.job_name}`
        )
      );
    }
  }, [specificJob]);

  const sendToReact = useCallback(
    (area) => {
      window.location.assign(
        `${process.env.REACT_APP_FABPRO}/new-${area}/?JobID=${selectedJob}`
      );
    },
    [selectedJob]
  );

  const getResults = ({ data }) => {
    setCsvData(data);
    const receivedHeaders = data[0];
    const receivedData = data.slice(1);
    setReceivedHeaders(receivedHeaders.filter((h) => h));

    let results = [];
    for (let i = 0; i < receivedData.length; i++) {
      let dataObj = {};
      for (let j = 0; j < receivedHeaders.length; j++) {
        if (receivedHeaders[j]) {
          dataObj[packagesHeaders[receivedHeaders[j]]] = receivedData[i][j];
        }
      }
      results.push(dataObj);
    }
    return results;
  };

  const materialTypesCSV = useMemo(() => {
    if (materialTypes && materialTypes.length)
      return buildCSV(
        ["Material Name"],
        materialTypes.map((m) => [m.name])
      );
  }, [materialTypes]);

  const drawingsCSV = useMemo(() => {
    if (drawingsInPackage) {
      return buildCSV(
        ["Name"],
        drawingsInPackage.map((d) => [d.name])
      );
    }
  }, [drawingsInPackage]);

  const flowsCSV = useMemo(() => {
    if (flows && flows.length)
      return buildCSV(
        ["id", "Flow Name", "Default Flow"],
        flows.map((f) => [f.id, f.name, f.default ? "Yes" : ""])
      );
  }, [flows]);

  const confirmDeletion = () => {
    dispatch(
      handleDeleteDrawings(selectedRows.map((r) => r.id).join(","))
    ).then((res) => {
      if (!res.error) {
        dispatch(handleFetchDrawings(selectedJob)).then((res) => {
          if (!res.error) {
            dispatch(
              handleFetchPackageCostCodes(
                Array.from(new Set(res.map((d) => d.package_id)))
              )
            );
          }
        });
        dispatch(handleFetchPackages([selectedJob], true));
        drawingsGridOptionsApi.deselectAll();
        setSelectedRows([]);
      }

      toggleConfirmationModal(false);
    });
  };

  const sendBackForApproval = () => {
    dispatch(
      handleUpdateDrawings(
        selectedRows.map((r) => r.id).join(","),
        { timer_override: 1 },
        "sendBack"
      )
    ).then((res) => {
      dispatch(handleFetchDrawings(selectedJob));
      setSelectedRows([]);

      if (res.success > 0) {
        dispatch(
          notify({
            id: Date.now(),
            type: "SUCCESS",
            message: `${res.success} drawings were successfully sent back.`,
          })
        );
      }

      if (res.warning > 0) {
        dispatch(
          notify({
            id: Date.now(),
            type: "WARN",
            message: `${res.warning} drawings were already at Pending Approval and did not move.`,
          })
        );
      }

      if (res.error > 0) {
        dispatch(
          notify({
            id: Date.now(),
            type: "ERROR",
            message: `${res.error} drawings encountered a problem and could not be moved.`,
          })
        );
      }
    });
  };

  const uploadDrawings = async () => {
    const newDrawings = await dispatch(
      handleCreateDrawingsViaPdfs(selectedPackage, uploadedFiles)
    );
    trackMixPanelEvent(
      "Upload Drawings",
      newDrawings.length,
      "Wizard Drawings Tab",
      Date.now(),
      "Uploading Drawings to Package"
    );
    if (newDrawings && Array.isArray(newDrawings)) {
      if (createWorkItems) {
        await dispatch(
          handleCreateItems(
            selectedJob,
            selectedPackage,
            newDrawings.map((d) => ({
              drawing_id: d.id,
              material_type_id: defaultMaterial,
              fab: 1,
            }))
          )
        );
      }

      dispatch(handleFetchPackages([selectedJob], true));
      if (!drawings || !drawings.length) {
        dispatch(
          handleFetchPackageCostCodes(
            Array.from(new Set(newDrawings.map((d) => d.package_id)))
          )
        );
      }

      setUploadedFiles(null);
      toggleCreateWorkItems(false);
      setDefaultMaterial(null);
      setSelectedPackage(null);
      toggleUploadDrawings(false);
    }
  };

  const handleSubmit = () => {
    let costCodesToCreate = [],
      costCodesToAdd = [];
    const idxName = csvData[0].indexOf("Package Name");
    const packagesToCreate = csvData.slice(1).map((r) => {
      let result = {};

      let idxMonth, idxDay, idxYear;

      csvData[0].forEach((h, idx) => {
        if (
          ![
            "Due Date Month",
            "Due Date Day",
            "Due Date Year",
            "Cost Code",
          ].includes(h)
        )
          result[packagesHeaders[h]] = r[idx];
        else if (h === "Cost Code" && r[idx] && r[idx].toString().trim()) {
          const existingCostCode = costCodes.find(
            (cc) =>
              cc.name.toLowerCase() === r[idx].toString().trim().toLowerCase()
          );
          if (existingCostCode)
            costCodesToAdd.push({
              packageName: r[idxName],
              costCodeId: existingCostCode.id,
            });
          else
            costCodesToCreate.push({
              packageName: r[idxName],
              costCodeName: r[idx].toString().trim(),
            });
        }

        switch (h) {
          case "Due Date Month":
            idxMonth = idx;
            break;
          case "Due Date Day":
            idxDay = idx;
            break;
          case "Due Date Year":
            idxYear = idx;
            break;
          default:
            break;
        }
      });

      result.due_date = `${r[idxYear]}-${r[idxMonth]}-${r[idxDay]}`;

      return result;
    });

    dispatch(handleCreatePackages(selectedJob, packagesToCreate)).then(
      async (res) => {
        if (!res.error && res[2].length) {
          // REDUCE TO PACKAGES THAT WERE CREATED
          if (costCodesToAdd.length) {
            const reducedArr = costCodesToAdd.reduce((acc, curr) => {
              const newPackage = res[2].find(
                (p) => p.package_name === curr.packageName
              );
              if (newPackage) {
                acc.push({
                  packageId: newPackage.id,
                  costCode: curr.costCodeId,
                });
              }

              return acc;
            }, []);

            const newJobCostCodes = reducedArr
              .filter(
                (ra) => !jobCostCodes.find((jcc) => jcc.id === ra.costCode)
              )
              .map((ra) => ({ cost_code_id: ra.costCode }));

            if (newJobCostCodes.length) {
              await dispatch(
                handleUpdateJobCostCodes(selectedJob, "add", newJobCostCodes)
              );
            }

            // ADD TO PACKAGES THAT WERE CREATED
            for (let i = 0; i < reducedArr.length; i++) {
              await dispatch(
                handleUpdatePackageCostCodes(reducedArr[i].packageId, "add", [
                  reducedArr[i].costCode,
                ])
              );
            }
          }

          // REDUCE TO PACKAGES THAT WERE CREATED
          if (costCodesToCreate.length) {
            const reducedArr = costCodesToCreate.reduce((acc, curr) => {
              const newPackage = res[2].find(
                (p) => p.package_name === curr.packageName
              );
              if (newPackage) {
                acc.push({
                  packageId: newPackage.id,
                  costCode: curr.costCodeName,
                });
              }

              return acc;
            }, []);

            // REMOVE DUPLICATE COST CODES TO CREATE
            const dedupedCostCodes = Array.from(
              new Set(reducedArr.map((cc) => cc.costCode))
            );

            // PREPARE FOR POST
            const formattedCostCodes = dedupedCostCodes.map((cc) => ({
              name: cc,
            }));

            // GET RETURNED CREATED COST CODES
            const createdCostCodes = await dispatch(
              handleCreateCostCodes(formattedCostCodes)
            ).then((res) => {
              if (res.error) return [];
              return res[2];
            });

            // ADD TO PACKAGES THAT WERE CREATED
            if (createdCostCodes.length) {
              await dispatch(
                handleUpdateJobCostCodes(
                  selectedJob,
                  "add",
                  createdCostCodes.map((ccc) => ({ cost_code_id: ccc.id }))
                )
              );

              for (let i = 0; i < reducedArr.length; i++) {
                let costCode = createdCostCodes.find(
                  (ccc) => ccc.name === reducedArr[i].costCode
                );
                if (costCode) {
                  await dispatch(
                    handleUpdatePackageCostCodes(
                      reducedArr[i].packageId,
                      "add",
                      [costCode.id]
                    )
                  );
                }
              }
            }
          }
          dispatch(
            handleFetchPackageCostCodes([
              ...packages.map((p) => p.id),
              ...res[2].map((p) => p.id),
            ])
          );

          toggleCSVModal(false);
        }
      }
    );
  };

  const cancelJobCreation = (confirmed = false) => {
    if (confirmed) {
      dispatch(handleClearJobsState);
      setTimeout(
        () => window.location.assign(`${process.env.REACT_APP_FABPRO}/jobs/`),
        100
      );
    } else toggleCancelConfirmationModal(true);
  };

  const displayedPackages = useMemo(() => {
    if (packages) {
      return packages
        .sort((a, b) => naturalSort(a.package_name, b.package_name))
        .map((p) => ({
          id: p.id,
          value: p.id,
          display: `(${p.id}) ${p.package_name} - ${p.drawing_count} Drawings`,
        }));
    } else return [];
  }, [packages]);

  const displayedMaterials = useMemo(() => {
    if (materialTypes) {
      return materialTypes
        .sort((a, b) => naturalSort(a.name, b.name))
        .map((mt) => ({ id: mt.id, value: mt.id, display: mt.name }));
    } else return [];
  }, [materialTypes]);

  const displayedHeaders = useMemo(() => {
    if (receivedHeaders) {
      return receivedHeaders;
    } else return [];
  }, [receivedHeaders]);

  const csvTemplate = useMemo(() => {
    return buildCSV(Object.keys(packagesHeaders));
  }, [packagesHeaders]);

  const csvTemplateName = useMemo(() => {
    if (specificJob) {
      return `${specificJob.job_name}-${specificJob.job_number}_Package_Name_Template.csv`;
    } else return "package_template.csv";
  }, [specificJob]);

  return (
    <div className="wizard-wrapper">
      <Stepper
        currentStep={currentStep}
        setCurrentStep={setCurrentStep}
        setStepTitle={setStepTitle}
        complete={{}}
        sendToReact={sendToReact}
        selectedJob={selectedJob}
      />
      <div className="wizard-header">
        <h1 className="wizard-step-title">{stepTitle}</h1>
        <div className="button-wrapper">
          {currentStep === 1 && !selectedJob && (
            <Button
              className="cancel-job"
              onClick={() => cancelJobCreation(false)}
            >
              Cancel Job
            </Button>
          )}
          {currentStep === 2 && (
            <>
              <Button onClick={() => toggleCostCodeUploadModal(true)}>
                Upload Cost Codes
              </Button>
              <Button onClick={() => toggleCostCodeTypesModal(true)}>
                Cost Code Types
              </Button>
            </>
          )}
          {currentStep === 3 && (
            <>
              <Button as="a" href={flowsCSV} download="flows.csv">
                Export Flows
              </Button>
              <Button onClick={() => toggleCSVModal(true)}>
                Upload Package(s)
              </Button>
            </>
          )}
          {currentStep === 4 && (
            <>
              {features &&
                features.includes(41) &&
                permissions &&
                permissions.includes(302) && (
                  <>
                    <DownloadMAJsButton selectedDrawings={selectedRows} />
                    <Button
                      onClick={() => toggleManageMAJModal(true)}
                      disabled={!selectedRows.length}
                    >
                      Manage MAJ(s)
                    </Button>
                  </>
                )}
              <Button
                onClick={sendBackForApproval}
                disabled={!selectedRows.length}
              >
                Resubmit for Approval
              </Button>
              <Button
                onClick={() => toggleConfirmationModal(true)}
                disabled={!selectedRows.length}
              >
                Delete Selected Drawing(s)
              </Button>
              <Button
                onClick={() => toggleUploadDrawings(true)}
                disabled={!packages || !packages.length}
              >
                Upload Drawings
              </Button>
            </>
          )}
        </div>
        {currentStep === 5 && (
          <div className="link-wrapper">
            {materialTypes && materialTypes.length > 0 && (
              <a
                href={materialTypesCSV}
                download="materials_list.csv"
                className="csv-download material-types"
              >
                List of Materials
              </a>
            )}
            {drawingsInPackage && selectedPackage && (
              <a
                href={drawingsCSV}
                download="drawings_list.csv"
                className="csv-download drawings"
              >
                List of Drawings
              </a>
            )}
          </div>
        )}
      </div>
      <div
        className={`wizard-content ${
          currentStep === 3 || (currentStep === 1 && specificJob) ? "tall" : ""
        }`}
      >
        {currentStep === 1 && <Job setSelectedJob={setSelectedJob} />}
        {currentStep === 2 && <CostCodes selectedJob={selectedJob} />}
        {currentStep === 3 && <Packages selectedJob={selectedJob} />}
        {currentStep === 4 && (
          <Drawings
            selectedJob={selectedJob}
            setSelectedRows={setSelectedRows}
            setDrawingsGridOptionsApi={setDrawingsGridOptionsApi}
          />
        )}
        {currentStep === 5 && (
          <Items
            selectedPackage={selectedPackage}
            setSelectedPackage={setSelectedPackage}
            selectedJob={selectedJob}
          />
        )}
      </div>
      {showConfirmationModal && (
        <ConfirmationModal
          showModal={showConfirmationModal}
          toggleModal={() => toggleConfirmationModal(false)}
          action={"DELETE"}
          item={`${selectedRows.length} Drawings`}
          handleClick={confirmDeletion}
        />
      )}
      {showUploadDrawings && (
        <Modal
          open={showUploadDrawings}
          handleClose={() => {
            setUploadedFiles(null);
            toggleCreateWorkItems(false);
            setDefaultMaterial(null);
            setSelectedPackage(null);
            toggleUploadDrawings(false);
            setInvalidPdfNames([]);
          }}
        >
          <div className="upload-drawings-container">
            <h2 className="title">Upload Drawing(s)</h2>
            <div className="content">
              <label className="packages">
                Select a Package:
                <Select
                  options={displayedPackages}
                  onInput={(e) => setSelectedPackage(parseInt(e.target.value))}
                  value={selectedPackage}
                  required
                />
              </label>
              <label className="pdfs">
                Select Drawings to Upload:
                <Input
                  type="file"
                  accept=".pdf"
                  multiple
                  onChange={(e) => {
                    e.persist();
                    let invalidFiles = [];
                    for (let file of Object.entries(e.target.files)) {
                      if (/[^a-z0-9\.\-\_ ]/gi.test(file[1].name))
                        invalidFiles.push(file[1].name);
                    }

                    setInvalidPdfNames(invalidFiles);
                    if (!invalidFiles.length) setUploadedFiles(e.target.files);
                  }}
                  required
                />
              </label>
              {invalidPdfNames.length ? (
                <p className="invalid-pdf-names">
                  <span className="title">
                    The following PDFs have invalid characters in their name:
                  </span>
                  <ul>
                    {invalidPdfNames.map((n, i) => {
                      return (
                        <li>
                          {n
                            .split("")
                            .map((c) =>
                              /[^a-z0-9\.\-\_ ]/i.test(c) ? (
                                <span className="invalid-character">{c}</span>
                              ) : (
                                c
                              )
                            )}
                        </li>
                      );
                    })}
                  </ul>
                </p>
              ) : (
                <></>
              )}
              <label className="create-work-items">
                <input
                  type="checkbox"
                  onChange={() => {
                    if (!createWorkItems === false) {
                      setDefaultMaterial(null);
                    }

                    toggleCreateWorkItems(!createWorkItems);
                  }}
                />
                Create Work Item(s)?
              </label>
              {createWorkItems && (
                <label className="default-material">
                  Default Material:
                  <Select
                    options={displayedMaterials}
                    onInput={(e) =>
                      setDefaultMaterial(parseInt(e.target.value))
                    }
                    value={defaultMaterial}
                    required
                  />
                </label>
              )}
            </div>
            <div className="buttons">
              <Button
                className="cancel"
                onClick={() => {
                  setDefaultMaterial(null);
                  toggleCreateWorkItems(false);
                  setUploadedFiles(null);
                  setInvalidPdfNames([]);
                  setSelectedPackage(null);
                  toggleUploadDrawings(false);
                }}
              >
                Cancel
              </Button>
              <Button
                className="submit"
                onClick={uploadDrawings}
                disabled={
                  !!invalidPdfNames.length ||
                  !uploadedFiles ||
                  !uploadedFiles.length ||
                  !selectedPackage
                }
              >
                Upload <RiUploadCloudLine />
              </Button>
            </div>
          </div>
        </Modal>
      )}
      <CSVImport
        showCSVModal={showCSVModal}
        toggleCSVModal={toggleCSVModal}
        csvError={csvError}
        setCSVError={setCSVError}
        submitText="Submit"
        title="Upload Items"
        getResults={getResults}
        headers={displayedHeaders}
        images={packagesImages}
        templatePath={csvTemplate}
        download={csvTemplateName}
        handleSubmit={handleSubmit}
      />
      {showCancelConfirmationModal && (
        <ConfirmationModal
          showModal={showCancelConfirmationModal}
          toggleModal={() => toggleCancelConfirmationModal(false)}
          action="WARN"
          message="Are you sure you want to cancel the job setup?"
          submitText="Yes, cancel it!"
          cancelText="No!"
          handleClick={() => cancelJobCreation(true)}
        />
      )}
      {showCostCodeUploadModal && (
        <CostCodeUploadModal
          showModal={showCostCodeUploadModal}
          handleClose={toggleCostCodeUploadModal}
          selectedJobId={selectedJob}
        />
      )}
      {showCostCodeTypesModal && (
        <CostCodeTypesModal
          showModal={showCostCodeTypesModal}
          handleClose={toggleCostCodeTypesModal}
        />
      )}
      {showManageMAJModal && (
        <ManageMAJModal
          drawingOrPackage={{
            drawing_ids: selectedRows.map((r) => r.id),
            job_id: selectedJob,
          }}
          showModal={showManageMAJModal}
          handleClose={() => toggleManageMAJModal(false)}
          currentTable={"DRAWINGS"}
        />
      )}
    </div>
  );
};

export default Wizard;
