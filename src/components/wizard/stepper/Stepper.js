// NPM PACKAGE IMPORTS
import React from "react";
import { Fa<PERSON>he<PERSON>, FaArrowLeft } from "react-icons/fa";
import { ImPencil2 } from "react-icons/im";
import StepperNextButtonWithOptionalTooltip from "./StepperNextButtonWithOptionalTooltip.js";

// STYLE IMPORTS
import "./stylesStepper.scss";

// CONSTANTS IMPORTS
import { steps } from "../wizardConstants";

const Stepper = ({
  currentStep,
  setCurrentStep,
  setStepTitle,
  complete,
  sendToReact,
  selectedJob,
}) => {
  return (
    <ul className="stepper">
      {steps.map((s, idx) => (
        <li
          key={s.id}
          className={`step-wrapper ${currentStep === s.id ? "selected" : ""} ${
            complete[s.id] ? "complete" : ""
          } ${currentStep > s.id ? "passed" : ""}`}
          onClick={() => {
            sendToReact(
              s.id === 1 ? "job" : s.name.toLowerCase().replace(" ", "")
            );
          }}
        >
          <div className="step-content">
            <span className="step-circle">
              {complete[s.id] ? (
                <FaCheck />
              ) : currentStep === s.id ? (
                <ImPencil2 />
              ) : (
                s.id
              )}
            </span>
            <span className="step-name">{s.name}</span>
          </div>
          <div className="step-background"></div>
        </li>
      ))}
      <div className="stepper-buttons">
        <div
          className={`stepper-button ${currentStep === 1 ? "disabled" : ""}`}
          onClick={() => {
            if (currentStep <= 1 || !steps[currentStep - 2]) return;
            setStepTitle(steps[currentStep - 2].name);
            setCurrentStep(currentStep - 1);
          }}
        >
          <FaArrowLeft />
          Previous
        </div>
        <StepperNextButtonWithOptionalTooltip
          currentStep={currentStep}
          selectedJob={selectedJob}
          steps={steps}
          setStepTitle={setStepTitle}
          setCurrentStep={setCurrentStep}
          loadTooltip={currentStep === 1 && !selectedJob} // Pass true to enable tooltip
        />
      </div>
    </ul>
  );
};

export default Stepper;
