import React from "react";
import withTooltip from "../../reusable/tootltipHOC/withTooltip";
import StepperNextButton from "./StepperNextButton";

const StepperNextButtonWithOptionalTooltip = (props) => {
  const { currentStep, selectedJob, loadTooltip } = props;
  const tooltipText =
    currentStep === 1 && !selectedJob ? "Please fill out required fields." : "";

  const WrappedButton = loadTooltip
    ? withTooltip(StepperNextButton)
    : StepperNextButton;

  return (
    <WrappedButton
      {...props}
      tooltipText={tooltipText} // Pass the tooltip text as a prop
    />
  );
};

export default StepperNextButtonWithOptionalTooltip;
