@import "../../styles/colors.scss";

ul.stepper {
  margin: 0;
  list-style: none;

  display: grid;
  grid-template-rows: 70px 30px;
  grid-template-columns: repeat(10, 1fr);
  background-color: #2d3039;
  row-gap: 20px;
  padding: 5px 0 30px;
  justify-items: center;

  & > div.stepper-buttons {
    grid-column: 5/7;
    grid-row: 2/-1;

    display: flex;
    column-gap: 5px;

    & div.stepper-button {
      font-size: 0.8rem;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      border-radius: 3px;
      height: 30px;
      width: 80px;
      padding: 0;
      align-self: center;

      &:not(.disabled) {
        cursor: pointer;
        color: #fff;
        background-color: $fabProBlue;

        &:hover {
          background-color: darken($fabProBlue, 10%);
        }
      }
    }
  }

  & li.step-wrapper {
    height: 100%;
    padding: 5px 5px 10px 5px;
    position: relative;
    width: 100px;
    text-align: center;
    color: #aaa;
    cursor: pointer;

    &:not(.selected):hover {
      & div.step-background,
      span.step-circle {
        color: $fabProBlue;
        border-color: $fabProBlue;
      }
    }

    &:nth-of-type(1) {
      grid-column: 1/3;
    }

    &:nth-of-type(2) {
      grid-column: 3/5;
    }

    &:nth-of-type(3) {
      grid-column: 5/7;
    }

    &:nth-of-type(4) {
      grid-column: 7/9;
    }

    &:nth-of-type(5) {
      grid-column: 9/-1;
    }

    & div.step-background {
      max-height: 10px;
      background-color: #eee;
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      border-radius: 10px;
      transition: max-height 300ms ease, transform 300ms ease,
        border-radius 300ms ease;
      z-index: -1;
      transform: translateY(20px);
    }

    & div.step-content {
      display: grid;
      grid-template-rows: 40px 1fr;
      row-gap: 5px;

      & span {
        display: block;
      }

      & span.step-name {
        color: gray;
        font-size: 0.9rem;
        padding-top: 5px;
      }

      & span.step-circle {
        background-color: #eee;
        margin: 0 auto;
        font-size: 24px;
        width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 50%;
        font-family: "Times New Roman", Times, serif;
        border: 1px solid #2d3039;

        & svg {
          font-size: 15px;
        }
      }
    }

    &.selected,
    &.complete {
      & div.step-background {
        max-height: 100%;
        transform: unset;
        border-radius: unset;
      }
    }

    &.complete {
      border-color: $green;

      & div.step-background {
        background-color: $green;
        border-color: $green;
      }

      & div.step-content {
        & span.step-circle {
          border-color: $green;
          background-color: $green;
        }
      }
    }

    &.passed div.step-content span.step-circle {
      background-color: $fabProBlue;
      color: #fff;
      border-color: #fff;
    }

    &.selected {
      color: $fabProBlue;
      border-color: $fabProBlue;
      cursor: default;

      & div.step-content span.step-name {
        color: #fff;
      }

      & div.step-background {
        background-color: #eee;
        border-color: $fabProBlue;
      }

      & div.step-content {
        & span.step-circle {
          background-color: #eee;
          border-color: $fabProBlue;
          border-width: 2px;
        }
      }
    }
  }
}
