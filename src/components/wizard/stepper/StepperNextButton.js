import React from "react";
import { FaArrowRight } from "react-icons/fa";

const StepperNextButton = ({
  currentStep,
  selectedJob,
  steps,
  setStepTitle,
  setCurrentStep,
}) => {
  const handleClick = () => {
    if (currentStep === 1 && !selectedJob) return;
    if (currentStep >= 5 || !steps[currentStep]) return;

    setStepTitle(steps[currentStep].name);
    setCurrentStep(currentStep + 1);
  };

  // Determine if the button should be disabled or show an error style
  const buttonClass = `stepper-button ${
    currentStep === 5 || (currentStep === 1 && !selectedJob) ? "disabled" : ""
  }`;

  return (
    <div className={buttonClass} onClick={handleClick}>
      Next
      <FaArrowRight />
    </div>
  );
};

export default StepperNextButton;
