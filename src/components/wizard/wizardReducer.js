const initialState = {
  isLoading: false,
  error: null,
  users: [],
  costCodes: null,
  drawingsInPackage: null,
  userAssignments: null,
  wizardItemsColumnState: null,
  drawings: null,
  packageCostCodes: null,
  packageCostCodesSpecificPackage: null,
  packages: null,
  flows: null,
  jobCostCodes: [],
  generalCostCodes: null,
  allUsers: null,
  costCodeTypes: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_WIZARD_USERS_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_WIZARD_USERS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, users: payload };
    case "RECEIVE_WIZARD_USERS_FAILED":
      return { ...state, isLoading: false, error: payload, users: [] };
    case "RECEIVE_WIZARD_COST_CODES_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_WIZARD_COST_CODES_SUCCEEDED":
      return { ...state, isLoading: false, error: null, costCodes: payload };
    case "RECEIVE_WIZARD_COST_CODES_FAILED":
      return { ...state, isLoading: false, error: payload, costCodes: [] };
    case "RECEIVE_WIZARD_GENERAL_COST_CODES_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_WIZARD_GENERAL_COST_CODES_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        generalCostCodes: payload,
      };
    case "RECEIVE_WIZARD_GENERAL_COST_CODES_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        generalCostCodes: null,
      };
    case "RECEIVE_WIZARD_ADDITIONAL_COST_CODES_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        costCodes: [...payload[2], ...state.costCodes],
      };
    case "RECEIVE_DRAWINGS_IN_PACKAGE_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_DRAWINGS_IN_PACKAGE_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        drawingsInPackage: payload,
      };
    case "RECEIVE_DRAWINGS_IN_PACKAGE_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        drawingsInPackage: null,
      };
    case "RECEIVE_WIZARD_USER_ASSIGNMENTS_STARTED":
      return { ...state, isLoading: true, error: null, userAssignments: null };
    case "RECEIVE_WIZARD_USER_ASSIGNMENTS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        userAssignments: payload,
      };
    case "RECEIVE_WIZARD_USER_ASSIGNMENTS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        userAssignments: [],
      };
    case "RECEIVE_WIZARD_ITEMS_COLUMN_STATE_STARTED":
      return { ...state, wizardItemsColumnState: null };
    case "RECEIVE_WIZARD_ITEMS_COLUMN_STATE_SUCCEEDED":
      return { ...state, wizardItemsColumnState: payload };
    case "RECEIVE_WIZARD_ITEMS_COLUMN_STATE_FAILED":
      return { ...state, wizardItemsColumnState: [] };
    case "RECEIVE_WIZARD_DRAWINGS_STARTED":
      return { ...state, isLoading: true, drawings: null };
    case "RECEIVE_WIZARD_DRAWINGS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, drawings: payload };
    case "RECEIVE_WIZARD_DRAWINGS_FAILED":
      return { ...state, isLoading: false, error: payload, drawings: [] };
    case "RECEIVE_WIZARD_DRAWINGS_ADDITIONAL_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_WIZARD_DRAWINGS_ADDITIONAL_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        drawings: [...payload, ...state.drawings],
      };
    case "RECEIVE_WIZARD_DRAWINGS_ADDITIONAL_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_WIZARD_PACKAGE_COST_CODES_STARTED":
      return { ...state, isLoading: true, error: null, packageCostCodes: null };
    case "RECEIVE_WIZARD_PACKAGE_COST_CODES_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        packageCostCodes: payload,
      };
    case "RECEIVE_WIZARD_PACKAGE_COST_CODES_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        packageCostCodes: [],
      };
    case "RECEIVE_WIZARD_PACKAGE_COST_CODES_SPECIFIC_PACKAGE_STARTED":
      return {
        ...state,
        isLoading: true,
        error: null,
        packageCostCodesSpecificPackage: null,
      };
    case "RECEIVE_WIZARD_PACKAGE_COST_CODES_SPECIFIC_PACKAGE_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        packageCostCodesSpecificPackage: payload,
      };
    case "RECEIVE_WIZARD_PACKAGE_COST_CODES_SPECIFIC_PACKAGE_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        packageCostCodesSpecificPackage: [],
      };
    case "RECEIVE_WIZARD_FLOWS_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_WIZARD_FLOWS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, flows: payload };
    case "RECEIVE_WIZARD_FLOWS_FAILED":
      return { ...state, isLoading: false, error: payload, flows: [] };
    case "RECEIVE_WIZARD_JOB_COST_CODES_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_WIZARD_JOB_COST_CODES_SUCCEEDED":
      return { ...state, isLoading: false, error: null, jobCostCodes: payload };
    case "RECEIVE_WIZARD_JOB_COST_CODES_FAILED":
      return { ...state, isLoading: false, error: payload, jobCostCodes: [] };
    case "RECEIVE_WIZARD_ADDITIONAL_JOB_COST_CODES_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        jobCostCodes: [...payload, ...state.jobCostCodes],
      };
    case "RECEIVE_WIZARD_REMOVE_JOB_COST_CODES_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        jobCostCodes: state.jobCostCodes.filter(
          (jcc) => !payload.find((cc) => cc.id === jcc.id)
        ),
      };
    case "RECEIVE_WIZARD_ALL_USERS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_WIZARD_ALL_USERS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, allUsers: payload };
    case "RECEIVE_WIZARD_ALL_USERS_FAILED":
      return { ...state, isLoading: false, error: payload, allUsers: [] };
    case "RECEIVE_COST_CODE_TYPES_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_COST_CODE_TYPES_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        costCodeTypes: payload,
      };
    case "RECEIVE_COST_CODE_TYPES_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "UPDATE_COST_CODE_TYPES_STARTED":
      return { ...state, isLoading: true, error: null };
    case "UPDATE_COST_CODE_TYPES_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "UPDATE_COST_CODE_TYPES_FAILED":
      return { ...state, isLoading: false, error: payload };
    default:
      return state;
  }
}
