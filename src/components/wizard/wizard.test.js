import configureMockStore from "redux-mock-store";
import axios from "axios";
import Mock<PERSON>dapter from "axios-mock-adapter";
import thunk from "redux-thunk";

import {
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  handleClearUsers,
  handleFetchUsersAssignedToItem,
  handleFetchCostCodes,
  handleFetchDrawingsInPackage,
  handleFetchDrawings,
  handleFetchPackageCostCodes,
  handleFetchPackageCostCodesSpecificPackage,
  handleFetchFlows,
  handleUpdateJobCostCodes,
  handleUpdatePackageCostCodes,
} from "./wizardActions";

describe("Wizard", () => {
  const testError = (type, message) => ({
    error: {
      status: 400,
      message: `${message ? message : `No ${type} founnd.`}`,
    },
  });

  const testUsers = [
    {
      id: 1,
      first_name: "test",
      last_name: "user",
      role_id: 1,
    },
    {
      id: 2,
      first_name: "test",
      last_name: "user 2",
      role_id: 1,
    },
    {
      id: 3,
      first_name: "test",
      last_name: "user 3",
      role_id: 2,
    },
  ];

  describe("action handlers should perform the necessary functions", () => {
    let store;
    let httpMock;

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    it("handleClearUsers clears user state", () => {
      store.dispatch(handleClearUsers);

      const receivedActions = store.getActions();
      const expectedActions = [
        receiveSucceeded("WIZARD_USERS", []),
        receiveSucceeded("WIZARD_USER_ASSIGNMENTS", []),
      ];

      expect(receivedActions).toEqual(expectedActions);
    });

    it("handleFetchUsersAssignedToItem fetches all users assigned to an item", async () => {
      const type = "WIZARD_USER_ASSIGNMENTS";
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/users/assignments?item_id=1&item_level=job`
        )
        .replyOnce(200, testUsers)
        .onGet(
          `${process.env.REACT_APP_API}/users/assignments?item_id=1&item_level=job&role_id=1`
        )
        .replyOnce(200, testUsers.slice(0, 1))
        .onGet(
          `${process.env.REACT_APP_API}/users/assignments?item_id=10&item_level=job`
        )
        .replyOnce(400, testError("items"));

      await store
        .dispatch(handleFetchUsersAssignedToItem(1, "job"))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted(type),
            receiveSucceeded(type, testUsers),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testUsers);

          store.clearActions();
        });

      await store
        .dispatch(handleFetchUsersAssignedToItem(1, "job", 1))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted(type),
            receiveSucceeded(type, testUsers.slice(0, 1)),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testUsers.slice(0, 1));

          store.clearActions();
        });

      return store
        .dispatch(handleFetchUsersAssignedToItem(10, "job"))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted(type),
            receiveFailed(type, testError("items")),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testError("items"));
        });
    });

    it("handleFetchCostCodes fetches all cost codes or cost codes within specified job", async () => {
      const typeOne = "WIZARD_COST_CODES",
        typeTwo = "WIZARD_JOB_COST_CODES";
      const responseArr = [
        { id: 2, name: "z" },
        { id: 3, name: "y" },
        { id: 4, name: "x" },
      ];

      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/cost-codes?is_active=1&include_general=1&split_out=0&only_general=0`
        )
        .replyOnce(200, responseArr)
        .onGet(
          `${process.env.REACT_APP_API}/cost-codes?is_active=1&include_general=1&split_out=0&only_general=0&job_ids=1`
        )
        .replyOnce(200, responseArr.slice(1))
        .onGet(
          `${process.env.REACT_APP_API}/cost-codes?is_active=1&include_general=1&split_out=0&only_general=0`
        )
        .replyOnce(400, testError("cost codes"));

      await store.dispatch(handleFetchCostCodes()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(typeOne),
          receiveSucceeded(typeOne, [...responseArr].reverse()),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual([...responseArr].reverse());

        store.clearActions();
      });

      await store.dispatch(handleFetchCostCodes([1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(typeTwo),
          receiveSucceeded(typeTwo, [...responseArr.slice(1)].reverse()),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(
          [...responseArr.slice(1)].reverse()
        );

        store.clearActions();
      });

      return store.dispatch(handleFetchCostCodes()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(typeOne),
          receiveFailed(typeOne, testError("cost codes").error),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(
          testError("cost codes").error
        );
      });
    });

    it("handleFetchDrawingsInPackage fetches all drawings within a specified package", async () => {
      const type = "DRAWINGS_IN_PACKAGE";
      const responseArr = [{ id: 2 }, { id: 3 }, { id: 4 }];

      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/drawings/simplified?app_type=fab&is_active=1&package_ids=1&forge_info=1`
        )
        .replyOnce(200, responseArr)
        .onGet(
          `${process.env.REACT_APP_API}/drawings/simplified?app_type=fab&is_active=1&package_ids=1&forge_info=1`
        )
        .replyOnce(400, testError("drawings"));

      await store.dispatch(handleFetchDrawingsInPackage(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(type),
          receiveSucceeded(type, responseArr),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(responseArr);

        store.clearActions();
      });

      return store.dispatch(handleFetchDrawingsInPackage(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(type),
          receiveFailed(type, testError("drawings").error),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testError("drawings").error);
      });
    });

    it("handleFetchDrawings fetches all drawings within a specified job", async () => {
      const type = "WIZARD_DRAWINGS";
      const responseArr = [{ id: 2 }, { id: 3 }, { id: 4 }];

      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/drawings/simplified?app_type=fab&is_active=1&job_ids=1&forge_info=1`
        )
        .replyOnce(200, responseArr)
        .onGet(
          `${process.env.REACT_APP_API}/drawings/simplified?app_type=fab&is_active=1&job_ids=1&forge_info=1`
        )
        .replyOnce(400, testError("drawings"));

      await store.dispatch(handleFetchDrawings(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(type),
          receiveSucceeded(type, responseArr),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(responseArr);

        store.clearActions();
      });

      return store.dispatch(handleFetchDrawings(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(type),
          receiveFailed(type, testError("drawings").error),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testError("drawings").error);
      });
    });

    it("handleFetchPackageCostCodes fetches all cost codes within specified packages", async () => {
      const type = "WIZARD_PACKAGE_COST_CODES";
      const responseArr = [
        { id: 2, name: "z" },
        { id: 3, name: "y" },
        { id: 4, name: "x" },
      ];
      let payload = [
        { id: 4, name: "x" },
        { id: 3, name: "y" },
        { id: 2, name: "z" },
      ];

      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/cost-codes?is_active=1&include_general=0&split_out=1&only_general=0&package_ids=1`
        )
        .replyOnce(200, responseArr)
        .onGet(
          `${process.env.REACT_APP_API}/cost-codes?is_active=1&include_general=0&split_out=1&only_general=0&package_ids=1`
        )
        .replyOnce(400, testError("cost codes"));

      await store.dispatch(handleFetchPackageCostCodes([1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(type),
          receiveSucceeded(type, payload),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(payload);

        store.clearActions();
      });

      return store.dispatch(handleFetchPackageCostCodes([1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(type),
          receiveFailed(type, testError("cost codes").error),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(
          testError("cost codes").error
        );
      });
    });

    it("handleFetchPackageCostCodesSpecificPackage fetches all cost codes within specific package", async () => {
      const type = "WIZARD_PACKAGE_COST_CODES_SPECIFIC_PACKAGE";
      const responseArr = [
        { id: 2, name: "z" },
        { id: 3, name: "y" },
        { id: 4, name: "x" },
      ];
      let payload = [
        { id: 4, name: "x" },
        { id: 3, name: "y" },
        { id: 2, name: "z" },
      ];

      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/cost-codes?is_active=1&include_general=0&split_out=1&only_general=0&package_ids=1`
        )
        .replyOnce(200, responseArr)
        .onGet(
          `${process.env.REACT_APP_API}/cost-codes?is_active=1&include_general=0&split_out=1&only_general=0&package_ids=1`
        )
        .replyOnce(400, testError("cost codes"));

      await store
        .dispatch(handleFetchPackageCostCodesSpecificPackage(1))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted(type),
            receiveSucceeded(type, payload),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(payload);

          store.clearActions();
        });

      return store
        .dispatch(handleFetchPackageCostCodesSpecificPackage(1))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted(type),
            receiveFailed(type, testError("cost codes").error),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(
            testError("cost codes").error
          );
        });
    });

    it("handleFetchFlows fetches all work flows", async () => {
      const type = "WIZARD_FLOWS";
      const responseArr = [
        { id: 2, name: "z" },
        { id: 3, name: "y" },
        { id: 4, name: "x" },
      ];

      httpMock
        .onGet(`${process.env.REACT_APP_API}/work-flows`)
        .replyOnce(200, responseArr)
        .onGet(`${process.env.REACT_APP_API}/work-flows`)
        .replyOnce(400, testError("work flows"));

      await store.dispatch(handleFetchFlows).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(type),
          receiveSucceeded(type, responseArr),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(responseArr);

        store.clearActions();
      });

      return store.dispatch(handleFetchFlows).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted(type),
          receiveFailed(type, testError("work flows").error),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(
          testError("work flows").error
        );
      });
    });

    it("handleUpdateJobCostCodes adds or removes cost codes", async () => {
      const typeOne = "WIZARD_ADDITIONAL_JOB_COST_CODES";
      const typeTwo = "WIZARD_REMOVE_JOB_COST_CODES";
      const testBody = (action) => ({
        action,
        data: [{ cost_code_id: 1 }, { cost_code_id: 2 }, { cost_code_id: 3 }],
      });
      const responseArr = [[], [], [{ id: 1 }]];

      httpMock
        .onPut(`${process.env.REACT_APP_API}/jobs/cost-codes/1`)
        .replyOnce(200, responseArr)
        .onPut(`${process.env.REACT_APP_API}/jobs/cost-codes/1`)
        .replyOnce(200, responseArr);

      await store
        .dispatch(
          handleUpdateJobCostCodes(1, "add", [
            { cost_code_id: 1 },
            { cost_code_id: 2 },
            { cost_code_id: 3 },
          ])
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [receiveSucceeded(typeOne, responseArr[2])];

          expect(receivedActions).toEqual(expectedActions);
          expect(httpMock.history.put[0].data).toEqual(
            JSON.stringify(testBody("add"))
          );
          store.clearActions();
        });
      return store
        .dispatch(
          handleUpdateJobCostCodes(1, "remove", [
            { cost_code_id: 1 },
            { cost_code_id: 2 },
            { cost_code_id: 3 },
          ])
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [receiveSucceeded(typeTwo, responseArr[2])];

          expect(receivedActions).toEqual(expectedActions);
          expect(httpMock.history.put[1].data).toEqual(
            JSON.stringify(testBody("remove"))
          );
          store.clearActions();
        });
    });

    it("handleUpdatePackageCostCodes adds or removes cost codes", async () => {
      const testBody = (action) => ({ action, cost_code_ids: "1,2,3" });

      httpMock
        .onPut(`${process.env.REACT_APP_API}/packages/cost-codes/1`)
        .replyOnce(200, { message: "Success" })
        .onPut(`${process.env.REACT_APP_API}/packages/cost-codes/1`)
        .replyOnce(200, { message: "Success" });

      await store
        .dispatch(handleUpdatePackageCostCodes(1, "add", [1, 2, 3]))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [];

          expect(receivedActions).toEqual(expectedActions);
          expect(httpMock.history.put[0].data).toEqual(
            JSON.stringify(testBody("add"))
          );
          store.clearActions();
        });
      return store
        .dispatch(handleUpdatePackageCostCodes(1, "remove", [1, 2, 3]))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [];

          expect(receivedActions).toEqual(expectedActions);
          expect(httpMock.history.put[1].data).toEqual(
            JSON.stringify(testBody("remove"))
          );
          store.clearActions();
        });
    });
  });
});
