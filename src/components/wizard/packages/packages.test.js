import { packagesColumns } from "./packagesConstants";

describe("Packages Column Defs", () => {
  const defaultHeaders = [
    "",
    "Pkg ID",
    "Package Name",
    "Exclude from Float Mode?",
    "Flow",
    "Area",
    "Budget Hours",
    "Due Date",
    "Cost Codes",
    "Action",
  ];
  const testPackages = [
    {
      id: 32,
      package_name: "Test",
      due_date_unix: 1918008000,
      budget_hours: 2.4583212,
      exclude_float_mode: 0,
      work_flow_id: 15,
      cost_codes: "Test 1, Test 2",
      area: null,
    },
  ];
  const testStore = {
    getState: () => ({ profileData: testStore.profileData }),
    profileData: {
      systemSettings: {
        date_display: "MM-DD-YYYY",
        timezone: "America/Chicago",
      },
    },
  };

  const deleteAction = jest.fn();
  const flows = [{ id: 15, display: "Test" }];

  let populatedColumns;

  beforeEach(() => {
    populatedColumns = packagesColumns(deleteAction, flows, testStore);
  });

  it("Packages headers are correct", () => {
    let columnHeaders = populatedColumns.map((c) => c.headerName);
    expect(columnHeaders).toEqual(defaultHeaders);
  });

  describe("PACKAGE NAME", () => {
    let column;
    beforeEach(() => {
      column = populatedColumns.find((c) => c.headerName === "Package Name");
    });
    it("valueParser", () => {
      const newValueParams = {
        oldValue: "oldValue",
        newValue: "newValue",
        data: testPackages[0],
      };
      const oldValueParams = {
        oldValue: "oldValue",
        newValue: "",
        data: testPackages[0],
      };
      expect(column.valueParser(newValueParams)).toEqual("newValue");
      expect(column.valueParser(oldValueParams)).toEqual("oldValue");
    });
    it("getQuickFilterText gets value from params.data", () => {
      const params = {
        data: testPackages[0],
      };
      expect(column.getQuickFilterText(params)).toEqual("Test");
    });
  });

  describe("EXCLUDE FROM FLOAT MODE?", () => {
    let column;
    beforeEach(() => {
      column = populatedColumns.find(
        (c) => c.headerName === "Exclude from Float Mode?"
      );
    });
    it("valueFormatter", () => {
      const params = {
        data: testPackages[0],
      };

      expect(column.valueFormatter(params)).toEqual(false);
    });
    it("cellRenderer", () => {
      expect(column.cellRenderer).toEqual("toggleCellEditor");
    });
    it("cellRendererParams", () => {
      const params = {
        node: {
          rowHeight: 60,
          childIndex: 3,
        },
        value: false,
      };

      expect(JSON.stringify(column.cellRendererParams(params))).toEqual(
        JSON.stringify({
          rowHeight: 60,
          even: false,
          value: false,
        })
      );
    });
  });

  describe("FLOW", () => {
    let column;
    beforeEach(() => {
      column = populatedColumns.find((c) => c.headerName === "Flow");
    });
    const params = {
      value: testPackages[0].work_flow_id,
      data: testPackages[0],
    };
    it("valueFormatter", () => {
      expect(column.valueFormatter(params)).toEqual("Test");
    });
    it("getQuickFilterText", () => {
      expect(column.getQuickFilterText({ ...params, colDef: column })).toEqual(
        "Test"
      );
    });
    it("cellEditorParams", () => {
      expect(JSON.stringify(column.cellEditorParams(params))).toEqual(
        JSON.stringify({ ...params, values: flows })
      );
    });
  });

  describe("AREA", () => {
    let column;
    beforeEach(() => {
      column = populatedColumns.find((c) => c.headerName === "Area");
    });
    it("valueParser", () => {
      const newValueParams = {
        oldValue: "oldValue",
        newValue: "newValue",
        data: testPackages[0],
      };
      const oldValueParams = {
        oldValue: "oldValue",
        newValue: "",
        data: testPackages[0],
      };
      expect(column.valueParser(newValueParams)).toEqual("newValue");
      expect(column.valueParser(oldValueParams)).toEqual(null);
    });
    it("getQuickFilterText gets value from params.data", () => {
      const params = {
        data: testPackages[0],
      };
      expect(column.getQuickFilterText(params)).toEqual(testPackages[0].area);
    });
  });

  describe("BUDGET HOURS", () => {
    let column;
    beforeEach(() => {
      column = populatedColumns.find((c) => c.headerName === "Budget Hours");
    });
    it("valueFormatter", () => {
      const params = {
        value: testPackages[0].budget_hours,
        data: testPackages[0],
      };
      const emptyParams = {
        data: {},
      };
      expect(column.valueFormatter(params)).toEqual("2.46");
      expect(column.valueFormatter(emptyParams)).toEqual("0.00");
    });
    it("valueParser", () => {
      const params = {
        oldValue: 10,
        newValue: "100",
      };
      const badParams = {
        oldValue: 10,
        newValue: "",
      };
      expect(column.valueParser(params)).toEqual(100);
      expect(column.valueParser(badParams)).toEqual(10);
    });
    it("getQuickFilterText", () => {
      const params = {
        colDef: column,
        data: testPackages[0],
      };
      expect(column.getQuickFilterText(params)).toEqual(
        column.valueFormatter(params)
      );
    });
  });

  describe("DUE DATE", () => {
    let column;
    beforeEach(() => {
      column = populatedColumns.find((c) => c.headerName === "Due Date");
    });
    it("valueParser", () => {
      const params = {
        oldValue: testPackages[0].due_date_unix,
        newValue: "10-20-2021",
      };
      const badParams = {
        oldValue: "",
        newValue: "",
      };
      expect(column.valueParser(params)).toEqual(1634706000);
      expect(column.valueParser(badParams)).toEqual("");
    });
    it("valueFormatter", () => {
      const params = {
        value: testPackages[0].due_date_unix,
      };
      const badParams = {
        value: "",
      };
      expect(column.valueFormatter(params)).toEqual("10-12-2030");
      expect(column.valueFormatter(badParams)).toEqual("-");
    });
    it("getQuickFilterText", () => {
      const params = {
        colDef: {
          ...column,
        },
        value: "10-12-2030",
      };
      expect(column.getQuickFilterText(params)).toEqual("10-12-2030");
    });
  });

  describe("COST CODES", () => {
    let column;
    beforeEach(() => {
      column = populatedColumns.find((c) => c.headerName === "Cost Codes");
    });
    const params = {
      value: testPackages[0].cost_codes,
      data: testPackages[0],
    };
    it("getQuickFilterText", () => {
      expect(column.getQuickFilterText(params)).toEqual("Test 1, Test 2");
    });
  });

  describe("ACTION", () => {
    let column;
    beforeEach(() => {
      column = populatedColumns.find((c) => c.headerName === "Action");
    });
    const params = {
      data: testPackages[0],
    };
    it("valueGetter", () => {
      expect(column.valueGetter(params)).toEqual(null);
    });
    it("valueFormatter", () => {
      expect(JSON.stringify(column.valueFormatter(params))).toEqual(
        JSON.stringify({
          actions: [
            {
              action: () => deleteAction(params.data.id),
              actionName: "Delete",
            },
          ],
        })
      );
    });
  });
});
