// NPM PACKAGE IMPORTS
import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import Input from "msuite_storybook/dist/input/Input";
import Select from "msuite_storybook/dist/select/Select";
import Toggle from "msuite_storybook/dist/toggle/Toggle";
import DatePicker from "react-datepicker";
import moment from "moment";

// REDUX IMPORTS
import { handleCreatePackages } from "../../packages/packagesActions";

const PackagesInputs = ({
  selectedJob,
  setSelectedPackage,
  displayedFlows,
}) => {
  const [packageName, setPackageName] = useState("");
  const [excludeFloatMode, toggleExcludeFloatMode] = useState(false);
  const [selectedFlow, setSelectedFlow] = useState(null);
  const [dueDate, setDueDate] = useState(null);

  const dispatch = useDispatch();

  const onBlur = () => {
    if (packageName.trim() && selectedFlow && dueDate) {
      dispatch(
        handleCreatePackages(selectedJob, [
          {
            package_name: packageName,
            exclude_float_mode: excludeFloatMode ? 1 : 0,
            work_flow_id: selectedFlow,
            due_date: moment(dueDate).format("YYYY-MM-DD"),
          },
        ])
      ).then((res) => {
        if (!res.error && res[2].length) {
          setPackageName("");
          toggleExcludeFloatMode(false);
          setDueDate(null);
          if (displayedFlows) {
            const defaultFlow = displayedFlows.find((f) => f.default === 1);
            if (defaultFlow) setSelectedFlow(defaultFlow.id);
          }

          setSelectedPackage(res[2][0]);
        }
      });
    }
  };

  useEffect(() => {
    onBlur();
  }, [selectedFlow]);

  useEffect(() => {
    if (displayedFlows) {
      const defaultFlow = displayedFlows.find((f) => f.default === 1);
      if (defaultFlow) setSelectedFlow(defaultFlow.id);
    }
  }, [displayedFlows]);

  return (
    <div className="packages-inputs">
      <h3 className="packages-inputs-title">Create Package</h3>
      <label className="wizard-content-label">
        <span>
          Package Name <span style={{ color: "red" }}>*</span>
        </span>
        <Input
          placeholder="Package Name"
          value={packageName}
          onChange={(e) => setPackageName(e.target.value)}
          onBlur={onBlur}
        />
      </label>
      <div className="wizard-content-label">
        <span>Exclude from Float Mode?</span>
        <Toggle
          name="float-mode-toggle"
          text={["yes", "no"]}
          defaultChecked={excludeFloatMode}
          onToggleChanged={() => toggleExcludeFloatMode(!excludeFloatMode)}
        />
      </div>
      <label className="wizard-content-label">
        <span>
          Work Flow <span style={{ color: "red" }}>*</span>
        </span>
        <Select
          options={displayedFlows}
          value={selectedFlow}
          onInput={(e) => setSelectedFlow(parseInt(e.target.value))}
          placeholder="Select Flow"
        />
      </label>
      <label className="wizard-content-label date-picker">
        <span>
          Due Date <span style={{ color: "red" }}>*</span>
        </span>
        <DatePicker
          className="date-picker"
          autoComplete="off"
          selected={dueDate}
          onChange={setDueDate}
          onBlur={onBlur}
        />
      </label>
    </div>
  );
};

export default PackagesInputs;
