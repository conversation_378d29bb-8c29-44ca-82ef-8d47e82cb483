@import "../../styles/colors.scss";

div.packages {
  grid-template-columns: repeat(4, 1fr);
  row-gap: 10px;

  & > div.packages-inputs {
    grid-column: 1/-1;
    grid-template-columns: 1fr 200px repeat(2, 1fr);
    grid-template-rows: 1fr 1fr;

    background-color: $darkFab;
    padding: 0 15px;
    border: 1px solid #000;
    border-radius: 3px;

    & > h3.packages-inputs-title {
      grid-column: 1/-1;
      grid-row: 1/2;
      margin: 0;
      padding: 0;
      color: $lighterSlate;
      font-size: 1rem;
    }

    & label.wizard-content-label.date-picker div {
      & input.date-picker {
        height: 30px;
        padding: 0;
        border: 1px solid #ccc;
        width: 100%;
        outline: none;
        font-size: 1.4rem;
        box-sizing: border-box;
        border-radius: 3px;

        &:hover {
          border-color: $fabProBlue;
        }

        &:focus {
          border: 2px solid $fabProBlue;
        }
      }
      & .react-datepicker-popper[data-placement^="bottom"] {
        margin: 0;
        top: 10px !important;
      }
    }
  }

  & div.packages-table {
    grid-column: 1/-1;

    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: 30px 1fr;
    row-gap: 15px;

    & > div:not(.ag-theme-balham-dark):not(.export-dropdown-wrapper) {
      grid-column: 1/2;
    }

    & > div > input.wizard-packages-table-search {
      height: 30px;
      font-size: 0.8rem;
      width: 200px;
    }

    & div.ag-theme-balham-dark.custom-ag-styles {
      height: calc(100vh - 300px);
      width: 100%;
      grid-row: 2/-1;
      grid-column: 1/-1;
    }

    & button.action-button {
      background-color: $red;
      border-color: $red;
      color: #fff;
    }

    div.due-date-cell-renderer {
      & div.react-datepicker-popper {
        left: calc(100vw - 580px) !important;
      }

      &.nav-expanded {
        & div.react-datepicker-popper {
          left: calc(100vw - 760px) !important;
        }
      }
    }
  }

  & > div.additional-package-info {
    grid-column: 1/-1;
    grid-template-columns: repeat(4, 1fr);

    background-color: $darkFab;
    border: 1px solid #000;
    border-radius: 3px;
    padding: 0 15px;

    & h3.additional-package-info-title {
      grid-column: 1/-1;
      color: $lighterSlate;
      font-size: 1rem;
    }

    & > label.wizard-content-label.cost-codes {
      position: relative;
      height: 30px;

      & > div.selectable-filter-wrapper > div.menu {
        top: 0;
      }
    }

    & div.google-map-container {
      grid-column: 1/3;
      height: 400px;
      width: 100%;
      position: relative;

      & > div > div {
        height: 400px !important;

        & > div {
          height: 100% !important;
        }
      }
    }

    & label.file-upload {
      align-self: start;
      grid-column: 3/-1;
      padding-top: 10px;

      & > div.file-upload-container {
        display: grid;
        grid-template-columns: 1fr repeat(2, 125px);
        column-gap: 15px;
      }

      & > div.file-upload-container > button {
        height: 30px;
        font-size: 1rem;
        padding: 0 10px;
        border: 1px solid #000;

        display: flex;
        align-items: center;
        justify-content: center;
        column-gap: 5px;
      }

      & > div.file-upload-container > button.upload {
        background-color: $fabProBlue;
        color: #fff;
      }

      & > div.file-upload-container > button.remove:disabled {
        color: #fff;
      }
    }
  }

  & > div.assignments {
    grid-column: 1/-1;
  }
}
