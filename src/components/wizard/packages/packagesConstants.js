// NPM PACKAGE IMPORTS
import "moment-timezone";

// REDUX IMPORTS
import store from "../../../redux/store";

// HELPER FUNCTION IMPORTS
import { naturalSort, generateTime } from "../../../_utils";

// FILE IMPORTS
import CSV_EXAMPLE from "../../../assets/PACKAGES_CSV_EXAMPLE.PNG";

export const packagesColumns = (
  deleteAction = (f) => f,
  flows = [],
  testStore = null
) => {
  const { systemSettings, permissions } = (
    testStore || store
  ).getState().profileData;
  let dateFormatting = systemSettings && systemSettings.date_display;

  return [
    {
      headerName: "",
      width: 40,
      maxWidth: 40,
      minWidth: 40,
      checkboxSelection: true,
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      pinned: "left",
      colId: "checkbox",
    },
    {
      headerName: "Pkg ID",
      field: "id",
      cellClass: "bold",
      sortable: true,
      filter: "agNumberColumnFilter",
      menuTabs: ["filterMenuTab"],
    },
    {
      headerName: "Package Name",
      field: "package_name",
      valueParser: (params) => {
        if (
          params.newValue === "" ||
          params.newValue === undefined ||
          (typeof params.newValue === "string" && !params.newValue.trim())
        )
          return params.oldValue;

        return params.newValue;
      },
      getQuickFilterText: (params) => params.data.package_name,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
      cellClass: "bold",
      width: 200,
      sortable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      autoHeight: true,
      editable: true,
      resizable: true,
      colId: "package_name",
    },
    {
      headerName: "Exclude from Float Mode?",
      field: "exclude_float_mode",
      valueFormatter: (params) => (params.value ? true : false),
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      cellRenderer: "toggleCellEditor",
      cellRendererParams: (params) => {
        return {
          rowHeight: params.node.rowHeight,
          even: params.node.childIndex % 2 === 0,
          value: params.value ? true : false,
          setValue: params.setValue,
        };
      },
      cellClass: "bold",
      sortable: true,
      autoHeight: true,
      editable: true,
      resizable: true,
      colId: "exclude_float_mode",
    },
    {
      headerName: "Flow",
      field: "work_flow_id",
      cellClass: "bold",
      valueFormatter: (params) => {
        return (flows.find((f) => f.id === params.value) || {}).display || "";
      },
      getQuickFilterText: (params) => params.colDef.valueFormatter(params),
      sortable: false,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      autoHeight: true,
      editable: true,
      resizable: true,
      cellEditor: "dropdownEditorRenderer",
      cellEditorParams: (params) => ({
        ...params,
        values: flows,
      }),
      colId: "work_flow_id",
    },
    {
      headerName: "Area",
      field: "area",
      valueParser: (params) => {
        if (
          params.newValue === "" ||
          params.newValue === undefined ||
          (typeof params.newValue === "string" && !params.newValue.trim())
        )
          return null;

        return params.newValue;
      },
      getQuickFilterText: (params) => params.data.area,
      cellClass: "bold",
      sortable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      autoHeight: true,
      editable: true,
      resizable: true,
      colId: "area",
    },
    {
      headerName: "Budget Hours",
      field: "budget_hours",
      valueFormatter: (params) => {
        const value = params.value
          ? `${parseFloat(params.value).toFixed(2)}`
          : "0.00";
        return value;
      },
      valueParser: (params) => {
        if (
          params.newValue === "" ||
          params.newValue === undefined ||
          (typeof params.newValue === "string" && !params.newValue.trim())
        )
          return params.oldValue;

        if (!/^\d{0,}\.?\d{1,}$/.test(params.newValue)) return params.oldValue;

        return parseFloat(params.newValue);
      },
      getQuickFilterText: (params) => params.colDef.valueFormatter(params),
      cellClass: "bold",
      sortable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      autoHeight: true,
      editable: true,
      resizable: true,
      colId: "budget_hours",
    },
    {
      headerName: "Due Date",
      field: "due_date_unix",
      valueFormatter: (params) => {
        return params.value
          ? typeof params.value === "number"
            ? generateTime(params.value * 1000, false, true, "-", testStore)
            : params.value
          : "-";
      },
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue;

        const defaultRegex = new RegExp("\\d{2}-\\d{2}-\\d{4}");
        const dateFormattingRegex = dateFormatting
          ? /-/.test(dateFormatting)
            ? new RegExp(
                dateFormatting
                  .split("-")
                  .map((part) => `\\d{${part.length}}`)
                  .join("\\-")
              )
            : /\//.test(dateFormatting)
            ? new RegExp(
                dateFormatting
                  .split("/")
                  .map((part) => `\\d{${part.length}}`)
                  .join("\\/")
              )
            : defaultRegex
          : defaultRegex;

        if (!dateFormattingRegex.test(params.newValue)) {
          return params.oldValue;
        } else return Math.floor(new Date(params.newValue).getTime() / 1000);
      },
      getQuickFilterText: (params) => params.colDef.valueFormatter(params),
      filter: "agDateColumnFilter",
      editable: true,
      cellEditor: "dueDateEditorRenderer",
      minWidth: 100,
      width: 200,
      filterParams: {
        buttons: ["reset"],
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = cellValue
            ? typeof cellValue === "number"
              ? new Date(
                  generateTime(cellValue * 1000, false, true, "-", testStore)
                )
              : new Date(cellValue)
            : "-";

          return cellDate < filterLocalDateAtMidnight
            ? -1
            : cellDate > filterLocalDateAtMidnight
            ? 1
            : 0;
        },
      },
      menuTabs: ["filterMenuTab"],
      colId: "due_date_unix",
    },
    {
      headerName: "Cost Codes",
      field: "cost_codes",
      getQuickFilterText: (params) => params.value,
      cellClass: "bold",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      autoHeight: true,
      resizable: true,
      colId: "cost_codes",
    },
    {
      headerName: "Action",
      valueGetter: (params) => null,
      valueFormatter: (params) => ({
        actions: [
          {
            action: () => deleteAction(params.data.id, params),
            actionName: "Delete",
          },
        ],
        isDisabled: !permissions?.includes(23) ? true : false,
      }),
      minWidth: 80,
      width: 100,
      sortable: false,
      suppressMenu: true,
      filter: false,
      cellRenderer: "actionCellRenderer",
      pinned: "right",
      suppressMovable: true,
      colId: "action",
    },
  ];
};

export const headers = {
  "Package Name": "package_name",
  Description: "description",
  "Budget Hours": "budget_hours",
  "Due Date Month": "due_date_month",
  "Due Date Day": "due_date_day",
  "Due Date Year": "due_date_year",
  Area: "area",
  "Cost Code": "cost_code",
  "Flow ID": "work_flow_id",
};

export const images = [CSV_EXAMPLE];
