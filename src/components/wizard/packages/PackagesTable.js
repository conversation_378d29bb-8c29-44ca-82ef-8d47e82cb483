// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useMemo } from "react";
import { useDispatch } from "react-redux";
import Input from "msuite_storybook/dist/input/Input";
import moment from "moment";
import "moment-timezone";

// REDUX IMPORTS
import {
  handleUpdatePackages,
  handleDeletePackage,
  handleUpdatePackageNoRefresh,
} from "../../packages/packagesActions";
import { handleChangeItemWorkflow } from "../../flows/flowsActions";

// COMPONENT IMPORTS
import AgTable from "../../reusable/agTable/AgTable";
import ToggleCellEditor from "../../reusable/frameworkComponents/ToggleCellEditor";
import ActionCellRenderer from "../../reusable/frameworkComponents/ActionCellRenderer";
import DropdownEditorRenderer from "../../reusable/frameworkComponents/DropdownEditorRenderer";
import DueDateEditorRenderer from "../../reusable/frameworkComponents/DueDateEditorRenderer";

// CONSTANTS IMPORTS
import { packagesColumns as columns } from "./packagesConstants";

const PackagesTable = ({
  packages,
  setSelectedPackage = (f) => f,
  gridOptionsApi,
  setGridOptionsApi,
  displayedFlows,
  packageCostCodes,
}) => {
  const [searchInput, setSearchInput] = useState("");

  const dispatch = useDispatch();

  const packagesWithCostCodes = useMemo(() => {
    if (packageCostCodes && packages) {
      return packages.map((p) => ({
        ...p,
        cost_codes: packageCostCodes
          .filter((cc) => cc.package_id === p.id)
          .map((cc) => cc.name)
          .join(", "),
      }));
    } else return [];
  }, [packages, packageCostCodes]);

  useEffect(() => {
    if (packagesWithCostCodes && gridOptionsApi) {
      gridOptionsApi.setRowData(packagesWithCostCodes);

      const packageQuery = window.location.search.match(/PackageID=\d+/);
      const packageId = packageQuery && parseInt(packageQuery[0].split("=")[1]);

      gridOptionsApi.forEachNode((node) => {
        if (node.data.id === packageId) node.setSelected(true);
      });
    }
  }, [packagesWithCostCodes, gridOptionsApi]);

  useEffect(() => {
    if (gridOptionsApi) {
      gridOptionsApi.setQuickFilter(searchInput);
    }
  }, [searchInput, gridOptionsApi]);

  const deleteAction = (pkgId, params) => {
    dispatch(handleDeletePackage(pkgId)).then((res) => {
      if (!res.error) {
        const rowNode = params.api.getRowNode(pkgId);
        if (rowNode) {
          params.api.applyTransaction({ remove: [rowNode] });
        }
        const selectedRows = params.api.getSelectedRows();

        // Somehow data was still in the selected rows, so we clear regardless?
        setSelectedPackage(null);
        dispatch(handleUpdatePackageNoRefresh(packages, pkgId));
        params.api.redrawRows();
      }
    });
  };

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);

    const sidebar = document.getElementsByClassName("ag-side-bar");
    if (sidebar && sidebar.length) {
      sidebar[0].addEventListener("click", () => params.api.stopEditing());
    }
  };

  const onSortChanged = (params) => {
    params.api.redrawRows();
  };

  const onSelectionChanged = (params) => {
    const rows = params.api.getSelectedRows();
    if (rows && rows.length) setSelectedPackage(rows[0]);
    else setSelectedPackage(null);
  };

  const onCellValueChanged = (params) => {
    if (params.colDef.field === "due_date_unix") {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const utcOffset =
        (moment.tz(timezone).utcOffset() -
          moment.tz("America/New_York").utcOffset()) *
        60;
      if (params.newValue + utcOffset === params.oldValue) return;
    }
    if (params.colDef.field === "work_flow_id") {
      dispatch(
        handleChangeItemWorkflow(params.data.id, "package", params.newValue)
      ).then((res) => {
        if (!res.error) {
          const rowNode = params.api.getRowNode(params.data.id);
          rowNode.setData({ ...rowNode.data, work_flow_id: params.newValue });
          dispatch(
            handleUpdatePackageNoRefresh(packages, {
              ...rowNode.data,
              work_flow_id: params.newValue,
            })
          );
        }
      });
    } else {
      dispatch(
        handleUpdatePackages([params.data.id], {
          [params.colDef.field === "due_date_unix"
            ? "due_date"
            : params.colDef.field]:
            params.colDef.field === "exclude_float_mode"
              ? params.newValue
                ? 1
                : 0
              : params.newValue,
        })
      ).then((res) => {
        if (!res.error) {
          const rowNode = params.api.getRowNode(params.data.id);
          rowNode.setData({ ...res[0], cost_codes: rowNode.data.cost_codes });
          dispatch(
            handleUpdatePackageNoRefresh(packages, {
              ...res[0],
              cost_codes: rowNode.data.cost_codes,
            })
          );
        }
      });
    }
  };

  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };

  const gridOptions = {
    rowData: packagesWithCostCodes,
    defaultColDef: {
      wrapText: true,
    },
    pagination: true,
    paginationPageSize: 100,
    columnDefs: columns(deleteAction, displayedFlows),
    frameworkComponents: {
      toggleCellEditor: ToggleCellEditor,
      dropdownEditorRenderer: DropdownEditorRenderer,
      dueDateEditorRenderer: DueDateEditorRenderer,
      actionCellRenderer: ActionCellRenderer,
    },
    onGridReady,
    onSortChanged,
    onSelectionChanged,
    onCellValueChanged,
    getRowNodeId: (data) => data.id,
    rowSelection: "single",
    suppressRowClickSelection: true,
    rowClassRules,
  };

  return (
    <div className="packages-table">
      <Input
        className="wizard-packages-table-search"
        value={searchInput}
        onChange={(e) => setSearchInput(e.target.value)}
        placeholder="Search Packages"
      />
      <AgTable gridOptions={gridOptions} />
    </div>
  );
};

export default PackagesTable;
