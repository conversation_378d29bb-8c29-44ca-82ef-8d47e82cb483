// NPM PACKAGES IMPORTS
import React, { useEffect, useState, useRef } from "react";
import Input from "msuite_storybook/dist/input/Input";
import GoogleMap from "msuite_storybook/dist/googleMap/GoogleMap";
import { useSelector, useDispatch } from "react-redux";
import Button from "msuite_storybook/dist/button/Button";
import { FaTrashAlt } from "react-icons/fa";
import { RiUploadCloudLine } from "react-icons/ri";

// REDUX IMPORTS
import {
  handleUpdatePackageMap,
  handleUpdatePackages,
  handleUpdatePackageNoRefresh,
} from "../../packages/packagesActions";
import {
  handleFetchPackageCostCodesSpecificPackage,
  handleUpdatePackageCostCodes,
} from "../wizardActions";

// COMPONENT IMPORTS
import Filter from "../../reusable/filter/Filter";

const PackagesAdditionalInfo = ({
  selectedPackage = {},
  setSelectedPackage,
  gridOptionsApi,
}) => {
  const [packageName, setPackageName] = useState("");
  const [packageNumber, setPackageNumber] = useState("");
  const [description, setDescription] = useState("");
  const [deliveryAddress, setDeliveryAddress] = useState({});
  const [selectedCostCodes, setSelectedCostCodes] = useState([]);
  const [costCodesSearchInput, setCostCodesSearchInput] = useState("");
  const [fileToUpload, setFileToUpload] = useState(null);

  const dispatch = useDispatch();
  const { jobCostCodes, packageCostCodesSpecificPackage } = useSelector(
    (state) => state.wizardData
  );
  const { packages } = useSelector((state) => state.packagesData);

  const additionalInfoRef = useRef(null);

  useEffect(() => {
    setPackageName(selectedPackage.package_name);
    setPackageNumber(selectedPackage.number || "");
    setDescription(selectedPackage.description || "");
    setDeliveryAddress({
      street: selectedPackage.job_site_address,
      city: selectedPackage.job_site_city,
      state: selectedPackage.job_site_state,
      zip: selectedPackage.job_site_zip,
      formatted_address: [
        selectedPackage.job_site_address || "",
        selectedPackage.job_site_city || "",
        selectedPackage.job_site_state || "",
        selectedPackage.job_site_zip || "",
      ]
        .filter((v) => v)
        .join(", "),
    });
  }, [selectedPackage]);

  useEffect(() => {
    if (additionalInfoRef.current) additionalInfoRef.current.scrollIntoView();
    dispatch(handleFetchPackageCostCodesSpecificPackage(selectedPackage.id));
  }, [selectedPackage.id]);

  useEffect(() => {
    if (packageCostCodesSpecificPackage && jobCostCodes) {
      const selectedCostCodeIds = packageCostCodesSpecificPackage
        .filter((cc) => cc.package_id)
        .map((cc) => cc.id);

      const jobCostCodesToSelect = jobCostCodes.filter((jcc) =>
        selectedCostCodeIds.includes(jcc.cost_code_id)
      );

      setSelectedCostCodes(jobCostCodesToSelect);
    }
  }, [packageCostCodesSpecificPackage, jobCostCodes]);

  const updateAddress = ({ key, value }) => {
    setDeliveryAddress({ ...deliveryAddress, [key]: value });
  };

  const saveUpdatedInfo = (field, newValue) => {
    if (field === "package_name" && !newValue.trim()) return;
    if (selectedPackage[field] === newValue.trim()) return;
    if (selectedPackage[`job_site_${field}`] === newValue.trim()) return;

    dispatch(
      handleUpdatePackages([selectedPackage.id], { [field]: newValue.trim() })
    ).then((res) => {
      if (!res.error) {
        if (gridOptionsApi) {
          const rowNode = gridOptionsApi.getRowNode(selectedPackage.id);
          if (rowNode) rowNode.setData(res[0]);
        }

        // update the in state memory of the record without refetching ALL packages
        dispatch(handleUpdatePackageNoRefresh(packages, res[0]));
        setSelectedPackage(res[0]);
      }
    });
  };

  const saveFullUpdatedAddress = (updatedAddress) => {
    dispatch(handleUpdatePackages([selectedPackage.id], updatedAddress)).then(
      (res) => {
        if (!res.error) {
          if (gridOptionsApi) {
            const rowNode = gridOptionsApi.getRowNode(selectedPackage.id);
            if (rowNode) rowNode.setData(res[0]);
          }

          // update the in state memory of the record without refetching ALL packages
          dispatch(handleUpdatePackageNoRefresh(packages, res[0]));
          setSelectedPackage(res[0]);
        }
      }
    );
  };

  const updateSelectedCostCodes = async (newSelected) => {
    const costCodesToAdd = newSelected.filter(
      (cc) => !selectedCostCodes.find((scc) => scc.id === cc.id)
    );
    const costCodesToRemove = selectedCostCodes.filter(
      (scc) => !newSelected.find((cc) => cc.id === scc.id)
    );

    if (costCodesToAdd.length) {
      await dispatch(
        handleUpdatePackageCostCodes(
          selectedPackage.id,
          "add",
          costCodesToAdd.map((cc) => cc.cost_code_id)
        )
      );
    }

    if (costCodesToRemove.length) {
      await dispatch(
        handleUpdatePackageCostCodes(
          selectedPackage.id,
          "remove",
          costCodesToRemove.map((cc) => cc.cost_code_id)
        )
      );
    }

    if (gridOptionsApi) {
      const rowNode = gridOptionsApi.getRowNode(selectedPackage.id);
      if (rowNode) {
        const updatedPackage = {
          ...rowNode.data,
          cost_codes: newSelected.map((s) => s.name).join(", "),
        };
        rowNode.setData(updatedPackage);
        // update the in state memory of the record without refetching ALL packages
        dispatch(handleUpdatePackageNoRefresh(packages, updatedPackage));
      }
    }

    dispatch(handleFetchPackageCostCodesSpecificPackage(selectedPackage.id));
    setSelectedCostCodes(newSelected);
  };

  const uploadPackageMap = () => {
    if (!fileToUpload) return;

    dispatch(
      handleUpdatePackageMap(
        selectedPackage.id,
        selectedPackage.job_id,
        fileToUpload.file
      )
    );
  };

  const removeFileToUpload = () => {
    setFileToUpload(null);
  };

  return (
    <div className="additional-package-info" ref={additionalInfoRef}>
      <h3 className="additional-package-info-title">
        Additional Package Information
      </h3>
      <label className="wizard-content-label">
        <span>Package Name</span>
        <Input
          value={packageName}
          onChange={(e) => setPackageName(e.target.value)}
          placeholder="Package Name"
          required
          onBlur={(e) => saveUpdatedInfo("package_name", e.target.value)}
        />
      </label>
      <label className="wizard-content-label">
        <span>Package Number</span>
        <Input
          value={packageNumber}
          onChange={(e) => setPackageNumber(e.target.value)}
          placeholder="Package Number"
          onBlur={(e) => saveUpdatedInfo("number", e.target.value)}
        />
      </label>
      <label className="wizard-content-label">
        <span style={{ fontSize: "0.7rem" }}>
          Special Delivery Instructions (Description)
        </span>
        <Input
          as="textarea"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Special Delivery Instructions"
          onBlur={(e) => saveUpdatedInfo("description", e.target.value)}
        />
      </label>
      <label className="wizard-content-label cost-codes">
        <Filter
          nameKey="name"
          idKey="id"
          type="Cost Codes"
          list={jobCostCodes}
          setSelected={updateSelectedCostCodes}
          handleParentSelect={updateSelectedCostCodes}
          selected={selectedCostCodes}
          selectAll={true}
          orientation="HORIZONTAL"
          smallView={true}
          searchInput={costCodesSearchInput}
          setSearchInput={setCostCodesSearchInput}
        />
      </label>
      <label className="wizard-content-label">
        <span>Delivery Address</span>
        <Input
          placeholder="Delivery Address"
          value={deliveryAddress.street || ""}
          onChange={(e) =>
            updateAddress({ key: "street", value: e.target.value })
          }
          onBlur={(e) => saveUpdatedInfo("address", e.target.value)}
        />
      </label>
      <label className="wizard-content-label">
        <span>Delivery City</span>
        <Input
          placeholder="Delivery City"
          value={deliveryAddress.city || ""}
          onChange={(e) =>
            updateAddress({ key: "city", value: e.target.value })
          }
          onBlur={(e) => saveUpdatedInfo("city", e.target.value)}
        />
      </label>
      <label className="wizard-content-label">
        <span>Delivery State</span>
        <Input
          placeholder="Delivery State"
          value={deliveryAddress.state || ""}
          onChange={(e) =>
            updateAddress({ key: "state", value: e.target.value })
          }
          onBlur={(e) => saveUpdatedInfo("state", e.target.value)}
        />
      </label>
      <label className="wizard-content-label">
        <span>Delivery Zip</span>
        <Input
          placeholder="Delivery Zip"
          value={deliveryAddress.zip || ""}
          onChange={(e) => updateAddress({ key: "zip", value: e.target.value })}
          onBlur={(e) => saveUpdatedInfo("zip", e.target.value)}
        />
      </label>
      <div className="google-map-container">
        <GoogleMap
          address={
            (deliveryAddress && deliveryAddress.formatted_address) ||
            "311 3rd Ave SE Suite 450, Cedar Rapids, IA 52401"
          }
          draggableMarker
          onMarkerDragEnd={({ address }) => {
            const updatedAddress = {
              city: address.city || "",
              state: address.state || "",
              address:
                (address.number ? address.number + " " : "") +
                (address.street || ""),
              zip: address.zip || "",
            };
            saveFullUpdatedAddress(updatedAddress);
            setDeliveryAddress({
              ...updatedAddress,
              formatted_address: [
                updatedAddress.address,
                updatedAddress.city,
                updatedAddress.state,
                updatedAddress.zip,
              ]
                .filter((v) => v)
                .join(", "),
            });
          }}
        />
      </div>
      <label className="wizard-content-label file-upload">
        <span>Upload Package Map</span>
        <div className="file-upload-container">
          <Input
            type="file"
            placeholder="No File Selected"
            className="file-upload"
            accept=".pdf,.jpg,.jpeg,.png,application/pdf,image/jpeg,image/png"
            value={fileToUpload ? fileToUpload.fakepath : ""}
            onChange={(e) => {
              e.persist();
              setFileToUpload({
                fakepath: e.target.value,
                file: e.target.files[0],
              });
            }}
          />
          <Button
            className="upload"
            disabled={!fileToUpload}
            onClick={uploadPackageMap}
          >
            <RiUploadCloudLine /> Upload
          </Button>
          <Button
            className="remove"
            disabled={!fileToUpload}
            onClick={removeFileToUpload}
          >
            <FaTrashAlt /> Remove
          </Button>
        </div>
      </label>
    </div>
  );
};

export default PackagesAdditionalInfo;
