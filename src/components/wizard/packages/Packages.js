// NPM PACKAGE IMPORTS
import React, { useState, useMemo, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";

// REDUX IMPORTS
import { handleFetchPackageCostCodes } from "../wizardActions";

// COMPONENT IMPORTS
import { default as Inputs } from "./PackagesInputs";
import { default as Table } from "./PackagesTable";
import { default as AdditionalInformation } from "./PackagesAdditionalInfo";
import { default as Assignments } from "../../reusable/assignments/Assignments";

// HELPER FUNCTION IMPORTS
import { naturalSort } from "../../../_utils";

// STYLE IMPORTS
import "./stylesPackages.scss";
import ManageCrews from "../../reusable/manageCrews/ManageCrews";

const Packages = ({ selectedJob }) => {
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [showManageCrewsModal, toggleManageCrewsModal] = useState(false);

  const dispatch = useDispatch();
  const { systemSettings } = useSelector((state) => state.profileData);
  const { packages } = useSelector((state) => state.packagesData);
  const { flows } = useSelector((state) => state.wizardData);
  const { packageCostCodes } = useSelector((state) => state.wizardData);

  useEffect(() => {
    if (packages && packages.length) {
      dispatch(handleFetchPackageCostCodes(packages.map((p) => p.id)));
    }
  }, [packages]);

  const displayedPackages = useMemo(() => {
    if (packages) return packages.sort((a, b) => a.id - b.id);
    else return [];
  }, [packages]);

  const displayedFlows = useMemo(() => {
    return (flows || [])
      .map((f) => ({ id: f.id, value: f.id, display: f.name }))
      .sort((a, b) => naturalSort(a.display, b.display));
  }, [flows]);

  return (
    <div className="packages">
      <Inputs
        gridOptionsApi={gridOptionsApi}
        selectedJob={selectedJob}
        setSelectedPackage={setSelectedPackage}
        displayedFlows={displayedFlows}
      />
      {systemSettings && flows && packageCostCodes && (
        <Table
          packages={displayedPackages}
          setSelectedPackage={setSelectedPackage}
          gridOptionsApi={gridOptionsApi}
          setGridOptionsApi={setGridOptionsApi}
          displayedFlows={displayedFlows}
          packageCostCodes={packageCostCodes}
        />
      )}
      {selectedPackage && (
        <>
          <AdditionalInformation
            selectedPackage={selectedPackage}
            setSelectedPackage={setSelectedPackage}
            gridOptionsApi={gridOptionsApi}
          />
          <Assignments
            selectedItem={selectedPackage}
            currentTable="PACKAGES"
            openManageCrews={() => toggleManageCrewsModal(true)}
          />
        </>
      )}
      {showManageCrewsModal && (
        <ManageCrews
          open={showManageCrewsModal}
          handleClose={() => toggleManageCrewsModal(false)}
        />
      )}
    </div>
  );
};

export default Packages;
