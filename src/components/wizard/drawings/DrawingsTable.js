// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "msuite_storybook/dist/modal/Modal";
import Input from "msuite_storybook/dist/input/Input";
import Button from "msuite_storybook/dist/button/Button";
import { FaArrowRight } from "react-icons/fa";
import styled from "styled-components";

// REDUX IMPORTS
import {
  handleFetchDrawings,
  handleUpdateDrawings,
  handleDuplicateDrawing,
} from "../../drawings/drawingsActions";
import { handleFetchPackages } from "../../packages/packagesActions";
import {
  handleFetchPackageFile,
  handleFetchDrawingFiles,
} from "../../files/filesActions";

// COMPONENT IMPORTS
import AgTable from "../../reusable/agTable/AgTable";
import TextInputCellRenderer from "../../reusable/frameworkComponents/TextInputCellRenderer";
import DropdownEditorRenderer from "../../reusable/frameworkComponents/DropdownEditorRenderer";
import ActionCellRenderer from "../../reusable/frameworkComponents/ActionCellRenderer";
import PDFModal from "../../reusable/pdfModal/PDFModal";

// CONSTANTS IMPORTS
import { drawingsColumns as columns } from "./drawingsConstants";

const DrawingsTableDiv = styled.div`
  & div.ag-theme-balham-dark.custom-ag-styles {
    margin-bottom: 60px;
    height: calc(100vh - 400px);
  }
`;

const DrawingsTable = ({
  drawings,
  packageCostCodes,
  selectedJob,
  setSelectedRows,
  setDrawingsGridOptionsApi,
}) => {
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [showPdfModal, togglePdfModal] = useState(false);
  const [selectedDrawing, setSelectedDrawing] = useState(null);
  const [file, setFile] = useState(null);
  const [drawingToDuplicate, setDrawingToDuplicate] = useState(null);
  const [duplicationQty, setDuplicationQty] = useState(1);
  const [searchInput, setSearchInput] = useState("");

  const { packageFiles, drawingFiles } = useSelector(
    (state) => state.filesData
  );

  const dispatch = useDispatch();

  const packagesCostCodesRef = useRef(packageCostCodes);

  useEffect(() => {
    if (gridOptionsApi) {
      gridOptionsApi.setRowData(drawings);
    }
  }, [drawings]);

  const duplicateDrawing = () => {
    dispatch(handleDuplicateDrawing(drawingToDuplicate, duplicationQty)).then(
      (res) => {
        if (!res.error) {
          setDrawingToDuplicate(null);
          setDuplicationQty(1);
          dispatch(handleFetchPackages([selectedJob], true));
        }
      }
    );
  };

  const refreshPdfModal = () => {
    dispatch(handleFetchDrawings(selectedJob)).then((res) => {
      if (!res.error) {
        const drawing = res.find((d) => d.id === selectedDrawing.id);
        if (drawing) {
          setSelectedDrawing(drawing);
          setFile(drawingFiles?.[drawing.id]?.original);
          const rowNode = gridOptionsApi.getRowNode(drawing.id);
          rowNode.setData(drawing);
          gridOptionsApi.redrawRows({ rowNodes: [rowNode] });
        }
      }
    });
  };

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
    setDrawingsGridOptionsApi(params.api);
  };

  const onSortChanged = (params) => {
    params.api.redrawRows();
  };

  const onSelectionChanged = (params) => {
    let rows = params.api.getSelectedRows();
    return setSelectedRows(rows);
  };

  const onCellValueChanged = (params) => {
    let property, value, callAction;
    if (params.colDef.field === "cost_code_name") {
      property = "cost_code";
      callAction = "cost_code";

      if ([null, undefined, ""].includes(params.newValue)) {
        value = null;
      } else {
        const updatedCostCode = packagesCostCodesRef.current.find(
          (cc) => cc.name === params.newValue
        );
        value = updatedCostCode?.id;
      }
    } else if (params.colDef.field === "name") {
      property = "original_name";
      callAction = "update";

      if ([null, undefined, ""].includes(params.newValue)) return;
      else value = params.newValue;
    }

    if (value !== null || params.colDef.field === "cost_code_name") {
      dispatch(
        handleUpdateDrawings(params.data.id, { [property]: value }, callAction)
      ).then((res) => {
        if (!res.error) {
          params.node.setData(res[0]);
        }
      });
    }
  };

  // TODO - Consolidate to a reusable function, duplicated code!
  // common code for setting the package map as the file
  const getPackagePDFs = (packageId, updateFile = false) => {
    if (!packageId) return;
    const files = packageFiles;
    if (!files?.hasOwnProperty(packageId)) {
      dispatch(handleFetchPackageFile(packageId)).then((res) => {
        if (res?.[packageId] && updateFile) {
          setFile(res[packageId]);
        }
      });
    } else if (updateFile) {
      // update the file if it already exists and we want to update...
      setFile(files[packageId]);
    }
    // else we exit because we already know the drawing package info
  };

  // TODO - Consolidate to a reusable function, duplicated code!
  const getDrawingPDFs = (id, rowData) => {
    if (!id) return;
    // if the file has no files to fetch, return (kept separate for logic simplicity)
    if (!(rowData.has_original ?? 0) && !(rowData.has_package_map ?? 0)) return;
    const files = drawingFiles?.current;
    if (files?.hasOwnProperty(id)) {
      // if drawing doesn't have original check for map
      if (files?.[id]?.original) {
        setFile(files[id].original);
      }
      // load package PDF and set if original was missing
      if (rowData.has_package_map) {
        getPackagePDFs(rowData.package_id, !files?.[id]?.original);
      }
    } else if (rowData?.has_original) {
      dispatch(handleFetchDrawingFiles(id)).then((res) => {
        // if drawing doesn't have original check for map
        if (res?.[id]?.original) {
          setFile(res[id].original);
        }
        // load package PDF and set if original was missing
        if (rowData.has_package_map) {
          getPackagePDFs(rowData.package_id, !res?.[id]?.original);
        }
      });
    } else if (rowData?.has_package_map) {
      // load package PDF and set
      getPackagePDFs(rowData.package_id, true);
    } // else do nothing... nothing to load
  };

  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };
  const gridOptions = {
    rowData: drawings,
    defaultColDef: {
      wrapText: true,
    },
    columnDefs: columns(
      (drawing) => {
        // only try to fetch if files are present...
        getDrawingPDFs(drawing.id, drawing);
        setSelectedDrawing(drawing);
        togglePdfModal(true);
      },
      (drawingId) => setDrawingToDuplicate(drawingId),
      packageCostCodes
    ),
    pagination: true,
    paginationPageSize: 100,
    editType: "fullRow",
    frameworkComponents: {
      textInputCellRenderer: TextInputCellRenderer,
      dropdownEditorRenderer: DropdownEditorRenderer,
      actionCellRenderer: ActionCellRenderer,
    },
    getRowNodeId: (data) => data.id,
    onGridReady,
    onSortChanged,
    onCellValueChanged,
    onSelectionChanged,
    rowSelection: "multiple",
    suppressRowClickSelection: true,
    stopEditingWhenGridLosesFocus: true,
    rowClassRules,
  };

  useEffect(() => {
    if (!gridOptionsApi) return;

    gridOptionsApi.setQuickFilter(searchInput);
    gridOptionsApi.redrawRows();
  }, [searchInput, gridOptionsApi]);

  return (
    <DrawingsTableDiv className="drawings-table" drawings={drawings}>
      <input
        placeholder="Search drawings"
        type="text"
        onChange={(e) => setSearchInput(e.target.value)}
        value={searchInput}
        className="table-search"
      />
      <AgTable gridOptions={gridOptions} />
      {showPdfModal && (
        <PDFModal
          pdfViewer={showPdfModal}
          togglePDFViewer={() => togglePdfModal(false)}
          selectedItem={selectedDrawing}
          itemId={selectedDrawing.id}
          itemType="DRAWING"
          file={file}
          setFile={setFile}
          refresh={refreshPdfModal}
          setSelectedItem={setSelectedDrawing}
        />
      )}
      {!!drawingToDuplicate && (
        <Modal
          open={!!drawingToDuplicate}
          handleClose={() => {
            setDrawingToDuplicate(null);
            setDuplicationQty(1);
          }}
        >
          <div className="duplicate-drawing-container">
            <h2 className="title">Duplicate Drawing</h2>
            <div className="content">
              <label className="duplication-quantity">
                Quantity:
                <Input
                  type="number"
                  min={1}
                  onChange={(e) => {
                    e.persist();
                    setDuplicationQty(parseInt(e.target.value));
                  }}
                  value={duplicationQty}
                  required
                />
              </label>
            </div>
            <div className="buttons">
              <Button
                className="cancel"
                onClick={() => {
                  setDrawingToDuplicate(null);
                  setDuplicationQty(1);
                }}
              >
                Cancel
              </Button>
              <Button
                className="submit"
                onClick={duplicateDrawing}
                disabled={!drawingToDuplicate || !duplicationQty}
              >
                Submit <FaArrowRight />
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </DrawingsTableDiv>
  );
};

export default DrawingsTable;
