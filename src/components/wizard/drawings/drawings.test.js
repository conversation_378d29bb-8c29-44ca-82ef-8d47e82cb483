import { drawingsColumns } from "./drawingsConstants";

describe("Drawings Column Defs", () => {
  const defaultHeaders = [
    "",
    "Job #",
    "Package Name",
    "Drawing Name",
    "Rev #",
    "Drawing Status",
    "Cost Code",
    "Action",
  ];
  const costCodes = [{ id: 1, name: "one", package_id: 1 }];
  const viewAction = jest.fn();
  const duplicateAction = jest.fn();

  let populatedColumns;

  beforeEach(() => {
    populatedColumns = drawingsColumns(viewAction, duplicateAction, costCodes);
  });

  it("Drawings Headers are correct", () => {
    let columnHeaders = populatedColumns.map((c) => c.headerName);
    expect(columnHeaders).toEqual(defaultHeaders);
  });

  describe("DRAWING NAME", () => {
    let column;
    beforeEach(() => {
      column = populatedColumns.find((c) => c.headerName === "Drawing Name");
    });
    it("Value Parser", () => {
      const newValueParams = {
        oldValue: "oldValue",
        newValue: "newValue",
        data: {
          name: "test",
          id: 1,
        },
      };
      const oldValueParams = {
        oldValue: "oldValue",
        newValue: "",
        data: {
          name: "test",
          id: 1,
        },
      };
      expect(column.valueParser(oldValueParams)).toEqual("oldValue");
      expect(column.valueParser(newValueParams)).toEqual("newValue");
    });
  });

  describe("COST CODE", () => {
    let column;
    beforeEach(() => {
      column = populatedColumns.find((c) => c.headerName === "Cost Code");
    });
    it("valueParser", () => {
      const newValueParams = {
        oldValue: "oldValue",
        newValue: "newValue",
        data: {
          name: "test",
          id: 1,
        },
      };
      const oldValueParams = {
        oldValue: "oldValue",
        newValue: "",
        data: {
          name: "test",
          id: 1,
        },
      };
      expect(column.valueParser(oldValueParams)).toEqual(null);
      expect(column.valueParser(newValueParams)).toEqual("newValue");
    });
    it("cellEditorParams ", () => {
      const paramsOne = {
          data: {
            package_id: 1,
          },
        },
        paramsTwo = {
          data: {
            package_id: 2,
          },
        };
      expect(column.cellEditorParams(paramsOne)).toEqual({
        values: costCodes,
      });
      expect(column.cellEditorParams(paramsTwo)).toEqual({
        values: [],
      });
    });
  });

  describe("ACTION", () => {
    let column;
    beforeEach(() => {
      column = populatedColumns.find((c) => c.headerName === "Action");
    });
    const params = {
      data: {
        id: 1,
      },
    };
    it("valueGetter", () => {
      expect(column.valueGetter(params)).toEqual(null);
    });
    it("valueFormatter", () => {
      expect(JSON.stringify(column.valueFormatter(params))).toEqual(
        JSON.stringify({
          actions: [
            {
              action: () => viewAction(params.data),
              actionName: "View",
            },
            {
              action: () => duplicateAction(params.data.id),
              actionName: "Duplicate",
            },
          ],
        })
      );
    });
  });
});
