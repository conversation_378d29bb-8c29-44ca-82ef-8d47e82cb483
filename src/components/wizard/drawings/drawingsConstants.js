// REDUX IMPORTS
import store from "../../../redux/store";
import { notify } from "../../reusable/alertPopup/alertPopupActions";

// HELPER FUNCTION IMPORTS
import { naturalSort } from "../../../_utils";

export const drawingsColumns = (
  viewAction = (f) => f,
  duplicateAction = (f) => f,
  costCodes = []
) => [
  {
    headerName: "",
    headerCheckboxSelection: true,
    headerCheckboxSelectionFilteredOnly: true,
    width: 40,
    maxWidth: 40,
    minWidth: 40,
    checkboxSelection: true,
    suppressMenu: true,
    suppressColumnsToolPanel: true,
    pinned: "left",
    colId: "checkbox",
  },
  {
    headerName: "Job #",
    field: "job_number",
    cellClass: "bold",
    sortable: true,
    filter: "agTextColumnFilter",
    menuTabs: ["filterMenuTab"],
    colId: "job_number",
  },
  {
    headerName: "Package Name",
    field: "package_name",
    cellClass: "bold",
    sortable: true,
    filter: "agTextColumnFilter",
    menuTabs: ["filterMenuTab"],
    autoHeight: true,
    colId: "package_name",
  },
  {
    headerName: "Drawing Name",
    field: "name",
    valueFormatter: (params) => params,
    cellClass: "bold",
    sortable: true,
    filter: "agTextColumnFilter",
    menuTabs: ["filterMenuTab"],
    cellRenderer: "textInputCellRenderer",
    autoHeight: true,
    editable: true,
    valueParser: (params) => {
      if (
        (params.newValue ? params.newValue.trim() : "") === "" ||
        params.newValue === undefined ||
        params.newValue === null
      ) {
        store.dispatch(
          notify({
            id: Date.now(),
            type: "ERROR",
            message: "Drawing name cannot be empty",
          })
        );
        return params.oldValue;
      }

      if (/[^a-z0-9\.\-\_ ]/gi.test(params.newValue)) {
        store.dispatch(
          notify({
            id: Date.now(),
            type: "ERROR",
            message:
              "Drawing name can only contain the following characters (a-z A-Z 0-9 . - _)",
          })
        );
        return params.oldValue;
      }

      return params.newValue;
    },
    colId: "drawing_name",
  },
  {
    headerName: "Rev #",
    field: "revision_number",
    cellClass: "bold",
    sortable: true,
    filter: "agTextColumnFilter",
    menuTabs: ["filterMenuTab"],
    colId: "revision_number",
  },
  {
    headerName: "Drawing Status",
    field: "drawing_status",
    cellClass: "bold",
    sortable: true,
    filter: "agTextColumnFilter",
    menuTabs: ["filterMenuTab"],
    autoHeight: true,
    colId: "drawing_status",
  },
  {
    headerName: "Cost Code",
    field: "cost_code_name",
    valueParser: (params) => {
      if (params.newValue === "" || params.newValue === undefined) return null;

      // dropdownCellRenderer is returning id so need to map to it's name
      if (typeof params.newValue === "number") {
        const updatedCostCode = costCodes.find(
          (cc) => cc.id === params.newValue
        );
        return updatedCostCode?.name;
      }
      return params.newValue;
    },
    cellClass: "bold",
    filter: "agTextColumnFilter",
    menuTabs: ["filterMenuTab"],
    cellEditor: "dropdownEditorRenderer",
    cellEditorParams: (params) => ({
      values: costCodes.filter(
        (cc) => cc.package_id === params.data.package_id
      ),
      value: params.data.cost_code_id,
    }),
    editable: true,
    comparator: (valueA, valueB, nodeA, nodeB) =>
      valueA
        ? valueB
          ? naturalSort(nodeA.data.cost_code_name, nodeB.data.cost_code_name)
          : -1
        : 1,
    colId: "cost_code_id",
  },
  {
    headerName: "Action",
    valueGetter: (params) => null,
    valueFormatter: (params) => ({
      actions: [
        {
          action: () => viewAction(params.data),
          actionName: "View",
        },
        {
          action: () => duplicateAction(params.data.id),
          actionName: "Duplicate",
        },
      ],
    }),
    sortable: false,
    suppressMenu: true,
    filter: false,
    cellRenderer: "actionCellRenderer",
    pinned: "right",
    suppressMovable: true,
    colId: "action",
  },
];
