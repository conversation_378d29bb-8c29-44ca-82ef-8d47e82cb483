// NPM PACKAGE IMPORTS
import React from "react";
import { useSelector } from "react-redux";

// COMPONENT IMPORTS
import { default as Table } from "./DrawingsTable";

// STYLE IMPORTS
import "./stylesDrawings.scss";

const Drawings = ({
  selectedJob,
  setSelectedRows,
  setDrawingsGridOptionsApi,
}) => {
  const { drawings, packageCostCodes } = useSelector(
    (state) => state.wizardData
  );

  return (
    <div className="drawings">
      {drawings && packageCostCodes && (
        <Table
          drawings={drawings}
          packageCostCodes={packageCostCodes}
          selectedJob={selectedJob}
          setSelectedRows={setSelectedRows}
          setDrawingsGridOptionsApi={setDrawingsGridOptionsApi}
        />
      )}
    </div>
  );
};

export default Drawings;
