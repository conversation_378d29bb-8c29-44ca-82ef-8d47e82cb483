export const steps = [
  { id: 1, name: "Job Info" },
  { id: 2, name: "Cost Codes" },
  { id: 3, name: "Packages" },
  { id: 4, name: "Drawings" },
  { id: 5, name: "Items" },
];

export const costCodesColumns = (toggleConfirmationModal, costCodeTypes) => {
  return [
    {
      headerName: "Code",
      field: "name",
      valueGetter: (params) => params.data.name,
      valueParser: (params) => {
        if (params.oldValue === "" || params.newValue === undefined)
          return params.oldValue;

        return params.newValue;
      },
      getQuickFilterText: (params) => params.data.name,
      cellClass: "bold",
      sortable: true,
      filter: "agTextColumnFilter",
      menuTabs: ["filterMenuTab"],
      editable: true,
      colId: "name",
    },
    {
      headerName: "Area",
      field: "area",
      valueGetter: (params) =>
        params.data.general_code ? "" : params.data.area,
      valueParser: (params) => {
        if (params.oldValue === "" || params.newValue === undefined)
          return params.oldValue;

        return params.newValue;
      },
      getQuickFilterText: (params) => params.data.area,
      cellClass: "bold",
      sortable: true,
      filter: "agTextColumnFilter",
      menuTabs: ["filterMenuTab"],
      autoHeight: true,
      editable: (params) => (params.data.general_code ? false : true),
      colId: "area",
    },
    {
      headerName: "Description",
      field: "description",
      valueGetter: (params) => params.data.description,
      valueParser: (params) => {
        if (params.oldValue === "" || params.newValue === undefined)
          return params.oldValue;

        return params.newValue;
      },
      getQuickFilterText: (params) => params.data.description,
      cellClass: "bold",
      sortable: true,
      filter: "agTextColumnFilter",
      menuTabs: ["filterMenuTab"],
      autoHeight: true,
      editable: true,
      colId: "description",
    },
    {
      headerName: "Budget Hours",
      field: "budget_hours",
      valueGetter: (params) =>
        params.data.general_code ? "" : params.data.budget_hours,
      valueParser: (params) => {
        if (params.oldValue === "" || params.newValue === undefined)
          return params.oldValue;

        return params.newValue;
      },
      getQuickFilterText: (params) => params.data.budget_hours,
      cellClass: "bold",
      sortable: true,
      filter: "agTextColumnFilter",
      menuTabs: ["filterMenuTab"],
      editable: (params) => (params.data.general_code ? false : true),
      colId: "budget_hours",
    },
    {
      headerName: "Budgeted Cost",
      field: "budgeted_cost",
      valueGetter: (params) =>
        params.data.general_code ? "" : params.data.budgeted_cost,
      valueParser: (params) => {
        if (params.oldValue === "" || params.newValue === undefined)
          return params.oldValue;

        return params.newValue;
      },
      getQuickFilterText: (params) => params.data.budgeted_cost,
      cellClass: "bold",
      sortable: true,
      filter: "agTextColumnFilter",
      menuTabs: ["filterMenuTab"],
      editable: (params) => (params.data.general_code ? false : true),
      colId: "budgeted_cost",
    },
    {
      headerName: "Type",
      field: "type_name",
      valueGetter: (params) =>
        params.data.general_code ? "" : params.data.type_name,
      valueParser: (params) => {
        if (params.oldValue === "" || params.newValue === undefined)
          return params.oldValue;

        return params.newValue;
      },
      getQuickFilterText: (params) => params.data.type,
      sortable: true,
      filter: false,
      suppressMenu: true,
      editable: (params) => (params.data.general_code ? false : true),
      cellEditor: "agSelectCellEditor",
      cellEditorParams: () => ({
        values: costCodeTypes ? costCodeTypes.map((c) => c.name) : [],
      }),
      colId: "type",
    },
    {
      headerName: "Keep as General?",
      field: "general_code",
      valueGetter: (params) => (params.data.general_code ? true : false),
      cellRendererParams: (params) => {
        return {
          rowHeight: params.node.rowHeight,
          even: params.node.childIndex % 2 === 0,
          value: params.value ? true : false,
          setValue: params.setValue,
        };
      },
      sortable: true,
      filter: false,
      cellRenderer: "keepAsGeneralCellRenderer",
      pinned: "right",
      suppressMovable: true,
      suppressMenu: true,
      colId: "general_code",
    },
    {
      headerName: "Action",
      valueGetter: (params) => null,
      valueFormatter: (params) => ({
        actions: [
          {
            action: () => toggleConfirmationModal(params.data.name),
            actionName: "Remove",
          },
        ],
      }),
      sortable: false,
      suppressMenu: true,
      filter: false,
      cellRenderer: "actionCellRenderer",
      pinned: "right",
      suppressMovable: true,
      colId: "remove",
    },
  ];
};
