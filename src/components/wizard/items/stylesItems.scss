@import "../../styles/colors.scss";

div.items {
  & div.options-wrapper {
    display: flex;
    justify-content: space-between;
    width: 45%;
    margin: 0 auto;
    min-width: 650px;

    & div.main {
      display: flex;
      justify-content: space-between;
      width: 550px;
      margin: 0 auto 10px;
      height: 40px;

      & select {
        height: 40px;
        font-size: 1rem;
        width: 280px;

        & option {
          padding: 8px;
        }
      }
    }

    & button.options-button {
      background-color: $fabProBlue;
      color: #fff;
      height: 40px;
      padding: 8px 10px;
      font-size: 1rem;

      &:not(:disabled):hover {
        background-color: darken($fabProBlue, 10%);
      }
    }
    & button.new-item-submit {
      position: relative;
      background-color: $green;
      color: white;
      height: 40px;
      padding: 8px 10px;
      font-size: 1rem;
    }
  }

  & .custom-ag-styles.ag-theme-balham-dark {
    height: calc(100vh - 60px - 32px - 155px - 70px - 50px - 60px);
    width: 100%;

    & div.ag-cell {
      background-color: transparent;
      & div.ag-cell-edit-wrapper {
        height: 30px;
      }
    }

    & .ag-cell-inline-editing {
      border: none !important;
    }
  }
  & .custom-ag-styles {
    background-color: transparent;
  }

  & div.overlay {
    display: none;
  }
}

div.items {
  & > button.upload-button {
    grid-column: 3/4;

    background-color: $fabProBlue;
    color: #fff;
    height: 40px;
    padding: 0;
    font-size: 1rem;

    &:not(:disabled):hover {
      background-color: darken($fabProBlue, 10%);
    }
  }

  & > div.csv-downloads {
    grid-row: 2/3;
    grid-column: 2/4;

    display: flex;
    flex-direction: column;
    row-gap: 15px;

    & > a.csv-download {
      color: $lighterSlate;

      &:hover {
        color: #fff;
      }

      &.material-types {
        grid-column: 2/3;
      }
    }
  }

  & > section.csv-modal form.csv-modal-content div.csv-results {
    max-height: 300px;
  }
}
