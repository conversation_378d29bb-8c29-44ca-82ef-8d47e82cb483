// NPM PACKAGE IMPORTS
import React, {
  useState,
  useEffect,
  useMemo,
  useRef,
  useCallback,
} from "react";
import CSVImport from "msuite_storybook/dist/csvImport/CSVImport";
import Select from "msuite_storybook/dist/select/Select";
import Button from "msuite_storybook/dist/button/Button";
import { useDispatch, useSelector } from "react-redux";
import { v4 as uuid } from "uuid";

// REDUX IMPORTS
import {
  handleSaveSortState,
  handleFetchSortState,
  handleFetchColumnState,
  handleFetchForgeModelInfo,
} from "../../../redux/generalActions";
import { handleUpdateJobs } from "../../jobs/jobsActions";
import {
  handleFetchPackages,
  handleUpdatePackages,
} from "../../packages/packagesActions";
import {
  handleFetchMaterialTypes,
  handleCreateItemsViaCsv,
  handleFetchItems,
  handleDeleteItem,
  handleUpdateItems,
  handleFetchVisibleColumns,
  handleFetchJoiningProcedures,
  handleCreateMaterialType,
  handleCreateJoiningProcedure,
} from "../../items/itemsActions";
import { handleFetchDrawingsInPackage } from "../wizardActions";
import { handleFetchLaydownLocations } from "../../shipping/shippingActions";
import {
  handleFetchContainers,
  handleUpdateDrawings,
} from "../../drawings/drawingsActions";
import { notify } from "../../reusable/alertPopup/alertPopupActions";

// HELPER FUNCTION IMPORTS
import {
  buildCSV,
  permissionLock,
  objectColumns,
  titleize,
  onDisplayedColumnsChanged,
  convertFracToDec,
} from "../../../_utils";

import usePrevious from "../../../hooks/usePrevious";

// TRANSLATION IMPORTS
import useTranslations from "../../../hooks/useTranslations";
import jobsTranslations from "../../jobs/jobsTranslations.json";

// CONSTANT IMPORTS
import { headers, images } from "./itemsConstants";
import { itemsTableColumnDefs } from "../../workTable/itemsTableColumnDefs";
import { frameworkComponents } from "../../jobs/jobsConstants";

// COMPONENT IMPORTS
import AgTable from "../../reusable/agTable/AgTable";
import MoreInfoDropdown from "../../reusable/moreInfoDropdown/MoreInfoDropdown";
import ConfirmationModal from "../../reusable/confirmationModal/ConfirmationModal";
import CreateSelectModal from "../../reusable/createSelectModal/CreateSelectModal";
import HeatNumberModal from "../../workTable/heatNumberModal/HeatNumberModal";
import PDFModal from "../../reusable/pdfModal/PDFModal";
import ForgeModal from "../../reusable/forge/ForgeModal";

// STYLE IMPORTS
import "./stylesItems.scss";
import {
  handleFetchDrawingFiles,
  handleFetchPackageFile,
} from "../../files/filesActions";

// In future, we should create a new column in the work item columns table to track this similar to how it works in the My Work area
const columnsToExcludeFromWizard = [
  "Drawing Area",
  "Drawing Due Date",
  "Drawing Priority",
  "Job Name",
  "Job Number",
  "Package Area",
  "Package Due Date",
  "Package Name",
  "Package ID",
  "Package Number",
  "Area",
  "Bend Bender Type",
  "Is Bomable",
  "Is Cut",
  "Measurement Area",
  "Estimated Weight",
];

const Items = ({ selectedJob, selectedPackage, setSelectedPackage }) => {
  const [showCSVModal, toggleCSVModal] = useState(false);
  const [csvError, setCSVError] = useState(null);
  const [receivedHeaders, setReceivedHeaders] = useState(null);
  const [csvData, setCsvData] = useState(null);
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [rowInfo, setRowInfo] = useState(null);
  // const [showHeatNumberModal, toggleHeatNumberModal] = useState(false);
  const [showMoreInfo, toggleMoreInfo] = useState(false);
  const [moreInfoLocation, setMoreInfoLocation] = useState(null);
  const [confirmation, toggleConfirmation] = useState(null);
  const [displayedRows, setDisplayedRows] = useState(null);
  const [newItemsData, setNewItemsData] = useState([]);
  const [showMaterialTypesModal, toggleMaterialTypesModal] = useState(false);
  const [showJoiningProceduresModal, toggleJoiningProceduresModal] = useState(
    false
  );
  const [showHeatNumbersModal, toggleHeatNumbersModal] = useState(false);
  const [showLaydownLocationsModal, toggleLaydownLocationsModal] = useState(
    false
  );
  const [isColumnStateLoaded, toggleColumnStateLoaded] = useState(false);
  const [emptyRowEditing, toggleEmptyRowEditing] = useState(false);
  const [displayNewItemSubmit, toggleNewItemSubmit] = useState(false);
  const [notified, toggleNotified] = useState(false);
  const [showPdfViewer, togglePdfViewer] = useState(false);
  const [showForgeViewer, toggleForgeViewer] = useState(false);
  const [forgeInfo, setForgeInfo] = useState(null);
  const [file, setFile] = useState(null);
  // for item inline creation
  const [isDrawingValid, toggleDrawingValid] = useState(false);
  const [isMaterialValid, toggleMaterialValid] = useState(false);
  const [highestItemId, setHighestItemId] = useState(null);
  const [currentEmptyRowIndex, setCurrentEmptyRowIndex] = useState(null);

  const emptyRowEditingRef = useRef(false);
  const createSelectDropdownDisplayRef = useRef(false);
  const newItemsDataRef = useRef([]);

  const dispatch = useDispatch();
  const translate = useTranslations(jobsTranslations);

  const { packages } = useSelector((state) => state.packagesData);
  const {
    materialTypes,
    items,
    isLoading,
    joiningProcedures,
    visibleColumns,
  } = useSelector((state) => state.itemsData);
  const { drawingsInPackage, error, wizardItemsColumnState } = useSelector(
    (state) => state.wizardData
  );
  const { containers, heatNumbers } = useSelector(
    (state) => state.drawingsData
  );
  const { laydown_locations } = useSelector((state) => state.shippingData);
  const { sortState } = useSelector((state) => state.generalData);
  const { packageFiles, drawingFiles } = useSelector(
    (state) => state.filesData
  );

  useEffect(() => {
    dispatch(handleFetchMaterialTypes());
    dispatch(handleFetchVisibleColumns);
    dispatch(handleFetchJoiningProcedures);
    dispatch(handleFetchLaydownLocations("materials"));
    dispatch(handleFetchColumnState("WIZARD_ITEMS")).then(() => {
      toggleColumnStateLoaded(true);
    });
    dispatch(handleFetchSortState("WIZARD_ITEMS"));
  }, []);

  useEffect(() => {
    if (!error || notified) return;
    if (error.message === "No drawings found") {
      toggleNotified(true);
      dispatch(
        notify({
          id: Date.now(),
          type: "ERROR",
          message:
            "Package doesn't have any drawings, so items cannot be created.",
        })
      );
    }
  }, [error]);

  // toggle ref to determine if createSelectModal is being shown
  useEffect(() => {
    if (showMaterialTypesModal || showJoiningProceduresModal)
      createSelectDropdownDisplayRef.current = true;
    else createSelectDropdownDisplayRef.current = false;
  }, [showMaterialTypesModal, showJoiningProceduresModal]);

  const itemsRef = useRef(null);

  useEffect(() => {
    if (isLoading) return;
    if (!items || !items.length) {
      itemsRef.current = null;
      setDisplayedRows([]);
      setHighestItemId(null);
    } else {
      itemsRef.current = items;
      setDisplayedRows(items);
      setHighestItemId(Math.max(...items.map((item) => item.id)));
    }
  }, [items, isLoading]);

  useEffect(() => {
    if (!gridOptionsApi) return;
    gridOptionsApi.setRowData(displayedRows);

    if (emptyRowEditing) {
      // redraw rows first to fix row backgrounds
      gridOptionsApi.redrawRows();

      gridOptionsApi.setFocusedCell(currentEmptyRowIndex || 0, "drawing_name");
      gridOptionsApi.startEditingCell({
        rowIndex: currentEmptyRowIndex || 0,
        colKey: "drawing_name",
      });
    }
  }, [displayedRows, gridOptionsApi, emptyRowEditing, currentEmptyRowIndex]);

  useEffect(() => {
    if (!selectedPackage) return;

    const packageObj = packages.find((p) => p.id === parseInt(selectedPackage));
    if (packageObj.forge_models) setForgeInfo(packageObj.forge_models);
    else setForgeInfo(null);

    setNewItemsData([]);
    toggleNotified(false);
    toggleEmptyRowEditing(false);

    dispatch(handleFetchItems(null, [selectedPackage], null, null, true));
    dispatch(handleFetchDrawingsInPackage(selectedPackage));
  }, [selectedPackage, packages]);

  useEffect(() => {
    if (selectedJob) {
      dispatch(handleFetchPackages([selectedJob], true));
      dispatch(handleFetchContainers(selectedJob.id));
    }
  }, [selectedJob]);

  const displayedJoiningProcedures = useMemo(() => {
    if (joiningProcedures) return joiningProcedures.map((jp) => jp.name);
    else return null;
  }, [joiningProcedures]);

  const displayedMaterialTypes = useMemo(() => {
    if (materialTypes) return materialTypes.map((mt) => mt.name);
    else return null;
  }, [materialTypes]);

  const displayedDrawings = useMemo(() => {
    if (drawingsInPackage) return drawingsInPackage.map((d) => d.name);
    else return null;
  }, [drawingsInPackage]);

  const getResults = ({ data }) => {
    setCsvData(data);
    const receivedHeaders = data[0];
    const receivedData = data.slice(1);
    setReceivedHeaders(receivedHeaders.filter((h) => h));

    let results = [];
    for (let i = 0; i < receivedData.length; i++) {
      let dataObj = {};
      for (let j = 0; j < receivedHeaders.length; j++) {
        if (receivedHeaders[j]) {
          dataObj[headers[receivedHeaders[j]]] = receivedData[i][j];
        }
      }
      results.push(dataObj);
    }
    return results;
  };

  const onRowDataChanged = (params) => {
    params.api.resetRowHeights();
  };

  const handleSubmit = () => {
    toggleEmptyRowEditing(false);
    let lengthIndex = csvData[0].indexOf("Length");

    const updatedData = csvData.slice(1).map((row) => {
      const isFraction =
        lengthIndex !== -1 &&
        (row[lengthIndex].includes(`'`) || row[lengthIndex].includes(`"`));

      // if length value is a fractional value, convert to decimal
      if (isFraction) row[lengthIndex] = convertFracToDec(row[lengthIndex]);

      // drawing name is 2nd value in array (row[1])
      // update drawing name to be the original_name in case of revisions
      const drawing = drawingsInPackage.find((d) => d.name === row[1]);
      if (drawing) row[1] = drawing.original_name;

      return row;
    });

    const itemsToCreate = buildCSV(
      csvData[0],
      updatedData.map((r) =>
        r.map((v) => (typeof v === "string" ? `'${v}'` : v))
      )
    );

    dispatch(
      handleCreateItemsViaCsv(
        parseInt(selectedJob),
        parseInt(selectedPackage),
        itemsToCreate
      )
    ).then(() => {
      toggleCSVModal(false);
      dispatch(handleFetchItems(null, [selectedPackage], null, null, true));
    });
  };

  const displayedPackages = useMemo(() => {
    if (packages) {
      return packages
        .map((p) => ({
          id: p.id,
          value: p.id,
          display: `${p.id}. ${p.package_name}`,
        }))
        .sort((a, b) => a.id - b.id);
    }
  }, [packages]);

  const displayedHeaders = useMemo(() => {
    if (receivedHeaders) {
      return receivedHeaders;
    } else return [];
  }, [receivedHeaders]);

  const itemsCSVTemplate = useMemo(() => {
    return buildCSV(Object.keys(headers));
  }, [headers]);

  const onFilterChanged = (params) => {
    params.api.redrawRows();
  };

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
  };

  const moreInfoClick = async (event, toggleMoreInfo, rowData) => {
    event.persist();
    setMoreInfoLocation({ x: event.clientX, y: event.clientY + 80 });
    let forgeModelInfo;

    if (forgeInfo) {
      forgeModelInfo = await dispatch(
        handleFetchForgeModelInfo(JSON.parse(forgeInfo)[0].forge_model_id)
      );
    }

    if (forgeModelInfo) {
      setRowInfo({
        ...rowData,
        forge_urn: forgeModelInfo[0].urn,
        model_name: forgeModelInfo[0].model_name,
      });
    } else setRowInfo(rowData);
    toggleMoreInfo(true);
  };

  const onCellValueChanged = (params) => {
    if (emptyRowEditingRef.current || typeof params.data.id === "string")
      return;

    if (!params.newValue && !params.oldValue) return;
    if (params.newValue !== params.oldValue) {
      if (/joint_heat_number/.test(params.colDef.field)) {
        return params.node.setData({
          ...params.data,
          [params.colDef.field]: params.oldValue,
        });
      } else {
        const property = (field) => {
          switch (field) {
            case "container_name":
              return "shipping_container_id";
            case "joining_procedure_name":
              return "joining_procedure_id";
            case "material_name":
              return "material_type_id";
            case "laydown_location_name":
              return "laydown_location_id";
            default:
              return field;
          }
        };

        const value = (field) => {
          if (objectColumns.includes(field)) return params.newValue.decimal;

          switch (field) {
            case "container_name":
              return (
                (
                  containers.find(
                    (c) =>
                      c.name === params.newValue &&
                      c.job_id === params.data.job_id
                  ) || {}
                ).id || null
              );
            case "joining_procedure_name":
              return (
                (
                  joiningProcedures.find((jp) => jp.name === params.newValue) ||
                  {}
                ).id || null
              );
            case "material_name":
              return (
                (materialTypes.find((m) => m.name === params.newValue) || {})
                  .id || null
              );
            case "laydown_location_name":
              return (
                (
                  laydown_locations.find((ll) => ll.name === params.newValue) ||
                  {}
                ).id || null
              );
            default:
              return params.newValue || null;
          }
        };

        if (
          [
            "joining_procedure_id",
            "insulation_specification",
            "material_type_id",
          ].includes(property(params.colDef.field)) &&
          !value(params.colDef.field)
        )
          return;

        if (objectColumns.includes(params.colDef.field)) {
          const newValue = params.newValue,
            oldValue = params.oldValue;

          if (
            newValue.decimal === oldValue.decimal ||
            newValue.display === oldValue.display
          )
            return;
        }

        if (params.colDef.field === "drawing_due_date") {
          const updatedData =
            params.colDef.field === "drawing_name"
              ? { original_name: params.newValue }
              : {
                  [params.colDef.field.slice(8)]:
                    new Date(params.newValue).getTime() / 1000,
                };
          dispatch(
            handleUpdateDrawings(params.data.drawing_id, updatedData, "update")
          ).then((res) => {
            if (!res.error) {
              dispatch(
                handleFetchItems(null, [selectedPackage], null, null, true)
              );
            }
          });
        } else if (
          [
            "package_name",
            "package_number",
            "package_due_date",
            "package_area",
          ].includes(params.colDef.field)
        ) {
          const updatedData =
            params.colDef.field === "package_name"
              ? { package_name: params.newValue }
              : {
                  [params.colDef.field.slice(8)]:
                    params.colDef.field === "package_due_date"
                      ? new Date(params.newValue).getTime() / 1000
                      : params.newValue,
                };

          dispatch(
            handleUpdatePackages(params.data.package_id, updatedData)
          ).then((res) => {
            dispatch(
              handleFetchItems(null, [selectedPackage], null, null, true)
            );
          });
        } else if (["job_name", "job_number"].includes(params.colDef.field)) {
          const updatedData =
            params.colDef.field === "job_name"
              ? { job_name: params.newValue }
              : { job_number: params.newValue };

          dispatch(handleUpdateJobs(params.data.job_id, updatedData)).then(
            (res) => {
              dispatch(
                handleFetchItems(null, [selectedPackage], null, null, true)
              );
            }
          );
        } else {
          dispatch(
            handleUpdateItems(
              params.data.id,
              {
                [property(params.colDef.field)]:
                  value(params.colDef.field) ||
                  (objectColumns.includes(params.colDef.field) ? 0 : null),
              },
              null,
              null,
              params.data.quantity
            )
          ).then((res) => {
            const rowNode = params.api.getRowNode(params.data.id);
            if (!res.error) {
              rowNode.setData(res[0]);
            } else {
              rowNode.setData({
                ...rowNode.data,
                [params.colDef.field]: params.oldValue,
              });
            }
          });
        }
      }
    }
  };

  const handleDeleteConfirmation = () => {
    toggleConfirmation(false);
    if (newItemsData && newItemsData.length) {
      const emptyRowIds = newItemsData.map((o) => o.data.id);

      // check if we are deleting an empty row
      if (emptyRowIds.includes(rowInfo.id)) {
        gridOptionsApi.applyTransaction({
          remove: [{ id: rowInfo.id }],
        });
        gridOptionsApi.redrawRows();

        const refreshedData = newItemsData.filter(
          (o) => o.data.id !== rowInfo.id
        );
        setNewItemsData(refreshedData);
        setDisplayedRows(displayedRows.filter((r) => r.id !== rowInfo.id));
        toggleEmptyRowEditing(false);
        return;
      }
    } else
      dispatch(handleDeleteItem(rowInfo.id)).then(() => {
        dispatch(handleFetchItems(null, [selectedPackage], null, null, true));
      });
  };

  const onRowEditingStopped = (params) => {
    if (createSelectDropdownDisplayRef.current && emptyRowEditingRef.current)
      return;

    if (emptyRowEditingRef.current) {
      if (!newItemsDataRef.current || !newItemsDataRef.current.length)
        setNewItemsData([params]);

      const emptyRowIds = newItemsDataRef.current.map((o) => o.data.id);
      // check if row has already been edited
      if (emptyRowIds.includes(params.data.id)) {
        let result = [];
        newItemsDataRef.current.forEach((o) => {
          if (o.data.id === params.data.id) {
            result.push(params);
          } else result.push(o);
        });

        setNewItemsData(result);
      } else setNewItemsData([...newItemsDataRef.current, params]);
    }
    toggleEmptyRowEditing(false);
    setCurrentEmptyRowIndex(null);
  };

  const onRowEditingStarted = (params) => {
    if (params.data.empty) return;

    setCurrentEmptyRowIndex(null);
    toggleEmptyRowEditing(false);
  };

  useEffect(() => {
    newItemsDataRef.current = newItemsData;
  }, [newItemsData]);

  const handleSubmitNewItems = () => {
    // need to wait for ag-grids onRowEditingStopped to run in order for data to populate
    setTimeout(() => {
      if (!newItemsData) return;

      const data = newItemsData.map((params) => {
        let f_data = params.data;
        f_data.laydown_location = f_data.laydown_location_name;
        delete f_data.laydown_location_name;

        // must grab the drawing's original_name to send when creating items to not include rev
        const drawing = drawingsInPackage.find(
          (d) => d.name === f_data.drawing_name
        );
        if (drawing) f_data.drawing_name = drawing.original_name;

        if (f_data.length) f_data.length = f_data.length.decimal;

        return f_data;
      });

      let headers = Object.keys(data[0]);

      let newData = [];
      data.forEach((d) => {
        for (let i = 0; i < parseFloat(d.quantityToCreate); i++) {
          newData.push(Object.values(d));
        }
      });

      // the escaped data still cannot handle double quotes back to back
      // data with back to back quotes should be rejected or cleansed
      const newItems = buildCSV(
        headers.map((x) => titleize(x)),
        newData.map((r) => r.map((v) => (typeof v === "string" ? `'${v}'` : v)))
      );
      dispatch(
        handleCreateItemsViaCsv(
          parseInt(selectedJob),
          parseInt(selectedPackage),
          newItems
        )
      ).then(() => {
        toggleDrawingValid(false);
        toggleMaterialValid(false);
        setNewItemsData([]);
        toggleEmptyRowEditing(false);
        dispatch(handleFetchMaterialTypes());
      });
    }, 500);
  };

  const showQuantityCreationColumn = useMemo(() => {
    if (!emptyRowEditing && !newItemsData.length) return false;
    else return true;
  }, [emptyRowEditing, newItemsData]);

  const displayNotification = (message) => {
    dispatch(
      notify({
        id: Date.now(),
        type: "ERROR",
        message,
      })
    );
  };

  const handlePDFClick = (params) => {
    setRowInfo(params.data);
    togglePdfViewer(true);
  };

  // used for the PDF files that we fetch from AWS S3 service
  const packageFilesRef = useRef(null);
  useEffect(() => {
    packageFilesRef.current = packageFiles;
  }, [packageFiles]);

  const drawingFilesRef = useRef(null);
  useEffect(() => {
    drawingFilesRef.current = drawingFiles;
  }, [drawingFiles]);

  // TODO - Consolidate to a reusable function, duplicated code!
  // common code for setting the package map as the file
  const getPackagePDFs = (packageId, updateFile = false) => {
    if (!packageId) return;
    const files = packageFilesRef?.current;
    if (!files?.hasOwnProperty(packageId)) {
      dispatch(handleFetchPackageFile(packageId)).then((res) => {
        if (res?.[packageId] && updateFile) {
          setFile(res[packageId]);
        }
      });
    } else if (updateFile) {
      // update the file if it already exists and we want to update...
      setFile(files[packageId]);
    }
    // else we exit because we already know the drawing package info
  };

  // TODO - Consolidate to a reusable function, duplicated code!
  const getDrawingPDFs = (id, rowData) => {
    if (!id) return;
    // if the file has no files to fetch, return (kept separate for logic simplicity)
    if (!(rowData.has_original ?? 0) && !(rowData.has_package_map ?? 0)) return;
    const files = drawingFilesRef?.current;
    if (files?.hasOwnProperty(id)) {
      // if drawing doesn't have original check for map
      if (files?.[id]?.original) {
        setFile(files[id].original);
      }
      // load package PDF and set if original was missing
      if (rowData.has_package_map) {
        getPackagePDFs(rowData.package_id, !files?.[id]?.original);
      }
    } else if (rowData?.has_original) {
      dispatch(handleFetchDrawingFiles(id)).then((res) => {
        // if drawing doesn't have original check for map
        if (res?.[id]?.original) {
          setFile(res[id].original);
        }
        // load package PDF and set if original was missing
        if (rowData.has_package_map) {
          getPackagePDFs(rowData.package_id, !res?.[id]?.original);
        }
      });
    } else if (rowData?.has_package_map) {
      // load package PDF and set
      getPackagePDFs(rowData.package_id, true);
    } // else do nothing... nothing to load
  };

  const setDisplayedPdf = (rowData) => {
    let id = rowData?.drawing_id || rowData.id;
    getDrawingPDFs(id, rowData);
  };

  const permissionLockedColumns = useMemo(() => {
    if (
      isColumnStateLoaded &&
      containers &&
      displayedJoiningProcedures &&
      displayedMaterialTypes &&
      visibleColumns &&
      laydown_locations &&
      sortState
    ) {
      let colDefs = itemsTableColumnDefs(
        wizardItemsColumnState,
        visibleColumns,
        "ungrouped",
        sortState,
        moreInfoClick,
        toggleMoreInfo,
        handlePDFClick,
        containers,
        (r) => {
          toggleMaterialTypesModal(true);
          setRowInfo(r);
        },
        (r) => {
          toggleLaydownLocationsModal(true);
          setRowInfo(r);
        },
        (r) => {
          toggleJoiningProceduresModal(true);
          setRowInfo(r);
        },
        (r) => {
          toggleHeatNumbersModal(true);
          setRowInfo(r);
        },
        null, // create function to send notification
        null, // testStore
        null, // items - used for drawing_priority
        false, // customColumn feature turned on
        "WIZARD_ITEMS",
        displayedDrawings,
        toggleDrawingValid,
        toggleMaterialValid,
        showQuantityCreationColumn,
        setDisplayedPdf
      )?.filter(
        (colDef) => !columnsToExcludeFromWizard.includes(colDef.headerName)
      );

      return permissionLock(colDefs);
    } else return null;
  }, [
    isColumnStateLoaded,
    containers,
    displayedJoiningProcedures,
    displayedMaterialTypes,
    visibleColumns,
    laydown_locations,
    displayedDrawings,
    sortState,
    rowInfo,
    showQuantityCreationColumn,
  ]);

  const permissionLockedColumnsRef = useRef(null);
  const prevPermissionLockedColumns = usePrevious(permissionLockedColumns);

  useEffect(() => {
    if (!gridOptionsApi || !permissionLockedColumns) return;
    permissionLockedColumnsRef.current = permissionLockedColumns;
    gridOptionsApi.setColumnDefs(permissionLockedColumns);
  }, [gridOptionsApi, permissionLockedColumns]);

  const onSortChanged = (params) => {
    params.api.redrawRows();
    const sortedColumn = params.columnApi.getAllColumns().find((c) => c.sort);
    dispatch(
      handleSaveSortState(
        sortedColumn ? sortedColumn.colId : null,
        sortedColumn ? sortedColumn.sort : null,
        "WIZARD_ITEMS",
        1 // UPDATE WHEN GROUPING FUNC GETS ADDED
      )
    );
  };

  const handleDisplayedColumnsChange = (params) => {
    // check if the reason for the change is the quantity column being added
    if (
      permissionLockedColumnsRef.current?.find(
        (col) => col.headerName === "Quantity"
      )
    )
      return;

    // check if prevPermissionLockedColumns are the same as current (for when quantity column is hidden after creation)
    if (
      JSON.stringify(prevPermissionLockedColumns.current) ===
      JSON.stringify(permissionLockedColumnsRef.current)
    )
      return;

    return onDisplayedColumnsChanged("WIZARD_ITEMS", params, null, null, null);
  };

  const previousHighestItemId = usePrevious(highestItemId);

  const postSort = useCallback(
    (rowNodes) => {
      let nextInsertPos = 0;

      // return if there are no items to sort - must have this in order to auto enter edit mode on emoty table when adding new item
      if (!itemsRef.current?.length) return;
      for (let i = 0; i < rowNodes.length; i++) {
        const isEmpty = rowNodes[i].data.empty ? true : false;
        if (
          isEmpty ||
          (previousHighestItemId && rowNodes[i].data.id > previousHighestItemId)
        ) {
          rowNodes.splice(nextInsertPos, 0, rowNodes.splice(i, 1)[0]);
          nextInsertPos++;
        }
      }
    },
    [previousHighestItemId]
  );

  const gridOptions = {
    immutableData: true,
    rowData: displayedRows,
    columnDefs: permissionLockedColumns,
    reactNext: true,
    frameworkComponents: frameworkComponents(),
    getRowStyle: (params) => {
      if (params.node.childIndex % 2 === 1) {
        return { background: "#363d49 !important" };
      } else return { background: "#48525f !important" };
    },
    onFilterChanged,
    onSortChanged,
    onGridReady,
    onRowDataChanged,
    suppressRowClickSelection: true,
    editType: "fullRow",
    rowHeight: 60,
    defaultColDef: {
      cellClass: ["no-border", "custom-wrap"],
      wrapText: true,
      autoHeight: true,
      flex: 1,
    },
    tabToNextCell: () => null,
    getRowNodeId: (data) => data.id,
    onDisplayedColumnsChanged: handleDisplayedColumnsChange,
    onCellValueChanged,
    onRowEditingStopped,
    onRowEditingStarted,
    // move empty rows to top of table if sorted
    postSort,
    stopEditingWhenGridLosesFocus: true,
  };

  const createNewRowData = () => {
    return {
      id: uuid(),
      drawing_name: null,
      drawing_id: null,
      tag_number: null,
      end_prep_1: null,
      end_prep_2: null,
      end_prep_3: null,
      end_prep_4: null,
      length: null,
      stock_length: null,
      identifier: null,
      measurement_area: null,
      height: null,
      width: null,
      thickness: null,
      paint_spec: null,
      texture: null,
      fixture_type: null,
      gauge: null,
      weight: null,
      hanger_size: null,
      product_code: null,
      globally_unique_id: null,
      insulation: null,
      insulation_area: null,
      insulation_specification: null,
      insulation_gauge: null,
      joining_procedure_name: null,
      joining_procedure_id: null,
      material_name: null,
      material_type_id: null,
      service_name: null,
      service_color_name: null,
      service_color_argb: null,
      status: null,
      size: null,
      area: null,
      random_length: null,
      rod_size: null,
      support_rod_length: null,
      support_rod_length_2: null,
      laydown_location: null,
      laydown_location_id: null,
      liner_spec: null,
      heat_number: null,
      filler_metal: null,
      vendor: null,
      empty: true,
      quantityToCreate: "1",
    };
  };

  const handleAddNewRow = () => {
    const emptyRow = createNewRowData();

    setRowInfo(emptyRow);
    setDisplayedRows([emptyRow, ...displayedRows]);

    // setting these here now as opposed to onRowEditingStarted to prevent an infinite loop when redrawing rows in the useEffect above
    setCurrentEmptyRowIndex(0);
    toggleEmptyRowEditing(true);

    toggleNewItemSubmit(true);
  };

  useEffect(() => {
    if (!newItemsData || !newItemsData.length) toggleNewItemSubmit(false);
  }, [newItemsData]);

  useEffect(() => {
    emptyRowEditingRef.current = emptyRowEditing;
  }, [emptyRowEditing]);

  const handleClosePDFViewer = () => {
    togglePdfViewer(false);
    setRowInfo(null);
  };

  return (
    <div className="items">
      <div className="options-wrapper">
        <div className="main">
          <Select
            options={displayedPackages}
            placeholder="Select a package:"
            onInput={(e) => setSelectedPackage(e.target.value)}
            value={selectedPackage || "DEFAULT"}
          />
          <Button
            className="options-button"
            disabled={
              !selectedPackage ||
              !displayedDrawings ||
              !displayedDrawings.length
            }
            onClick={() => toggleCSVModal(true)}
          >
            Upload CSV
          </Button>
          <Button
            className="options-button"
            disabled={
              !selectedPackage ||
              !gridOptionsApi ||
              isLoading ||
              !displayedDrawings ||
              !displayedDrawings.length
            }
            onClick={handleAddNewRow}
          >
            Add New Item
          </Button>
        </div>
        {displayNewItemSubmit && (
          <Button
            disabled={!isDrawingValid || !isMaterialValid}
            className="new-item-submit"
            onClick={handleSubmitNewItems}
          >
            Submit
          </Button>
        )}
      </div>
      {selectedPackage && <AgTable gridOptions={gridOptions} />}
      {showMoreInfo && moreInfoLocation && (
        <MoreInfoDropdown
          rowInfo={rowInfo}
          currentTable="WIZARD_ITEMS"
          moreInfoLocation={moreInfoLocation}
          toggleMoreInfo={toggleMoreInfo}
          toggleConfirmationModal={toggleConfirmation}
          toggleForgeViewer={() => toggleForgeViewer(true)}
        />
      )}
      {confirmation && (
        <ConfirmationModal
          showModal={confirmation}
          toggleModal={() => toggleConfirmation(false)}
          action={confirmation.action}
          item={"this item"}
          handleClick={handleDeleteConfirmation}
        />
      )}
      {showMaterialTypesModal && (
        <CreateSelectModal
          items={materialTypes}
          rowInfo={rowInfo}
          open={showMaterialTypesModal}
          handleClose={() => toggleMaterialTypesModal(false)}
          gridOptionsApi={gridOptionsApi}
          callback={() => toggleMaterialValid(true)}
          handleCreateNewItem={handleCreateMaterialType}
          handleUpdateItem={handleUpdateItems}
          idKey="material_type_id"
          nameKey="material_name"
        />
      )}
      {showJoiningProceduresModal && (
        <CreateSelectModal
          items={joiningProcedures}
          rowInfo={rowInfo}
          open={showJoiningProceduresModal}
          handleClose={() => toggleJoiningProceduresModal(false)}
          gridOptionsApi={gridOptionsApi}
          handleCreateNewItem={handleCreateJoiningProcedure}
          handleUpdateItem={handleUpdateItems}
          idKey="joining_procedure_id"
          nameKey="joining_procedure_name"
        />
      )}
      {showLaydownLocationsModal && (
        <CreateSelectModal
          items={laydown_locations}
          rowInfo={rowInfo}
          open={showLaydownLocationsModal}
          handleClose={() => toggleLaydownLocationsModal(false)}
          gridOptionsApi={gridOptionsApi}
          handleUpdateItem={handleUpdateItems}
          idKey="laydown_location_id"
          nameKey="laydown_location_name"
        />
      )}
      {showHeatNumbersModal && (
        <HeatNumberModal
          open={showHeatNumbersModal}
          handleClose={() => toggleHeatNumbersModal(false)}
          rowInfo={rowInfo}
          heatNumbers={heatNumbers}
          gridOptionsApi={gridOptionsApi}
          translate={translate}
          tableType="WIZARD"
        />
      )}
      {showPdfViewer && (
        <PDFModal
          pdfViewer={showPdfViewer}
          togglePDFViewer={handleClosePDFViewer}
          selectedItem={rowInfo}
          itemId={rowInfo && rowInfo.drawing_id}
          itemType="DRAWING"
          file={file}
          setFile={setFile}
          area="VIEWER"
          currentRowOrder={false}
          setSelectedItem={setRowInfo}
        />
      )}
      {showForgeViewer && (
        <ForgeModal
          forgeViewer={showForgeViewer}
          toggleForgeViewer={() => toggleForgeViewer(false)}
          selectedItem={rowInfo}
          type="ITEMS"
          area="VIEWER"
        />
      )}
      <CSVImport
        showCSVModal={showCSVModal}
        toggleCSVModal={toggleCSVModal}
        csvError={csvError}
        setCSVError={setCSVError}
        submitText="Submit"
        title="Upload Items"
        getResults={getResults}
        headers={displayedHeaders}
        images={images}
        templatePath={itemsCSVTemplate}
        download="fabpro_items_template.csv"
        handleSubmit={handleSubmit}
      />
    </div>
  );
};

export default Items;
