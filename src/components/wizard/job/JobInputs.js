// NPM PACKAGE IMPORTS
import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import Input from "msuite_storybook/dist/input/Input";
import Toggle from "msuite_storybook/dist/toggle/Toggle";
import GoogleMap from "msuite_storybook/dist/googleMap/GoogleMap";
import DatePicker from "react-datepicker";

// HELPER FUNCTION IMPORTS
import { generateTime } from "../../../_utils";
import { handleCreateJob, handleUpdateJobs } from "../../jobs/jobsActions";

const JobInputs = ({ jobInfo, wizardSetSelectedJob }) => {
  const [selectedJob, setSelectedJob] = useState(null);
  const [jobName, setJobName] = useState("");
  const [jobNumber, setJobNumber] = useState("");
  const [dueDate, setDueDate] = useState(null);
  const [floatMode, toggleFloatMode] = useState(false);
  const [address, setAddress] = useState({});
  const [budgetHours, setBudgetHours] = useState(0);

  const dispatch = useDispatch();

  useEffect(() => {
    if (jobInfo) setSelectedJob(jobInfo);
  }, [jobInfo]);

  useEffect(() => {
    if (selectedJob) {
      setJobName(selectedJob.job_name);
      setJobNumber(selectedJob.job_number);
      if (selectedJob.unix_target_date) {
        setDueDate(
          new Date(
            generateTime(selectedJob.unix_target_date * 1000, false, true, "-")
          )
        );
      }
      toggleFloatMode(!!selectedJob.exclude_float_mode);
      setAddress({
        address: selectedJob.address || "",
        city: selectedJob.city || "",
        state: selectedJob.state || "",
        zip: selectedJob.zip || "",
        formatted_address: [
          selectedJob.address || "",
          selectedJob.city || "",
          selectedJob.state || "",
          selectedJob.zip || "",
        ]
          .filter((v) => v)
          .join(", "),
      });
      if (selectedJob.budget_hours)
        setBudgetHours(selectedJob.budget_hours.toFixed(2));
    }
  }, [selectedJob]);

  const updateAddress = ({ key, value }) => {
    setAddress({ ...address, [key]: value });
  };

  const saveNewJob = () => {
    if (jobInfo) return;
    if (!jobName || !jobName.trim()) return;
    if (!jobNumber || !jobNumber.trim()) return;

    dispatch(
      handleCreateJob({
        job_name: jobName,
        job_number: jobNumber,
        target_date: dueDate ? Math.trunc(dueDate.getTime() / 1000) : null,
        address: address.address || null,
        city: address.city || null,
        state: address.state || null,
        zip: address.zip || null,
        budget_hours: budgetHours,
        exclude_float: floatMode ? 1 : 0,
      })
    ).then((res) => {
      if (!res.error) {
        if (window.history.pushState) {
          var newurl =
            window.location.protocol +
            "//" +
            window.location.host +
            window.location.pathname +
            `?JobID=${res.id}`;
          window.history.pushState({ path: newurl }, "", newurl);
        }
        wizardSetSelectedJob(res.id);
      }
    });
  };

  const saveUpdatedInfo = (field, newValue) => {
    if (!selectedJob) return;

    if (!["target_date", "exclude_float_mode"].includes(field)) {
      if (field === "job_name" && !newValue.trim()) return;
      if (field === "job_number" && !newValue.trim()) return;
      if (selectedJob[field] === newValue.trim()) return;
    }

    if (
      field === "target_date" &&
      ((!newValue && !selectedJob.unix_target_date) ||
        (selectedJob.unix_target_date &&
          newValue &&
          new Date(
            generateTime(selectedJob.unix_target_date * 1000, false, true, "-")
          ).getTime() === newValue.getTime()))
    )
      return;
    if (
      field === "exclude_float_mode" &&
      selectedJob.exclude_float_mode === newValue
    )
      return;

    let valueToSend;

    switch (field) {
      case "target_date":
        valueToSend = newValue ? Math.trunc(newValue.getTime() / 1000) : null;
        break;
      case "exclude_float_mode":
        valueToSend = newValue;
        break;
      default:
        valueToSend = newValue.trim();
        break;
    }

    if (valueToSend === undefined) return;

    dispatch(handleUpdateJobs(selectedJob.id, { [field]: valueToSend })).then(
      (res) => {
        if (!res.error) {
          setSelectedJob(res[0]);
        }
      }
    );
  };

  const saveFullUpdatedAddress = (updatedAddress) => {
    dispatch(handleUpdateJobs(selectedJob.id, updatedAddress)).then((res) => {
      if (!res.error) {
        setSelectedJob(res[0]);
      }
    });
  };

  return (
    <div className="jobs-inputs">
      <label className="wizard-content-label job-name">
        <span>
          Job Name <span style={{ color: "red" }}>*</span>
        </span>
        <Input
          placeholder="Job Name"
          value={jobName}
          onChange={(e) => setJobName(e.target.value)}
          onBlur={(e) => {
            if (jobInfo) saveUpdatedInfo("job_name", e.target.value);
            else saveNewJob();
          }}
        />
      </label>
      <label className="wizard-content-label job-number">
        <span>
          Job Number <span style={{ color: "red" }}>*</span>
        </span>
        <Input
          placeholder="Job Number"
          value={jobNumber}
          onChange={(e) => setJobNumber(e.target.value)}
          onBlur={(e) => {
            if (jobInfo) saveUpdatedInfo("job_number", e.target.value);
            else saveNewJob();
          }}
        />
      </label>
      <label className="wizard-content-label date-picker">
        <span>Due Date</span>
        <form autoComplete="off">
          <DatePicker
            className="date-picker"
            selected={dueDate}
            onChange={setDueDate}
            onBlur={() => saveUpdatedInfo("target_date", dueDate)}
          />
        </form>
      </label>
      <div className="wizard-content-label float-mode">
        <span>Exclude from Float Mode?</span>
        <Toggle
          name="float-mode-toggle"
          text={["yes", "no"]}
          defaultChecked={floatMode}
          onToggleChanged={() => {
            toggleFloatMode(!floatMode);
            saveUpdatedInfo("exclude_float_mode", !floatMode === true ? 1 : 0);
          }}
        />
      </div>
      <label className="wizard-content-label address">
        <span>Job Site Address</span>
        <Input
          placeholder="Job Site Address"
          value={address ? address.address || "" : ""}
          onChange={(e) =>
            updateAddress({ key: "address", value: e.target.value })
          }
          onBlur={(e) => saveUpdatedInfo("address", e.target.value)}
        />
      </label>
      <label className="wizard-content-label city">
        <span>City</span>
        <Input
          placeholder="City"
          value={address ? address.city || "" : ""}
          onChange={(e) =>
            updateAddress({ key: "city", value: e.target.value })
          }
          onBlur={(e) => saveUpdatedInfo("city", e.target.value)}
        />
      </label>
      <label className="wizard-content-label state">
        <span>State</span>
        <Input
          placeholder="State"
          value={address ? address.state || "" : ""}
          onChange={(e) =>
            updateAddress({ key: "state", value: e.target.value })
          }
          onBlur={(e) => saveUpdatedInfo("state", e.target.value)}
        />
      </label>
      <label className="wizard-content-label zip">
        <span>Zip Code</span>
        <Input
          placeholder="Zip Code"
          value={address ? address.zip || "" : ""}
          onChange={(e) => updateAddress({ key: "zip", value: e.target.value })}
          onBlur={(e) => saveUpdatedInfo("zip", e.target.value)}
        />
      </label>
      <label className="wizard-content-label budget-hours">
        <span>Budget Hours</span>
        <Input
          placeholder="Budget Hours"
          value={budgetHours}
          onChange={(e) => setBudgetHours(e.target.value)}
          onBlur={(e) => saveUpdatedInfo("budget_hours", e.target.value)}
          type="number"
        />
      </label>
      <div className="google-map-container">
        <GoogleMap
          address={
            (address && address.formatted_address) ||
            "311 3rd Ave SE Suite 450, Cedar Rapids, IA 52401"
          }
          draggableMarker
          onMarkerDragEnd={({ address }) => {
            const updatedAddress = {
              city: address.city || "",
              state: address.state || "",
              address:
                (address.number ? address.number + " " : "") +
                (address.street || ""),
              zip: address.zip || "",
            };
            saveFullUpdatedAddress(updatedAddress);
            setAddress({
              ...updatedAddress,
              formatted_address: [
                updatedAddress.address,
                updatedAddress.city,
                updatedAddress.state,
                updatedAddress.zip,
              ]
                .filter((v) => v)
                .join(", "),
            });
          }}
        />
      </div>
    </div>
  );
};

export default JobInputs;
