// NPM PACKAGE IMPORTS
import React, { useState } from "react";
import { useSelector } from "react-redux";

// COMPONENT IMPORTS
import { default as Inputs } from "./JobInputs";
import { default as Assignments } from "../../reusable/assignments/Assignments";
import { default as ManageCrews } from "../../reusable/manageCrews/ManageCrews";

// STYLE IMPORTS
import "./stylesJob.scss";

const Job = ({ setSelectedJob }) => {
  const [showManageCrewsModal, toggleManageCrewsModal] = useState(false);

  const { specificJob } = useSelector((state) => state.jobsData);

  return (
    <div className="job-info">
      <Inputs jobInfo={specificJob} wizardSetSelectedJob={setSelectedJob} />
      {specificJob && (
        <Assignments
          selectedItem={specificJob}
          currentTable="JOBS"
          openManageCrews={() => toggleManageCrewsModal(true)}
        />
      )}
      {showManageCrewsModal && (
        <ManageCrews
          open={showManageCrewsModal}
          handleClose={() => toggleManageCrewsModal(false)}
        />
      )}
    </div>
  );
};

export default Job;
