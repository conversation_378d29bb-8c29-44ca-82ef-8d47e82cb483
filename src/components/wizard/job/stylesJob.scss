@import "../../styles/colors.scss";

div.job-info {
  & > div.jobs-inputs {
    grid-template-areas:
      "a a a b b b c c d d"
      "e e f f g g h h i i"
      "j j j j j . . . . ."
      "j j j j j . . . . .";
    row-gap: 10px;

    background-color: $darkFab;
    padding: 15px 15px 0;
    border: 1px solid #000;
    border-radius: 3px;

    & > label.wizard-content-label.job-name {
      grid-area: a;
    }

    & > label.wizard-content-label.job-number {
      grid-area: b;
    }

    & > label.wizard-content-label.date-picker {
      grid-area: c;

      & > form > div {
        width: 100%;
      }
    }

    & > div.wizard-content-label.float-mode {
      grid-area: d;
    }

    & > label.wizard-content-label.address {
      grid-area: e;
    }

    & > label.wizard-content-label.city {
      grid-area: f;
    }

    & > label.wizard-content-label.state {
      grid-area: g;
    }

    & > label.wizard-content-label.zip {
      grid-area: h;
    }

    & > label.wizard-content-label.budget-hours {
      grid-area: i;
    }

    & > div.google-map-container {
      grid-area: j;
    }

    & label.wizard-content-label.date-picker div {
      & input.date-picker {
        height: 30px;
        padding: 0;
        border: 1px solid #ccc;
        width: 100%;
        outline: none;
        font-size: rem;
        box-sizing: border-box;
        border-radius: 3px;

        &:hover {
          border-color: $fabProBlue;
        }

        &:focus {
          border: 2px solid $fabProBlue;
        }
      }
      & .react-datepicker-popper[data-placement^="bottom"] {
        margin: 0;
        top: 10px !important;
      }
    }

    & div.google-map-container {
      grid-column: 1/-1;
      height: 400px;
      width: 500px;
      position: relative;

      & > div > div {
        height: 400px !important;

        & > div {
          height: 100% !important;
        }
      }
    }

    & div.assignments {
      grid-column: 1/-1;
    }
  }
}
