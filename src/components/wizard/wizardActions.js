import {
  fetchCostCodes,
  fetchJobSpecificCostCodes,
  fetchDrawings,
  fetchUsersAssignedToItem,
  fetchFlows,
  updatePackageCostCodes,
  createCostCodes,
  updateJobCostCodes,
  fetchUsers,
  fetchCostCodeTypes,
  updateGeneralCostCode,
  deleteGeneralCostCode,
  createCostCodeType,
  updateCostCodeType,
  deleteCostCodeType,
} from "../../_services";
import { naturalSort } from "../../_utils";

export const receiveStarted = (type) => ({
  type: `RECEIVE_${type}_STARTED`,
});
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});
export const updateStarted = (type) => ({
  type: `UPDATE_${type}_STARTED`,
});
export const updateSucceeded = (type, payload) => ({
  type: `UPDATE_${type}_SUCCEEDED`,
  payload,
});
export const updateFailed = (type, error) => ({
  type: `UPDATE_${type}_FAILED`,
  payload: error,
});

export const handleClearUsers = (dispatch) => {
  dispatch(receiveSucceeded("WIZARD_USERS", []));
  dispatch(receiveSucceeded("WIZARD_USER_ASSIGNMENTS", []));
};

export const handleFetchUsersAssignedToItem = (
  itemId,
  level,
  roleId = null
) => (dispatch) => {
  const type = "WIZARD_USER_ASSIGNMENTS";
  dispatch(receiveStarted(type));
  return fetchUsersAssignedToItem(itemId, level, roleId).then((res) => {
    if (res.error) return dispatch(receiveFailed(type, res));
    return dispatch(receiveSucceeded(type, res));
  });
};

export const handleFetchCostCodes = (
  jobIds = null,
  includeGeneral = 1,
  onlyGeneral = 0
) => (dispatch) => {
  const type =
    "WIZARD_" +
    (jobIds && jobIds.length ? "JOB_" : onlyGeneral ? "GENERAL_" : "") +
    "COST_CODES";

  dispatch(receiveStarted(type));
  return fetchCostCodes(null, includeGeneral, 0, jobIds, onlyGeneral).then(
    (res) => {
      if (res.error) {
        dispatch(receiveFailed(type, res.error));
        return res;
      }

      res.sort((a, b) => naturalSort(a.name, b.name));
      dispatch(receiveSucceeded(type, res));
      return res;
    }
  );
};

export const handleFetchJobSpecificCostCodes = (jobId) => (dispatch) => {
  const type = "WIZARD_JOB_COST_CODES";

  dispatch(receiveStarted(type));
  return fetchJobSpecificCostCodes(jobId).then((res) => {
    if (res.error) {
      dispatch(receiveFailed(type, res.error));
      return res;
    }
    res.sort((a, b) => naturalSort(a.name, b.name));
    dispatch(receiveSucceeded(type, res));
    return res;
  });
};

export const handleFetchDrawingsInPackage = (packageId) => (dispatch) => {
  const type = "DRAWINGS_IN_PACKAGE";

  dispatch(receiveStarted(type));
  return fetchDrawings(null, [packageId], true).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchDrawings = (jobId) => (dispatch) => {
  const type = "WIZARD_DRAWINGS";

  dispatch(receiveStarted(type));
  return fetchDrawings([jobId], null, true).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchPackageCostCodes = (pkgIds = []) => (dispatch) => {
  const type = "WIZARD_PACKAGE_COST_CODES";

  dispatch(receiveStarted(type));
  return fetchCostCodes(pkgIds, 0, 1).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else {
      res.sort((a, b) => {
        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;
        else return -1;
      });

      dispatch(receiveSucceeded(type, res));
    }

    return res;
  });
};

export const handleFetchPackageCostCodesSpecificPackage = (pkgId) => (
  dispatch
) => {
  const type = "WIZARD_PACKAGE_COST_CODES_SPECIFIC_PACKAGE";

  dispatch(receiveStarted(type));
  return fetchCostCodes([pkgId], 0, 1).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else {
      res.sort((a, b) => {
        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;
        else return -1;
      });

      dispatch(receiveSucceeded(type, res));
    }

    return res;
  });
};

export const handleFetchFlows = (dispatch) => {
  const type = "WIZARD_FLOWS";

  dispatch(receiveStarted(type));
  return fetchFlows().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleUpdateJobCostCodes = (
  jobId,
  action,
  costCodes,
  noAdditional = false,
  addedViaCreation = false
) => (dispatch) => {
  const type = `WIZARD_${
    action === "add" && !noAdditional
      ? "ADDITIONAL"
      : action === "remove"
      ? "REMOVE"
      : action === "update"
      ? "UPDATE"
      : ""
  }_JOB_COST_CODES`;

  return updateJobCostCodes(jobId, action, costCodes, addedViaCreation).then(
    (res) => {
      if (res && res[2] && res[2].length) {
        dispatch(receiveSucceeded(type, res[2]));
      }

      return res;
    }
  );
};

export const handleUpdatePackageCostCodes = (pkgId, action, costCodes) => (
  dispatch
) => {
  return updatePackageCostCodes(pkgId, action, costCodes);
};

export const handleUpdateGeneralCostCode = (id, data) => (dispatch) => {
  const type = "COST_CODES";

  dispatch(updateStarted(type));
  return updateGeneralCostCode(id, data).then((res) => {
    if (res.error) dispatch(updateFailed(type));
    else dispatch(updateSucceeded(type, res));

    return res;
  });
};

export const handleCreateCostCodes = (data) => (dispatch) => {
  const type = "WIZARD_ADDITIONAL_COST_CODES";

  return createCostCodes(data).then((res) => {
    if (!res.error) dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleCreateCostCodeType = (name) => (dispatch) => {
  const type = "COST_CODE_TYPES";

  dispatch(updateStarted(type));
  return createCostCodeType(name).then((res) => {
    if (res.error) dispatch(updateFailed(type, res));
    else dispatch(updateSucceeded(type));

    return res;
  });
};

export const handleUpdateCostCodeType = (id, isActive) => (dispatch) => {
  const type = "COST_CODE_TYPES";

  dispatch(updateStarted(type));
  return updateCostCodeType(id, isActive).then((res) => {
    if (res.error) dispatch(updateFailed(type, res));
    else dispatch(updateSucceeded(type));

    return res;
  });
};

export const handleDeleteCostCodeType = (id) => (dispatch) => {
  const type = "COST_CODE_TYPES";

  return deleteCostCodeType(id).then((res) => {
    if (res.error) dispatch(updateFailed(type, res));
    else dispatch(updateSucceeded(type));

    return res;
  });
};

export const handleFetchCostCodeTypes = (includeInactive) => (dispatch) => {
  const type = "COST_CODE_TYPES";

  dispatch(receiveStarted(type));
  return fetchCostCodeTypes(includeInactive).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleDeleteGeneralCostCode = (id) => (dispatch) => {
  return deleteGeneralCostCode(id).then((res) => {
    if (res.error) return;

    dispatch(handleFetchCostCodes(null, 1, 1));
  });
};

export const handleFetchUsers = (dispatch) => {
  const type = "WIZARD_ALL_USERS";

  dispatch(receiveStarted(type));
  return fetchUsers({ includeInactive: false }).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};
