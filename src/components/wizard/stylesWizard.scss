@import "../styles/colors.scss";

div.wizard-header {
  background-color: $fabProBlue;
  display: flex;
  align-content: flex-start;
  align-items: center;
  padding: 15px 0 15px 15px;

  & > h1.wizard-step-title {
    flex: 1;
    margin: 0 10px 0 0;
    height: 1.6rem;
    line-height: 1.6rem;
    color: #fff;
    font-size: 1.2rem;
    font-weight: 300;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
  }

  & > div.link-wrapper {
    display: flex;
    justify-content: space-between;
    width: 300px;
    align-self: flex-end;
    margin-right: 15px;

    & > a {
      color: #fff;
      &:hover {
        color: $lighterSlate;
      }
    }
  }

  & > div.button-wrapper {
    display: flex;
    justify-content: space-between;
    align-self: flex-end;
    margin-right: 15px;
    column-gap: 5px;

    & > button,
    & > a {
      font-size: 0.8rem;
      padding: 0 10px;
      height: 30px;
      line-height: 0.8rem;
      background-color: $darkFab;
      color: #1997c6;

      &:not(:disabled):hover {
        color: #fff;
      }
    }

    & > button.cancel-job {
      background-color: $redWashDark;
      color: #fff;
    }

    & > a {
      display: flex;
      align-items: center;
      text-decoration: none;
    }
  }
}

div.wizard-content {
  padding: 15px 30px;

  &.tall {
    height: calc(100vh - 55px - 80px - 33px - 50px - 160px);
    overflow-y: scroll;
  }

  & .toggle-switch .toggle-switch-label .toggle-switch-inner::after {
    background-color: #fff;
    color: $darkGrey;
  }

  & div.pdf-viewer-wrapper .viewer {
    height: calc(100vh - 110px);
  }

  & div.job-info,
  div.cost-codes,
  div.packages,
  div.packages-inputs,
  div.additional-package-info,
  div.jobs-inputs {
    display: grid;
    align-items: center;
    column-gap: 15px;

    & .wizard-content-label {
      display: grid;
      box-sizing: border-box;
      height: 60px;
      color: $lighterSlate;
      font-weight: 600;
    }

    &
      > .wizard-content-label:not(.date-picker)
      > div:not(.toggle-switch):not(.selectable-filter-wrapper) {
      height: 30px;

      & > div {
        height: 30px;

        & > select {
          font-size: 0.8rem;
          height: 30px;
        }
      }

      & input,
      textarea {
        font-size: 0.8rem;
        height: 30px;
      }
    }

    & .toggle-switch-switch {
      margin: 0;
      height: 24px;
      top: 1px;
      right: 50px;
    }
  }
}

div.duplicate-drawing-container {
  background-color: #fff;
  display: grid;
  grid-template-rows: 30px 1fr 30px;
  row-gap: 10px;
  padding-bottom: 20px;
  height: 150px;
  width: 300px;

  & > h2.title {
    background-color: $fabProBlue;
    color: #fff;
    font-size: 1rem;
    margin: 0;
    padding: 0 10px;
    line-height: 30px;
  }

  & > div.content {
    padding: 0 30px;
  }

  & > div.content > label.duplication-quantity > div > input {
    height: 30px;
    font-size: 1.2rem;
  }

  & > div.content > label.duplication-quantity > div > svg {
    top: 35%;
  }

  & > div.buttons {
    display: flex;
    justify-content: space-evenly;

    & > button {
      height: 30px;
      padding: 0 10px;
      font-size: 1rem;
    }

    & > button.cancel {
      background-color: #fff;
      color: #333;
      border: 1px solid #333;

      &:hover {
        background-color: darken(#fff, 10%);
      }
    }

    & > button.submit {
      background-color: $green;
      border: 1px solid #333;

      &:not(:disabled):hover {
        background-color: darken($green, 10%);
      }
    }
  }
}

div.upload-drawings-container {
  background-color: #fff;
  display: grid;
  grid-template-rows: 30px 1fr 30px;
  row-gap: 10px;
  padding-bottom: 20px;
  max-height: 500px;
  width: 500px;

  & > h2.title {
    background-color: $fabProBlue;
    color: #fff;
    font-size: 1rem;
    margin: 0;
    padding: 0 10px;
    line-height: 30px;
  }

  & > div.content {
    padding: 0 30px;
    display: grid;
    row-gap: 10px;
    grid-template-rows: 50px 50px 1fr 30px 50px;
  }

  & > div.content > label.packages > div,
  & > div.content > label.default-material > div {
    height: 30px;

    & > div {
      height: 30px;

      & > select {
        height: 30px;
        font-size: 1rem;
      }
    }
  }

  & > div.content > label.pdfs > div > svg {
    top: 35%;
  }

  & > div.content > label.pdfs > div > input {
    height: 30px;
    font-size: 1.2rem;
  }

  & > div.content > p.invalid-pdf-names {
    margin: 0;
    max-height: 200px;
    overflow-y: scroll;
  }

  & > div.content > p.invalid-pdf-names > ul > li {
    font-weight: 600;
  }

  & > div.content > p.invalid-pdf-names > ul > li > span.invalid-character {
    color: #ff4d39;
  }

  & > div.content > label.create-work-items {
    display: flex;
    align-items: center;

    & > input[type="checkbox"] {
      height: 15px;
      width: 15px;
    }
  }

  & > div.buttons {
    display: flex;
    justify-content: space-evenly;

    & > button {
      height: 30px;
      padding: 0 10px;
      font-size: 1rem;

      display: flex;
      align-items: center;
      justify-content: center;
      column-gap: 5px;
    }

    & > button.cancel {
      background-color: #fff;
      color: #333;
      border: 1px solid #333;

      &:hover {
        background-color: darken(#fff, 10%);
      }
    }

    & > button.submit {
      background-color: $green;
      color: #fff;
      border: 1px solid #333;

      &:not(:disabled):hover {
        background-color: darken($green, 10%);
      }
    }
  }
}
