// NPM PACKAGE IMPORTS
import React, { useState, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";

import Input from "msuite_storybook/dist/input/Input";
import Select from "msuite_storybook/dist/select/Select";
import Toggle from "msuite_storybook/dist/toggle/Toggle";
import Button from "msuite_storybook/dist/button/Button";

// REDUX IMPORTS
import {
  handleCreateCostCodes,
  handleFetchCostCodes,
  handleFetchJobSpecificCostCodes,
  handleUpdateJobCostCodes,
} from "../wizardActions";

const CostCodesInputs = ({ selectedJobId }) => {
  const [codeValue, setCodeValue] = useState("");
  const [general, toggleGeneral] = useState(false);
  const [area, setArea] = useState("");
  const [description, setDescription] = useState("");
  const [budgetHours, setBudgetHours] = useState("");
  const [budgetedCost, setBudgetedCost] = useState("");
  const [type, setType] = useState(null);

  const dispatch = useDispatch();
  const { costCodeTypes } = useSelector((state) => state.wizardData);

  const selectedCostCodeTypeId = useMemo(() => {
    if (!type || !costCodeTypes || !costCodeTypes.length) return null;

    return costCodeTypes.find((c) => c.name === type).id || null;
  }, [type, costCodeTypes]);

  const handleNewCostCode = () => {
    const data = {
      name: codeValue,
      is_general: general ? 1 : 0,
      ...(description && { description: description }),
    };
    dispatch(handleCreateCostCodes([data])).then((res) => {
      if (res.error) return;

      if (general) {
        return dispatch(handleFetchCostCodes(null, 1, 1));
      }

      const jobCodeData = {
        cost_code_id: res[2][0].id,
        ...(budgetHours && { budget_hours: parseFloat(budgetHours) }),
        ...(budgetedCost && { budgeted_cost: parseFloat(budgetedCost) }),
        ...(area && { area: area }),
        ...(type && { type_id: selectedCostCodeTypeId }),
        ...(description && { description: description }),
      };

      dispatch(
        handleUpdateJobCostCodes(
          selectedJobId,
          "add",
          [jobCodeData],
          true,
          true
        )
      ).then((res) => {
        if (res.error) return;
        dispatch(handleFetchJobSpecificCostCodes(selectedJobId));
      });
    });

    setCodeValue("");
    setArea("");
    setDescription("");
    setBudgetHours("");
    setBudgetedCost("");
    setType(null);
  };

  const f_costCodeTypes = useMemo(() => {
    if (!costCodeTypes || !costCodeTypes.length) return [];
    return costCodeTypes
      .filter((c) => c.active)
      .map((c) => ({
        display: c.name,
        value: c.name,
      }));
  }, [costCodeTypes]);

  return (
    <>
      <label className="wizard-content-label cost-code">
        <span>Code</span>
        <Input
          placeholder="Cost Code"
          value={codeValue}
          onChange={(e) => setCodeValue(e.target.value)}
          required
        />
      </label>
      <label className="wizard-content-label">
        <span>Area</span>
        <Input
          placeholder="Area"
          value={area}
          onChange={(e) => setArea(e.target.value)}
        />
      </label>
      <label className="wizard-content-label">
        <span>Description</span>
        <Input
          placeholder="Description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
        />
      </label>
      <label className="wizard-content-label">
        <span>Budget Hours</span>
        <Input
          placeholder="Budget Hours"
          value={budgetHours}
          onChange={(e) => setBudgetHours(e.target.value)}
          type="number"
        />
      </label>
      <label className="wizard-content-label">
        <span>Budgeted Cost</span>
        <Input
          placeholder="Budgeted Cost"
          value={budgetedCost}
          onChange={(e) => setBudgetedCost(e.target.value)}
          type="number"
        />
      </label>
      <label className="wizard-content-label">
        <span>Type</span>
        <Select
          options={f_costCodeTypes}
          placeholder="Select Cost"
          value={type ? type : null}
          onInput={(e) => setType(e.target.value)}
        />
      </label>
      <div className="wizard-content-label">
        <span>General?</span>
        <Toggle
          name="general-toggle"
          text={["yes", "no"]}
          defaultChecked={general}
          onToggleChanged={() => toggleGeneral(!general)}
        />
      </div>
      <label className="wizard-content-label">
        <span>Action</span>
        <Button
          onClick={handleNewCostCode}
          disabled={!codeValue || !codeValue.trim()}
        >
          Add Cost Code
        </Button>
      </label>
    </>
  );
};

export default CostCodesInputs;
