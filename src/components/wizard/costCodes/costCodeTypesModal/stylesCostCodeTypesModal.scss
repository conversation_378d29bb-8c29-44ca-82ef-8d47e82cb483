@import "../../../styles/colors.scss";

div.cost-code-types-wrapper {
  height: 600px;
  width: 800px;
  padding: 5px;

  div.create-type-wrapper {
    display: flex;
    height: 40px;
    justify-content: flex-start;

    & input {
      font-size: 0.8rem;
      height: 30px;
      width: 400px;
    }
    & button {
      height: 30px;
      margin-left: 10px;
      font-size: 0.8rem;
      color: white;
      background-color: $fabProBlue;
      padding: 0 5px;
    }
  }

  & div.type-table-container {
    margin-top: 10px;
    & input {
      width: 200px;
      height: 30px;
      font-size: 0.8rem;
      // margin-bottom: 2px;
    }

    & button.action-button {
      width: 100%;
      background-color: $red;
      color: white;
      border: none;
    }

    & div.custom-ag-styles.ag-theme-balham-dark {
      width: 100%;
      height: calc(600px - 95px);
    }
  }
}

section.modal-container .modal-content {
  padding: 0px;
}
