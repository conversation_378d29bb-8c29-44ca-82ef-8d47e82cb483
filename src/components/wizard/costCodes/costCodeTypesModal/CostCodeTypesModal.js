// NPM PACKAGE IMPORTS
import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import Input from "msuite_storybook/dist/input/Input";
import Button from "msuite_storybook/dist/button/Button";

// REDUX IMPORTS
import {
  handleCreateCostCodeType,
  handleDeleteCostCodeType,
  handleUpdateCostCodeType,
  handleFetchCostCodeTypes,
} from "./../../wizardActions";

// COMPONENT IMPORTS
import AgTable from "../../../reusable/agTable/AgTable";
import Modal from "../../../reusable/modal/Modal";
import ToggleCellEditor from "../../../reusable/frameworkComponents/ToggleCellEditor";
import ActionCellRenderer from "../../../reusable/frameworkComponents/ActionCellRenderer";
import ConfirmationModal from "../../../reusable/confirmationModal/ConfirmationModal";

// STYLES IMPORTS
import "./stylesCostCodeTypesModal.scss";

const columnDefs = (handleDeleteClick) => {
  return [
    {
      headerName: "ID",
      field: "id",
      getQuickFilterText: (params) => params.data.id,
      cellClass: "bold",
      sortable: true,
      filter: "agTextColumnFilter",
      menuTabs: ["filterMenuTab"],
      colId: "id",
    },
    {
      headerName: "Cost Code Type",
      field: "name",
      valueParser: (params) => {
        if (params.oldValue === "" || params.newValue === undefined)
          return params.oldValue;

        return params.newValue;
      },
      getQuickFilterText: (params) => params.data.name,
      sortable: true,
      filter: false,
      suppressMenu: true,
      colId: "name",
    },
    {
      headerName: "Active",
      field: "active",
      valueGetter: (params) => (params.data.active ? true : false),
      cellRendererParams: (params) => {
        return {
          rowHeight: params.node.rowHeight,
          even: params.node.childIndex % 2 === 0,
          value: params.value ? true : false,
          setValue: params.setValue,
        };
      },
      sortable: true,
      filter: false,
      cellRenderer: "toggleCellEditor",
      suppressMovable: true,
      suppressMenu: true,
      colId: "active",
    },
    {
      headerName: "Delete",
      valueFormatter: (params) => ({
        actions: [
          {
            action: (e) => handleDeleteClick(e, params.data),
            actionName: "Delete",
          },
        ],
      }),
      sortable: false,
      suppressMenu: true,
      filter: false,
      cellRenderer: "actionCellRenderer",
      // pinned: "right",
      suppressMovable: true,
      colId: "delete",
    },
  ];
};

const CostCodeTypesModal = ({ showModal, handleClose }) => {
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [newCostCodeInput, setNewCostCodeInput] = useState("");
  const [searchInput, setSearchInput] = useState("");
  const [showConfirmationModal, toggleConfirmationModal] = useState(false);
  const [selectedCostCodeType, setSelectedCostCodeType] = useState(null);

  const { costCodeTypes } = useSelector((state) => state.wizardData);

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(handleFetchCostCodeTypes(true));
  }, []);

  useEffect(() => {
    if (!gridOptionsApi || !costCodeTypes) return;
    gridOptionsApi.setRowData(costCodeTypes);
  }, [costCodeTypes, gridOptionsApi]);

  const handleCostCodeTypeCreation = () => {
    dispatch(handleCreateCostCodeType(newCostCodeInput)).then((res) => {
      if (res.error) return;
      dispatch(handleFetchCostCodeTypes(true));
    });
    setNewCostCodeInput("");
  };

  const handleCostCodeTypeDeletion = () => {
    toggleConfirmationModal(false);
    dispatch(handleDeleteCostCodeType(selectedCostCodeType.id)).then((res) => {
      if (res.error) return;
      dispatch(handleFetchCostCodeTypes(true));
    });
  };

  const handleDeleteClick = (e, costCodeType) => {
    e.preventDefault();
    toggleConfirmationModal(true);
    setSelectedCostCodeType(costCodeType);
  };

  useEffect(() => {
    if (!gridOptionsApi) return;
    gridOptionsApi.setQuickFilter(searchInput);
    gridOptionsApi.redrawRows();
  }, [searchInput, gridOptionsApi]);

  const onCellValueChanged = (params) => {
    dispatch(handleUpdateCostCodeType(params.data.id, params.newValue)).then(
      (res) => {
        if (res.error) return;
        dispatch(handleFetchCostCodeTypes(true));
      }
    );
  };

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
  };

  const onSortChanged = (params) => {
    params.api.redrawRows();
  };

  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };

  const gridOptions = {
    rowData: [],
    defaultColDef: {
      wrapText: true,
      cellClass: ["no-border", "custom-wrap"],
    },
    columnDefs: columnDefs(handleDeleteClick),
    frameworkComponents: {
      actionCellRenderer: ActionCellRenderer,
      toggleCellEditor: ToggleCellEditor,
    },
    rowHeight: 60,
    autoHeight: true,
    rowClassRules,
    onSortChanged,
    onGridReady,
    onCellValueChanged,
    suppressRowClickSelection: true,
    getRowNodeId: (data) => data.id,
  };

  return (
    <>
      <Modal
        title="Manage Cost Code Types"
        showModal={showModal}
        onClose={() => handleClose(false)}
        suppressForm
        showExit
        // onSubmit={() => null}
      >
        <div className="cost-code-types-wrapper">
          <div className="create-type-wrapper">
            <Input
              placeholder="Cost Code Type"
              value={newCostCodeInput}
              onChange={(e) => setNewCostCodeInput(e.target.value)}
            />
            <Button
              onClick={handleCostCodeTypeCreation}
              disabled={!newCostCodeInput}
            >
              Add Cost Code Type
            </Button>
          </div>
          <div className="type-table-container">
            <Input
              placeholder="Search"
              onChange={(e) => setSearchInput(e.target.value)}
            />
            <AgTable gridOptions={gridOptions} />
          </div>
        </div>
        {showConfirmationModal && (
          <ConfirmationModal
            toggleModal={toggleConfirmationModal}
            item={selectedCostCodeType ? selectedCostCodeType.name : ""}
            action="DELETE"
            handleClick={() => handleCostCodeTypeDeletion(selectedCostCodeType)}
          />
        )}
      </Modal>
    </>
  );
};

export default CostCodeTypesModal;
