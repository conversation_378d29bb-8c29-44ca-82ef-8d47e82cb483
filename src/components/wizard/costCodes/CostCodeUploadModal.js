// NPM PACKAGE IMPORTS
import React, { useState, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import CSVImport from "msuite_storybook/dist/csvImport/CSVImport";

// REDUX IMPORTS
import {
  handleCreateCostCodes,
  handleUpdateJobCostCodes,
  handleFetchCostCodes,
  handleFetchJobSpecificCostCodes,
} from "../wizardActions";
import { notify } from "../../reusable/alertPopup/alertPopupActions";

// HELPER FUNCTION IMPORTS
import { buildCSV } from "../../../_utils";

// FILE IMPORTS
import CSV_EXAMPLE from "../../../assets/EXAMPLE_COST_CODES_IMPORT.png";

// STYLES IMPORTS
import "./stylesCostCodes.scss";

const costCodeHeaders = {
  "Cost Code": "name",
  Area: "area",
  Description: "description",
  "Budgeted Hours": "budget_hours",
  "Budgeted Cost": "budgeted_cost",
  Type: "type",
  "Make General? (0 or 1)": "is_general",
};

const CostCodeUploadModal = ({ showModal, handleClose, selectedJobId }) => {
  const [csvData, setCsvData] = useState(null);
  const [csvError, setCsvError] = useState(null);
  const [receivedHeaders, setReceivedHeaders] = useState(null);

  const { costCodeTypes } = useSelector((state) => state.wizardData);

  const dispatch = useDispatch();

  const costCodeCSVTemplate = useMemo(() => {
    return buildCSV(Object.keys(costCodeHeaders));
  }, []);

  const handleSubmit = async () => {
    const headers = csvData[0];
    const newItems = csvData.slice(1);

    let results = [];
    for (let i = 0; i < newItems.length; i++) {
      let obj = {};
      for (let j = 0; j < headers.length; j++) {
        obj[costCodeHeaders[headers[j]]] = newItems[i][j];
      }
      results.push(obj);
    }

    const jobSpecificCostCodes = results.filter((c) => !c.is_general);

    const generalData = results.map((r) => ({
      name: r.name.toString().trim(),
      is_general: r.is_general,
      ...(r.description && { description: r.description.trim() }),
    }));

    const jobSpecificData = results.map((r) => ({
      name: r.name.toString().trim(),
      ...(r.area && { area: r.area.trim() }),
      ...(r.description && { description: r.description.trim() }),
      ...(r.budget_hours && { budget_hours: r.budget_hours }),
      ...(r.budgeted_cost && { budgeted_cost: r.budgeted_cost }),
      ...(r.type && { type: r.type }),
    }));

    dispatch(handleCreateCostCodes(generalData))
      .then((res) => {
        if (res.error) return;

        let costCodesToAddToJob = res[2].filter((c) => !c.general_code);

        if (costCodesToAddToJob && costCodesToAddToJob.length) {
          let jobData = [];

          costCodesToAddToJob.forEach((c) => {
            let jobSpecificCostCode = jobSpecificData.find(
              (jcc) => jcc.name.toLowerCase() === c.name.toLowerCase()
            );

            if (jobSpecificCostCode && jobSpecificCostCode.type) {
              let costCodeType = costCodeTypes.find(
                (type) =>
                  type.name.toLowerCase() ===
                  jobSpecificCostCode.type.trim().toLowerCase()
              );

              if (!costCodeType) {
                dispatch(
                  notify({
                    id: Date.now(),
                    type: "WARN",
                    message: "Unable to find cost code type.",
                  })
                );
              } else {
                jobSpecificCostCode.type_id = costCodeType.id;
              }
            }

            let costCodeData;
            if (jobSpecificCostCode) {
              costCodeData = Object.assign({}, jobSpecificCostCode);
              costCodeData.cost_code_id = c.id;
              delete costCodeData.type;
              delete costCodeData.name;
            }

            jobData.push(costCodeData);
          });

          dispatch(
            handleUpdateJobCostCodes(selectedJobId, "add", jobData, true, true)
          );
        }
      })
      .then(() => {
        dispatch(handleFetchCostCodes(null, 1, 1));
        if (jobSpecificCostCodes && jobSpecificCostCodes.length) {
          dispatch(handleFetchJobSpecificCostCodes(selectedJobId));
        }

        handleClose(false);
      });
  };

  const getResults = ({ data }) => {
    setCsvData(data);
    const receivedHeaders = data[0];
    const receivedData = data.slice(1);
    setReceivedHeaders(receivedHeaders.filter((h) => h));

    let results = [];
    for (let i = 0; i < receivedData.length; i++) {
      let dataObj = {};
      for (let j = 0; j < receivedHeaders.length; j++) {
        if (receivedHeaders[j]) {
          dataObj[costCodeHeaders[receivedHeaders[j]]] = receivedData[i][j];
        }
      }
      results.push(dataObj);
    }

    return results;
  };

  return (
    <CSVImport
      showCSVModal={showModal}
      toggleCSVModal={handleClose}
      csvError={csvError}
      setCSVError={setCsvError}
      submitText="Submit"
      title="Upload Cost Codes"
      getResults={getResults}
      headers={receivedHeaders}
      images={[CSV_EXAMPLE]}
      templatePath={costCodeCSVTemplate}
      download="fabpro_cost_codes_template.csv"
      handleSubmit={handleSubmit}
    />
  );
};

export default CostCodeUploadModal;
