@import "../../styles/colors.scss";

div.cost-codes {
  grid-template-columns: repeat(6, 1fr) 80px 1fr;
  row-gap: 30px;
  padding-bottom: 30px;

  & .wizard-content-label:last-of-type {
    text-align: center;

    & button {
      height: 30px;
      font-size: 0.8rem;
      padding: 0;
      background-color: $fabProBlue;
      border: 1px solid $darkGrey;
      color: white;
      width: 80%;
      min-width: 120px;
      margin: 0 auto;

      &:not(:disabled):hover {
        background-color: darken($fabProBlue, 10%);
      }
    }
  }

  & div.cost-codes-tabs {
    grid-column: 1/-1;
    padding: 0px;

    & div.custom-ag-styles.ag-theme-balham-dark {
      width: 100%;
      height: calc(100vh - 120px - 32px - 155px - 55.5px - 125px) !important;

      & .ag-body-viewport {
        overflow-y: scroll !important;

        & .ag-cell-inline-editing {
          border: none;
        }
      }
    }
  }
}

div.cost-code-upload-wrapper {
  height: 300px;
  width: 400px;
}
