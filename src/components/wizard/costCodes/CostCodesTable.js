// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useMemo, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import styled from "styled-components";

// REDUX IMPORTS
import {
  handleFetchCostCodes,
  handleFetchJobSpecificCostCodes,
  handleUpdateGeneralCostCode,
  handleFetchCostCodeTypes,
  handleUpdateJobCostCodes,
  handleDeleteGeneralCostCode,
} from "../wizardActions";

// CONSTANTS IMPORTS
import { costCodesColumns as columns } from "../wizardConstants";

// COMPONENT IMPORTS
import AgTable from "../../reusable/agTable/AgTable";
import ToggleCellEditor from "../../reusable/frameworkComponents/ToggleCellEditor";
import ActionCellRenderer from "../../reusable/frameworkComponents/ActionCellRenderer";
import TextInputCellRenderer from "../../reusable/frameworkComponents/TextInputCellRenderer";
import TableContainer from "../../reusable/tableContainer/TableContainer";
import ConfirmationModal from "../../reusable/confirmationModal/ConfirmationModal";

// HELPER FUNCTION IMPORTS
import { permissionLock } from "../../../_utils";

const CostCodesTableDiv = styled.div`
  & div.ag-theme-balham-dark.custom-ag-styles {
    height: ${(props) =>
      100 +
      60 *
        (props.displayedCostCodes
          ? Math.min(props.displayedCostCodes.length, 10)
          : 0)}px !important;
  }
`;

const CostCodesTable = ({ selectedJobId }) => {
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [displayedCostCodes, setDisplayedCostCodes] = useState(null);
  const [viewingGeneral, toggleGeneral] = useState(true);
  const [searchInput, setSearchInput] = useState("");
  const [showConfirmationModal, toggleConfirmationModal] = useState(false);
  const [selectedCostCode, setSelectedCostCode] = useState(null);

  const dispatch = useDispatch();
  const { generalCostCodes, jobCostCodes, costCodeTypes } = useSelector(
    (state) => state.wizardData
  );

  const viewingGeneralRef = useRef();
  const costCodeTypesRef = useRef();

  useEffect(() => {
    dispatch(handleFetchCostCodes(null, 1, 1));
    dispatch(handleFetchJobSpecificCostCodes(selectedJobId));
    dispatch(handleFetchCostCodeTypes());
  }, [selectedJobId, dispatch]);

  useEffect(() => {
    if (!costCodeTypes || !costCodeTypes.length) costCodeTypesRef.current = [];
    else costCodeTypesRef.current = costCodeTypes;
  }, [costCodeTypes]);

  useEffect(() => {
    if (!viewingGeneral) {
      setDisplayedCostCodes(jobCostCodes);
      viewingGeneralRef.current = false;
    } else {
      setDisplayedCostCodes(generalCostCodes);
      viewingGeneralRef.current = true;
    }
  }, [viewingGeneral, generalCostCodes, jobCostCodes]);

  useEffect(() => {
    if (!gridOptionsApi || !displayedCostCodes) return;
    gridOptionsApi.setRowData(displayedCostCodes);
  }, [displayedCostCodes, gridOptionsApi]);

  const handleRemoveCostCode = (costCode) => {
    toggleConfirmationModal(true);
    setSelectedCostCode(costCode);
  };

  const handleRemoveConfirmation = (id) => {
    // if general cost code, remove from system
    if (viewingGeneral) {
      // cost code refresh handled in action
      dispatch(handleDeleteGeneralCostCode(id));
    } else {
      // remove cost code from job
      dispatch(
        handleUpdateJobCostCodes(selectedJobId, "remove", [
          { cost_code_id: id },
        ])
      ).then((res) => {
        if (res.error) return;
        dispatch(handleFetchJobSpecificCostCodes(selectedJobId));
      });
    }

    toggleConfirmationModal(false);
  };

  const selectedCostCodeId = useMemo(() => {
    if (!selectedCostCode || !displayedCostCodes || !displayedCostCodes.length)
      return null;

    if (!viewingGeneral) {
      return (
        displayedCostCodes.find((c) => c.name === selectedCostCode)
          .cost_code_id || null
      );
    }
    return (
      displayedCostCodes.find((c) => c.name === selectedCostCode).id || null
    );
  }, [selectedCostCode]);

  const onCellValueChanged = (params) => {
    if (!params.newValue && !params.oldValue) return;
    if (params.newValue !== params.oldValue) {
      // general toggle changed
      if (params.colDef.field === "general_code") {
        if (viewingGeneralRef.current) {
          dispatch(
            handleUpdateGeneralCostCode(params.data.id, { is_general: 0 })
          ).then((res) => {
            if (res.error) return;

            const data = {
              cost_code_id: params.data.id,
              description: params.data.description,
            };

            dispatch(
              handleUpdateJobCostCodes(selectedJobId, "add", [data], true)
            ).then((res) => {
              if (res.error) return;

              dispatch(handleFetchJobSpecificCostCodes(selectedJobId));
              dispatch(handleFetchCostCodes(null, 1, 1));
            });
          });
        } else {
          // updating cost code to be general = 1 should automatically remove from job - also need to grab cost_code_id instead of id
          dispatch(
            handleUpdateGeneralCostCode(params.data.cost_code_id, {
              is_general: 1,
            })
          ).then((res) => {
            if (res.error) return;
            dispatch(handleFetchJobSpecificCostCodes(selectedJobId));
            dispatch(handleFetchCostCodes(null, 1, 1));
          });
        }

        return;
      }

      if (viewingGeneralRef.current) {
        // general editing
        dispatch(
          handleUpdateGeneralCostCode(params.data.id, {
            [params.colDef.field]: params.newValue,
          })
        ).then((res) => {
          if (res.error) return;
          dispatch(handleFetchCostCodes(null, 1, 1));
        });
      } else {
        // job specific
        if (params.colDef.field === "type_name") {
          const newTypeId =
            costCodeTypesRef.current.find((c) => c.name === params.newValue)
              .id || null;

          dispatch(
            handleUpdateJobCostCodes(selectedJobId, "update", [
              { cost_code_id: params.data.cost_code_id, type_id: newTypeId },
            ])
          );
        } else
          dispatch(
            handleUpdateJobCostCodes(selectedJobId, "update", [
              {
                cost_code_id: params.data.cost_code_id,
                [params.colDef.field]: params.newValue,
              },
            ])
          );
      }
    }
  };

  const permissionLockedColumns = useMemo(() => {
    return permissionLock(
      columns(
        handleRemoveCostCode,
        costCodeTypes ? costCodeTypes.filter((c) => c.active) : []
      )
    );
  }, [costCodeTypes]);

  useEffect(() => {
    if (!gridOptionsApi || !permissionLockedColumns) return;

    gridOptionsApi.setColumnDefs(permissionLockedColumns);
  }, [permissionLockedColumns, gridOptionsApi]);

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
  };

  const onSortChanged = (params) => {
    params.api.redrawRows();
  };

  const exportParams = useMemo(() => {
    if (!permissionLockedColumns || !permissionLockedColumns.length) return;

    const exportedColumns = permissionLockedColumns
      .filter((col) => col.field && !col.hide)
      .map((col) => col.field);

    return {
      columnKeys: exportedColumns,
      fileName: "cost-codes",
      processCellCallback: (params) => {
        if (typeof params.value === "number" && params.column.colId !== "id") {
          return params.value.toFixed(2);
        } else return params.value;
      },
    };
  }, [permissionLockedColumns]);

  const handleExcelExport = () => {
    if (gridOptionsApi) gridOptionsApi.exportDataAsExcel(exportParams);
  };
  const handleCSVExport = () => {
    if (gridOptionsApi) gridOptionsApi.exportDataAsCsv(exportParams);
  };

  useEffect(() => {
    if (!gridOptionsApi) return;
    gridOptionsApi.setQuickFilter(searchInput);
    gridOptionsApi.redrawRows();
  }, [searchInput, gridOptionsApi]);

  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };

  const gridOptions = {
    rowData: displayedCostCodes,
    defaultColDef: {
      wrapText: true,
      cellClass: ["no-border", "custom-wrap"],
    },
    columnDefs: permissionLockedColumns,
    frameworkComponents: {
      keepAsGeneralCellRenderer: ToggleCellEditor,
      actionCellRenderer: ActionCellRenderer,
      textInputCellRenderer: TextInputCellRenderer,
    },
    rowClassRules,
    onSortChanged,
    onGridReady,
    onCellValueChanged,
    suppressRowClickSelection: true,
    editType: "fullRow",
    stopEditingWhenGridLosesFocus: true,
    tabToNextCell: () => null,
    getRowNodeId: (data) => data.id,
  };

  const tabs = [
    { name: "General", selected: viewingGeneral },
    { name: "Job Specific", selected: !viewingGeneral },
  ];

  return (
    <>
      <TableContainer
        tabs={tabs}
        handleToggle={() => toggleGeneral(!viewingGeneral)}
        currentTable="Cost Codes"
        searchInput={searchInput}
        setSearchInput={setSearchInput}
        selectedRows={[]}
        handleExcel={handleExcelExport}
        handleCSV={handleCSVExport}
      >
        <CostCodesTableDiv
          className="cost-codes-table"
          displayedCostCodes={displayedCostCodes}
        >
          {displayedCostCodes ? <AgTable gridOptions={gridOptions} /> : <></>}
        </CostCodesTableDiv>
      </TableContainer>
      {showConfirmationModal && (
        <ConfirmationModal
          toggleModal={toggleConfirmationModal}
          item={selectedCostCode}
          action="DELETE"
          handleClick={() => handleRemoveConfirmation(selectedCostCodeId)}
        />
      )}
    </>
  );
};

export default CostCodesTable;
