@import "../../styles/colors.scss";

div.layout-footer {
  user-select: none;
  display: flex !important;
  justify-content: space-between;
  width: 100vw;
  padding: 0 120px;

  @media (max-width: 800px) {
    padding: 0 70px;
  }

  & div.info-container {
    display: flex;
    align-items: center;

    & p {
      font-size: 0.8rem;
      color: #555;
    }
    & span.logout {
      font-size: 0.8rem;
      margin-left: 5px;
    }
  }
  & span.footer-link,
  a.footer-link {
    cursor: pointer;
    transition: color 0.3s ease;
    color: #555;

    &.logout {
      color: #fff;
    }

    &:not(.logout) {
      text-decoration: underline;
    }

    &:hover {
      color: #fff;
    }
  }

  & div.footer-link-container {
    display: flex;
    align-items: center;
    color: #555;

    & > a,
    span {
      font-size: 0.8rem;
    }
    & span {
      color: #555;
      margin: 0 5px;
    }
  }
}
