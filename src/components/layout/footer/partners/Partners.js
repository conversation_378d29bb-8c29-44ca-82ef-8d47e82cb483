// NPM PACKAGE IMPORTS
import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import Button from "msuite_storybook/dist/button/Button";
import Modal from "msuite_storybook/dist/modal/Modal";

// REDUX IMPORTS
import {
  handleFetchPartners,
  handleFetchPrimaryPartnerSessions,
  handleCreatePrimaryPartnerSession,
  handleFetchMyPartnerSessions,
  handleEndPrimaryPartnerSession,
} from "./partnersActions";

// COMPONENT IMPORTS
import PartnersModal from "./PartnersModal";

// TRANSLATION IMPORTS
import useTranslations from "../../../../hooks/useTranslations";
import partnerTranslations from "./partnerTranslations.json";

// STYLE IMPORTS
import "./stylesPartners.scss";

// EXPORTS
const Partners = () => {
  const [showModal, setShowModal] = useState(false);
  const translate = useTranslations(partnerTranslations);

  const dispatch = useDispatch();
  const { role } = useSelector((state) => state.profileData);

  useEffect(() => {
    if (showModal) {
      dispatch(handleFetchPartners());
      dispatch(handleFetchPrimaryPartnerSessions());
      dispatch(handleFetchMyPartnerSessions());
    }
  }, [showModal]);

  const toggleModal = () => setShowModal(!showModal);
  const handleSubmit = (partner) => {
    dispatch(handleCreatePrimaryPartnerSession(parseInt(partner)));
  };
  const endPartnership = (partner) => {
    dispatch(handleEndPrimaryPartnerSession(parseInt(partner)));
  };

  return (
    <>
      {role === 3 ? (
        <div className="partners">
          <Button className="general-button green" onClick={toggleModal}>
            {translate("Partners")}
          </Button>
          {showModal && (
            <Modal open={showModal} handleClose={() => setShowModal(false)}>
              <PartnersModal
                toggleModal={toggleModal}
                handleSubmit={handleSubmit}
                translate={translate}
                endPartnership={endPartnership}
              />
            </Modal>
          )}
        </div>
      ) : (
        <></>
      )}
    </>
  );
};

export default Partners;
