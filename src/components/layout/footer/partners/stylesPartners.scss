@import "../../../styles/colors.scss";

div.partners {
  display: grid;
  align-items: center;
  margin-right: 20px;

  & button {
    height: 34px;
    font-size: 13px;
    padding: 6px 11px;
    font-weight: 400;
  }
}

div.partners-modal {
  width: 600px;
  font-size: 0.7rem;

  & > h1.partners-modal-title {
    margin: 0;
    padding: 10px;
    background-color: $fabProBlue;
    color: #fff;
    font-size: 1.2rem;
  }

  & > div.partners-modal-content {
    padding: 15px;
    background-color: #fff;
    clear: both;
    overflow: hidden;

    & > div.partners-modal-section {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      column-gap: 15px;

      & > div,
      & > div div,
      & > div div select.partners-modal-select,
      & > button.partners-modal-submit {
        height: 30px;
        font-size: 1rem;
      }

      & > button.partners-modal-submit {
        background-color: $fabProBlue;
        padding: 0;
        color: #fff;

        &:not(:disabled):hover {
          background-color: darken($fabProBlue, 10%);
        }
      }

      &:last-of-type {
        grid-template-columns: repeat(3, 1fr);

        & > h2.partners-modal-section-title {
          grid-column: 1/-1;
          padding: 10px 0 0;
          margin: 0;
        }
      }

      & > ul.partners-modal-section-list {
        grid-column: 1/-1;
        padding: 0 0 15px;
        margin: 0;

        & > li {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          align-items: center;

          font-size: 1.1rem;
          height: 40px;
          line-height: 40px;
          box-sizing: border-box;
          padding: 0 10px;

          &.even {
            background-color: #ddd;
          }

          & > button.end-partnership {
            height: 30px;
            width: 75px;
            padding: 0;
            background-color: $red;
            justify-self: end;
            color: #fff;
            border: 1px solid #333;

            &:hover {
              background-color: darken($red, 10%);
            }
          }
        }
      }
    }

    & > button.partners-modal-close {
      padding: 0;
      width: 100px;
      height: 40px;
      background-color: #fff;
      border: 1px solid #000;
      font-size: 1rem;
      float: right;

      &:hover {
        background-color: darken(#fff, 10%);
      }
    }
  }
}
