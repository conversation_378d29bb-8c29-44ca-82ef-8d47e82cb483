import {
  fetchPartners,
  fetchPrimaryPartnerSessions,
  createPrimaryPartnerSession,
  fetchMyPartnerSessions,
  endPrimaryPartnerSession,
} from "../../../../_services";

export const receiveStarted = (type) => ({
  type: `RECEIVE_${type}_STARTED`,
});
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const handleFetchPartners = () => (dispatch) => {
  dispatch(receiveStarted("PARTNERS"));
  return fetchPartners().then((res) => {
    if (res.error) return dispatch(receiveFailed("PARTNERS", res.error));
    return dispatch(receiveSucceeded("PARTNERS", res));
  });
};

export const handleFetchPrimaryPartnerSessions = () => (dispatch) => {
  dispatch(receiveStarted("PRIMARY_PARTNER_SESSIONS"));
  return fetchPrimaryPartnerSessions().then((res) => {
    if (res.error)
      return dispatch(receiveFailed("PRIMARY_PARTNER_SESSIONS", res.error));
    return dispatch(receiveSucceeded("PRIMARY_PARTNER_SESSIONS", res));
  });
};

export const handleCreatePrimaryPartnerSession = (partnerId) => (dispatch) => {
  return createPrimaryPartnerSession(partnerId).then(() => {
    return dispatch(handleFetchPrimaryPartnerSessions()).then(() => {
      return dispatch(handleFetchMyPartnerSessions()).then(() => {
        return dispatch(handleFetchPartners());
      });
    });
  });
};

export const handleFetchMyPartnerSessions = () => (dispatch) => {
  dispatch(receiveStarted("MY_PARTNER_SESSIONS"));
  return fetchMyPartnerSessions().then((res) => {
    if (res.error)
      return dispatch(receiveFailed("MY_PARTNER_SESSIONS", res.error));
    return dispatch(receiveSucceeded("MY_PARTNER_SESSIONS", res));
  });
};

export const handleEndPrimaryPartnerSession = (partnerId) => (dispatch) => {
  return endPrimaryPartnerSession(partnerId).then(() => {
    return dispatch(handleFetchPrimaryPartnerSessions()).then(() => {
      return dispatch(handleFetchPartners());
    });
  });
};
