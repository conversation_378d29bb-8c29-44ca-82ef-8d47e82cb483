const initialState = {
  partners: null,
  primaryPartnerSessions: null,
  myPartnerSessions: null,
  isLoading: false,
  error: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_PARTNERS_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_PARTNERS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, partners: payload };
    case "RECEIVE_PARTNERS_FAILED":
      return { ...state, isLoading: false, error: payload, partners: null };
    case "RECEIVE_PRIMARY_PARTNER_SESSIONS_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_PRIMARY_PARTNER_SESSIONS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        primaryPartnerSessions: payload,
      };
    case "RECEIVE_PRIMARY_PARTNER_SESSIONS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        primaryPartnerSessions: null,
      };
    case "RECEIVE_MY_PARTNER_SESSIONS_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_MY_PARTNER_SESSIONS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        myPartnerSessions: payload,
      };
    case "RECEIVE_MY_PARTNER_SESSIONS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        myPartnerSessions: null,
      };
    default:
      return state;
  }
}
