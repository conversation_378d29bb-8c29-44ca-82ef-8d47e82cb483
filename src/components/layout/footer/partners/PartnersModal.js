// NPM PACKAGE IMPORTS
import React, { useState, useMemo } from "react";
import Select from "msuite_storybook/dist/select/Select";
import Button from "msuite_storybook/dist/button/Button";
import { useSelector } from "react-redux";

// HELPER FUNCTION IMPORTS
import { generateTimev2 } from "../../../../_utils";

// EXPORTS
const PartnersModal = ({
  toggleModal,
  handleSubmit,
  translate,
  endPartnership,
}) => {
  const [partner, setPartner] = useState(null);

  const { partners, primaryPartnerSessions, myPartnerSessions } = useSelector(
    (state) => state.partnersData
  );
  const { userId } = useSelector((state) => state.profileData);

  const partnersSelectOptions = useMemo(() => {
    return partners
      ? partners
          .filter((p) => p.user_id !== userId)
          .map((p) => ({
            id: p.user_id,
            value: p.user_id,
            display: p.user_name,
          }))
      : [];
  }, [partners]);

  return (
    <>
      <div className="partners-modal">
        <h1 className="partners-modal-title">
          {translate("Partner Sessions")}
        </h1>
        <div className="partners-modal-content">
          <div className="partners-modal-section">
            <Select
              placeholder={translate("Select Partner")}
              options={partnersSelectOptions}
              onInput={(e) => setPartner(e.target.value)}
              value={partner}
              required
              className="partners-modal-select"
            />
            <Button
              className="partners-modal-submit"
              disabled={!partner}
              onClick={() => {
                handleSubmit(partner);
                setPartner(null);
              }}
            >
              {translate("Add Partner")}
            </Button>
          </div>
          <div className="partners-modal-section">
            <h2 className="partners-modal-section-title">
              {translate("Open Partner Sessions")}
            </h2>
            <h3 className="partners-modal-section-subtitle">
              {translate("Partner Name")}
            </h3>
            <h3 className="partners-modal-section-subtitle">
              {translate("Session Started")}
            </h3>
            <h3 className="partners-modal-section-subtitle">
              {translate("End Partnership")}
            </h3>
            {!primaryPartnerSessions ? (
              <p className="partners-modal-section-no-results">
                {translate("You have no active partners.")}
              </p>
            ) : (
              ""
            )}
            <ul className="partners-modal-section-list">
              {primaryPartnerSessions
                ? primaryPartnerSessions.map((ps, idx) => {
                    return (
                      <li
                        key={ps.user_id}
                        className={idx % 2 === 0 ? "even" : ""}
                      >
                        <span>{ps.partner_name}</span>
                        <span>
                          {(() => {
                            if (myPartnerSessions) {
                              const session = myPartnerSessions.find(
                                (i) => i.session_id === ps.partner_session_id
                              );

                              if (session) {
                                return generateTimev2(
                                  session.session_started_unix * 1000,
                                  true,
                                  true
                                );
                              } else return "UNKNOWN";
                            } else return "UNKNOWN";
                          })()}
                        </span>
                        <Button
                          className="end-partnership"
                          onClick={() => endPartnership(ps.user_id)}
                        >
                          End
                        </Button>
                      </li>
                    );
                  })
                : ""}
            </ul>
          </div>
          <Button className="partners-modal-close" onClick={toggleModal}>
            {translate("Close")}
          </Button>
        </div>
      </div>
    </>
  );
};

export default PartnersModal;
