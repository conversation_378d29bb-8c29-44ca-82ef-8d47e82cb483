// NPM PACKAGE IMPORTS
import configureMockStore from "redux-mock-store";
import axios from "axios";
import Mock<PERSON><PERSON>pter from "axios-mock-adapter";
import thunk from "redux-thunk";

// REDUX IMPORTS
import {
  // ACTION CREATORS
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  // ACTION HANDLERS
  handleFetchPartners,
  handleFetchPrimaryPartnerSessions,
  handleCreatePrimaryPartnerSession,
  handleFetchMyPartnerSessions,
  handleEndPrimaryPartnerSession,
} from "./partnersActions";

describe("PARTNERS", () => {
  describe("action handlers should perform the necessary functions", () => {
    let store;
    let httpMock;

    const primaryPartnerSession = [
      {
        id: 1,
        partner_session_id: 1,
        user_id: 1,
        partner_name: "<PERSON>",
        user_type: 1,
        shift_type_id: null,
        manager_mode: 0,
        expected_duration: 28800,
        extended_duration: 0,
        shift_date: "2019-02-27",
        shift_start: 16892302,
        shift_end: 16892602,
        actual_duration: null,
        shift_timer_time: 0,
        shift_non_work_time: 2700,
        shift_generic_time: 0,
        deleted: 0,
        deleted_by: 0,
        deleted_on: null,
        excluded: 0,
      },
    ];
    const myPartnerSession = [
      {
        user_id: 2,
        user_employee_number: null,
        user_welder_id: null,
        user_name: "Ray Smith",
        user_id_active: true,
        status: null,
        user_shift_id: 12,
        partner_user_id: 1,
        partner_shift_id: 13,
        session_id: 1,
        session_started: "2019-03-11 08:34:15",
        session_ended: null,
        stop_type: null,
      },
    ];
    const availablePartners = [
      {
        user_id: 1,
        user_employee_number: null,
        user_welder_id: null,
        user_name: "Bob Righ",
        user_id_active: true,
        status: null,
        user_shift_id: 10,
        partner_user_id: null,
        partner_shift_id: null,
        session_id: null,
        session_started: null,
        session_ended: null,
        stop_type: null,
      },
      {
        user_id: 2,
        user_employee_number: null,
        user_welder_id: null,
        user_name: "Ray Smith",
        user_id_active: true,
        status: null,
        user_shift_id: 12,
        partner_user_id: null,
        partner_shift_id: null,
        session_id: null,
        session_started: null,
        session_ended: null,
        stop_type: null,
      },
    ];

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    it("handleFetchPartners fetches all available people to partner with", async () => {
      const testError = {
        error: { status: 404, message: "partners not found." },
      };

      httpMock
        .onGet(`${process.env.REACT_APP_API}/partners?status=available`)
        .replyOnce(200, availablePartners)
        .onGet(`${process.env.REACT_APP_API}/partners?status=available`)
        .replyOnce(404, testError);

      await store.dispatch(handleFetchPartners()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("PARTNERS"),
          receiveSucceeded("PARTNERS", availablePartners),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(availablePartners[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchPartners()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("PARTNERS"),
          receiveFailed("PARTNERS", testError.error),
        ];
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchPrimaryPartnerSessions fetches primary partner sessions", async () => {
      const testError = {
        error: { status: 404, message: "No partner sessions found." },
      };

      httpMock
        .onGet(`${process.env.REACT_APP_API}/self/partners/shifts`)
        .replyOnce(200, primaryPartnerSession)
        .onGet(`${process.env.REACT_APP_API}/self/partners/shifts`)
        .replyOnce(404, testError);

      await store.dispatch(handleFetchPrimaryPartnerSessions()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("PRIMARY_PARTNER_SESSIONS"),
          receiveSucceeded("PRIMARY_PARTNER_SESSIONS", primaryPartnerSession),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(primaryPartnerSession[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchPrimaryPartnerSessions()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("PRIMARY_PARTNER_SESSIONS"),
          receiveFailed("PRIMARY_PARTNER_SESSIONS", testError.error),
        ];
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleCreatePrimaryPartnerSession creates a new primary partner session", async () => {
      const testError = {
        error: { status: 404, message: "SQL Error:" },
      };

      httpMock
        .onPost(`${process.env.REACT_APP_API}/self/partners`)
        .replyOnce(200)
        .onGet(`${process.env.REACT_APP_API}/self/partners/shifts`)
        .replyOnce(200, primaryPartnerSession)
        .onGet(`${process.env.REACT_APP_API}/partners/1?is_active=1`)
        .replyOnce(200, myPartnerSession)
        .onGet(`${process.env.REACT_APP_API}/partners?status=available`)
        .replyOnce(200, availablePartners);

      httpMock
        .onPost(`${process.env.REACT_APP_API}/self/partners`)
        .replyOnce(404, testError)
        .onGet(`${process.env.REACT_APP_API}/self/partners/shifts`)
        .replyOnce(404, testError)
        .onGet(`${process.env.REACT_APP_API}/partners/1?is_active=1`)
        .replyOnce(404, testError)
        .onGet(`${process.env.REACT_APP_API}/partners?status=available`)
        .replyOnce(404, testError);

      await store.dispatch(handleCreatePrimaryPartnerSession(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("PRIMARY_PARTNER_SESSIONS"),
          receiveSucceeded("PRIMARY_PARTNER_SESSIONS", primaryPartnerSession),
          receiveStarted("MY_PARTNER_SESSIONS"),
          receiveSucceeded("MY_PARTNER_SESSIONS", myPartnerSession),
          receiveStarted("PARTNERS"),
          receiveSucceeded("PARTNERS", availablePartners),
        ];

        expect(httpMock.history.post[0].data).toEqual(
          JSON.stringify({ partner_id: 1 })
        );
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(primaryPartnerSession[0]);
        expect(receivedActions[3].payload[0]).toEqual(myPartnerSession[0]);
        expect(receivedActions[5].payload[0]).toEqual(availablePartners[0]);
        store.clearActions();
      });

      return store.dispatch(handleCreatePrimaryPartnerSession(10)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("PRIMARY_PARTNER_SESSIONS"),
          receiveFailed("PRIMARY_PARTNER_SESSIONS", testError.error),
          receiveStarted("MY_PARTNER_SESSIONS"),
          receiveFailed("MY_PARTNER_SESSIONS", testError.error),
          receiveStarted("PARTNERS"),
          receiveFailed("PARTNERS", testError.error),
        ];

        expect(httpMock.history.post[1].data).toEqual(
          JSON.stringify({ partner_id: 10 })
        );
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchMyPartnerSessions fetches user partner session", async () => {
      const testError = {
        error: { status: 404, message: "SQL Error:" },
      };

      httpMock
        .onGet(`${process.env.REACT_APP_API}/partners/1?is_active=1`)
        .replyOnce(200, myPartnerSession)
        .onGet(`${process.env.REACT_APP_API}/partners/1?is_active=1`)
        .replyOnce(404, testError);

      await store.dispatch(handleFetchMyPartnerSessions()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("MY_PARTNER_SESSIONS"),
          receiveSucceeded("MY_PARTNER_SESSIONS", myPartnerSession),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(myPartnerSession[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchMyPartnerSessions()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("MY_PARTNER_SESSIONS"),
          receiveFailed("MY_PARTNER_SESSIONS", testError.error),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleEndPrimaryPartnerSession ends partner session", async () => {
      const body = {
        callAction: "remove",
        callParams: {
          partner_id: 2,
        },
      };
      const testError = {
        error: { status: 404, message: "SQL Error:" },
      };

      httpMock
        .onPut(`${process.env.REACT_APP_API}/self/partners`, body)
        .replyOnce(200)
        .onGet(`${process.env.REACT_APP_API}/self/partners/shifts`)
        .replyOnce(200, primaryPartnerSession)
        .onGet(`${process.env.REACT_APP_API}/partners?status=available`)
        .replyOnce(200, availablePartners);

      httpMock
        .onPut(`${process.env.REACT_APP_API}/self/partners`, body)
        .replyOnce(404, testError)
        .onGet(`${process.env.REACT_APP_API}/self/partners/shifts`)
        .replyOnce(404, testError)
        .onGet(`${process.env.REACT_APP_API}/partners?status=available`)
        .replyOnce(404, testError);

      await store.dispatch(handleEndPrimaryPartnerSession(2)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("PRIMARY_PARTNER_SESSIONS"),
          receiveSucceeded("PRIMARY_PARTNER_SESSIONS", primaryPartnerSession),
          receiveStarted("PARTNERS"),
          receiveSucceeded("PARTNERS", availablePartners),
        ];

        expect(httpMock.history.put[0].data).toEqual(JSON.stringify(body));
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(primaryPartnerSession[0]);
        expect(receivedActions[3].payload[0]).toEqual(availablePartners[0]);
        store.clearActions();
      });

      return store.dispatch(handleEndPrimaryPartnerSession(2)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("PRIMARY_PARTNER_SESSIONS"),
          receiveFailed("PRIMARY_PARTNER_SESSIONS", testError.error),
          receiveStarted("PARTNERS"),
          receiveFailed("PARTNERS", testError.error),
        ];

        expect(httpMock.history.put[1].data).toEqual(JSON.stringify(body));
        expect(receivedActions).toEqual(expectedActions);
      });
    });
  });
});
