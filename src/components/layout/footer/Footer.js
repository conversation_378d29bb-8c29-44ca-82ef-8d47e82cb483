// NPM PACKAGE IMPORTS
import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";

// COMPONENT IMPORTS
import Partners from "./partners/Partners";
import TermsOfServiceModal from "../../legalDocuments/termsOfService/TermsOfServiceModal";
import LogoutModal from "../../reusable/logoutModal/LogoutModal";

// REDUX IMPORTS
import { handleFetchEula } from "../../legalDocuments/termsOfService/termsOfServiceActions";
import { handleClockOut } from "../../profile/profileActions";

// TRANSLATION IMPORTS
import useTranslations from "../../../hooks/useTranslations";
import footerTranslations from "./footerTranslations.json";

// STYLE IMPORTS
import "./stylesFooter.scss";

// EXPORTS
const Footer = () => {
  const [showLogoutModal, toggleLogoutModal] = useState(false);

  const translate = useTranslations(footerTranslations);
  const dispatch = useDispatch();
  const { shiftNow, userInfo } = useSelector((state) => state.profileData);

  useEffect(() => {
    dispatch(handleFetchEula);
  }, []);

  const logout = (endShift = 0) => {
    localStorage.removeItem("msuite_token");
    localStorage.removeItem("initFinished");
    window.location.replace(
      `${process.env.REACT_APP_FABPRO}/logout.php?endShift=${endShift}`
    );
  };

  const endSession = () => {
    dispatch(handleClockOut(shiftNow.id));
  };

  const endSessionAndLogout = () => {
    endSession();
    logout(1);
  };

  return (
    <>
      <div className="layout-footer">
        <div className="info-container">
          <Partners />
          {userInfo && userInfo.first_name && userInfo.last_name && (
            <p>
              {`FabPro - ${userInfo && userInfo.first_name} ${
                userInfo && userInfo.last_name
              } - `}
            </p>
          )}
          {/* <p>{`FabPro - ${userInfo && userInfo.first_name} ${userInfo && userInfo.last_name} - `}</p> */}
          <span
            className="footer-link logout"
            onClick={() => (shiftNow ? toggleLogoutModal(true) : logout())}
          >
            {translate("Logout")}
          </span>
        </div>
        <div className="footer-link-container">
          <a
            href="https://www.msuite.com/terms-of-use"
            className="footer-link"
            target="_blank"
            rel="noreferrer"
          >
            {translate("Terms of Use")}
          </a>
          <span>{` - `}</span>
          <Link to="/credits/" className="footer-link">
            {translate("Credits")}
          </Link>
        </div>
      </div>
      {showLogoutModal && (
        <LogoutModal
          logout={logout}
          fullLogout={endSessionAndLogout}
          showModal={showLogoutModal}
          onClose={() => toggleLogoutModal(false)}
        />
      )}
      <TermsOfServiceModal />
    </>
  );
};

export default Footer;
