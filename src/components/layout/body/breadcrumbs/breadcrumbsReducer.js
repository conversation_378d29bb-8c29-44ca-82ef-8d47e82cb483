const initialState = {
  trail: [],
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_BREADCRUMBS":
      return { ...state, trail: [...state.trail, ...payload] };
    case "CLEANUP_BREADCRUMBS":
      return { ...state, trail: payload };
    case "CLEAR_BREADCRUMBS":
      return { ...state, trail: [] };
    default:
      return state;
  }
}
