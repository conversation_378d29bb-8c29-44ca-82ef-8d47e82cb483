// NPM PACKAGE IMPORTS
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";

// REDUX IMPORTS
import { handleCleanupBreadcrumbs } from "./breadcrumbsActions";

// STYLE IMPORTS
import "./stylesBreadcrumbs.scss";

// EXPORTS
const Breadcrumbs = () => {
  const dispatch = useDispatch();
  const { trail } = useSelector((state) => state.breadcrumbsData);
  const { pageTitle } = useSelector((state) => state.generalData);

  const handleCleanup = (title) => {
    const endPoint = trail.findIndex((bc) => bc.title === title);
    const newTrail = trail.slice(0, endPoint);
    dispatch(handleCleanupBreadcrumbs(newTrail));
  };

  if (!trail.length) return <></>;
  return (
    <div className="breadcrumbs-container">
      {trail.map((bc) => (
        <>
          <Link
            className="breadcrumbs-link"
            key={bc.title}
            to={bc.destination}
            onClick={() => bc.cleanup && handleCleanup(bc.title)}
          >
            {bc.title}
          </Link>
          {" / "}
        </>
      ))}
      <span className="breadcrumbs-current">{pageTitle}</span>
    </div>
  );
};

export default Breadcrumbs;
