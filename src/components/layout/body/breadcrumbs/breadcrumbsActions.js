const receiveBreadcrumbs = (breadcrumbs) => ({
  type: "RECEIVE_BREADCRUMBS",
  payload: breadcrumbs,
});
const cleanupBreadcrumbs = (newTrail) => ({
  type: "CLEANUP_BREADCRUMBS",
  payload: newTrail,
});
const clearBreadcrumbs = () => ({
  type: "CLEAR_BREADCRUMBS",
});

export const handleReceiveBreadcrumbs = (breadcrumbs) => (dispatch) =>
  dispatch(receiveBreadcrumbs(breadcrumbs));
export const handleCleanupBreadcrumbs = (newTrail) => (dispatch) =>
  dispatch(cleanupBreadcrumbs(newTrail));
export const handleClearBreadcrumbs = (dispatch) =>
  dispatch(clearBreadcrumbs());
