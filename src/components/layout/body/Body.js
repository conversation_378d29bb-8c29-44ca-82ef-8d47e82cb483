// NPM PACKAGE IMPORTS
import React from "react";
import { useSelector } from "react-redux";

// COMPONENT IMPORTS
import Breadcrumbs from "./breadcrumbs/Breadcrumbs";
import { MSuiteNewsPopup } from "../../MSuiteNewsPopup";

// EXPORTS
const Body = (props) => {
  const { pageTitle, navExpanded } = useSelector((state) => state.generalData);
  const { token } = useSelector((state) => state.profileData);

  return (
    <>
      <MSuiteNewsPopup />
      <div className={`layout-body ${navExpanded ? "narrow" : ""}`}>
        {pageTitle && pageTitle.length && (
          <h1 className="page-title">{pageTitle}</h1>
        )}
        <Breadcrumbs />
        {token && props.children}
      </div>
    </>
  );
};

export default Body;
