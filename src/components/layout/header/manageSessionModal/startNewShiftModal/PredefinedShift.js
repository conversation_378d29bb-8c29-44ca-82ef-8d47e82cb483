// NPM PACKAGE IMPORTS
import React, { useEffect } from "react";
import Button from "msuite_storybook/dist/button/Button";

// TRANSLATION IMPORTS
import useTranslations from "../../../../../hooks/useTranslations";
import translations from "./translations.json";

const PredefinedShiftModal = ({
  f_predefined,
  handleModalTransition,
  handlePredefinedClockIn,
  f_previous,
  setTitle,
}) => {
  const translate = useTranslations(translations);

  useEffect(() => {
    setTitle("Select a Pre-Existing Session to Join:");
  }, []);

  const PredefinedList = () => (
    <ul>
      {f_predefined &&
        f_predefined.map((shift) => (
          <li key={shift.id} className="predefined-shift">
            <p className="name">{shift.name}</p>
            <p className="time-span">{`${shift.start_time} - ${shift.end_time}`}</p>
            <Button
              className="action"
              onClick={() =>
                handlePredefinedClockIn({
                  ...shift,
                  start_time: shift.original_start_time,
                  end_time: shift.original_end_time,
                })
              }
            >
              Clock In
            </Button>
          </li>
        ))}
    </ul>
  );

  return (
    <div className="predefinedShifts-previous__wrapper">
      {f_predefined && f_predefined.length ? (
        <PredefinedList />
      ) : (
        <p className="no-shift-types">
          {translate("There are no shift types for this date.")}
        </p>
      )}
      {f_previous ? (
        <Button
          type="button"
          onClick={() => handleModalTransition("resumePreviousShift")}
          className="resume-previous-shift"
        >
          {translate("Click Here to resume your previous session")}
        </Button>
      ) : (
        <></>
      )}

      <Button
        onClick={() => handleModalTransition("customShift")}
        className="custom-shift__button"
      >
        {translate(
          "Click Here to create a session with custom start and end times"
        )}
      </Button>
    </div>
  );
};

export default PredefinedShiftModal;
