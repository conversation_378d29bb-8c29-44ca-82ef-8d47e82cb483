// NPM PACKAGE IMPORTS
import React, { useState, useEffect } from "react";
import Modal from "msuite_storybook/dist/modal/Modal";
import { useSelector, useDispatch } from "react-redux";
import moment from "moment";

// REDUX IMPORTS
import {
  handleClockIn,
  handleFetchLastShift,
  handleFetchPredefinedShifts,
  handleExtendShift,
  handleClockIntoShift,
  handleFetchFutureShift,
} from "../../../profile/profileActions";

// COMPONENT IMPORTS
import PredefinedShift from "./startNewShiftModal/PredefinedShift";
import ResumePreviousShift from "./resumePreviousShiftModal/ResumePreviousShiftModal";
import CustomShift from "./customShiftModal/CustomShift";

// HELPER FUNCTION IMPORTS
import {
  populateDropdownTimes,
  checkPreviousShiftTime,
  generateTime,
} from "../../../../_utils";

// STYLE IMPORTS
import "./manageSessionModal.scss";

const DATE_FORMAT = "MM/DD h:mm a";

const ManageSessionModal = ({
  // onSubmit,
  onClose,
  showModal,
  setSoftClockout,
}) => {
  const [title, setTitle] = useState("Manage Session:");
  const [previousType, setPreviousType] = useState(null);
  const [extendTimeOptions, setExtendTimeOptions] = useState([]);
  const [extendedTime, setExtendedTime] = useState("");
  const [f_previousShift, setPreviousShift] = useState(null);
  const [f_shiftNow, setShiftNow] = useState(null);
  const [f_predefinedShifts, setPredefinedShifts] = useState(null);
  const [modalType, setModalType] = useState(null);

  const dispatch = useDispatch();

  const {
    userId,
    shiftNow,
    lastShift,
    predefinedShifts,
    futureShift,
  } = useSelector((state) => state.profileData);

  useEffect(() => {
    if (userId) {
      dispatch(handleFetchLastShift);
      dispatch(handleFetchPredefinedShifts);
      dispatch(handleFetchFutureShift());
    }
  }, [userId, dispatch]);

  useEffect(() => {
    if (shiftNow && !shiftNow.error) {
      let { id, shift_start, shift_end } = shiftNow;
      const f_shift_start = moment.unix(shift_start).format(DATE_FORMAT);
      const f_shift_end = moment.unix(shift_end).format(DATE_FORMAT);

      const f_shiftNow = {
        id,
        shift_start: f_shift_start,
        shift_end: f_shift_end,
      };
      setShiftNow(f_shiftNow);
    }
  }, [shiftNow]);

  useEffect(() => {
    if (lastShift) {
      let { id, shift_start, shift_end } = lastShift;
      const f_shift_start = moment.unix(shift_start).format(DATE_FORMAT);
      const f_shift_end = moment.unix(shift_end).format(DATE_FORMAT);

      let f_lastShift = {
        id,
        shift_start: f_shift_start,
        shift_end: f_shift_end,
      };
      if (checkPreviousShiftTime(lastShift.shift_end)) {
        setPreviousShift(f_lastShift);
        setExtendTimeOptions(populateDropdownTimes(lastShift));
      }
    }
  }, [lastShift]);

  useEffect(() => {
    if (
      predefinedShifts &&
      predefinedShifts.length &&
      !predefinedShifts.error
    ) {
      const f_predefined = predefinedShifts.map((shift) => {
        let { id, start_time, end_time, name } = shift;

        return {
          id,
          name,
          start_time: generateTime(start_time * 1000, true),
          end_time: generateTime(end_time * 1000, true),
          original_start_time: start_time,
          original_end_time: end_time,
        };
      });
      setPredefinedShifts(f_predefined);
    }
  }, [predefinedShifts]);

  const handleExtendPreviousShift = () => {
    const shift_end = extendedTime;
    const id = lastShift.id;
    return dispatch(handleExtendShift(id, shift_end));
  };

  const handleManageSessionSubmit = (shiftId, startTime, endTime, comment) => {
    switch (modalType) {
      case "futureShift":
        dispatch(
          handleClockIntoShift({
            id: shiftId,
            start_time: startTime,
            end_time: endTime,
            comment,
          })
        ).then((res) => {
          if (!res.error && !(res.payload && res.payload.status !== 200)) {
            onClose();
            setSoftClockout && setSoftClockout(false);
          }
        });
        break;
      case "currentShift":
        dispatch(
          handleClockIntoShift({
            id: shiftId,
            start_time: startTime,
            end_time: endTime,
            comment,
          })
        ).then((res) => {
          if (!res.error && !(res.payload && res.payload.status !== 200)) {
            onClose();
            if (startTime * 1000 > Date.now()) {
              setSoftClockout && setSoftClockout(true);
            } else {
              setSoftClockout && setSoftClockout(false);
            }
          }
        });
        break;
      case "customShift":
        dispatch(
          handleClockIn({ start_time: startTime, end_time: endTime })
        ).then((res) => {
          if (!res.error && !(res.payload && res.payload.status !== 200)) {
            onClose();
            setSoftClockout && setSoftClockout(false);
          }
        });
        break;
      case "resumePreviousShift":
        handleExtendPreviousShift().then((res) => {
          if (!res.error && !(res.payload && res.payload.status !== 200)) {
            setSoftClockout && setSoftClockout(false);
          }
        });
        break;
      case "predefinedShifts":
        dispatch(
          handleClockIn({ start_time: startTime, end_time: endTime })
        ).then((res) => {
          if (!res.error && !(res.payload && res.payload.status !== 200)) {
            onClose();
            setSoftClockout && setSoftClockout(false);
          }
        });
        break;
      default:
        return;
    }
  };

  useEffect(() => {
    if (shiftNow && !f_shiftNow) return;
    if (f_shiftNow) {
      setModalType("currentShift");
    } else if (futureShift) {
      setModalType("futureShift");
    } else if (
      !f_shiftNow &&
      f_previousShift &&
      Math.abs(
        generateTime(
          lastShift.shift_end * 1000,
          false,
          true,
          null,
          null,
          true
        ) - Date.now()
      ) <
        3600000 * 4 &&
      lastShift.actual_duration * 1000 < 3600000 * 18
    ) {
      setModalType("resumePreviousShift");
    } else if (
      !f_previousShift &&
      !f_shiftNow &&
      predefinedShifts &&
      !predefinedShifts.error
    ) {
      setModalType("predefinedShifts");
    }
  }, [f_shiftNow, f_previousShift, predefinedShifts, showModal, futureShift]);

  useEffect(() => {
    if (modalType !== "customShift") {
      setPreviousType(modalType);
    }
  }, [modalType]);

  const handleModalTransition = (newType) => {
    setModalType(newType);
  };

  const predefinedClockIn = (shift) => {
    dispatch(handleClockIn(shift));
    if (
      generateTime(shift.start_time * 1000, false, true, null, null, true) >
      Date.now()
    ) {
      setModalType("futureShift");
    } else {
      setModalType("currentShift");
    }
    onClose();
  };

  return (
    <Modal handleClose={onClose} open={showModal}>
      <h2 className="manage-session-modal-title">{title}</h2>
      <div className="manage-session-modal-content">
        {modalType === "predefinedShifts" && (
          <PredefinedShift
            handleModalTransition={handleModalTransition}
            handlePredefinedClockIn={predefinedClockIn}
            // CHANGED THIS FROM predefinedShifts
            f_predefined={f_predefinedShifts}
            f_previous={f_previousShift}
            setTitle={setTitle}
          />
        )}

        {modalType === "resumePreviousShift" && (
          <ResumePreviousShift
            f_previousShift={f_previousShift}
            extendedTime={extendedTime}
            setExtendedTime={setExtendedTime}
            extendTimeOptions={extendTimeOptions}
            handleModalTransition={handleModalTransition}
            onSubmit={handleManageSessionSubmit}
            setTitle={setTitle}
          />
        )}

        {(modalType === "customShift" ||
          modalType === "currentShift" ||
          modalType === "futureShift") && (
          <CustomShift
            handleModalTransition={(close) =>
              close === true ? onClose() : handleModalTransition(previousType)
            }
            onSubmit={handleManageSessionSubmit}
            type={modalType}
            setTitle={setTitle}
            shiftNow={shiftNow}
            lastShift={lastShift}
            futureShift={futureShift}
            previousType={previousType}
          />
        )}
      </div>
    </Modal>
  );
};

export default ManageSessionModal;
