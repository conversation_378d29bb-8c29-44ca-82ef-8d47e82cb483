@import "../../../styles/colors.scss";

h2.manage-session-modal-title {
  background-color: $fabProBlue;
  padding: 10px;
  margin: 0;
  color: #fff;
}

div.manage-session-modal-content {
  width: 100vw;
  padding: 15px;
  background-color: #fff;
  text-align: center;

  @media (min-width: 690px) {
    width: 690px;
  }

  & > div.predefinedShifts-previous__wrapper {
    display: grid;
    row-gap: 15px;

    & > p.no-shift-types {
      height: 200px;
      line-height: 200px;
      margin: 0;
    }

    & > button {
      background-color: $fabProBlue;
      color: #fff;
      height: 40px;
      padding: 0 10px;
      border: 1px solid #333;

      &:not(:disabled):hover {
        background-color: darken($fabProBlue, 10%);
      }
    }

    & > button.resume-previous-shift {
      background-color: #fff;
      color: #333;
      border: 1px solid #333;

      &:hover {
        background-color: darken(#fff, 10%);
      }
    }

    & > ul {
      list-style: none;
      overflow-y: scroll;
      height: 200px;
      padding: 0;
      margin: 0;

      &::-webkit-scrollbar {
        width: 10px;
        background-color: #f5f5f5;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 2px;
        background-color: #555;
      }

      & > li.predefined-shift {
        height: 40px;
        padding: 0 15px;

        display: grid;
        grid-template-columns: 2fr 1fr 100px;
        column-gap: 10px;
        align-items: center;

        & > p {
          margin: 0;
        }

        & > p.name {
          text-align: left;
        }

        & > p.time-span {
          text-align: right;
        }

        &:nth-of-type(odd) {
          background-color: #eee;
        }

        & > button.action {
          color: #fff;
          background-color: $lightGreen;
          height: 30px;
          padding: 0;
          border: 1px solid #333;

          &:hover {
            background-color: darken($lightGreen, 10%);
          }
        }
      }
    }
  }

  & > div.custom-shift__wrapper {
    display: grid;
    row-gap: 15px;

    & > div.calendar-container {
      display: grid;
      grid-template-rows: 1fr 1fr;
      row-gap: 15px;
      overflow-y: scroll;
      max-height: 500px;

      @media (min-width: 690px) {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: unset;
        column-gap: 30px;
        overflow-y: hidden;
        max-height: unset;
      }

      &.futureShift {
        grid-template-columns: 1fr;
      }
    }

    & > div.shift-comment-wrapper {
      display: grid;
      grid-template-columns: 15% 30px 1fr 15%;
      align-items: center;
      column-gap: 10px;

      & > span {
        font-size: 1.2rem;
        color: $fabProBlue;
        grid-column: 2/3;
      }

      & > div {
        grid-column: 3/4;
        height: 40px;

        & > input {
          height: 40px;
        }
      }
    }

    & > span.button-wrapper {
      display: grid;
      grid-template-columns: 1fr 1fr;
      justify-items: center;

      & > button {
        width: 50%;
        height: 40px;
        padding: 0;
        border: 1px solid #333;
      }

      & > button.cancel {
        color: #333;
        background-color: #fff;

        &:hover {
          background-color: darken(#fff, 10%);
        }
      }

      & > button.submit {
        color: #fff;
        background-color: $lightGreen;

        &:not(:disabled):hover {
          background-color: darken($lightGreen, 10%);
        }
      }
    }

    & > button.join-existing-shift {
      background-color: #fff;
      color: #333;
      border: 1px solid #333;

      &:hover {
        background-color: darken(#fff, 10%);
      }
    }
  }

  & > div.no-shifts-previous__wrapper {
    display: grid;
    row-gap: 15px;

    & > div.row-options {
      display: grid;
      grid-template-columns: 1fr 1fr;
      column-gap: 15px;
      width: 75%;
      margin: 0 auto;

      & > select {
        box-sizing: border-box;
        border-radius: 3px;

        &:hover {
          border-color: $fabProBlue;
        }

        &:focus {
          border: 2px solid $fabProBlue;
        }
      }

      & > button {
        height: 40px;
        padding: 0;
        color: #fff;
        background-color: $lightGreen;
        border: 1px solid #333;

        &:not(:disabled):hover {
          background-color: darken($lightGreen, 10%);
        }
      }
    }

    & > button {
      width: 50%;
      margin: 0 auto;
      background-color: $fabProBlue;
      color: #fff;
      height: 40px;
      padding: 0;
      border: 1px solid #333;

      &:hover {
        background-color: darken($fabProBlue, 10%);
      }
    }
  }

  & > div.no-shifts-no-previous__wrapper,
  & > div.current-shift__wrapper {
    & > p {
      padding: 0 0 10px;
    }

    & > button {
      background-color: $fabProBlue;
      color: #fff;
      border: 1px solid #333;

      &:hover {
        background-color: darken($fabProBlue, 10%);
      }
    }
  }
}
