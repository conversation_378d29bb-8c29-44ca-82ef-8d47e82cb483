// NPM PACKAGE IMPORTS
import React, { useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowRight } from "@fortawesome/free-solid-svg-icons";
import moment from "moment";
import Button from "msuite_storybook/dist/button/Button";

// TRANSLATION IMPORTS
import useTranslations from "../../../../../hooks/useTranslations";
import translations from "./translations.json";

const DATE_FORMAT = "MM/DD h:mm a";

// DOUBLE CHECK ROUTE DOES THE THING CORRECTLY (EXTEND IN REACT AND GO TO PHP TO VERIFY)
const ResumePreviousShiftModal = ({
  f_previousShift,
  extendedTime,
  setExtendedTime,
  extendTimeOptions,
  handleModalTransition,
  onSubmit,
  setTitle,
}) => {
  const translate = useTranslations(translations);

  useEffect(() => {
    setTitle("Manage Session:");
  }, []);

  return (
    <div className="no-shifts-previous__wrapper">
      <p>
        {translate(
          "It looks like you have a previous session today, would you like to resume that session, or begin a new one?"
        )}
      </p>
      <p className="previous-session">
        {translate("Resume Previous Session:")} (
        {`${moment(f_previousShift.shift_start, DATE_FORMAT).format(
          "h:mm a"
        )} - ${moment(f_previousShift.shift_end, DATE_FORMAT).format(
          "h:mm a"
        )}`}
        )
      </p>
      <div className="row-options">
        <select
          value={extendedTime}
          onChange={(e) => setExtendedTime(e.target.value)}
        >
          <option default>{translate("Select time to extend to:")}</option>
          {extendTimeOptions.map((option) => (
            <option value={option.value} key={option.value}>
              {translate("Extend to")} {option.display}
            </option>
          ))}
        </select>
        <Button type="submit" onClick={onSubmit} disabled={!extendedTime}>
          {translate("Resume Session")} <FontAwesomeIcon icon={faArrowRight} />
        </Button>
      </div>
      <Button
        onClick={() => handleModalTransition("predefinedShifts")}
        className="text-button"
      >
        {translate("Or Click Here to start a new session")}
      </Button>
    </div>
  );
};

export default ResumePreviousShiftModal;
