// NPM PACKAGE IMPORTS
import React, { useState, useEffect } from "react";
import DatePicker from "react-datepicker";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCommentAlt,
  faArrowLeft,
  faArrowRight,
} from "@fortawesome/free-solid-svg-icons";
import Button from "msuite_storybook/dist/button/Button";
import Input from "msuite_storybook/dist/input/Input";
import { useDispatch } from "react-redux";

// REDUX IMPORTS
import { notify } from "../../../../reusable/alertPopup/alertPopupActions";

// TRANSLATION IMPORTS
import useTranslations from "../../../../../hooks/useTranslations";
import translations from "./translations.json";

// HELPER FUNCTION IMPORTS
import { generateTime } from "../../../../../_utils";

// STYLE IMPORTS
import "react-datepicker/dist/react-datepicker.css";

const CustomShift = ({
  handleModalTransition,
  onSubmit,
  setTitle,
  shiftNow,
  lastShift,
  futureShift,
  previousType,
}) => {
  const [customStart, setCustomStart] = useState(null);
  const [customEnd, setCustomEnd] = useState(null);
  const [shiftComment, setShiftComment] = useState("");

  const translate = useTranslations(translations);
  const dispatch = useDispatch();

  useEffect(() => {
    if (previousType === "futureShift" && futureShift) {
      setCustomStart(
        new Date(
          generateTime(
            futureShift.shift_start * 1000,
            false,
            true,
            null,
            null,
            true
          )
        )
      );
    } else if (previousType === "currentShift" && shiftNow) {
      setCustomStart(
        new Date(
          generateTime(
            shiftNow.shift_start * 1000,
            false,
            false,
            null,
            null,
            true
          )
        )
      );
      setCustomEnd(
        new Date(
          generateTime(
            shiftNow.shift_end * 1000,
            false,
            false,
            null,
            null,
            true
          )
        )
      );
    } else if (
      (previousType === "resumePreviousShift" ||
        previousType === "predefinedShifts") &&
      lastShift
    ) {
      let customStartValue = new Date();
      let customEndValue = new Date();
      const lastShiftStartDate = new Date(
        generateTime(
          lastShift.shift_start * 1000,
          false,
          false,
          null,
          null,
          true
        )
      );
      const lastShiftEndDate = new Date(
        generateTime(
          (lastShift.shift_start + lastShift.expected_duration) * 1000,
          false,
          false,
          null,
          null,
          true
        )
      );

      customStartValue.setHours(lastShiftStartDate.getHours());
      customStartValue.setMinutes(lastShiftStartDate.getMinutes());
      customStartValue.setSeconds(lastShiftStartDate.getSeconds());
      customStartValue.setMilliseconds(lastShiftStartDate.getMilliseconds());

      customEndValue.setHours(lastShiftEndDate.getHours());
      customEndValue.setMinutes(lastShiftEndDate.getMinutes());
      customEndValue.setSeconds(lastShiftEndDate.getSeconds());
      customEndValue.setMilliseconds(lastShiftEndDate.getMilliseconds());

      if (customEndValue < customStartValue) {
        customEndValue.setDate(customEndValue.getDate() + 1);
      }

      setCustomStart(customStartValue);
      setCustomEnd(customEndValue);
    } else if (previousType !== null) {
      let now = new Date();
      now.setHours(
        (((now.getMinutes() / 105 + 0.5) | 0) + now.getHours()) % 24
      );
      now.setMinutes(((((now.getMinutes() + 7.5) / 15) | 0) * 15) % 60);

      setCustomStart(now);

      let end = new Date(now);
      end.setHours(end.getHours() + 12);
      setCustomEnd(end);
    }
  }, [previousType]);

  useEffect(() => {
    setTitle(
      previousType === "currentShift"
        ? "Manage Session:"
        : previousType === "futureShift"
        ? "Your Scheduled Shift has not yet begun: Start your Session now?"
        : "Set a Custom Shift:"
    );
  }, [previousType]);

  useEffect(() => {
    if (previousType === "futureShift" && futureShift && customStart) {
      if (
        Math.abs(
          customStart -
            generateTime(
              futureShift.shift_start * 1000,
              null,
              true,
              null,
              null,
              true
            )
        ) > 3600000
      ) {
        dispatch(
          notify({
            id: Date.now(),
            type: "ERROR",
            message: "Session cannot be set more than one hour ahead of time.",
          })
        );
      }

      if (customStart < new Date()) {
        dispatch(
          notify({
            id: Date.now(),
            type: "ERROR",
            message: "Session cannot be set before now.",
          })
        );
      }
    } else if (customStart && customEnd) {
      if (customEnd < new Date()) {
        dispatch(
          notify({
            id: Date.now(),
            type: "ERROR",
            message: "Session cannot end before now.",
          })
        );
      }
      if (customEnd < customStart) {
        dispatch(
          notify({
            id: Date.now(),
            type: "ERROR",
            message: `Session cannot end before it start${
              customStart > new Date() ? "s" : "ed"
            }.`,
          })
        );
      }
    }
  }, [futureShift, customStart, customEnd]);

  return (
    <div className="custom-shift__wrapper">
      <div className={`calendar-container ${previousType}`}>
        <span>
          <p className="calendar-label">{translate("START")}</p>
          <DatePicker
            timeIntervals={15}
            selected={customStart}
            onChange={setCustomStart}
            showTimeSelect
            inline
          />
        </span>
        {previousType !== "futureShift" && (
          <span>
            <p className="calendar-label">{translate("END")}</p>
            <DatePicker
              timeIntervals={15}
              selected={customEnd}
              onChange={setCustomEnd}
              showTimeSelect
              inline
            />
          </span>
        )}
      </div>
      <div className="shift-comment-wrapper">
        <span>
          <FontAwesomeIcon icon={faCommentAlt} />
        </span>
        <Input
          placeholder={translate("Enter shift comments here")}
          value={shiftComment}
          onChange={(e) => setShiftComment(e.target.value)}
        />
      </div>
      <span className="button-wrapper">
        <Button
          className="cancel"
          onClick={() =>
            handleModalTransition(
              previousType === "currentShift" || previousType === "futureShift"
            )
          }
        >
          <FontAwesomeIcon icon={faArrowLeft} /> Cancel
        </Button>
        <Button
          onClick={() => {
            switch (previousType) {
              case "currentShift":
                if (!shiftNow) return;
                return onSubmit(
                  shiftNow.id,
                  customStart / 1000,
                  customEnd / 1000,
                  shiftComment
                );
              case "futureShift":
                return onSubmit(
                  futureShift.id,
                  customStart / 1000,
                  futureShift.shift_end
                );
              case "customShift":
                return onSubmit(
                  null,
                  customStart / 1000,
                  customEnd / 1000,
                  shiftComment
                );
              case "predefinedShifts":
                return onSubmit(
                  null,
                  customStart / 1000,
                  customEnd / 1000,
                  shiftComment
                );
              default:
                return;
            }
          }}
          className="submit"
          disabled={
            !customStart ||
            (!futureShift &&
              (!customEnd ||
                JSON.stringify(customStart) === JSON.stringify(customEnd) ||
                customEnd < customStart)) ||
            (previousType !== "currentShift" &&
              futureShift &&
              (customStart < Date.now() ||
                Math.abs(
                  generateTime(
                    futureShift.shift_start * 1000,
                    null,
                    true,
                    null,
                    null,
                    true
                  ) - customStart
                ) > 3600000)) ||
            (previousType === "currentShift" && !shiftNow)
          }
        >
          {previousType === "currentShift"
            ? "Update"
            : previousType === "futureShift"
            ? "Custom Start"
            : "Start Shift"}{" "}
          <FontAwesomeIcon icon={faArrowRight} />
        </Button>
      </span>
      {previousType !== "currentShift" && (
        <Button
          className="join-existing-shift"
          onClick={
            previousType === "futureShift"
              ? () =>
                  onSubmit(
                    futureShift.id,
                    Date.now() / 1000,
                    futureShift.shift_end
                  )
              : () => handleModalTransition("predefinedShifts")
          }
        >
          {previousType === "futureShift" ? (
            <>
              Start Session Now <FontAwesomeIcon icon={faArrowRight} />
            </>
          ) : (
            <>
              <FontAwesomeIcon icon={faArrowLeft} /> Click here to join an
              existing shift
            </>
          )}
        </Button>
      )}
    </div>
  );
};

export default CustomShift;
