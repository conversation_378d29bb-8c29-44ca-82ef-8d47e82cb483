// NPM PACKAGE IMPORTS
import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import Select from "msuite_storybook/dist/select/Select";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowRight } from "@fortawesome/free-solid-svg-icons";
import { BsExclamationCircle } from "react-icons/bs";

// REDUX IMPORTS
import {
  handleCheckRegularShift,
  handleCheckPartnerShifts,
  handleExtendShift,
  handleAcknowledgeShiftEnd,
  handleFetchShiftNow,
} from "../../../profile/profileActions";
import { notify } from "../../../reusable/alertPopup/alertPopupActions";

// TRANSLATION IMPORTS
import useTranslations from "../../../../hooks/useTranslations";
import shiftEndAlertModalsTranslations from "./shiftEndAlertModalsTranslations.json";

// HELPER FUNCTION IMPORTS
import { generateTime, populateDropdownTimes } from "../../../../_utils";

// STYLE IMPORTS
import "./stylesShiftEndAlertModal.scss";

// EXPORTS
const ShiftEndAlertModals = ({ isClockedIn, softClockout, setClockedIn }) => {
  const [alertFifteen, setAlertFifteen] = useState(false);
  const [alertFive, setAlertFive] = useState(false);
  const [alertPartnersFifteen, setAlertPartnersFifteen] = useState([]);
  const [alertPartnersFive, setAlertPartnersFive] = useState([]);
  const [partnersFifteenAlerted, setPartnersFifteenAlerted] = useState([]);
  const [partnersFiveAlerted, setPartnersFiveAlerted] = useState([]);
  const [shiftEndInterval, setShiftEndInterval] = useState(null);
  const [shiftExtendOption, setShiftExtendOption] = useState(null);

  const translate = useTranslations(shiftEndAlertModalsTranslations);
  const dispatch = useDispatch();
  const { token, shiftEnd, partnerShifts, shiftNow, permissions } = useSelector(
    (state) => state.profileData
  );

  useEffect(() => {
    if (token && isClockedIn) {
      dispatch(handleCheckRegularShift);
      dispatch(handleCheckPartnerShifts);
    }
  }, []);

  useEffect(() => {
    if (token && isClockedIn) {
      setShiftEndInterval(
        setInterval(() => {
          dispatch(handleCheckRegularShift);
          dispatch(handleCheckPartnerShifts);
        }, 30000)
      );
    }
  }, [dispatch, isClockedIn, token]);

  useEffect(() => {
    if ((!isClockedIn && shiftEndInterval) || softClockout) {
      clearInterval(shiftEndInterval);
      setShiftEndInterval(null);
    }
  }, [isClockedIn, shiftEndInterval, softClockout]);

  useEffect(() => {
    if (softClockout) setClockedIn(false);
  }, [softClockout]);

  const shiftEndCheck = () => {
    if (shiftEnd) {
      if (shiftNow && shiftNow.warned === 2) {
        dispatch(handleFetchShiftNow);
        return false;
      }
      // check that shift start is not in the future
      if (
        shiftEnd.error &&
        !softClockout &&
        generateTime(shiftNow.shift_start, false, false, null, null, true) <=
          Math.floor(Date.now() / 1000)
      )
        return "END";

      if (shiftNow && shiftNow.warned === 1) return false;

      if (
        !alertFive &&
        generateTime(shiftEnd, false, false, null, null, true) - Date.now() <=
          300000
      ) {
        return true;
      } else if (alertFive) return false;

      if (
        !alertFifteen &&
        generateTime(shiftEnd, false, false, null, null, true) - Date.now() <=
          900000
      ) {
        return true;
      } else if (alertFifteen) return false;
    }

    return false;
  };

  const partnerShiftsEndCheck = useCallback(() => {
    if (partnerShifts && !partnerShifts.error) {
      let newAlertPartnersFive = alertPartnersFive;
      let newAlertPartnersFifteen = alertPartnersFifteen;

      for (let i = 0; i < partnerShifts.length; i++) {
        let id = partnerShifts[i].partner_session_id;
        let partnerShiftEnd = partnerShifts[i].shift_end;
        if (
          !partnersFiveAlerted.includes(id) &&
          generateTime(partnerShiftEnd * 1000, false, false, null, null, true) -
            Date.now() <=
            300000
        ) {
          newAlertPartnersFive.push(id);
        }
        if (
          !partnersFifteenAlerted.includes(id) &&
          generateTime(partnerShiftEnd * 1000, false, false, null, null, true) -
            Date.now() <=
            900000
        ) {
          newAlertPartnersFifteen.push(id);
        }
      }
      setAlertPartnersFive(newAlertPartnersFive);
      setAlertPartnersFifteen(newAlertPartnersFifteen);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [partnerShifts]);

  const shiftEndModalClose = () => {
    if (isClockedIn && shiftEnd.error) {
      if (permissions && permissions.includes(289)) {
        setClockedIn(false);
        return dispatch(
          notify({
            id: Date.now(),
            type: "WARN",
            message: "Your shift has ended.",
          })
        );
      } else {
        localStorage.removeItem("msuite_token");
        localStorage.removeItem("initFinished");
        return window.location.replace(process.env.REACT_APP_LOGIN);
      }
    } else if (
      !alertFive &&
      generateTime(shiftEnd, false, false, null, null, true) - Date.now() <=
        300000
    ) {
      dispatch(handleAcknowledgeShiftEnd(shiftNow.id));
      return setAlertFive(true);
    } else if (
      !alertFifteen &&
      generateTime(shiftEnd, false, false, null, null, true) - Date.now() <=
        900000
    ) {
      return setAlertFifteen(true);
    }
  };

  const partnerShiftEndModalClose = (id, type) => {
    switch (type) {
      case "FIVE":
        let newAlertPartnersFive = alertPartnersFive.filter((pf) => pf !== id);
        let newPartnersFiveAlerted = [...partnersFiveAlerted, id];
        setPartnersFiveAlerted(newPartnersFiveAlerted);
        return setAlertPartnersFive(newAlertPartnersFive);
      case "FIFTEEN":
        let newAlertPartnersFifteen = alertPartnersFifteen.filter(
          (pf) => pf !== id
        );
        let newPartnersFifteenAlerted = [...partnersFifteenAlerted, id];
        setPartnersFifteenAlerted(newPartnersFifteenAlerted);
        return setAlertPartnersFifteen(newAlertPartnersFifteen);
      default:
        return;
    }
  };

  const shiftEndModalText = () => {
    if (!shiftEnd || (shiftEnd && shiftEnd.error))
      return translate("Your shift has ended.");
    if (
      generateTime(shiftEnd, false, false, null, null, true) - Date.now() <=
      900000
    )
      return `${translate(
        "Your session is scheduled to end at"
      )} ${generateTime(shiftEnd, true)}, would you like to extend it?`;
  };

  const partnerShiftEndModalText = (partner, endTime) => {
    if (
      generateTime(endTime, false, false, null, null, true) - Date.now() <=
      900000
    ) {
      return `${translate("Your partner session with")} ${partner} ${translate(
        "is scheduled to end at"
      )} ${generateTime(endTime, true)}. ${translate(
        "They need to login and manage session to extend."
      )}`;
    }
  };

  const extendShift = () => {
    dispatch(handleExtendShift(shiftNow.id, shiftExtendOption)).then((res) => {
      if (!res.error) {
        setAlertFifteen(false);
        setAlertFive(false);
      }
      shiftEndModalClose();
    });
  };

  useEffect(() => {
    partnerShiftsEndCheck();
  }, [partnerShiftsEndCheck]);

  const extendShiftOptions = useMemo(() => {
    if (!shiftEnd) return [];

    return populateDropdownTimes({ shift_end: shiftEnd / 1000 });
  }, [shiftEnd]);

  return (
    <>
      {isClockedIn && shiftEndCheck() && (
        <Modal open={shiftEndCheck()} handleClose={(f) => f}>
          <div className="shift-alert-wrapper">
            <h2 className="shift-alert-title">
              {translate("Your session has ended")}
            </h2>
            <div className="shift-alert-content">
              <p className="shift-alert-text">{shiftEndModalText()}</p>
              {shiftEndCheck() === true ? (
                <Select
                  className="shift-alert-extend-select"
                  onInput={(e) => setShiftExtendOption(e.target.value)}
                  options={extendShiftOptions}
                  placeholder={translate("Select time to extend to:")}
                  value={shiftExtendOption}
                />
              ) : (
                <div></div>
              )}
              <Button
                className="shift-alert-button"
                onClick={shiftEndModalClose}
              >
                {translate("Okay")}
              </Button>
              {shiftEndCheck() === true ? (
                <Button
                  className="shift-alert-button extend-shift"
                  onClick={extendShift}
                  disabled={!shiftExtendOption || isNaN(shiftExtendOption)}
                >
                  Extend Session <FontAwesomeIcon icon={faArrowRight} />
                </Button>
              ) : (
                <></>
              )}
            </div>
          </div>
        </Modal>
      )}
      {partnerShifts &&
        !partnerShifts.error &&
        partnerShifts.map((ps) => {
          let id = ps.partner_session_id;
          let { shift_end, partner_name } = ps;

          if (alertPartnersFive.includes(id)) {
            return (
              <Modal
                key={id}
                open={alertPartnersFive.includes(id)}
                handleClose={(f) => f}
              >
                <div className="shift-alert-wrapper">
                  <div className="shift-alert-content partners">
                    <BsExclamationCircle />
                    <p className="shift-alert-text">
                      <span>Partner Session(s) About To Close</span>
                      {partnerShiftEndModalText(
                        `${partner_name}`,
                        shift_end * 1000
                      )}
                    </p>
                    <Button
                      className="shift-alert-button"
                      onClick={() => partnerShiftEndModalClose(id, "FIVE")}
                    >
                      {translate("Okay")}
                    </Button>
                  </div>
                </div>
              </Modal>
            );
          } else if (alertPartnersFifteen.includes(id)) {
            return (
              <Modal
                key={id}
                open={alertPartnersFifteen.includes(id)}
                handleClose={(f) => f}
              >
                <div className="shift-alert-wrapper">
                  <div className="shift-alert-content partners">
                    <BsExclamationCircle />
                    <p className="shift-alert-text">
                      <span>Partner Session(s) About To Close</span>
                      {partnerShiftEndModalText(
                        `${partner_name}`,
                        shift_end * 1000
                      )}
                    </p>
                    <Button
                      className="shift-alert-button"
                      onClick={() => partnerShiftEndModalClose(id, "FIFTEEN")}
                    >
                      {translate("Okay")}
                    </Button>
                  </div>
                </div>
              </Modal>
            );
          } else return <></>;
        })}
    </>
  );
};

export default ShiftEndAlertModals;
