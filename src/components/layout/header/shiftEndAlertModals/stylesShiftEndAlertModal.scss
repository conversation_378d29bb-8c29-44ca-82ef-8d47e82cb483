@import "../../../styles/colors.scss";

div.shift-alert-wrapper {
  padding-bottom: 15px;
  min-height: 150px;
  min-width: 300px;
  background-color: #fff;

  & > h2.shift-alert-title {
    background-color: $fabProBlue;
    color: #fff;
    grid-column: 1/-1;
    margin: 0;
    padding: 0 15px;
    height: 40px;
    line-height: 40px;
    font-size: 1.2rem;
  }

  & > div.shift-alert-content {
    padding: 15px;

    display: grid;
    grid-template-rows: 1fr 40px 40px;
    grid-template-columns: 1fr 1fr;
    column-gap: 15px;
    row-gap: 15px;
    justify-content: center;
    align-items: center;

    &.partners {
      grid-template-rows: 1fr 1fr 40px;
      grid-template-columns: 1fr;

      text-align: center;

      & > svg {
        font-size: 6rem;
        color: darken($yellow, 10%);
        justify-self: center;
      }
    }

    & > p.shift-alert-text {
      color: #333;
      font-weight: 600;
      grid-column: 1/-1;
      text-align: center;

      & > span {
        font-size: 1.6rem;
        display: block;
      }
    }

    & > div {
      grid-column: 1/-1;

      & > div select.shift-alert-extend-select {
        height: 40px;
        font-size: 1.2rem;
      }
    }

    & > button.shift-alert-button {
      width: 200px;
      height: 40px;
      padding: 0;
      background-color: $fabProBlue;
      color: #fff;
      justify-self: center;
      margin: 0 auto;

      &:disabled {
        cursor: default;
      }

      &:hover {
        background-color: darken($fabProBlue, 10%);
      }

      &.extend-shift {
        background-color: $lightGreen;

        &:not(:disabled):hover {
          background-color: darken($lightGreen, 10%);
        }
      }
    }
  }
}
