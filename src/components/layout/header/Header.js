// NPM PACKAGE IMPORTS
import React, { useState, useRef, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faChevronDown } from "@fortawesome/free-solid-svg-icons";
import { useSelector, useDispatch } from "react-redux";
import Button from "msuite_storybook/dist/button/Button";

// REDUX IMPORTS
import { handleClockIn, handleClockOut } from "../../profile/profileActions";
import { handleLogout } from "../../auth/authActions";

// COMPONENT IMPORTS
import Dropdown from "./dropdown/Dropdown";
import LogoutModal from "../../reusable/logoutModal/LogoutModal";
import ShiftEndAlertModals from "./shiftEndAlertModals/ShiftEndAlertModals";
import ClockoutModal from "../../reusable/clockoutModal";
import ManageSessionModal from "./manageSessionModal/ManageSessionModal";

// TRANSLATION IMPORTS
import useTranslations from "../../../hooks/useTranslations";
import headerTranslations from "./headerTranslations.json";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../hooks/useOutsideClick";

// STYLE IMPORTS
import "./header.scss";
import { identifyMixPanelUser } from "../../../utils/_mixPanelUtils";

// EXPORTS
const Header = () => {
  const [isClockedIn, setClockedIn] = useState(false);
  const [showDropdown, toggleDropdown] = useState(false);
  const [lockDropdown, toggleLockDropdown] = useState(false);
  const [showClockOut, toggleClockOut] = useState(false);
  const [showLogoutModal, toggleLogoutModal] = useState(false);
  const [profilePicture, setProfilePicture] = useState(null);
  const [showManageSession, toggleManageSession] = useState(false);
  const [softClockout, setSoftClockout] = useState(false);

  const dispatch = useDispatch();
  const { userInfo, shiftNow, permissions } = useSelector(
    (state) => state.profileData
  );

  useEffect(() => {
    toggleDropdown(false);
    toggleLockDropdown(false);
  }, [showManageSession]);

  const translate = useTranslations(headerTranslations);

  useEffect(() => {
    if (userInfo && userInfo.pass_change)
      return window.location.replace(
        `${process.env.REACT_APP_FABPRO}/change_password.php`
      );

    if (userInfo && userInfo.profile_photo)
      setProfilePicture(userInfo.profile_photo);
  }, [userInfo]);

  useEffect(() => {
    setClockedIn(!!shiftNow);
  }, [shiftNow]);

  const dropdownRef = useRef(null);
  useOutsideClick(dropdownRef, () => {
    toggleDropdown(false);
    toggleLockDropdown(false);
  });

  const handleMouseEnter = () => {
    toggleDropdown(true);
  };

  const handleMouseLeave = () => {
    !lockDropdown && toggleDropdown(false);
  };

  const clockIn = () => {
    dispatch(handleClockIn());
    setSoftClockout(false);
  };
  const endSession = () => {
    dispatch(handleClockOut(shiftNow.id));
  };

  const logout = () => {
    handleLogout();
  };

  const endSessionAndLogout = () => {
    endSession();
    handleLogout(1);
  };

  return (
    <div className="layout-header">
      <link
        href="https://fonts.googleapis.com/css?family=Roboto:400,300,100,500,700,900"
        rel="stylesheet"
        type="text/css"
      ></link>
      <span className="header__nav">
        {permissions && permissions.includes(295) ? (
          <Button
            onClick={() => (isClockedIn ? toggleClockOut(true) : clockIn())}
            className="clockin-out"
          >
            {translate(isClockedIn ? "Clock Out" : "Clock In")}
          </Button>
        ) : (
          <div></div>
        )}
        <span
          onMouseLeave={handleMouseLeave}
          onMouseEnter={handleMouseEnter}
          className={
            lockDropdown
              ? "header__dropdown__button locked"
              : "header__dropdown__button"
          }
          onClick={() => toggleLockDropdown(!lockDropdown)}
          ref={dropdownRef}
        >
          {profilePicture && (
            <img className="profile-picture" src={profilePicture} alt="" />
          )}
          {userInfo && userInfo.first_name}{" "}
          <FontAwesomeIcon icon={faChevronDown} />
          {showDropdown && (
            <Dropdown
              toggleLogout={isClockedIn ? toggleLogoutModal : logout}
              toggleDropdown={toggleDropdown}
              translate={translate}
              toggleManageSession={() => toggleManageSession(true)}
            />
          )}
        </span>
      </span>
      {showClockOut && (
        <ClockoutModal
          translations={headerTranslations}
          showClockOut={showClockOut}
          toggleClockOut={toggleClockOut}
          endSessionAndLogout={endSessionAndLogout}
        />
      )}
      {showLogoutModal && (
        <LogoutModal
          logout={logout}
          fullLogout={endSessionAndLogout}
          showModal={showLogoutModal}
          onClose={() => toggleLogoutModal(false)}
        />
      )}
      <ShiftEndAlertModals
        isClockedIn={isClockedIn}
        softClockout={softClockout}
        setClockedIn={setClockedIn}
      />
      {showManageSession && (
        <ManageSessionModal
          showModal={showManageSession}
          onClose={() => toggleManageSession(false)}
          softClockout={softClockout}
          setSoftClockout={setSoftClockout}
        />
      )}
    </div>
  );
};

export default Header;
