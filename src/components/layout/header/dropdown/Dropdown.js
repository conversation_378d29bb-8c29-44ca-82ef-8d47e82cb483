// NPM PACKAGE IMPORTS
import React from "react";
import { ImUser } from "react-icons/im";
import { FaReg<PERSON><PERSON>, FaPowerOff } from "react-icons/fa";
// import { BiPowerOff } from "react-icons/bi"
import { useSelector } from "react-redux";

// HELPER FUNCTION IMPORTS
import { generateIcon } from "../../../../_utils";

// STYLES
import "../header.scss";

// EXPORTS

// DISPLAY MANAGE SESSION BUTTON IF PERMISSION USER MANAGED SESSION OR HAS SKIP SHIFT AND NOT CLOCK IN
const Dropdown = ({
  toggleDropdown,
  toggleLogout,
  toggleManageSession,
  translate,
}) => {
  const { permissions } = useSelector((state) => state.profileData);

  const handleLogout = () => {
    toggleDropdown(false);
    toggleLogout(true);
  };

  const handleManageSession = () => {
    toggleManageSession();
  };

  return (
    <div className="header--dropdown">
      <a
        onClick={() => toggleDropdown(false)}
        className="header__dropdown-link"
        href={`/profile/`}
      >
        <span>
          <ImUser />
          {translate("My Profile")}
        </span>
      </a>
      <a
        target="_blank"
        href="https://support.msuite.com/portal/en/community/msuite/filter/announcement"
        className="header__dropdown-link"
        rel="noreferrer"
      >
        <span>
          {generateIcon({ type: "ICOMOON", className: "icon-stack-text" })}
          {translate("Releases")}
        </span>
      </a>
      {permissions &&
        (permissions.includes(296) ||
          (permissions.includes(289) && !permissions.includes(295))) && (
          <button
            onClick={handleManageSession}
            className="header__dropdown-link"
          >
            <span>
              <FaRegClock />
              {translate("Manage Session")}
            </span>
          </button>
        )}
      <button onClick={handleLogout} className="header__dropdown-link">
        <span>
          <FaPowerOff />
          {translate("Logout")}
        </span>
      </button>
    </div>
  );
};

export default Dropdown;
