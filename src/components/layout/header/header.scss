@import "../../styles/colors.scss";
@import "../../styles//icons//icomoon/styles";

div.layout-header {
  background-color: $navFab !important;
  position: relative;

  & > .header__nav {
    display: flex;
    width: 200px;
    color: white;
    // justify-content: space-around;
    align-items: center;
    height: inherit;
    float: right;
    margin-right: 250px;

    & > button.clockin-out {
      background-color: $lightGreen;
      border: none;
      padding: 8px 14px;
      color: inherit;
      font-weight: 400;
      font-family: inherit;
      border-radius: 3px;
      height: 38px;
      cursor: pointer;
      font-size: 12px;
      margin-right: 5px;

      &:hover {
        background-color: darken($lightGreen, 10%);
      }
    }
  }

  .header__dropdown__button {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-direction: row;
    width: 100px;
    font-size: 0.8rem;
    cursor: pointer;
    padding: 12px 10px;
    box-sizing: border-box;
    height: 100%;

    img.profile-picture {
      width: 30px;
      border-radius: 50%;
      margin-right: 10px;
    }

    svg {
      font-size: 0.7rem;
      padding-left: 5px;
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.2);
    }
  }

  .header--dropdown {
    position: absolute;
    top: 50px;
    right: 1px;
    background-color: white;
    width: 180px;
    z-index: 20;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;

    & button {
      border: none;
      padding: 0;
    }

    .header__dropdown-link {
      display: flex;
      align-items: center;
      height: 40px;
      text-decoration: none;
      font-size: 0.8rem;
      width: 100%;
      background-color: inherit;
      color: rgb(51, 51, 51);
      border-bottom: 1px solid #ddd;
      text-align: left;

      span {
        padding-left: 10px;
      }

      &:hover {
        background-color: #ddd;
      }

      &:focus {
        outline: none;
      }

      svg,
      i {
        padding-right: 10px;
        font-size: 0.9rem;
        color: #555;
      }

      i {
        padding-left: 5px;
      }
    }

    .header__dropdown-link:last-of-type {
      border-bottom-right-radius: 3px;
      border-bottom-left-radius: 3px;
    }
  }
}

.locked {
  background-color: rgba(0, 0, 0, 0.2);
}
