// NPM PACKAGE IMPORTS
import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { FaExclamationCircle, FaCircle } from "react-icons/fa";
import {
  faChevronRight,
  faExclamationCircle,
} from "@fortawesome/free-solid-svg-icons";
import { useDispatch } from "react-redux";

// REDUX IMPORTS
import { handleUpdateWorkView } from "../../manpower/manpowerActions";

// HELPER FUNCTION IMPORTS
import { generateIcon, permissionLock } from "../../../_utils";
import { useSelector } from "react-redux";

// EXPORTS
const NavLink = ({
  link,
  features,
  navExpanded,
  currentPage,
  translate,
  releasesPageNotification,
  activeTimerNotification,
  genericTimerNotification,
  pendingApprovalsNotification,
  onClick,
}) => {
  const [hovered, setHovered] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const isApprovalPending = useSelector(
    (state) =>
      state.packagesPendingApprovalData.fetchIsApprovalPending.isApprovalPending
  );
  const dispatch = useDispatch();

  useEffect(() => {
    setExpanded(false);
  }, [navExpanded]);

  const to = { pathname: link.destination, state: link.state };
  const internal = /^\//.test(link.destination);

  const generateTitle = () => {
    return (
      <span
        className={`nav-menu-link-title ${
          navExpanded || hovered ? "" : "hidden"
        } ${navExpanded ? "nav-expanded" : ""} ${
          link.subsection ? "expandable" : ""
        }`}
        style={{
          backgroundColor: !navExpanded && hovered ? "#101010" : "inherit",
        }}
        onClick={() => link.subsection && setExpanded(!expanded)}
      >
        {translate(link.title)}
        {navExpanded && link.subsection && (
          <FontAwesomeIcon
            className={`nav-menu-link-subsection-arrow ${
              expanded ? "expanded" : ""
            }`}
            icon={faChevronRight}
          />
        )}
      </span>
    );
  };

  const generateSubsection = () => {
    const featureLockedSubsection = link.subsection.filter(
      (l) => !l.feature || (l.feature && features.includes(l.feature))
    );
    const permissionLockedSubsection = permissionLock(featureLockedSubsection);
    return (
      <div
        className={`nav-menu-link-subsection ${
          (!navExpanded && hovered) || (navExpanded && expanded) ? "" : "hidden"
        } ${expanded ? "expanded" : ""}`}
      >
        {permissionLockedSubsection.map((s) => {
          const subTo = { pathname: s.destination, state: s.state };
          const usersClick = () => {
            dispatch(handleUpdateWorkView(false));
            onClick();
          };

          const subInternal = /^\//.test(s.destination);
          if (!subInternal) {
            return (
              <a
                key={s.title}
                href={s.destination}
                className={`nav-menu-link-subsection-link ${
                  expanded ? "expanded" : ""
                } ${
                  (!navExpanded && hovered) || (navExpanded && expanded)
                    ? ""
                    : "hidden"
                }`}
                onClick={onClick}
              >
                {generateIcon(s, true)}
                <span className="nav-menu-link-subsection-title">
                  {translate(s.title)}
                </span>
              </a>
            );
          }
          return (
            <Link
              key={s.title}
              to={subTo}
              className={`nav-menu-link-subsection-link ${
                expanded ? "expanded" : ""
              } ${
                (!navExpanded && hovered) || (navExpanded && expanded)
                  ? ""
                  : "hidden"
              }`}
              onClick={s.title === "Users" ? usersClick : onClick}
            >
              {generateIcon(s, true)}
              <span className="nav-menu-link-subsection-title">
                {translate(s.title)}
              </span>
            </Link>
          );
        })}
      </div>
    );
  };

  if (link.subsection) {
    return (
      <span
        key={link.title}
        href={link.destination}
        className={`nav-menu-link ${navExpanded ? "nav-expanded" : ""} ${
          expanded ? "expanded" : ""
        } ${currentPage ? "current-page" : ""}`}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
        style={{ maxHeight: `${(link.subsection.length + 1) * 40}px` }}
      >
        {generateIcon(link, true)}
        {navExpanded && generateTitle()}
        {generateSubsection()}
      </span>
    );
  }
  if (!internal) {
    return (
      <a
        key={link.title}
        href={link.destination}
        className={`nav-menu-link ${link.title === "Home" ? "home" : ""} ${
          navExpanded ? "nav-expanded" : ""
        } ${expanded ? "expanded" : ""} ${currentPage ? "current-page" : ""}`}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
        onClick={onClick}
        target={link.target ? link.target : ""}
      >
        {generateIcon(link, true)}
        {link.title === "Releases" && releasesPageNotification && (
          <FontAwesomeIcon
            icon={faExclamationCircle}
            className="releases-notification-icon"
          />
        )}
        {link.title === "My Work" &&
          activeTimerNotification &&
          !genericTimerNotification && (
            <FaExclamationCircle className="active-timer-notification-icon" />
          )}
        {link.title === "Pending Approvals" &&
          pendingApprovalsNotification &&
          isApprovalPending && (
            <FaExclamationCircle className="pending-approvals-notification-icon" />
          )}
        {link.title === "Generic Time" && genericTimerNotification && (
          <FaExclamationCircle className="generic-timer-notification-icon" />
        )}
        {generateTitle()}
      </a>
    );
  }

  return (
    <Link
      key={link.title}
      to={to}
      className={`nav-menu-link ${link.title === "Home" ? "home" : ""} ${
        navExpanded ? "nav-expanded" : ""
      } ${expanded ? "expanded" : ""} ${currentPage ? "current-page" : ""}`}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      onClick={onClick}
    >
      {generateIcon(link, true)}
      {link.title === "Releases" && releasesPageNotification && (
        <FontAwesomeIcon
          icon={faExclamationCircle}
          className="releases-notification-icon"
        />
      )}
      {link.title === "My Work" &&
        activeTimerNotification &&
        !genericTimerNotification && (
          <FaExclamationCircle className="active-timer-notification-icon" />
        )}
      {link.title === "Generic Time" && genericTimerNotification && (
        <FaExclamationCircle className="generic-timer-notification-icon" />
      )}

      {link.title === "Dashboard Analytics" && (
        <FaCircle className="new-feature-notification-icon" />
      )}
      {generateTitle()}
    </Link>
  );
};

export default NavLink;
