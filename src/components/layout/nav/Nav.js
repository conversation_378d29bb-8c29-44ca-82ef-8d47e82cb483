// NPM PACKAGE IMPORTS
import React, { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import { handleSetNavExpanded } from "../../../redux/generalActions";
import { handleClearBreadcrumbs } from "../body/breadcrumbs/breadcrumbsActions";

// COMPONENT IMPORTS
import NavLink from "./NavLink";

// TRANSLATION IMPORTS
import useTranslations from "../../../hooks/useTranslations";
import navTranslations from "./navTranslations.json";

// CONSTANTS IMPORTS
import { links } from "./navConstants";

// HELPER FUNCTION IMPORTS
import { permissionLock } from "../../../_utils";

import { handleFetchApprovalsPending } from "../../pendingApproval/packages/packagesPendingApprovalActions";

// STYLE IMPORTS
import "./stylesNav.scss";

// EXPORTS
const Nav = () => {
  const translate = useTranslations(navTranslations);
  const dispatch = useDispatch();
  const { navExpanded, pageTitle } = useSelector((state) => state.generalData);
  const {
    userSettings,
    features,
    releasesPageNotification,
    permissions,
  } = useSelector((state) => state.profileData);
  const { activeTimer } = useSelector((state) => state.timerData);
  const { drawingsPendingApprovalFlag } = useSelector(
    (state) => state.drawingsData
  );

  useEffect(() => {
    if (userSettings)
      dispatch(handleSetNavExpanded(!!userSettings.expand_menu));
    dispatch(handleFetchApprovalsPending());
  }, [dispatch, userSettings]);

  const homePagePath =
    userSettings && userSettings.home_page
      ? userSettings.home_page
      : permissions && permissions.includes(25)
      ? "jobs"
      : "my-work";
  const currentPage = (link) => {
    const pathnameToUse = /\/$/.test(window.location.pathname)
      ? window.location.pathname.slice(0, -1)
      : window.location.pathname;
    const subsectionDestinations = link.subsection
      ? link.subsection
          ?.filter(
            (l) => !l.feature || (l.feature && features.includes(l.feature))
          )
          .map((l) => l.destination)
      : [];
    return (
      pageTitle === link.title ||
      new RegExp(pathnameToUse, "i").test(link.destination) ||
      subsectionDestinations.includes(pathnameToUse)
    );
  };
  const activeTimerNotification = !!activeTimer;
  const pendingApprovalsNotification = !!drawingsPendingApprovalFlag?.has_pending_drawings;
  const genericTimerNotification =
    activeTimer && activeTimer.length && activeTimer[0].type === "generic";
  const generatedLinks = links(homePagePath);
  const featureLockedLinks = useMemo(
    () =>
      generatedLinks.filter((l) => {
        if (features) {
          const featureDisabled =
            !l.featureDisabled || !features.includes(l.featureDisabled);
          const featureEnabled = l.feature && features.includes(l.feature);

          if ((!l.feature && featureDisabled) || featureEnabled) return true;
          else return false;
        }

        return false;
      }),
    [features]
  );
  const permissionLockedLinks = useMemo(
    () => permissionLock(featureLockedLinks),
    [featureLockedLinks]
  );
  const onClick = () => {
    dispatch(handleClearBreadcrumbs);
  };

  return (
    <div className="layout-nav">
      <div
        className="nav-expander"
        onClick={() => dispatch(handleSetNavExpanded(!navExpanded))}
      >
        <i className="icon-paragraph-justify3 nav-icon" />
      </div>
      <div className={`nav-menu ${navExpanded ? "nav-expanded" : ""}`}>
        {pageTitle !== null &&
          permissionLockedLinks.map((link, idx) => (
            <React.Fragment key={link.title}>
              <NavLink
                link={link}
                features={features}
                navExpanded={navExpanded}
                currentPage={currentPage(link)}
                translate={translate}
                releasesPageNotification={releasesPageNotification}
                activeTimerNotification={activeTimerNotification}
                pendingApprovalsNotification={pendingApprovalsNotification}
                genericTimerNotification={genericTimerNotification}
                onClick={onClick}
              />
            </React.Fragment>
          ))
        // generatedLinks.map((link, idx) => (
        //   <React.Fragment key={link.title}>
        //     <NavLink
        //       link={link}
        //       navExpanded={navExpanded}
        //       currentPage={currentPage(link)}
        //       translate={translate}
        //       releasesPageNotification={releasesPageNotification}
        //       onClick={onClick}
        //     />
        //   </React.Fragment>
        // ))
        }
      </div>
    </div>
  );
};

export default Nav;
