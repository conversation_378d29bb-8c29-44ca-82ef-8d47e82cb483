export const links = (home_page) => {
  const homePagePath = () => {
    if (/\.php/.test(home_page) || !home_page) {
      return `${process.env.REACT_APP_FABPRO}/${home_page}`;
    }

    return `/${home_page}/`;
  };

  return [
    {
      type: "ICOMOON",
      title: "Home",
      className: "nav-menu-link-icon icon-home4",
      destination: homePagePath(),
    },
    {
      type: "ICOMOON",
      title: "My Work",
      className: "nav-menu-link-icon icon-checkbox-checked",
      destination: `/my-work/`,
    },
    {
      type: "LETTER",
      title: "Generic Time",
      className: "nav-menu-link-icon",
      icon: "G",
      destination: `/generic-time/`,
      permissions: [260],
    },
    {
      type: "ICOMOON",
      title: "Active Work",
      className: "nav-menu-link-icon icon-alarm",
      destination: `${process.env.REACT_APP_FABPRO}/active_timers.php`,
      permissions: [19],
    },
    {
      type: "LETTER",
      title: "Jobs",
      className: "nav-menu-link-icon",
      icon: "J",
      destination: `/jobs/`,
      permissions: [25],
    },
    {
      type: "ICOMOON",
      title: "Pending Approvals",
      className: "nav-menu-link-icon icon-drawer-in",
      destination: `${process.env.REACT_APP_FABPRO}/packages-pending-approval/?tab=incoming`,
      permissions: [231],
    },
    {
      type: "ICOMOON",
      title: "Shipping",
      className: "nav-menu-link-icon icon-truck",
      destination: `${process.env.REACT_APP_FABPRO}/shipping/bill_of_lading.php`,
      permissions: [252],
    },
    {
      type: "ICOMOON",
      title: "Reports",
      className: "nav-menu-link-icon icon-statistics",
      destination: `${process.env.REACT_APP_FABPRO}/analytics/reports.php`,
      permissions: [21],
    },
    {
      type: "ICOMOON",
      title: "Dashboard Analytics",
      className: "nav-menu-link-icon icon-stats-bars4",
      destination: `/dashboard-analytics`,
      permissions: [304],
      feature: 35,
    },
    {
      type: "ICOMOON",
      title: "Manpower",
      className: "nav-menu-link-icon icon-users4",
      destination: `${process.env.REACT_APP_FABPRO}/manpower/shifts.php`,
      permissions: [290],
      featureDisabled: 49,
    },
    {
      type: "ICOMOON",
      title: "Manpower",
      className: "nav-menu-link-icon icon-users4",
      permissions: [290],
      feature: 49,
      subsection: [
        {
          type: "ICOMOON",
          title: "Shifts",
          className: "nav-menu-link-icon icon-calendar",
          destination: `${process.env.REACT_APP_FABPRO}/manpower/shifts.php`,
        },
        {
          type: "ICOMOON",
          title: "Manage Assignments",
          className: "nav-menu-link-icon icon-collaboration",
          destination: `/manpower/assignments/`,
        },
        //     {
        //       type: "ICON",
        //       title: "Manpower Home",
        //       className: "nav-menu-link-icon",
        //       icon: faHome,
        //       destination: `/manpower`,
        //       permissions: [290]
        //     },
        //     {
        //       type: "ICON",
        //       title: "Users",
        //       className: "nav-menu-link-icon",
        //       icon: faUsers,
        //       destination: `/manpower`,
        //       permissions: [290]
        //     },
        //     {
        //       type: "ICON",
        //       title: "Shifts",
        //       className: "nav-menu-link-icon",
        //       icon: faClock,
        //       destination: `/manpower`,
        //       permissions: [290]
        //     }
      ],
    },
    {
      type: "ICOMOON",
      title: "Settings",
      className: "nav-menu-link-icon icon-gear",
      permissions: [62],
      subsection: [
        {
          type: "ICOMOON",
          title: "Throughputs",
          className: "nav-menu-link-icon icon-equalizer",
          destination: `/settings/throughputs/`,
        },
        {
          type: "ICOMOON",
          title: "Cost Codes",
          className: "nav-menu-link-icon icon-list-ordered",
          destination: `${process.env.REACT_APP_FABPRO}/settings/codes.php`,
        },
        {
          type: "ICOMOON",
          title: "Flows, Stages",
          className: "nav-menu-link-icon icon-list-ordered",
          destination: `/settings/flows/`,
        },
        {
          type: "ICOMOON",
          title: "Tasks",
          className: "nav-menu-link-icon icon-list-ordered",
          destination: `${process.env.REACT_APP_FABPRO}/settings/tasks.php`,
        },
        {
          type: "ICOMOON",
          title: "Custom Columns",
          className: "nav-menu-link-icon icon-list-ordered",
          destination: `/settings/custom-columns/`,
          feature: 53,
          permissions: [303],
        },
        {
          type: "ICOMOON",
          title: "Tiers & Permissions",
          className: "nav-menu-link-icon icon-eye",
          destination: `${process.env.REACT_APP_FABPRO}/settings/permissions.php`,
        },
        {
          type: "ICOMOON",
          title: "Titles",
          className: "nav-menu-link-icon icon-list-ordered",
          destination: `${process.env.REACT_APP_FABPRO}/settings/titles.php`,
        },
        {
          type: "ICOMOON",
          title: "Materials/Joining Procedures",
          className: "nav-menu-link-icon icon-list-ordered",
          destination: `${process.env.REACT_APP_FABPRO}/settings/materials.php`,
        },
        {
          type: "ICOMOON",
          title: "Laydown Locations",
          className: "nav-menu-link-icon icon-list-ordered",
          destination: `${process.env.REACT_APP_FABPRO}/settings/laydownlocations.php`,
        },
        {
          type: "ICOMOON",
          title: "Users",
          className: "nav-menu-link-icon icon-users",
          destination: `/users/`,
        },
        {
          type: "ICOMOON",
          title: "General Settings",
          className: "nav-menu-link-icon icon-gear",
          destination: `${process.env.REACT_APP_FABPRO}/settings/systemsettings.php`,
        },
        {
          type: "ICOMOON",
          title: "Autodesk Account Settings",
          className: "nav-menu-link-icon icon-office",
          destination: `/autodesk-account-settings/`,
          feature: 47,
        },
      ],
    },
    {
      type: "ICOMOON",
      title: "Help",
      className: "nav-menu-link-icon icon-help",
      destination: `https://support.msuite.com`,
      target: "_blank",
      feature: 7,
    },
    {
      type: "LETTER",
      title: "Archive Area",
      className: "nav-menu-link-icon",
      icon: "A",
      permissions: [101],
      feature: 8,
      subsection: [
        {
          type: "LETTER",
          title: "Archived Jobs",
          className: "nav-menu-link-icon",
          icon: "J",
          destination: `${process.env.REACT_APP_FABPRO}/archive-area/`,
        },
        {
          type: "LETTER",
          title: "Archived Packages",
          className: "nav-menu-link-icon",
          icon: "P",
          destination: `${process.env.REACT_APP_FABPRO}/archive-area/archived-packages.php`,
        },
      ],
    },
  ];
};
