@import "../../styles/colors.scss";

div.nav-expander {
  box-sizing: border-box;

  display: flex;
  justify-content: center;
  background-color: $navFab;
  padding-top: 26px;

  &:hover {
    cursor: pointer;
    background-color: darken($grey, 5%);
  }

  i.nav-icon {
    color: #fff;
    font-size: 1rem;
  }
}

div.nav-menu {
  grid-row: 2/-1;
  grid-column: 1/-1;

  position: relative;

  overflow-y: scroll;
  &::-webkit-scrollbar {
    display: none;
  }

  & a.nav-menu-link,
  span.nav-menu-link {
    height: 44px;
    color: #ccc;
    text-decoration: none;
    transition: all 0.3s ease;

    display: grid;
    grid-template-columns: 55px 1fr;

    position: relative;

    &.expanded {
      grid-template-rows: 40px 1fr;

      color: #fff;
      height: 100%;

      & svg.nav-svg-icon {
        fill: #fff;
      }
    }

    &.current-page {
      background-color: $darkFab;
      color: #fff;

      & svg.nav-svg-icon {
        fill: #fff;
      }
    }

    &.home i {
      color: #1997c6;
    }

    &:not(.home):hover {
      color: #fff;

      & svg.nav-svg-icon {
        fill: #fff;
      }
    }

    &:not(.nav-expanded):not(.current-page):hover {
      background-color: lighten($grey, 10%);
    }

    &.nav-expanded:not(.expanded):not(.current-page):hover {
      background-color: darken($grey, 5%);
    }

    & .nav-menu-link-icon {
      justify-self: center;

      font-size: 1rem;
    }

    & svg.nav-svg-icon {
      fill: #ccc;
      height: 20px;
      width: 20px;
      transition: all 0.3s ease;

      & path#c,
      path#d {
        transform: matrix(-1, 0, 0, -1, 512, 512);
      }
    }

    & span.nav-menu-link-icon {
      grid-row: 1/2;
      grid-column: 1/2;
      font-family: "icomoon";
    }

    & span.nav-menu-link-title {
      width: 200px;
      height: 44px;
      line-height: 44px;
      padding-left: 10px;
      box-sizing: border-box;
      font-size: 0.8rem;

      &:not(.nav-expanded) {
        position: fixed;
        left: 55px;
      }

      &.expandable {
        display: grid;
        grid-template-columns: 1fr 40px;
      }

      &.expanded {
        width: 200px;
      }

      & svg.nav-menu-link-subsection-arrow {
        transition: all 0.3s ease;

        align-self: center;

        &.expanded {
          transform: rotate(90deg);
        }
      }
    }
  }
}

div.nav-menu-link-subsection {
  box-sizing: border-box;
  background-color: #263238;

  &:not(.hidden):not(.expanded) {
    display: grid;

    width: 250px;
    position: fixed;
    left: 55px;
    transform: translateY(calc(-100% + 45px));

    overflow-y: scroll;
    max-height: 200px;
  }

  &.expanded {
    width: 100%;
    height: 100%;
    background-color: #263238;

    grid-row: 2/-1;
    grid-column: 1/-1;
  }

  & a.nav-menu-link-subsection-link:not(.hidden),
  span.nav-menu-link-subsection-link:not(.hidden) {
    height: 38.5px;
    font-size: 0.8rem;
    font-weight: 500 !important;
    text-decoration: none;
    color: rgba(255, 255, 255, 0.75);
    transition: all 0.3s ease;

    display: grid;
    grid-template-columns: 40px 1fr;
    align-items: center;

    &:hover {
      background-color: rgba(0, 0, 0, 0.2);
    }
  }

  &.expanded {
    & a.nav-menu-link-subsection-link,
    span.nav-menu-link-subsection-link {
      padding-left: 40px;

      &:hover {
        background-color: rgba(0, 0, 0, 0.2);
      }
    }
  }
}

svg.releases-notification-icon,
svg.pending-approvals-notification-icon,
svg.generic-timer-notification-icon,
svg.active-timer-notification-icon {
  position: absolute;
  top: 1px;
  right: 9px;
  color: $red;
  border: 1px solid #000;
  background-color: #333;
  border-radius: 50%;
}

svg.new-feature-notification-icon {
  position: absolute;
  border: 0;
  background-color: #1997c6;
  border-radius: 50%;
  color: #1997c6;
  height: 7px;
  width: 7px;
}

.nav-expanded svg.new-feature-notification-icon {
  top: 20px;
  right: 25px;
}

svg.new-feature-notification-icon:not(.nav-expanded
    svg.new-feature-notification-icon) {
  top: 7px;
  right: 7px;
}
