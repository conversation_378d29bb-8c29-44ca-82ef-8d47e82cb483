// STYLE IMPORTS
import "../styles/tables.scss";

// EXPORTS
export const materialsColumnDefs = (
  editRow,
  selectedRow,
  stages,
  materials,
  editedFields,
  setEditedFields
) => {
  return [
    {
      headerName: "Session",
      width: 100,
      cellRenderer: "timerButtonCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Category",
      field: "work_item_category_name",
      width: 100,
      suppressMenu: true,
    },
    {
      headerName: "Stage",
      field: "stage_name",
      width: 120,
      valueFormatter: (params) => ({
        columnName: "stage_name",
        editing: editRow && selectedRow.id === params.data.id,
        options: stages,
        editedFields,
        setEditedFields,
      }),
      // cellRenderer: "dropdownCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Drawing",
      field: "drawing_name",
      width: 80,
      suppressMenu: true,
    },
    {
      headerName: "Material",
      field: "material_name",
      width: 100,
      valueFormatter: (params) => ({
        columnName: "material_name",
        editing: editRow && selectedRow.id === params.data.id,
        options: materials,
        editedFields,
        setEditedFields,
      }),
      // cellRenderer: "dropdownCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Tag #",
      field: "tag_number",
      width: 80,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "tag_number",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Laydown Location",
      field: "laydown_location",
      width: 100,
      // hide: true,
      valueFormatter: (params) => ({
        columnName: "laydown_location",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      // cellRenderer: "dropdownCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Hanger Size",
      field: "hanger_size",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "hangar_size",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Product Code",
      field: "product_code",
      width: 80,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "product_code",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Insulation",
      field: "insulation",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "insulation",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Insulation Area",
      field: "insulation_area",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "insulation_area",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Insulation Spec",
      field: "insulation_specificiation",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "insulation_specification",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Insulation Guage",
      field: "insulation_guage",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "insulation_guage",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Joining Procedure",
      field: "joining_procedure",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "joining_procedure",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Service",
      field: "service",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "service",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Service Color",
      field: "service_color_name",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "service_color_name",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Status",
      field: "status",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "status",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Size",
      field: "size",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "size",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Length",
      field: "length",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "random_length",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Qty",
      field: "quantity",
      width: 100,
      valueFormatter: (params) => ({
        columnName: "quantity",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "End Prep 1",
      field: "end_prep_1",
      width: 100,
      valueFormatter: (params) => ({
        columnName: "end_prep_1",
        editing: editRow && selectedRow.id === params.data.id,
        options: materials,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "End Prep 2",
      field: "end_prep_2",
      width: 100,
      valueFormatter: (params) => ({
        columnName: "end_prep_2",
        editing: editRow && selectedRow.id === params.data.id,
        options: materials,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "End Prep 3",
      field: "end_prep_3",
      width: 100,
      valueFormatter: (params) => ({
        columnName: "end_prep_3",
        editing: editRow && selectedRow.id === params.data.id,
        options: materials,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "End Prep 4",
      field: "end_prep_4",
      width: 100,
      valueFormatter: (params) => ({
        columnName: "end_prep_4",
        editing: editRow && selectedRow.id === params.data.id,
        options: materials,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    // STRING
    {
      headerName: "Heat #",
      field: "heat_number",
      width: 100,
      valueFormatter: (params) => ({
        columnName: "",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Joint Heat Numbers",
      field: "joint_heat_numbers",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "joint_heat_numbers",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Height",
      field: "height",
      width: 80,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "height",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Width",
      field: "width",
      width: 80,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "width",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Thickness",
      field: "thickness",
      width: 80,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "thickness",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Paint Spec",
      field: "paint_spec",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "paint_spec",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Texture",
      field: "texture",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "texture",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Fixture",
      field: "fixture_type",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "fixture_type",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      // cellRenderer: "dropdownCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Hanger Size",
      field: "hanger_size",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "hangar_size",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Product Code",
      field: "product_code",
      width: 80,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "product_code",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Insulation",
      field: "insulation",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "insulation",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Insulation Area",
      field: "insulation_area",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "insulation_area",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Insulation Spec",
      field: "insulation_specificiation",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "insulation_specification",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Insulation Guage",
      field: "insulation_guage",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "insulation_guage",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Joining Procedure",
      field: "joining_procedure",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "joining_procedure",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Service",
      field: "service",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "service",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Service Color",
      field: "service_color_name",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "service_color_name",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Status",
      field: "status",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "status",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Size",
      field: "size",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "size",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      // IF EDITED, ADD TO ROUNDED_CUT_LENGTH
      headerName: "Random Length",
      field: "random_length",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "random_length",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Rod Size",
      field: "rod_size",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "row_size",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    // STRING
    {
      headerName: "Support Rod Length 1",
      field: "support_rod_length",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "support_rod_length",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    // STRING
    {
      headerName: "Support Rod Length 2",
      field: "support_rod_length_2",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "support_rod_length_2",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Liner Spec",
      field: "liner_spec",
      width: 100,
      hide: true,
      valueFormatter: (params) => ({
        columnName: "liner_spec",
        editing: editRow && selectedRow.id === params.data.id,
        editedFields,
        setEditedFields,
      }),
      cellRenderer: "textInputCellRenderer",
      suppressMenu: true,
    },
    {
      headerName: "Manage",
      sortable: false,
      width: 80,
      cellRenderer: "moreInfoCellRenderer",
      suppressMenu: true,
    },
  ];
};
