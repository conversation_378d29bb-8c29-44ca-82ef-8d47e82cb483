import {
  fetchItems,
  archiveItem,
  deleteItem,
  updateWorkItems,
  fetchMaterialTypes,
  createMaterialType,
  fetchJoiningProcedures,
  fetchBOM,
  fetchWorkItemCategories,
  updateJointHeatNumber,
  completeUncomplete,
  fetchWorkableItems,
  createItemsViaCsv,
  fetchRejectionHistory,
  resolveRejection,
  fetchSentBackStages,
  rejectItems,
  fetchRejectionCategories,
  editGroupedItems,
  fetchItemRejectionsByPackage,
  createLaydownLocation,
  fetchAvailableStages,
  fetchWorkItemColumns,
  createJoiningProcedure,
  fetchItemsById,
  fetchItemsv2,
  createItems,
} from "../../_services";
import { trackMixPanelEvent } from "../../utils/_mixPanelUtils";
import store from "../../redux/store";
import utils from "../../_utils";

export const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
export const receiveSucceeded = (type, items) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload: items,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});
export const updateStarted = (type) => ({ type: `UPDATE_${type}_STARTED` });
export const updateSucceeded = (type, payload) => ({
  type: `UPDATE_${type}_SUCCEEDED`,
  payload,
});
export const updateFailed = (type, error) => ({
  type: `UPDATE_${type}_FAILED`,
  payload: error,
});
export const receiveAllItemsStarted = () => ({
  type: "RECEIVE_ALL_ITEMS_STARTED",
});
export const receiveAllItemsSucceeded = (items) => ({
  type: "RECEIVE_ALL_ITEMS_SUCCEEDED",
  payload: items,
});
export const receiveAllItemsFailed = (error) => ({
  type: "RECEIVE_ALL_ITEMS_FAILED",
  payload: error,
});
export const createStarted = (type) => ({ type: `CREATE_${type}_STARTED` });
export const createSucceeded = (type, items) => ({
  type: `CREATE_${type}_SUCCEEDED`,
  payload: items,
});
export const createFailed = (type, error) => ({
  type: `CREATE_${type}_FAILED`,
  payload: error,
});
export const archiveStarted = (type) => ({ type: `ARCHIVE_${type}_STARTED` });
export const archiveEnded = (type) => ({ type: `ARCHIVE_${type}_ENDED` });

export const handleClearItemsSavedColumnState = (dispatch) => {
  dispatch(receiveSucceeded("ITEMS_CLEAR_COLUMN_STATE"));
};
export const handleClearItemsState = (dispatch) => {
  dispatch(receiveSucceeded("CLEAR_ITEMS_STATE"));
};

export const handleFetchItems = (
  jobIds,
  packageIds,
  drawingIds,
  stageIds,
  showAll,
  includeArchived,
  grouping,
  groupingColumns
) => (dispatch) => {
  dispatch(receiveStarted("ITEMS"));
  return fetchItems(
    jobIds,
    packageIds,
    drawingIds,
    stageIds,
    showAll,
    includeArchived,
    grouping,
    groupingColumns
  ).then((res) => {
    if (res.error) return dispatch(receiveFailed("ITEMS", res));
    else dispatch(receiveSucceeded("ITEMS", res));

    return res;
  });
};
export const handleFetchAllItems = (dispatch) => {
  dispatch(receiveAllItemsStarted());
  return fetchItems([], [], null, true).then((res) => {
    if (res.error) return dispatch(receiveAllItemsFailed(res));
    return dispatch(receiveAllItemsSucceeded(res));
  });
};
export const handleUpdateItems = (
  idsToUpdate,
  updatedData,
  parentData,
  callback,
  quantity
) => (dispatch) => {
  dispatch(updateStarted("ITEMS"));
  return updateWorkItems(idsToUpdate, updatedData, quantity).then(
    async (res1) => {
      if (res1.error) dispatch(updateFailed("ITEMS", res1));
      else dispatch(updateSucceeded("ITEMS", res1));

      if (!res1.error) {
        if (
          parentData &&
          parentData.isWorkable &&
          callback &&
          typeof callback === "function"
        ) {
          callback();
        } else if (parentData) {
          dispatch(receiveStarted("ITEMS"));
          await fetchItems(...parentData).then((res2) => {
            if (res2.error) dispatch(receiveFailed("ITEMS", res2));
            else dispatch(receiveSucceeded("ITEMS", res2));
          });
        }
      }

      return res1;
    }
  );
};

export const handleUpdateJointHeatNumber = (
  heatNumberObj,
  parentData,
  callback
) => (dispatch) => {
  const type = "ITEMS";
  dispatch(updateStarted(type));
  return updateJointHeatNumber(heatNumberObj).then((res) => {
    if (res.error) dispatch(updateFailed(type, res));
    else dispatch(updateSucceeded(type, res));

    if (
      parentData &&
      parentData.isWorkable &&
      callback &&
      typeof callback === "function"
    ) {
      return callback();
    } else if (parentData) {
      return fetchItems(...parentData).then((res) => {
        dispatch(receiveStarted(type));
        if (res.error) return dispatch(receiveFailed(type, res));
        return dispatch(receiveSucceeded(type, res));
      });
    } else return res;
  });
};

export const handleFetchMaterialTypes = (serviceTypeIds) => (dispatch) => {
  return fetchMaterialTypes(serviceTypeIds).then((res) => {
    if (res.error) return dispatch(receiveFailed("MATERIAL_TYPES", res));
    return dispatch(receiveSucceeded("MATERIAL_TYPES", res));
  });
};

export const handleFetchJoiningProcedures = (dispatch) => {
  return fetchJoiningProcedures().then((res) => {
    if (res.error) return dispatch(receiveFailed("JOINING_PROCEDURES", res));
    let result =
      res && Array.isArray(res) && res.length
        ? res.sort((a, b) =>
            a.name.toLowerCase() < b.name.toLowerCase()
              ? -1
              : a.name.toLowerCase() > b.name.toLowerCase()
              ? 1
              : 0
          )
        : [];
    return dispatch(receiveSucceeded("JOINING_PROCEDURES", result));
  });
};

export const handleFetchBOM = (parentIds, type, isGrouped) => (dispatch) => {
  dispatch(receiveStarted("BOM"));

  const mixPanelEventStart = Date.now();

  return fetchBOM(parentIds, type, isGrouped).then((res) => {
    if (res.error) {
      dispatch(receiveFailed("BOM", res));
    } else {
      if (window && window.ChurnZero) {
        const { username } = store.getState().profileData.userInfo;
        const { userId } = store.getState().profileData;

        window.ChurnZero.push([
          "trackEvent",
          "FP View BOM",
          `${type} IDs: ${parentIds.join(",")}, Items ${
            isGrouped ? "Grouped" : "Ungrouped"
          }`,
          1,
          {
            Product: "FabPro",
            SubGroup: "BOM",
            Version: process.env.REACT_APP_ENVIRONMENT,
            UserName: username,
            UserId: userId,
          },
        ]);
      }
      trackMixPanelEvent(
        "Fetch BOM",
        res.length,
        "Bill of Materials",
        mixPanelEventStart
      );

      dispatch(receiveSucceeded("BOM", res));
    }
  });
};

export const handleCreateItemsViaCsv = (jobId, packageId, items) => (
  dispatch
) => {
  dispatch(createStarted("ITEMS"));
  return createItemsViaCsv(jobId, packageId, items).then((res) => {
    if (res.error) return dispatch(createFailed("ITEMS", res));
    return dispatch(createSucceeded("ITEMS", res[2]));
  });
};

export const handleCreateItems = (jobId, packageId, items) => (dispatch) => {
  return createItems(jobId, packageId, items);
};

export const handleFetchWorkItemCategories = (dispatch) => {
  const type = "WORK_ITEM_CATEGORIES";

  dispatch(receiveStarted(type));
  return fetchWorkItemCategories().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleArchiveItem = (itemId) => (dispatch) => {
  const type = "ITEMS";
  dispatch(updateStarted(type));
  return archiveItem(itemId).then((res) => {
    dispatch(updateSucceeded(type));
    return res;
  });
};

export const handleDeleteItem = (itemId) => (dispatch) => {
  const type = "ITEMS";
  dispatch(updateStarted(type));
  return deleteItem(itemId).then((res) => {
    dispatch(updateSucceeded(type));
    return res;
  });
};

export const handleCreateMaterialType = (materialName) => (dispatch) => {
  const type = "MATERIAL_TYPE";
  dispatch(createStarted(type));
  return createMaterialType(materialName).then((res) => {
    if (res.error) dispatch(createFailed(type, res));
    else dispatch(createSucceeded(type));

    return res;
  });
};

export const handleCreateJoiningProcedure = (name) => (dispatch) => {
  const type = "JOINING_PROCEDURES";
  dispatch(createStarted(type));
  return createJoiningProcedure(name).then((res) => {
    if (res.error) dispatch(createFailed(type, res));
    else dispatch(createSucceeded(type));

    return res;
  });
};

export const handleCompleteUncomplete = (
  // 0 for uncomplete, 1 for complete
  direction,
  stageId,
  workItemId,
  all
  // callback
) => (dispatch) => {
  const mixPanelEventStart = Date.now();

  return completeUncomplete(direction, stageId, workItemId, all).then((res) => {
    if (typeof res === "string" || (typeof res === "object" && !res.error)) {
      if (window && window.ChurnZero) {
        const { username } = store.getState().profileData.userInfo;
        const { userId } = store.getState().profileData;

        window.ChurnZero.push([
          "trackEvent",
          `FP ${direction === 1 ? "Complete" : "Uncomplete"}`,
          `Stage ID: ${all ? "all" : stageId}, Work Item ID: ${workItemId}`,
          1,
          {
            Product: "FabPro",
            SubGroup: "Work",
            Version: process.env.REACT_APP_ENVIRONMENT,
            UserName: username,
            UserId: userId,
          },
        ]);
      }

      trackMixPanelEvent(
        `${direction === 1 ? "Complete" : "Uncomplete"}`,
        1,
        "Items",
        mixPanelEventStart,
        `Stage ID: ${all ? "all" : stageId}, Work Item ID: ${workItemId}`
      );

      dispatch(
        updateSucceeded("SINGLE_ITEM", {
          id: workItemId,
          completed: direction,
        })
      );
    }

    // if ((callback && typeof callback === 'function')) callback();
    return res;
  });
};

export const handleResetItemsReducer = (dispatch) => {
  dispatch(receiveSucceeded("ITEMS", []));
  dispatch(receiveSucceeded("WORKABLE_ITEMS", []));
};

export const handleFetchWorkableItems = (
  jobIds,
  packageIds,
  drawingIds,
  stageIds,
  groupingType,
  maxGroupQuantity,
  callback
) => (dispatch) => {
  const type = "WORKABLE_ITEMS";
  dispatch(receiveStarted(type));
  return fetchWorkableItems(
    jobIds,
    packageIds,
    drawingIds,
    stageIds,
    groupingType,
    maxGroupQuantity
  ).then((res) => {
    if (res.error) {
      dispatch(receiveFailed(type, res.error));
      return res;
    }

    // turn drawing priority into rank (ex. "6,9,4" -> "1,2,3")
    // PERFORMANCE IMPROVEMENT - move this into the API/sproc and return with the workableItems response
    const ranks = utils.transformDrawingPriorityToRank(res);

    const itemsWithRank = res.map((item) => {
      const rank = ranks.find((r) => r.id === item.drawing_id);

      return {
        ...item,
        drawing_priority: rank.drawing_priority || null,
      };
    });

    dispatch(receiveSucceeded(type, itemsWithRank));

    if (callback && typeof callback === "function") callback();
    return res;
  });
};

export const handleFetchRejectionHistory = (workItemId) => (dispatch) => {
  const type = "REJECTION_HISTORY";

  return fetchRejectionHistory(workItemId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleResolveRejection = (workItemId) => (dispatch) => {
  return resolveRejection(workItemId);
};

export const handleFetchSentBackStages = (workItemIds, stageId) => (
  dispatch
) => {
  const type = "SENT_BACK_STAGES";

  return fetchSentBackStages(workItemIds, stageId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleRejectItems = (
  itemId,
  rejectionCategoryId,
  type,
  currentStageId,
  sentBackStageId,
  description,
  workTimerId,
  quantity
) => (dispatch) => {
  return rejectItems(
    itemId,
    rejectionCategoryId,
    type,
    currentStageId,
    sentBackStageId,
    description,
    workTimerId,
    quantity
  );
};

export const handleFetchRejectionCategories = (dispatch) => {
  const type = "REJECTION_CATEGORIES";

  return fetchRejectionCategories().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleUpdateGroupedItems = (
  groupIds,
  data,
  quantity,
  callback
) => (dispatch) => {
  const type = "GROUPED_ITEMS";

  dispatch(updateStarted(type));
  return editGroupedItems(groupIds, data, quantity).then((res) => {
    if (res.error) return dispatch(updateFailed(type, res));
    dispatch(updateSucceeded(type, res));
    if (callback && typeof callback === "function") {
      return callback();
    }

    return res;
  });
};

export const handleFetchRejectionsByPackage = (
  packageId,
  isGrouped,
  sendResolved
) => (dispatch) => {
  const type = "REJECTIONS_BY_PACKAGE";

  dispatch(receiveStarted(type));
  return fetchItemRejectionsByPackage(packageId, isGrouped, sendResolved).then(
    (res) => {
      if (res.error) dispatch(receiveFailed(type, res.error));
      else dispatch(receiveSucceeded(type, res));

      return res;
    }
  );
};

export const handleCreateLaydownLocation = (itemIds, newLocation) => (
  dispatch
) => {
  const type = "LAYDOWN_LOCATION";
  dispatch(createStarted(type));
  return createLaydownLocation(itemIds, newLocation).then((res) => {
    if (res.error) return dispatch(createFailed(type, res));
    return dispatch(createSucceeded(type));
  });
};

export const handleFetchAvailableStages = (workItemId) => (dispatch) => {
  const type = "AVAILABLE_STAGES";

  return fetchAvailableStages(workItemId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchVisibleColumns = (dispatch) => {
  const type = "VISIBLE_COLUMNS";

  return fetchWorkItemColumns(true).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchItemsById = (ids, queryParams = {}) => (dispatch) => {
  const type = "ITEMS_BY_ID";
  return fetchItemsById(ids, queryParams).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type));

    return res;
  });
};

export const handleFetchWorkTableItems = (
  jobIds,
  packageIds,
  drawingIds,
  stageIds,
  showAll,
  grouping,
  groupingColumns
) => (dispatch) => {
  dispatch(receiveStarted("WORK_TABLE_ITEMS"));
  return fetchItemsv2(
    null, // jobIds,
    null, // packageIds,
    null, // drawingIds,
    stageIds,
    showAll
  ).then((res) => {
    let mappedRes = [];
    let filteredData = [];
    if (res.error) return dispatch(receiveFailed("WORK_TABLE_ITEMS", res));
    else {
      // TODO: improve, takes 4.5 sec to run on 130k items
      const state = store.getState();
      const jobs = state.jobsData.jobs ?? [];
      const packages = state.packagesData.packages ?? [];
      const drawings = state.drawingsData.drawings ?? [];
      let job;
      let pkg;
      let drawing;
      mappedRes = res.map((i) => {
        // todo: can we store just the ids as a set for id: job to simplify this or just the ids
        job = jobs.find((j) => j.id === i.job_id) ?? {};
        pkg = packages.find((p) => p.id === i.package_id) ?? {};
        drawing = drawings.find((d) => d.id === i.drawing_id) ?? {};

        return {
          ...i,
          job_name: job.job_name,
          job_number: job.job_number,
          package_name: pkg.package_name,
          package_number: pkg.package_number,
          package_area: pkg.area,
          package_due_date: pkg.due_date_unix,
          package_priority: pkg.priority,
          drawing_name: drawing.name,
          drawing_area: drawing.drawing_area,
          drawing_due_date: drawing.due_date,
          drawing_priority: drawing.priority,
          on_hold: drawing.on_hold,
          drawing_pending_approval: drawing.pending_approval,
          forge_urn: drawing.forge_urn,
          model_name: drawing.model_name,
          forge_model_id: drawing.forge_model_id,
          has_original: drawing.has_original,
          has_annotated: drawing.has_annotated,
          has_package_map: drawing.has_package_map,
        };
      });
      filteredData = filterItems([...mappedRes], {
        jobIds,
        packageIds,
        drawingIds,
        stageIds,
        grouping,
        groupingColumns,
      });
      dispatch(receiveSucceeded("WORK_TABLE_ITEMS", mappedRes));
      dispatch(updateSucceeded("FILTER_ITEMS", filteredData));
    }

    return filteredData;
  });
};

/**
 * Action to take existing items and filter/group based on the client filters.
 * @param {*} jobIds
 * @param {*} packageIds
 * @param {*} drawingIds
 * @param {*} stageIds
 * @param {*} grouping
 * @param {*} groupingColumns
 * @returns
 */
export const handleFilterItems = (
  jobIds,
  packageIds,
  drawingIds,
  stageIds,
  grouping = 0,
  groupingColumns = null
) => (dispatch) => {
  const type = "FILTER_ITEMS";
  const items = store.getState().itemsData.items;
  let filteredItems;
  dispatch(updateStarted(type));
  filteredItems = filterItems(items, {
    jobIds,
    packageIds,
    drawingIds,
    stageIds,
    grouping,
    groupingColumns,
  });
  dispatch(updateSucceeded(type, filteredItems));
  return filteredItems;
};

/**
 * Reusable function to filter and group items.
 * @param {*} data
 * @param {*} params
 * @returns
 */
const filterItems = (data, params) => {
  const filteredItems = utils.filterAndGroupItems([...data], params);
  return filteredItems;
};

// this is used to handle inline edits and properly update the cached state
export const handleUpdateItemNoRefresh = (updatedItem) => (dispatch) => {
  const type = "ITEMS";
  const items = store.getState().itemsData.items;

  const updatedIndex = items.findIndex((item) => item.id === updatedItem.id);

  if (updatedIndex !== -1) {
    items[updatedIndex] = updatedItem;
  }

  dispatch(receiveSucceeded(type, items));
};
