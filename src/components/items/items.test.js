import configureMockStore from "redux-mock-store";
import axios from "axios";
import <PERSON>ck<PERSON><PERSON>pter from "axios-mock-adapter";
import thunk from "redux-thunk";

import {
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  updateStarted,
  updateSucceeded,
  updateFailed,
  createStarted,
  createSucceeded,
  createFailed,
  handleFetchItems,
  handleFetchAllItems,
  handleUpdateItems,
  handleUpdateJointHeatNumber,
  handleFetchMaterialTypes,
  handleFetchJoiningProcedures,
  handleFetchBOM,
  handleFetchWorkItemCategories,
  handleArchiveItem,
  handleDeleteItem,
  handleCreateMaterialType,
  handleCompleteUncomplete,
  handleResetItemsReducer,
  handleFetchWorkableItems,
  handleFetchRejectionHistory,
  handleResolveRejection,
  handleFetchSentBackStages,
  handleRejectItems,
  handleFetchRejectionCategories,
  handleUpdateGroupedItems,
  handleFetchRejectionsByPackage,
  handleCreateLaydownLocation,
  handleFetchAvailableStages,
  handleFetchVisibleColumns,
  handleCreateItemsViaCsv,
} from "./itemsActions";
import { buildCSV } from "../../_utils";

import { stageColumnDefs } from "./itemsConstants";

describe("Items", () => {
  const testError = (type) => ({
    error: { status: 404, message: `No ${type} found.` },
  });

  const testJoiningProcedures = [
    {
      id: 1,
      abbreviation: null,
      name: "#89 Rigid Coupling (E Gskt)",
      isogrid: 0,
      archived: 0,
      deleted: 0,
    },
  ];
  const testMaterials = [
    {
      id: 693,
      name: "-PVDF",
      isogrid: 0,
      is_cut: 0,
      is_bomable: 0,
      standard_stock_length: null,
      rounding_rule_id: null,
      round_direction_id: 2,
    },
  ];
  const testLaydownLocations = [
    {
      id: 42,
      name: "test laydown",
      description: "",
      type: "Shop",
      type_id: null,
    },
  ];
  const testContainers = [
    {
      id: 1,
      name: "test cont",
      description: "",
      job_id: 1,
      job_name: "JK Test Data",
      job_number: "12345",
      laydown_location: null,
      created_by: 158,
      created_on: "2019-02-27T23:52:33.000Z",
      deleted_by: null,
      deleted_on: null,
      deleted: 0,
      locked: 0,
      location_name: null,
      location_id: null,
      drawing_ids: null,
      on_hold: 0,
      loaded_date: null,
      unavailable: 1,
    },
  ];
  const testVisibleColumns = [
    {
      id: 1,
      display_name: "Job Number",
      name: "JobNumber",
      normal_name: "job_number",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Jobs",
      editable: 1,
      groupable: 0,
    },
    {
      id: 2,
      display_name: "Job Name",
      name: "JobTitle",
      normal_name: "job_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Jobs",
      editable: 1,
      groupable: 1,
    },
    {
      id: 3,
      display_name: "Package ID",
      name: "id",
      normal_name: "package_id",
      data_type: "integer",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Packages",
      editable: 0,
      groupable: 0,
    },
    {
      id: 4,
      display_name: "Package Name",
      name: "PackageName",
      normal_name: "package_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Packages",
      editable: 1,
      groupable: 1,
    },
    {
      id: 5,
      display_name: "Package Number",
      name: "number",
      normal_name: "package_number",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Packages",
      editable: 1,
      groupable: 0,
    },
    {
      id: 6,
      display_name: "Package Due Date",
      name: "dueDate",
      normal_name: "package_due_date",
      data_type: "date",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Packages",
      editable: 1,
      groupable: 0,
    },
    {
      id: 8,
      display_name: "Package Area",
      name: "area",
      normal_name: "package_area",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Packages",
      editable: 1,
      groupable: 0,
    },
    {
      id: 9,
      display_name: "Drawing Name",
      name: "SpoolName",
      normal_name: "drawing_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Spools",
      editable: 1,
      groupable: 1,
    },
    {
      id: 10,
      display_name: "Drawing Due Date",
      name: "dueDate",
      normal_name: "drawing_due_date",
      data_type: "date",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Spools",
      editable: 1,
      groupable: 0,
    },
    {
      id: 11,
      display_name: "Drawing Priority",
      name: "priority",
      normal_name: "drawing_priority",
      data_type: "integer",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Spools",
      editable: 1,
      groupable: 0,
    },
    {
      id: 12,
      display_name: "Drawing Area",
      name: "area",
      normal_name: "drawing_area",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "Spools",
      editable: 1,
      groupable: 0,
    },
    {
      id: 14,
      display_name: "Tag Number",
      name: "tag_number",
      normal_name: "tag_number",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 15,
      display_name: "End Prep 1",
      name: "end_prep_1",
      normal_name: "end_prep_1",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 16,
      display_name: "End Prep 2",
      name: "end_prep_2",
      normal_name: "end_prep_2",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 17,
      display_name: "End Prep 3",
      name: "end_prep_3",
      normal_name: "end_prep_3",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 18,
      display_name: "End Prep 4",
      name: "end_prep_4",
      normal_name: "end_prep_4",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 19,
      display_name: "Length",
      name: "rounded_cut_length",
      normal_name: "length",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 1,
    },
    {
      id: 20,
      display_name: "Stock Length",
      name: "stock_length",
      normal_name: "stock_length",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 22,
      display_name: "Height",
      name: "height",
      normal_name: "height",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 23,
      display_name: "Width",
      name: "width",
      normal_name: "width",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 24,
      display_name: "Thickness",
      name: "thickness",
      normal_name: "thickness",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 25,
      display_name: "Paint Spec",
      name: "paint_spec",
      normal_name: "paint_spec",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 26,
      display_name: "Texture",
      name: "texture",
      normal_name: "texture",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 27,
      display_name: "Fixture",
      name: "fixture_type",
      normal_name: "fixture_type",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 28,
      display_name: "Gauge",
      name: "gauge",
      normal_name: "gauge",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 29,
      display_name: "Weight",
      name: "weight",
      normal_name: "weight",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 30,
      display_name: "Hanger Size",
      name: "hanger_size",
      normal_name: "hanger_size",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 31,
      display_name: "Product Code",
      name: "product_code",
      normal_name: "product_code",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 32,
      display_name: "Insulation",
      name: "insulation",
      normal_name: "insulation",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 33,
      display_name: "Insulation Area",
      name: "insulation_area",
      normal_name: "insulation_area",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 34,
      display_name: "Insulation Spec",
      name: "insulation_specification",
      normal_name: "insulation_specification",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 35,
      display_name: "Insulation Gauge",
      name: "insulation_gauge",
      normal_name: "insulation_gauge",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 36,
      display_name: "Joining Procedure",
      name: "name",
      normal_name: "joining_procedure_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "joining_procedures",
      editable: 1,
      groupable: 1,
    },
    {
      id: 37,
      display_name: "Material Name",
      name: "name",
      normal_name: "material_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "material_types",
      editable: 1,
      groupable: 1,
    },
    {
      id: 38,
      display_name: "Service",
      name: "service_name",
      normal_name: "service_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 39,
      display_name: "Service Color",
      name: "service_color_name",
      normal_name: "service_color_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 41,
      display_name: "Size",
      name: "size",
      normal_name: "size",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 1,
    },
    {
      id: 42,
      display_name: "Area",
      name: "area",
      normal_name: "area",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 43,
      display_name: "Random Length",
      name: "random_length",
      normal_name: "random_length",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 44,
      display_name: "Rod Size",
      name: "rod_size",
      normal_name: "rod_size",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 45,
      display_name: "Support Rod Length 1",
      name: "support_rod_length",
      normal_name: "support_rod_length",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 46,
      display_name: "Laydown Location",
      name: "name",
      normal_name: "laydown_location_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "laydown_locations",
      editable: 1,
      groupable: 0,
    },
    {
      id: 47,
      display_name: "Liner Spec",
      name: "liner_spec",
      normal_name: "liner_spec",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 48,
      display_name: "Heat Number",
      name: "heat_number",
      normal_name: "heat_number",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 1,
    },
    {
      id: 49,
      display_name: "Joint Heat Number",
      name: "heat_number",
      normal_name: "heat_number",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "joint_heat_numbers",
      editable: 1,
      groupable: 0,
    },
    {
      id: 50,
      display_name: "Container",
      name: "name",
      normal_name: "container_name",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "containers",
      editable: 1,
      groupable: 0,
    },
    {
      id: 51,
      display_name: "Filler Metal",
      name: "filler_metal",
      normal_name: "filler_metal",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 52,
      display_name: "Vendor",
      name: "vendor",
      normal_name: "vendor",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 53,
      display_name: "Measurement Area",
      name: "measurement_area",
      normal_name: "measurement_area",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 54,
      display_name: "Is Cut",
      name: "is_cut",
      normal_name: "is_cut",
      data_type: "integer",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "material_types",
      editable: 0,
      groupable: 1,
    },
    {
      id: 55,
      display_name: "Support Rod Length 2",
      name: "support_rod_length_2",
      normal_name: "support_rod_length_2",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 1,
      table_source: "work_items",
      editable: 1,
      groupable: 0,
    },
    {
      id: 57,
      display_name: "Bend Angle 1",
      name: "bend_angle_1",
      normal_name: "bend_angle_1",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "revit_conduit_bend_information",
      editable: 0,
      groupable: 0,
    },
    {
      id: 58,
      display_name: "Bend Angle 2",
      name: "bend_angle_2",
      normal_name: "bend_angle_2",
      data_type: "decimal",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "revit_conduit_bend_information",
      editable: 0,
      groupable: 0,
    },
    {
      id: 59,
      display_name: "Bend Dim A",
      name: "bend_dim_a",
      normal_name: "bend_dim_a",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "revit_conduit_bend_information",
      editable: 0,
      groupable: 0,
    },
    {
      id: 60,
      display_name: "Bend Dim B",
      name: "bend_dim_b",
      normal_name: "bend_dim_b",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "revit_conduit_bend_information",
      editable: 0,
      groupable: 0,
    },
    {
      id: 61,
      display_name: "Bend Dim C",
      name: "bend_dim_c",
      normal_name: "bend_dim_c",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "revit_conduit_bend_information",
      editable: 0,
      groupable: 0,
    },
    {
      id: 62,
      display_name: "Bend Dim D",
      name: "bend_dim_d",
      normal_name: "bend_dim_d",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "revit_conduit_bend_information",
      editable: 0,
      groupable: 0,
    },
    {
      id: 63,
      display_name: "Bend Dim E",
      name: "bend_dim_e",
      normal_name: "bend_dim_e",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "revit_conduit_bend_information",
      editable: 0,
      groupable: 0,
    },
    {
      id: 64,
      display_name: "Bend Dim F",
      name: "bend_dim_f",
      normal_name: "bend_dim_f",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "revit_conduit_bend_information",
      editable: 0,
      groupable: 0,
    },
    {
      id: 65,
      display_name: "Bend Dim G",
      name: "bend_dim_g",
      normal_name: "bend_dim_g",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "revit_conduit_bend_information",
      editable: 0,
      groupable: 0,
    },
    {
      id: 66,
      display_name: "Bend Deduct",
      name: "bend_deduct",
      normal_name: "bend_deduct",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "revit_conduit_bend_information",
      editable: 0,
      groupable: 0,
    },
    {
      id: 67,
      display_name: "Bend Mark 1",
      name: "bend_mark_1",
      normal_name: "bend_mark_1",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "revit_conduit_bend_information",
      editable: 0,
      groupable: 0,
    },
    {
      id: 68,
      display_name: "Bend Mark 2",
      name: "bend_mark_2",
      normal_name: "bend_mark_2",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "revit_conduit_bend_information",
      editable: 0,
      groupable: 0,
    },
    {
      id: 69,
      display_name: "Bender Type",
      name: "bend_bender_type",
      normal_name: "bend_bender_type",
      data_type: "string",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "revit_conduit_bend_information",
      editable: 0,
      groupable: 0,
    },
    {
      id: 70,
      display_name: "Bend Type ID",
      name: "type_id",
      normal_name: "bend_type_id",
      data_type: "integer",
      visible_in_work_table: 1,
      table_effect_id: null,
      color: null,
      usable_for_conditions: 0,
      table_source: "revit_conduit_bend_information",
      editable: 0,
      groupable: 1,
    },
  ];
  const sortState = { sorting_column_name: "id", sorting_method: "asc" };
  const testStore = {
    getState: () => ({ profileData: testStore.profileData }),
    profileData: {
      systemSettings: {
        date_display: "MM-DD-YYYY",
        timezone: "America/Chicago",
      },
      permissions: [279, 280, 281],
      features: [37],
    },
  };

  const moreInfoClick = jest.fn();
  const toggleMoreInfo = jest.fn();
  const togglePDFViewer = jest.fn();
  const heatNumbers = jest.fn();
  const handleJoiningProcedures = jest.fn();
  const handleLaydownLocations = jest.fn();
  const materialTypes = jest.fn();
  const toggleMaterialValid = jest.fn;
  const toggleDrawingValid = jest.fn;

  describe("action handlers should perform the necessary functions", () => {
    let store;
    let httpMock;

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    const testItems = [
      {
        id: 1,
        job_id: 1,
        package_id: 1,
        drawing_id: 1,
      },
      {
        id: 2,
        job_id: 1,
        package_id: 2,
        drawing_id: 2,
      },
      {
        id: "10,11,12,13",
        job_id: 2,
        package_id: 3,
        drawing_id: 3,
      },
    ];
    const testColumnIds = [{ id: 20 }, { id: 30 }, { id: 40 }, { id: 50 }];
    // const testColumnIds = [20, 30, 40, 50];
    const testMaterialTypes = [
      {
        id: 1,
        name: "test mat 1",
      },
      {
        id: 2,
        name: "test mat 2",
      },
    ];

    it("handleFetchItems returns items specific to a job/package/drawing/stage", async () => {
      const testBody = {
        app_type: "fab",
        job_ids: "1",
        is_assigned: true,
      };
      const testBody2 = {
        app_type: "fab",
        package_ids: "1",
        is_assigned: true,
      };
      const testBody3 = {
        app_type: "fab",
        drawing_ids: "1",
        is_assigned: true,
      };
      const testBody4 = {
        app_type: "fab",
        stage_ids: "1",
        is_assigned: true,
      };

      httpMock
        .onPost(`${process.env.REACT_APP_API}/items/fetch`)
        .replyOnce(200, testItems)
        .onPost(`${process.env.REACT_APP_API}/items/fetch`)
        .replyOnce(200, testItems[0])
        .onPost(`${process.env.REACT_APP_API}/items/fetch`)
        .replyOnce(200, testItems[1])
        .onPost(`${process.env.REACT_APP_API}/items/fetch`)
        .replyOnce(200, testItems[1])
        .onPost(`${process.env.REACT_APP_API}/items/fetch`)
        .replyOnce(404, testError("items"));

      await store
        .dispatch(
          handleFetchItems([1], null, null, null, false, false, null, null)
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("ITEMS"),
            receiveSucceeded("ITEMS", testItems),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testItems);
          expect(httpMock.history.post[0].data).toBe(JSON.stringify(testBody));

          store.clearActions();
        });

      await store
        .dispatch(
          handleFetchItems(null, [1], null, null, false, false, null, null)
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("ITEMS"),
            receiveSucceeded("ITEMS", testItems[0]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testItems[0]);
          expect(httpMock.history.post[1].data).toBe(JSON.stringify(testBody2));

          store.clearActions();
        });

      await store
        .dispatch(
          handleFetchItems(null, null, [1], null, false, false, null, null)
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("ITEMS"),
            receiveSucceeded("ITEMS", testItems[1]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testItems[1]);
          expect(httpMock.history.post[2].data).toBe(JSON.stringify(testBody3));

          store.clearActions();
        });

      await store
        .dispatch(
          handleFetchItems(null, null, null, [1], false, false, null, null)
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("ITEMS"),
            receiveSucceeded("ITEMS", testItems[1]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testItems[1]);
          expect(httpMock.history.post[3].data).toBe(JSON.stringify(testBody4));

          store.clearActions();
        });

      return store
        .dispatch(handleFetchItems(null, null, null, false, false, null, null))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("ITEMS"),
            receiveFailed("ITEMS", testError("items")),
          ];
          expect(receivedActions).toEqual(expectedActions);
          expect(httpMock.history.post[4].data).toBe(
            JSON.stringify({
              app_type: "fab",
              is_assigned: true,
            })
          );
        });
    });

    it("handleFetchItems returns grouped items", async () => {
      const testBody = {
        app_type: "fab",
        is_assigned: true,
        grouped: 1,
        work_item_column_ids: "20,30,40,50",
      };

      httpMock
        .onPost(`${process.env.REACT_APP_API}/items/fetch`)
        .replyOnce(200, testItems[2]);

      await store
        .dispatch(
          handleFetchItems(
            null,
            null,
            null,
            null,
            false,
            false,
            1,
            testColumnIds
          )
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("ITEMS"),
            receiveSucceeded("ITEMS", testItems[2]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testItems[2]);
          expect(httpMock.history.post[0].data).toBe(JSON.stringify(testBody));

          store.clearActions();
        });
    });

    it("handleFetchAllItems return all items", async () => {
      const testBody = {
        app_type: "fab",
        is_assigned: true,
      };

      httpMock
        .onPost(`${process.env.REACT_APP_API}/items/fetch`)
        .replyOnce(200, testItems)
        .onPost(`${process.env.REACT_APP_API}/items/fetch`)
        .replyOnce(404, testError("items"));

      await store.dispatch(handleFetchAllItems).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("ALL_ITEMS"),
          receiveSucceeded("ALL_ITEMS", testItems),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testItems);
        expect(httpMock.history.post[0].data).toBe(JSON.stringify(testBody));

        store.clearActions();
      });

      return store.dispatch(handleFetchAllItems).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("ALL_ITEMS"),
          receiveFailed("ALL_ITEMS", testError("items")),
        ];
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchMaterialTypes fetches all material types", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/material-types?app_type=fab`)
        .replyOnce(200, testMaterialTypes)
        .onGet(
          `${process.env.REACT_APP_API}/material-types?service_type_ids=1&app_type=fab`
        )
        .replyOnce(200, testMaterialTypes)
        .onGet(
          `${process.env.REACT_APP_API}/material-types?service_type_ids=100&app_type=fab`
        )
        .replyOnce(404, testError("material types"));

      await store.dispatch(handleFetchMaterialTypes()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveSucceeded("MATERIAL_TYPES", testMaterialTypes),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload).toEqual(testMaterialTypes);

        store.clearActions();
      });

      await store.dispatch(handleFetchMaterialTypes([1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveSucceeded("MATERIAL_TYPES", testMaterialTypes),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload).toEqual(testMaterialTypes);

        store.clearActions();
      });

      return store.dispatch(handleFetchMaterialTypes([100])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveFailed("MATERIAL_TYPES", testError("material types")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchJoiningProcedures fetches all joining procedures", async () => {
      const testJoiningProcedures = [
        {
          id: 1,
          name: "test jp 1",
        },
        {
          id: 2,
          name: "test jp 2",
        },
      ];

      httpMock
        .onGet(`${process.env.REACT_APP_API}/joining-procedures`)
        .replyOnce(200, testJoiningProcedures)
        .onGet(`${process.env.REACT_APP_API}/joining-procedures`)
        .replyOnce(404, testError("joining procedures"));

      await store.dispatch(handleFetchJoiningProcedures).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveSucceeded("JOINING_PROCEDURES", testJoiningProcedures),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload).toEqual(testJoiningProcedures);

        store.clearActions();
      });

      return store.dispatch(handleFetchJoiningProcedures).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveFailed("JOINING_PROCEDURES", testError("joining procedures")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchBOM fetches BOM based on parent IDs", async () => {
      const testBOM = [
        {
          id: 1,
          name: "test mat 1",
        },
        {
          id: 2,
          name: "test mat 2",
        },
      ];

      const testBody = {
        bom: true,
      };

      httpMock
        .onPost(`${process.env.REACT_APP_API}/items/fetch`)
        .replyOnce(200, testBOM)
        .onPost(`${process.env.REACT_APP_API}/items/fetch`)
        .replyOnce(404, testError("BOM"));

      await store.dispatch(handleFetchBOM([1, "JOBS", false])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("BOM"),
          receiveSucceeded("BOM", testBOM),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testBOM);
        expect(httpMock.history.post[0].data).toBe(JSON.stringify(testBody));

        store.clearActions();
      });

      return store.dispatch(handleFetchBOM()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("BOM"),
          receiveFailed("BOM", testError("BOM")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchWorkItemCategories fetches work item categories", async () => {
      const testWorkItemCategories = [
        {
          id: 1,
          name: "test cat 1",
        },
        {
          id: 2,
          name: "test cat 2",
        },
      ];

      httpMock
        .onGet(`${process.env.REACT_APP_API}/work-item-categories`)
        .replyOnce(200, testWorkItemCategories)
        .onGet(`${process.env.REACT_APP_API}/work-item-categories`)
        .replyOnce(404, testError("work item categories"));

      await store.dispatch(handleFetchWorkItemCategories).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_ITEM_CATEGORIES"),
          receiveSucceeded("WORK_ITEM_CATEGORIES", testWorkItemCategories),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testWorkItemCategories);

        store.clearActions();
      });

      return store.dispatch(handleFetchWorkItemCategories).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_ITEM_CATEGORIES"),
          receiveFailed(
            "WORK_ITEM_CATEGORIES",
            testError("work item categories")
          ),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchWorkableItems fetches V2 workable items based off job/package/drawing/stage", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/v2/items/workable?`)
        .replyOnce(200, testItems)
        .onGet(`${process.env.REACT_APP_API}/v2/items/workable?job_ids=1&`)
        .replyOnce(200, testItems[0])
        .onGet(
          `${process.env.REACT_APP_API}/v2/items/workable?job_ids=1&package_ids=1&`
        )
        .replyOnce(200, testItems[0])
        .onGet(
          `${process.env.REACT_APP_API}/v2/items/workable?job_ids=1&package_ids=1&drawing_ids=1&`
        )
        .replyOnce(200, testItems[0])
        .onGet(
          `${process.env.REACT_APP_API}/v2/items/workable?job_ids=1&package_ids=1&drawing_ids=1&stage_ids=1`
        )
        .replyOnce(200, testItems[0])
        .onGet(`${process.env.REACT_APP_API}/v2/items/workable?`)
        .replyOnce(404, testError("workable items"));

      await store.dispatch(handleFetchWorkableItems()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_ITEMS"),
          receiveSucceeded("WORKABLE_ITEMS", testItems),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testItems);

        store.clearActions();
      });

      await store.dispatch(handleFetchWorkableItems([1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_ITEMS"),
          receiveSucceeded("WORKABLE_ITEMS", testItems[0]),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testItems[0]);

        store.clearActions();
      });

      await store.dispatch(handleFetchWorkableItems([1], [1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_ITEMS"),
          receiveSucceeded("WORKABLE_ITEMS", testItems[0]),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testItems[0]);

        store.clearActions();
      });

      await store.dispatch(handleFetchWorkableItems([1], [1], [1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_ITEMS"),
          receiveSucceeded("WORKABLE_ITEMS", testItems[0]),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testItems[0]);

        store.clearActions();
      });

      await store
        .dispatch(handleFetchWorkableItems([1], [1], [1], [1]))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("WORKABLE_ITEMS"),
            receiveSucceeded("WORKABLE_ITEMS", testItems[0]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testItems[0]);

          store.clearActions();
        });

      return store.dispatch(handleFetchWorkableItems()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKABLE_ITEMS"),
          receiveFailed("WORKABLE_ITEMS", testError("workable items").error),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchRejectionHistory fetches rejection history", async () => {
      const testRejections = [
        {
          work_item_ids: 1,
          material_name: "Copper",
        },
        {
          work_item_ids: 2,
          name: "Steel",
        },
      ];

      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/items/rejection-history?work_item_ids=1`
        )
        .replyOnce(200, testRejections[0])
        .onGet(
          `${process.env.REACT_APP_API}/items/rejection-history?work_item_ids=10`
        )
        .replyOnce(404, testError("rejection history"));

      await store.dispatch(handleFetchRejectionHistory(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveSucceeded("REJECTION_HISTORY", testRejections[0]),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload).toEqual(testRejections[0]);

        store.clearActions();
      });

      return store.dispatch(handleFetchRejectionHistory(10)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveFailed(
            "REJECTION_HISTORY",
            testError("rejection history").error
          ),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchSentBackStages fetches stages to send item to", async () => {
      const testSentBackStages = [
        {
          work_item_ids: "1,2,3",
          stage_id: 1,
        },
        {
          work_item_ids: "20,30,40",
          stage_id: 2,
        },
      ];
      const testBody = {
        work_item_ids: "1,2,3",
        stage_id: null,
      };

      httpMock
        .onPost(`${process.env.REACT_APP_API}/items/sent-back-stages/fetch`)
        .replyOnce(200, testSentBackStages[0])
        .onPost(`${process.env.REACT_APP_API}/items/sent-back-stages/fetch`)
        .replyOnce(404, testError("sent back stages"));

      await store
        .dispatch(handleFetchSentBackStages([1, 2, 3], null))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveSucceeded("SENT_BACK_STAGES", testSentBackStages[0]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[0].payload).toEqual(testSentBackStages[0]);
          expect(httpMock.history.post[0].data).toBe(JSON.stringify(testBody));

          store.clearActions();
        });

      return store
        .dispatch(handleFetchSentBackStages([20, 30, 40]))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveFailed(
              "SENT_BACK_STAGES",
              testError("sent back stages").error
            ),
          ];

          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleFetchRejectionCategories fetches rejection categories", async () => {
      const testRejectionCategories = [
        {
          id: 1,
          name: "test cat 1",
        },
        {
          id: 2,
          name: "test cat 2",
        },
      ];

      httpMock
        .onGet(`${process.env.REACT_APP_API}/rejection-category`)
        .replyOnce(200, testRejectionCategories)
        .onGet(`${process.env.REACT_APP_API}/rejection-category`)
        .replyOnce(404, testError("rejection categories"));

      await store.dispatch(handleFetchRejectionCategories).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveSucceeded("REJECTION_CATEGORIES", testRejectionCategories),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload).toEqual(testRejectionCategories);

        store.clearActions();
      });

      return store.dispatch(handleFetchRejectionCategories).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveFailed(
            "REJECTION_CATEGORIES",
            testError("rejection categories").error
          ),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchRejectionsByPackage fetches rejections by a specific package", async () => {
      const testRejections = [
        {
          id: 1,
          name: "test rej 1",
        },
        {
          id: 2,
          name: "test rej 2",
        },
      ];

      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/packages/rejection-report/1?grouped=0&app_type=fab&send_resolved=1`
        )
        .replyOnce(200, testRejections)
        .onGet(
          `${process.env.REACT_APP_API}/packages/rejection-report/5?grouped=0&app_type=fab&send_resolved=0`
        )
        .replyOnce(404, testError("rejections by package"));

      await store.dispatch(handleFetchRejectionsByPackage(1, 0, 1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("REJECTIONS_BY_PACKAGE"),
          receiveSucceeded("REJECTIONS_BY_PACKAGE", testRejections),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testRejections);

        store.clearActions();
      });

      return store
        .dispatch(handleFetchRejectionsByPackage(5, 0, 0))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("REJECTIONS_BY_PACKAGE"),
            receiveFailed(
              "REJECTIONS_BY_PACKAGE",
              testError("rejections by package").error
            ),
          ];

          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleFetchAvailableStages fetches stages available for item", async () => {
      const testStages = [
        {
          id: 1,
          name: "test stage 1",
        },
        {
          id: 2,
          name: "test stage 2",
        },
      ];

      httpMock
        .onGet(`${process.env.REACT_APP_API}/items/1/available-stages`)
        .replyOnce(200, testStages)
        .onGet(`${process.env.REACT_APP_API}/items/10/available-stages`)
        .replyOnce(404, testError("available stages"));

      await store.dispatch(handleFetchAvailableStages(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveSucceeded("AVAILABLE_STAGES", testStages),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload).toEqual(testStages);

        store.clearActions();
      });

      return store.dispatch(handleFetchAvailableStages(10)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveFailed(
            "AVAILABLE_STAGES",
            testError("available stages").error
          ),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchVisibleColumns fetches rejection categories", async () => {
      const testCols = [
        {
          id: 1,
          name: "test col 1",
        },
        {
          id: 2,
          name: "test col 2",
        },
      ];

      httpMock
        .onGet(`${process.env.REACT_APP_API}/columns?visible=1`)
        .replyOnce(200, testCols)
        .onGet(`${process.env.REACT_APP_API}/columns?visible=1`)
        .replyOnce(404, testError("visible columns"));

      await store.dispatch(handleFetchVisibleColumns).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [receiveSucceeded("VISIBLE_COLUMNS", testCols)];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload).toEqual(testCols);

        store.clearActions();
      });

      return store.dispatch(handleFetchVisibleColumns).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveFailed("VISIBLE_COLUMNS", testError("visible columns").error),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleUpdateItems updates specific items and performs fetch if parentData is passed in", async () => {
      const testBody = {
        data: {
          tag_number: "100",
        },
        ids: "1",
      };
      const testUpdatedItem = {
        ...testItems[0],
        tag_number: "100",
      };

      httpMock
        .onPut(`${process.env.REACT_APP_API}/items`)
        .replyOnce(200, testUpdatedItem)
        .onPut(`${process.env.REACT_APP_API}/items`)
        .replyOnce(200, testUpdatedItem)
        .onPost(`${process.env.REACT_APP_API}/items/fetch`)
        .replyOnce(200, testUpdatedItem)
        .onPut(`${process.env.REACT_APP_API}/items`)
        .replyOnce(404, {
          error: { status: 404, message: "No items with specified ID." },
        });

      await store
        .dispatch(handleUpdateItems([1], { tag_number: "100" }))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("ITEMS"),
            updateSucceeded("ITEMS", testUpdatedItem),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testUpdatedItem);
          expect(httpMock.history.put[0].data).toBe(JSON.stringify(testBody));

          store.clearActions();
        });

      await store
        .dispatch(
          handleUpdateItems(
            [1],
            { tag_number: "100" },
            [[1, 1], [1]],
            null,
            null
          )
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("ITEMS"),
            updateSucceeded("ITEMS", testUpdatedItem),
            receiveStarted("ITEMS"),
            receiveSucceeded("ITEMS", testUpdatedItem),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testUpdatedItem);
          expect(receivedActions[3].payload).toEqual(testUpdatedItem);

          store.clearActions();
        });

      return store
        .dispatch(handleUpdateItems(100, null, null, false, false, null, null))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("ITEMS"),
            updateFailed("ITEMS", {
              error: { status: 404, message: "No items with specified ID." },
            }),
          ];
          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleUpdateJointHeatNumber updates specific heat numbers", async () => {
      const testParentData = [[1, 2, 3]];
      const testHNObj = {
        work_item_ids: [1],
        position: 1,
        heat_number: "test hn 2",
      };
      const testBody = {
        work_item_ids: "1",
        position: 1,
        heat_number: "test hn 2",
      };
      const testUpdatedHN = {
        heat_number: "test hn 2",
        position: 1,
      };

      httpMock
        .onPut(`${process.env.REACT_APP_API}/items/joint-heat-number`)
        .replyOnce(200, testUpdatedHN)
        .onPut(`${process.env.REACT_APP_API}/items/joint-heat-number`)
        .replyOnce(200, testUpdatedHN)
        .onPost(`${process.env.REACT_APP_API}/items/fetch`)
        .replyOnce(200, testItems)
        .onPut(`${process.env.REACT_APP_API}/items/joint-heat-number`)
        .replyOnce(404, {
          error: { status: 404, message: "No items with specified ID." },
        });

      await store.dispatch(handleUpdateJointHeatNumber(testHNObj)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          updateStarted("ITEMS"),
          updateSucceeded("ITEMS", testUpdatedHN),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(httpMock.history.put[0].data).toBe(JSON.stringify(testBody));

        store.clearActions();
      });

      await store
        .dispatch(handleUpdateJointHeatNumber(testHNObj, testParentData))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("ITEMS"),
            updateSucceeded("ITEMS", testUpdatedHN),
            receiveStarted("ITEMS"),
            receiveSucceeded("ITEMS", testItems),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[3].payload).toEqual(testItems);
          expect(httpMock.history.put[0].data).toBe(JSON.stringify(testBody));

          store.clearActions();
        });

      return store
        .dispatch(handleUpdateJointHeatNumber({ work_item_ids: [10] }))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("ITEMS"),
            updateFailed("ITEMS", {
              error: { status: 404, message: "No items with specified ID." },
            }),
          ];
          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleArchiveItem archives item", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/items/archive?ids=1`)
        .replyOnce(200);

      await store.dispatch(handleArchiveItem(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          updateStarted("ITEMS"),
          updateSucceeded("ITEMS"),
        ];

        expect(receivedActions).toEqual(expectedActions);

        store.clearActions();
      });
    });

    it("handleDeleteItem deletes item", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/items/delete`)
        .replyOnce(200);

      await store.dispatch(handleDeleteItem("1")).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          updateStarted("ITEMS"),
          updateSucceeded("ITEMS"),
        ];

        expect(receivedActions).toEqual(expectedActions);

        store.clearActions();
      });
    });

    it("handleCreateMaterialType creates new material type", async () => {
      const testNewMat = { id: 1, name: "test mat 1" };
      const testBody = { app_type: "fab", material_type_name: testNewMat.name };

      httpMock
        .onPost(`${process.env.REACT_APP_API}/material-types`)
        .replyOnce(200, testNewMat)
        .onPost(`${process.env.REACT_APP_API}/material-types`)
        .replyOnce(400, {
          error: { status: 400, message: "Unable to create new material" },
        });

      await store
        .dispatch(handleCreateMaterialType(testNewMat.name))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            createStarted("MATERIAL_TYPE"),
            createSucceeded("MATERIAL_TYPE"),
          ];
          expect(httpMock.history.post[0].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(receivedActions).toEqual(expectedActions);

          store.clearActions();
        });

      return store.dispatch(handleCreateMaterialType("10")).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          createStarted("MATERIAL_TYPE"),
          createFailed("MATERIAL_TYPE", {
            error: { status: 400, message: "Unable to create new material" },
          }),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleCompleteUncomplete completes/uncompletes specified item.", async () => {
      const testBody = {
        app_type: "fab",
        data: {
          stage_id: "all",
          work_item_ids: "1",
          direction: 0,
        },
      };
      const testItems = [
        {
          id: 1,
          completed: 0,
        },
        {
          id: 2,
          completed: 1,
        },
      ];

      httpMock
        .onPut(`${process.env.REACT_APP_API}/items/completion`)
        .replyOnce(200, testItems[0])
        .onPut(`${process.env.REACT_APP_API}/items/completion`)
        .replyOnce(200, testItems[1]);

      await store.dispatch(handleCompleteUncomplete(0, 1, 1, true)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [updateSucceeded("SINGLE_ITEM", testItems[0])];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload).toEqual(testItems[0]);
        expect(httpMock.history.put[0].data).toEqual(JSON.stringify(testBody));

        store.clearActions();
      });

      return store
        .dispatch(handleCompleteUncomplete(1, 1, 2, true))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateSucceeded("SINGLE_ITEM", testItems[1]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[0].payload).toEqual(testItems[1]);
        });
    });

    it("handleResolveRejection resolves rejected item.", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/items/rejection-resolution`)
        .replyOnce(200);

      await store.dispatch(handleResolveRejection(1)).then(() => {
        const receivedActions = store.getActions();

        expect(httpMock.history.put[0].data).toEqual(
          JSON.stringify({ work_item_ids: "1" })
        );
        expect(receivedActions).toEqual([]);
      });
    });

    it("handleRejectItems rejects items", async () => {
      const testBody = {
        app_type: "fab",
        item_ids: "1",
        rejection_category_id: 10,
        type: "scrap",
      };

      httpMock
        .onPut(`${process.env.REACT_APP_API}/items/reject`)
        .replyOnce(200);

      await store.dispatch(handleRejectItems(1, 10, "scrap")).then(() => {
        const receivedActions = store.getActions();

        expect(httpMock.history.put[0].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual([]);
      });
    });

    it("handleResetItemsReducer empties out items state", async () => {
      store.dispatch(handleResetItemsReducer);

      const receivedActions = store.getActions();
      const expectedActions = [
        receiveSucceeded("ITEMS", []),
        receiveSucceeded("WORKABLE_ITEMS", []),
      ];

      expect(receivedActions).toEqual(expectedActions);
    });

    it("handleUpdateGroupedItems updates grouped items", async () => {
      const testBody = {
        group_ids: "1,2,3",
        quantity: 3,
        data: {
          stage_id: 2,
        },
      };
      const testGroupedItems = [
        {
          id: "1,2,3",
          quantity: 3,
          stage_id: 2,
        },
        {
          id: "",
          quantity: 0,
          stage_id: 2,
        },
      ];

      httpMock
        .onPut(`${process.env.REACT_APP_API}/items/edit-grouped-items`)
        .replyOnce(200, testGroupedItems[0])
        .onPut(`${process.env.REACT_APP_API}/items/edit-grouped-items`)
        .replyOnce(404, testError("items"));

      await store
        .dispatch(
          handleUpdateGroupedItems(
            testGroupedItems[0].id,
            { stage_id: 2 },
            testGroupedItems[0].quantity
          )
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("GROUPED_ITEMS"),
            updateSucceeded("GROUPED_ITEMS", testGroupedItems[0]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testGroupedItems[0]);
          expect(httpMock.history.put[0].data).toBe(JSON.stringify(testBody));

          store.clearActions();
        });

      return store
        .dispatch(
          handleUpdateGroupedItems(
            testGroupedItems[1].id,
            { stage_id: 1 },
            testGroupedItems[1].quantity
          )
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("GROUPED_ITEMS"),
            updateFailed("GROUPED_ITEMS", testError("items")),
          ];

          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleCreateLaydownLocation creates new laydown location", async () => {
      const testBody = {
        work_item_ids: "1",
        laydown_location_name: "test laydown 1",
      };
      const testUpdatedLaydown = {};

      httpMock
        .onPut(`${process.env.REACT_APP_API}/items/new-laydown-location`)
        .replyOnce(200, testUpdatedLaydown)
        .onPut(`${process.env.REACT_APP_API}/items/new-laydown-location`)
        .replyOnce(400, {
          error: {
            status: 400,
            message: "Unable to create new laydown location",
          },
        });

      await store
        .dispatch(handleCreateLaydownLocation([1], "test laydown 1"))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            createStarted("LAYDOWN_LOCATION"),
            createSucceeded("LAYDOWN_LOCATION"),
          ];

          expect(httpMock.history.put[0].data).toBe(JSON.stringify(testBody));
          expect(receivedActions).toEqual(expectedActions);

          store.clearActions();
        });

      return store
        .dispatch(handleCreateLaydownLocation([10], "test laydown 2"))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            createStarted("LAYDOWN_LOCATION"),
            createFailed("LAYDOWN_LOCATION", {
              error: {
                status: 400,
                message: "Unable to create new laydown location",
              },
            }),
          ];

          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleCreateItemsViaCsv creates items with CSV data", async () => {
      const testItems = buildCSV(
          ["Name", "Age"],
          [
            ["Charlie", 13],
            ["Bandit", 5],
            ["Scruffy", 14],
          ]
        ),
        testBody = {
          app_type: "fab",
          job_id: 1,
          package_id: 2,
          data: testItems,
        };
      const testResponse = [
        [
          {
            total_items_created: 1,
            new_ids: "idk",
          },
        ],
        [
          {
            failed_rows: null,
          },
        ],
        [
          {
            name: "Charlie",
            age: 13,
          },
          {
            name: "Bandit",
            age: 5,
          },
          {
            name: "Scruffy",
            age: 14,
          },
        ],
      ];

      httpMock
        .onPost(`${process.env.REACT_APP_API}/items/csv`)
        .replyOnce(200, testResponse);

      return store
        .dispatch(handleCreateItemsViaCsv(1, 2, testItems))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            createStarted("ITEMS"),
            createSucceeded("ITEMS", testResponse[2]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(httpMock.history.post.length).toBe(1);
          expect(httpMock.history.post[0].data).toBe(JSON.stringify(testBody));

          store.clearActions();
        });
    });
  });

  describe("Items Column Defs Without WorkStageColumns", () => {
    const defaultHeaders = [
      "",
      "",
      "Bend Angle 1",
      "Bend Angle 2",
      "Bend Deduct",
      "Bend Dim A",
      "Bend Dim B",
      "Bend Dim C",
      "Bend Dim D",
      "Bend Dim E",
      "Bend Dim F",
      "Bend Dim G",
      "Bend Mark 1",
      "Bend Mark 2",
      "Bend Type ID",
      "Bender Type",
      "Container",
      "Drawing",
      "End Prep 1",
      "End Prep 2",
      "End Prep 3",
      "End Prep 4",
      "Filler Metal",
      "Fixture",
      "Gauge",
      "Hanger Size",
      "Heat Number",
      "Height",
      "Insulation",
      "Insulation Area",
      "Insulation Gauge",
      "Insulation Spec",
      "Joining Procedure",
      "Joint Heat Number 1",
      "Joint Heat Number 2",
      "Laydown Location",
      "Length",
      "Liner Spec",
      "Material",
      "Paint Spec",
      "Product Code",
      "Random Length",
      "Rod Size",
      "Service",
      "Service Color",
      "Size",
      "Status",
      "Stock Length",
      "Support Rod Length 1",
      "Support Rod Length 2",
      "Tag #",
      "Texture",
      "Thickness",
      "Vendor",
      "Width",
      "Manage",
    ];

    let populatedColumns;

    beforeEach(() => {
      populatedColumns = stageColumnDefs(
        null,
        testContainers,
        heatNumbers,
        testJoiningProcedures,
        testMaterials,
        moreInfoClick,
        toggleMoreInfo,
        [],
        testVisibleColumns,
        testLaydownLocations,
        togglePDFViewer,
        sortState,
        testStore,
        "JOBS",
        [],
        toggleDrawingValid,
        toggleMaterialValid,
        null,
        materialTypes,
        handleJoiningProcedures
      );
    });

    it("Items headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);

      expect(columnHeaders).toEqual(defaultHeaders);
    });

    describe("CONTAINER", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Container");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            container_name: "test container",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test container");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });

      it("cellEditorParams ", () => {
        const params = {
          data: {
            job_id: 1,
          },
        };
        expect(column.cellEditorParams(params)).toEqual({
          values: ["test cont"],
        });
      });
    });

    describe("DRAWINGS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Drawing");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            drawing_name: "HHWR0014",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("HHWR0014");
      });

      it("cellRendererParams", () => {
        expect(JSON.stringify(column.cellRendererParams())).toEqual(
          JSON.stringify({
            moreInfoClick,
            togglePDFViewer: () => togglePDFViewer(),
          })
        );
      });
    });

    describe("MATERIAL", () => {
      let column;
      const params = {
        oldValue: "old",
        newValue: "new",
        value: "new",
        data: {
          name: "test",
          id: 1,
        },
      };
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Material");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            material_name: "test mat",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test mat");
      });
      it("valueParser", () => {
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual("old");
      });

      it("cellEditorParams", () => {
        expect(JSON.stringify(column.cellEditorParams(params))).toEqual(
          JSON.stringify({
            value: "new",
            params,
            toggleValid: toggleMaterialValid,
            required: true,
            toggleModal: () =>
              materialTypes({ ...params.data, _column: "material_name" }),
          })
        );
      });
    });

    describe("TAG NUMBER", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Tag #");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            tag_number: "4",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("4");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("LAYDOWN LOCATION", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Laydown Location"
        );
      });
      const params = {
        data: {
          laydown_location_name: "test laydown",
        },
        value: "test laydown",
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test laydown");
      });

      it("cellEditorParams ", () => {
        expect(JSON.stringify(column.cellEditorParams(params))).toEqual(
          JSON.stringify({
            value: "test laydown",
            params,
            toggleModal: () =>
              handleLaydownLocations({
                ...params.data,
                _column: "laydown_location_name",
              }),
          })
        );
      });
    });

    describe("HANGER SIZE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Hanger Size");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            hanger_size: "10",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("10");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "10",
          newValue: "200",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "10",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("200");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("PRODUCT CODE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Product Code");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            product_code: "2413737677867112129",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(
          "2413737677867112129"
        );
      });
      it("valueParser", () => {
        const params = {
          oldValue: "5757",
          newValue: "500",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "555",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("500");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("INSULATION", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Insulation");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            insulation: "test insulation",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test insulation");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("INSULATION AREA", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Insulation Area"
        );
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            insulation_area: "1000",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("1000");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "1000",
          newValue: "2000",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "1000",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("2000");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("INSULATION SPEC", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Insulation Spec"
        );
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            insulation_specification: "test spec",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test spec");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("INSULATION GAUGE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Insulation Gauge"
        );
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            insulation_gauge: "test gauge",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test gauge");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("JOINING PROCEDURE", () => {
      let column;
      const params = {
        oldValue: "old",
        newValue: "new",
        value: "new",
        data: {
          name: "test",
          id: 1,
        },
      };
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Joining Procedure"
        );
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            joining_procedure_name: "test jp",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test jp");
      });
      it("valueParser", () => {
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });

      it("cellEditorParams", () => {
        expect(JSON.stringify(column.cellEditorParams(params))).toEqual(
          JSON.stringify({
            value: "new",
            params,
            toggleModal: () =>
              handleJoiningProcedures({
                ...params.data,
                _column: "joining_procedure_name",
              }),
          })
        );
      });
    });

    describe("SERVICE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Service");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            service_name: "test service",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test service");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("SERVICE COLOR", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Service Color");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            service_color_name: "test color",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test color");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("STATUS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Status");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            completedness: 0,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(0);
      });
    });

    describe("FILLER METAL", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Filler Metal");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            filler_metal: "test filler",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test filler");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("SIZE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Size");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            size: "test size",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test size");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "12",
          newValue: "12 1/2",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "10",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams2 = {
          oldValue: null,
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("12 1/2");
        expect(column.valueParser(badParams)).toEqual("");
        expect(column.valueParser(badParams2)).toEqual(null);
      });

      it("Comparator", () => {
        const values = ["10", "10.50", "8.50", "9", "3"];
        expect(values.sort(column.comparator)).toEqual([
          "3",
          "8.50",
          "9",
          "10",
          "10.50",
        ]);
      });
    });

    describe("LENGTH", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Length");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            length: {
              decimal: 10,
              display: "10",
            },
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("10");
      });

      it("valueFormatter", () => {
        const params = {
          data: {
            length: {
              decimal: 12.5,
              display: "12 1/2",
            },
          },
        };

        expect(column.valueFormatter(params)).toEqual("12 1/2");
      });

      it("valueParser", () => {
        const params = {
          oldValue: {
            decimal: 10,
            display: `10"`,
          },
          newValue: `12"`,
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: {
            decimal: 10,
            display: `10"`,
          },
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual({
          decimal: 12,
          display: `12"`,
        });
        expect(column.valueParser(badParams)).toEqual({
          decimal: 0,
          display: "",
        });
      });

      it("Comparator", () => {
        const values = [
          {
            data: {
              length: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              length: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
          {
            data: {
              length: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
        ];
        expect(
          values.sort((a, b) => column.comparator(null, null, a, b))
        ).toEqual([
          {
            data: {
              length: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              length: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
          {
            data: {
              length: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
        ]);
      });
    });

    describe("GAUGE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Gauge");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            gauge: 10,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(10);
      });
      it("valueParser", () => {
        const params = {
          oldValue: 10,
          newValue: 10.5,
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: 10.5,
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual(10.5);
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("END PREP 1", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "End Prep 1");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            end_prep_1: "test prep",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test prep");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("END PREP 2", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "End Prep 2");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            end_prep_2: "test prep",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test prep");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("END PREP 3", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "End Prep 3");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            end_prep_3: "test prep",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test prep");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("END PREP 4", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "End Prep 4");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            end_prep_4: "test prep",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test prep");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("HEAT NUMBER", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Heat Number");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            heat_number: "test hn",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test hn");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });

      it("cellEditorParams ", () => {
        const params = {
          data: { id: 1 },
          value: "test hn",
        };

        expect(JSON.stringify(column.cellEditorParams(params))).toEqual(
          JSON.stringify({
            value: "test hn",
            params,
            toggleHeatNumbersModal: () =>
              heatNumbers({ ...params.data, _column: "heat_number" }),
          })
        );
      });
    });

    describe("JOINT HEAT NUMBER 1", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Joint Heat Number 1"
        );
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            joint_heat_number_1: "test hn",
          },
        };

        expect(column.getQuickFilterText(params)).toEqual("test hn");
      });
      it("valueGetter", () => {
        const params = {
          data: {
            joint_heat_number_1: "test hn",
          },
        };
        const badParams = {
          data: {},
        };

        expect(column.valueGetter(params)).toEqual("test hn");
        expect(column.valueGetter(badParams)).toEqual("");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: undefined,
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });

      it("cellEditorParams ", () => {
        const params = {
          value: "test hn",
          data: { id: 1 },
        };

        expect(JSON.stringify(column.cellEditorParams(params))).toEqual(
          JSON.stringify({
            value: "test hn",
            params,
            toggleHeatNumbersModal: () =>
              heatNumbers({ ...params.data, _column: "joint_heat_number_1" }),
          })
        );
      });
    });

    describe("JOINT HEAT NUMBER 2", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Joint Heat Number 2"
        );
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            joint_heat_number_2: "test hn",
          },
        };

        expect(column.getQuickFilterText(params)).toEqual("test hn");
      });
      it("valueGetter", () => {
        const params = {
          data: {
            joint_heat_number_2: "test hn",
          },
        };
        const badParams = {
          data: {},
        };

        expect(column.valueGetter(params)).toEqual("test hn");
        expect(column.valueGetter(badParams)).toEqual("");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: undefined,
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });

      it("cellEditorParams ", () => {
        const params = {
          value: "test hn",
          data: { id: 1 },
        };
        expect(JSON.stringify(column.cellEditorParams(params))).toEqual(
          JSON.stringify({
            value: "test hn",
            params,
            toggleHeatNumbersModal: () =>
              heatNumbers({ ...params.data, _column: "joint_heat_number_2" }),
          })
        );
      });
    });

    describe("HEIGHT", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Height");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            height: 10,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(10);
      });
      it("valueParser", () => {
        const params = {
          oldValue: 10,
          newValue: 20,
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: 10,
          newValue: "hello",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual(20);
        expect(column.valueParser(badParams)).toEqual(10);
      });
    });

    describe("WIDTH", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Width");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            width: 10,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(10);
      });
      it("valueParser", () => {
        const params = {
          oldValue: 10,
          newValue: 20,
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: 20,
          newValue: "hello",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual(20);
        expect(column.valueParser(badParams)).toEqual(20);
      });
    });

    describe("THICKNESS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Thickness");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            thickness: 10,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(10);
      });
      it("valueParser", () => {
        const params = {
          oldValue: 10,
          newValue: 20,
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: 10,
          newValue: "hello",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual(20);
        expect(column.valueParser(badParams)).toEqual(10);
      });
    });

    describe("PAINT SPEC", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Paint Spec");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            paint_spec: "test paint spec",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test paint spec");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("TEXTURE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Texture");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            texture: "test texture",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test texture");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("FIXTURE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Fixture");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            fixture: "test fixture",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test fixture");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("RANDOM LENGTH", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Random Length");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            random_length: 10,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(10);
      });
      it("valueParser", () => {
        const params = {
          oldValue: 10,
          newValue: 20,
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: 10,
          newValue: "hello",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual(20);
        expect(column.valueParser(badParams)).toEqual(10);
      });
    });

    describe("ROD SIZE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Rod Size");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            rod_size: 10,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(10);
      });
      it("valueParser", () => {
        const params = {
          oldValue: 10,
          newValue: 20,
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: 10,
          newValue: "hello",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual(20);
        expect(column.valueParser(badParams)).toEqual(10);
      });
    });

    describe("SUPPORT ROD LENGTH1 ", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Support Rod Length 1"
        );
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            support_rod_length: 10,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(10);
      });
      it("valueParser", () => {
        const params = {
          oldValue: 10,
          newValue: 20,
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: 10,
          newValue: "hello",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual(20);
        expect(column.valueParser(badParams)).toEqual(10);
      });
    });

    describe("SUPPORT ROD LENGTH 2", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Support Rod Length 2"
        );
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            support_rod_length_2: 10,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(10);
      });
      it("valueParser", () => {
        const params = {
          oldValue: 10,
          newValue: 20,
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: 10,
          newValue: "hello",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual(20);
        expect(column.valueParser(badParams)).toEqual(10);
      });
    });

    describe("STOCK LENGTH", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Stock Length");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            stock_length: 10,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(10);
      });
      it("valueParser", () => {
        const params = {
          oldValue: 10,
          newValue: 20,
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: 10,
          newValue: "hello",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual(20);
        expect(column.valueParser(badParams)).toEqual(10);
      });
    });

    describe("LINER SPEC", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Liner Spec");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            liner_spec: "test liner",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test liner");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("VENDOR", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Vendor");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            vendor: "test vendor",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test vendor");
      });
      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("BENDER TYPE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Bender Type");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            bend_bender_type: "custom bender type",
          },
          value: "custom bender type",
        };
        expect(column.getQuickFilterText(params)).toEqual("custom bender type");
      });
    });

    describe("BEND TYPE ID", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Bend Type ID");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            bend_type_id: 15,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(15);
      });
    });

    describe("BEND ANGLE 1", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Bend Angle 1");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            bend_angle_1: 10,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(10);
      });
    });

    describe("BEND ANGLE 2", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Bend Angle 2");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            bend_angle_2: 10,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(10);
      });
    });

    describe("BEND DEDUCT", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Bend Deduct");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            bend_deduct: {
              decimal: 10,
              display: "10",
            },
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("10");
      });

      it("valueFormatter", () => {
        const params = {
          data: {
            bend_deduct: {
              decimal: 12.5,
              display: "12 1/2",
            },
          },
        };

        expect(column.valueFormatter(params)).toEqual("12 1/2");
      });

      it("Comparator", () => {
        const values = [
          {
            data: {
              bend_deduct: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              bend_deduct: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
          {
            data: {
              bend_deduct: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
        ];
        expect(
          values.sort((a, b) => column.comparator(null, null, a, b))
        ).toEqual([
          {
            data: {
              bend_deduct: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              bend_deduct: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
          {
            data: {
              bend_deduct: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
        ]);
      });
    });

    describe("BEND DIM A", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Bend Dim A");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            bend_dim_a: {
              decimal: 10,
              display: "10",
            },
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("10");
      });

      it("valueFormatter", () => {
        const params = {
          data: {
            bend_dim_a: {
              decimal: 12.5,
              display: "12 1/2",
            },
          },
        };

        expect(column.valueFormatter(params)).toEqual("12 1/2");
      });

      it("Comparator", () => {
        const values = [
          {
            data: {
              bend_dim_a: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              bend_dim_a: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
          {
            data: {
              bend_dim_a: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
        ];
        expect(
          values.sort((a, b) => column.comparator(null, null, a, b))
        ).toEqual([
          {
            data: {
              bend_dim_a: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              bend_dim_a: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
          {
            data: {
              bend_dim_a: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
        ]);
      });
    });

    describe("BEND DIM B", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Bend Dim B");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            bend_dim_b: {
              decimal: 10,
              display: "10",
            },
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("10");
      });

      it("valueFormatter", () => {
        const params = {
          data: {
            bend_dim_b: {
              decimal: 12.5,
              display: "12 1/2",
            },
          },
        };

        expect(column.valueFormatter(params)).toEqual("12 1/2");
      });

      it("Comparator", () => {
        const values = [
          {
            data: {
              bend_dim_b: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              bend_dim_b: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
          {
            data: {
              bend_dim_b: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
        ];
        expect(
          values.sort((a, b) => column.comparator(null, null, a, b))
        ).toEqual([
          {
            data: {
              bend_dim_b: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              bend_dim_b: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
          {
            data: {
              bend_dim_b: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
        ]);
      });
    });

    describe("BEND DIM C", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Bend Dim C");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            bend_dim_c: {
              decimal: 10,
              display: "10",
            },
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("10");
      });

      it("valueFormatter", () => {
        const params = {
          data: {
            bend_dim_c: {
              decimal: 12.5,
              display: "12 1/2",
            },
          },
        };

        expect(column.valueFormatter(params)).toEqual("12 1/2");
      });

      it("Comparator", () => {
        const values = [
          {
            data: {
              bend_dim_c: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              bend_dim_c: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
          {
            data: {
              bend_dim_c: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
        ];
        expect(
          values.sort((a, b) => column.comparator(null, null, a, b))
        ).toEqual([
          {
            data: {
              bend_dim_c: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              bend_dim_c: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
          {
            data: {
              bend_dim_c: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
        ]);
      });
    });

    describe("BEND DIM D", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Bend Dim D");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            bend_dim_d: {
              decimal: 10,
              display: "10",
            },
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("10");
      });

      it("valueFormatter", () => {
        const params = {
          data: {
            bend_dim_d: {
              decimal: 12.5,
              display: "12 1/2",
            },
          },
        };

        expect(column.valueFormatter(params)).toEqual("12 1/2");
      });

      it("Comparator", () => {
        const values = [
          {
            data: {
              bend_dim_d: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              bend_dim_d: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
          {
            data: {
              bend_dim_d: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
        ];
        expect(
          values.sort((a, b) => column.comparator(null, null, a, b))
        ).toEqual([
          {
            data: {
              bend_dim_d: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              bend_dim_d: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
          {
            data: {
              bend_dim_d: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
        ]);
      });
    });

    describe("BEND DIM E", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Bend Dim E");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            bend_dim_e: {
              decimal: 10,
              display: "10",
            },
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("10");
      });

      it("valueFormatter", () => {
        const params = {
          data: {
            bend_dim_e: {
              decimal: 12.5,
              display: "12 1/2",
            },
          },
        };

        expect(column.valueFormatter(params)).toEqual("12 1/2");
      });

      it("Comparator", () => {
        const values = [
          {
            data: {
              bend_dim_e: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              bend_dim_e: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
          {
            data: {
              bend_dim_e: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
        ];
        expect(
          values.sort((a, b) => column.comparator(null, null, a, b))
        ).toEqual([
          {
            data: {
              bend_dim_e: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              bend_dim_e: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
          {
            data: {
              bend_dim_e: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
        ]);
      });
    });

    describe("BEND DIM F", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Bend Dim F");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            bend_dim_f: {
              decimal: 10,
              display: "10",
            },
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("10");
      });

      it("valueFormatter", () => {
        const params = {
          data: {
            bend_dim_f: {
              decimal: 12.5,
              display: "12 1/2",
            },
          },
        };

        expect(column.valueFormatter(params)).toEqual("12 1/2");
      });

      it("Comparator", () => {
        const values = [
          {
            data: {
              bend_dim_f: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              bend_dim_f: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
          {
            data: {
              bend_dim_f: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
        ];
        expect(
          values.sort((a, b) => column.comparator(null, null, a, b))
        ).toEqual([
          {
            data: {
              bend_dim_f: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              bend_dim_f: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
          {
            data: {
              bend_dim_f: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
        ]);
      });
    });

    describe("BEND DIM G", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Bend Dim G");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            bend_dim_g: {
              decimal: 10,
              display: "10",
            },
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("10");
      });

      it("valueFormatter", () => {
        const params = {
          data: {
            bend_dim_g: {
              decimal: 12.5,
              display: "12 1/2",
            },
          },
        };

        expect(column.valueFormatter(params)).toEqual("12 1/2");
      });

      it("Comparator", () => {
        const values = [
          {
            data: {
              bend_dim_g: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              bend_dim_g: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
          {
            data: {
              bend_dim_g: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
        ];
        expect(
          values.sort((a, b) => column.comparator(null, null, a, b))
        ).toEqual([
          {
            data: {
              bend_dim_g: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              bend_dim_g: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
          {
            data: {
              bend_dim_g: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
        ]);
      });
    });
  });

  describe("Items Column Defs With WorkStageColumns", () => {
    const defaultHeaders = [
      "",
      "",
      "Heat Number",
      "Material",
      "Tag #",
      "Status",
      "Manage",
    ];
    const testWorkStageColumns = [
      {
        normal_name: "material_name",
        display_name: "Material",
      },
      {
        normal_name: "tag_number",
        display_name: "Tag #",
      },
      {
        normal_name: "heat_number",
        display_name: "Heat Number",
      },
    ];

    let populatedColumns;
    beforeEach(() => {
      populatedColumns = stageColumnDefs(
        null,
        testContainers,
        heatNumbers,
        testJoiningProcedures,
        testMaterials,
        moreInfoClick,
        toggleMoreInfo,
        testWorkStageColumns,
        testVisibleColumns,
        testLaydownLocations,
        togglePDFViewer,
        sortState,
        testStore
      );
    });

    it("Items headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);

      expect(columnHeaders).toEqual(defaultHeaders);
    });
  });
});
