// NPM PACKAGE IMPORTS
import moment from "moment";
import "moment-timezone";

// REDUX IMPORTS
import store from "../../redux/store";

// HELPER FUNCTION IMPORTS
import {
  convertFracToDec,
  fractionalRegex,
  multidimensionRegex,
  decimalRegex,
  multidimensionOnlyX,
  generateTime,
  naturalSort,
  objectColumnDefs,
} from "../../_utils";

// STYLES IMPORTS
import "../styles/tables.scss";

export const stageColumnDefs = (
  savedColumnState,
  containers = [],
  heatNumbers = (f) => f,
  joiningProcedures = [],
  materials = [],
  moreInfoClick,
  toggleMoreInfo,
  workStageColumns = [],
  visibleColumns,
  laydownLocations = [],
  togglePDFViewer,
  sortState,
  testStore,
  tableType,
  drawings,
  toggleDrawingValid,
  toggleMaterialValid,
  jobId,
  materialTypes = (f) => f,
  handleJoiningProcedures = (f) => f,
  handleLaydownLocations = (f) => f,
  isNewRow,
  displayNotification,
  setDisplayedPDF
) => {
  const { systemSettings, permissions, features } = (
    testStore || store
  ).getState().profileData;
  let dateFormatting = systemSettings && systemSettings.date_display;
  let jointHeatNumbers = ["joint_heat_number_1", "joint_heat_number_2"];

  let columns = [
    {
      headerName: "Category",
      field: "category_name",
      getQuickFilterText: (params) => params.data.category_name,
      minWidth: 120,
      width: 120,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "category_name",
      sort:
        sortState.sorting_column_name === "category_name"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Container",
      field: "container_name",
      cellEditorParams: (params) => {
        return {
          values: containers
            .filter((c) => c.job_id === (params.data.job_id || jobId))
            .map((c) => c.name),
        };
      },
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined) {
          return null;
        }

        return params.newValue;
      },
      minWidth: 120,
      width: 140,
      cellEditor:
        tableType === "WIZARD_ITEMS" ? null : "dropdownEditorRenderer",
      editable: tableType === "WIZARD_ITEMS" ? false : true,
      getQuickFilterText: (params) => params.data.container_name,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "container_name",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "container_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Drawing",
      field: "drawing_name",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue;

        return params.newValue;
      },
      getQuickFilterText: (params) => params.data.drawing_name,
      minWidth: 120,
      width: 140,
      editable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      lockVisible: tableType === "WIZARD_ITEMS" ? true : false,
      cellEditorParams: (params) => {
        return {
          values: drawings,
          toggleValid: toggleDrawingValid,
          required: !params.data.drawing_name ? true : false,
        };
      },
      cellRendererParams: (params) => {
        return {
          moreInfoClick,
          togglePDFViewer: () => togglePDFViewer(params),
          setDisplayedPDF,
        };
      },
      cellRenderer: "drawingNameCellRenderer",
      cellEditor:
        tableType === "WIZARD_ITEMS" ? "dropdownEditorRenderer" : null,
      colId: "drawing_name",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "drawing_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Material",
      field: "material_name",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue;

        return params.newValue;
      },
      editable: true,
      cellEditor: "dropdownInputEditorRenderer",
      lockVisible: true,
      cellEditorParams: (params) => {
        return {
          value: params.value,
          params,
          toggleValid: toggleMaterialValid,
          required: !params.data.material_name ? true : false,
          toggleModal: () =>
            materialTypes({ ...params.data, _column: "material_name" }),
        };
      },
      getQuickFilterText: (params) => params.data.material_name,
      minWidth: 120,
      width: 140,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "material_name",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "material_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Tag #",
      field: "tag_number",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 80,
      width: 100,
      editable: true,
      getQuickFilterText: (params) => params.data.tag_number,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "tag_number",
      sort:
        sortState.sorting_column_name === "tag_number"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Laydown Location",
      field: "laydown_location_name",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined) {
          return null;
        }

        return params.newValue;
      },
      getQuickFilterText: (params) => params.data.laydown_location_name,
      minWidth: 120,
      width: 140,
      editable: true,
      cellEditor: "dropdownInputEditorRenderer",
      cellEditorParams: (params) => ({
        value: params.value,
        params,
        toggleModal: () =>
          handleLaydownLocations({
            ...params.data,
            _column: "laydown_location_name",
          }),
      }),
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "laydown_location_name",
      sort:
        sortState.sorting_column_name === "laydown_location_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Hanger Size",
      field: "hanger_size",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        if (!decimalRegex.test(params.newValue)) return params.oldValue;

        return params.newValue;
      },
      minWidth: 80,
      width: 100,
      editable: true,
      getQuickFilterText: (params) => params.data.hanger_size,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "hanger_size",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "hanger_size"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Product Code",
      field: "product_code",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 100,
      width: 120,
      editable: true,
      getQuickFilterText: (params) => params.data.product_code,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "product_code",
      cellClass: "custom-wrap",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "product_code"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Insulation",
      field: "insulation",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 120,
      width: 140,
      editable: true,
      getQuickFilterText: (params) => params.data.insulation,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "insulation",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "insulation"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Insulation Area",
      field: "insulation_area",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        if (!decimalRegex.test(params.newValue)) return params.oldValue;

        return params.newValue;
      },
      minWidth: 80,
      width: 100,
      editable: true,
      getQuickFilterText: (params) => params.data.insulation_area,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "insulation_area",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "insulation_area"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Insulation Spec",
      field: "insulation_specification",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 120,
      width: 140,
      editable: true,
      getQuickFilterText: (params) => params.data.insulation_specification,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "insulation_specification",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "insulation_specification"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Insulation Gauge",
      field: "insulation_gauge",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 120,
      width: 140,
      editable: true,
      getQuickFilterText: (params) => params.data.insulation_gauge,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "insulation_gauge",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "insulation_gauge"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Joining Procedure",
      field: "joining_procedure_name",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 200,
      width: 200,
      editable: true,
      cellEditor: "dropdownInputEditorRenderer",
      cellEditorParams: (params) => {
        return {
          value: params.value,
          params,
          toggleModal: () =>
            handleJoiningProcedures({
              ...params.data,
              _column: "joining_procedure_name",
            }),
        };
      },
      getQuickFilterText: (params) => params.data.joining_procedure_name,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "joining_procedure_name",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "joining_procedure_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Service",
      field: "service_name",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 120,
      width: 140,
      editable: true,
      getQuickFilterText: (params) => params.data.service_name,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "service_name",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "service_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Service Color",
      field: "service_color_name",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 120,
      width: 140,
      editable: true,
      getQuickFilterText: (params) => params.data.service_color_name,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "service_color_name",
      sort:
        sortState.sorting_column_name === "service_color_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Status",
      field: "completedness",
      minWidth: 120,
      cellRenderer: "itemStatusCellRenderer",
      getQuickFilterText: (params) => params.data.completedness,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "completedness",
      sort:
        sortState.sorting_column_name === "completedness"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Filler Metal",
      field: "filler_metal",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 100,
      width: 120,
      getQuickFilterText: (params) => params.data.filler_metal,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      editable: true,
      colId: "filler_metal",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "filler_metal"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Size",
      field: "size",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue === null ? params.oldValue : "";
        if (
          !multidimensionRegex.test(params.newValue) ||
          !multidimensionOnlyX.test(params.newValue)
        ) {
          displayNotification(
            "Value not saved due to invalid format. Please provide a valid fraction."
          );
          return params.oldValue;
        }

        return params.newValue;
      },
      minWidth: 80,
      width: 100,
      editable: true,
      comparator: (valueA, valueB) => {
        if (!valueA) return -1;
        if (!valueB) return 1;
        return valueA.localeCompare(valueB, undefined, {
          numeric: true,
          sensitivity: "base",
        });
      },
      getQuickFilterText: (params) => params.data.size,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "size",
      sort:
        sortState.sorting_column_name === "size"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Length",
      field: "length",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue === null
            ? params.oldValue && params.oldValue.decimal
            : params.oldValue.decimal === null
            ? { decimal: null, display: null }
            : { decimal: 0, display: "" };
        if (params.oldValue === convertFracToDec(params.newValue))
          return params.oldValue;

        if (!fractionalRegex.test(params.newValue)) {
          displayNotification(
            `Value not saved due to invalid format. Please provide a valid fraction (ex. 2'-1/2")`
          );
          return params.oldValue;
        }

        return {
          decimal: convertFracToDec(params.newValue),
          display: params.newValue,
        };
      },
      filterValueGetter: objectColumnDefs.filterValueGetter("length"),
      valueFormatter: objectColumnDefs.valueFormatter("length"),
      getQuickFilterText: objectColumnDefs.getQuickFilterText("length"),
      comparator: objectColumnDefs.comparator("length"),
      minWidth: 80,
      width: 100,
      editable: true,
      cellEditor: "fractionalEditorRenderer",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "length",
      sort:
        sortState.sorting_column_name === "length"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Gauge",
      field: "gauge",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        if (!decimalRegex.test(params.newValue)) return params.oldValue;

        return params.newValue;
      },
      minWidth: 80,
      width: 100,
      editable: true,
      getQuickFilterText: (params) => params.data.gauge,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "gauge",
      sort:
        sortState.sorting_column_name === "gauge"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "End Prep 1",
      field: "end_prep_1",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 120,
      width: 140,
      editable: true,
      getQuickFilterText: (params) => params.data.end_prep_1,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "end_prep_1",
      sort:
        sortState.sorting_column_name === "end_prep_1"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "End Prep 2",
      field: "end_prep_2",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 120,
      width: 140,
      editable: true,
      getQuickFilterText: (params) => params.data.end_prep_2,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "end_prep_2",
      sort:
        sortState.sorting_column_name === "end_prep_2"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "End Prep 3",
      field: "end_prep_3",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 120,
      width: 140,
      editable: true,
      getQuickFilterText: (params) => params.data.end_prep_3,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "end_prep_3",
      sort:
        sortState.sorting_column_name === "end_prep_3"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "End Prep 4",
      field: "end_prep_4",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 120,
      width: 140,
      editable: true,
      getQuickFilterText: (params) => params.data.end_prep_4,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "end_prep_4",
      sort:
        sortState.sorting_column_name === "end_prep_4"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Heat Number",
      field: "heat_number",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 120,
      width: 140,
      // editable: params => params.data.size && params.data.material_name,
      editable: true,
      cellEditor: "dropdownInputEditorRenderer",
      cellEditorParams: (params) => {
        return {
          value: params.value,
          params,
          toggleModal: () =>
            heatNumbers({ ...params.data, _column: "heat_number" }),
        };
      },
      getQuickFilterText: (params) => params.data.heat_number,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "heat_number",
      sort:
        sortState.sorting_column_name === "heat_number"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Joint Heat Number 1",
      field: "joint_heat_number_1",
      valueGetter: (params) => {
        if (
          params.data &&
          params.data.joint_heat_number_1 !== undefined &&
          params.data.joint_heat_number_1 !== null
        ) {
          return params.data.joint_heat_number_1;
        } else if (
          params.data &&
          params.data.joint_heat_numbers &&
          Array.isArray(JSON.parse(params.data.joint_heat_numbers))
        )
          return JSON.parse(params.data.joint_heat_numbers).find(
            (j) => j["position"] === 1
          )
            ? JSON.parse(params.data.joint_heat_numbers).find(
                (j) => j["position"] === 1
              ).heat_number
            : null;
        else return "";
      },
      valueParser: (params) => {
        if (params.newValue === undefined) return null;

        return params.newValue;
      },
      minWidth: 120,
      width: 140,
      getQuickFilterText: (params) => {
        if (params.data && params.data.joint_heat_number_1)
          return params.data.joint_heat_number_1;
        else if (
          params.data &&
          params.data.joint_heat_numbers &&
          Array.isArray(JSON.parse(params.data.joint_heat_numbers))
        )
          return JSON.parse(params.data.joint_heat_numbers).find(
            (j) => j["position"] === 1
          )
            ? JSON.parse(params.data.joint_heat_numbers).find(
                (j) => j["position"] === 1
              ).heat_number
            : null;
        else return "";
      },
      editable: isNewRow ? false : true,
      cellEditor: isNewRow ? null : "dropdownInputEditorRenderer",
      cellEditorParams: (params) => {
        return {
          value: params.value,
          params,
          toggleModal: () =>
            heatNumbers({ ...params.data, _column: "joint_heat_number_1" }),
        };
      },
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "joint_heat_number_1",
      sort:
        sortState.sorting_column_name === "joint_heat_number_1"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Joint Heat Number 2",
      field: "joint_heat_number_2",
      valueGetter: (params) => {
        if (
          params.data &&
          params.data.joint_heat_number_2 !== undefined &&
          params.data.joint_heat_number_1 !== null
        )
          return params.data.joint_heat_number_2;
        else if (
          params.data &&
          params.data.joint_heat_numbers &&
          Array.isArray(JSON.parse(params.data.joint_heat_numbers))
        )
          return JSON.parse(params.data.joint_heat_numbers).find(
            (j) => j["position"] === 2
          )
            ? JSON.parse(params.data.joint_heat_numbers).find(
                (j) => j["position"] === 2
              ).heat_number
            : null;
        else return "";
      },
      valueParser: (params) => {
        if (params.newValue === undefined) return null;

        return params.newValue;
      },
      minWidth: 120,
      width: 140,
      getQuickFilterText: (params) => {
        if (params.data && params.data.joint_heat_number_2)
          return params.data.joint_heat_number_2;
        else if (
          params.data &&
          params.data.joint_heat_numbers &&
          Array.isArray(JSON.parse(params.data.joint_heat_numbers))
        )
          return JSON.parse(params.data.joint_heat_numbers).find(
            (j) => j["position"] === 2
          )
            ? JSON.parse(params.data.joint_heat_numbers).find(
                (j) => j["position"] === 2
              ).heat_number
            : null;
        else return "";
      },
      editable: isNewRow ? false : true,
      cellEditor: isNewRow ? null : "dropdownInputEditorRenderer",
      cellEditorParams: (params) => {
        return {
          value: params.value,
          params,
          toggleModal: () =>
            heatNumbers({ ...params.data, _column: "joint_heat_number_2" }),
        };
      },
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "joint_heat_number_2",
      sort:
        sortState.sorting_column_name === "joint_heat_number_2"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Height",
      field: "height",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        if (!decimalRegex.test(params.newValue)) return params.oldValue;

        return params.newValue;
      },
      minWidth: 80,
      width: 100,
      editable: true,
      getQuickFilterText: (params) => params.data.height,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "height",
      sort:
        sortState.sorting_column_name === "height"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Width",
      field: "width",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        if (!decimalRegex.test(params.newValue)) return params.oldValue;

        return params.newValue;
      },
      minWidth: 80,
      width: 100,
      editable: true,
      getQuickFilterText: (params) => params.data.width,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "width",
      sort:
        sortState.sorting_column_name === "width"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Thickness",
      field: "thickness",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        if (!decimalRegex.test(params.newValue)) return params.oldValue;

        return params.newValue;
      },
      minWidth: 80,
      width: 100,
      editable: true,
      getQuickFilterText: (params) => params.data.thickness,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "thickness",
      sort:
        sortState.sorting_column_name === "thickness"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Paint Spec",
      field: "paint_spec",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 80,
      width: 100,
      editable: true,
      getQuickFilterText: (params) => params.data.paint_spec,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "paint_spec",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "paint_spec"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Texture",
      field: "texture",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 80,
      width: 100,
      editable: true,
      getQuickFilterText: (params) => params.data.texture,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "texture",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "texture"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Fixture",
      field: "fixture_type",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 120,
      width: 140,
      editable: true,
      getQuickFilterText: (params) => params.data.fixture,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "fixture_type",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "fixture_type"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      // IF EDITED, ADD TO ROUNDED_CUT_LENGTH
      headerName: "Random Length",
      field: "random_length",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        if (!decimalRegex.test(params.newValue)) return params.oldValue;

        return params.newValue;
      },
      minWidth: 80,
      width: 100,
      editable: true,
      getQuickFilterText: (params) => params.data.random_length,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "random_length",
      sort:
        sortState.sorting_column_name === "random_length"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Rod Size",
      field: "rod_size",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        if (!decimalRegex.test(params.newValue)) return params.oldValue;

        return params.newValue;
      },
      minWidth: 80,
      width: 100,
      editable: true,
      getQuickFilterText: (params) => params.data.rod_size,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "rod_size",
      sort:
        sortState.sorting_column_name === "rod_size"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Support Rod Length 1",
      field: "support_rod_length",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        if (!decimalRegex.test(params.newValue)) return params.oldValue;

        return params.newValue;
      },
      minWidth: 80,
      width: 100,
      editable: true,
      getQuickFilterText: (params) => params.data.support_rod_length,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "support_rod_length",
      sort:
        sortState.sorting_column_name === "support_rod_length"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Support Rod Length 2",
      field: "support_rod_length_2",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        if (!decimalRegex.test(params.newValue)) return params.oldValue;

        return params.newValue;
      },
      minWidth: 80,
      width: 100,
      editable: true,
      getQuickFilterText: (params) => params.data.support_rod_length_2,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "support_rod_length_2",
      sort:
        sortState.sorting_column_name === "support_rod_length_2"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Stock Length",
      field: "stock_length",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        if (!decimalRegex.test(params.newValue)) return params.oldValue;

        return params.newValue;
      },
      minWidth: 80,
      width: 100,
      editable: true,
      getQuickFilterText: (params) => params.data.stock_length,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "stock_length",
      sort:
        sortState.sorting_column_name === "stock_length"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Liner Spec",
      field: "liner_spec",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 120,
      width: 140,
      editable: true,
      getQuickFilterText: (params) => params.data.liner_spec,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "liner_spec",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "liner_spec"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Vendor",
      field: "vendor",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      minWidth: 120,
      width: 140,
      editable: true,
      getQuickFilterText: (params) => params.data.vendor,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "vendor",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "vendor"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
  ];

  if (features.includes(37) || features.includes(38))
    columns.push(
      {
        headerName: "Bend Angle 1",
        field: "bend_angle_1",
        minWidth: 80,
        width: 100,
        getQuickFilterText: (params) => params.data.bend_angle_1,
        filter: "agNumberColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_angle_1",
        sort:
          sortState.sorting_column_name === "bend_angle_1"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "Bend Angle 2",
        field: "bend_angle_2",
        minWidth: 80,
        width: 100,
        getQuickFilterText: (params) => params.data.bend_angle_2,
        filter: "agNumberColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_angle_2",
        sort:
          sortState.sorting_column_name === "bend_angle_2"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "Bend Deduct",
        field: "bend_deduct",
        minWidth: 80,
        width: 100,
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_deduct"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_deduct"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_deduct"),
        comparator: objectColumnDefs.comparator("bend_deduct"),
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_deduct",
        sort:
          sortState.sorting_column_name === "bend_deduct"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "Bend Dim A",
        field: "bend_dim_a",
        minWidth: 80,
        width: 100,
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_dim_a"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_dim_a"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_dim_a"),
        comparator: objectColumnDefs.comparator("bend_dim_a"),
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_dim_a",
        sort:
          sortState.sorting_column_name === "bend_dim_a"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "Bend Dim B",
        field: "bend_dim_b",
        minWidth: 80,
        width: 100,
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_dim_b"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_dim_b"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_dim_b"),
        comparator: objectColumnDefs.comparator("bend_dim_b"),
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_dim_b",
        sort:
          sortState.sorting_column_name === "bend_dim_b"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "Bend Dim C",
        field: "bend_dim_c",
        minWidth: 80,
        width: 100,
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_dim_c"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_dim_c"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_dim_c"),
        comparator: objectColumnDefs.comparator("bend_dim_c"),
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_dim_c",
        sort:
          sortState.sorting_column_name === "bend_dim_c"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "Bend Dim D",
        field: "bend_dim_d",
        minWidth: 80,
        width: 100,
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_dim_d"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_dim_d"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_dim_d"),
        comparator: objectColumnDefs.comparator("bend_dim_d"),
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_dim_d",
        sort:
          sortState.sorting_column_name === "bend_dim_d"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "Bend Dim E",
        field: "bend_dim_e",
        minWidth: 80,
        width: 100,
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_dim_e"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_dim_e"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_dim_e"),
        comparator: objectColumnDefs.comparator("bend_dim_e"),
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_dim_e",
        sort:
          sortState.sorting_column_name === "bend_dim_e"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "Bend Dim F",
        field: "bend_dim_f",
        minWidth: 80,
        width: 100,
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_dim_f"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_dim_f"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_dim_f"),
        comparator: objectColumnDefs.comparator("bend_dim_f"),
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_dim_f",
        sort:
          sortState.sorting_column_name === "bend_dim_f"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "Bend Dim G",
        field: "bend_dim_g",
        minWidth: 80,
        width: 100,
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_dim_g"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_dim_g"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_dim_g"),
        comparator: objectColumnDefs.comparator("bend_dim_g"),
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_dim_g",
        sort:
          sortState.sorting_column_name === "bend_dim_g"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "Bend Mark 1",
        field: "bend_mark_1",
        minWidth: 80,
        width: 100,
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_mark_1"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_mark_1"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_mark_1"),
        comparator: objectColumnDefs.comparator("bend_mark_1"),
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_mark_1",
        sort:
          sortState.sorting_column_name === "bend_mark_1"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "Bend Mark 2",
        field: "bend_mark_2",
        minWidth: 80,
        width: 100,
        filterValueGetter: objectColumnDefs.filterValueGetter("bend_mark_2"),
        valueFormatter: objectColumnDefs.valueFormatter("bend_mark_2"),
        getQuickFilterText: objectColumnDefs.getQuickFilterText("bend_mark_2"),
        comparator: objectColumnDefs.comparator("bend_mark_2"),
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_mark_2",
        sort:
          sortState.sorting_column_name === "bend_mark_2"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "Bender Type",
        field: "bend_bender_type",
        minWidth: 80,
        width: 100,
        getQuickFilterText: (params) => params.value,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_bender_type",
        sort:
          sortState.sorting_column_name === "bend_bender_type"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "Bend Type ID",
        field: "bend_type_id",
        minWidth: 80,
        width: 100,
        getQuickFilterText: (params) => params.data.bend_type_id,
        filter: "agNumberColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "bend_type_id",
        sort:
          sortState.sorting_column_name === "bend_type_id"
            ? sortState.sorting_method
            : null,
      }
    );

  let columnsToUse = [];

  const onHoldColDef = {
    headerName: "",
    minWidth: 50,
    maxWidth: 50,
    width: 50,
    suppressMenu: true,
    sortable: true,
    comparator: (valueA, valueB, nodeA, nodeB) => {
      return nodeA.data.on_hold - nodeB.data.on_hold;
    },
    cellClassRules: {
      "cell-yellow": (params) => params.data.on_hold === 1,
      "cell-green": (params) => params.data.on_hold === 0,
    },
    pinned: "left",
    suppressColumnsToolPanel: true,
    colId: "status-indicator",
    headerClass: "status-indicator-button",
    sort:
      sortState.sorting_column_name === "status-indicator"
        ? sortState.sorting_method
        : null,
  };
  const checkboxColDef = {
    headerName: "",
    headerCheckboxSelection: true,
    headerCheckboxSelectionFilteredOnly: true,
    width: 40,
    maxWidth: 40,
    minWidth: 40,
    checkboxSelection: true,
    suppressMenu: true,
    suppressColumnsToolPanel: true,
    pinned: "left",
    colId: "checkbox",
  };
  const creationQuantityColDef = {
    headerName: "Quantity",
    width: 100,
    valueGetter: (params) => {
      if (params.data.quantityToCreate) return params.data.quantityToCreate;
      else return null;
    },
    valueSetter: (params) => {
      if (
        params.newValue === 0 ||
        params.newValue === "" ||
        isNaN(params.newValue) ||
        params.newValue < 1
      )
        return false;
      params.data.quantityToCreate = params.newValue;
      return true;
    },
    maxWidth: 100,
    minWidth: 100,
    editable: true,
    colId: "quantityToCreate",
    pinned: "left",
  };

  if (workStageColumns.length) {
    for (let i = 0; i < workStageColumns.length; i++) {
      let defaultDef = columns.find(
        (c) =>
          c.field ===
          (workStageColumns[i].normal_name === "status"
            ? "completedness"
            : workStageColumns[i].normal_name)
      );

      if (
        workStageColumns[i].display_name === "Joint Heat Number" &&
        !columnsToUse.find((c) => c.headerName.includes("Joint Heat Number"))
      ) {
        columnsToUse.push(
          ...columns.filter((c) => c.headerName.includes("Joint Heat Number"))
        );
      } else if (
        workStageColumns[i].display_name !== "Joint Heat Number" &&
        defaultDef
      )
        columnsToUse.push(defaultDef);
      else if (workStageColumns[i].display_name !== "Joint Heat Number") {
        const {
          display_name,
          normal_name,
          editable,
          data_type,
          table_source,
        } = workStageColumns[i];

        let normalNameToUse =
          normal_name === "status" ? "completedness" : normal_name;

        let columnDef = {
          headerName: display_name,
          field: normalNameToUse,
          editable: !["jobs", "packages", "spools"].includes(
            (table_source || "").toLowerCase()
          )
            ? !!editable
            : false,
          valueParser: (params) => {
            if (params.newValue === "" || !params.newValue) {
              return params.oldValue === null ? params.oldValue : "";
            }

            return params.newValue;
          },
          minWidth: 100,
          width: 120,
          getQuickFilterText: (params) => params.data[normalNameToUse],
          colId: normalNameToUse,
          autoHeight: true,
        };

        if (data_type === "date") {
          columnDef.valueFormatter = (params) => {
            let value;
            const date = params.data[normalNameToUse]
              ? new Date(params.data[normalNameToUse])
              : null;

            const timezone = Intl.DateTimeFormat().resolvedOptions().timezone;
            if (!date) value = "N/A";
            else value = moment.tz(date, timezone).format(dateFormatting);
            return value;
          };
          columnDef.getQuickFilterText = (params) =>
            params.colDef.valueFormatter(params);
        }

        if (["job_title", "job_name"].includes(normalNameToUse)) {
          columnDef.cellRenderer = "jobNameCellRenderer";
          columnDef.valueFormatter = (params) => ({
            wizardPermission:
              permissions &&
              (permissions.includes(279) ||
                permissions.includes(280) ||
                permissions.includes(281)),
          });
        }

        if (["package_title", "package_name"].includes(normalNameToUse)) {
          columnDef.cellRenderer = "packageNameCellRenderer";
          columnDef.valueFormatter = (params) => ({
            wizardPermission:
              permissions &&
              (permissions.includes(279) ||
                permissions.includes(280) ||
                permissions.includes(281)),
          });
        }

        if (
          display_name === "Stock Length" ||
          display_name.includes("Support Rod Length")
        ) {
          columnDef.valueParser = (params) => {
            if (params.newValue === "" || params.newValue === undefined)
              return null;

            if (!decimalRegex.test(params.newValue)) return params.oldValue;

            return params.newValue;
          };
          columnDef.editable = true;
          columnDef.filter = "agNumberColumnFilter";
        }

        if (
          [
            "package_name",
            "package_number",
            "job_number",
            "job_name",
            "package_area",
            "package_due_date",
            "drawing_due_date",
          ].includes(normalNameToUse)
        ) {
          columnDef.editable = true;

          if (
            normalNameToUse === "package_due_date" ||
            normalNameToUse === "drawing_due_date"
          ) {
            columnDef.cellEditor = "dueDateEditorRenderer";
            columnDef.valueFormatter = (params) => {
              // Due dates come from API as unix time in seconds
              // But this will also handle ISO as well
              if (!params.value) return "-";
              const unixTimeSeconds =
                typeof params.value === "number"
                  ? params.value
                  : moment(params.value).unix();
              return generateTime(
                unixTimeSeconds * 1000,
                false,
                true,
                "-",
                testStore
              );
            };
            columnDef.valueParser = (params) => {
              // Parse the date BACK INTO A UNIX TIME from the edit format
              if (params.newValue + "".trim() === "" || !params.newValue)
                return params.oldValue;
              const likelyFormats = ["MM-DD-YYYY", "MM/DD/YYYY"];
              if (moment(params.newValue, likelyFormats).isValid())
                return moment(params.newValue, likelyFormats).unix();
              else if (moment(params.newValue).isValid())
                // Check standard ISO
                return moment(params.newValue).unix();
              else return params.oldValue;
            };
          } else {
            columnDef.valueParser = (params) => {
              if (
                ["package_number", "package_area"].includes(normalNameToUse) &&
                params.oldValue === null &&
                (params.newValue === "" ||
                  params.newValue === undefined ||
                  !params.newValue.trim())
              )
                return params.oldValue;

              return params.newValue || null;
            };
          }
        }

        if (
          normalNameToUse === "package_due_date" ||
          normalNameToUse === "drawing_due_date"
        ) {
          columnDef.filter = "agDateColumnFilter";
          columnDef.filterParams = {
            buttons: ["reset"],
            comparator: (filterLocalDateAtMidnight, cellValue) => {
              const cellDate = cellValue
                ? typeof cellValue === "number"
                  ? new Date(generateTime(cellValue * 1000, false, true, "-"))
                  : new Date(cellValue)
                : "-";

              return cellDate < filterLocalDateAtMidnight
                ? -1
                : cellDate > filterLocalDateAtMidnight
                ? 1
                : 0;
            },
          };
        } else {
          columnDef.filter = "agTextColumnFilter";
          columnDef.filterParams = {
            buttons: ["reset"],
          };
        }
        columnDef.menuTabs = ["filterMenuTab"];

        columnsToUse.push(columnDef);
      }
    }
  } else columnsToUse = columns;

  if (savedColumnState && savedColumnState.length) {
    let result = [];

    for (let i = 0; i < columnsToUse.length; i++) {
      let savedDef = savedColumnState.find(
        (c) => c.header_name === columnsToUse[i].headerName
      );

      if (savedDef) {
        result.push({
          ...columnsToUse[i],
          pinned: savedDef.pinned,
          hide:
            savedDef.visible === 1 ||
            savedDef.header_name === "Material" ||
            (tableType === "WIZARD_ITEMS" && savedDef.header_name === "Drawing")
              ? false
              : true,
          position: savedDef.position,
        });
      } else result.push(columnsToUse[i]);
    }

    result = result
      .sort((a, b) => {
        if (a.position === b.position) {
          if (a.headerName.toLowerCase() > b.headerName.toLowerCase()) return 1;
          else return -1;
        } else return a.position - b.position;
      })
      .map((col) => {
        if (col.position !== undefined) delete col.position;
        return col;
      });

    if (tableType === "WIZARD_ITEMS" && isNewRow) {
      result.unshift(creationQuantityColDef);
    }

    if (tableType === "WIZARD_ITEMS") {
      result.unshift(onHoldColDef);
    } else result.unshift(onHoldColDef, checkboxColDef);

    if (!result.find((c) => c.headerName === "Status")) {
      result.push(columns.find((c) => c.headerName === "Status"));
    }

    result.push({
      headerName: "Manage",
      sortable: false,
      minWidth: 60,
      width: 60,
      valueFormatter: () => ({
        moreInfoClick,
        toggleMoreInfo,
      }),
      cellRenderer: "moreInfoCellRenderer",
      suppressMenu: true,
      pinned: "right",
      suppressColumnsToolPanel: true,
      suppressSizeToFit: true,
      lockVisible: true,
      suppressMovable: true,
      colId: "manage",
    });

    return result.filter(
      (c) =>
        c.field === undefined ||
        c.field === "completedness" ||
        visibleColumns.find(
          (vc) =>
            vc.normal_name === c.field ||
            (jointHeatNumbers.includes(c.field) &&
              vc.display_name === "Joint Heat Number")
        )
    );
  } else {
    let result = [];

    if (tableType !== "WIZARD_ITEMS") {
      for (let i = 0; i < columnsToUse.length; i++) {
        let defaultPosition = workStageColumns.find(
          (c) =>
            c.normal_name === columnsToUse[i].colId ||
            (c.display_name === "Joint Heat Number" &&
              columnsToUse[i].headerName.includes("Joint Heat Number"))
        );

        if (defaultPosition) {
          result.push({
            ...columnsToUse[i],
            position: defaultPosition.position,
          });
        } else result.push(columnsToUse[i]);
      }

      result = result
        .map((a) => (!a.position ? { ...a, position: 0 } : a))
        .sort((a, b) => {
          if (a.position === b.position) {
            if (a.headerName.toLowerCase() > b.headerName.toLowerCase())
              return 1;
            else return -1;
          } else return a.position - b.position;
        })
        .map((col) => {
          if (col.position !== undefined) delete col.position;
          return col;
        });
    } else {
      result = columnsToUse;
    }

    if (tableType === "WIZARD_ITEMS" && isNewRow) {
      result.unshift(creationQuantityColDef);
    }

    if (tableType === "WIZARD_ITEMS") {
      result.unshift(onHoldColDef);
    } else result.unshift(onHoldColDef, checkboxColDef);

    if (!result.find((c) => c.headerName === "Status")) {
      result.push(columns.find((c) => c.headerName === "Status"));
    }

    result.push({
      headerName: "Manage",
      sortable: false,
      minWidth: 60,
      width: 60,
      valueFormatter: () => ({
        moreInfoClick,
        toggleMoreInfo,
      }),
      cellRenderer: "moreInfoCellRenderer",
      suppressMenu: true,
      pinned: "right",
      suppressColumnsToolPanel: true,
      suppressSizeToFit: true,
      lockVisible: true,
      suppressMovable: true,
      colId: "manage",
    });

    return result.filter(
      (c) =>
        c.field === undefined ||
        c.field === "completedness" ||
        c.field === "container_name" ||
        visibleColumns.find(
          (vc) =>
            vc.normal_name === c.field ||
            (jointHeatNumbers.includes(c.field) &&
              vc.display_name === "Joint Heat Number")
        )
    );
  }
};

export const qtyColumnDef = {
  headerName: "Quantity",
  field: "quantity",
  valueFormatter: (params) => {
    if (params.value && typeof params.value === "object")
      return params.value.display;
    else if (params.value) return params.value;
    else if (typeof params.data.work_item_ids === "string")
      return params.data.work_item_ids.split(",").length;
    else if (typeof params.data.work_item_ids === "number") return 1;
    else return params.value || "-";
  },
  width: 100,
  maxWidth: 100,
  colId: "quantity",
  filter: "agTextColumnFilter",
  filterParams: {
    buttons: ["reset"],
  },
  menuTabs: ["filterMenuTab"],
};

export const checkboxColumnDef = {
  headerName: "",
  headerCheckboxSelection: true,
  headerCheckboxSelectionFilteredOnly: true,
  width: 40,
  maxWidth: 40,
  minWidth: 40,
  checkboxSelection: true,
  suppressMenu: true,
  suppressColumnsToolPanel: true,
  lockVisible: true,
  suppressMovable: true,
  pinned: "left",
  colId: "checkbox",
};
