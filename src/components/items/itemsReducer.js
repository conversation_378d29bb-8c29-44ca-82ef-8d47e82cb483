const initialState = {
  isLoading: false,
  error: null,
  items: [],
  filteredItems: [],
  allItems: [],
  materialTypes: null,
  bom: [],
  workItemCategories: [],
  workableItems: [],
  savedColumnState: null,
  joiningProcedures: null,
  sentBackStages: [],
  rejectionCategories: [],
  rejectionHistory: null,
  rejectionsByPackage: null,
  availableStages: null,
  visibleColumns: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_ITEMS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_ITEMS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, items: payload };
    case "RECEIVE_ITEMS_FAILED":
      return { ...state, isLoading: false, error: true, items: [] };
    case "RECEIVE_WORK_TABLE_ITEMS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_WORK_TABLE_ITEMS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, items: payload };
    case "RECEIVE_WORK_TABLE_ITEMS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: true,
        items: [],
        filteredItems: [],
      };
    case "UPDATE_FILTER_ITEMS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "UPDATE_FILTER_ITEMS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        filteredItems: payload,
      };
    case "UPDATE_FILTER_ITEMS_FAILED":
      return { ...state, isLoading: false, error: true, filteredItems: [] };
    case "RECEIVE_ALL_ITEMS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_ALL_ITEMS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, allItems: payload };
    case "RECEIVE_ALL_ITEMS_FAILED":
      return { ...state, isLoading: false, error: payload, allItems: [] };
    case "UPDATE_ITEMS_SUCCEEDED":
      return { ...state, error: null };
    case "UPDATE_ITEMS_FAILED":
      return { ...state, error: payload };
    case "RECEIVE_BOM_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_BOM_SUCCEEDED":
      return { ...state, isLoading: false, error: null, bom: payload };
    case "RECEIVE_BOM_FAILED":
      return { ...state, isLoading: false, error: payload, bom: [] };
    case "RECEIVE_MATERIAL_TYPES_SUCCEEDED":
      return {
        ...state,
        materialTypes: payload,
      };
    case "RECEIVE_MATERIAL_TYPES_FAILED":
      return { ...state, materialTypes: [] };
    case "RECEIVE_WORK_ITEM_CATEGORIES_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_WORK_ITEM_CATEGORIES_SUCCEEDED":
      return {
        ...state,
        isLoading: true,
        error: null,
        workItemCategories: payload,
      };
    case "RECEIVE_WORK_ITEM_CATEGORIES_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "UPDATE_SINGLE_ITEM_SUCCEEDED":
      return {
        ...state,
        items: state.items.map((i) => {
          if (i.id === payload.id) return { ...i, ...payload };
          else return i;
        }),
      };
    case "RECEIVE_WORKABLE_ITEMS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_WORKABLE_ITEMS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        workableItems: payload,
      };
    case "RECEIVE_WORKABLE_ITEMS_FAILED":
      return { ...state, isLoading: false, error: true, workableItems: [] };
    case "CREATE_MATERIAL_TYPE_STARTED":
      return { ...state, isLoading: true, error: null };
    case "CREATE_MATERIAL_TYPE_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "CREATE_MATERIAL_TYPE_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_ITEMS_COLUMN_STATE_STARTED":
      return { ...state, savedColumnState: null };
    case "RECEIVE_ITEMS_COLUMN_STATE_SUCCEEDED":
      return { ...state, savedColumnState: payload };
    case "RECEIVE_ITEMS_COLUMN_STATE_FAILED":
      return { ...state, savedColumnState: [] };
    case "RECEIVE_REJECTION_HISTORY_SUCCEEDED":
      return { ...state, rejectionHistory: payload };
    case "RECEIVE_REJECTION_HISTORY_FAILED":
      return { ...state, rejectionHistory: [] };
    case "RECEIVE_STAGES_FOR_REJECTION_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_STAGES_FOR_REJECTION_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        stagesForRejection: payload,
      };
    case "RECEIVE_STAGES_FOR_REJECTION_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_JOINING_PROCEDURES_SUCCEEDED":
      return { ...state, joiningProcedures: payload };
    case "RECEIVE_JOINING_PROCEDURES_FAILED":
      return { ...state, joiningProcedures: [] };
    case "REJECT_ITEM_STARTED":
      return { ...state, isLoading: true, error: null };
    case "REJECT_ITEM_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "REJECT_ITEM_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_SENT_BACK_STAGES_SUCCEEDED":
      return { ...state, sentBackStages: payload };
    case "RECEIVE_SENT_BACK_STAGES_FAILED":
      return { ...state, sentBackStages: [] };
    case "RECEIVE_REJECTION_CATEGORIES_SUCCEEDED":
      return { ...state, rejectionCategories: payload };
    case "RECEIVE_REJECTION_CATEGORIES_FAILED":
      return { ...state, rejectionCategories: [] };
    case "UPDATE_GROUPED_ITEMS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "UPDATE_GROUPED_ITEMS_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "UPDATE_GROUPED_ITEMS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_REJECTIONS_BY_PACKAGE_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_REJECTIONS_BY_PACKAGE_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        rejectionsByPackage: payload,
      };
    case "RECEIVE_REJECTIONS_BY_PACKAGE_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "CREATE_LAYDOWN_LOCATION_STARTED":
      return { ...state, isLoading: true, error: null };
    case "CREATE_LAYDOWN_LOCATION_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "CREATE_LAYDOWN_LOCATION_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_AVAILABLE_STAGES_SUCCEEDED":
      return { ...state, availableStages: payload };
    case "RECEIVE_AVAILABLE_STAGES_FAILED":
      return { ...state, availableStages: null };
    case "RECEIVE_VISIBLE_COLUMNS_SUCCEEDED":
      return { ...state, visibleColumns: payload };
    case "RECEIVE_VISIBLE_COLUMNS_FAILED":
      return { ...state, visibleColumns: null };
    case "RECEIVE_BOM_COLUMN_STATE_STARTED":
      return { ...state, bomColumnState: null };
    case "RECEIVE_BOM_COLUMN_STATE_SUCCEEDED":
      return { ...state, bomColumnState: payload };
    case "RECEIVE_BOM_COLUMN_STATE_FAILED":
      return { ...state, bomColumnState: [] };
    case "CREATE_JOINING_PROCEDURE_STARTED":
      return { ...state, isLoading: true, error: null };
    case "CREATE_JOINING_PROCEDURE_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "CREATE_JOINING_PROCEDURE_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "CREATE_ITEMS_STARTED":
      return { ...state, isLoading: true };
    case "CREATE_ITEMS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "CREATE_ITEMS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        items: [...payload, ...state.items],
      };
    case "RECEIVE_ITEMS_BY_ID_SUCCEEDED":
      return {
        ...state,
      };
    case "RECEIVE_ITEMS_BY_ID_FAILED":
      return {
        ...state,
        error: payload,
      };
    case "RECEIVE_ITEMS_CLEAR_COLUMN_STATE_SUCCEEDED":
      return { ...state, savedColumnState: null };
    case "RECEIVE_CLEAR_ITEMS_STATE_SUCCEEDED":
      return state;
    case "RECEIVE_FILTER_ITEMS_AND_ITEMS_SUCCEEDED":
      return { ...state, items: payload, filteredItems: payload };
    default:
      return state;
  }
}
