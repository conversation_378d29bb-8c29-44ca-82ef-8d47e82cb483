@import "./sizes.scss";

div.layout {
  &-header {
    height: 60px;
    box-sizing: border-box;

    position: fixed;
    left: #{$navCollapsed};
    right: 0;
    z-index: 2;
  }
  &-nav {
    background-color: $navFab !important;
    position: fixed;
    bottom: 50px;
    top: 0;
    z-index: 1;
  }
  &-body {
    background-color: $bodyDarkGrey !important;
    height: calc(100vh - #{$headerFooter});
    width: calc(100vw - #{$navCollapsed});

    @supports (-webkit-touch-callout: none) {
      /* CSS specific to iOS devices */
      height: calc(100vh - #{$headerFooter} - #{$iosAddressBar});
    }

    box-sizing: border-box;
    padding: 0;
    z-index: 0;

    position: fixed;
    top: 60px;
    right: 0;
    left: #{$navCollapsed};

    &.narrow {
      left: #{$navExpanded};
      width: calc(100vw - #{$navExpanded});
    }
  }
  &-footer {
    background-color: $darkFab !important;
    height: 50px;
    box-sizing: border-box;

    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 2;
  }
}

div.zsiq_custommain.siq_bL,
div#zsiq_float {
  user-select: none;
  height: 52.5px !important;
}
