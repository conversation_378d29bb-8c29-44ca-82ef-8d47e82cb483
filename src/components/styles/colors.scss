$grey: #48525f;
$darkGrey: #363d49;
$offGrey: #283141;
$neutral: #777;
$lightGreen: #48a64c;

// $blue: #316085;
$yellow: #fcd581;
$offBlue: #3f7cac;
$red: #e35655;
// $green: rgb(0, 255, 0);

$fieldProOrange: #f05133;
$fabProBlue: #2196f3;

// FAB V2 COLORS
$navFab: #373c48;
$darkFab: #252830;
$bodyDarkGrey: #2d3039;

// UPDATED COLORS
$blue: #2196f3;
$orange: #fe7d35;
$bimGreen: #187333;
$lighterGrey: #434c5e;
$lighterSlate: #a3b7c8;
$darkBlue: #356a9a;
$green: #5bb559;
$dropdownBlue: #3b465e;

// MY WORK COLORS
$bgDark: #20232a;
$textGreyAlt: #8c8f91;
$textGrey: #9b9ea4;
$textLight: #fafbfa;
$textLightAlt: #dddfdf;
$rowGreen: #16603d;
$rowEven: #253137;
$rowYellowOutline: #d6993a;
$timerRed: #8e333f;
$bgLightDarkGrey: #18181f;
$bgDarkGrey: #0e0e12;
$nestingYellow: #ffc413;
$inputDark: #272e36;
$buttonRed: #af4c57;
$redWash: #b50751;
$redWashDark: #e01929;

// specifically for MUI...
$primaryMain: #84d2ef;
$primaryLight: #3db4e5;
$primaryDark: #59c0e8;
$primaryContrastText: #000000;

$secondaryMain: #ffffff;

$textPrimary: #ffffff;
$textSecondary: #ffffffb3;
$textDisabled: #ffffff61;
$textYellow: #ffc107;
$textDonutMain: #2196f3; // charts
$textDonutLight: #84d2ef; // charts

$backgroundPrimary: #313131;
$backgroundSecondary: #2d3039;

$success: #81c784;
$error: #f44336;
$warning: #ffa726;
$info: #29b6f6;

$customMain: #ffc107;
$actionHover: rgba(61, 180, 229, 0.08);
$actionSelected: rgba(61, 180, 229, 0.16);

// MUI theme
$primaryMain: #84d2ef;
$primaryDark: #59c0e8;
$primaryLight: #3db4e5; // note this is not in the figma
$primaryContrastText: #000000;
$secondaryMain: #ffffff;
$textPrimary: #ffffff;
$textSecondary: #ffffffb3;
$textDisabled: #ffffff61;
$textYellow: #ffc107;
$textDonutMain: #2196f3; // charts
$textDonutLight: #84d2ef; // charts
$backgroundPrimary: #313131;
$backgroundSecondary: #2d3039;
$success: #81c784;
$error: #f44336;
$warning: #ffa726;
$info: #29b6f6;
$customMain: #ffc107;
$actionHover: rgba(61, 180, 229, 0.08);
$actionSelected: rgba(61, 180, 229, 0.16);
