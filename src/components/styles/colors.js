import { hashUnitInterval } from "./../../_utils";

export const grey = "#373c48";
export const darkGrey = "#252830";
export const offGrey = "#263238";
export const neutral = "#555";
export const lightGreen = "#48a64c";

// export const blue = '#316085';
export const yellow = "#fcd581";
export const offBlue = "#3f7cac";
export const red = "#e35655";
export const green = "rgb(0, 225, 0)";

export const fieldProOrange = "#f05133";
export const fabProBlue = "#2196f3";

// UPDATED COLORS
export const blue = "#2196f3";
export const orange = "#fe7d35";
export const bimGreen = "#187333";
export const lighterGrey = "#434c5e";
export const lighterSlate = "#a3b7c8";
export const bodyDarkGrey = "#2d3039";

const rainbowHsl = [
  "hsl(299,  20%, 29%)",
  "hsl(324,  32%, 39%)",
  "hsl(340,  72%, 39%)",
  "hsl(348,  23%, 28%)",
  "hsl( 14,  30%, 38%)",
  "hsl( 20,  18%, 27%)",
  "hsl( 36, 100%, 37%)",
  "hsl(108,  32%, 28%)",
  "hsl(151,  37%, 14%)",
  "hsl(174,  62%, 18%)",
  "hsl(187,  44%, 24%)",
  "hsl(199,  36%, 34%)",
  "hsl(231,  39%, 33%)",
  "hsl(232,  51%, 30%)",
  "hsl(262,  41%, 32%)",
];

const hashCache = {};

export const rainbowHash = (val) => {
  if (hashCache[val]) return hashCache[val];

  // 1 rainbow slightly modified with rando technique 4
  let color = rainbowHsl[(hashUnitInterval([val]) * rainbowHsl.length) >> 0];
  const hRange = 180;
  const sRange = 40;
  const lRange = 20;
  const hash0 = hashUnitInterval(val);
  const hash1 = hashUnitInterval([hash0]);
  const hash2 = hashUnitInterval([hash1]);
  const hO = hash0 * hRange - hRange / 2;
  const sO = hash1 * sRange - sRange / 2;
  const lO = hash2 * lRange - lRange / 2;
  const nH = parseInt(color.substring(4, 7)) + hO;
  const nS = parseInt(color.substring(9, 12)) + sO;
  const nL = parseInt(color.substring(15, 17)) + lO;
  hashCache[val] = `hsl(${nH | 0}, ${nS | 0}%, ${nL | 0}%)`;
  return hashCache[val];
};
