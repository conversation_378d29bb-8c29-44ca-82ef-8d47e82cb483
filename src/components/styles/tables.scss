@import "./colors.scss";

div.ag-theme-balham-dark.custom-ag-styles {
  & div.ag-body-viewport {
    overflow-x: auto !important;
    overflow-y: auto !important;
  }
  div.ag-cell-inline-editing {
    border: none;
  }
}
div.ag-theme-balham-dark div.ag-root,
div.ag-root-wrapper {
  border: none !important;
  background-color: $darkGrey;
  box-sizing: border-box;
}
div.ag-theme-balham-dark div.ag-header {
  background-color: $darkGrey;
  border: 1px solid $lighterGrey;
  border-right: none;
  border-bottom: none;
  border-left: none;
}
div.ag-header-cell {
  font-size: 0.7rem;
}
div.ag-theme-balham-dark div.ag-header-cell::after,
div.ag-theme-balham-dark div.ag-header-group-cell::after {
  height: 55px;
}
div.ag-header-cell-label span.ag-header-cell-text {
  white-space: normal;
}
div.ag-row {
  font-size: 0.8rem;
  border: none;
}
div.ag-column-select {
  background-color: $darkGrey;
}
div.ag-column-select-column {
  margin: 8px 0;
}
div.column-select-list {
  border-top: 1px solid $lighterGrey !important;
}

div.custom-ag-styles.ag-theme-balham-dark div.ag-side-bar div.ag-side-buttons {
  background-color: $darkGrey;
  width: 25px;
  font-size: 0.8rem;
  border-top: 1px solid $lighterGrey;

  & button {
    color: #fff;
    & .ag-icon {
      color: #fff;
    }
  }
}
.ag-theme-balham-dark .ag-selected .ag-side-button-button {
  background-color: $darkGrey !important;
}
div.ag-row.--custom-grid-even {
  background-color: $grey;
}

div.ag-row.--custom-grid-odd {
  background-color: $darkGrey;
}

div.ag-row.--rejected-row {
  background-color: $redWash;
}

div.ag-theme-balham-dark div.ag-paging-panel {
  background-color: $darkGrey;
  font-size: 0.8rem;
}
div.ag-theme-balham-dark .ag-row:not(.ag-row-first) {
  border: none;
}

div.ag-react-container {
  width: 100%;
}
.ag-theme-balham-dark button.ag-side-button-button {
  border-top: none;
  border-bottom: none;
}
div.ag-row-selected {
  // background-color: $fabProBlue !important;
  background-color: $darkBlue !important;
}
.ag-theme-balham-dark
  .ag-cell.ag-cell-first-right-pinned:not(.ag-cell-range-left):not(.ag-cell-range-single-cell) {
  border-left: none;
  padding-right: 0;
}

div.ag-cell {
  min-height: 60px !important;
  white-space: -moz-pre-wrap !important; /* Mozilla, since 1999 */
  white-space: -pre-wrap; /* Opera 4-6 */
  white-space: -o-pre-wrap; /* Opera 7 */
  white-space: pre-wrap; /* css-3 */
  word-wrap: break-word; /* Internet Explorer 5.5+ */
  white-space: -webkit-pre-wrap; /* Newer versions of Chrome/Safari*/
  word-break: break-all;
  white-space: normal;
}

.ag-cell-focus,
.ag-cell-no-focus {
  border: none !important;
  box-sizing: border-box !important;
}
.no-border.ag-cell:focus {
  border: none !important;
  outline: none;
  box-sizing: border-box !important;
}
// .ag-horizontal-left-spacer {
//   display: none;
// }

div.cell-red {
  background-color: $red !important;
  width: 8px !important;
  padding: 0 !important;
  margin: 0 !important;
}
div.cell-green {
  background-color: $green !important;
  width: 8px !important;
  padding: 0 !important;
  margin: 0 !important;
}
div.cell-yellow {
  background-color: #f2db1b !important;
  width: 8px !important;
  padding: 0 !important;
  margin: 0 !important;
}

div.cell-green.cell-value {
  width: 8px !important;
}

.custom-wrap {
  white-space: -moz-pre-wrap !important; /* Mozilla, since 1999 */
  white-space: -pre-wrap; /* Opera 4-6 */
  white-space: -o-pre-wrap; /* Opera 7 */
  white-space: pre-wrap; /* css-3 */
  word-wrap: break-word; /* Internet Explorer 5.5+ */
  white-space: -webkit-pre-wrap; /* Newer versions of Chrome/Safari*/
  word-break: break-all;
  white-space: normal;
}

.status-indicator-button {
  padding-left: 0px !important;
  padding-right: 24px;
}
