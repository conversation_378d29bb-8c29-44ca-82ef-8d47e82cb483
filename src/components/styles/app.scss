@import "./colors.scss";
@import "./layout.scss";
@import "./tables.scss";
@import "../reusable/frameworkComponents/stylesFrameworkComponents.scss";

body {
  font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
  background-color: $grey;
}

div.layout {
  &-header {
    // background-color: $offGrey;
    // border-bottom: 1px solid #fff;
    background-color: $grey;
  }
  &-nav {
    // background-color: $offGrey;
    // border-right: 1px solid #fff;
    background-color: $grey;

    display: grid;
    grid-template-columns: 55px 1fr;
    grid-template-rows: 60px 1fr;
  }
  &-body {
    background-color: #363f50;

    & h1.page-title {
      margin: 5px 0 5px 20px;
      padding: 0;
      font-size: 1.2rem;
      font-weight: 500;
      color: #fff;
    }
  }
  &-footer {
    // background-color: $offGrey;
    background-color: $darkGrey;
    border-top: 1px solid #fff;
    border-bottom: 1px solid #fff;

    display: grid;
    grid-template-columns: 100px 100px 1fr 300px;
  }
}

.hidden {
  display: none;
}

.modal-overlay {
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 20;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
}

button.general-button {
  font-size: 0.8rem;
  font-weight: 600;

  &.green {
    background-color: $lightGreen;
    color: #fff;

    &:not(:disabled):hover {
      background-color: darken($lightGreen, 10%);
    }
  }
}
