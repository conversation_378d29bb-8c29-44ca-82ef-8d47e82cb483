const initialState = {
  isLoading: false,
  error: null,
  userStages: [],
  unassignedStages: [],
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_USER_STAGES_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_USER_STAGES_SUCCEEDED":
      return { ...state, isLoading: false, error: null, userStages: payload };
    case "RECEIVE_USER_STAGES_FAILED":
      return { ...state, isLoading: false, error: payload, userStages: [] };
    case "RECEIVE_UNASSIGNED_STAGES_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_UNASSIGNED_STAGES_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        unassignedStages: payload,
      };
    case "RECEIVE_UNASSIGNED_STAGES_FAILED":
      return { ...state, isLoading: false, error: null, unassignedStages: [] };
    default:
      return state;
  }
}
