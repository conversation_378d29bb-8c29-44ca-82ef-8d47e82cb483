import {
  fetchStages,
  assignUsersToStages,
  unassignUsersFromStages,
} from "../../_services";

const receiveUserStagesStarted = () => ({
  type: "RECEIVE_USER_STAGES_STARTED",
});
const receiveUserStagesSucceeded = (userStages) => ({
  type: "RECEIVE_USER_STAGES_SUCCEEDED",
  payload: userStages,
});
const receiveUserStagesFailed = (error) => ({
  type: "RECEIVE_USER_STAGES_FAILED",
  payload: error,
});
const receiveUnassignedStagesStarted = () => ({
  type: "RECEIVE_UNASSIGNED_STAGES_STARTED",
});
const receiveUnassignedStagesSucceeded = (unassignedWork) => ({
  type: "RECEIVE_UNASSIGNED_STAGES_SUCCEEDED",
  payload: unassignedWork,
});
const receiveUnassignedStagesFailed = (error) => ({
  type: "RECEIVE_UNASSIGNED_STAGES_FAILED",
  payload: error,
});

export const handleFetchUserStages = (assignedUserId) => (dispatch) => {
  dispatch(receiveUserStagesStarted());
  return fetchStages({ assignedUserId }).then((res) => {
    if (res.error) return dispatch(receiveUserStagesFailed(res.error));
    return dispatch(receiveUserStagesSucceeded(res));
  });
};

export const handleFetchUnassignedStages = (notAssignedUserId) => (
  dispatch
) => {
  dispatch(receiveUnassignedStagesStarted());
  return fetchStages({ notAssignedUserId }).then((res) => {
    if (res.error) return dispatch(receiveUnassignedStagesFailed(res.error));
    return dispatch(receiveUnassignedStagesSucceeded(res));
  });
};

export const handleAssignUsersToStages = (userIds, stageIds) => (dispatch) => {
  return assignUsersToStages(userIds, stageIds);
};

export const handleUnassignUsersFromStages = (userIds, stageIds) => (
  dispatch
) => {
  return unassignUsersFromStages(userIds, stageIds);
};
