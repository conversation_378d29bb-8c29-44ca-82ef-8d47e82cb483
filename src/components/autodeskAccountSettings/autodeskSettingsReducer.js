const initialState = {
  accounts: null,
  archivedAccounts: null,
  isLoading: false,
  error: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_AUTODESK_ACCOUNTS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_AUTODESK_ACCOUNTS_SUCCEEDED": {
      const accounts = [];
      const archivedAccounts = [];
      for (const account of payload) {
        if (account.archived) archivedAccounts.push(account);
        else accounts.push(account);
      }
      return {
        ...state,
        isLoading: false,
        error: null,
        accounts: accounts,
        archivedAccounts: archivedAccounts,
      };
    }
    case "RECEIVE_AUTODESK_ACCOUNTS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        accounts: [],
        archivedAccounts: [],
      };
    case "RECEIVE_ARCHIVED_AUTODESK_ACCOUNTS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_ARCHIVED_AUTODESK_ACCOUNTS_SUCCEEDED": {
      const accounts = [];
      for (const account of payload) {
        if (account.archived === 1) accounts.push(account);
      }
      return {
        ...state,
        isLoading: false,
        error: null,
        archivedAccounts: accounts,
      };
    }
    case "RECEIVE_ARCHIVED_AUTODESK_ACCOUNTS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        archivedAccounts: [],
      };
    case "CREATE_AUTODESK_ACCOUNTS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "CREATE_AUTODESK_ACCOUNTS_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "CREATE_AUTODESK_ACCOUNTS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "UPDATE_AUTODESK_ACCOUNTS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "UPDATE_AUTODESK_ACCOUNTS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "UPDATE_AUTODESK_ACCOUNTS_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "RECEIVE_ASSOCIATED_JOBS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_ASSOCIATED_JOBS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        associatedJobs: payload,
      };
    case "RECEIVE_ASSOCIATED_JOBS_FAILED":
      return { ...state, isLoading: false, error: payload, associatedJobs: [] };
    default:
      return state;
  }
}
