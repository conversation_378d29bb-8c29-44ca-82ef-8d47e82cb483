// NPM PACKAGE IMPORTS
import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockAdapter from "axios-mock-adapter";
import thunk from "redux-thunk";

// REDUX IMPORTS
import {
  // ACTION CREATORS
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  createStarted,
  createSucceeded,
  createFailed,
  updateStarted,
  updateSucceeded,
  updateFailed,
  // deleteStarted,
  // deleteSucceeded,
  // deleteFailed,
  // ACTION HANDLERS
  handleFetchAutodeskAccounts,
  handleSaveAutodeskAccount,
  handleUpdateAutodeskAccounts,
  handleFetchAssociatedJobs,
} from "./autodeskSettingsActions";

describe("AUTODESK_ACCOUNTS", () => {
  let store, httpMock;

  const baseURL = `${process.env.REACT_APP_API}`;

  const testError = (message) => ({
    error: { status: 404, message },
  });
  const testErrorVariant = (message) => ({
    status: 404,
    message,
  });

  describe("Action handlers should perform the necessary functions", () => {
    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    const testAccounts = [
      {
        id: 1,
        account_guid: "7424ad11-e7e8-49dd-b6a4-648ec05bfd15",
        name: "MSUITE1",
        default: 1,
        archived: 0,
      },
      {
        id: 2,
        account_guid: "7324ad11-e7e8-49dd-b6a4-648ec05bfd15",
        name: "MSUITE2",
        default: 0,
        archived: 0,
      },
    ];

    it("handleFetchAutodeskAccounts", async () => {
      const type = "AUTODESK_ACCOUNTS";
      httpMock
        .onGet(`${baseURL}/autodesk/accounts?is_active=0`)
        .replyOnce(200, testAccounts)
        .onGet(`${baseURL}/autodesk/accounts?is_active=0`)
        .replyOnce(404, testErrorVariant("No autodesk accounts found."));

      let response = await store.dispatch(handleFetchAutodeskAccounts());

      let receivedActions = store.getActions();
      let expectedActions = [
        receiveStarted(type),
        receiveSucceeded(type, testAccounts),
      ];
      expect(response).toEqual(testAccounts);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleFetchAutodeskAccounts());

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted(type),
        receiveFailed(type, testErrorVariant("No autodesk accounts found.")),
      ];
      expect(response).toEqual(testError("No autodesk accounts found."));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });

    it("handleSaveAutodeskAccount", async () => {
      const type = "AUTODESK_ACCOUNTS";
      const createdAccount = {
        id: 1,
        account_guid: "7424ad11-e7e8-49dd-b6a4-648ec05bfd15",
        name: "MSUITE1",
        default: 1,
        archived: 0,
      };
      const newData = {
        account_guid: "7124ad11-e7e8-49dd-b6a4-648ec05bfd15",
      };

      httpMock
        .onPost(`${baseURL}/autodesk/accounts`)
        .replyOnce(200, createdAccount)
        .onPost(`${baseURL}/autodesk/accounts`)
        .replyOnce(404, testErrorVariant("Autodesk account guid not valid"));

      let response = await store.dispatch(handleSaveAutodeskAccount(newData));
      let receivedActions = store.getActions();
      let expectedActions = [createStarted(type), createSucceeded(type)];

      expect(response).toEqual(createdAccount);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleSaveAutodeskAccount(newData));
      receivedActions = store.getActions();
      expectedActions = [
        createStarted(type),
        createFailed(type, testErrorVariant("Autodesk account guid not valid")),
      ];

      expect(response).toEqual(testError("Autodesk account guid not valid"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });

    it("handleUpdateAutodeskAccounts", async () => {
      const type = "AUTODESK_ACCOUNTS";
      const updatedAccount = {
        id: 4,
        account_guid: "123456-e7e8-49dd-b6a4-648ec05bfd15",
        name: "MSUITE2",
        default: 0,
        archived: 0,
      };

      httpMock
        .onPut(`${baseURL}/autodesk/accounts/4`)
        .replyOnce(200, [updatedAccount])
        .onPut(`${baseURL}/autodesk/accounts/99999`)
        .replyOnce(400, testError("Autodesk account(s) 99999 not found."));

      let response = await store.dispatch(handleUpdateAutodeskAccounts(4, 0));

      let receivedActions = store.getActions();
      let expectedActions = [updateStarted(type), updateSucceeded(type)];

      expect(response).toEqual([updatedAccount]);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleUpdateAutodeskAccounts(99999, 0));
      receivedActions = store.getActions();
      expectedActions = [
        updateStarted(type),
        updateFailed(
          type,
          testErrorVariant("Autodesk account(s) 99999 not found.")
        ),
      ];

      expect(response).toEqual(
        testError("Autodesk account(s) 99999 not found.")
      );
      expect(receivedActions).toEqual(expectedActions);
    });
    it("handleFetchAssociatedJobs", async () => {
      const type = "ASSOCIATED_JOBS";
      const testJobs = [
        {
          job_title: "test",
          job_number: "1234",
          id: 1,
          archived: 0,
        },
      ];

      httpMock
        .onGet(`${baseURL}/autodesk/accounts/1/jobs`)
        .replyOnce(200, testJobs)
        .onGet(`${baseURL}/autodesk/accounts/2/jobs`)
        .replyOnce(404, testErrorVariant("No associated jobs found."));

      let response = await store.dispatch(handleFetchAssociatedJobs(1));

      let receivedActions = store.getActions();
      let expectedActions = [
        receiveStarted(type),
        receiveSucceeded(type, testJobs),
      ];

      expect(response).toEqual(testJobs);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleFetchAssociatedJobs(2));

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted(type),
        receiveFailed(type, testErrorVariant("No associated jobs found.")),
      ];

      expect(response).toEqual(testError("No associated jobs found."));
      expect(receivedActions).toEqual(expectedActions);
    });
  });
});
