@import "../styles/colors.scss";
@import "../styles/sizes.scss";

div.autodesk-account-settings-wrapper {
  /* Shared */

  & div {
    display: flex;
    justify-content: space-between;

    & section {
      width: 50%;
      margin: 2rem 1rem 1rem 2rem;
    }

    & .scroll-section {
      display: block;
      // 50px - top header, 64px section margin, 72px add btn, 72px archived btn
      height: calc(100vh - $headerFooter - 50px - 64px - 72px - 72px);
      overflow: auto;
    }
  }

  & .autodesk-account-settings {
    // 50px - top header
    height: calc(100vh - $headerFooter - 50px);
  }

  // border between associated jobs and autodesk accounts
  & div.separator {
    margin: 0 10px 0;
    border-left: 2px solid #fff;
    height: 100%;
  }

  /* associated jobs */
  & section.associated-jobs {
    & h2 {
      color: white;
      font-weight: normal;
      margin: 0;
      font-size: 1.1rem;
    }
    & h4 {
      color: $fabProBlue;
    }
  }

  /* Blue bar across top */
  & > div.action-row {
    justify-content: flex-start;
    color: white;
    margin-top: 1rem;

    & > h2 {
      margin-right: 1rem;
      font-size: 1rem;
      font-weight: 400;
    }

    & > a {
      color: inherit;
    }
  }

  /* Left section */
  & .add {
    display: flex;
    align-items: center;
    user-select: none;
    background-color: $fabProBlue;
    color: white;
    font-size: 1rem;
    height: 2.5rem;
    margin-bottom: 2rem;

    & > svg {
      margin-right: 0.5rem;
    }
  }

  & .add.disabled {
    color: $lighterSlate;
    cursor: default;

    // needs to be important or gets overriden by msuite_web csvimport
    & > svg {
      color: $lighterSlate !important;
    }
  }

  /** account row(s) **/
  & article.autodesk-account {
    display: flex;
    flex-direction: column;
    margin-bottom: 1rem;

    & div {
      align-items: center;
      justify-content: flex-start;
      gap: 15px;
    }

    // default button
    & button {
      width: 120px;
      height: 36px;
      color: white;
      background-color: $green;
      font-size: 0.8rem;

      &:disabled {
        background-color: grey;
        color: white;
      }
    }
    & button.default {
      background-color: $fabProBlue;
      cursor: default;
    }

    & .guid-input {
      width: 22rem;
      padding: 0.5rem;
      font-size: 1rem;
    }

    // icons in input
    & svg.valid {
      color: white;
      background-color: $green;
      margin-left: -3rem;
    }

    & svg.error {
      color: $red;
      margin-left: -3rem;
    }

    // save button
    & .save {
      color: $fabProBlue;
      background-color: inherit;
      margin: 0 0.5rem;
      padding: 0 0.5rem;
      box-shadow: none;
      height: 45px;
    }

    // view associated jobs button
    & .view-jobs {
      width: 160px;
      background-color: $fabProBlue;
      color: white;
      font-size: 0.8rem;
      margin-left: 1rem;
      padding: 12px 10px;
    }

    // account name / message below input
    & > p {
      font-weight: bold;
      margin: 0.5rem 0 1rem 0;
    }

    & > p.error {
      color: $red;
    }

    & > p.info {
      color: $fabProBlue;
    }
  }

  /**Archive Button **/
  & .archiveIcon {
    color: #ff9900;
    margin-left: 1.5rem;
    height: 2.5rem;
    &:hover {
      color: white;
      cursor: pointer;
    }
  }
  & .disabledArchive {
    color: #ff9900;
    opacity: 25%;
    margin-left: 1.5rem;
    height: 2.5rem;
    &:hover {
      cursor: not-allowed;
    }
  }
  .defaultArchiveIconContainer {
    position: relative;
    display: inline-block;

    & span {
      visibility: hidden;
      width: 120px;
      background-color: black;
      color: #fff;
      text-align: center;
      padding: 5px 0;
      border-radius: 6px;

      // right sided tooltip
      position: absolute;
      z-index: 1;
      top: -5px;
      left: 105%;
    }
    &:hover span {
      visibility: visible;
    }
  }

  /** Archived Accounts**/
  & .archived-accounts-button {
    display: flex;
    align-items: center;
    user-select: none;
    background-color: $fabProBlue;
    color: white;
    font-size: 1rem;
    height: 2.5rem;
    margin-top: 2rem;

    & > svg {
      margin-right: 0.5rem;
    }
  }
}
