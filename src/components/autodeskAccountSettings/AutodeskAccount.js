// NPM PACKAGE IMPORTS
import React from "react";
import { BsInfoCircleFill } from "react-icons/bs";
import { FaSave } from "react-icons/fa";
import { RiCheckFill } from "react-icons/ri";
import Button from "msuite_storybook/dist/button/Button";

// STYLE IMPORTS
import { FiArchive } from "react-icons/fi";
import "./stylesAutodeskAccountSettings.scss";

//REDUX imports
import { showConfirmArchiveModal } from "../reusable/generalModal/generalModalActions";

const AutodeskAccount = ({
  accountId,
  accountGuid,
  name,
  isDefault,
  errorMessage,
  isInputDisabled = false,
  isDefaultDisabled = false,
  autoFocus = false,
  selectedAccount,
  onChange = () => {},
  onSave = () => {},
  updateDefault = () => {},
  dispatch,
  onArchive = () => {},
  handleAssociatedJobsClick = () => {},
}) => {
  return (
    <article className="autodesk-account">
      <div>
        <Button
          onClick={() => !isDefault && updateDefault(accountId)}
          disabled={isDefaultDisabled}
          className={isDefault && `default`}
        >
          {isDefault ? `Default Account` : `Set As Default`}
        </Button>
        <div>
          <input
            type="text"
            value={accountGuid}
            disabled={isInputDisabled}
            className="guid-input"
            autoFocus={autoFocus}
            onChange={onChange}
            onBlur={onSave}
          />
          {!!accountId ? (
            <RiCheckFill className="valid" size={22} />
          ) : !!errorMessage ? (
            <a
              href="https://support.msuite.com/portal/en/kb/articles/msuite"
              target="_blank"
              rel="noreferrer"
            >
              <BsInfoCircleFill className="error" size={22} />
            </a>
          ) : (
            <></>
          )}
          {accountId ? (
            <>
              <div className={isDefault ? "defaultArchiveIconContainer" : ""}>
                <FiArchive
                  className={!isDefault ? "archiveIcon" : "disabledArchive"}
                  size={25}
                  onClick={() => {
                    if (!isDefault)
                      dispatch(
                        showConfirmArchiveModal(name, accountId, onArchive)
                      );
                  }}
                />
                {isDefault ? (
                  <div>
                    <span>Can't archive default account</span>
                  </div>
                ) : (
                  <></>
                )}
              </div>
              <Button
                className="view-jobs"
                onClick={() =>
                  handleAssociatedJobsClick(
                    accountId,
                    selectedAccount?.id === accountId
                  )
                }
              >
                {selectedAccount?.id === accountId
                  ? "Close Associated Jobs"
                  : "View Associated Jobs"}
              </Button>
            </>
          ) : (
            <></>
          )}
        </div>
        {!!accountId ? (
          <></>
        ) : !!!errorMessage ? (
          <Button className="save" onClick={onSave}>
            <FaSave size={25} />
          </Button>
        ) : (
          <></>
        )}
      </div>
      <p className={errorMessage ? "error" : "info"}>
        {errorMessage ?? `Account Name: ${name}`}
      </p>
    </article>
  );
};

export default AutodeskAccount;
