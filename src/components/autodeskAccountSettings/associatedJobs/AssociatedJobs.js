// NPM PACKAGE IMPORTS
import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";

// REDUX IMPORTS

// COMPONENT IMPORTS
import JobStatus<PERSON>ellRenderer from "../../reusable/frameworkComponents/JobStatusCellRenderer";
import AgTable from "../../reusable/agTable/AgTable";
// STYLE IMPORTS
import "./stylesAssociatedJobs.scss";

const columnDefs = [
  {
    headerName: "Job Name",
    field: "job_title",
    minWidth: 220,
    autoHeight: true,
  },
  {
    headerName: "Job #",
    field: "job_number",
    autoHeight: true,
    minWidth: 120,
  },
  {
    headerName: "Job ID",
    field: "id",
    autoHeight: true,
    minWidth: 100,
  },
  {
    headerName: "Job Status",
    field: "name",
    autoHeight: true,
    cellRenderer: "jobStatusCellRenderer",
    minWidth: 120,
  },
];

const AssociatedJobs = () => {
  const [gridOptionsApi, setGridOptionsApi] = useState(null);

  const { associatedJobs } = useSelector(
    (state) => state.autodeskAccountSettings
  );

  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };
  const onGridReady = (params) => {
    params.api.setSideBarVisible(false);
    setGridOptionsApi(params.api);
  };

  useEffect(() => {
    if (!associatedJobs?.length || !gridOptionsApi) return;
    gridOptionsApi.setRowData(associatedJobs);
  }, [associatedJobs]);

  const gridOptions = {
    rowData: associatedJobs,
    columnDefs,
    frameworkComponents: {
      jobStatusCellRenderer: JobStatusCellRenderer,
    },
    reactNext: true,
    onGridReady: onGridReady,
    rowClassRules,
    suppressRowClickSelection: true,
    suppressPaginationPanel: true,
    defaultColDef: {
      cellClass: ["no-border", "custom-wrap"],
      wrapText: true,
      suppressSizeToFit: true,
      autoHeight: true,
    },
  };

  return (
    <div className="jobs-table-wrapper">
      <AgTable gridOptions={gridOptions} />
    </div>
  );
};

export default AssociatedJobs;
