// NPM PACKAGE IMPORTS
import React, { useState, useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AiFillQuestionCircle } from "react-icons/ai";
import { BiPlus } from "react-icons/bi";
import Button from "msuite_storybook/dist/button/Button";

// REDUX IMPORTS
import {
  handleFetchAutodeskAccounts,
  handleSaveAutodeskAccount,
  handleUpdateAutodeskAccounts,
  handleFetchAssociatedJobs,
} from "./autodeskSettingsActions";
import { showArchivedAccountsModal } from "../reusable/generalModal/generalModalActions";
import { handleSetPageTitle } from "../../redux/generalActions";

// COMPONENT IMPORTS
import AutodeskAccount from "./AutodeskAccount";

// HELPER FUNCTION IMPORTS
import { validateGuidInput } from "../../_utils";

// TRANSLATION IMPORTS
import useTranslations from "../../hooks/useTranslations";
import autodeskAccountTranslations from "./autodeskAccountTranslations.json";

// STYLE IMPORTS
import "./stylesAutodeskAccountSettings.scss";
import AssociatedJobs from "./associatedJobs/AssociatedJobs";

const AutodeskAccountSettings = () => {
  const translate = useTranslations(autodeskAccountTranslations);
  const dispatch = useDispatch();

  const { accounts, associatedJobs, isLoading } = useSelector(
    (state) => state.autodeskAccountSettings
  );

  const [newAccountEditing, toggleNewAccountEditing] = useState(false);
  const [newAccountError, toggleNewAccountError] = useState(false);
  const [newAccountGuid, setNewAccountGuid] = useState("");
  const [showAssociatedJobs, toggleAssociatedJobs] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState(null);
  const { permissions, userSettings, features } = useSelector(
    (state) => state.profileData
  );
  useEffect(() => {
    if (!permissions.length || !features.length) return;

    // redirect to homepage if user doesn't have permission to view settings / feature on
    if (!permissions.includes(62) || !features.includes(47)) {
      const homePage = userSettings?.home_page || "jobs";
      window.location.assign(`${process.env.REACT_APP_FABPRO}/${homePage}`);
    }
  }, [permissions, userSettings, features]);

  useEffect(() => {
    // clearing title, not part of design for this page
    dispatch(handleSetPageTitle());
    dispatch(handleFetchAutodeskAccounts());
  }, [dispatch]);

  useEffect(() => {
    if (newAccountEditing) {
      if (newAccountGuid.trim().length) {
        toggleNewAccountError(!validateGuidInput(newAccountGuid));
      }

      // error if it matches an existing account
      if (accounts.find((a) => a.account_guid === newAccountGuid)) {
        toggleNewAccountError(true);
      }
    } else {
      toggleNewAccountError(false);
    }
  }, [newAccountGuid, newAccountEditing, accounts]);

  const onSave = useCallback(() => {
    // TODO: later pass isDefault here,
    // make sure to also add it to dependency array if needed
    dispatch(handleSaveAutodeskAccount(newAccountGuid)).then((res) => {
      if (!res.error) {
        dispatch(handleFetchAutodeskAccounts());
        toggleNewAccountEditing(false);
      } else {
        toggleNewAccountError(true);
      }
    });
  }, [newAccountGuid]);

  const updateDefaultAccount = (accountId) => {
    // currently can't change an account from default to non-default so always passing in true for default
    dispatch(handleUpdateAutodeskAccounts(accountId, null, true)).then(
      (res) => {
        if (!res.error) dispatch(handleFetchAutodeskAccounts());
      }
    );
  };

  const updateArchiveStatus = (accountId, archive) => {
    dispatch(handleUpdateAutodeskAccounts(accountId, archive)).then((res) => {
      if (!res.error) dispatch(handleFetchAutodeskAccounts());
    });
  };

  const isExisting = accounts?.find((a) => a.account_guid === newAccountGuid);

  const handleAssociatedJobsClick = async (accountId, isClosing) => {
    // check if we're toggling associated jobs off
    if (isClosing) {
      setSelectedAccount(null);
      return toggleAssociatedJobs(false);
    }

    const currentAccount = accounts.find((acc) => acc.id === accountId);

    dispatch(handleFetchAssociatedJobs(accountId)).then((res) => {
      if (res.error) {
        setSelectedAccount(null);
        toggleAssociatedJobs(false);
      } else {
        setSelectedAccount(currentAccount);
        toggleAssociatedJobs(true);
      }
    });
  };

  return (
    <div className="autodesk-account-settings-wrapper">
      <div className="action-row">
        <h2>{translate("Autodesk Account Settings")}</h2>
        <a
          href="https://support.msuite.com/portal/en/kb/articles/multiple-autodesk-accounts"
          target="_blank"
          rel="noreferrer"
        >
          <AiFillQuestionCircle size={25} />
        </a>
      </div>
      <div className="autodesk-account-settings">
        <section>
          <Button
            isDisabled={newAccountEditing}
            className={`add ${newAccountEditing ? "disabled" : ""}`}
            onClick={() => toggleNewAccountEditing(true)}
          >
            <BiPlus size={25} />
            {translate("Add Autodesk Account")}
          </Button>
          {newAccountEditing && (
            <AutodeskAccount
              accountId={null}
              accountGuid={newAccountGuid}
              name={null}
              isInputDisabled={false}
              isDefaultDisabled={true}
              errorMessage={
                newAccountError && newAccountGuid.length > 35
                  ? isExisting
                    ? "This account is already saved"
                    : "No Account Access: Check GUID Name and ensure Msuite App has access to account"
                  : ""
              }
              autoFocus={true}
              onChange={(e) => {
                let value = e.target.value.trim();
                if (value.length > 30) {
                  // if they've entered most of it
                  value = value.replace("b.", ""); // remove 'b.' prefix
                  const hex = value.replace(/[^\da-f]/gi, "").slice(0, 34); // Remove all non-hex characters and limit size
                  // make sure its formatted right
                  value = `${hex.slice(0, 8)}-${hex.slice(8, 12)}-${hex.slice(
                    12,
                    16
                  )}-${hex.slice(16, 20)}-${hex.slice(20)}`;
                }
                setNewAccountGuid(value);
              }}
              onSave={!newAccountError ? onSave : null}
              key={"new_account"}
            />
          )}
          <div className={"scroll-section"}>
            {accounts?.length ? (
              accounts.map((account, idx) => (
                <AutodeskAccount
                  accountId={account.id}
                  accountGuid={account.account_guid}
                  name={account.name}
                  isDisabled={true}
                  key={idx}
                  handleAssociatedJobsClick={handleAssociatedJobsClick}
                  selectedAccount={selectedAccount}
                  dispatch={dispatch}
                  onArchive={updateArchiveStatus}
                  isDefault={account.default}
                  updateDefault={updateDefaultAccount}
                />
              ))
            ) : (
              <></>
            )}
          </div>
          <Button
            className={`archived-accounts-button`}
            onClick={() => dispatch(showArchivedAccountsModal())}
          >
            <BiPlus size={25} />
            {translate("Archived Accounts")}
          </Button>
        </section>
        {showAssociatedJobs && associatedJobs?.length && !isLoading && (
          <>
            <div className="separator"></div>
            <section className="associated-jobs">
              <h2>{translate("Associated Jobs")}</h2>
              <h4>{selectedAccount?.name}</h4>
              <AssociatedJobs />
            </section>
          </>
        )}
      </div>
    </div>
  );
};

export default AutodeskAccountSettings;
