import {
  fetchAutodeskAccounts,
  saveAutodeskAccount,
  updateAutodeskAccounts,
  fetchAssociatedJobs,
} from "../../_services";

export const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const createStarted = (type) => ({
  type: `CREATE_${type}_STARTED`,
});
export const createSucceeded = (type) => ({
  type: `CREATE_${type}_SUCCEEDED`,
});
export const createFailed = (type, error) => ({
  type: `CREATE_${type}_FAILED`,
  payload: error,
});

export const updateStarted = (type) => ({
  type: `UPDATE_${type}_STARTED`,
});
export const updateSucceeded = (type) => ({
  type: `UPDATE_${type}_SUCCEEDED`,
});
export const updateFailed = (type, error) => ({
  type: `UPDATE_${type}_FAILED`,
  payload: error,
});

export const handleFetchAutodeskAccounts = () => (dispatch) => {
  const type = "AUTODESK_ACCOUNTS";

  dispatch(receiveStarted(type));
  return fetchAutodeskAccounts(0).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchArchivedAutodeskAccounts = () => (dispatch) => {
  const type = "ARCHIVED_AUTODESK_ACCOUNTS";
  dispatch(receiveStarted(type));
  return fetchAutodeskAccounts(0).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));
    return res;
  });
};

export const handleSaveAutodeskAccount = (accountGuid, isDefault) => (
  dispatch
) => {
  const type = "AUTODESK_ACCOUNTS";

  dispatch(createStarted(type));
  return saveAutodeskAccount(accountGuid, isDefault).then((res) => {
    if (res.error) dispatch(createFailed(type, res.error));
    else dispatch(createSucceeded(type, res[0]));

    return res;
  });
};

export const handleUpdateAutodeskAccounts = (
  accountIds,
  archived,
  isDefault
) => (dispatch) => {
  const type = "AUTODESK_ACCOUNTS";

  dispatch(updateStarted(type));
  return updateAutodeskAccounts(accountIds, archived, isDefault).then((res) => {
    if (res.error) dispatch(updateFailed(type, res.error));
    else dispatch(updateSucceeded(type));

    return res;
  });
};

export const handleFetchAssociatedJobs = (accountId) => (dispatch) => {
  const type = "ASSOCIATED_JOBS";

  dispatch(receiveStarted(type));
  return fetchAssociatedJobs(accountId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));
    return res;
  });
};
