import {
  fetchItemGroupTypes,
  fetchItemGroups,
  assignUsersToItemGroups,
  unassignUsersFromItemGroups,
} from "../../_services";

const receiveItemGroupTypesStarted = () => ({
  type: "RECEIVE_ITEM_GROUP_TYPES_STARTED",
});
const receiveItemGroupTypesSucceeded = (itemGroupTypes) => ({
  type: "RECEIVE_ITEM_GROUP_TYPES_SUCCEEDED",
  payload: itemGroupTypes,
});
const receiveItemGroupTypesFailed = (error) => ({
  type: "RECEIVE_ITEM_GROUP_TYPES_FAILED",
  payload: error,
});
const receiveItemGroupsStarted = () => ({
  type: "RECEIVE_ITEM_GROUPS_STARTED",
});
const receiveItemGroupsSucceeded = (itemGroups) => ({
  type: "RECEIVE_ITEM_GROUPS_SUCCEEDED",
  payload: itemGroups,
});
const receiveItemGroupsFailed = (error) => ({
  type: "RECEIVE_ITEM_GROUPS_FAILED",
  payload: error,
});
const receiveUserWorkStarted = () => ({
  type: "RECEIVE_USER_WORK_STARTED",
});
const receiveUserWorkSucceeded = (userWork) => ({
  type: "RECEIVE_USER_WORK_SUCCEEDED",
  payload: userWork,
});
const receiveUserWorkFailed = (error) => ({
  type: "RECEIVE_USER_WORK_FAILED",
  payload: error,
});
const receiveUnassignedWorkStarted = () => ({
  type: "RECEIVE_UNASSIGNED_WORK_STARTED",
});
const receiveUnassignedWorkSucceeded = (unassignedWork) => ({
  type: "RECEIVE_UNASSIGNED_WORK_SUCCEEDED",
  payload: unassignedWork,
});
const receiveUnassignedWorkFailed = (error) => ({
  type: "RECEIVE_UNASSIGNED_WORK_FAILED",
  payload: error,
});

export const handleFetchItemGroupTypes = (dispatch) => {
  dispatch(receiveItemGroupTypesStarted());
  return fetchItemGroupTypes().then((res) => {
    if (res.error) return dispatch(receiveItemGroupTypesFailed(res.error));
    return dispatch(receiveItemGroupTypesSucceeded(res));
  });
};

export const handleFetchItemGroups = (groupTypeId) => (dispatch) => {
  dispatch(receiveItemGroupsStarted());
  return fetchItemGroups({ groupTypeId }).then((res) => {
    if (res.error) return dispatch(receiveItemGroupsFailed(res.error));
    return dispatch(receiveItemGroupsSucceeded(res));
  });
};

export const handleFetchUserWork = (assignedUserId, locationId) => (
  dispatch
) => {
  dispatch(receiveUserWorkStarted());
  return fetchItemGroups({ assignedUserId, locationId }).then((res) => {
    if (res.error) return dispatch(receiveUserWorkFailed(res.error));
    return dispatch(receiveUserWorkSucceeded(res));
  });
};

export const handleFetchUnassignedWork = (notAssignedUserId) => (dispatch) => {
  dispatch(receiveUnassignedWorkStarted());
  return fetchItemGroups({ notAssignedUserId }).then((res) => {
    if (res.error) return dispatch(receiveUnassignedWorkFailed(res.error));
    return dispatch(receiveUnassignedWorkSucceeded(res));
  });
};

export const handleAssignUsersToItemGroups = (userIds, groupIds) => (
  dispatch
) => {
  return assignUsersToItemGroups(userIds, groupIds);
};

export const handleUnassignUsersFromItemGroups = (userIds, groupIds) => (
  dispatch
) => {
  return unassignUsersFromItemGroups(userIds, groupIds);
};
