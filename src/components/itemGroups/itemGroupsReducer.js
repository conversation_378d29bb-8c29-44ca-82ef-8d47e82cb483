const initialState = {
  itemGroupTypes: [],
  itemGroups: [],
  userWork: [],
  unassignedWork: [],
  isLoading: false,
  error: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_ITEM_GROUP_TYPES_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_ITEM_GROUP_TYPES_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        itemGroupTypes: payload,
      };
    case "RECEIVE_ITEM_GROUP_TYPES_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_ITEM_GROUPS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_ITEM_GROUPS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        itemGroups: payload,
      };
    case "RECEIVE_ITEM_GROUPS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_USER_WORK_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_USER_WORK_SUCCEEDED":
      return { ...state, isLoading: false, error: null, userWork: payload };
    case "RECEIVE_USER_WORK_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_UNASSIGNED_WORK_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_UNASSIGNED_WORK_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        unassignedWork: payload,
      };
    case "RECEIVE_UNASSIGNED_WORK_FAILED":
      return { ...state, isLoading: false, error: payload };
    default:
      return state;
  }
}
