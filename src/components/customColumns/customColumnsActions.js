import {
  fetchWorkItemColumns,
  saveCustomColumn,
  updateCustomColumn,
  updateCustomColumnData,
  deleteCustomColumn,
  updateManyCustomColumnData,
} from "../../_services";

export const receiveSucceeded = (type, items) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload: items,
});

export const receiveStarted = (type) => ({
  type: `RECEIVE_${type}_STARTED`,
});

export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});
export const createStarted = (type) => ({
  type: `CREATE_${type}_STARTED`,
});
export const createSucceeded = (type) => ({
  type: `CREATE_${type}_SUCCEEDED`,
});
export const createFailed = (type, error) => ({
  type: `CREATE_${type}_FAILED`,
  payload: error,
});
export const updateStarted = (type) => ({
  type: `UPDATE_${type}_STARTED`,
});
export const updateFailed = (type, error) => ({
  type: `UPDATE_${type}_FAILED`,
  payload: error,
});
export const updateSucceeded = (type) => ({
  type: `UPDATE_${type}_SUCCEEDED`,
});
export const deleteStarted = (type) => ({
  type: `DELETE_${type}_STARTED`,
});
export const deleteFailed = (type, error) => ({
  type: `DELETE_${type}_FAILED`,
  payload: error,
});
export const deleteSucceeded = (type) => ({
  type: `DELETE_${type}_SUCCEEDED`,
});

export const handleFetchCustomColumns = (dispatch) => {
  const type = "CUSTOM_COLUMNS";
  dispatch(receiveStarted(type));
  return fetchWorkItemColumns(true).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));
    return res;
  });
};

export const handleSaveCustomColumn = (name, dataType, tableTarget) => (
  dispatch
) => {
  const type = "CUSTOM_COLUMNS";
  dispatch(createStarted(type));
  return saveCustomColumn(name, dataType, tableTarget).then((res) => {
    if (res.error) dispatch(createFailed(type, res.error));
    else dispatch(createSucceeded(type));
    return res;
  });
};

export const handleUpdateCustomColumn = (id, updatedName) => (dispatch) => {
  const type = "CUSTOM_COLUMNS";
  dispatch(updateStarted(type));

  return updateCustomColumn(id, updatedName).then((res) => {
    if (res.error) dispatch(updateFailed(type, res.error));
    else dispatch(updateSucceeded(type));

    return res;
  });
};

export const handleUpdateCustomColumnData = (
  tableTarget,
  itemId,
  columnId,
  updatedValue
) => (dispatch) => {
  const type = "CUSTOM_COLUMNS";
  dispatch(updateStarted(type));

  return updateCustomColumnData(
    tableTarget,
    itemId,
    columnId,
    updatedValue
  ).then((res) => {
    if (res.error) dispatch(updateFailed(type, res.error));
    else dispatch(updateSucceeded(type));

    return res;
  });
};

export const handleDeleteCustomColumn = (id) => (dispatch) => {
  const type = "CUSTOM_COLUMNS";
  dispatch(deleteStarted(type));

  return deleteCustomColumn(id).then((res) => {
    if (res.error) dispatch(deleteFailed(type, res.error));
    else dispatch(deleteSucceeded(type));

    return res;
  });
};

export const handleUpdateManyCustomColumnData = (tableTarget, data) => (
  dispatch
) => {
  const type = "CUSTOM_COLUMNS";
  dispatch(updateStarted(type));

  return updateManyCustomColumnData(tableTarget, data).then((res) => {
    if (res.error) dispatch(updateFailed(type, res.error));
    else dispatch(updateSucceeded(type));

    return res;
  });
};
