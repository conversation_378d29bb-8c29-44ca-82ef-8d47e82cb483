const initialState = {
  isLoading: false,
  error: null,
  customColumns: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_CUSTOM_COLUMNS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_CUSTOM_COLUMNS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        customColumns: payload.filter((column) => column.is_custom === 1),
      };
    case "RECEIVE_CUSTOM_COLUMNS_FAILED":
      return {
        ...state,
        isLoading: false,
        visibleColumns: null,
        error: payload,
      };
    case "CREATE_CUSTOM_COLUMNS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "CREATE_CUSTOM_COLUMNS_SUCEEDED":
      return { ...state, isLoading: false, error: null };
    case "CREATE_CUSTOM_COLUMNS_FAILED":
      return { ...state, isLoadung: false, error: payload };
    case "UPDATE_CUSTOM_COLUMNS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "UPDATE_CUSTOM_COLUMNS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "UPDATE_CUSTOM_COLUMNS_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    default:
      return state;
  }
}
