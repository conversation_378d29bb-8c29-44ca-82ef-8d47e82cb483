@import "../styles/colors.scss";
@import "../styles/sizes.scss";

div.custom-columns-wrapper {
  width: calc(100vw - 100px);
  padding: 20px;
  color: $textLight;

  & div.custom-ag-styles.ag-theme-balham-dark {
    width: 100%;
    height: calc(100vh - $headerFooter - $pageTitle - 20px - 100px);
  }

  div.table-container-tabs {
    display: flex;
    height: 32px;
    justify-content: space-between;
  }

  .custom-columns-title {
    margin: 0 0 15px;
  }

  & div.header-row {
    display: flex;
  }

  .tabs {
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    overflow: hidden;
    width: 800px;
  }

  & button.add-new-column {
    margin: 0 0 0 auto;
    width: 160px;
    height: 40px;
    font-size: 0.9rem;
    background-color: $fabProBlue;
    color: white;
  }

  & button.disabled {
    background-color: darken($fabProBlue, 20%);
    cursor: default;
  }

  .tab-buttons {
    display: flex;
    justify-content: flex-start;
    padding: 10px;
    margin-bottom: 10px;
  }

  .tab-buttons button {
    border: none;
    outline: none;
    cursor: pointer;
    margin-right: 20px;
    padding: 10px 20px 5px;
    font-size: 16px;
    font-weight: bold;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    background-color: transparent;
    color: white;
    border-radius: 0px;
    box-shadow: none;
  }

  .tab-buttons button:hover {
    background-color: $fabProBlue;
  }

  .tab-buttons button.active {
    border-bottom: 2px solid $fabProBlue;
  }
}
