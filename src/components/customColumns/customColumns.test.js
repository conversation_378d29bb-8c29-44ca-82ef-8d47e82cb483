// NPM PACKAGE IMPORTS
import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockAdapter from "axios-mock-adapter";
import thunk from "redux-thunk";

// REDUX IMPORTS
import {
  // ACTION CREATORS
  receiveStarted,
  receiveFailed,
  receiveSucceeded,
  createStarted,
  createSucceeded,
  createFailed,
  updateStarted,
  updateFailed,
  updateSucceeded,
  deleteStarted,
  deleteFailed,
  deleteSucceeded,
  // ACTION HANDLERS
  handleFetchCustomColumns,
  handleSaveCustomColumn,
  handleUpdateCustomColumn,
  handleDeleteCustomColumn,
  handleUpdateCustomColumnData,
} from "./customColumnsActions";

describe("CUSTOM COLUMNS", () => {
  let store, httpMock;
  const baseURL = `${process.env.REACT_APP_API}`;
  const testError = (message) => ({
    error: { status: 404, message },
  });
  const testErrorVariant = (message) => ({
    status: 404,
    message,
  });
  const type = "CUSTOM_COLUMNS";

  describe("Action handlers should perform the neccessary functions", () => {
    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });
    const testColumns = [
      {
        id: 107,
        display_name: "work item column",
        name: "work_item_column",
        normal_name: "work_item_column",
        data_type: "date",
        visible_in_work_table: 1,
        table_effect_id: null,
        color: null,
        usable_for_conditions: 0,
        table_source: "custom_columns_data",
        editable: 1,
        groupable: 0,
        is_custom: 1,
        table_target: "work_items",
      },
      {
        id: 106,
        display_name: "another drawing column",
        name: "another_drawing_column",
        normal_name: "another_drawing_column",
        data_type: "date",
        visible_in_work_table: 1,
        table_effect_id: null,
        color: null,
        usable_for_conditions: 0,
        table_source: "custom_columns_data",
        editable: 1,
        groupable: 0,
        is_custom: 1,
        table_target: "drawings",
      },
    ];

    it("handleFetchCustomColumn", async () => {
      httpMock
        .onGet(`${baseURL}/columns?visible=1`)
        .replyOnce(200, testColumns)
        .onGet(`${baseURL}/columns?visible=1`)
        .replyOnce(404, testErrorVariant("No columns found."));
      let response = await store.dispatch(handleFetchCustomColumns);
      let receivedActions = store.getActions();
      let expectedActions = [
        receiveStarted(type),
        receiveSucceeded(type, testColumns),
      ];
      expect(response).toEqual(testColumns);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
      response = await store.dispatch(handleFetchCustomColumns);
      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted(type),
        receiveFailed(type, testErrorVariant("No columns found.")),
      ];
      expect(response).toEqual(testError("No columns found."));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
    it("handleSaveCustomColumn", async () => {
      const createdColumn = {
        id: 108,
        display_name: "a third drawing column",
        name: "a_third_drawing_column",
        normal_name: "a_third_drawing_column",
        data_type: "decimal",
        visible_in_work_table: 1,
        table_effect_id: null,
        color: null,
        usable_for_conditions: 0,
        table_source: "custom_columns_data",
        editable: 1,
        groupable: 0,
        is_custom: 1,
        table_target: "drawings",
      };
      const newDisplayName = "a third drawing column";
      const newDataType = "decimal";
      const newTableTarget = "drawings";

      httpMock
        .onPost(`${baseURL}/columns`)
        .replyOnce(200, createdColumn)
        .onPost(`${baseURL}/columns`)
        .replyOnce(
          400,
          testErrorVariant(
            "Following column(s) already exist: a third drawing column"
          )
        );

      let response = await store.dispatch(
        handleSaveCustomColumn(newDisplayName, newDataType, newTableTarget)
      );
      let receivedActions = store.getActions();
      let expectedActions = [createStarted(type), createSucceeded(type)];

      expect(response).toEqual(createdColumn);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
      response = await store.dispatch(
        handleSaveCustomColumn(newDisplayName, newDataType, newTableTarget)
      );
      receivedActions = store.getActions();
      expectedActions = [
        createStarted(type),
        createFailed(
          type,
          testErrorVariant(
            "Following column(s) already exist: a third drawing column"
          )
        ),
      ];
      expect(response).toEqual(
        testError("Following column(s) already exist: a third drawing column")
      );
      expect(receivedActions).toEqual(expectedActions);
    });
    it("handleUpdateCustomColumn", async () => {
      const updatedColumn = {
        id: 1,
        display_name: "update display name",
        data_type: "decimal",
      };

      httpMock
        .onPut(`${baseURL}/columns/1`)
        .replyOnce(200, updatedColumn)
        .onPut(`${baseURL}/columns/200`)
        .replyOnce(404, testError("Unable to update custom column"));

      let response = await store.dispatch(
        handleUpdateCustomColumn(1, "update display name")
      );

      let receivedActions = store.getActions();
      let expectedActions = [updateStarted(type), updateSucceeded(type)];

      expect(response).toEqual(updatedColumn);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleUpdateCustomColumn(200, ""));

      receivedActions = store.getActions();
      expectedActions = [
        updateStarted(type),
        updateFailed(type, testErrorVariant("Unable to update custom column")),
      ];

      expect(response).toEqual(testError("Unable to update custom column"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });
  });

  it("handleUpdateCustomColumnData", async () => {
    const type = "CUSTOM_COLUMNS";
    const message = "Unable to update custom column data";

    const testColumn = [
      {
        id: 1,
        parent_id: 1,
        custom_columns_id: 1,
        data: "updated value",
      },
    ];

    httpMock
      .onPut(`${baseURL}/items/1/columns/1`)
      .replyOnce(200, testColumn)
      .onPut(`${baseURL}/items/1/columns/100`)
      .replyOnce(400, testErrorVariant(message));

    let response = await store.dispatch(
      handleUpdateCustomColumnData("items", 1, 1, "updated value")
    );

    let receivedActions = store.getActions();
    let expectedActions = [updateStarted(type), updateSucceeded(type)];

    expect(response).toEqual(testColumn);
    expect(receivedActions).toEqual(expectedActions);

    store.clearActions();

    response = await store.dispatch(
      handleUpdateCustomColumnData("items", 1, 100, "updated value")
    );

    receivedActions = store.getActions();
    expectedActions = [
      updateStarted(type),
      updateFailed(type, testErrorVariant(message)),
    ];

    expect(response).toEqual(testError(message));
    expect(receivedActions).toEqual(expectedActions);
  });

  it("handleDeleteCustomColumn", async () => {
    httpMock
      .onDelete(`${baseURL}/columns/10`)
      .replyOnce(200, `Successfully deleted column`)
      .onDelete(`${baseURL}/columns/20`)
      .replyOnce(404, testError("Unable to delete column"));

    let response = await store.dispatch(handleDeleteCustomColumn(10));

    let receivedActions = store.getActions();
    let expectedActions = [deleteStarted(type), deleteSucceeded(type)];

    expect(response).toEqual(`Successfully deleted column`);
    expect(receivedActions).toEqual(expectedActions);

    store.clearActions();

    response = await store.dispatch(handleDeleteCustomColumn(20));

    receivedActions = store.getActions();
    expectedActions = [
      deleteStarted(type),
      deleteFailed(type, testErrorVariant("Unable to delete column")),
    ];

    expect(response).toEqual(testError("Unable to delete column"));
    expect(receivedActions).toEqual(expectedActions);
  });
});
