// NPM PACKAGE IMPORTS
import React, { useState, useEffect } from "react";

//STYLE IMPORTS
import "./stylesCustomColumns.scss";

//REDUX IMPORTS
import { useDispatch, useSelector } from "react-redux";
import {
  handleFetchCustomColumns,
  handleUpdateCustomColumn,
  handleDeleteCustomColumn,
} from "./customColumnsActions";

//COMPONENT IMPORTS
import AgTable from "../reusable/agTable/AgTable";
import EditCustomColumnCellRenderer from "../reusable/frameworkComponents/EditCustomColumnCellRenderer";
import DeleteCellRenderer from "../reusable/frameworkComponents/deleteCellRenderer/DeleteCellRenderer";
import Button from "msuite_storybook/dist/button/Button";
import ConfirmationModal from "../reusable/confirmationModal/ConfirmationModal";

//HELPER FUNCTION IMPORTS
import { rowClassRules } from "../../_utils";
import { notify } from "../reusable/alertPopup/alertPopupActions";
import useMount from "../../hooks/useMount";

const MAX_CUSTOM_COLUMNS = 8;
export const customColDataTypes = {
  date: "Date (mm-dd-yyyy)",
  string: "Text",
  decimal: "Number Value (Decimal)",
};

const CustomColumns = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [filteredColumns, setFilteredColumns] = useState([]);
  const [isAddingNewColumn, toggleAddingNewColumn] = useState(false);
  const [showDeleteConfirmation, toggleDeleteConfirmation] = useState(false);
  const [columnIdToDelete, setColumnIdToDelete] = useState(null);

  const { customColumns } = useSelector((state) => state.customColumns);
  const { permissions, userSettings, features } = useSelector(
    (state) => state.profileData
  );
  const dispatch = useDispatch();

  const dataTypes = {
    date: "Date",
    text: "Text",
    decimal: "Number",
  };
  const dataTypeKeys = Object.keys(dataTypes);
  const tableTypes = ["jobs", "packages", "drawings", "work_items"];

  const onGridReady = (params) => {
    if (params.api) setGridOptionsApi(params.api);
  };

  const handleTabClick = (index) => {
    setActiveTab(index);
    toggleAddingNewColumn(false);
  };

  const handleDeleteConfirmation = (id) => {
    toggleDeleteConfirmation(false);
    dispatch(handleDeleteCustomColumn(id)).then((res) => {
      if (res.error) return;

      toggleAddingNewColumn(false);
      dispatch(handleFetchCustomColumns);
    });
  };

  const buildColumnDefs = () => {
    return [
      {
        valueGetter: (params) =>
          `${params.node.rowIndex + 1} of ${MAX_CUSTOM_COLUMNS}`,
        sortable: false,
        autoHeight: true,
        menuTabs: [],
        width: 75,
        colId: "rowIndex",
      },
      {
        headerName: "Name",
        field: "display_name",
        valueParser: (params) => {
          if (params?.newValue === "") return params?.oldValue;
          else return params?.newValue?.trim();
        },
        sortable: false,
        autoHeight: true,
        menuTabs: [],
        width: 150,
        colId: "display_name",
        editable: true,
      },
      {
        headerName: "Type",
        field: "data_type",
        sortable: false,
        autoHeight: true,
        menuTabs: [],
        width: 150,
        colId: "data_type",
        // show "text" instead of "string"
        valueGetter: (params) =>
          params.data.data_type === "string" ? "text" : params.data.data_type,
        editable: (params) => params.data.addRow === true,
        cellEditor: "agSelectCellEditor",
        cellEditorParams: {
          values: dataTypeKeys,
        },
      },
      {
        sortable: false,
        cellRendererSelector: (gridParams) => {
          const createColumnDetails = {
            component: "editCustomColumn",
            params: {
              params: gridParams,
              sendErrorNotification: () => {
                dispatch(
                  notify({
                    id: Date.now(),
                    type: "ERROR",
                    message:
                      "Name and Type are required to create a custom column",
                  })
                );
              },
            },
          };
          const updateColumnDetails = {
            component: "customColumnDelete",
            params: {
              params: gridParams,
              onClick: () => {
                setColumnIdToDelete(gridParams.data.id);
                toggleDeleteConfirmation(true);
              },
            },
          };

          return gridParams.data.addRow
            ? createColumnDetails
            : updateColumnDetails;
        },
        suppressMenu: true,
        width: 75,
        colId: "action",
      },
    ];
  };

  const onCellValueChanged = (params) => {
    // ignore if updating new row columns
    if (params?.data.addRow) return;

    const columnId = params?.data.id;
    const updatedValue = params?.newValue;
    dispatch(handleUpdateCustomColumn(columnId, updatedValue)).then((res) => {
      if (res.error) {
        if (params.oldValue) {
          params.node.setData({
            ...params.data,
            [params.column.colId]: params.oldValue,
          });
        }
      }

      dispatch(handleFetchCustomColumns);
    });
  };

  const gridOptions = {
    rowData: filteredColumns,
    columnDefs: buildColumnDefs(),
    rowClassRules,
    suppressRowClickSelection: true,
    onGridReady,
    stopEditingWhenGridLosesFocus: true,
    editType: "fullRow",
    onCellValueChanged,
    frameworkComponents: {
      editCustomColumn: EditCustomColumnCellRenderer,
      customColumnDelete: DeleteCellRenderer,
    },
    sideBar: false,
  };

  useEffect(() => {
    if (!permissions.length || !features.length) return;
    // redirect to homepage if user doesn't have permission to view settings / feature on
    if (!permissions.includes(303) || !features.includes(53)) {
      const homePage = userSettings?.home_page || "jobs";
      window.location.assign(`${process.env.REACT_APP_FABPRO}/${homePage}`);
    }
  }, [permissions, userSettings, features]);

  useMount(() => dispatch(handleFetchCustomColumns));

  useEffect(() => {
    if (customColumns)
      setFilteredColumns(
        customColumns.filter(
          (column) => column.table_target === tableTypes[activeTab]
        )
      );
  }, [customColumns, activeTab]);

  useEffect(() => {
    if (!gridOptionsApi) return;

    gridOptionsApi.setRowData(filteredColumns);
  }, [filteredColumns, gridOptionsApi]);

  const addNewColumn = () => {
    toggleAddingNewColumn(true);

    gridOptionsApi.setRowData([
      ...filteredColumns,
      {
        addRow: true,
        display_name: "",
        data_type: null,
        table_type: tableTypes[activeTab],
        toggleAddColumn: toggleAddingNewColumn,
      },
    ]);

    // might want to loop using gridOptionsApi.forEachNode if we plan on filtering/sorting this table
    const rowToEdit = gridOptionsApi.getDisplayedRowAtIndex(
      filteredColumns.length
    );

    gridOptionsApi.setFocusedCell(rowToEdit?.rowIndex, "display_name");
    gridOptionsApi.startEditingCell({
      rowIndex: rowToEdit?.rowIndex,
      colKey: "display_name",
    });
  };

  return (
    <div className="custom-columns-wrapper">
      <h3 className="custom-columns-title">Custom Columns</h3>
      <div className="header-row">
        <div className="tabs">
          <div className="tab-buttons">
            <Button
              className={activeTab === 0 ? "active" : ""}
              onClick={() => handleTabClick(0)}
            >
              Jobs
            </Button>
            <Button
              className={activeTab === 1 ? "active" : ""}
              onClick={() => handleTabClick(1)}
            >
              Packages
            </Button>
            <Button
              className={activeTab === 2 ? "active" : ""}
              onClick={() => handleTabClick(2)}
            >
              Drawings
            </Button>
            <Button
              className={activeTab === 3 ? "active" : ""}
              onClick={() => handleTabClick(3)}
            >
              Items
            </Button>
          </div>
        </div>
        <Button
          disabled={isAddingNewColumn || filteredColumns?.length > 7}
          onClick={addNewColumn}
          className={`add-new-column ${
            (isAddingNewColumn || filteredColumns?.length > 7) && "disabled"
          }`}
        >
          Add New Column
        </Button>
      </div>
      <div className="tab-content">
        <AgTable gridOptions={gridOptions} />
      </div>
      {showDeleteConfirmation && columnIdToDelete && (
        <ConfirmationModal
          showModal={showDeleteConfirmation}
          handleClick={() => handleDeleteConfirmation(columnIdToDelete)}
          action="DELETE"
          message={`Are you sure you want to delete this custom column? This action cannot be undone.`}
          submitText="Delete"
          handleCancel={() => {
            toggleDeleteConfirmation(false);
            setColumnIdToDelete(null);
          }}
        />
      )}
    </div>
  );
};

export default CustomColumns;
