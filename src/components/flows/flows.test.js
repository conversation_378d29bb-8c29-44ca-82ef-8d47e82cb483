// NPM PACKAGE IMPORTS
import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockAdapter from "axios-mock-adapter";
import thunk from "redux-thunk";

// REDUX IMPORTS
import {
  // ACTION CREATORS
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  // ACTION HANDLERS
  handleFetchWorkStages,
  handleFetchWorkStagesGet,
  handleFetchFlows,
  handleFetchFlowMap,
  handleFetchWorkStageColumns,
  handleSaveFlowMap,
  handleCleanup,
  handleUpdateFlow,
  handleFetchConditions,
  handleFetchWorkItemColumns,
  handleSaveCondition,
  handleFetchWorkStageConditions,
  handleSaveWorkStageConditions,
  handleCreateWorkStage,
  handleFetchItemOpenConditions,
  handleUpdateStageStatusGroup,
  handleUpdateWorkStage,
  handleCreateWorkFlow,
  handleChangeItemWorkflow,
  handleUpdateWorkStageColumns,
  handleResetFlowsReducer,
  handleFetchShippingBlocks,
  handleDeleteCondition,
  handleFetchFlowCodes,
  handleFetchWorkStageGroupableColumns,
  handleCreateFlowCode,
  handleFetchStageCodes,
  handleCreateStageCode,
  handleDeleteFlow,
  handleDuplicateStage,
} from "./flowsActions";

import { alphaNumericSortByName } from "../../_utils";

describe("FLOWS", () => {
  describe("action handlers should perform the necessary functions", () => {
    let store;
    let httpMock;

    const testError = (mes) => ({
      error: { status: 404, message: mes },
    });

    const workStages = [
      {
        id: 1,
        name: "Cut Table",
        stage_work_level_id: 1,
        shape: "Square",
        stage_code_id: null,
        metric_work_item_column_id: 41,
        metric_work_item_column_name: "Size",
        locked: 1,
        archived: 0,
        nestable: 1,
        rejectable: 0,
        groupable: 0,
        shipping_block_id: null,
        shipping_block_position: null,
      },
      {
        id: 2,
        name: "New Stage",
        stage_work_level_id: 1,
        shape: "Square",
        stage_code_id: null,
        metric_work_item_column_id: 41,
        metric_work_item_column_name: "Size",
        locked: 1,
        archived: 0,
        nestable: 1,
        rejectable: 0,
        groupable: 0,
        shipping_block_id: null,
        shipping_block_position: null,
      },
      {
        id: 86,
        name: "Weld",
        stage_work_level_id: 1,
        shape: "Square",
        stage_code_id: 11,
        metric_work_item_column_id: 41,
        metric_work_item_column_name: "Size",
        locked: 1,
        archived: 0,
        nestable: 0,
        rejectable: 0,
        groupable: 1,
        shipping_block_id: null,
        shipping_block_position: null,
      },
    ];
    const workFlows = [
      {
        id: 1,
        name: "Hercutech",
        default: 0,
        archived: 0,
        flow_code_id: null,
      },
      {
        id: 2,
        name: "FabPro LITE",
        default: 0,
        archived: 0,
        flow_code_id: null,
      },
    ];
    const workFlowMap = [
      {
        id: 23,
        name: "JK test flow 2",
        work_flow_map: [
          {
            node_id: 1,
            stage_id: 53,
            stage_name: "Materials Prep",
            shape: "Square",
            logic_gate_id: null,
            stage_status_group_name: "jk test status group 1",
            stage_status_group_color: "#0000ff",
            default_weight: null,
            dynamic_weight: null,
            coordinate: '{"x":5381.653746770025,"y":5148.449612403099}',
            metric_work_item_column_id: 37,
            nestable: 0,
            rejectable: 0,
            stage_work_level_id: 1,
            groupable: 1,
            stage_code_id: null,
            stage_code_name: null,
            stage_report: 1,
            stage_order: 1,
            shipping_block_position: null,
            shipping_block_id: null,
          },
          {
            node_id: 2,
            stage_id: 102,
            stage_name: "JK test new stage",
            shape: "Square",
            logic_gate_id: null,
            stage_status_group_name: "jk test status group",
            stage_status_group_color: "#000000",
            default_weight: null,
            dynamic_weight: null,
            coordinate: '{"x":5372.609819121447,"y":5301.03359173127}',
            metric_work_item_column_id: 42,
            nestable: 1,
            rejectable: 1,
            stage_work_level_id: 1,
            groupable: 1,
            stage_code_id: 20,
            stage_code_name: "Test Stage Code",
            stage_report: 0,
            stage_order: 2,
            shipping_block_position: null,
            shipping_block_id: null,
          },
        ],
        work_flow_link: [
          {
            from_node_id: 1,
            to_node_id: 2,
            item_open_condition_id: 1,
            item_open_condition_name: "Open",
          },
        ],
      },
    ];
    const workStageColumns = [
      {
        id: 1,
        name: "Action Racking Assemblies",
        stage_work_level_id: 1,
        shape: "Square",
        stage_code_id: 15,
        metric_work_item_column_id: null,
        locked: 0,
        archived: 0,
        nestable: 0,
        rejectable: 0,
        groupable: 0,
        shipping_block_id: null,
        shipping_block_position: null,
        work_item_columns: [
          {
            id: 9,
            display_name: "Drawing Name",
            name: "SpoolName",
            normal_name: "drawing_name",
            data_type: "string",
            visible_in_work_table: 1,
            table_effect_id: null,
            color: null,
            usable_for_conditions: 0,
            table_source: "Spools",
            editable: 1,
            groupable: 1,
            group_by: 1,
          },
        ],
      },
    ];
    const flowMapPost = {
      work_flow_id: 59,
      work_flow_map_data: [
        {
          node_id: 1,
          stage_id: 228,
          logic_gate_id: null,
          stage_status_group_name: "In Fabrication",
          stage_status_group_color: "#000080",
          default_weight: null,
          coordinate: '{"x":5376,"y":5102}',
          stage_report: 0,
          stage_order: 1,
        },
        {
          node_id: 3,
          stage_id: 229,
          logic_gate_id: null,
          stage_status_group_name: "In Fabrication",
          stage_status_group_color: "#000080",
          default_weight: null,
          coordinate: '{"x":5210,"y":5251}',
          stage_report: 0,
          stage_order: 2,
        },
      ],
      work_flow_link_data: [
        {
          from_node_id: 1,
          to_node_id: 3,
          item_open_condition_id: 2,
        },
      ],
    };
    const conditionsFetch = [
      {
        id: 27,
        work_item_columns_id: 15,
        work_item_column_name: "End Prep 1",
        comparison: "Not Equal To",
        value: "B",
      },
    ];
    const stageConditionsFetch = [
      {
        sub_group: 1,
        sub_group_level: 1,
        sub_group_operand: "start",
        conditions: [
          {
            work_item_columns_id: 36,
            work_item_column_name: "Joining Procedure",
            comparison: "Does Not Contain",
            value: "solder",
            operand: "start",
            work_item_condition_id: 142,
            deleted: 0,
            sub_group: 1,
          },
        ],
        grouping_level_1_id: 1,
        grouping_level_2_id: null,
        grouping_level_3_id: null,
        grouping_level_4_id: null,
        grouping_level_5_id: null,
        grouping_level_6_id: null,
      },
    ];
    const workItemColumnFetch = [
      {
        id: 1,
        display_name: "Job Number",
        name: "JobNumber",
        normal_name: "job_number",
        data_type: "string",
        visible_in_work_table: 1,
        table_effect_id: null,
        color: null,
        usable_for_conditions: 0,
        table_source: "Jobs",
        editable: 1,
        groupable: 0,
      },
    ];
    const conditionGroupPost = [
      {
        conditions: [
          {
            work_item_columns_id: 36,
            work_item_column_name: "Joining Procedure",
            comparison: "Does Not Contain",
            value: "solder",
            operand: "start",
            work_item_condition_id: 142,
            deleted: 0,
            sub_group: 1,
            sub_group_level: 1,
            grouping_level_1_id: 1,
            grouping_level_2_id: null,
            grouping_level_3_id: null,
            grouping_level_4_id: null,
            grouping_level_5_id: null,
            grouping_level_6_id: null,
          },
        ],
        sub_group: 1,
        sub_group_level: 1,
        sub_group_operand: "start",
        grouping_level_1_id: null,
        grouping_level_2_id: null,
        grouping_level_3_id: null,
        grouping_level_4_id: null,
        grouping_level_5_id: null,
        grouping_level_6_id: null,
      },
    ];
    const shippingBlocks = [
      {
        id: 1,
        name: "General Shipping",
        stages: [
          {
            id: 176,
            name: "Loading",
            stage_work_level_id: 4,
            shape: "Square",
            stage_code_id: null,
            metric_work_item_column_id: null,
            locked: 0,
            archived: 0,
            nestable: 0,
            rejectable: 0,
            shipping_block_position: 1,
          },
          {
            id: 15,
            name: "Delivery",
            stage_work_level_id: 4,
            shape: "Square",
            stage_code_id: null,
            metric_work_item_column_id: null,
            locked: 0,
            archived: 0,
            nestable: 0,
            rejectable: 0,
            shipping_block_position: 2,
          },
          {
            id: 177,
            name: "Receiving",
            stage_work_level_id: 4,
            shape: "Square",
            stage_code_id: null,
            metric_work_item_column_id: null,
            locked: 0,
            archived: 0,
            nestable: 0,
            rejectable: 0,
            shipping_block_position: 3,
          },
        ],
      },
    ];
    const flowCodes = [
      {
        id: 21,
        name: "RL Test Flow Code",
        created_by: 1,
        created_on: 1606859848,
        updated_by: null,
        deleted: 0,
        deleted_by: null,
        deleted_on: null,
        assigned_flows:
          "AJ Dup Test 2, AJ Dup Test 3, JK test flow 2, RL Test Flow Creation",
      },
    ];
    const stageCodes = [
      {
        id: 20,
        name: "Test Stage Code",
        created_by: 166,
        created_on: 1590002077,
        updated_by: null,
        deleted: 0,
        deleted_by: null,
        deleted_on: null,
        assigned_stages: "",
      },
    ];

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    it("handleFetchWorkStages fetches all work stages for all given filters", async () => {
      const filters = [false, [973], [1904], [102141], 23];
      const testBody = {
        job_ids: "973",
        package_ids: "1904",
        drawing_ids: "102141",
        app_type: "fab",
        exclude_shipping_blocks: 1,
        flow_ids: "23",
      };
      const err = testError("No work stages found.");
      httpMock
        .onPost(`${process.env.REACT_APP_API}/work-stages/fetch`)
        .replyOnce(200, workStages)
        .onPost(`${process.env.REACT_APP_API}/work-stages/fetch`)
        .replyOnce(404, err);

      await store.dispatch(handleFetchWorkStages(...filters)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGES"),
          receiveSucceeded("WORK_STAGES", workStages),
        ];

        expect(httpMock.history.post[0].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(workStages[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchWorkStages(...filters)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGES"),
          receiveFailed("WORK_STAGES", err),
        ];

        expect(httpMock.history.post[1].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchWorkStages fetches all work stages for drawing and flow filters", async () => {
      const filters = [false, null, null, [102141], 23];
      const testBody = {
        drawing_ids: "102141",
        app_type: "fab",
        exclude_shipping_blocks: 1,
        flow_ids: "23",
      };
      const err = testError("No work stages found.");

      httpMock
        .onPost(`${process.env.REACT_APP_API}/work-stages/fetch`)
        .replyOnce(200, workStages)
        .onPost(`${process.env.REACT_APP_API}/work-stages/fetch`)
        .replyOnce(404, err);

      await store.dispatch(handleFetchWorkStages(...filters)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGES"),
          receiveSucceeded("WORK_STAGES", workStages),
        ];

        expect(httpMock.history.post[0].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(workStages[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchWorkStages(...filters)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGES"),
          receiveFailed("WORK_STAGES", err),
        ];

        expect(httpMock.history.post[1].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchWorkStages fetches all work stages for job filter", async () => {
      const filters = [false, [973]];
      const testBody = {
        job_ids: "973",
        app_type: "fab",
        exclude_shipping_blocks: 1,
      };
      const err = testError("No work stages found.");

      httpMock
        .onPost(`${process.env.REACT_APP_API}/work-stages/fetch`)
        .replyOnce(200, workStages)
        .onPost(`${process.env.REACT_APP_API}/work-stages/fetch`)
        .replyOnce(404, err);

      await store.dispatch(handleFetchWorkStages(...filters)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGES"),
          receiveSucceeded("WORK_STAGES", workStages),
        ];

        expect(httpMock.history.post[0].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(workStages[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchWorkStages(...filters)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGES"),
          receiveFailed("WORK_STAGES", err),
        ];

        expect(httpMock.history.post[1].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchWorkStages fetches all workable work stages for all filter", async () => {
      const filters = [true, [973], [1904], [102141]];
      const testBody = {
        job_ids: "973",
        package_ids: "1904",
        drawing_ids: "102141",
      };
      const err = testError("No workable stages were found");

      httpMock
        .onPost(`${process.env.REACT_APP_API}/work-stages/workable`)
        .replyOnce(200, workStages)
        .onPost(`${process.env.REACT_APP_API}/work-stages/workable`)
        .replyOnce(404, err);

      await store.dispatch(handleFetchWorkStages(...filters)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGES"),
          receiveSucceeded("WORK_STAGES", workStages),
        ];

        expect(httpMock.history.post[0].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(workStages[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchWorkStages(...filters)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGES"),
          receiveFailed("WORK_STAGES", err),
        ];

        expect(httpMock.history.post[1].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchWorkStages fetches all workable work stages for job filter", async () => {
      const filters = [true, [973]];
      const testBody = { job_ids: "973" };
      const err = testError("No workable stages were found");

      httpMock
        .onPost(`${process.env.REACT_APP_API}/work-stages/workable`)
        .replyOnce(200, workStages)
        .onPost(`${process.env.REACT_APP_API}/work-stages/workable`)
        .replyOnce(404, err);

      await store.dispatch(handleFetchWorkStages(...filters)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGES"),
          receiveSucceeded("WORK_STAGES", workStages),
        ];

        expect(httpMock.history.post[0].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(workStages[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchWorkStages(...filters)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGES"),
          receiveFailed("WORK_STAGES", err),
        ];

        expect(httpMock.history.post[1].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchWorkStagesGet fetches all work stages excluding shipping block", async () => {
      const err = testError("No work stages found.");

      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/work-stages?app_type=fab&exclude_shipping_blocks=1`
        )
        .replyOnce(200, workStages)
        .onGet(
          `${process.env.REACT_APP_API}/work-stages?app_type=fab&exclude_shipping_blocks=1`
        )
        .replyOnce(404, err);

      await store.dispatch(handleFetchWorkStagesGet()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGES_GET"),
          receiveSucceeded("WORK_STAGES_GET", workStages),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(workStages[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchWorkStagesGet()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGES_GET"),
          receiveFailed("WORK_STAGES_GET", err),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchFlows fetches all work flows", async () => {
      const err = testError("No workable stages were found");

      httpMock
        .onGet(`${process.env.REACT_APP_API}/work-flows`)
        .replyOnce(200, workFlows)
        .onGet(`${process.env.REACT_APP_API}/work-flows`)
        .replyOnce(404, err);

      await store.dispatch(handleFetchFlows()).then(() => {
        const receivedActions = store.getActions();
        const sortedWorkFlows = alphaNumericSortByName(workFlows);
        const expectedActions = [
          receiveStarted("FLOWS"),
          receiveSucceeded("FLOWS", sortedWorkFlows),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(sortedWorkFlows[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchFlows()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("FLOWS"),
          receiveFailed("FLOWS", err),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchFlowMap fetches work flow map for given flow id", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/work-flows/map-link?app_type=fab&id=1`
        )
        .replyOnce(200, workFlowMap);

      return store.dispatch(handleFetchFlowMap(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("FLOW_MAP"),
          receiveSucceeded("FLOW_MAP", workFlowMap[0]),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(workFlowMap[0]);
        store.clearActions();
      });
    });

    it("handleFetchWorkStageColumns fetches work item columns for given stages", async () => {
      const err = testError("No work item columns found for stage id(s): 1.");

      httpMock
        .onGet(`${process.env.REACT_APP_API}/work-stages/1/columns`)
        .replyOnce(200, workStageColumns)
        .onGet(`${process.env.REACT_APP_API}/work-stages/1/columns`)
        .replyOnce(404, err);

      await store.dispatch(handleFetchWorkStageColumns([1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGE_COLUMNS"),
          receiveSucceeded(
            "WORK_STAGE_COLUMNS",
            workStageColumns[0].work_item_columns
          ),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(
          workStageColumns[0].work_item_columns[0]
        );
        store.clearActions();
      });

      return store.dispatch(handleFetchWorkStageColumns([1])).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGE_COLUMNS"),
          receiveFailed("WORK_STAGE_COLUMNS", err.error),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleSaveFlowMap saves given flow map", async () => {
      const testBody = {
        app_type: "fab",
        data: flowMapPost,
      };
      const err = testError("No work-flow found");

      httpMock
        .onPost(`${process.env.REACT_APP_API}/work-flows/maps-links`)
        .replyOnce(200, workFlowMap)
        .onPost(`${process.env.REACT_APP_API}/work-flows/maps-links`)
        .replyOnce(404, err);

      await store.dispatch(handleSaveFlowMap(flowMapPost)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("FLOW_MAP"),
          receiveSucceeded("FLOW_MAP", workFlowMap[0]),
        ];

        expect(httpMock.history.post[0].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(workFlowMap[0]);
        store.clearActions();
      });

      return store.dispatch(handleSaveFlowMap(flowMapPost)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("FLOW_MAP"),
          receiveFailed("FLOW_MAP", err.error),
        ];

        expect(httpMock.history.post[1].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleCleanup cleans up redux state", async () => {
      store.dispatch(handleCleanup());

      const receivedActions = store.getActions();
      const expectedActions = [
        receiveSucceeded("FLOW_MAP", null),
        receiveSucceeded("WORK_STAGES", []),
      ];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });

    it("handleUpdateFlow updates flow based on given data", async () => {
      const err = testError("Work flows 1 not found.");

      const updatedFlow = {
        data: {
          name: "test fllow name",
        },
        id: 1,
      };

      httpMock
        .onPut(`${process.env.REACT_APP_API}/work-flows`)
        .replyOnce(200, workFlowMap)
        .onPut(`${process.env.REACT_APP_API}/work-flows`)
        .replyOnce(404, err);

      await store.dispatch(handleUpdateFlow(updatedFlow)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("UPDATED_FLOW"),
          receiveSucceeded("UPDATED_FLOW", workFlowMap[0]),
        ];

        expect(httpMock.history.put[0].data).toEqual(
          JSON.stringify(updatedFlow)
        );
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(workFlowMap[0]);
        store.clearActions();
      });

      return store.dispatch(handleUpdateFlow(updatedFlow)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("UPDATED_FLOW"),
          receiveFailed("UPDATED_FLOW", err.error),
        ];

        expect(httpMock.history.put[1].data).toEqual(
          JSON.stringify(updatedFlow)
        );
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchConditions fetches conditions", async () => {
      const err = testError("No conditions found");

      httpMock
        .onGet(`${process.env.REACT_APP_API}/conditions`)
        .replyOnce(200, conditionsFetch)
        .onGet(`${process.env.REACT_APP_API}/conditions`)
        .replyOnce(404, err);

      await store.dispatch(handleFetchConditions()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("CONDITIONS"),
          receiveSucceeded("CONDITIONS", conditionsFetch),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(conditionsFetch[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchConditions()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("CONDITIONS"),
          receiveFailed("CONDITIONS", err.error),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchWorkItemColumns fetches work item columns", async () => {
      const err = testError("No work item columns found");

      httpMock
        .onGet(`${process.env.REACT_APP_API}/columns?visible=1`)
        .replyOnce(200, workItemColumnFetch)
        .onGet(`${process.env.REACT_APP_API}/columns?visible=1`)
        .replyOnce(404, err);

      await store.dispatch(handleFetchWorkItemColumns()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_ITEM_COLUMNS"),
          receiveSucceeded("WORK_ITEM_COLUMNS", workItemColumnFetch),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(workItemColumnFetch[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchWorkItemColumns()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_ITEM_COLUMNS"),
          receiveFailed("WORK_ITEM_COLUMNS", err.error),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleSaveCondition saves coondition", async () => {
      const conditionObj = {
        comparison: "Does Not Contain",
        condition: "solder",
        filter: 36,
      };
      const testBody = {
        comparison: conditionObj.comparison,
        value: conditionObj.condition,
        work_item_column_id: conditionObj.filter,
      };

      httpMock
        .onPost(`${process.env.REACT_APP_API}/conditions`)
        .replyOnce(200, conditionsFetch)
        .onPost(`${process.env.REACT_APP_API}/conditions`)
        .replyOnce(404);

      await store.dispatch(handleSaveCondition(conditionObj)).then((res) => {
        expect(httpMock.history.post[0].data).toEqual(JSON.stringify(testBody));
        expect(res[0]).toEqual(conditionsFetch[0]);
        store.clearActions();
      });

      return store.dispatch(handleSaveCondition(conditionObj)).then((res) => {
        expect(httpMock.history.post[1].data).toEqual(JSON.stringify(testBody));
        // doesn't return anything if error
        expect(res).toEqual(undefined);
      });
    });

    it("handleFetchWorkStageConditions fetches conditions for given stage", async () => {
      const err = testError("No conditions found");

      httpMock
        .onGet(`${process.env.REACT_APP_API}/conditions/stage/1`)
        .replyOnce(200, stageConditionsFetch)
        .onGet(`${process.env.REACT_APP_API}/conditions/stage/1`)
        .replyOnce(404, err);

      await store.dispatch(handleFetchWorkStageConditions(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGE_CONDITIONS"),
          receiveSucceeded("WORK_STAGE_CONDITIONS", stageConditionsFetch),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(stageConditionsFetch[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchWorkStageConditions(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGE_CONDITIONS"),
          receiveFailed("WORK_STAGE_CONDITIONS", err.error),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleSaveWorkStageConditions saves conditions for given stage", async () => {
      const testBody = [
        {
          work_item_stage_id: 1,
          work_item_condition_id: 142,
          operand: "start",
          sub_group: 1,
          sub_group_operand: "start",
          sub_group_level: 1,
          grouping_level_1_id: 1,
          grouping_level_2_id: null,
          grouping_level_3_id: null,
          grouping_level_4_id: null,
          grouping_level_5_id: null,
          grouping_level_6_id: null,
        },
      ];
      const err = testError("No conditions found");

      httpMock
        .onPost(`${process.env.REACT_APP_API}/conditions/stage`)
        .replyOnce(200, stageConditionsFetch)
        .onPost(`${process.env.REACT_APP_API}/conditions/stage`)
        .replyOnce(404, err);

      await store
        .dispatch(handleSaveWorkStageConditions(1, conditionGroupPost))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [receiveStarted("WORK_STAGE_CONDITIONS")];
          expect(httpMock.history.post[0].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(receivedActions).toEqual(expectedActions);
          store.clearActions();
        });

      return store
        .dispatch(handleSaveWorkStageConditions(1, conditionGroupPost))
        .then((res) => {
          const expectedActions = [
            receiveFailed("WORK_STAGE_CONDITIONS", err.error),
          ];
          expect(httpMock.history.post[1].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(res.error).toEqual(expectedActions[0].payload);
        });
    });

    it("handleCreateWorkStage creates new stage", async () => {
      const stageInfo = {
        name: "Cut Table",
        fab: 1,
        metric: 1,
        nestable: 1,
        rejectable: 0,
        groupable: 0,
        columns: [1, 2, 3, 4, 5, 6, 7, 8, 9],
        selectedGroupableColumns: [],
        conditions: [],
      };
      const testBody = {
        app_type: "fab",
        callParams: {
          data: [
            {
              name: "Cut Table",
              stage_work_level_id: 1,
              shape: "Square",
              fab: 1,
              metric_work_item_column_id: 1,
              nestable: 1,
              rejectable: 0,
              groupable: 0,
            },
          ],
        },
      };
      const err = testError("Work stages 1 not found.");

      httpMock
        .onPost(`${process.env.REACT_APP_API}/work-stages`)
        .replyOnce(200, workStages)
        .onPost(`${process.env.REACT_APP_API}/work-stages`)
        .replyOnce(404, err);

      await store.dispatch(handleCreateWorkStage(stageInfo)).then((res) => {
        expect(httpMock.history.post[0].data).toEqual(JSON.stringify(testBody));
        expect(res[0]).toEqual(workStages[0]);
        store.clearActions();
      });

      return store.dispatch(handleCreateWorkStage(stageInfo)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [receiveFailed("WORK_STAGES", err.error)];

        expect(httpMock.history.post[1].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleCreateWorkStage creates new stage", async () => {
      const stageInfo = {
        name: "Cut Table",
        fab: 1,
        metric: 1,
        nestable: 1,
        rejectable: 0,
        groupable: 0,
        columns: [],
        selectedGroupableColumns: [],
        conditions: [],
      };
      const testBody = {
        app_type: "fab",
        callParams: {
          data: [
            {
              name: "Cut Table",
              stage_work_level_id: 1,
              shape: "Square",
              fab: 1,
              metric_work_item_column_id: 1,
              nestable: 1,
              rejectable: 0,
              groupable: 0,
            },
          ],
        },
      };
      const err = testError("Work stages 1 not found.");

      httpMock
        .onPost(`${process.env.REACT_APP_API}/work-stages`)
        .replyOnce(200, workStages)
        .onGet(
          `${process.env.REACT_APP_API}/work-stages?app_type=fab&exclude_shipping_blocks=1`
        )
        .replyOnce(200, workStages);
      httpMock
        .onPost(`${process.env.REACT_APP_API}/work-stages`)
        .replyOnce(404, err);

      await store.dispatch(handleCreateWorkStage(stageInfo)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORK_STAGES"),
          receiveStarted("WORK_STAGES_GET"),
          receiveSucceeded("WORK_STAGES_GET", workStages),
          receiveSucceeded("WORK_STAGES", workStages),
        ];

        expect(httpMock.history.post[0].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[2].payload[0]).toEqual(workStages[0]);
        expect(receivedActions[3].payload[0]).toEqual(workStages[0]);
        store.clearActions();
      });

      return store.dispatch(handleCreateWorkStage(stageInfo)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [receiveFailed("WORK_STAGES", err.error)];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleCreateWorkStage creates new stage", async () => {
      const stageInfo = {
        name: "Cut Table",
        fab: 1,
        metric: 1,
        nestable: 1,
        rejectable: 0,
        groupable: 0,
        columns: [1, 2, 3, 4, 5],
        selectedGroupableColumns: [2, 3],
        conditions: conditionGroupPost,
      };
      const testBody = {
        app_type: "fab",
        callParams: {
          data: [
            {
              name: "Cut Table",
              stage_work_level_id: 1,
              shape: "Square",
              fab: 1,
              metric_work_item_column_id: 1,
              nestable: 1,
              rejectable: 0,
              groupable: 0,
            },
          ],
        },
      };
      const err = testError("Work stages 1 not found.");

      httpMock
        .onPost(`${process.env.REACT_APP_API}/work-stages`)
        .replyOnce(200, workStages)
        .onPost(`${process.env.REACT_APP_API}/work-stages`)
        .replyOnce(404, err);

      await store.dispatch(handleCreateWorkStage(stageInfo)).then((res) => {
        const expectedActions = [
          receiveSucceeded("WORK_STAGES_GET", workStages),
        ];
        expect(httpMock.history.post[0].data).toEqual(JSON.stringify(testBody));
        expect(res).toEqual(expectedActions[0].payload);
        store.clearActions();
      });

      return store.dispatch(handleCreateWorkStage(stageInfo)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [receiveFailed("WORK_STAGES", err.error)];
        expect(httpMock.history.post[2].data).toEqual(JSON.stringify(testBody));
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchItemOpenConditions fetches item open conditions", async () => {
      const itemOpenConditions = [
        {
          id: 1,
          name: "Open",
          description: null,
        },
      ];
      const err = testError("Item open conditions not found");

      httpMock
        .onGet(`${process.env.REACT_APP_API}/work-flows/item-open-conditions`)
        .replyOnce(200, itemOpenConditions)
        .onGet(`${process.env.REACT_APP_API}/work-flows/item-open-conditions`)
        .replyOnce(404, err);

      await store.dispatch(handleFetchItemOpenConditions()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("ITEM_OPEN_CONDITIONS"),
          receiveSucceeded("ITEM_OPEN_CONDITIONS", itemOpenConditions),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(itemOpenConditions[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchItemOpenConditions()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("ITEM_OPEN_CONDITIONS"),
          receiveFailed("ITEM_OPEN_CONDITIONS", err.error),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleUpdateStageStatusGroup updates stage status group", async () => {
      const statusGroupCall = [1, 2, "test status group", "#000000"];
      const testBody = {
        app_type: "fab",
        work_stage_id: 1,
        work_flow_id: 2,
        stage_status_group_name: "test status group",
        stage_status_group_color: "#000000",
      };
      const err = testError("No work-flow found");

      httpMock
        .onPut(`${process.env.REACT_APP_API}/work-flows/stage-status-group`)
        .replyOnce(200, workFlowMap)
        .onPut(`${process.env.REACT_APP_API}/work-flows/stage-status-group`)
        .replyOnce(404, err);

      await store
        .dispatch(handleUpdateStageStatusGroup(...statusGroupCall))
        .then((res) => {
          expect(httpMock.history.put[0].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(res[0]).toEqual(workFlowMap[0]);
          store.clearActions();
        });

      return store
        .dispatch(handleUpdateStageStatusGroup(...statusGroupCall))
        .then((res) => {
          expect(httpMock.history.put[1].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(res).toEqual(err);
        });
    });

    it("handleUpdateWorkStage updates stage no metric or flow/stage code id", async () => {
      const stageInfo = {
        id: 1,
        name: "Cut Table",
        shape: "Square",
        nestable: 1,
        rejectable: 0,
        groupable: 0,
      };
      const testBody = {
        id: 1,
        data: {
          name: "Cut Table",
          shape: "Square",
          nestable: 1,
          rejectable: 0,
          groupable: 0,
        },
      };
      const err = testError("Work stages 1 not found.");

      httpMock
        .onPut(`${process.env.REACT_APP_API}/work-stages`)
        .replyOnce(200, workStages)
        .onPut(`${process.env.REACT_APP_API}/work-stages`)
        .replyOnce(404, err);

      await store.dispatch(handleUpdateWorkStage(stageInfo)).then((res) => {
        expect(httpMock.history.put[0].data).toEqual(JSON.stringify(testBody));
        expect(res[0]).toEqual(workStages[0]);
        store.clearActions();
      });

      return store.dispatch(handleUpdateWorkStage(stageInfo)).then((res) => {
        expect(httpMock.history.put[1].data).toEqual(JSON.stringify(testBody));
        expect(res).toEqual(err);
      });
    });

    it("handleUpdateWorkStage updates stage with metric and flow and stage code id", async () => {
      const stageInfo = {
        id: 1,
        name: "Cut Table",
        shape: "Square",
        nestable: 1,
        rejectable: 0,
        groupable: 0,
        metric: 5,
        flow_id: 1,
        stage_code_id: 6,
      };
      const testBody = {
        id: 1,
        data: {
          name: "Cut Table",
          shape: "Square",
          nestable: 1,
          rejectable: 0,
          groupable: 0,
          metric_work_item_column_id: 5,
          flow_id: 1,
          stage_code_id: 6,
        },
      };
      const err = testError("Work stages 1 not found.");

      httpMock
        .onPut(`${process.env.REACT_APP_API}/work-stages`)
        .replyOnce(200, workStages)
        .onPut(`${process.env.REACT_APP_API}/work-stages`)
        .replyOnce(404, err);

      await store.dispatch(handleUpdateWorkStage(stageInfo)).then((res) => {
        expect(httpMock.history.put[0].data).toEqual(JSON.stringify(testBody));
        expect(res[0]).toEqual(workStages[0]);
        store.clearActions();
      });

      return store.dispatch(handleUpdateWorkStage(stageInfo)).then((res) => {
        expect(httpMock.history.put[1].data).toEqual(JSON.stringify(testBody));
        expect(res).toEqual(err);
      });
    });

    it("handleCreateWorkFlow creates new work flow", async () => {
      const flowInfo = {
        name: "Test Flow",
        default: 0,
        flow_to_duplicate: false,
      };
      const testBody = {
        app_type: "fab",
        data: {
          name: "Test Flow",
          default: 0,
          fab: 1,
        },
      };
      const err = testError("Work flows 1 not found.");

      httpMock
        .onPost(`${process.env.REACT_APP_API}/work-flows`)
        .replyOnce(200, workFlows)
        .onPost(`${process.env.REACT_APP_API}/work-flows`)
        .replyOnce(404, err);

      await store.dispatch(handleCreateWorkFlow(flowInfo)).then((res) => {
        expect(httpMock.history.post[0].data).toEqual(JSON.stringify(testBody));
        expect(res[0]).toEqual(workFlows[0]);
        store.clearActions();
      });

      return store.dispatch(handleCreateWorkFlow(flowInfo)).then((res) => {
        expect(httpMock.history.post[1].data).toEqual(JSON.stringify(testBody));
        expect(res).toEqual(err);
      });
    });

    it("handleCreateWorkFlow duplicates work flow", async () => {
      const flowInfo = {
        name: "Test Flow Copy",
        default: 0,
        flow_to_duplicate: 1,
      };
      const testBody = {
        work_flow_id: 1,
        new_flow_name: "Test Flow Copy",
        default: 0,
      };
      const err = testError("Work flows 2 not found.");

      httpMock
        .onPost(`${process.env.REACT_APP_API}/work-flows/duplicate`)
        .replyOnce(200, workFlows)
        .onPost(`${process.env.REACT_APP_API}/work-flows/duplicate`)
        .replyOnce(404, err);

      await store.dispatch(handleCreateWorkFlow(flowInfo)).then((res) => {
        expect(httpMock.history.post[0].data).toEqual(JSON.stringify(testBody));
        expect(res[0]).toEqual(workFlows[0]);
        store.clearActions();
      });

      return store.dispatch(handleCreateWorkFlow(flowInfo)).then((res) => {
        expect(httpMock.history.post[1].data).toEqual(JSON.stringify(testBody));
        expect(res).toEqual(err);
      });
    });

    it("handleChangeItemWorkflow changes item workflow", async () => {
      const putInfo = [1, "drawing", 2];
      const testBody = {
        callParams: {
          drawing_id: 1,
          work_flow_id: 2,
        },
      };
      const failTestBody = {
        callParams: {
          drawing_id: "1,2",
          work_flow_id: 2,
        },
      };
      const success = {
        state: "success",
        message: "Drawing flow updated.",
      };
      const err = testError("You can only edit one drawing's flow at a time");

      httpMock
        .onPut(`${process.env.REACT_APP_API}/drawings/work-flow`)
        .replyOnce(200, success)
        .onPut(`${process.env.REACT_APP_API}/drawings/work-flow`)
        .replyOnce(400, err);

      await store.dispatch(handleChangeItemWorkflow(...putInfo)).then((res) => {
        expect(httpMock.history.put[0].data).toEqual(JSON.stringify(testBody));
        expect(res).toEqual(success);
        store.clearActions();
      });

      return store
        .dispatch(handleChangeItemWorkflow("1,2", "drawing", 2))
        .then((res) => {
          expect(httpMock.history.put[1].data).toEqual(
            JSON.stringify(failTestBody)
          );
          expect(res).toEqual(err);
        });
    });

    it("handleUpdateWorkStageColumns remove work stage columns", async () => {
      const putInfo = [
        0, // direction 0 or 1 to remove or add
        1, // stage id
        [
          {
            id: 5,
            display_name: "Package Number",
            name: "number",
            normal_name: "package_number",
            data_type: "string",
            visible_in_work_table: 1,
            table_effect_id: null,
            color: null,
            usable_for_conditions: 0,
            table_source: "Packages",
            editable: 1,
            groupable: 0,
            group_by: 0,
          },
        ], // work item column objects
        0, // group by
      ];
      const testBody = {
        app_type: "fab",
        updateData: [
          {
            stage_id: 1,
            group_by: 0,
            work_item_column_ids: "5",
          },
        ],
      };
      const err = testError("No work item columns found for stage id(s): 1.");

      httpMock
        .onPut(`${process.env.REACT_APP_API}/work-stages/columns/delete`)
        .replyOnce(200, workStageColumns)
        .onPut(`${process.env.REACT_APP_API}/work-stages/columns/delete`)
        .replyOnce(400, err);

      await store
        .dispatch(handleUpdateWorkStageColumns(...putInfo))
        .then((res) => {
          expect(httpMock.history.put[0].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(res[0]).toEqual(workStageColumns[0]);
          store.clearActions();
        });

      return store
        .dispatch(handleUpdateWorkStageColumns(...putInfo))
        .then((res) => {
          expect(httpMock.history.put[1].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(res).toEqual(err);
        });
    });

    it("handleUpdateWorkStageColumns add group work stage columns", async () => {
      const putInfo = [
        1, // direction 0 or 1 to remove or add
        1, // stage id
        [4], // work item column ids
        1, // group by
        "EDIT",
        [
          {
            column_id: 4,
            position: 1,
          },
        ],
      ];
      const testBody = {
        app_type: "fab",
        updateData: [
          {
            stage_id: 1,
            group_by: 1,
            data: [
              {
                column_id: 4,
                position: 1,
              },
            ],
          },
        ],
      };
      const err = testError("No work item columns found for stage id(s): 1.");

      httpMock
        .onPut(`${process.env.REACT_APP_API}/work-stages/columns`)
        .replyOnce(200, workStageColumns)
        .onPut(`${process.env.REACT_APP_API}/work-stages/columns`)
        .replyOnce(400, err);

      await store
        .dispatch(handleUpdateWorkStageColumns(...putInfo))
        .then((res) => {
          expect(httpMock.history.put[0].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(res[0]).toEqual(workStageColumns[0]);
          store.clearActions();
        });

      return store
        .dispatch(handleUpdateWorkStageColumns(...putInfo))
        .then((res) => {
          expect(httpMock.history.put[1].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(res).toEqual(err);
        });
    });

    it("handleResetFlowsReducer resets flows work stage columns", async () => {
      store.dispatch(handleResetFlowsReducer());

      const receivedActions = store.getActions();
      const expectedActions = [receiveSucceeded("WORK_STAGE_COLUMNS", [])];

      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });

    it("handleFetchShippingBlocks fetches shipping blocks", async () => {
      const err = testError("No shipping blocks found.");

      httpMock
        .onGet(`${process.env.REACT_APP_API}/shipping-blocks`)
        .replyOnce(200, shippingBlocks)
        .onGet(`${process.env.REACT_APP_API}/shipping-blocks`)
        .replyOnce(404, err);

      await store.dispatch(handleFetchShippingBlocks()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("SHIPPING_BLOCKS"),
          receiveSucceeded("SHIPPING_BLOCKS", shippingBlocks),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload[0]).toEqual(shippingBlocks[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchShippingBlocks()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("SHIPPING_BLOCKS"),
          receiveFailed("SHIPPING_BLOCKS", err.error),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleDeleteCondition delete condition", async () => {
      const testBody = {
        condition_id: 1,
        call_action: "delete",
      };
      const err = testError("No conditions found");

      httpMock
        .onPut(`${process.env.REACT_APP_API}/conditions`)
        .replyOnce(200, conditionsFetch)
        .onPut(`${process.env.REACT_APP_API}/conditions`)
        .replyOnce(400, err);

      await store.dispatch(handleDeleteCondition(1)).then((res) => {
        expect(httpMock.history.put[0].data).toEqual(JSON.stringify(testBody));
        expect(res[0]).toEqual(conditionsFetch[0]);
        store.clearActions();
      });

      return store.dispatch(handleDeleteCondition(1)).then((res) => {
        expect(httpMock.history.put[1].data).toEqual(JSON.stringify(testBody));
        expect(res).toEqual(err);
      });
    });

    it("handleFetchFlowCodes fetches flow codes", async () => {
      const err = testError("No flow codes found");

      httpMock
        .onGet(`${process.env.REACT_APP_API}/flow-codes`)
        .replyOnce(200, flowCodes)
        .onGet(`${process.env.REACT_APP_API}/flow-codes`)
        .replyOnce(404, err);

      await store.dispatch(handleFetchFlowCodes()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [receiveSucceeded("FLOW_CODES", flowCodes)];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload[0]).toEqual(flowCodes[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchFlowCodes()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [receiveFailed("FLOW_CODES", err.error)];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchWorkStageGroupableColumns groupable columns for stage", async () => {
      const err = testError("No work item columns found for stage id(s): 1.");

      httpMock
        .onGet(`${process.env.REACT_APP_API}/work-stages/1/columns?groupable=1`)
        .replyOnce(200, workStageColumns)
        .onGet(`${process.env.REACT_APP_API}/work-stages/1/columns?groupable=1`)
        .replyOnce(404, err);

      await store.dispatch(handleFetchWorkStageGroupableColumns(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WS_GROUPABLE_COLUMNS_OPTIONS"),
          receiveSucceeded(
            "WS_GROUPABLE_COLUMNS_OPTIONS",
            workStageColumns[0].work_item_columns
          ),
        ];

        expect(receivedActions).toEqual(expectedActions);
        store.clearActions();
      });

      return store
        .dispatch(handleFetchWorkStageGroupableColumns(1))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("WS_GROUPABLE_COLUMNS_OPTIONS"),
            receiveFailed("WS_GROUPABLE_COLUMNS_OPTIONS", err.error),
          ];

          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleFetchWorkStageGroupableColumns groupable columns for stage with group by 1", async () => {
      const err = testError("No work item columns found for stage id(s): 1.");

      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/work-stages/1/columns?groupable=1&group_by=1`
        )
        .replyOnce(200, workStageColumns)
        .onGet(
          `${process.env.REACT_APP_API}/work-stages/1/columns?groupable=1&group_by=1`
        )
        .replyOnce(404, err);

      await store
        .dispatch(handleFetchWorkStageGroupableColumns(1, true))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("WS_GROUPABLE_COLUMNS"),
            receiveSucceeded(
              "WS_GROUPABLE_COLUMNS",
              workStageColumns[0].work_item_columns
            ),
          ];

          expect(receivedActions).toEqual(expectedActions);
          store.clearActions();
        });

      return store
        .dispatch(handleFetchWorkStageGroupableColumns(1, true))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("WS_GROUPABLE_COLUMNS"),
            receiveFailed("WS_GROUPABLE_COLUMNS", err.error),
          ];

          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleCreateFlowCode create flow code", async () => {
      const testBody = {
        flow_code_name: "test flow code",
      };
      const err = testError("flow codes 1 not found");

      httpMock
        .onPost(`${process.env.REACT_APP_API}/flow-codes`)
        .replyOnce(200, flowCodes)
        .onPost(`${process.env.REACT_APP_API}/flow-codes`)
        .replyOnce(400, err);

      await store
        .dispatch(handleCreateFlowCode("test flow code"))
        .then((res) => {
          expect(httpMock.history.post[0].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(res[0]).toEqual(flowCodes[0]);
          store.clearActions();
        });

      return store
        .dispatch(handleCreateFlowCode("test flow code"))
        .then((res) => {
          expect(httpMock.history.post[1].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(res).toEqual(err);
        });
    });

    it("handleFetchStageCodes fetches stage codes", async () => {
      const err = testError("No stage codes found");

      httpMock
        .onGet(`${process.env.REACT_APP_API}/stage-codes`)
        .replyOnce(200, stageCodes)
        .onGet(`${process.env.REACT_APP_API}/stage-codes`)
        .replyOnce(404, err);

      await store.dispatch(handleFetchStageCodes()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [receiveSucceeded("STAGE_CODES", stageCodes)];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload[0]).toEqual(stageCodes[0]);
        store.clearActions();
      });

      return store.dispatch(handleFetchStageCodes()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [receiveFailed("STAGE_CODES", err.error)];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleCreateStageCode creates stage code", async () => {
      const testBody = {
        stage_code_name: "test stage code",
      };
      const err = testError("stage codes 1 not found");

      httpMock
        .onPost(`${process.env.REACT_APP_API}/stage-codes`)
        .replyOnce(200, stageCodes)
        .onPost(`${process.env.REACT_APP_API}/stage-codes`)
        .replyOnce(400, err);

      await store
        .dispatch(handleCreateStageCode("test stage code"))
        .then((res) => {
          expect(httpMock.history.post[0].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(res[0]).toEqual(stageCodes[0]);
          store.clearActions();
        });

      return store
        .dispatch(handleCreateStageCode("test stage code"))
        .then((res) => {
          expect(httpMock.history.post[1].data).toEqual(
            JSON.stringify(testBody)
          );
          expect(res).toEqual(err);
        });
    });

    it("handleDeleteFlow deletes flow", async () => {
      const testBody = {
        work_flow_id: 1,
      };
      const success = {
        state: "success",
        message: "Work flow deleted.",
      };
      const err = testError("No valid work flow supplied");

      httpMock
        .onPut(`${process.env.REACT_APP_API}/work-flows/delete`)
        .replyOnce(200, success)
        .onPut(`${process.env.REACT_APP_API}/work-flows/delete`)
        .replyOnce(500, err);

      await store.dispatch(handleDeleteFlow(1)).then((res) => {
        expect(httpMock.history.put[0].data).toEqual(JSON.stringify(testBody));
        expect(res).toEqual(success);
        store.clearActions();
      });

      return store.dispatch(handleDeleteFlow(1)).then((res) => {
        expect(httpMock.history.put[1].data).toEqual(JSON.stringify(testBody));
        expect(res).toEqual(err);
      });
    });
  });
});
