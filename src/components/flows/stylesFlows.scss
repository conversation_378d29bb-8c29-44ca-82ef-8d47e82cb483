@import "../styles/colors.scss";

div.all-flows-wrapper {
  height: calc(100vh - 33px - 100px);

  &::-webkit-scrollbar {
    width: 10px;
    background-color: #f5f5f5;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #555;
  }
  overflow-y: auto;

  & > div.card-wrapper {
    display: flex;
    flex-wrap: wrap;
    padding: 20px;
  }

  & div.flow-card {
    height: 140px;
    width: 120px;
    margin: 5px;
    border: 2px solid $blue;
    border-radius: 5px;
    font-size: 0.8rem;
    color: white;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;

    &:hover {
      background-color: $lighterGrey;
    }

    & .flow-screenshot {
      display: grid;
      justify-content: center;
      align-items: center;
      height: 60%;
      font-size: 4rem;
    }

    & .flow-name {
      height: 80px;
      background-color: $blue;
      max-width: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0;
      padding: 5px;
      text-align: center;
      word-break: break-all;
    }

    & > svg.default-flow-indicator {
      position: absolute;
      top: 5px;
      right: 5px;

      font-size: 1.2rem;
      color: $fieldProOrange;
    }
  }

  & div.new-flow-card {
    height: 140px;
    width: 124px;
    margin: 5px;
    cursor: pointer;
    color: #fff;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    & > svg {
      font-size: 4rem;
    }

    & > p.title {
      font-size: 0.8rem;
    }

    &:hover > svg {
      color: $fabProBlue;
    }
  }
}

div.flows-wrapper {
  height: calc(100vh - 33px - 100px);
  width: calc(100vw - 55px);

  display: grid;
  grid-template-columns: 250px 1fr;
  grid-template-rows: 45px 1fr;

  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  & div.flows-toolbar-wrapper {
    grid-column: 1/-1;
    grid-row: 1/2;
    width: calc(100vw - 55px);
    background-color: $darkFab;
  }

  & div.sidebar {
    width: 250px;
    height: calc(100vh - 33px - 108px - 55px) !important;
    background-color: $offGrey;
    padding-bottom: 10px;

    display: grid;
    grid-template-rows: 1fr 30px;

    grid-column: 1/2;
    grid-row: 2/-1;

    & div.flows-sidebar-wrapper {
      height: calc(100vh - 33px - 120px - 65px) !important;
      background-color: $darkFab;

      & div.collapsible-list-wrapper {
        display: grid;
        grid-template-rows: 30px calc(100vh - 33px - 100px - 300px - 50px) 30px;
        height: calc(100vh - 33px - 100px - 300px - 50px) !important;

        & > header.header {
          padding: 0 10px;
          display: flex;
          align-items: center;
        }

        & div.collapsible-list-content {
          display: flex;
          flex-direction: column;
          padding-top: 0;
        }

        & > button {
          margin: 5px auto 0;
        }
      }

      & .sidebar-stage-card {
        border-radius: 3px;
      }
    }

    &.create-locked {
      & div.flows-sidebar-wrapper {
        height: calc(100vh - 33px - 120px - 40px) !important;

        & div.collapsible-list-wrapper {
          display: grid;
          grid-template-rows: 30px calc(100vh - 375px) 30px;
          height: calc(100vh - 375px) !important;

          & div.collapsible-list-content {
            display: flex;
            flex-direction: column;
          }
        }
      }
    }

    & h3,
    & svg,
    & p {
      color: $blue;
    }
    & p {
      font-size: 0.8rem;
      font-weight: bold;
      color: $lighterSlate;
    }

    & button.manage-conditions-button {
      height: 30px;
      padding: 0;
      width: calc(100% - 20px);
      margin: 0 auto;
      font-size: 0.8rem;
      background-color: $fabProBlue;
      color: #fff;
      transition: none;

      &:hover {
        background-color: darken($fabProBlue, 10%);
      }
    }
  }
}

div.status-groups-canvas-wrapper {
  position: absolute;
  top: 0;
  left: 0;
}

canvas#status-groups-canvas {
  position: fixed;
}

div.link-color-legend {
  position: fixed;
  top: 135px;
  right: 5px;
  z-index: 5;

  display: grid;
  grid-template-columns: 50px 1fr;
  grid-template-rows: repeat(5, 15px);
  row-gap: 5px;
  column-gap: 5px;
  align-items: center;

  font-size: 0.7rem;
  background-color: #eee;
  padding: 10px;
  border: 1px solid #333;
  color: #333;

  & h2.title {
    margin: 0;
    padding: 0;
    grid-column: 1/-1;
    font-size: 0.8rem;
    text-align: center;
  }

  & > span.color {
    grid-column: 1/2;
    display: block;
    height: 15px;
    width: 50px;
    border: 1px solid #333;

    &.green {
      background-color: $green;
    }

    &.yellow {
      background-color: $yellow;
    }

    &.orange {
      background-color: $orange;
    }

    &.red {
      background-color: $red;
    }
  }

  & > span.name {
    grid-column: 2/-1;
  }
}

div.flow-creator circle {
  display: none;
}

div.manage-conditions-modal {
  display: grid;
  grid-template-rows: 30px 1fr;
  width: 600px;

  & > h2.title {
    background-color: $fabProBlue;
    color: #fff;
    margin: 0;
    padding: 5px;
    font-size: 1rem;
    border-bottom: 1px solid #333;
  }

  & > div.content {
    background-color: #fff;
    max-height: 400px;
    padding-bottom: 10px;
  }
}

div.edit-condition-group {
  padding: 10px;
  height: 100%;
  box-sizing: border-box;

  display: grid;
  grid-template-rows: 30px 1fr 30px;
  row-gap: 10px;

  & > div.toggle {
    display: flex;
    column-gap: 10px;

    color: #040404;
  }

  & > div.conditions-list {
    display: flex;
    flex-direction: column;
    row-gap: 10px;
  }
}

div.conditions-display {
  display: grid;
  grid-template-rows: 30px 1fr;
  column-gap: 5px;
  row-gap: 10px;

  & > div input.search-input {
    grid-column: 1/2;
    height: 30px;
    font-size: 1rem;
    margin-top: 10px;
    margin-left: 10px;
    width: calc(100% - 20px);
  }

  & > div.conditions-container {
    overflow-y: scroll;
    height: 360px;

    &::-webkit-scrollbar {
      width: 10px;
      background-color: #f5f5f5;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background-color: #555;
    }

    & > div.condition-line {
      height: 40px;
      line-height: 40px;
      grid-column: 1/2;
      padding: 0 10px;
      border-bottom: 1px solid #333;

      display: flex;
      justify-content: space-between;
      align-items: center;

      &.odd {
        background-color: #eee;
      }

      & > svg {
        cursor: pointer;
        color: darken($red, 10%);
        background-color: #fff;
        border: 1px solid #333;
        padding: 5px;
        border-radius: 3px;

        &:hover {
          color: $red;
        }
      }
    }

    & > div.condition {
      display: grid;
      grid-template-columns: repeat(3, 1fr) 30px;
      column-gap: 5px;
      padding: 10px;
      border-bottom: 1px solid #333;

      & > div:not(.plus-minus),
      input {
        height: 30px;
        font-size: 1rem;

        & > div {
          height: 30px;

          & > select {
            height: 30px;
            font-size: 1rem;
          }
        }
      }

      & > button.save {
        height: 30px;
        padding: 2px 0 0;
        border: 1px solid #333;
        background-color: $fabProBlue;
        color: #fff;
        grid-column: 4/-1;

        &:not(:disabled):hover {
          background-color: darken($fabProBlue, 10%);
        }
      }
    }

    & > p.no-conditions {
      display: flex;
      justify-content: center;
      align-items: center;
      height: calc(100% - 100px);
    }
  }

  & > div.actions-container {
    grid-row: 1/-1;
    grid-column: 2/3;
    padding-top: 10px;

    display: flex;
    flex-direction: column;
    row-gap: 10px;

    & > button {
      width: 35px;
      height: 35px;
      padding: 0;
      font-size: 42px;
      border: 1px solid #333;
      border-radius: 5px;
      transition: none;

      display: flex;
      align-items: center;
      justify-content: center;

      &.new {
        color: $lightGreen;

        &:not(.creating):hover {
          color: darken($lightGreen, 10%);
        }

        &.creating {
          background-color: darken($red, 10%);
          color: #fff;

          &:hover {
            background-color: $red;
          }
        }
      }

      &.edit {
        color: $fabProBlue;

        &:not(:disabled):not(.editing):hover {
          color: darken($fabProBlue, 10%);
        }

        &.editing {
          background-color: darken($red, 10%);
          color: #fff;

          &:hover {
            background-color: $red;
          }
        }
      }
    }
  }
}

div.delete-flow-confirmation,
div.delete-stage-confirmation,
div.save-flow-confirmation {
  display: grid;
  grid-template-rows: 30px 1fr;

  width: 400px;
  background-color: #fff;

  & > h2.title {
    font-size: 0.8rem;
    background-color: $fabProBlue;
    color: #fff;
    margin: 0;
    padding: 0 10px;
    line-height: 30px;
  }

  & > div.content {
    padding: 10px;

    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 10px;
    justify-items: center;
  }

  & > div.content p {
    text-align: center;
    grid-column: 1/-1;
  }

  & > div.content p span {
    color: $fabProBlue;
    font-weight: 600;
  }

  & > div.content button {
    padding: 0 10px;
    font-size: 0.8rem;
    height: 30px;
    width: 100px;
    border: 1px solid #333;

    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  & > div.content button.cancel {
    background-color: #fff;
    color: #333;

    &:hover {
      background-color: darken(#fff, 10%);
    }
  }

  & > div.content button.submit {
    background-color: $green;
    color: #fff;

    &:hover {
      background-color: darken($green, 10%);
    }
  }
}
