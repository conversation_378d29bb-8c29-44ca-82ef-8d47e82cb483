{"en-US": {"Flows": "Flows", "Manage Conditions": "Manage Conditions", "Save": "Save", "-- Column --": "-- Column --", "-- Comparison --": "-- Comparison --", "-- Condition --": "-- Condition --", "Contains": "Contains", "Does Not Contain": "Does Not Contain", "Greater Than": "Greater Than", "Less Than": "Less Than", "Greater Than or Equal To": "Greater Than or Equal To", "Less Than or Equal To": "Less Than or Equal To", "-- Group Operand --": "-- Group Operand --", "-- Operand --": "-- Operand --", "< Group Operand": "< Group Operand", "Create New Stage:": "Create New Stage:", "Name:": "Name:", "Duplicate from existing stage:": "Duplicate from existing stage:", "-- Name --": "-- Name --", "-- Existing Stages --": "-- Existing Stages --", "Add existing conditions:": "Add existing conditions:", "Create new condition:": "Create new condition:", "Holdpoint?": "Holdpoint?", "New Flow": "New Flow", "Create New Flow:": "Create New Flow:", "Default Flow?": "Default Flow?", "Remove from flow": "Remove from flow", "Metric:": "Metric:", "-- Selected Columns --": "-- Selected Columns --", "Changes won't take effect until the work flow is saved!": "Changes won't take effect until the work flow is saved!", "-- Open Condition --": "-- Open Condition --", "Open": "Open", "Specific Item Open": "Specific Item Open", "Locked - First Item": "Locked - First Item", "Locked - Last Item": "Locked - <PERSON> Item", "Nestable?": "Nestable?", "Rejectable?": "Rejectable?", "Groupable?": "Groupable?", "Columns To Group By": "Columns To Group By", "Flow Specific Cost Code:": "Flow Specific Cost Code:", "Flow Start?": "Flow Start?", "Duplicate From:": "Duplicate From:", "Equal To": "Equal To", "Not Equal To": "Not Equal To", "Is Blank": "Is Blank", "Is Not Blank": "Is Not Blank"}}