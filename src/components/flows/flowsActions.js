import {
  fetchWorkStages,
  fetchWorkStagesGet,
  fetchFlows,
  fetchWorkStageColumns,
  fetchFlowMap,
  saveFlowMap,
  saveFlow,
  fetchConditions,
  fetchWorkItemColumns,
  saveCondition,
  fetchWorkStageConditions,
  saveWorkStageConditions,
  createWorkStage,
  fetchItemOpenConditions,
  updateStageStatusGroup,
  updateWorkStage,
  createWorkFlow,
  changeItemWorkflow,
  updateWorkStageColumns,
  fetchShippingBlocks,
  updateCondition,
  fetchFlowCodes,
  createFlowCode,
  fetchWorkStageGroupableColumns,
  fetchStageCodes,
  createStageCode,
  deleteFlow,
  deleteStage,
  duplicateStage,
} from "../../_services";
import { alphaNumericSortByName } from "../../_utils";

export const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const handleFetchWorkStages = (
  workable,
  jobIds,
  packageIds,
  drawingIds,
  flowId
) => (dispatch) => {
  dispatch(receiveStarted("WORK_STAGES"));
  return fetchWorkStages(workable, jobIds, packageIds, drawingIds, flowId).then(
    (res) => {
      if (res.error) dispatch(receiveFailed("WORK_STAGES", res));
      else dispatch(receiveSucceeded("WORK_STAGES", res));

      return res;
    }
  );
};

export const handleFetchWorkStagesGet = () => (dispatch) => {
  dispatch(receiveStarted("WORK_STAGES_GET"));
  return fetchWorkStagesGet().then((res) => {
    if (res.error) dispatch(receiveFailed("WORK_STAGES_GET", res));
    else dispatch(receiveSucceeded("WORK_STAGES_GET", res));

    return res;
  });
};

export const handleFetchFlows = () => (dispatch) => {
  dispatch(receiveStarted("FLOWS"));
  return fetchFlows().then((res) => {
    if (res.error) {
      dispatch(receiveFailed("FLOWS", res));
      return res;
    }
    const sorted = alphaNumericSortByName(res);

    dispatch(receiveSucceeded("FLOWS", sorted));
    return sorted;
  });
};

export const handleFetchFlowMap = (flowId) => (dispatch) => {
  const type = "FLOW_MAP";

  dispatch(receiveStarted(type));
  return fetchFlowMap(flowId).then((res) => {
    if (res.error) {
      dispatch(receiveFailed(type, res.error));
    } else {
      dispatch(receiveSucceeded(type, res[0]));
    }

    return res;
  });
};

// Array.flat not supported when running tests, this is a polyfill for it
// Remove once support exists
Object.defineProperty(Array.prototype, "flat", {
  value: function (depth = 1) {
    return this.reduce(function (flat, toFlatten) {
      return flat.concat(
        Array.isArray(toFlatten) && depth > 1
          ? toFlatten.flat(depth - 1)
          : toFlatten
      );
    }, []);
  },
});

export const handleFetchWorkStageColumns = (stageIds, includeCustom) => (
  dispatch
) => {
  dispatch(receiveStarted("WORK_STAGE_COLUMNS"));
  return fetchWorkStageColumns(stageIds, includeCustom).then((res) => {
    if (res.error) {
      dispatch(receiveFailed("WORK_STAGE_COLUMNS", res.error));
    } else {
      // combine columns from each selected stage
      let columns = res
        .map(
          (stage) =>
            stage.work_item_columns &&
            stage.work_item_columns.length &&
            stage.work_item_columns
        )
        .flat();

      // filter out duplicates
      const colIds = [];
      columns = columns.filter((col) => {
        if (!col || colIds.includes(col.id)) return false;
        colIds.push(col.id);
        return true;
      });
      dispatch(receiveSucceeded("WORK_STAGE_COLUMNS", columns));
    }

    return res;
  });
};

export const handleSaveFlowMap = (newFlowMap) => (dispatch) => {
  const type = "FLOW_MAP";

  dispatch(receiveStarted(type));
  return saveFlowMap(newFlowMap).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res[0]));

    return res;
  });
};

export const handleCleanup = () => (dispatch) => {
  dispatch(receiveSucceeded("FLOW_MAP", null));
  dispatch(receiveSucceeded("WORK_STAGES", []));
};

export const handleUpdateFlow = (updatedFlow) => (dispatch) => {
  const type = "UPDATED_FLOW";

  dispatch(receiveStarted(type));
  return saveFlow(updatedFlow).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res[0]));

    return res;
  });
};

export const handleFetchConditions = () => (dispatch) => {
  const type = "CONDITIONS";

  dispatch(receiveStarted(type));
  return fetchConditions().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchWorkItemColumns = (tableTarget = null) => (
  dispatch
) => {
  const type = "WORK_ITEM_COLUMNS";

  dispatch(receiveStarted(type));
  return fetchWorkItemColumns(true, tableTarget).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleSaveCondition = (conditionObj) => (dispatch) => {
  return saveCondition(conditionObj).then((res) => {
    if (!res.error) {
      return res;
    }
  });
};

export const handleFetchWorkStageConditions = (workStageId) => (dispatch) => {
  const type = "WORK_STAGE_CONDITIONS";

  dispatch(receiveStarted(type));
  return fetchWorkStageConditions(workStageId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleClearWorkStageConditions = (dispatch) => {
  const type = "WORK_STAGE_CONDITIONS";

  dispatch(receiveSucceeded(type, []));
};

const createConditionsMap = (stageId, conditions, group) => {
  let result = [];

  result.push(
    ...conditions
      .filter((c) => !!c.work_item_condition_id)
      .map((c, idx) => ({
        work_item_stage_id: stageId,
        work_item_condition_id: c.work_item_condition_id,
        operand:
          idx === 0 && !conditions.find((c) => c.operand === "start")
            ? "start"
            : c.operand,
        sub_group: c.sub_group,
        sub_group_operand: group.sub_group_operand,
        sub_group_level: c.sub_group_level,
        grouping_level_1_id: c.grouping_level_1_id ?? null,
        grouping_level_2_id: c.grouping_level_2_id ?? null,
        grouping_level_3_id: c.grouping_level_3_id ?? null,
        grouping_level_4_id: c.grouping_level_4_id ?? null,
        grouping_level_5_id: c.grouping_level_5_id ?? null,
        grouping_level_6_id: c.grouping_level_6_id ?? null,
      }))
  );
  if (conditions.filter((c) => !c.work_item_condition_id).length) {
    conditions
      .filter((c) => !c.work_item_condition_id)
      .forEach((c) => {
        const isGroupOfOnlyGroups =
          group.conditions &&
          group.conditions.every((o) => !o.work_item_condition_id);
        if (c.conditions) {
          if (isGroupOfOnlyGroups) {
            result.push({
              work_item_stage_id: stageId,
              work_item_condition_id: null,
              operand: null,
              sub_group: group.sub_group,
              sub_group_operand: group.sub_group_operand,
              sub_group_level: group.sub_group_level,
              grouping_level_1_id: group.grouping_level_1_id ?? null,
              grouping_level_2_id: group.grouping_level_2_id ?? null,
              grouping_level_3_id: group.grouping_level_3_id ?? null,
              grouping_level_4_id: group.grouping_level_4_id ?? null,
              grouping_level_5_id: group.grouping_level_5_id ?? null,
              grouping_level_6_id: group.grouping_level_6_id ?? null,
              [`grouping_level_${group.sub_group_level}_id`]: group.id,
            });
          }
          result = result.concat(createConditionsMap(stageId, c.conditions, c));
        }
      });
  }
  return result;
};

export const handleSaveWorkStageConditions = (stageId, conditionGroups) => (
  dispatch
) => {
  let formattedConditions = [];
  conditionGroups.forEach((c) => {
    if (c.conditions) {
      formattedConditions.push(
        ...createConditionsMap(stageId, c.conditions, c)
      );
    }
  });

  if (!formattedConditions.length) {
    formattedConditions.push({ work_item_stage_id: stageId });
  }
  return saveWorkStageConditions(formattedConditions).then((res) => {
    if (!res.error) {
      dispatch(handleFetchWorkStageConditions(stageId));
    }

    return res;
  });
};

export const handleCreateWorkStage = (stageInfo) => (dispatch) => {
  const type = "WORK_STAGES";

  return createWorkStage(stageInfo).then(async (res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else {
      if (stageInfo.columns.length) {
        let columnToPositionMap = stageInfo.columns.map((o, i) => ({
          column_id: o.id,
          position: i + 1,
        }));
        await dispatch(
          handleUpdateWorkStageColumns(
            1,
            res[0].id,
            stageInfo.columns,
            undefined,
            "EDIT",
            columnToPositionMap
          )
        );
      }

      if (stageInfo.selectedGroupableColumns.length) {
        let columnToPositionMap = stageInfo.selectedGroupableColumns.map(
          (o, i) => ({
            column_id: o.id,
            position: i + 1,
          })
        );
        await dispatch(
          handleUpdateWorkStageColumns(
            1,
            res[0].id,
            stageInfo.selectedGroupableColumns,
            1,
            "CREATE",
            columnToPositionMap
          )
        );
      }

      if (stageInfo?.conditions?.length) {
        await dispatch(
          handleSaveWorkStageConditions(res[0].id, stageInfo.conditions)
        );
      }

      if (!stageInfo?.conditions?.length && !stageInfo?.columns?.length) {
        if (res.error) dispatch(receiveFailed(type, res.error));
        else {
          dispatch(receiveStarted(type));
          return dispatch(handleFetchWorkStagesGet()).then((res) => {
            if (res.error) dispatch(receiveFailed(type, res.error));
            else {
              dispatch(receiveSucceeded(type, res));
            }

            return res;
          });
        }
      }
    }

    return res;
  });
};

export const handleFetchItemOpenConditions = () => (dispatch) => {
  const type = "ITEM_OPEN_CONDITIONS";

  dispatch(receiveStarted(type));
  return fetchItemOpenConditions().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleUpdateStageStatusGroup = (
  workStageId,
  workFlowId,
  statusGroupName,
  statusGroupColor
) => (dispatch) => {
  return updateStageStatusGroup(
    workStageId,
    workFlowId,
    statusGroupName,
    statusGroupColor
  );
};

export const handleUpdateWorkStage = (stageInfo) => (dispatch) => {
  return updateWorkStage(stageInfo);
};

export const handleCreateWorkFlow = (flowInfo) => (dispatch) => {
  return createWorkFlow(flowInfo).then((res) => {
    if (!res.error) {
      dispatch(handleFetchFlows());
    }
    return res;
  });
};

export const handleChangeItemWorkflow = (itemId, itemType, newWorkflow) => (
  dispatch
) => {
  return changeItemWorkflow(itemId, itemType, newWorkflow);
};

export const handleUpdateWorkStageColumns = (
  direction,
  stageId,
  workItemColumnIds, // can be ids or column objects
  groupBy,
  origin = "EDIT",
  columnToPositionMap
) => (dispatch) => {
  return updateWorkStageColumns(
    direction,
    stageId,
    workItemColumnIds,
    groupBy,
    origin,
    columnToPositionMap
  );
};

export const handleResetFlowsReducer = () => (dispatch) => {
  dispatch(receiveSucceeded("WORK_STAGE_COLUMNS", []));
};

export const handleFetchShippingBlocks = () => (dispatch) => {
  const type = "SHIPPING_BLOCKS";

  dispatch(receiveStarted(type));
  return fetchShippingBlocks().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleDeleteCondition = (conditionId) => (dispatch) => {
  return updateCondition(conditionId, "delete").then((res) => {
    if (!res.error) {
      dispatch(handleFetchConditions());
    }

    return res;
  });
};

export const handleFetchFlowCodes = () => (dispatch) => {
  const type = "FLOW_CODES";

  return fetchFlowCodes().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleClearWorkStageGroupableColumns = (dispatch) => {
  const type = "WS_GROUPABLE_COLUMNS";

  dispatch(receiveStarted(type));
};

export const handleFetchWorkStageGroupableColumns = (
  stageId,
  groupBy = false
) => (dispatch) => {
  const type = groupBy
    ? "WS_GROUPABLE_COLUMNS"
    : "WS_GROUPABLE_COLUMNS_OPTIONS";

  dispatch(receiveStarted(type));
  return fetchWorkStageGroupableColumns(stageId, groupBy).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else
      dispatch(
        receiveSucceeded(type, res[0] ? res[0].work_item_columns || [] : [])
      );

    return res;
  });
};

export const handleCreateFlowCode = (name) => (dispatch) => {
  return createFlowCode(name);
};

export const handleFetchStageCodes = () => (dispatch) => {
  const type = "STAGE_CODES";

  return fetchStageCodes().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleCreateStageCode = (name) => (dispatch) => {
  return createStageCode(name);
};

export const handleDeleteFlow = (flowId) => (dispatch) => {
  return deleteFlow(flowId);
};

export const handleDeleteStage = (stageId) => (dispatch) => {
  return deleteStage(stageId);
};

export const handleDuplicateStage = (stageId, newStageName) => (dispatch) => {
  return duplicateStage(stageId, newStageName).then((res) => {
    return res;
  });
};
