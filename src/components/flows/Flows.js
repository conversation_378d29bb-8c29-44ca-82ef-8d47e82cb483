// NPM PACKAGE IMPORTS
import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import FlowsToolbar from "msuite_storybook/dist/flowsToolbar/FlowsToolbar";
import Button from "msuite_storybook/dist/button/Button";
import { FcWorkflow } from "react-icons/fc";
import { AiFillStar, AiOutlinePlusCircle } from "react-icons/ai";
import { FaArrowRight } from "react-icons/fa";
import Modal from "msuite_storybook/dist/modal/Modal";
import { logger } from "./../../utils/_dataDogUtils";
import { isEdge, isNode, removeElements } from "react-flow-renderer";

// REDUX IMPORTS
import { handleSetPageTitle } from "../../redux/generalActions";
import {
  handleFetchFlows,
  handleFetchWorkStagesGet,
  handleFetchFlowMap,
  handleSaveFlowMap,
  handleCleanup,
  handleUpdateFlow,
  handleFetchShippingBlocks,
  handleFetchFlowCodes,
  handleCreateFlowCode,
  handleDeleteFlow,
  handleDeleteStage,
} from "./flowsActions";
import { notify } from "../reusable/alertPopup/alertPopupActions";

// COMPONENT IMPORTS
import FlowRenderer from "./flowRenderer/FlowRenderer";
import ManageConditions from "./conditions/ManageConditions";
import NewStageModal from "./createNewModals/NewStageModal";
import NewFlowModal from "./createNewModals/NewFlowModal";
import FlowsSidebar from "./flowsSidebar/FlowsSidebar";
import RightPanel from "./flowRenderer/editingPanels/RightPanel";
import ConfirmationModal from "../reusable/confirmationModal/ConfirmationModal";

// TRANSLATION IMPORTS
import useTranslations from "../../hooks/useTranslations";
import flowsTranslations from "./flowsTranslations.json";

// HELPER IMPORTS
import { flattenFlow } from "../../_utils";

// STYLES IMPORTS
import "./stylesFlows.scss";
import "./createNewModals/stylesCreateNewModals.scss";

const Flows = () => {
  const [selectedFlow, setSelectedFlow] = useState(null);
  const [currentFlowState, setCurrentFlowState] = useState([]);
  const [hasNoStart, setHasNoStart] = useState(false);
  const [startHasIncomingLinks, setStartHasIncomingLinks] = useState(false);
  const [hasIsland, setHasIsland] = useState(false);
  const [hasLoop, setHasLoop] = useState(false);
  const [
    hasLogicGatesConnectionsError,
    setHasLogicGatesConnectionsError,
  ] = useState(false);
  const [
    hasLogicGatesConnectedError,
    setHasLogicGatesConnectedError,
  ] = useState(false);
  const [statusColorUpdated, setStatusColorUpdated] = useState(false);
  const [manageConditions, toggleManageConditions] = useState(false);
  const [newStage, toggleNewStage] = useState(false);
  const [inFlow, setInFlow] = useState(true);
  const [newFlow, toggleNewFlow] = useState(false);
  const [canEdit, setCanEdit] = useState(false);
  const [canCreate, setCanCreate] = useState(false);
  const [shippingBlockUsed, toggleShippingBlockUsed] = useState(false);
  const [showDeleteFlow, toggleDeleteFlow] = useState(false);
  const [saveWarning, toggleSaveWarning] = useState(false);
  const [selectedStage, setSelectedStage] = useState(null);
  const [reactFlowInstance, setReactFlowInstance] = useState(null);
  const [rightPanelAction, setRightPanelAction] = useState(null);
  const [selectedNode, setSelectedNode] = useState(null);
  const [selectedLink, setSelectedLink] = useState(null);
  const [statusColorDropdownInfo, setStatusColorDropdownInfo] = useState(null);
  const [confirmationType, setConfirmationType] = useState(null);

  const translate = useTranslations(flowsTranslations);
  const dispatch = useDispatch();

  const {
    workStages,
    flows,
    currentFlowMap,
    flowCodes,
    isLoadingStages,
  } = useSelector((state) => state.flowsData);
  const { permissions, userId, userInfo, userSettings } = useSelector(
    (state) => state.profileData
  );

  useEffect(() => {
    if (!permissions.length) return;
    // redirect to homepage if user doesn't have permission to view settings
    if (!permissions.includes(62)) {
      const homePage = userSettings?.home_page || "jobs";
      window.location.assign(`${process.env.REACT_APP_FABPRO}/${homePage}`);
    }
  }, [permissions, userSettings]);

  useEffect(() => {
    dispatch(handleSetPageTitle(translate("Flows")));
    dispatch(handleFetchFlows());
    dispatch(handleFetchShippingBlocks());
    dispatch(handleFetchFlowCodes());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch]);

  useEffect(() => {
    if (selectedFlow) {
      dispatch(handleFetchWorkStagesGet());
      dispatch(handleFetchFlowMap(selectedFlow.id));
    }
  }, [selectedFlow, dispatch]);

  useEffect(() => {
    if (permissions) {
      if (permissions.includes(248)) setCanEdit(true);
      if (permissions.includes(277)) setCanCreate(true);
    }
  }, [permissions]);

  const checkForStart = (nodes) => {
    if (nodes.find((n) => n.data.startingNode)) return true;

    return false;
  };

  const checkStartHasNoIncomingLinks = (startingNodeId, links) => {
    if (links.find((l) => l.target === startingNodeId)) return false;

    return true;
  };

  const checkForLoops = (links) => {
    const nodes = Object.keys(links);
    const visited = {};
    const recStack = {};

    const detectLoop = (vertex, visited, recStack) => {
      if (!visited[vertex]) {
        visited[vertex] = true;
        recStack[vertex] = true;
        const linkList = Object.values(links).filter(
          (i) => i.target === vertex.split("-")[0] && i.id !== vertex
        );

        for (const link of linkList) {
          if (!visited[link.id] && detectLoop(link.id, visited, recStack)) {
            return true;
          } else if (recStack[link.id]) {
            return true;
          }
        }

        recStack[vertex] = false;
        return false;
      }
    };

    for (const node of nodes) {
      if (detectLoop(node, visited, recStack)) {
        return true;
      }
    }

    return false;
  };

  const checkForIslands = ({ links, nodes }) => {
    const filteredNodes = Object.values(nodes)
      .filter((n) => !n.data.startingNode)
      .reduce((acc, curr) => {
        acc[curr.id] = curr;
        return acc;
      }, {});

    for (const node in filteredNodes) {
      const toPattern = new RegExp(`(?:${node})$`);
      const fromPattern = new RegExp(`^${node}-`);
      let island = false;

      if (!Object.values(links).length && Object.values(nodes).length > 1)
        island = true;
      for (const link in links) {
        island = !toPattern.test(link) && !fromPattern.test(link);

        if (!island) break;
      }

      if (island) return true;
    }

    return false;
  };

  const checkLogicGatesConnections = ({ links, nodes }) => {
    for (let node in nodes) {
      if (nodes[node].data.logicGate) {
        let connections = [];

        let nodePattern = new RegExp(node);
        for (let link in links) {
          if (
            nodePattern.test(links[link].source) ||
            nodePattern.test(links[link].target)
          )
            connections.push(links[link]);
        }

        if (
          connections.every(
            (c) => c.openCondition === connections[0].openCondition
          )
        )
          return false;
        else return true;
      }
    }
  };

  const checkLogicGatesConnected = ({ links, nodes }) => {
    const linksArray = Object.values(links);
    for (let node in nodes) {
      if (nodes[node].data.logicGate) {
        if (!linksArray.find((l) => l.source === node)) return true;
        if (!linksArray.find((l) => l.target === node)) return true;
      }
    }

    return false;
  };

  const formatLinks = (links) => {
    const result = links.map((l) => {
      let { source, target, data } = l;
      let { itemOpenConditionId } = data;

      return {
        from_node_id: parseInt(source),
        to_node_id: parseInt(target),
        item_open_condition_id: itemOpenConditionId,
      };
    });

    return result;
  };

  const handleSaveFlow = (elementsToUse = null) => {
    if (reactFlowInstance) {
      const elements = elementsToUse || reactFlowInstance.getElements();
      const nodes = elements.filter(isNode);
      const links = elements.filter(isEdge);

      let reducedLinks = links
        .sort((a, b) => parseInt(a.source) - parseInt(b.source))
        .reduce((acc, curr) => {
          acc[curr.id] = curr;
          return acc;
        }, {});

      let reducedNodes = nodes.reduce((acc, curr) => {
        acc[curr.id] = curr;
        return acc;
      }, {});

      const formattedState = { links: reducedLinks, nodes: reducedNodes };

      let hasNoStart,
        startHasIncomingLinks,
        hasLoop,
        hasIsland,
        hasLogicGatesConnectionsError,
        hasLogicGatesConnectedError;

      if (!checkForStart(nodes)) {
        hasNoStart = true;
        setHasNoStart(true);
      }

      if (
        !checkStartHasNoIncomingLinks(
          (nodes.find((n) => n.data.startingNode) || {}).id,
          links
        )
      ) {
        startHasIncomingLinks = true;
        setStartHasIncomingLinks(true);
      }

      if (checkForLoops(reducedLinks)) {
        hasLoop = true;
        setHasLoop(true);
      }

      if (checkForIslands(formattedState)) {
        hasIsland = true;
        setHasIsland(true);
      }

      if (checkLogicGatesConnections(formattedState)) {
        hasLogicGatesConnectionsError = true;
        setHasLogicGatesConnectionsError(true);
      }

      if (checkLogicGatesConnected(formattedState)) {
        hasLogicGatesConnectedError = true;
        setHasLogicGatesConnectedError(true);
      }

      if (
        hasNoStart ||
        startHasIncomingLinks ||
        hasIsland ||
        hasLoop ||
        hasLogicGatesConnectionsError ||
        hasLogicGatesConnectedError
      )
        return;
      else {
        const flattenedMap = flattenFlow(nodes, links);

        if (flattenedMap.length < Object.values(reducedNodes).length) {
          hasIsland = true;
          setHasIsland(true);
          return;
        }

        const formattedMapLinks = formatLinks(links);

        let newWorkFlowMap = {
          work_flow_id: selectedFlow.id,
          work_flow_map_data: flattenedMap.map((n) => ({
            node_id: n.node_id,
            stage_id: n.stage_id,
            logic_gate_id: n.logic_gate_id,
            stage_status_group_name: n.stage_status_group_name,
            stage_status_group_color: n.stage_status_group_color,
            default_weight: n.default_weight,
            coordinate: n.coordinate,
            stage_report: n.stage_report,
            stage_order: n.stage_order,
          })),
          work_flow_link_data: formattedMapLinks,
        };

        logger.info("Flow Changed", {
          user_id: userId,
          username: userInfo.username,
          id: selectedFlow.id,
          name: selectedFlow.name,
          work_flow_map: newWorkFlowMap,
        });
        dispatch(handleSaveFlowMap(newWorkFlowMap)).then((res) => {
          if (!res.error) {
            setHasIsland(false);
            setHasLoop(false);
            setHasNoStart(false);
            setStartHasIncomingLinks(false);
            setHasLogicGatesConnectedError(false);
            setHasLogicGatesConnectionsError(false);
            setStatusColorUpdated(false);
          }
        });
      }
    }
  };

  const performDeleteFlow = useCallback((flowId, confirmed = false) => {
    if (confirmed) {
      dispatch(handleDeleteFlow(flowId)).then((res) => {
        if (!res.error) {
          toggleDeleteFlow(false);
          handleBack();
          dispatch(handleFetchFlows());
        }
      });
    } else {
      toggleDeleteFlow(true);
    }
  }, []);

  const performFlowUpdate = useCallback(
    async (newName, newDefaultValue, newCostCode) => {
      let data = {};

      if (selectedFlow.default === 1 && newDefaultValue === 0) {
        dispatch(
          notify({
            id: Date.now(),
            type: "ERROR",
            message: "To change default flow, set a different flow to default.",
          })
        );
      } else if (selectedFlow.default !== newDefaultValue) {
        Object.assign(data, {
          default: newDefaultValue,
        });
      }

      if (newName !== selectedFlow.name) Object.assign(data, { name: newName });

      if (typeof newCostCode === "string") {
        if (newCostCode.trim()) {
          const newFlowCode = await dispatch(handleCreateFlowCode(newCostCode));
          if (!newFlowCode.error) {
            Object.assign(data, { flow_code_id: newFlowCode[0].id });
            dispatch(handleFetchFlowCodes());
          }
        } else Object.assign(data, { flow_code_id: null });
      } else if (newCostCode !== selectedFlow.flow_code_id)
        Object.assign(data, { flow_code_id: newCostCode || null });

      if (!Object.keys(data).length) return Promise.resolve({ error: true });

      return dispatch(
        handleUpdateFlow({
          id: selectedFlow.id,
          data,
        })
      ).then((res) => {
        if (!res.error) {
          dispatch(handleFetchFlows());
          setSelectedFlow({ ...selectedFlow, ...data });
        }
        return res;
      });
    },
    [selectedFlow]
  );

  const handleSelectStage = (stage) => {
    const isLogicGate = stage?.shape?.includes("Triangle", "Circle");

    setSelectedStage({
      id: null,
      position: null,
      type: null,
      data: {
        stageId: stage.id,
        stageName: stage.name,
        logicGate: isLogicGate ? (stage.shape === "Triangle" ? 1 : 2) : null,
        shippingBlockPosition: null,
        holdpoint: stage.shape === "Diamond" ? true : false,
        nestable: !!stage.nestable,
        groupable: !!stage.groupable,
        rejectable: !!stage.rejectable,
        stageCodeId: stage.stage_code_id,
        stageCodeName: stage.stage_code_name,
        includeInJointLog: false,
        stageStatusGroupName: "In Fabrication",
        stageWorkLevelId: stage.stage_work_level_id,
        metricWorkItemColumnId: stage.metric_work_item_column_id,
        defaultWeight: stage.default_weight,
      },
    });
  };

  const updateSelectedStage = (updatedInfo) => {
    if (selectedStage) setSelectedStage((s) => ({ ...s, ...updatedInfo }));
  };

  const handleBack = () => {
    setCurrentFlowState(null);
    setInFlow(true);
    toggleNewStage(false);
    toggleManageConditions(false);
    setSelectedFlow(null);
    setHasIsland(false);
    setHasLoop(false);
    setHasNoStart(false);
    setStartHasIncomingLinks(false);
    setHasLogicGatesConnectionsError(false);
    setHasLogicGatesConnectedError(false);
    setStatusColorUpdated(false);
    toggleShippingBlockUsed(false);
    dispatch(handleCleanup());
  };

  const displayedFlows = useMemo(() => {
    if (flows) {
      let sortedFlows = [...flows].sort((a, b) => {
        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;
        else return -1;
      });
      return sortedFlows;
    } else return [];
  }, [flows]);

  const displayedStages = useMemo(() => {
    if (workStages) {
      let sortedStages = [...workStages].sort((a, b) => {
        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;
        else return -1;
      });
      return sortedStages;
    } else return [];
  }, [workStages]);

  const displayedStatusGroups = useMemo(() => {
    if (currentFlowState) {
      return Array.from(
        new Set(
          currentFlowState
            .filter(isNode)
            .map((n) => n.data.stageStatusGroupName)
        )
      )
        .filter((sg) => sg !== null)
        .sort((a, b) => {
          if (a.toLowerCase() > b.toLowerCase()) return 1;
          else return -1;
        });
    } else if (currentFlowMap && currentFlowMap.work_flow_map) {
      return Array.from(
        new Set(
          currentFlowMap.work_flow_map.map((ws) => ws.stage_status_group_name)
        )
      )
        .filter((sg) => sg !== null)
        .sort((a, b) => {
          if (a.toLowerCase() > b.toLowerCase()) return 1;
          else return -1;
        });
    } else return [];
  }, [currentFlowMap, currentFlowState]);

  const onElementsRemove = (elementsToRemove) => {
    if (
      !canEdit ||
      !elementsToRemove ||
      !elementsToRemove.length ||
      (isEdge(elementsToRemove[0]) && elementsToRemove[0].data.drawingLevelLink)
    )
      return;
    const nodeToRemove = elementsToRemove.find((el) => isNode(el));

    if (nodeToRemove && nodeToRemove.data.shippingBlockPosition) {
      toggleShippingBlockUsed(false);
      const shippingStageNodes = currentFlowState
        .filter((el) => isNode(el) && el.data.shippingBlockPosition)
        .map((n) => n.id);
      const shippingStageEdges = currentFlowState
        .filter(
          (el) =>
            isEdge(el) &&
            (shippingStageNodes.includes(el.source) ||
              shippingStageNodes.includes(el.target))
        )
        .map((e) => e.id);
      setCurrentFlowState((els) =>
        els.filter(
          (el) =>
            !shippingStageNodes.includes(el.id) &&
            !shippingStageEdges.includes(el.id)
        )
      );
    } else {
      setCurrentFlowState((els) => removeElements(elementsToRemove, els));
    }

    setSelectedNode(null);
    setSelectedStage(null);
    setSelectedLink(null);
  };

  const updateStatusGroup = (stageId, newStatusGroup, newStatusColor) => {
    setStatusColorDropdownInfo(null);
    setCurrentFlowState((els) => {
      for (let node of els) {
        if (isNode(node) && node.data.stageId === stageId) {
          node.data.stageStatusGroupName = newStatusGroup;
          node.data.stageStatusGroupColor = newStatusColor;
          break;
        }
      }

      return [...els];
    });
  };

  const updateSelectedNode = (updatedNodeData, updateFlow = false) => {
    let updatedElements = null;
    setCurrentFlowState((els) => {
      for (let el of els) {
        if (el.id === selectedNode.id) {
          el.data = {
            ...el.data,
            ...updatedNodeData,
            label: updatedNodeData.stageName || el.data.label,
          };

          if (updatedNodeData.startingNode) {
            el.type = "starting-node";
          } else {
            el.type = "standard-node";
          }

          setSelectedNode(el);
        } else if (
          updatedNodeData.startingNode &&
          el.id !== selectedNode.id &&
          el.type === "starting-node"
        ) {
          el.data = {
            ...el.data,
            startingNode: false,
          };
          el.type = "standard-node";
        }
      }

      updatedElements = [...els];
      return updatedElements;
    });

    if (updatedElements && updateFlow) {
      handleSaveFlow(updatedElements);
    }
  };

  const deleteStageFromSystem = useCallback(() => {
    const stageIdToDelete = selectedStage?.data?.stageId || selectedNode?.id;
    if (!stageIdToDelete) return;
    dispatch(handleDeleteStage(stageIdToDelete)).then((res) => {
      if (!res.error) {
        dispatch(handleFetchWorkStagesGet());
        setSelectedStage(null);
      }
    });
  }, [selectedStage, selectedNode]);

  const confirmationProps = {
    delete: {
      message: `Are you sure you want to delete ${selectedStage?.data?.stageName}? Existing work flows will still use this stage, but you won't be able to assign it to work flows.`,
      cancelAction: () => setConfirmationType(false),
      continueAction: () => {
        deleteStageFromSystem();
        setConfirmationType(false);
      },
    },
    cancel: {
      message: `Are you sure you want to cancel stage creation? Any current changes will not be saved.`,
      cancelAction: () => setConfirmationType(false),
      continueAction: () => {
        setConfirmationType(false);
        handleClosePanel();
      },
    },
  };

  const handleClosePanel = () => {
    setSelectedStage(null);
    setSelectedNode(null);
    setRightPanelAction(null);
  };

  const AllFlows = () => (
    <div className="all-flows-wrapper">
      <div className="card-wrapper">
        {canCreate && (
          <div
            className="new-flow-card"
            onClick={() => toggleNewFlow(true)}
            key="new-flow"
          >
            <AiOutlinePlusCircle />
            <p className="title">{translate("New Flow")}</p>
          </div>
        )}
        {flows &&
          displayedFlows.length > 0 &&
          displayedFlows.map((flow) => (
            <div
              className="flow-card"
              onClick={() => setSelectedFlow(flow)}
              key={`${flow.name}${flow.id}`}
            >
              <span className="flow-screenshot">
                <FcWorkflow />
              </span>
              {/* truncating displayed name to not exceed box size */}
              <p className="flow-name">
                {flow.name.length > 55
                  ? `${flow.name.substring(0, 51)}...`
                  : flow.name}
              </p>
              {flow.default === 1 && (
                <AiFillStar className="default-flow-indicator" />
              )}
            </div>
          ))}
      </div>
    </div>
  );

  return (
    <>
      {!selectedFlow ? (
        <AllFlows />
      ) : (
        <div className="flows-wrapper">
          <FlowsToolbar
            title={selectedFlow.name}
            handleSave={
              currentFlowMap ? () => toggleSaveWarning(true) : handleSaveFlow
            }
            handleBack={() => handleBack()}
            hasIsland={hasIsland}
            hasLoop={hasLoop}
            hasNoStart={hasNoStart}
            startHasIncomingLinks={startHasIncomingLinks}
            hasLogicGatesConnectionsError={hasLogicGatesConnectionsError}
            hasLogicGatesConnectedError={hasLogicGatesConnectedError}
            statusColorUpdated={statusColorUpdated}
            handleUpdateFlow={performFlowUpdate}
            isDefaultFlow={selectedFlow.default === 1}
            flowId={selectedFlow.id}
            canEdit={canEdit}
            costCodes={flowCodes}
            currentCostCode={selectedFlow.flow_code_id}
            handleDeleteFlow={performDeleteFlow}
          />
          <div className={`sidebar ${!canCreate ? "create-locked" : ""}`}>
            <FlowsSidebar
              handleNewStage={() => {
                setRightPanelAction("CREATE");
                setSelectedStage(null);
                setSelectedNode(null);
              }}
              stages={displayedStages}
              currentFlowState={currentFlowState || []}
              setSelectedStage={(stage) => {
                handleSelectStage(stage);

                const displayedStageIds = currentFlowState
                  ? currentFlowState.filter(isNode).map((n) => n.data.stageId)
                  : [];

                const inFlow = displayedStageIds.includes(stage.id);
                setInFlow(inFlow);
              }}
              canCreate={canCreate}
              canEdit={canEdit}
              shippingBlockUsed={shippingBlockUsed}
            />
            {canCreate && (
              <Button
                className="manage-conditions-button"
                onClick={() => toggleManageConditions(true)}
              >
                {translate("Manage Conditions")}
              </Button>
            )}
          </div>
          {currentFlowMap && (workStages.length || !isLoadingStages) ? (
            <FlowRenderer
              currentFlowState={currentFlowState}
              setCurrentFlowState={setCurrentFlowState}
              statusGroups={displayedStatusGroups}
              inFlow={inFlow}
              setInFlow={setInFlow}
              selectedFlow={selectedFlow}
              handleSaveFlow={handleSaveFlow}
              toggleShippingBlockUsed={toggleShippingBlockUsed}
              shippingBlockUsed={shippingBlockUsed}
              setStatusColorUpdated={setStatusColorUpdated}
              canEdit={canEdit}
              canCreate={canCreate}
              selectedStage={selectedStage}
              setSelectedStage={setSelectedStage}
              updateSelectedStage={updateSelectedStage}
              reactFlowInstance={reactFlowInstance}
              setReactFlowInstance={setReactFlowInstance}
              rightPanelAction={rightPanelAction}
              setRightPanelAction={setRightPanelAction}
              selectedNode={selectedNode}
              setSelectedNode={setSelectedNode}
              selectedLink={selectedLink}
              setSelectedLink={setSelectedLink}
              statusColorDropdownInfo={statusColorDropdownInfo}
              setStatusColorDropdownInfo={setStatusColorDropdownInfo}
              setConfirmationType={setConfirmationType}
              onElementsRemove={onElementsRemove}
              updateSelectedNode={updateSelectedNode}
              updateStatusGroup={updateStatusGroup}
            />
          ) : (
            <></>
          )}
          {(selectedNode || selectedStage || rightPanelAction === "CREATE") && (
            <RightPanel
              type={rightPanelAction || "EDIT"}
              selectedStage={selectedNode || selectedStage}
              setSelectedStage={
                selectedNode ? setSelectedNode : setSelectedStage
              }
              removeStageFromFlow={() => onElementsRemove([selectedNode])}
              inFlow={inFlow}
              statusGroups={displayedStatusGroups}
              selectedFlow={selectedFlow}
              updateStatusGroup={updateStatusGroup}
              nodes={currentFlowState ? currentFlowState.filter(isNode) : []}
              updateSelectedStage={
                selectedNode ? updateSelectedNode : updateSelectedStage
              }
              handleSaveFlow={handleSaveFlow}
              setRightPanelAction={setRightPanelAction}
              setConfirmationType={setConfirmationType}
              confirmationType={confirmationType}
              handleSelectStage={handleSelectStage}
            />
          )}
          {manageConditions && (
            <ManageConditions
              open={manageConditions}
              handleClose={() => toggleManageConditions(false)}
            />
          )}
          {newStage && (
            <NewStageModal
              open={newStage}
              handleClose={() => toggleNewStage(false)}
              translate={translate}
            />
          )}
        </div>
      )}
      {newFlow && (
        <NewFlowModal
          open={newFlow}
          handleClose={() => toggleNewFlow(false)}
          translate={translate}
          setSelectedFlow={setSelectedFlow}
          flows={flows}
        />
      )}
      {showDeleteFlow && (
        <Modal
          open={showDeleteFlow}
          handleClose={() => toggleDeleteFlow(false)}
        >
          <div className="delete-flow-confirmation">
            <h2 className="title">Are you sure?</h2>
            <div className="content">
              <p>
                Are you sure you want to delete <span>{selectedFlow.name}</span>
                ? Existing Drawings will still use this work flow, but you won't
                be able to assign it to Drawings.
              </p>
              <Button
                className="cancel"
                onClick={() => toggleDeleteFlow(false)}
              >
                Cancel
              </Button>
              <Button
                className="submit"
                onClick={() => performDeleteFlow(selectedFlow.id, true)}
              >
                Continue <FaArrowRight />
              </Button>
            </div>
          </div>
        </Modal>
      )}
      {confirmationType && (
        <ConfirmationModal
          showModal={confirmationType ? true : false}
          handleClick={
            confirmationProps[confirmationType.toLowerCase()].continueAction
          }
          toggleModal={
            confirmationProps[confirmationType.toLowerCase()].cancelAction
          }
          message={confirmationProps[confirmationType.toLowerCase()].message}
          submitText="Continue"
          cancelText="Close"
          action="DELETE"
        />
      )}
      {saveWarning && (
        <Modal open={saveWarning} handleClose={() => toggleSaveWarning(false)}>
          <div className="save-flow-confirmation">
            <h2 className="title">Are you sure?</h2>
            <div className="content">
              <p>
                Changing flow will update all non-archived work using this flow.
                If you want to update only for new work you can instead
                duplicate the flow and make the changes to the new flow version.
                Are you sure you wish to continue?
              </p>
              <Button
                className="cancel"
                onClick={() => toggleSaveWarning(false)}
              >
                Cancel
              </Button>
              <Button
                className="submit"
                onClick={() => {
                  handleSaveFlow();
                  toggleSaveWarning(false);
                }}
              >
                Continue <FaArrowRight />
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </>
  );
};

export default Flows;
