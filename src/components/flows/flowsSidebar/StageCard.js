// NPM PACKAGE IMPORTS
import React, { useMemo } from "react";

// STYLES IMPORTS
import "./stylesFlowsSidebar.scss";

const StageCard = ({ stage, used = false, onClick = (f) => f }) => {
  const onDragStart = (event, nodeType) => {
    event.dataTransfer.setData("application/reactflow", nodeType);
    event.dataTransfer.effectAllowed = "move";
  };

  const className = `stage-shape ${
    stage.shape ? stage.shape.toLowerCase() : ""
  }`;

  const newNodeInfo = useMemo(() => {
    return {
      label: stage.name,
      stageId: stage.id,
      stageName: stage.name,
      logicGate: ["AND", "OR"].includes(stage.name)
        ? stage.name === "AND"
          ? 1
          : 2
        : null,
      shippingBlockPosition: null,
      holdpoint: stage.shape === "Diamond" ? true : false,
      nestable: !!stage.nestable,
      groupable: !!stage.groupable,
      rejectable: !!stage.rejectable,
      stageCodeId: stage.stage_code_id,
      stageCodeName: stage.stage_code_name,
      includeInJointLog: false,
      stageStatusGroupName: "In Fabrication",
      stageWorkLevelId: stage.stage_work_level_id,
      metricWorkItemColumnId: stage.metric_work_item_column_id,
      defaultWeight: stage.default_weight,
    };
  }, [stage]);

  return (
    <div
      onDragStart={(event) => onDragStart(event, JSON.stringify(newNodeInfo))}
      className={`sidebar-stage-card ${used ? "used" : ""} ${
        stage.shape && (stage.shape === "Triangle" || stage.shape === "Circle")
          ? "logic-gate"
          : ""
      } ${stage.name === "SHIPPING BLOCK" ? "shipping-block" : ""}`}
      onClick={onClick}
      draggable={!used}
    >
      <span className={className} />
      <p>{stage.name}</p>
    </div>
  );
};

export default StageCard;
