// NPM PACKAGE IMPORTS
import React, { useState, useMemo, useRef, useCallback } from "react";
import { FiSearch } from "react-icons/fi";
import { isNode } from "react-flow-renderer";
import CollapsibleList from "msuite_storybook/dist/collapsibleList/CollapsibleList";

// COMPONENT IMPORTS
import StageCard from "./StageCard";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../hooks/useOutsideClick";

// STYLES IMPORTS
import "./stylesFlowsSidebar.scss";

const FlowsSidebar = ({
  stages,
  handleNewStage,
  currentFlowState = [],
  setSelectedStage,
  canEdit,
  canCreate,
  shippingBlockUsed = false,
}) => {
  const [showInput, toggleInput] = useState(false);
  const [searchInput, setSearchInput] = useState("");

  const inputRef = useRef(null);
  useOutsideClick(inputRef, () => {
    if (!searchInput.length) toggleInput(false);
  });

  const f_stages = useMemo(() => {
    if (!stages || !stages.length) return [];
    if (!searchInput.length)
      return stages.filter((stage) => stage.stage_work_level_id !== 3);
    return stages.filter(
      (stage) =>
        stage.stage_work_level_id !== 3 &&
        stage.name.toLowerCase().includes(searchInput.toLowerCase())
    );
  }, [stages, searchInput]);

  const checkUsed = useCallback(
    (stage) => {
      if (
        currentFlowState.find(
          (el) => isNode(el) && el.data.stageId === stage.id
        )
      ) {
        return true;
      } else return false;
    },
    [currentFlowState]
  );

  return (
    <div className={`flows-sidebar-wrapper ${canEdit ? "" : "edit-locked"}`}>
      <span className="search-wrapper">
        <FiSearch
          onClick={() => {
            if (!showInput) toggleInput(true);
          }}
        />
        {showInput && (
          <input
            className="search-input"
            type="text"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            ref={inputRef}
          />
        )}
      </span>
      <CollapsibleList
        buttonTitle="New Stage"
        showButton={canCreate}
        handleButtonClick={handleNewStage}
        title="Stages"
        defaultCollapsed={false}
      >
        {f_stages && f_stages.length ? (
          f_stages.map((stage) => {
            const used = checkUsed(stage);
            return (
              <StageCard
                stage={stage}
                used={used}
                key={stage.id}
                onClick={canEdit ? () => setSelectedStage(stage) : (f) => f}
              />
            );
          })
        ) : (
          <p className="no-stages-message">No stages</p>
        )}
      </CollapsibleList>
      {canEdit && (
        <>
          <StageCard
            stage={{ name: "SHIPPING BLOCK" }}
            used={shippingBlockUsed}
          />
          <StageCard stage={{ name: "AND", shape: "Triangle" }} />
          <StageCard stage={{ name: "OR", shape: "Circle" }} />
        </>
      )}
    </div>
  );
};

export default FlowsSidebar;
