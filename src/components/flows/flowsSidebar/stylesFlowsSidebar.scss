@import "../../styles/colors.scss";

div.flows-sidebar-wrapper {
  display: grid;
  grid-template-rows: 33px 1fr 40px 40px;
  row-gap: 10px;

  & span.search-wrapper {
    width: 100%;
    height: 33px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 10px 10px 0;

    & input[type="text"].search-input {
      border: none;
      width: 180px;
      color: white;
      background-color: transparent;
      box-sizing: border-box;
      font-size: 1.1rem;
      border-bottom: 1px solid $blue;
    }

    & input[type="text"].search-input:focus {
      outline: none;
    }

    & svg {
      cursor: pointer;
      font-size: 1.2rem;
    }
  }

  & p.no-stages-message {
    text-align: center;
  }

  &.edit-locked {
    .sidebar-stage-card {
      cursor: default;

      &:hover {
        background-color: $lighterGrey;
      }
    }
  }
}

.sidebar-stage-card {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: $lighterGrey;
  cursor: pointer;
  color: $lighterSlate;
  transition: background-color 250ms ease;
  border: 1px solid $lighterSlate;
  border-radius: 3px;

  & span.stage-shape {
    height: 18px;
    width: 18px;
    background-color: $lighterSlate;
    margin-left: 12px;

    &.diamond {
      clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
    }

    &.triangle {
      clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    }

    &.circle {
      border-radius: 50%;
    }
  }

  & p {
    width: 75%;
    font-weight: normal;
  }

  &.used {
    color: $blue;
    opacity: 0.75;
    border-color: $blue;

    & span.stage-shape {
      background-color: $blue;
    }
  }

  &:not(.shipping-block.used):hover {
    background-color: lighten($lighterGrey, 10%);
  }

  &.logic-gate {
    margin: 0 auto;
    width: calc(100% - 20px);

    &:first-of-type {
      margin-bottom: 10px;
    }
  }

  &.shipping-block {
    margin: 0 auto;
    width: calc(100% - 20px);
  }

  &.shipping-block.used:hover {
    cursor: default;
  }
}
