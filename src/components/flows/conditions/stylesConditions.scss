@import "../../styles/colors.scss";

div.condition-group-line {
  display: grid;
  grid-template-columns: 50px 1fr;
  row-gap: 5px;

  padding: 5px 10px;
  border-bottom: 2px solid #333;

  &:first-of-type {
    border-top: 2px solid #333;
  }

  &:nth-of-type(odd) {
    background-color: $lighterGrey;
  }

  & > span.group-operand {
    grid-column: 1/2;
  }

  & > div.condition-row {
    grid-column: 2/-1;

    display: flex;
    column-gap: 10px;
  }
}
