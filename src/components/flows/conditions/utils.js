export const ConditionObj = (
  id,
  filter = null,
  comparison = null,
  condition = ""
) => ({
  id,
  filter,
  comparison,
  condition,
});

export const ConditionGroupObj = (
  subGroup,
  subGroupOperand = "start",
  conditions = [{ id: 1, operand: "or", work_item_condition_id: "" }]
) => ({ sub_group: subGroup, sub_group_operand: subGroupOperand, conditions });

export const textBasedFilters = [
  "Contains",
  "Does Not Contain",
  "Equal To",
  "Not Equal To",
];
