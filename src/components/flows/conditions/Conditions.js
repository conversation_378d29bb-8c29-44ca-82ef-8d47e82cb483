// NPM PACKAGE IMPORTS
import React, { useState, useCallback, useEffect, useMemo } from "react";
import Input from "msuite_storybook/dist/input/Input";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import {
  handleSaveCondition,
  handleDeleteCondition,
  handleFetchConditions,
} from "../flowsActions";
import { handleFetchColumnsForConditions } from "../../throughputs/throughputsActions";

// COMPONENT IMPORTS
import Condition from "./Condition";
import { ConditionObj } from "./utils";
import ConditionLine from "./ConditionLine";

// HELPER FUNCTION IMPORTS
import { escapeRegExp } from "../../../_utils";

const Conditions = ({ conditions, translate }) => {
  const [conditionToSave, setConditionToSave] = useState(ConditionObj());
  const [searchInput, setSearchInput] = useState("");

  const dispatch = useDispatch();
  const { columnsForConditions } = useSelector(
    (state) => state.throughputsData
  );

  useEffect(() => {
    dispatch(handleFetchColumnsForConditions());
  }, []);

  const onChange = useCallback(
    (value, field) => {
      let newConditionObj = { ...conditionToSave };

      newConditionObj[field] = value;
      if (["Is Blank", "Is Not Blank"].includes(value))
        newConditionObj.condition = "";

      setConditionToSave(newConditionObj);
    },
    [conditionToSave]
  );

  const handleSave = () => {
    dispatch(handleSaveCondition(conditionToSave)).then((res) => {
      if (!res.error) {
        dispatch(handleFetchConditions());
      }
    });
  };

  const formattedConditions = useMemo(() => {
    if (conditions) {
      return conditions.map((c) => ({
        ...c,
        display_name: c.work_item_column_name,
      }));
    } else return [];
  }, [conditions]);

  const displayedConditions = useMemo(() => {
    if (formattedConditions) {
      const pattern = new RegExp(escapeRegExp(searchInput), "i");
      return formattedConditions.filter(
        (c) =>
          pattern.test(c.display_name) ||
          pattern.test(c.comparison) ||
          pattern.test(c.value)
      );
    } else return [];
  }, [searchInput, formattedConditions]);

  return (
    <div className="conditions-display">
      <Input
        placeholder="-- Search --"
        value={searchInput}
        onChange={(e) => setSearchInput(e.target.value)}
        className="search-input"
      />
      <div className="conditions-container">
        <Condition
          key="new-condition"
          conditionObj={conditionToSave}
          columns={columnsForConditions?.filter(
            (x) =>
              x.is_custom !== 1 ||
              (x.is_custom === 1 && x.table_target === "work_items")
          )} // only work_items for now
          onChange={onChange}
          translate={translate}
          handleSave={handleSave}
        />
        {conditions ? (
          displayedConditions.length ? (
            displayedConditions.map((c, idx) => (
              <ConditionLine
                key={c.id}
                condition={c}
                className={idx % 2 ? "even" : "odd"}
                handleDelete={() => dispatch(handleDeleteCondition(c.id))}
              />
            ))
          ) : (
            <p className="no-conditions">
              No conditions{" "}
              {searchInput && !displayedConditions.length ? "found" : "created"}
              .
            </p>
          )
        ) : (
          <></>
        )}
      </div>
    </div>
  );
};

export default Conditions;
