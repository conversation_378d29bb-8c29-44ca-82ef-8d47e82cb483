// NPM PACKAGE IMPORTS
import React from "react";

// COMPONENT IMPORTS
import ConditionLine from "./ConditionLine";

const ConditionGroupLine = ({
  conditionGroup,
  className = "",
  selected = false,
  onClick = (f) => f,
}) => {
  return (
    <div
      className="condition-group-line"
      onClick={() => onClick(conditionGroup)}
    >
      {conditionGroup && (
        <>
          <span className="group-operand">
            {conditionGroup.sub_group_operand !== "start" &&
              conditionGroup.sub_group_operand.toUpperCase()}
          </span>
          {conditionGroup.conditions.map((c, idx) => (
            <div className="condition-row">
              {idx !== 0 && c.operand !== "start" && (
                <span className="condition-operand">
                  {c.operand.toUpperCase()}
                </span>
              )}
              <ConditionLine
                key={JSON.stringify(c)}
                condition={c}
                className={className}
                selected={selected}
              />
            </div>
          ))}
        </>
      )}
    </div>
  );
};

export default ConditionGroupLine;
