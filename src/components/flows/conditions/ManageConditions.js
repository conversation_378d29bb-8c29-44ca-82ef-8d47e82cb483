// NPM PACKAGE IMPORTS
import React, { useEffect } from "react";
import Modal from "msuite_storybook/dist/modal/Modal";
import { useSelector, useDispatch } from "react-redux";

// REDUX IMPORTS
import { handleFetchConditions } from "../flowsActions";

// COMPONENT IMPORTS
import Conditions from "./Conditions";

// TRANSLATION IMPORTS
import useTranslations from "../../../hooks/useTranslations";
import flowsTranslations from "../flowsTranslations.json";

const ManageConditions = ({ open, handleClose }) => {
  const dispatch = useDispatch();
  const { conditions } = useSelector((state) => state.flowsData);

  const translate = useTranslations(flowsTranslations);

  useEffect(() => {
    dispatch(handleFetchConditions());
  }, []);

  return (
    <Modal open={open} handleClose={handleClose}>
      <div className="manage-conditions-modal">
        <h2 className="title">{translate("Manage Conditions")}</h2>
        <div className="content">
          <Conditions conditions={conditions} translate={translate} />
        </div>
      </div>
    </Modal>
  );
};

export default ManageConditions;
