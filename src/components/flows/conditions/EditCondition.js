// NPM PACKAGE IMPORTS
import React, { useState, useCallback } from "react";
import Button from "msuite_storybook/dist/button/Button";

// COMPONENT IMPORTS
import Condition from "./Condition";
import { ConditionObj } from "./utils";

const EditConditionGroup = ({
  stageColumns,
  translate,
  selectedConditionGroup,
  clearSelectedConditionGroup,
}) => {
  const [matchAll, toggleMatchAll] = useState(false);
  const [conditions, setConditions] = useState([ConditionObj(1)]);

  const checkDisabled = useCallback(() => {
    for (let i = 0; i < conditions.length; i++) {
      if (
        conditions[i].filter &&
        conditions[i].comparison &&
        conditions[i].condition &&
        conditions[i].condition.trim()
      )
        return false;
      else return true;
    }
  }, [conditions]);

  const onChange = useCallback(
    (value, id, field) => {
      let newConditionObj = conditions.find((co) => co.id === id);

      newConditionObj[field] = value;

      const newConditions = [
        ...conditions.filter((co) => co.id !== id),
        newConditionObj,
      ].sort((a, b) => a.id - b.id);
      setConditions(newConditions);
    },
    [conditions]
  );

  const addCondition = useCallback(
    (id) => {
      const newCondition = ConditionObj(id + 1);

      const newConditions = [...conditions, newCondition].sort(
        (a, b) => a.id - b.id
      );

      setConditions(newConditions);
    },
    [conditions]
  );

  const removeCondition = useCallback(
    (id) => {
      const newConditions = conditions.filter((c) => c.id !== id);
      setConditions(newConditions);
    },
    [conditions]
  );
  return (
    <div className="edit-condition-group">
      <div className="toggle">
        <label>
          <input
            type="radio"
            radioGroup="match"
            onChange={() => toggleMatchAll(false)}
            checked={!matchAll}
          />
          Match ANY Criteria
        </label>
        <label>
          <input
            type="radio"
            radioGroup="match"
            onChange={() => toggleMatchAll(true)}
            checked={matchAll}
          />
          Match ALL Criteria
        </label>
      </div>
      <div className="conditions-list">
        {conditions.map((c, idx) => (
          <Condition
            key={c.id}
            conditionObj={c}
            columns={stageColumns}
            onChange={onChange}
            addCondition={addCondition}
            removeCondition={removeCondition}
            last={idx + 1 === conditions.length}
          />
        ))}
      </div>
      <Button className="cancel" onClick={clearSelectedConditionGroup}>
        {translate("Cancel")}
      </Button>
      <Button className="save" disabled={checkDisabled()}>
        {translate("Save")}
      </Button>
    </div>
  );
};

export default EditConditionGroup;
