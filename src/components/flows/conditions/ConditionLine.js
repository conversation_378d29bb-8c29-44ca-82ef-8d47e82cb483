// NPM PACKAGE IMPORTS
import React from "react";
import { FaTrash } from "react-icons/fa";
import { textBasedFilters } from "./utils";

const ConditionLine = ({ condition, className = "", handleDelete }) => {
  return (
    <div className={`condition-line ${className}`}>
      {textBasedFilters.includes(condition.comparison)
        ? `${condition.work_item_column_name} ${condition.comparison} "${condition.value}"`
        : `${condition.work_item_column_name} ${condition.comparison} ${condition.value}`}
      <FaTrash onClick={handleDelete} />
    </div>
  );
};

export default ConditionLine;
