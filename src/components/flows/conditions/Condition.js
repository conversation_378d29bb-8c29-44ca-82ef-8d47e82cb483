// NPM PACKAGE IMPORTS
import React, { useMemo, useCallback } from "react";
import Select from "msuite_storybook/dist/select/Select";
import Input from "msuite_storybook/dist/input/Input";
import Button from "msuite_storybook/dist/button/Button";
import { RiSave3Fill } from "react-icons/ri";

const Condition = ({
  conditionObj,
  columns,
  onChange,
  translate,
  handleSave,
}) => {
  const displayedColumns = useMemo(() => {
    return [
      { value: null, display: "" },
      ...(columns || [])
        .filter((c) => c.usable_for_conditions === 1)
        .map((c) => ({
          id: c.id,
          value: c.id,
          display: c.display_name,
        }))
        .sort((a, b) => {
          if (a.display.toLowerCase() < b.display.toLowerCase()) {
            return -1;
          } else return 1;
        }),
    ];
  }, [columns]);

  const displayedEvaluations = useMemo(() => {
    return [
      { value: null, display: "" },
      { value: "Contains", display: translate("Contains") },
      { value: "Does Not Contain", display: translate("Does Not Contain") },
      { value: "Greater Than", display: translate("Greater Than") },
      { value: "Less Than", display: translate("Less Than") },
      {
        value: "Greater Than or Equal To",
        display: translate("Greater Than or Equal To"),
      },
      {
        value: "Less Than or Equal To",
        display: translate("Less Than or Equal To"),
      },
      {
        value: "Equal To",
        display: translate("Equal To"),
      },
      {
        value: "Not Equal To",
        display: translate("Not Equal To"),
      },
      {
        value: "Is Blank",
        display: translate("Is Blank"),
      },
      {
        value: "Is Not Blank",
        display: translate("Is Not Blank"),
      },
    ];
  }, []);

  const checkDisabled = useCallback(() => {
    if (
      conditionObj.filter &&
      conditionObj.comparison &&
      (["Is Blank", "Is Not Blank"].includes(conditionObj.comparison) ||
        (conditionObj.condition && conditionObj.condition.trim()))
    )
      return false;
    else return true;
  }, [conditionObj]);

  return (
    <div className="condition">
      <Select
        options={displayedColumns}
        value={conditionObj.filter}
        onInput={(e) => onChange(e.target.value, "filter")}
        placeholder={translate("-- Column --")}
      />
      <Select
        options={displayedEvaluations}
        value={conditionObj.comparison}
        onInput={(e) => onChange(e.target.value, "comparison")}
        placeholder={translate("-- Comparison --")}
      />
      {!["Is Blank", "Is Not Blank"].includes(conditionObj.comparison) && (
        <Input
          value={conditionObj.condition}
          onChange={(e) => onChange(e.target.value, "condition")}
          placeholder={translate("-- Condition --")}
        />
      )}
      <Button className="save" disabled={checkDisabled()} onClick={handleSave}>
        <RiSave3Fill />
      </Button>
    </div>
  );
};

export default Condition;
