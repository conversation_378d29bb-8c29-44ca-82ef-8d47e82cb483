export const getConditionIds = (conditions) => {
  let result = [];

  // add top level condition group ids
  result.push(...conditions.map((c) => c.id));

  // check if current condition level has any more sub groups (if current level doesn't include work_item_condition_id property we need to go deeper)
  if (conditions.filter((c) => !c.work_item_condition_id).length) {
    conditions
      .filter((c) => !c.work_item_condition_id)
      .forEach((c) => {
        if (c.conditions) {
          result = result.concat(getConditionIds(c.conditions));
        }
      });
  }

  return result;
};

const flowsUtils = {
  getConditionIds,
};

export default flowsUtils;
