const initialState = {
  flows: [],
  stages: [],
  workStages: [],
  currentFlowMap: null,
  workStageColumns: [],
  conditions: null,
  workItemColumns: [],
  stageConditions: null,
  itemOpenConditions: [],
  shippingBlocks: [],
  flowCodes: [],
  workStageGroupableColumns: null,
  workStageGroupableColumnsOptions: [],
  stageCodes: [],
  isLoading: false,
  isLoadingStages: false,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_STAGES_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_STAGES_SUCCEEDED":
      return { ...state, isLoading: false, error: false, stages: payload };
    case "RECEIVE_STAGES_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_WORK_STAGES_STARTED":
    case "RECEIVE_WORK_STAGES_GET_STARTED":
      return { ...state, isLoadingStages: true, error: null };
    case "RECEIVE_WORK_STAGES_SUCCEEDED":
    case "RECEIVE_WORK_STAGES_GET_SUCCEEDED":
      return {
        ...state,
        isLoadingStages: false,
        error: false,
        workStages: payload,
      };
    case "RECEIVE_WORK_STAGES_FAILED":
    case "RECEIVE_WORK_STAGES_GET_FAILED":
      return {
        ...state,
        isLoadingStages: false,
        error: payload,
        workStages: [],
      };
    case "RECEIVE_FLOWS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_FLOWS_SUCCEEDED":
      return { ...state, isLoading: false, error: false, flows: payload };
    case "RECEIVE_FLOWS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_FLOW_MAP_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_FLOW_MAP_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        currentFlowMap: payload,
      };
    case "RECEIVE_FLOW_MAP_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_WORK_STAGE_COLUMNS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_WORK_STAGE_COLUMNS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        workStageColumns: payload,
      };
    case "RECEIVE_WORK_STAGE_COLUMNS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        workStageColumns: [],
      };
    case "RECEIVE_UPDATED_FLOW_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_UPDATED_FLOW_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        flows: state.flows.map((f) => (f.id === payload.id ? payload : f)),
      };
    case "RECEIVE_UPDATED_FLOW_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
      };
    case "RECEIVE_CONDITIONS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_CONDITIONS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, conditions: payload };
    case "RECEIVE_CONDITIONS_FAILED":
      return { ...state, isLoading: false, error: payload, conditions: [] };
    case "RECEIVE_WORK_ITEM_COLUMNS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_WORK_ITEM_COLUMNS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        workItemColumns: payload,
      };
    case "RECEIVE_WORK_ITEM_COLUMNS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_WORK_STAGE_CONDITIONS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_WORK_STAGE_CONDITIONS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        stageConditions: payload,
      };
    case "RECEIVE_WORK_STAGE_CONDITIONS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        stageConditions: null,
      };
    case "RECEIVE_ITEM_OPEN_CONDITIONS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_ITEM_OPEN_CONDITIONS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        itemOpenConditions: payload,
      };
    case "RECEIVE_ITEM_OPEN_CONDITIONS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        itemOpenConditions: [],
      };
    case "RECEIVE_SHIPPING_BLOCKS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_SHIPPING_BLOCKS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        shippingBlocks: payload,
      };
    case "RECEIVE_SHIPPING_BLOCKS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_FLOW_CODES_SUCCEEDED":
      return {
        ...state,
        flowCodes: payload,
      };
    case "RECEIVE_FLOW_CODES_FAILED":
      return { ...state, flowCodes: [] };
    case "RECEIVE_WS_GROUPABLE_COLUMNS_OPTIONS_STARTED":
      return { ...state, workStageGroupableColumns: null };
    case "RECEIVE_WS_GROUPABLE_COLUMNS_SUCCEEDED":
      return { ...state, workStageGroupableColumns: payload };
    case "RECEIVE_WS_GROUPABLE_COLUMNS_FAILED":
      return { ...state, workStageGroupableColumns: [] };
    case "RECEIVE_STAGE_CODES_SUCCEEDED":
      return {
        ...state,
        stageCodes: payload,
      };
    case "RECEIVE_STAGE_CODES_FAILED":
      return { ...state, stageCodes: [] };
    case "RECEIVE_WS_GROUPABLE_COLUMNS_OPTIONS_SUCCEEDED":
      return { ...state, workStageGroupableColumnsOptions: payload };
    case "RECEIVE_WS_GROUPABLE_COLUMNS_OPTIONS_FAILED":
      return { ...state, workStageGroupableColumnsOptions: [] };
    default:
      return state;
  }
}
