// NPM PACKAGE IMPORTS
import React, { useState } from "react";

// COMPONENT IMPORTS
import Filter from "../reusable/filter/Filter";

const WorkItemColumns = ({
  workItemColumnsList,
  selectedColumns,
  setSelectedColumns,
}) => {
  const [displayColumnSearch, setDisplayColumnSearch] = useState("");

  return (
    <Filter
      nameKey="display_name"
      idKey="id"
      type="Item Columns"
      list={workItemColumnsList}
      selected={selectedColumns}
      setSelected={setSelectedColumns}
      handleParentSelect={setSelectedColumns}
      toggleAllSelections={(f) => f}
      selectAll
      orientation="HORIZONTAL"
      smallView
      searchInput={displayColumnSearch}
      setSearchInput={setDisplayColumnSearch}
    />
  );
};

export default WorkItemColumns;
