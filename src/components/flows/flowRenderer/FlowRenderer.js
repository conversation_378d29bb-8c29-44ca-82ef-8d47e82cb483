// NPM PACKAGE IMPORTS
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useSelector } from "react-redux";
import ReactFlow, {
  Background,
  Controls,
  MiniMap,
  isNode,
  isEdge,
} from "react-flow-renderer";
import { BsFillPlusCircleFill } from "react-icons/bs";

// REDUX ACTIONS

// COMPONENT IMPORTS
import NoHandlesNode from "./nodeTypes/NoHandlesNode";
import StartingNode from "./nodeTypes/StartingNode";
import StandardNode from "./nodeTypes/StandardNode";
import StagesDropdownNode from "./nodeTypes/StagesDropdownNode";
import EditLinkPanel from "./editingPanels/EditLinkPanel";
import StatusColorSelector from "./statusColorSelector/StatusColorSelector";

// STYLE IMPORTS
import "./stylesFlowRenderer.scss";
import "./editingPanels/stylesRightPanel.scss";
import {
  green,
  yellow,
  orange,
  red,
  field<PERSON>ro<PERSON>range,
  lighter<PERSON>rey,
  fabProBlue,
  bodyD<PERSON><PERSON>rey,
} from "../../styles/colors";

const FlowRenderer = ({
  currentFlowState,
  setCurrentFlowState,
  setInFlow,
  shippingBlockUsed,
  toggleShippingBlockUsed,
  setStatusColorUpdated,
  canEdit,
  selectedStage,
  setSelectedStage,
  updateSelectedStage,
  reactFlowInstance,
  setReactFlowInstance,
  rightPanelAction,
  selectedNode,
  setSelectedNode,
  selectedLink,
  setSelectedLink,
  statusColorDropdownInfo,
  setStatusColorDropdownInfo,
  onElementsRemove,
}) => {
  const [stagesDropdownOpen, setStagesDropdownOpen] = useState(null);
  const [parentNodeInfo, setParentNodeInfo] = useState(null);
  const [statusColorHovered, toggleStatusColorHovered] = useState(false);

  const { currentFlowMap, workStages, shippingBlocks } = useSelector(
    (state) => state.flowsData
  );

  const reactFlowWrapper = useRef(null);

  useEffect(() => {
    if (reactFlowInstance) {
      const reactFlowPaneEl = document.getElementsByClassName(
        "react-flow__pane"
      );

      if (reactFlowPaneEl) {
        reactFlowPaneEl[0].removeEventListener(
          "click",
          reactFlowPanelClickHandler
        );
        reactFlowPaneEl[0].addEventListener(
          "click",
          reactFlowPanelClickHandler
        );
      }
    }
  }, [reactFlowInstance]);

  useEffect(() => {
    if (currentFlowMap) {
      const nodes = currentFlowMap.work_flow_map.sort(
        (a, b) => a.stage_order - b.stage_order
      );
      const links = currentFlowMap.work_flow_link;

      const convertedNodes = nodes.map((n) => {
        const parsedPos = JSON.parse(n.coordinate);
        let label = "";

        switch (n.logic_gate_id) {
          case 1:
            label = "AND";
            break;
          case 2:
            label = "OR";
            break;
          default:
            label = n.stage_name;
            break;
        }

        if (n.shipping_block_position && !shippingBlockUsed)
          toggleShippingBlockUsed(true);
        return {
          id: n.node_id.toString(),
          position: { x: parsedPos.x, y: parsedPos.y },
          data: {
            label,
            stageId: n.stage_id,
            stageName: n.stage_name,
            nodeId: n.node_id,
            startingNode: n.stage_order === 1 ? true : false,
            logicGate: n.logic_gate_id ? n.logic_gate_id : null,
            shippingBlockPosition: n.shipping_block_position,
            holdpoint: n.shape === "Diamond" ? true : false,
            nestable: !!n.nestable,
            groupable: !!n.groupable,
            rejectable: !!n.rejectable,
            stageCodeId: n.stage_code_id,
            stageCodeName: n.stage_code_name,
            includeInJointLog: !!n.stage_report,
            stageStatusGroupName: n.stage_status_group_name,
            stageStatusGroupColor: n.stage_status_group_color,
            stageWorkLevelId: n.stage_work_level_id,
            metricWorkItemColumnId: n.metric_work_item_column_id,
            defaultWeight: n.default_weight,
            onHandleClick: (event) =>
              setParentNodeInfo({
                id: n.node_id.toString(),
                data: {
                  stageName: n.stage_name,
                  logicGate: n.logic_gate_id ? n.logic_gate_id : null,
                },
              }),
            onStatusGroupClick: setStatusColorDropdownInfo,
            onStatusColorMouseEnter: () => toggleStatusColorHovered(true),
            onStatusColorMouseLeave: () => toggleStatusColorHovered(false),
          },
          type: n.stage_order === 1 ? "starting-node" : "standard-node",
        };
      });

      const convertedLinks = links
        .map((l) => {
          let stroke;

          switch (l.item_open_condition_id) {
            case 1:
              stroke = green;
              break;
            case 2:
              stroke = yellow;
              break;
            case 3:
              stroke = orange;
              break;
            case 4:
              stroke = red;
              break;
            default:
              break;
          }

          const sourceNode = convertedNodes.find(
            (n) => n.id === l.from_node_id.toString()
          );
          const targetNode = convertedNodes.find(
            (n) => n.id === l.to_node_id.toString()
          );

          if (!sourceNode || !targetNode) return null;

          return {
            id: `${l.from_node_id}-${l.to_node_id}`,
            source: l.from_node_id.toString(),
            target: l.to_node_id.toString(),
            arrowHeadType: "arrowclosed",
            style: { strokeWidth: 5, stroke, cursor: "pointer" },
            type: "smoothstep",
            data: {
              itemOpenConditionId: l.item_open_condition_id,
              drawingLevelLink: !!sourceNode?.data.shippingBlockPosition,
              sourceStageName:
                sourceNode.data.stageName ||
                (sourceNode.data.logicGate === 1 ? "AND" : "OR"),
              targetStageName:
                targetNode.data.stageName ||
                (targetNode.data.logicGate === 1 ? "AND" : "OR"),
            },
          };
        })
        .filter((link) => link);

      return setCurrentFlowState([...convertedNodes, ...convertedLinks]);
    } else return [];
  }, [currentFlowMap]);

  useEffect(() => {
    if (reactFlowInstance && parentNodeInfo) {
      const elements = reactFlowInstance.getElements();
      const parentElement = elements.find((el) => el.id === parentNodeInfo.id);
      const blankFlowNode = elements.find((el) => el.id === "blank-flow");

      if (parentElement) {
        setStagesDropdownOpen(true);
        setCurrentFlowState([
          {
            id: "stages-dropdown",
            position: {
              x: parentElement.position.x,
              y: parentElement.position.y + (blankFlowNode ? 0 : 100),
            },
            data: {
              stages: displayedStages,
              handleClose: () => {
                setCurrentFlowState(
                  currentFlowState
                    ? currentFlowState.filter((n) => n.id !== "stages-dropdown")
                    : []
                );
                setParentNodeInfo(null);
                setStagesDropdownOpen(false);
              },
              handleSelection,
            },
            type: "stages-dropdown-node",
          },
          ...(currentFlowState
            ? currentFlowState.filter(
                (n) => n.id !== "stages-dropdown" && n.id !== "blank-flow"
              )
            : []),
        ]);
      }
    }
  }, [reactFlowInstance, parentNodeInfo]);

  const reactFlowPanelClickHandler = () => {
    if (reactFlowInstance) {
      setCurrentFlowState(
        reactFlowInstance
          .getElements()
          .filter((el) => el.id !== "stages-dropdown")
      );
      setStagesDropdownOpen(false);
      setParentNodeInfo(null);
      setSelectedLink(null);
      setSelectedNode(null);
      setSelectedStage(null);
      setStatusColorDropdownInfo(null);
    }
  };

  const onLoad = (rfi) => {
    if (!reactFlowInstance) {
      setReactFlowInstance(rfi);
    }
  };

  const onElementClick = (event, el) => {
    if (!canEdit || stagesDropdownOpen || el.id === "blank-flow") return;
    if (isEdge(el)) {
      if (el.data.drawingLevelLink) {
        setSelectedLink(null);
      } else {
        setSelectedLink(el);
      }

      setSelectedNode(null);
      setSelectedStage(null);
    }

    if (isNode(el)) {
      if (statusColorHovered) {
        setSelectedNode(null);
      } else {
        setSelectedNode(el);
        setInFlow(true);
      }

      setSelectedLink(null);
      setSelectedStage(null);
    }
  };

  const onConnect = (params) => {
    if (!canEdit) return;
    const sourceStage = currentFlowState.find(
      (el) => isNode(el) && el.id === params.source
    );
    const targetStage = currentFlowState.find(
      (el) => isNode(el) && el.id === params.target
    );
    const existingExactLink = currentFlowState.find(
      (el) =>
        isEdge(el) && el.target === params.target && el.source === params.source
    );
    const existingTargetStageLink = currentFlowState.find(
      (el) => isEdge(el) && el.target === params.target
    );

    if (sourceStage.data.stageWorkLevelId === 4) return;
    if (
      targetStage.data.stageWorkLevelId === 4 &&
      targetStage.data.shippingBlockPosition !== 1
    )
      return;
    if (existingExactLink) return;
    if (!targetStage.data.logicGate && existingTargetStageLink) return;

    const newLink = {
      id: `${params.source}-${params.target}`,
      source: params.source,
      target: params.target,
      arrowHeadType: "arrowclosed",
      type: "smoothstep",
      style: { strokeWidth: 5, stroke: green, cursor: "pointer" },
      data: {
        itemOpenConditionId: 1,
        drawingLevelLink: false,
        sourceStageName: sourceStage.data.stageName,
        targetStageName: targetStage.data.stageName,
      },
    };

    setCurrentFlowState((els) => els.concat(newLink));
  };

  const onDragOver = (event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  };

  const onDrop = (event) => {
    event.preventDefault();

    const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
    const newNodeData = JSON.parse(
      event.dataTransfer.getData("application/reactflow")
    );
    const position = reactFlowInstance.project({
      x: event.clientX - reactFlowBounds.left,
      y: event.clientY - reactFlowBounds.top,
    });
    const elements = currentFlowState.filter(
      (n) => isNode(n) && n.id !== "blank-flow"
    );
    const startingNode = elements.find((el) => el.data.startingNode);
    const nodeIds = elements.map((el) =>
      isNode(el) ? el.data.nodeId || null : null
    );
    const nodeId = nodeIds.length ? Math.max(...nodeIds) + 1 : 1;
    const inFabricationStatusGroup = elements.find(
      (el) => el.data.stageStatusGroupName === "In Fabrication"
    );

    let newElements = [];

    if (newNodeData.stageName === "SHIPPING BLOCK") {
      if (!shippingBlockUsed) {
        toggleShippingBlockUsed(true);
        const generalShippingStages = shippingBlocks[0].stages;

        // NEW NODES
        newElements.push(
          {
            id: nodeId.toString(),
            position: position,
            data: {
              label: generalShippingStages[0].name,
              stageId: generalShippingStages[0].id,
              stageName: generalShippingStages[0].name,
              nodeId,
              startingNode: false,
              logicGate: null,
              shippingBlockPosition: 1,
              holdpoint: false,
              stageStatusGroupName: "Loading",
              stageStatusGroupColor: "#000000",
              stageWorkLevelId: generalShippingStages[0].stage_work_level_id,
              defaultWeight: generalShippingStages[0].default_weight,
              onHandleClick: (f) => f,
              onStatusGroupClick: setStatusColorDropdownInfo,
              onStatusColorMouseEnter: () => toggleStatusColorHovered(true),
              onStatusColorMouseLeave: () => toggleStatusColorHovered(false),
            },
            type: "standard-node",
          },
          {
            id: (nodeId + 1).toString(),
            position: { x: position.x, y: position.y + 100 },
            data: {
              label: generalShippingStages[1].name,
              stageId: generalShippingStages[1].id,
              stageName: generalShippingStages[1].name,
              nodeId: nodeId + 1,
              startingNode: false,
              logicGate: null,
              shippingBlockPosition: 2,
              holdpoint: false,
              stageStatusGroupName: "Delivery",
              stageStatusGroupColor: "#000000",
              stageWorkLevelId: generalShippingStages[1].stage_work_level_id,
              defaultWeight: generalShippingStages[1].default_weight,
              onHandleClick: (f) => f,
              onStatusGroupClick: setStatusColorDropdownInfo,
              onStatusColorMouseEnter: () => toggleStatusColorHovered(true),
              onStatusColorMouseLeave: () => toggleStatusColorHovered(false),
            },
            type: "standard-node",
          },
          {
            id: (nodeId + 2).toString(),
            position: { x: position.x, y: position.y + 200 },
            data: {
              label: generalShippingStages[2].name,
              stageId: generalShippingStages[2].id,
              stageName: generalShippingStages[2].name,
              nodeId: nodeId + 2,
              startingNode: false,
              logicGate: null,
              shippingBlockPosition: 3,
              holdpoint: false,
              stageStatusGroupName: "Receiving",
              stageStatusGroupColor: "#000000",
              stageWorkLevelId: generalShippingStages[2].stage_work_level_id,
              defaultWeight: generalShippingStages[2].default_weight,
              onHandleClick: (f) => f,
              onStatusGroupClick: setStatusColorDropdownInfo,
              onStatusColorMouseEnter: () => toggleStatusColorHovered(true),
              onStatusColorMouseLeave: () => toggleStatusColorHovered(false),
            },
            type: "standard-node",
          }
        );

        // NEW LINKS
        newElements.push(
          {
            id: `${nodeId}-${nodeId + 1}`,
            source: nodeId.toString(),
            target: (nodeId + 1).toString(),
            arrowHeadType: "arrowclosed",
            style: { strokeWidth: 5, stroke: yellow, cursor: "pointer" },
            type: "smoothstep",
            data: {
              itemOpenConditionId: 2,
              drawingLevelLink: true,
              sourceStageName: "Loading",
              targetStageName: "Delivery",
            },
          },
          {
            id: `${nodeId + 1}-${nodeId + 2}`,
            source: (nodeId + 1).toString(),
            target: (nodeId + 2).toString(),
            arrowHeadType: "arrowclosed",
            style: { strokeWidth: 5, stroke: yellow, cursor: "pointer" },
            type: "smoothstep",
            data: {
              itemOpenConditionId: 2,
              drawingLevelLink: true,
              sourceStageName: "Delivery",
              targetStageName: "Receiving",
            },
          }
        );
      }
    } else {
      newElements.push({
        id: nodeId.toString(),
        type: startingNode ? "standard-node" : "starting-node",
        position,
        data: {
          ...newNodeData,
          nodeId,
          stageStatusGroupColor: inFabricationStatusGroup
            ? inFabricationStatusGroup.data.stageStatusGroupColor
            : "#000000",
          startingNode: startingNode ? false : true,
          onHandleClick: (event) =>
            setParentNodeInfo({
              id: nodeId.toString(),
              data: {
                stageName: newNodeData.stageName,
                logicGate: newNodeData.logicGate,
              },
            }),
          onStatusGroupClick: setStatusColorDropdownInfo,
          onStatusColorMouseEnter: () => toggleStatusColorHovered(true),
          onStatusColorMouseLeave: () => toggleStatusColorHovered(false),
        },
      });
    }

    setCurrentFlowState((els) => [
      ...els.filter((el) => el.id !== "blank-flow"),
      ...newElements,
    ]);
  };

  const handleSelection = (stage, shippingBlock = false) => {
    const elements = reactFlowInstance.getElements();
    const blankFlowNode = elements.filter((el) => el.id === "blank-flow")[0];
    const existingElements = elements.filter(
      (el) => el.id !== "blank-flow" && el.id !== "stages-dropdown"
    );
    const nodeIds = existingElements
      .map((el) => (isNode(el) ? el.data.nodeId || null : null))
      .filter((n) => n);
    const nodeId = nodeIds.length ? Math.max(...nodeIds) + 1 : 1;
    const stagesDropdownElement = elements.find(
      (el) => el.id === "stages-dropdown"
    );

    let newElements = [];

    if (shippingBlock === false) {
      newElements = [
        {
          id: nodeId.toString(),
          position: blankFlowNode
            ? blankFlowNode.position
            : stagesDropdownElement
            ? stagesDropdownElement.position
            : { x: 100, y: 100 },
          data: {
            label: stage.name,
            stageId: stage.id,
            stageName: stage.name,
            nodeId,
            startingNode: existingElements.length ? false : true,
            logicGate: null,
            shippingBlockPosition: null,
            holdpoint: stage.shape === "Diamond" ? true : false,
            nestable: !!stage.nestable,
            groupable: !!stage.groupable,
            rejectable: !!stage.rejectable,
            stageCodeId: stage.stage_code_id,
            stageCodeName: stage.stage_code_name,
            includeInJointLog: 0,
            stageStatusGroupName: "In Fabrication",
            stageStatusGroupColor: "#000000",
            stageWorkLevelId: stage.stage_work_level_id,
            defaultWeight: stage.default_weight,
            onHandleClick: (event) =>
              setParentNodeInfo({
                id: nodeId.toString(),
                data: {
                  stageName: stage.name,
                  logicGate: null,
                },
                type: existingElements.length
                  ? "standard-node"
                  : "starting-node",
              }),
            onStatusGroupClick: setStatusColorDropdownInfo,
            onStatusColorMouseEnter: () => toggleStatusColorHovered(true),
            onStatusColorMouseLeave: () => toggleStatusColorHovered(false),
          },
          type: existingElements.length ? "standard-node" : "starting-node",
        },
      ];
    } else {
      toggleShippingBlockUsed(true);
      const generalShippingStages = shippingBlocks[0].stages;
      newElements = [
        {
          id: nodeId.toString(),
          position: stagesDropdownElement
            ? stagesDropdownElement.position
            : { x: 100, y: 100 },
          data: {
            label: generalShippingStages[0].name,
            stageId: generalShippingStages[0].id,
            stageName: generalShippingStages[0].name,
            nodeId,
            startingNode: false,
            logicGate: null,
            shippingBlockPosition: 1,
            holdpoint: false,
            stageStatusGroupName: "Loading",
            stageStatusGroupColor: "#000000",
            stageWorkLevelId: generalShippingStages[0].stage_work_level_id,
            defaultWeight: generalShippingStages[0].default_weight,
            onHandleClick: (f) => f,
            onStatusGroupClick: setStatusColorDropdownInfo,
            onStatusColorMouseEnter: () => toggleStatusColorHovered(true),
            onStatusColorMouseLeave: () => toggleStatusColorHovered(false),
          },
          type: "standard-node",
        },
        {
          id: (nodeId + 1).toString(),
          position: stagesDropdownElement
            ? {
                x: stagesDropdownElement.position.x,
                y: stagesDropdownElement.position.y + 100,
              }
            : { x: 100, y: 200 },
          data: {
            label: generalShippingStages[1].name,
            stageId: generalShippingStages[1].id,
            stageName: generalShippingStages[1].name,
            nodeId: nodeId + 1,
            startingNode: false,
            logicGate: null,
            shippingBlockPosition: 2,
            holdpoint: false,
            stageStatusGroupName: "Delivery",
            stageStatusGroupColor: "#000000",
            stageWorkLevelId: generalShippingStages[1].stage_work_level_id,
            defaultWeight: generalShippingStages[1].default_weight,
            onHandleClick: (f) => f,
            onStatusGroupClick: setStatusColorDropdownInfo,
            onStatusColorMouseEnter: () => toggleStatusColorHovered(true),
            onStatusColorMouseLeave: () => toggleStatusColorHovered(false),
          },
          type: "standard-node",
        },
        {
          id: (nodeId + 2).toString(),
          position: stagesDropdownElement
            ? {
                x: stagesDropdownElement.position.x,
                y: stagesDropdownElement.position.y + 200,
              }
            : { x: 100, y: 300 },
          data: {
            label: generalShippingStages[2].name,
            stageId: generalShippingStages[2].id,
            stageName: generalShippingStages[2].name,
            nodeId: nodeId + 2,
            startingNode: false,
            logicGate: null,
            shippingBlockPosition: 3,
            holdpoint: false,
            stageStatusGroupName: "Receiving",
            stageStatusGroupColor: "#000000",
            stageWorkLevelId: generalShippingStages[2].stage_work_level_id,
            defaultWeight: generalShippingStages[2].default_weight,
            onHandleClick: (f) => f,
            onStatusGroupClick: setStatusColorDropdownInfo,
            onStatusColorMouseEnter: () => toggleStatusColorHovered(true),
            onStatusColorMouseLeave: () => toggleStatusColorHovered(false),
          },
          type: "standard-node",
        },
      ];
    }

    if (parentNodeInfo && parentNodeInfo.data) {
      if (shippingBlock === false) {
        newElements.push({
          id: `${parentNodeInfo.id}-${nodeId}`,
          source: parentNodeInfo.id.toString(),
          target: nodeId.toString(),
          arrowHeadType: "arrowclosed",
          style: { strokeWidth: 5, stroke: green, cursor: "pointer" },
          type: "smoothstep",
          data: {
            itemOpenConditionId: 1,
            drawingLevelLink: false,
            sourceStageName:
              parentNodeInfo.data.stageName ||
              (parentNodeInfo.data.logicGate === 1 ? "AND" : "OR"),
            targetStageName: stage.name,
          },
        });
      } else {
        newElements.push(
          {
            id: `${parentNodeInfo.id}-${nodeId}`,
            source: parentNodeInfo.id.toString(),
            target: nodeId.toString(),
            arrowHeadType: "arrowclosed",
            style: { strokeWidth: 5, stroke: green, cursor: "pointer" },
            type: "smoothstep",
            data: {
              itemOpenConditionId: 1,
              drawingLevelLink: false,
              sourceStageName: parentNodeInfo.data.stageName,
              targetStageName: "Loading",
            },
          },
          {
            id: `${nodeId}-${nodeId + 1}`,
            source: nodeId.toString(),
            target: (nodeId + 1).toString(),
            arrowHeadType: "arrowclosed",
            style: { strokeWidth: 5, stroke: yellow, cursor: "pointer" },
            type: "smoothstep",
            data: {
              itemOpenConditionId: 2,
              drawingLevelLink: true,
              sourceStageName: "Loading",
              targetStageName: "Delivery",
            },
          },
          {
            id: `${nodeId + 1}-${nodeId + 2}`,
            source: (nodeId + 1).toString(),
            target: (nodeId + 2).toString(),
            arrowHeadType: "arrowclosed",
            style: { strokeWidth: 5, stroke: yellow, cursor: "pointer" },
            type: "smoothstep",
            data: {
              itemOpenConditionId: 2,
              drawingLevelLink: true,
              sourceStageName: "Delivery",
              targetStageName: "Receiving",
            },
          }
        );
      }
    }

    setCurrentFlowState([
      ...newElements,
      ...existingElements.filter((n) => n.id !== "stages-dropdown"),
    ]);
    setStagesDropdownOpen(false);
    setParentNodeInfo(null);
  };

  const updateSelectedLink = (newItemOpenConditionId) => {
    let newStrokeColor;

    switch (newItemOpenConditionId) {
      case 1:
        newStrokeColor = green;
        break;
      case 2:
        newStrokeColor = yellow;
        break;
      case 3:
        newStrokeColor = orange;
        break;
      case 4:
        newStrokeColor = red;
        break;
      default:
        break;
    }

    setCurrentFlowState((els) => {
      for (let el of els) {
        if (el.id === selectedLink.id) {
          el.style = { ...el.style, stroke: newStrokeColor };
          el.data.itemOpenConditionId = newItemOpenConditionId;
          break;
        }
      }

      return [...els];
    });
  };

  const updateStatusColor = (
    statusGroup = "In Fabrication",
    newStatusColor
  ) => {
    setStatusColorDropdownInfo(null);
    setStatusColorUpdated(true);
    setCurrentFlowState((els) => {
      for (let el of els) {
        // if the name is not set, stage is not part of flow yet
        // so use default "In Fabrication" as the status group
        if (!el.data.stageStatusGroupName && statusGroup === "In Fabrication") {
          el.data.stageStatusGroupColor = newStatusColor;
        }
        if (el.data.stageStatusGroupName === statusGroup) {
          el.data.stageStatusGroupColor = newStatusColor;
        }
      }

      return [...els];
    });
  };

  const displayedElements = useMemo(() => {
    if (currentFlowState && currentFlowState.length) {
      return currentFlowState;
    } else {
      return [
        {
          id: "blank-flow",
          position: { x: 100, y: 100 },
          data: {
            label: (
              <div
                className="blank-flow-node"
                onClick={() =>
                  setParentNodeInfo({
                    id: "blank-flow",
                  })
                }
              >
                <BsFillPlusCircleFill />
                <p>Click to add a stage</p>
              </div>
            ),
          },
          type: "no-handles",
        },
      ];
    }
  }, [currentFlowState]);

  const displayedStages = useMemo(() => {
    if (workStages && currentFlowState) {
      const shippingBlockOption = {
        id: 0,
        name: "Shipping Block",
      };
      let result = workStages.filter(
        (s) =>
          !currentFlowState.find((n) => isNode(n) && n.data.stageId === s.id)
      );

      if (
        !shippingBlockUsed &&
        currentFlowState.filter(
          (n) => n.id !== "blank-flow" && n.id !== "stages-dropdown"
        ).length
      ) {
        result.unshift(shippingBlockOption);
      }

      return result;
    } else return [];
  }, [workStages, currentFlowState, shippingBlockUsed]);

  return (
    <div
      className={`flow-renderer ${
        rightPanelAction === "CREATE" ? "panel-view" : ""
      } `}
      ref={reactFlowWrapper}
    >
      <ReactFlow
        elements={displayedElements}
        nodeTypes={{
          "no-handles": NoHandlesNode,
          "starting-node": StartingNode,
          "standard-node": StandardNode,
          "stages-dropdown-node": StagesDropdownNode,
        }}
        onLoad={onLoad}
        onElementClick={onElementClick}
        onElementsRemove={onElementsRemove}
        onConnect={onConnect}
        onDragOver={onDragOver}
        onDrop={onDrop}
        deleteKeyCode={46}
        zoomOnScroll={!stagesDropdownOpen}
        connectionLineType="smoothstep"
        nodesConnectable={canEdit}
        nodesDraggable={canEdit}
        elementsSelectable={canEdit}
        selectNodesOnDrag={false}
        snapToGrid={true}
        snapGrid={[50, 50]}
      >
        <MiniMap
          style={{ backgroundColor: bodyDarkGrey }}
          nodeBorderRadius={0}
          nodeStrokeWidth={5}
          nodeStrokeColor={(n) => {
            if (n.data.logicGate || n.data.holdpoint) return;
            if (n.data.startingNode) return fieldProOrange;
            if (n.data.shippingBlockPosition) return "rgb(81, 130, 74)";

            return fabProBlue;
          }}
          nodeColor={(n) => {
            if (n.data.logicGate === 1) return yellow;
            if (n.data.logicGate === 2) return fieldProOrange;
            if (n.data.shippingBlockPosition) return "rgb(81, 130, 74)";
            if (n.data.holdpoint) return fabProBlue;

            return lighterGrey;
          }}
          nodeClassName={(n) => {
            if (n.data.logicGate === 1) return "triangle";
            if (n.data.logicGate === 2) return "circle";
            if (n.data.holdpoint) return "diamond";
          }}
        />
        <Controls showInteractive={false} />
        <Background color="#aaa" gap={16} />
      </ReactFlow>
      <div className="link-color-legend">
        <h2 className="title">Link Legend</h2>
        <span className="color green"></span>
        <span className="name">Open</span>
        <span className="color yellow"></span>
        <span className="name">Specific Item Open</span>
        <span className="color orange"></span>
        <span className="name">Locked - First Item</span>
        <span className="color red"></span>
        <span className="name">Locked - Last Item</span>
      </div>
      {selectedLink && (
        <EditLinkPanel
          selectedLink={selectedLink}
          deleteLink={() => onElementsRemove([selectedLink])}
          updateSelectedLink={updateSelectedLink}
        />
      )}
      {canEdit && statusColorDropdownInfo && (
        <StatusColorSelector
          info={statusColorDropdownInfo}
          handleClose={() => setStatusColorDropdownInfo(null)}
          updateStatusColor={updateStatusColor}
        />
      )}
    </div>
  );
};

export default FlowRenderer;
