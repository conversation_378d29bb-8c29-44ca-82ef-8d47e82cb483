@import "../../styles/colors.scss";

div.panel-view {
  & .react-flow__pane,
  .react-flow__renderer,
  .react-flow__selectionpane {
    position: static;
  }
}

div[data-id="blank-flow"] > div.blank-flow-node > svg {
  color: #b4b4b4;
  font-size: 50px;
  background-color: $fabProBlue;
  border-radius: 50%;
  transition: color 250ms ease;
  cursor: pointer;

  &:hover {
    color: #fff;
  }
}

div[data-id="blank-flow"] > div.blank-flow-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

div[data-id="blank-flow"] > div.blank-flow-node > p {
  color: $fabProBlue;
  font-weight: 600;
  font-size: 1rem;
}

div[data-id="blank-flow"].selectable,
div[data-id="blank-flow"].selected {
  background-color: transparent;
  border: none;
  box-shadow: unset;

  &:hover {
    box-shadow: unset;
  }
}

div.react-flow__node-stages-dropdown-node {
  z-index: 4 !important;
}

div.node {
  height: 75px;
  width: 150px;
  background-color: $lighterGrey;
  border: 1px solid $fabProBlue;
  border-radius: 3px;
  font-weight: 600;
  color: #fff;

  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;

  & > div.border > h2.shape-modifier {
    font-size: 1rem;
    margin: 0;
    padding: 0;
    font-weight: 600;
  }

  &:not(.holdpoint).starting-node {
    border-color: $fieldProOrange;
  }

  &.starting-node.holdpoint > div.border > h2.shape-modifier {
    background-color: $fieldProOrange;
  }

  &.holdpoint {
    border: none;
    background-color: transparent;
    height: 100px;

    & > div.border {
      padding: 1px;
      background-color: $yellow;
      clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
    }

    & > div.border > h2.shape-modifier {
      background-color: $fabProBlue;
      clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
      height: 100px;
      width: 150px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
    }
  }

  &.and-logic-gate,
  &.or-logic-gate {
    width: 100px;
    height: 100px;
  }

  &.and-logic-gate {
    color: #000;
    background-color: transparent;
    border: none;

    & > div.border {
      padding: 1px;
      background-color: $fabProBlue;
      clip-path: polygon(50% -5%, -5% 105%, 105% 105%);
    }

    & > div.border > h2.shape-modifier {
      clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
      background-color: $yellow;
      height: 100px;
      width: 100px;
      padding-top: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
    }
  }

  &.or-logic-gate {
    border-radius: 50%;
    background-color: $fieldProOrange;
  }

  &.shipping-block-node {
    background-color: rgb(81, 130, 74);
  }

  & > span.status-group-indicator {
    position: absolute;
    top: 5px;
    left: 5px;
    height: 15px;
    width: 15px;
    border-radius: 50%;
    border: 1px solid rgba(0, 0, 0, 1);
    cursor: pointer;
  }

  &.selected {
    border-width: 5px;
  }

  &.and-logic-gate.selected > div.border,
  &.holdpoint.selected > div.border {
    padding: 5px;
  }

  &.and-logic-gate.selected > div.border {
    background-color: $fabProBlue;
  }

  &.holdpoint.selected > div.border {
    background-color: $yellow;
  }
}

rect.react-flow__minimap-node {
  &.triangle {
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  }

  &.circle {
    clip-path: circle(50% at 50% 50%);
  }

  &.diamond {
    clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
  }
}
