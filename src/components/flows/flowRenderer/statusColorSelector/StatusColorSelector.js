// NPM PACKAGE IMPORTS
import React, { useRef } from "react";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../../hooks/useOutsideClick";

// STYLE IMPORTS
import "./stylesStatusColorSelector.scss";

const statusColors = [
  "#000080",
  "#0000ff",
  "#00bfff",
  "#00ffff",
  "#2e8b57",
  "#2f4f4f",
  "#4169e1",
  "#7fff00",
  "#808000",
  "#8b0000",
  "#98fb98",
  "#a9a9a9",
  "#da70d6",
  "#f08080",
  "#f0e68c",
  "#ff0000",
  "#ff00ff",
  "#ff1493",
  "#ff8c00",
  "#ffff00",
];

const StatusColorSelector = ({ info, handleClose, updateStatusColor }) => {
  const statusColorSelectorRef = useRef(null);
  useOutsideClick(statusColorSelectorRef, handleClose);

  return (
    <div
      ref={statusColorSelectorRef}
      className="status-color-selector"
      style={{
        top: info.position.y,
        left: info.position.x,
      }}
    >
      {statusColors.map((c) => (
        <div
          key={c}
          className={
            c.toLowerCase() === (info.statusColor || "").toLowerCase()
              ? "selected"
              : ""
          }
          onClick={() => updateStatusColor(info.statusGroup, c)}
          style={{ backgroundColor: c }}
        ></div>
      ))}
    </div>
  );
};

export default StatusColorSelector;
