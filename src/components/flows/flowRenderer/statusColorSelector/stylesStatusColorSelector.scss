div.status-color-selector {
  position: fixed;
  border: 1px solid #333;
  z-index: 6;

  display: grid;
  grid-template-rows: repeat(5, 30px);
  grid-template-columns: repeat(4, 30px);

  & > div {
    border: 1px solid #333;
    cursor: pointer;
    box-sizing: border-box;
    width: 30px;
    height: 30px;

    &.selected {
      border: 3px solid #fff;
    }
  }

  & > div:hover {
    transform: scale(1.2);
  }
}
