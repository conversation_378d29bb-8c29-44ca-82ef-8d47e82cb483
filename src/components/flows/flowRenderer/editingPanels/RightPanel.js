// NPM PACKAGE IMPORTS
import React, {
  useState,
  useRef,
  useEffect,
  useMemo,
  useCallback,
} from "react";
import { FaEdit, FaAsterisk, FaTrash } from "react-icons/fa";
import { BiSearch } from "react-icons/bi";
import { RiSave3Fill } from "react-icons/ri";
import { useDispatch, useSelector } from "react-redux";
import Button from "msuite_storybook/dist/button/Button";
import Select from "msuite_storybook/dist/select/Select";
import Input from "msuite_storybook/dist/input/Input";

// COMPONENT IMPORTS
import Filter from "../../../reusable/filter/Filter";
import WorkItemColumns from "../../WorkItemColumns";
import Conditions from "./Conditions";
import { textBasedFilters } from "../../conditions/utils";

// REDUX IMPORTS
import {
  handleFetchWorkStageConditions,
  handleClearWorkStageConditions,
  handleFetchConditions,
  handleUpdateStageStatusGroup,
  handleUpdateWorkStage,
  handleFetchWorkStageColumns,
  handleUpdateWorkStageColumns,
  handleFetchWorkItemColumns,
  handleFetchWorkStagesGet,
  handleFetchWorkStageGroupableColumns,
  handleFetchStageCodes,
  handleCreateStageCode,
  handleCreateWorkStage,
  handleDuplicateStage,
} from "../../flowsActions";
import {
  handleUpdateThroughputColumns,
  handleFetchThroughputColumns,
} from "../../../throughputs/throughputsActions";
import { notify } from "../../../reusable/alertPopup/alertPopupActions";

// TRANSLATION IMPORTS
import useTranslations from "../../../../hooks/useTranslations";
import flowsTranslations from "../../flowsTranslations.json";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../../hooks/useOutsideClick";
import { escapeRegExp, naturalSort } from "../../../../_utils";

import { stageWorkLevels } from "../../flowsEnums";

const RightPanel = ({
  type = "EDIT",
  selectedStage,
  setSelectedStage,
  removeStageFromFlow,
  inFlow,
  statusGroups,
  selectedFlow,
  updateStatusGroup,
  nodes,
  updateSelectedStage,
  setRightPanelAction,
  setConfirmationType,
  confirmationType,
  handleSelectStage,
}) => {
  const [editName, toggleEditName] = useState(false);
  const [nameInputValue, setNameInputValue] = useState("");
  const [statusGroup, setStatusGroup] = useState("In Fabrication");
  const [newStatusGroup, setNewStatusGroup] = useState("");
  const [showDropdown, toggleDropdown] = useState(false);
  const [searchInput, setSearchInput] = useState(null);
  const [holdpoint, setHoldpoint] = useState(false);
  const [selectedColumns, setSelectedColumns] = useState([]);
  const [selectedMetric, setSelectedMetric] = useState(null);
  const [nestable, setNestable] = useState(false);
  const [rejectable, setRejectable] = useState(false);
  const [groupable, setGroupable] = useState(false);
  const [selectedGroupableColumns, setSelectedGroupableColumns] = useState([]);
  const [materialNameObj, setMaterialNameObj] = useState(null);
  const [costCodeValue, setCostCodeValue] = useState(null);
  const [costCodeSearchValue, setCostCodeSearchValue] = useState("");
  const [showCostCodeDropdown, toggleCostCodeDropdown] = useState(false);
  const [includeInJointLog, toggleIncludeInJointLog] = useState(false);
  const [selectedThroughputColumns, setSelectedThroughputColumns] = useState(
    []
  );
  const [startingStage, setStartingStage] = useState(false);
  const [groupableColumnSearch, setGroupableColumnSearch] = useState("");
  const [isFormUnsaved, toggleFormUnsaved] = useState(false);
  const [showEditConditions, toggleShowEditConditions] = useState(false);

  // STAGE DUPLICATION STATE
  const [stageToDuplicate, setStageToDuplicate] = useState(null);
  const [conditionsToAdd, setConditionsToAdd] = useState(null);

  const {
    stageConditions,
    conditions,
    workStages,
    workStageColumns,
    workItemColumns,
    workStageGroupableColumnsOptions,
    stageCodes,
    isLoading: isStagesLoading,
  } = useSelector((state) => state.flowsData);
  const { permissions } = useSelector((state) => state.profileData);
  const { throughputColumns } = useSelector((state) => state.throughputsData);

  const panelRef = useRef(null);
  useOutsideClick(
    panelRef,
    showEditConditions || confirmationType
      ? (f) => f
      : () => {
          if (type !== "CREATE") return setSelectedStage(null);

          if (!isFormUnsaved) {
            setRightPanelAction(null);
            setSelectedStage(null);
          } else setConfirmationType("CANCEL");
        }
  );

  const dropdownRef = useRef(null);
  useOutsideClick(dropdownRef, () => {
    if (newStatusGroup && !statusGroup) {
      setSearchInput(null);
      setStatusGroup(newStatusGroup);
    }
    toggleDropdown(false);
  });

  const nameInputRef = useRef(null);
  useOutsideClick(nameInputRef, () => {
    if (type === "CREATE") return;
    if (editName && !nameInputRef.current.children[0].value) {
      setNameInputValue(selectedStage?.data?.stageName);
    }
    toggleEditName(false);
  });

  const costCodeRef = useRef(null);
  useOutsideClick(costCodeRef, () => toggleCostCodeDropdown(false));

  const translate = useTranslations(flowsTranslations);
  const dispatch = useDispatch();

  if (type === "CREATE") inFlow = false;

  const drawingLevelStage =
    type !== "CREATE" && selectedStage?.data?.stageWorkLevelId === 4;

  useEffect(() => {
    if (!inFlow || selectedStage?.data?.logicGate || type === "CREATE") return;

    toggleIncludeInJointLog(selectedStage.data?.includeInJointLog);
    dispatch(handleFetchThroughputColumns(selectedStage?.data?.stageId));
  }, [inFlow, selectedStage, type]);

  useEffect(() => {
    if (!drawingLevelStage && !selectedStage?.data?.logicGate) {
      if (type !== "CREATE") {
        dispatch(handleFetchStageCodes());
      }

      dispatch(handleFetchConditions());
      dispatch(handleFetchWorkItemColumns("work_items"));
    }
  }, []);

  useEffect(() => {
    if (
      workItemColumns &&
      workItemColumns.length &&
      !selectedStage?.data?.logicGate
    ) {
      const materialName = workItemColumns.find(
        (c) => c.normal_name === "material_name"
      );

      if (materialName) setMaterialNameObj(materialName);
    }
  }, [workItemColumns]);

  useEffect(() => {
    if (materialNameObj && !selectedStage?.data?.logicGate) {
      const initialSelected = [materialNameObj];
      if (!selectedColumns.length) setSelectedColumns(initialSelected);
      if (!selectedGroupableColumns.length)
        setSelectedGroupableColumns(initialSelected);
    }
  }, [materialNameObj]);

  useEffect(() => {
    if (materialNameObj && !selectedStage?.data?.logicGate) {
      setSelectedGroupableColumns([
        materialNameObj,
        ...workStageGroupableColumnsOptions.filter(
          (c) => c.group_by && c.id !== materialNameObj.id
        ),
      ]);
    } else
      setSelectedGroupableColumns(
        workStageGroupableColumnsOptions.filter((c) => c.group_by)
      );
  }, [workStageGroupableColumnsOptions, materialNameObj]);

  // INITIAL INPUT POPULATION
  useEffect(() => {
    if (type === "CREATE") {
      setNameInputValue("");
      setSelectedMetric(null);
      dispatch(handleClearWorkStageConditions);
      return;
    }

    setNameInputValue(
      selectedStage?.data?.stageName || selectedStage?.data?.label
    );
    toggleEditName(false);
    setStatusGroup(
      selectedStage?.data?.stageStatusGroupName || "In Fabrication"
    );
    setHoldpoint(selectedStage?.data?.holdpoint);
    setNestable(selectedStage?.data?.nestable);
    setRejectable(selectedStage?.data?.rejectable);
    if (!selectedStage?.data?.logicGate && type !== "CREATE") {
      dispatch(handleFetchWorkStageConditions(selectedStage?.data?.stageId));
      dispatch(
        handleFetchWorkStageColumns([selectedStage?.data?.stageId], true)
      ).then((res) => {
        if (!res.error)
          setSelectedMetric(selectedStage?.data?.metricWorkItemColumnId);
      });
      dispatch(
        handleFetchWorkStageGroupableColumns(selectedStage?.data?.stageId)
      );
    }
    setGroupable(selectedStage?.data?.groupable);
    setStartingStage(selectedStage?.data?.startingNode);
  }, [selectedStage, type]);

  useEffect(() => {
    // use isStagesLoading to check if workStageColumns are being refreshed
    if (
      !selectedStage?.data?.logicGate &&
      type !== "CREATE" &&
      !isStagesLoading
    ) {
      setSelectedColumns(
        workStageColumns.sort((a, b) => a.position - b.position)
      );
    }
  }, [workStageColumns, isStagesLoading]);

  useEffect(() => {
    if (
      selectedMetric &&
      selectedColumns.length &&
      !selectedStage?.data?.logicGate
    ) {
      if (!selectedColumns.find((c) => c.id === selectedMetric)) {
        setSelectedMetric(null);
      }
    }
  }, [selectedColumns, selectedMetric]);

  useEffect(() => {
    if (selectedStage?.data?.stageCodeId && !selectedStage?.data?.logicGate) {
      const costCodeObj = stageCodes.find(
        (cc) => cc.id === selectedStage?.data?.stageCodeId
      );
      setCostCodeValue(costCodeObj ? costCodeObj.name : null);
    }
  }, [selectedStage, stageCodes]);

  useEffect(() => {
    if (!selectedStage?.data?.logicGate) {
      if (throughputColumns) setSelectedThroughputColumns(throughputColumns);
      else setSelectedThroughputColumns([]);
    }
  }, [throughputColumns]);

  const addCodePerm = permissions && permissions.includes(284);
  const editCodePerm = permissions && permissions.includes(284);

  const statusColorMap = useMemo(() => {
    if (nodes) {
      let result = {};

      for (let node in nodes) {
        if (!nodes[node].properties) continue;

        const currentStatusGroup =
          nodes[node].properties.stage_status_group_name;
        const currentStatusColor =
          nodes[node].properties.stage_status_group_color;
        if (currentStatusGroup) result[currentStatusGroup] = currentStatusColor;
      }

      return result;
    } else return {};
  }, [nodes]);

  // either current selected stage conditions / or conditions to create if creating new stage
  const displayedConditions = useMemo(() => {
    let result = [];

    if (conditions) result = conditions;

    return result
      .map((c) => {
        // Add a comment in the display name of condition so its clearly a custom field
        let suffix = "";
        if (workItemColumns) {
          const workItemColumn = workItemColumns.find(
            (x) => x.id === c.work_item_columns_id
          );
          if (workItemColumn?.is_custom) {
            suffix = " (custom)";
          }
        }
        return {
          id: c.id,
          value: `${c.work_item_column_name} ${c.comparison} ${c.value}`,
          display:
            `${c.work_item_column_name} ${c.comparison} ${c.value}` + suffix,
          workItemColumn: workItemColumns?.find(
            (x) => x.id === c.work_item_columns_id
          ),
        };
      })
      .sort((a, b) => naturalSort(a.display, b.display));
  }, [conditions, workItemColumns]);

  const nameChanged = useMemo(() => {
    return nameInputValue !== selectedStage?.data?.stageName;
  }, [nameInputValue]);

  const holdpointChanged = useMemo(() => {
    return holdpoint !== selectedStage?.data?.holdpoint;
  }, [holdpoint]);

  const metricChanged = useMemo(() => {
    return selectedMetric !== selectedStage?.data?.metricWorkItemColumnId;
  }, [selectedMetric]);

  const nestableChanged = useMemo(() => {
    return !!nestable !== selectedStage?.data?.nestable;
  }, [nestable]);

  const rejectableChanged = useMemo(() => {
    return !!rejectable !== selectedStage?.data?.rejectable;
  }, [rejectable]);

  const groupableChanged = useMemo(() => {
    return !!groupable !== selectedStage?.data?.groupable;
  }, [groupable]);

  const costCodeChanged = useMemo(() => {
    if ((addCodePerm || editCodePerm) && inFlow) {
      return costCodeValue !== selectedStage?.data?.stageCodeName;
    }
    return true;
  }, [costCodeValue]);

  const startingStageChanged = useMemo(() => {
    return startingStage !== selectedStage?.data?.startingNode;
  }, [startingStage]);

  const statusGroupChanged = useMemo(() => {
    return statusGroup !== selectedStage?.data?.stageStatusGroupName;
  }, [statusGroup]);

  const throughputColumnsChanged = useMemo(() => {
    return (
      JSON.stringify((throughputColumns ?? []).sort((a, b) => a.id - b.id)) !==
      JSON.stringify(selectedThroughputColumns.sort((a, b) => a.id - b.id))
    );
  }, [selectedThroughputColumns]);

  const handleCancelEditConditions = () => {
    toggleShowEditConditions(false);
  };

  const handleSaveEditedStage = async () => {
    if (inFlow && throughputColumnsChanged) {
      dispatch(
        handleUpdateThroughputColumns(
          selectedStage?.data?.stageId,
          selectedThroughputColumns.map((c) => c.id).join(",")
        )
      ).then((res) => {
        if (!res.error) {
          setSelectedThroughputColumns(res);
          dispatch(
            notify({
              id: Date.now(),
              type: "WARN",
              message: "Rebuilding throughputs. This may take up to a minute.",
            })
          );
        }
      });
    }

    if (inFlow && statusGroup !== selectedStage?.data?.stageStatusGroupName) {
      dispatch(
        handleUpdateStageStatusGroup(
          selectedStage?.data?.stageId,
          selectedFlow.id,
          statusGroup,
          statusColorMap[statusGroup] ? statusColorMap[statusGroup] : "#000000"
        )
      ).then((res) => {
        if (!res.error) {
          updateStatusGroup(
            selectedStage?.data?.stageId,
            statusGroup,
            statusColorMap[statusGroup]
              ? statusColorMap[statusGroup]
              : "#000000"
          );
          setNewStatusGroup("");
        }
      });
    }

    const existingCode = stageCodes.find(
      (sc) => sc.name === (costCodeValue ? costCodeValue.trim() : null)
    );

    let dataObj = { id: selectedStage.data.stageId };
    let updatedStageObj = {};

    if (
      nameInputValue.trim() &&
      nameInputValue.trim() !== selectedStage.data.stageName
    ) {
      Object.assign(dataObj, { name: nameInputValue.trim() });
    }
    Object.assign(updatedStageObj, { stageName: nameInputValue.trim() });

    if (holdpoint !== selectedStage.data.holdpoint) {
      Object.assign(dataObj, { shape: holdpoint ? "Diamond" : "Square" });
    }
    Object.assign(updatedStageObj, { holdpoint });

    if (
      selectedMetric &&
      selectedMetric !== selectedStage.data.metricWorkItemColumnId
    ) {
      Object.assign(dataObj, { metric: selectedMetric });
    }
    Object.assign(updatedStageObj, {
      metricWorkItemColumnId: selectedMetric,
    });

    if (nestable !== !!selectedStage.data.nestable) {
      Object.assign(dataObj, { nestable });
    }
    Object.assign(updatedStageObj, { nestable });

    if (rejectable !== !!selectedStage.data.rejectable) {
      Object.assign(dataObj, { rejectable });
    }
    Object.assign(updatedStageObj, { rejectable });

    if (groupable !== !!selectedStage.data.groupable) {
      Object.assign(dataObj, { groupable });
    }
    Object.assign(updatedStageObj, { groupable });

    if (inFlow && (addCodePerm || editCodePerm)) {
      if (
        existingCode ||
        (costCodeValue === "" && costCodeSearchValue === "")
      ) {
        Object.assign(dataObj, {
          flow_id: selectedFlow.id,
          stage_code_id:
            costCodeValue === "" && costCodeSearchValue === ""
              ? null
              : existingCode.id,
        });
        Object.assign(updatedStageObj, { stageCodeId: dataObj.stage_code_id });
      } else if (addCodePerm && costCodeValue && costCodeValue.trim()) {
        await dispatch(handleCreateStageCode(costCodeValue)).then((res) => {
          if (!res.error) {
            Object.assign(dataObj, {
              flow_id: selectedFlow.id,
              stage_code_id: res[0].id,
            });
            Object.assign(updatedStageObj, { stageCodeId: res[0].id });
          }
        });
      }
    }

    if (updatedStageReport) {
      Object.assign(dataObj, { includeInJointLog });
    }
    Object.assign(updatedStageObj, { includeInJointLog });

    Object.assign(updatedStageObj, { startingNode: startingStage });

    let error = null;
    if (Object.keys(dataObj).length > 1) {
      const res = await dispatch(handleUpdateWorkStage(dataObj));
      if (res.error) error = res;
    }

    const updateFlow =
      inFlow &&
      (selectedStage.data.includeInJointLog !== includeInJointLog ||
        startingStage !== selectedStage.data.startingNode);
    updateSelectedStage(updatedStageObj, updateFlow);

    if (Object.keys(dataObj).length > 1 && !error)
      dispatch(handleFetchWorkStagesGet());

    // grab columns as they are before they get sorted by ids
    let orderedColumns = selectedColumns.slice();
    if (
      JSON.stringify(workStageColumns.sort((a, b) => a.id - b.id)) !==
      JSON.stringify(selectedColumns.sort((a, b) => a.id - b.id))
    ) {
      let columnsToAdd = [];
      let columnsToAddWithPosition = [];
      orderedColumns.forEach((sc) => {
        if (
          !workStageColumns.find((wsc) => sc.id === wsc.id) ||
          workStageColumns.find(
            (wsc) => sc.id === wsc.id && sc.position !== wsc.position
          )
        ) {
          columnsToAdd.push(sc);
          columnsToAddWithPosition.push({
            column_id: sc.id,
            position: sc.position,
          });
        }
      });
      const columnsToRemove = workStageColumns.filter(
        (wsc) => !selectedColumns.find((sc) => sc.id === wsc.id)
      );

      if (columnsToRemove.length)
        await dispatch(
          handleUpdateWorkStageColumns(
            0,
            selectedStage.data.stageId,
            columnsToRemove
          )
        );
      if (columnsToAdd.length) {
        await dispatch(
          handleUpdateWorkStageColumns(
            1,
            selectedStage.data.stageId,
            columnsToAdd,
            undefined,
            "EDIT",
            columnsToAddWithPosition
          )
        );
      }
      if (columnsToAdd.length || columnsToRemove.length)
        dispatch(
          handleFetchWorkStageColumns([selectedStage.data.stageId], true)
        );
    }

    if (groupable && updatedGroupableColumns) {
      let groupableColumnsToAdd = [];
      let groupableColumnsToAddWithPosition = [];
      selectedGroupableColumns.forEach((sgc) => {
        if (
          workStageGroupableColumnsOptions.find(
            (wsgc) => !wsgc.group_by && sgc.id === wsgc.id
          )
        ) {
          groupableColumnsToAdd.push(sgc.id);
          const position = workStageGroupableColumnsOptions.find(
            (c) => c.id === sgc.id
          ).position;
          groupableColumnsToAddWithPosition.push({
            column_id: sgc.id,
            position: position ? position : 1,
          });
        }
      });
      const groupableColumnsToRemove = workStageGroupableColumnsOptions
        .filter(
          (wsgc) =>
            wsgc.group_by &&
            !selectedGroupableColumns.find((sgc) => sgc.id === wsgc.id)
        )
        .map((gctr) => gctr.id);

      if (groupableColumnsToRemove.length)
        await dispatch(
          handleUpdateWorkStageColumns(
            0,
            selectedStage.data.stageId,
            groupableColumnsToRemove,
            1,
            "EDIT"
          )
        );
      if (groupableColumnsToAdd.length)
        await dispatch(
          handleUpdateWorkStageColumns(
            1,
            selectedStage.data.stageId,
            groupableColumnsToAdd,
            1,
            "EDIT",
            groupableColumnsToAddWithPosition
          )
        );
      if (groupableColumnsToAdd.length || groupableColumnsToRemove.length) {
        dispatch(
          handleFetchWorkStageGroupableColumns(selectedStage.data.stageId)
        );
      }
    }
  };

  const saveDuplicatedStage = async (stageToDuplicate) => {
    dispatch(handleDuplicateStage(stageToDuplicate?.id, nameInputValue)).then(
      (res) => {
        if (!res.error && res.length) {
          // update type to be edit now that stage is created and set new selectedStage
          setRightPanelAction("EDIT");
          handleSelectStage(res[0]);

          dispatch(handleFetchWorkStagesGet());
        }
      }
    );
  };

  const handleStageCreation = useCallback(() => {
    dispatch(
      handleCreateWorkStage({
        name: nameInputValue,
        conditions: conditionsToAdd,
        holdpoint,
        stageToDuplicate,
        columns: selectedColumns,
        metric: selectedMetric,
        nestable,
        rejectable,
        groupable,
        selectedGroupableColumns,
      })
    ).then((res) => {
      if (res.error) return;
      dispatch(handleFetchWorkStagesGet());
    });
  }, [
    nameInputValue,
    conditionsToAdd,
    selectedColumns,
    holdpoint,
    stageToDuplicate,
    selectedMetric,
    nestable,
    rejectable,
    groupable,
    selectedGroupableColumns,
  ]);

  const displayedStatusGroups = useMemo(() => {
    if (statusGroups) {
      if (searchInput) {
        const pattern = new RegExp(escapeRegExp(searchInput), "i");
        return statusGroups.filter((sg) => pattern.test(sg));
      } else return statusGroups;
    } else return [];
  }, [statusGroups, searchInput]);

  const displayedSelectedColumns = useMemo(() => {
    return selectedColumns
      .filter((sc) => sc.usable_for_conditions && sc.is_custom === 0)
      .map((sc) => ({ id: sc.id, value: sc.id, display: sc.display_name }))
      .sort((a, b) =>
        a.display.toUpperCase() > b.display.toUpperCase() ? 1 : -1
      );
  }, [selectedColumns]);

  const displayedGroupableColumns = useMemo(() => {
    return selectedColumns
      .filter((c) => c.groupable)
      .sort((a, b) =>
        a.display_name.toUpperCase() > b.display_name.toUpperCase() ? 1 : -1
      );
  }, [selectedGroupableColumns, selectedColumns]);

  const selectGroupableColumns = useCallback(
    (newSelected) => {
      if (
        materialNameObj &&
        !newSelected.find((c) => c.id === materialNameObj.id)
      ) {
        setSelectedGroupableColumns([
          materialNameObj,
          ...newSelected.filter((c) => c.id !== materialNameObj.id),
        ]);
      } else setSelectedGroupableColumns(newSelected);
    },
    [displayedGroupableColumns, selectedGroupableColumns, materialNameObj]
  );

  const selectColumns = useCallback(
    (newSelected) => {
      let colsToSelect = [];
      if (
        materialNameObj &&
        !newSelected.find((c) => c.id === materialNameObj.id)
      ) {
        colsToSelect = [materialNameObj, ...newSelected];
      } else colsToSelect = newSelected;

      colsToSelect = colsToSelect.map((c, i) => {
        return {
          ...c,
          position: i + 1,
        };
      });

      setSelectedColumns(colsToSelect);
      setSelectedThroughputColumns(
        selectedThroughputColumns.filter((stc) =>
          colsToSelect.find((c) => c.id === stc.id)
        )
      );
    },
    [
      workItemColumns,
      selectedColumns,
      materialNameObj,
      selectedThroughputColumns,
    ]
  );

  const selectThroughputColumns = useCallback(
    (newSelected) => {
      if (selectedMetric && !newSelected.find((c) => c.id === selectedMetric)) {
        setSelectedThroughputColumns([
          workItemColumns.find((c) => c.id === selectedMetric),
          ...newSelected,
        ]);
      } else setSelectedThroughputColumns(newSelected);
    },
    [selectedMetric, selectedThroughputColumns, workItemColumns]
  );

  const displayedCostCodes = useMemo(() => {
    return costCodeSearchValue
      ? stageCodes.filter((cc) => cc.name.includes(costCodeSearchValue))
      : stageCodes;
  }, [stageCodes, costCodeSearchValue]);

  const currentStageCode = useMemo(() => {
    return stageCodes.find((sc) => sc.id === selectedStage?.data?.stageCodeId);
  }, [stageCodes, selectedStage]);

  const updatedStageReport = useMemo(() => {
    if (!inFlow) return false;

    return selectedStage?.data?.includeInJointLog !== includeInJointLog;
  }, [includeInJointLog, selectedStage, inFlow]);

  const updatedSelectedColumns = useMemo(() => {
    const stageColumnIds = workStageColumns
      ? workStageColumns.map((c) => c.id)
      : [];
    const selectedColumnIds = selectedColumns
      ? selectedColumns.map((c) => c.id)
      : [];
    return JSON.stringify(stageColumnIds) !== JSON.stringify(selectedColumnIds);
  }, [selectedColumns]);

  const updatedGroupableColumns = useMemo(() => {
    if (groupable) {
      return (
        JSON.stringify(
          workStageGroupableColumnsOptions
            .filter((c) => c.group_by)
            .sort((a, b) => a.id - b.id)
            .map((c) => c.display_name)
        ) !==
        JSON.stringify(
          selectedGroupableColumns
            .sort((a, b) => a.id - b.id)
            .map((c) => c.display_name)
        )
      );
    } else return false;
  }, [workStageGroupableColumnsOptions, selectedGroupableColumns, groupable]);

  const displayedThroughputColumns = useMemo(() => {
    let result = [];

    if (selectedColumns && selectedColumns.length) {
      result = selectedColumns.filter((c) => c.usable_for_conditions);
    }

    return result;
  }, [selectedColumns, selectedMetric]);

  const displayedWorkStages = useMemo(() => {
    if (workStages) {
      return (
        workStages
          // filter out tasks and shipping block stages
          .filter(
            (ws) =>
              ws.stage_work_level_id !== stageWorkLevels["task"] &&
              !ws.shipping_block_id
          )
          .map((ws) => {
            return {
              id: ws.id,
              value: JSON.stringify(ws),
              display: ws.name,
            };
          })
          .sort((a, b) => {
            if (a.display.toLowerCase() < b.display.toLowerCase()) return -1;
            else return 1;
          })
      );
    } else return [];
  }, [workStages]);

  const displayedStageConditions = useMemo(() => {
    return type === "CREATE" ? conditionsToAdd : stageConditions;
  }, [conditionsToAdd, stageConditions, type]);

  const handleSubmit = () => {
    if (type === "CREATE") {
      handleStageCreation();
      setRightPanelAction(null);
    } else handleSaveEditedStage();
  };

  const handleStageCreationCancelClick = () => {
    if (isFormUnsaved) setConfirmationType("CANCEL");
    else {
      setRightPanelAction(null);
      setSelectedStage(null);
    }
  };

  useEffect(() => {
    // TODO - update this to handle stage editing
    if (type !== "CREATE") return;

    nameInputValue.trim() ||
    selectedMetric ||
    holdpoint ||
    nestable ||
    rejectable ||
    groupable ||
    stageToDuplicate ||
    conditionsToAdd?.length
      ? toggleFormUnsaved(true)
      : toggleFormUnsaved(false);
  }, [
    nameInputValue,
    selectedMetric,
    holdpoint,
    nestable,
    rejectable,
    groupable,
    stageToDuplicate,
    conditionsToAdd,
  ]);

  return (
    <div className="edit-stage-panel" id="edit-stage-panel" ref={panelRef}>
      {type === "CREATE" ? (
        <input
          type="text"
          value={nameInputValue}
          onChange={(e) => setNameInputValue(e.target.value)}
          className="name-input create-stage-name"
          autoFocus
          placeholder="New Stage Name"
        />
      ) : (
        <h1
          ref={nameInputRef}
          className="name"
          onClick={
            selectedStage?.data?.logicGate
              ? (f) => f
              : () => toggleEditName(true)
          }
        >
          {editName ? (
            <input
              type="text"
              value={nameInputValue}
              onChange={(e) => setNameInputValue(e.target.value)}
              className="name-input"
              autoFocus
              onBlur={(e) => {
                if (!e.target.value) {
                  setNameInputValue(selectedStage?.data?.stageName);
                }
                toggleEditName(false);
              }}
            />
          ) : (
            <span className="name-value">{nameInputValue}</span>
          )}
          {selectedStage?.data?.logicGate ? <></> : <FaEdit />}
        </h1>
      )}
      {inFlow && !selectedStage?.data?.logicGate && (
        <div className="status-group" ref={dropdownRef}>
          <>
            <label
              onClick={() => {
                if (!showDropdown) toggleDropdown(true);
              }}
            >
              Status Group: <FaAsterisk className="required" />
            </label>
            <span
              className="search-wrapper"
              onClick={() => {
                if (!showDropdown) toggleDropdown(true);
              }}
            >
              <span>
                <BiSearch />
              </span>
              <input
                value={
                  searchInput !== null
                    ? searchInput
                    : statusGroup
                    ? statusGroup
                    : newStatusGroup
                }
                onChange={(e) => {
                  setStatusGroup(e.target.value);
                  setSearchInput(e.target.value);
                  setNewStatusGroup(e.target.value);
                }}
                type="text"
              />
            </span>
            {showDropdown && (
              <div className="dropdown-wrapper">
                <ul>
                  {!displayedStatusGroups.length && (
                    <li
                      onClick={() => {
                        setSearchInput(null);
                        setStatusGroup("In Fabrication");
                        setNewStatusGroup("");
                      }}
                    >
                      In Fabrication
                    </li>
                  )}
                  {displayedStatusGroups.map((sg) => (
                    <li
                      key={sg}
                      onClick={() => {
                        setSearchInput(null);
                        setStatusGroup(sg);
                        setNewStatusGroup("");
                      }}
                    >
                      {sg}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </>
        </div>
      )}
      {type === "CREATE" && (
        <div className="duplicate-stage-select">
          <label>
            Duplicate from Existing Stage:
            <Select
              options={displayedWorkStages}
              onInput={(e) => setStageToDuplicate(JSON.parse(e.target.value))}
              placeholder="-- Existing Stages --"
            />
          </label>
          <Button
            disabled={!stageToDuplicate || !nameInputValue}
            onClick={() =>
              saveDuplicatedStage(stageToDuplicate, nameInputValue)
            }
          >
            Duplicate
          </Button>
        </div>
      )}
      {!drawingLevelStage && !selectedStage?.data?.logicGate && (
        <>
          <div className="columns">
            <label className="columns-label">Display Columns: </label>
            <WorkItemColumns
              workItemColumnsList={workItemColumns}
              selectedColumns={selectedColumns}
              setSelectedColumns={selectColumns}
            />
          </div>
          <div className="metric-rejectable-nestable">
            <label className="metric">
              <>
                {translate("Metric:")} <FaAsterisk className="required" />
              </>
              <Select
                options={displayedSelectedColumns}
                value={selectedMetric}
                onInput={(e) => {
                  const newSelectedMetric = parseInt(e.target.value);

                  if (
                    !selectedThroughputColumns.find(
                      (c) => c.id === newSelectedMetric
                    )
                  )
                    setSelectedThroughputColumns((columns) => [
                      workItemColumns.find((c) => c.id === newSelectedMetric),
                      ...columns,
                    ]);
                  setSelectedMetric(newSelectedMetric);
                }}
                placeholder={translate("-- Selected Columns --")}
              />
            </label>
          </div>
          {inFlow && (
            <div className="columns">
              <label className="columns-label">
                Columns for Calculations:{" "}
              </label>
              <WorkItemColumns
                workItemColumnsList={displayedThroughputColumns}
                selectedColumns={selectedThroughputColumns}
                setSelectedColumns={selectThroughputColumns}
              />
            </div>
          )}
          <div className="checkbox-wrapper">
            {inFlow && (
              <div className="starting-stage">
                <label className="starting-stage">
                  <Input
                    type="checkbox"
                    checked={startingStage}
                    onChange={() => setStartingStage(!startingStage)}
                  />
                  {translate("Flow Start?")}
                </label>
              </div>
            )}
            <div className="groupable">
              <label className="groupable">
                <Input
                  type="checkbox"
                  checked={groupable}
                  onChange={() => setGroupable(!groupable)}
                />
                {translate("Groupable?")}
              </label>
              {groupable && (
                <label className="groupable-select">
                  <FaAsterisk className="required" />
                  <Filter
                    nameKey="display_name"
                    idKey="id"
                    type={translate("Columns To Group By")}
                    list={displayedGroupableColumns}
                    selected={selectedGroupableColumns}
                    setSelected={selectGroupableColumns}
                    handleParentSelect={selectGroupableColumns}
                    toggleAllSelections={(f) => f}
                    selectAll
                    orientation="HORIZONTAL"
                    smallView
                    alwaysCollapsed
                    preventFormat
                    searchInput={groupableColumnSearch}
                    setSearchInput={setGroupableColumnSearch}
                  />
                </label>
              )}
            </div>
            <label className="holdpoint">
              <Input
                type="checkbox"
                checked={holdpoint}
                onChange={() => setHoldpoint(!holdpoint)}
              />
              {translate("Holdpoint?")}
            </label>
            <label className="nestable">
              <Input
                type="checkbox"
                checked={nestable}
                onChange={() => setNestable(!nestable)}
              />
              {translate("Nestable?")}
            </label>
            <label className="rejectable">
              <Input
                type="checkbox"
                checked={rejectable}
                onChange={() => setRejectable(!rejectable)}
              />
              {translate("Rejectable?")}
            </label>
            {inFlow && (
              <label className="joint-log-toggle">
                <Input
                  type="checkbox"
                  checked={includeInJointLog}
                  onChange={() => toggleIncludeInJointLog(!includeInJointLog)}
                />
                Include in joint log?
              </label>
            )}
          </div>
          {inFlow ? (
            <div className="in-flow-inputs">
              <div
                className="stage-cost-code-wrapper"
                onFocus={() => toggleCostCodeDropdown(true)}
                ref={costCodeRef}
              >
                <span>{translate("Flow Specific Cost Code:")}</span>
                {addCodePerm || editCodePerm ? (
                  <>
                    <Input
                      value={costCodeSearchValue || costCodeValue || ""}
                      onChange={(e) => {
                        setCostCodeSearchValue(e.target.value);
                        setCostCodeValue(e.target.value);
                      }}
                      className="stage-cost-code-input"
                    />
                    {showCostCodeDropdown && (
                      <ul className="stage-code-code-dropdown">
                        {displayedCostCodes.map((cc) => (
                          <li
                            key={cc.id}
                            onClick={() => {
                              setCostCodeValue(cc.name);
                              setCostCodeSearchValue("");
                              toggleCostCodeDropdown(false);
                            }}
                          >
                            {cc.name}
                          </li>
                        ))}
                      </ul>
                    )}
                  </>
                ) : (
                  <div>{currentStageCode ? currentStageCode.name : "None"}</div>
                )}
              </div>
            </div>
          ) : (
            <></>
          )}
          <div className="read-only-conditions">
            <h2 className="title">
              Conditions{" "}
              <Button onClick={() => toggleShowEditConditions(true)}>
                Edit
              </Button>
            </h2>
            <div className="content">
              {displayedStageConditions && displayedStageConditions.length ? (
                displayedStageConditions.map((sc) => {
                  return (
                    <div className="condition-sub-group" key={sc.sub_group}>
                      {sc.sub_group_operand !== "start" && (
                        <>
                          <div className="sub-group-operand">
                            {sc.sub_group_operand.toUpperCase()}
                          </div>
                          <span className="sub-group-operand-label">
                            {translate("< Group Operand")}
                          </span>
                        </>
                      )}
                      <></>
                      {sc.conditions.length &&
                      sc.conditions.some((c) => !!c.work_item_condition_id) ? (
                        sc.conditions
                          .filter((c) => !!c.work_item_condition_id)
                          .map((c) => (
                            <React.Fragment
                              key={`${sc.sub_group}-${c.work_item_condition_id}`}
                            >
                              <div className="condition-operand">
                                {c.operand !== "start"
                                  ? c.operand
                                    ? c.operand.toUpperCase()
                                    : c.sub_group_operand.toUpperCase()
                                  : ""}
                              </div>
                              <div className="condition-value">
                                {c.work_item_condition_id &&
                                textBasedFilters.includes(c.comparison)
                                  ? `${c.work_item_column_name} ${c.comparison} "${c.value}"`
                                  : c.work_item_condition_id
                                  ? `${c.work_item_column_name} ${c.comparison} ${c.value}`
                                  : ""}
                              </div>
                            </React.Fragment>
                          ))
                      ) : (
                        <></>
                      )}
                    </div>
                  );
                })
              ) : (
                <></>
              )}
            </div>
          </div>
        </>
      )}
      <div
        className={`action-buttons ${
          selectedStage?.data?.logicGate && "logic-gate-btn-wrapper"
        }`}
      >
        {!selectedStage?.data?.logicGate ? (
          <Button
            className="save"
            onClick={() => handleSubmit()}
            disabled={
              !nameInputValue ||
              !nameInputValue.trim() ||
              !selectedMetric ||
              !statusGroup ||
              !statusGroup.trim() ||
              (!nameChanged &&
                !holdpointChanged &&
                !metricChanged &&
                !nestableChanged &&
                !rejectableChanged &&
                !groupableChanged &&
                !costCodeChanged &&
                !updatedStageReport &&
                !updatedSelectedColumns &&
                !updatedGroupableColumns &&
                !startingStageChanged &&
                !statusGroupChanged &&
                !throughputColumnsChanged)
            }
          >
            <RiSave3Fill />
          </Button>
        ) : (
          <></>
        )}
        {type === "CREATE" && (
          <Button className="delete" onClick={handleStageCreationCancelClick}>
            Cancel
          </Button>
        )}
        {inFlow && (
          <Button className="delete" onClick={removeStageFromFlow}>
            {translate("Remove from flow")}
          </Button>
        )}
        {!inFlow && type !== "CREATE" && (
          <FaTrash
            className="delete-icon"
            onClick={() => setConfirmationType("DELETE")}
          />
        )}
      </div>
      {showEditConditions && (
        <Conditions
          open={showEditConditions}
          handleClose={handleCancelEditConditions}
          displayedConditions={displayedConditions}
          conditions={conditions}
          stageId={selectedStage?.data?.stageId}
          stageName={selectedStage?.data?.stageName}
          actionType={type}
          conditionsToAdd={conditionsToAdd}
          setConditionsToAdd={setConditionsToAdd}
        ></Conditions>
      )}
    </div>
  );
};

export default RightPanel;
