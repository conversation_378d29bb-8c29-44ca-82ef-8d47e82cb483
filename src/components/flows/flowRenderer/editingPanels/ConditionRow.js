// NPM PACKAGE IMPORTS
import React from "react";
import Select from "msuite_storybook/dist/select/Select";
import Input from "msuite_storybook/dist/input/Input";
import { <PERSON>iP<PERSON>, BiMinus } from "react-icons/bi";
import { textBasedFilters } from "../../conditions/utils";

const ConditionRow = ({
  condition,
  conditions,
  displayedOperands,
  displayedConditions,
  conditionGroupId,
  index,
  onUpdateOperand,
  onChangeSelectedCondition,
  onAddRemoveCondition,
}) => {
  const hasOtherSingleConditions = conditions.length > 1;
  const hasSelect = condition.operand !== "start" && index === 1;
  const hasTextOperand = condition.operand !== "start" && index > 1;
  return (
    <div className={`condition-wrapper`}>
      {hasSelect && (
        <Select
          className={"condition-operand"}
          options={displayedOperands}
          value={condition.operand}
          placeholder={"-- Operand --"}
          onInput={(e) =>
            onUpdateOperand(e, condition, conditionGroupId, index)
          }
        />
      )}
      {hasTextOperand && <span>{condition.operand.toUpperCase()}</span>}
      <div
        className="condition-select"
        id={`conditions-select-${condition.id}`}
      >
        <Input
          list={`conditions-${
            condition.operand +
            condition.work_item_column_name +
            condition.comparison +
            condition.value +
            condition.id
          }`}
          key={`input-${
            condition.operand +
            condition.work_item_column_name +
            condition.comparison +
            condition.value +
            condition.id
          }`}
          defaultValue={
            condition.work_item_column_name &&
            textBasedFilters.includes(condition.comparison)
              ? `${condition.work_item_column_name} ${condition.comparison} "${condition.value}"`
              : condition.work_item_column_name
              ? `${condition.work_item_column_name} ${condition.comparison} ${condition.value}`
              : null
          }
          placeholder="-- Select Condition --"
          onChange={(e) =>
            onChangeSelectedCondition(e, conditionGroupId, condition)
          }
        />
        <datalist
          id={`conditions-${
            condition.operand +
            condition.work_item_column_name +
            condition.comparison +
            condition.value +
            condition.id
          }`}
          key={`datalist-${
            condition.operand +
            condition.work_item_column_name +
            condition.comparison +
            condition.value +
            condition.id
          }`}
        >
          {displayedConditions?.map((c) => (
            <option key={c.value} value={c.display} />
          ))}
        </datalist>
      </div>
      <div className="actions">
        <div className="actions-wrapper">
          {(!hasOtherSingleConditions ||
            conditions[conditions.length - 1].id === condition.id) && (
            <BiPlus
              onClick={() =>
                onAddRemoveCondition(
                  conditionGroupId,
                  condition.sub_group_level + 1,
                  {
                    grouping_level_1_id: condition.grouping_level_1_id,
                    grouping_level_2_id: condition.grouping_level_2_id,
                    grouping_level_3_id: condition.grouping_level_3_id,
                    grouping_level_4_id: condition.grouping_level_4_id,
                    grouping_level_5_id: condition.grouping_level_5_id,
                    grouping_level_6_id: condition.grouping_level_6_id,
                  },
                  index,
                  "add",
                  "row"
                )
              }
            />
          )}
          {conditions.length > 1 ? ( //  Hide the minus when there's only one condition
            <BiMinus
              onClick={() =>
                onAddRemoveCondition(
                  conditionGroupId,
                  condition.sub_group_level + 1,
                  {
                    grouping_level_1_id: condition.grouping_level_1_id,
                    grouping_level_2_id: condition.grouping_level_2_id,
                    grouping_level_3_id: condition.grouping_level_3_id,
                    grouping_level_4_id: condition.grouping_level_4_id,
                    grouping_level_5_id: condition.grouping_level_5_id,
                    grouping_level_6_id: condition.grouping_level_6_id,
                  },
                  index,
                  "remove",
                  "row"
                )
              }
            />
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default ConditionRow;
