// NPM PACKAGE IMPORTS
import React from "react";
import Button from "msuite_storybook/dist/button/Button";
import Select from "msuite_storybook/dist/select/Select";
import ConditionRow from "./ConditionRow";
import { FaTrash, FaChevronDown, FaChevronUp } from "react-icons/fa";

// STYLE IMPORTS
import "./stylesConditions.scss";

const ConditionGroup = ({
  open,
  handleClose,
  displayedConditions,
  displayedOperands,
  conditionGroup,
  conditionGroups,
  onUpdateOperand,
  onAddRemoveCondition,
  index,
  onCollapse,
  onChangeSelectedCondition,
}) => {
  const hasOperand =
    conditionGroup.sub_group_operand !== "start" &&
    conditionGroup.conditions.length > 0;
  const hasSelect = hasOperand && index === 1;
  const hasTextOperand = hasOperand && index > 1;

  return (
    <div
      className={`condition-group level${conditionGroup.sub_group_level} ${
        conditionGroup.sub_group_level === 1 && index === 0 ? " first" : ""
      } ${conditionGroup.is_collapsed ? " collapsed" : ""}`}
    >
      <div className="row">
        {hasTextOperand && (
          <span>{conditionGroup.sub_group_operand.toUpperCase()}</span>
        )}
        {hasSelect && (
          <div className="condition-group-operand">
            <Select
              options={displayedOperands}
              value={conditionGroup.sub_group_operand}
              placeholder={"-- Operand --"}
              onInput={(e) =>
                onUpdateOperand(e, conditionGroup, conditionGroup.id, index)
              }
            />
          </div>
        )}
        <div
          className={`row group-header level${
            conditionGroup.sub_group_level + 1
          }`}
        >
          <h4>
            Group {conditionGroup.sub_group_level}-
            {conditionGroups.findIndex((o) => o.id === conditionGroup.id) + 1}
            {conditionGroup.is_collapsed ? (
              <FaChevronUp
                className="collapse"
                onClick={() =>
                  onCollapse(
                    !conditionGroup.is_collapsed,
                    conditionGroup,
                    index
                  )
                }
              />
            ) : (
              <FaChevronDown
                className="collapse"
                onClick={() =>
                  onCollapse(
                    !conditionGroup.is_collapsed,
                    conditionGroup,
                    index
                  )
                }
              />
            )}
          </h4>

          <div className="actions">
            <div className="actions-wrapper">
              {conditionGroup.sub_group_level + 1 < 7 && (
                <Button
                  className="add-sub-group"
                  onClick={() =>
                    onAddRemoveCondition(
                      conditionGroup.id,
                      conditionGroup.sub_group_level + 1,
                      conditionGroup.grouping_level_1_id
                        ? {
                            grouping_level_1_id:
                              conditionGroup.grouping_level_1_id,
                            grouping_level_2_id:
                              conditionGroup.grouping_level_2_id,
                            grouping_level_3_id:
                              conditionGroup.grouping_level_3_id,
                            grouping_level_4_id:
                              conditionGroup.grouping_level_4_id,
                            grouping_level_5_id:
                              conditionGroup.grouping_level_5_id,
                            grouping_level_6_id:
                              conditionGroup.grouping_level_6_id,
                            [`grouping_level_${conditionGroup.sub_group_level}_id`]: conditionGroup.id,
                          }
                        : { grouping_level_1_id: conditionGroup.id },
                      conditionGroup.conditions.length - 1,
                      "add",
                      "sub-group"
                    )
                  }
                >
                  Add SubGroup
                </Button>
              )}
              <FaTrash
                className="cancel"
                onClick={() =>
                  onAddRemoveCondition(
                    conditionGroup.id,
                    conditionGroup.sub_group_level,
                    {
                      grouping_level_1_id: conditionGroup.grouping_level_1_id,
                      grouping_level_2_id: conditionGroup.grouping_level_2_id,
                      grouping_level_3_id: conditionGroup.grouping_level_3_id,
                      grouping_level_4_id: conditionGroup.grouping_level_4_id,
                      grouping_level_5_id: conditionGroup.grouping_level_5_id,
                      grouping_level_6_id: conditionGroup.grouping_level_6_id,
                    },
                    index,
                    "remove",
                    "group"
                  )
                }
              />
            </div>
          </div>
        </div>
      </div>
      <div className="row">
        <div className={`level${conditionGroup.sub_group_level + 1}`}>
          {conditionGroup.conditions.map((c, i) =>
            !c.conditions ? (
              <ConditionRow
                key={c.id}
                condition={c}
                conditions={conditionGroup.conditions.filter(
                  (o) => !o.conditions
                )}
                displayedOperands={displayedOperands}
                displayedConditions={displayedConditions}
                conditionGroupId={conditionGroup.id}
                index={i}
                onUpdateOperand={onUpdateOperand}
                onChangeSelectedCondition={onChangeSelectedCondition}
                onAddRemoveCondition={onAddRemoveCondition}
              />
            ) : (
              <ConditionGroup
                key={c.id}
                open={open}
                handleClose={handleClose}
                displayedOperands={displayedOperands}
                displayedConditions={displayedConditions}
                conditionGroup={c}
                conditionGroups={conditionGroup.conditions.filter(
                  (o) => !!o.conditions
                )}
                index={i}
                onAddRemoveCondition={onAddRemoveCondition}
                onUpdateOperand={onUpdateOperand}
                onCollapse={onCollapse}
                onChangeSelectedCondition={onChangeSelectedCondition}
              />
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default ConditionGroup;
