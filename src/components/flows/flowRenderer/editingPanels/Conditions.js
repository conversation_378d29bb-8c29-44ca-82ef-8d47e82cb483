// NPM PACKAGE IMPORTS
import React, { useCallback, useEffect, useState, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import ConditionGroup from "./ConditionGroup";
import { logger } from "./../../../../utils/_dataDogUtils";

// STYLE IMPORTS
import "./stylesConditions.scss";

// REDUX IMPORTS
import { handleSaveWorkStageConditions } from "../../flowsActions";

// HELPER IMPORTS
import { getConditionIds } from "../../flowsUtils";

const Conditions = ({
  open,
  handleClose,
  displayedConditions,
  conditions,
  stageId,
  stageName,
  actionType,
  conditionsToAdd,
  setConditionsToAdd,
}) => {
  const displayedOperands = useMemo(() => {
    return [
      { id: 1, value: "or", display: "OR" },
      { id: 2, value: "and", display: "AND" },
    ];
  }, []);

  const dispatch = useDispatch();
  const { stageConditions } = useSelector((state) => state.flowsData);
  const { userId, userInfo } = useSelector((state) => state.profileData);

  const [conditionGroups, setConditionGroups] = useState([]);
  const [originalConditionGroups, setOriginalConditionGroups] = useState([]);
  // internal id used for keys, must be unique
  const [minId, setMinId] = useState(1);
  // used to determine state of close/cancel button
  const [hasChanges, toggleHasChanges] = useState(false);

  useEffect(() => {
    toggleHasChanges(
      JSON.stringify(conditionGroups) !==
        JSON.stringify(originalConditionGroups)
        ? true
        : false
    );
  }, [conditionGroups, originalConditionGroups]);

  useEffect(() => {
    if (actionType !== "CREATE" || !conditionsToAdd?.length) return;

    // find condition with the highest id in order to not have duplicate keys when reopening conditions without saving stage
    const conditionIds = getConditionIds(conditionsToAdd);
    if (conditionIds?.length) {
      setMinId(Math.max(...conditionIds));
    }

    setConditionGroups(conditionsToAdd);
  }, [conditionsToAdd, actionType]);

  // set default values needed on individual conditions
  const setDefaultValues = (subGroup, i, id) => {
    let conditions = [];
    subGroup.conditions.forEach((o) => {
      if (!!o.work_item_condition_id) {
        conditions.push({
          ...o,
          id: id,
          sub_group: subGroup.sub_group,
          sub_group_level: subGroup.sub_group_level,
          grouping_level_1_id:
            i === 0 ? subGroup.id : subGroup.grouping_level_1_id,
          grouping_level_2_id:
            i === 1 ? subGroup.id : subGroup.grouping_level_2_id,
          grouping_level_3_id:
            i === 2 ? subGroup.id : subGroup.grouping_level_3_id,
          grouping_level_4_id:
            i === 3 ? subGroup.id : subGroup.grouping_level_4_id,
          grouping_level_5_id:
            i === 4 ? subGroup.id : subGroup.grouping_level_5_id,
          grouping_level_6_id:
            i === 5 ? subGroup.id : subGroup.grouping_level_6_id,
        });
        id++;
      } else if (o.conditions) {
        let result = setDefaultValues(o, i + 1, id);
        conditions.push({
          ...o,
          conditions: result.conditions,
        });
        id = result.id;
      }
    });
    return { conditions, id };
  };

  const cascadeUpdateUp = (
    subGroup,
    c,
    subGroups,
    result,
    level,
    updateMain = false
  ) => {
    // cascade up, go through and update each subgroup until you reach level 1, then just update the main array
    let updatedGroup = subGroup;
    let newConditions = [];
    for (let i = level - 2; i > 0; i--) {
      if (updatedGroup && updatedGroup.conditions.length) {
        let updatedGroupId = updatedGroup.id;
        newConditions = subGroups[i].conditions;
        newConditions.splice(
          subGroups[i].conditions.findIndex((o) => o.id === updatedGroupId),
          1,
          updatedGroup
        );
      } else if (subGroups[i]?.conditions) {
        newConditions = subGroups[i].conditions.filter(
          (o) => o.id !== c[`grouping_level_${i + 1}_id`]
        );
      }
      updatedGroup = {
        ...subGroups[i],
        conditions: newConditions,
      };
    }

    if (updatedGroup) {
      result.splice(
        result.findIndex((o) => o.id === updatedGroup.id),
        1,
        updatedGroup
      );
      if (updateMain) {
        setConditionGroups([...conditionGroups]);
      }
    } else if (updateMain && subGroup.sub_group_level === 1) {
      setConditionGroups(conditionGroups.filter((o) => o.id !== subGroup.id));
    }
  };

  // generate the subGroups mapping
  const setSubGroupsMap = (groups, condition, isInitialSet = false) => {
    let group = groups.find((g) => g.id === condition.grouping_level_1_id);
    let subGroup = group;
    let subGroups = [];

    for (let i = 1; i < condition.sub_group_level; i++) {
      if (i > 1) {
        subGroup = subGroup.conditions.find((o) => {
          return o.id === condition[`grouping_level_${i}_id`];
        });
      }
      if (isInitialSet && i === condition.sub_group_level - 1) {
        subGroup.conditions.push({
          ...condition,
          id: condition.sub_group,
          is_collapsed: false,
        });
      }
      subGroups[i] = subGroup;
    }
    return { group, subGroup, subGroups };
  };

  useEffect(() => {
    let result = [];
    let id = 1;
    let clone = {};
    if (stageConditions && actionType !== "CREATE") {
      // nest the conditions as needed, result from api is not nested
      stageConditions.forEach((c, i) => {
        clone = JSON.parse(JSON.stringify(c));
        // can add top level groups as is
        if (c.sub_group_level === 1) {
          result.push({
            ...clone,
            id: clone.sub_group,
            grouping_level_1_id: null,
            is_collapsed: false,
          });
        } else {
          const mapOutput = setSubGroupsMap(result, clone, true);
          cascadeUpdateUp(
            mapOutput.subGroup,
            clone,
            mapOutput.subGroups,
            result,
            clone.sub_group_level
          );
        }

        if (id <= clone.sub_group) {
          id = clone.sub_group + 1;
        }
      });

      // set default values frontend needs on individual conditions
      result.forEach((c, i) => {
        let group = c;
        let subGroup = group;
        let subGroups = [];

        for (let i = 0; i < c.sub_group_level; i++) {
          if (subGroup.conditions?.length) {
            const output = setDefaultValues(subGroup, i, id);
            subGroup.conditions = output.conditions;
            id = output.id;
          }
          if (i > 1) {
            subGroup = subGroup.conditions.find((o) => {
              return o.id === c[`grouping_level_${i}_id`];
            });
          }
          subGroups[i] = subGroup;
        }

        cascadeUpdateUp(subGroup, c, subGroups, result, c.sub_group_level);

        if (id <= c.sub_group) {
          id = c.sub_group + 1;
        }
      });
    }

    if (actionType === "CREATE") return;

    setMinId(id);
    setOriginalConditionGroups(JSON.parse(JSON.stringify(result)));
    setConditionGroups(result);
  }, [stageConditions, actionType]);

  // this adds / removes / replaces a single condition or condition group
  // replace is used with operand changes as all groups / conditions
  // within the same group need to have the operand change applied
  const handleAddRemoveCondition = useCallback(
    (groupId, level, levels, index, action, type, operand) => {
      // top level group - level 1
      const group = conditionGroups.find(
        (c) => c.id === (level === 1 ? groupId : levels.grouping_level_1_id)
      );

      // if adding group using top green button, add empty level 1 group to end
      if (!group && level === 1 && action === "add" && type === "group") {
        setConditionGroups([
          ...conditionGroups,
          {
            id: minId + 1,
            operand: null,
            sub_group: minId + 1,
            sub_group_operand: !conditionGroups.length
              ? "start"
              : conditionGroups.length > 2
              ? conditionGroups[1].sub_group_operand
              : "or",
            conditions: [
              {
                id: minId + 2,
                operand: "start",
                sub_group: minId + 1,
                sub_group_operand: !conditionGroups.length
                  ? "start"
                  : conditionGroups.length > 2
                  ? conditionGroups[1].sub_group_operand
                  : "or",
                work_item_condition_id: null,
                sub_group_level: level,
                grouping_level_1_id: minId + 1,
                grouping_level_2_id: null,
                grouping_level_3_id: null,
                grouping_level_4_id: null,
                grouping_level_5_id: null,
                grouping_level_6_id: null,
              },
            ],
            sub_group_level: level,
            grouping_level_1_id: null,
            grouping_level_2_id: null,
            grouping_level_3_id: null,
            grouping_level_4_id: null,
            grouping_level_5_id: null,
            grouping_level_6_id: null,
          },
        ]);
        setMinId(minId + 2);
        return;
      }

      // conditions and groups can be nested to n levels so find group at each level
      let subGroup = group;
      const subGroups = [];
      if (level !== 1) {
        for (let i = 1; i < level; i++) {
          if (i > 1) {
            subGroup = subGroup.conditions.find((o) => {
              return o.id === levels[`grouping_level_${i}_id`];
            });
          }
          subGroups[i] = subGroup;
        }
      }

      if (action === "add") {
        const newOperand =
          level === 1 && conditionGroups.length < 2
            ? "start"
            : subGroup.conditions.length > 1 &&
              subGroup.conditions[subGroup.conditions.length - 1]
            ? subGroup.conditions[subGroup.conditions.length - 1].conditions
              ? subGroup.conditions[subGroup.conditions.length - 1]
                  .sub_group_operand
              : subGroup.conditions[subGroup.conditions.length - 1].operand
            : "or";
        if (type === "row") {
          // will need to figure out how to calc ids later
          subGroup.conditions.splice(index + 1, 0, {
            id: minId + 1,
            operand: newOperand,
            work_item_condition_id: null,
            sub_group: subGroup.sub_group,
            sub_group_operand: subGroup.sub_group_operand,
            sub_group_level: level - 1,
            grouping_level_1_id: levels?.grouping_level_1_id ?? group.id,
            grouping_level_2_id: levels?.grouping_level_2_id ?? null,
            grouping_level_3_id: levels?.grouping_level_3_id ?? null,
            grouping_level_4_id: levels?.grouping_level_4_id ?? null,
            grouping_level_5_id: levels?.grouping_level_5_id ?? null,
            grouping_level_6_id: levels?.grouping_level_6_id ?? null,
          });
          setMinId(minId + 1);
        } else if (type === "group") {
          subGroup.conditions.splice(index + 1, 0, {
            id: minId + 1,
            operand: null,
            sub_group: minId + 1,
            sub_group_operand: newOperand,
            conditions: [
              {
                id: minId + 2,
                operand: "start",
                work_item_condition_id: null,
                sub_group: minId + 1,
                sub_group_operand: newOperand,
                sub_group_level: level,
                grouping_level_1_id: levels?.grouping_level_1_id ?? null,
                grouping_level_2_id: levels?.grouping_level_2_id ?? null,
                grouping_level_3_id: levels?.grouping_level_3_id ?? null,
                grouping_level_4_id: levels?.grouping_level_4_id ?? null,
                grouping_level_5_id: levels?.grouping_level_5_id ?? null,
                grouping_level_6_id: levels?.grouping_level_6_id ?? null,
                [`grouping_level_${level}_id`]: minId + 1,
              },
            ],
            sub_group_level: level,
            grouping_level_1_id: levels?.grouping_level_1_id ?? null,
            grouping_level_2_id: levels?.grouping_level_2_id ?? null,
            grouping_level_3_id: levels?.grouping_level_3_id ?? null,
            grouping_level_4_id: levels?.grouping_level_4_id ?? null,
            grouping_level_5_id: levels?.grouping_level_5_id ?? null,
            grouping_level_6_id: levels?.grouping_level_6_id ?? null,
          });
          setMinId(minId + 2);
        } else if (type === "sub-group") {
          subGroup.is_collapsed = false;
          subGroup.conditions.splice(index + 1, 0, {
            id: minId + 1,
            operand: null,
            sub_group: minId + 1,
            sub_group_operand: newOperand,
            conditions: [
              {
                id: minId + 2,
                operand: "start",
                work_item_condition_id: null,
                sub_group: minId + 1,
                sub_group_operand: newOperand,
                sub_group_level: level,
                grouping_level_1_id: levels?.grouping_level_1_id ?? null,
                grouping_level_2_id: levels?.grouping_level_2_id ?? null,
                grouping_level_3_id: levels?.grouping_level_3_id ?? null,
                grouping_level_4_id: levels?.grouping_level_4_id ?? null,
                grouping_level_5_id: levels?.grouping_level_5_id ?? null,
                grouping_level_6_id: levels?.grouping_level_6_id ?? null,
                [`grouping_level_${level}_id`]: minId + 1,
              },
            ],
            sub_group_level: level,
            grouping_level_1_id: levels?.grouping_level_1_id ?? null,
            grouping_level_2_id: levels?.grouping_level_2_id ?? null,
            grouping_level_3_id: levels?.grouping_level_3_id ?? null,
            grouping_level_4_id: levels?.grouping_level_4_id ?? null,
            grouping_level_5_id: levels?.grouping_level_5_id ?? null,
            grouping_level_6_id: levels?.grouping_level_6_id ?? null,
          });
          setMinId(minId + 2);
        }
      } else if (action === "remove") {
        if (level === 1) {
          return setConditionGroups([
            ...conditionGroups.filter((o) => o.id !== groupId),
          ]);
        }
        subGroup.conditions.splice(index, 1);

        // prevent operand from showing when only one
        if (
          subGroup.conditions.length === 1 &&
          subGroup.conditions[0].work_item_condition_id
        ) {
          subGroup = {
            ...subGroup,
            conditions: [
              {
                ...subGroup.conditions[0],
                operand: "start",
              },
            ],
          };
        }
      } else if (action === "replace" && operand) {
        // update operand being used
        if (level === 1) {
          return setConditionGroups([
            ...conditionGroups.map((o) => {
              return {
                ...o,
                operand: null,
                sub_group_operand:
                  o.sub_group_operand !== "start"
                    ? operand
                    : o.sub_group_operand,
              };
            }),
          ]);
        }
        subGroup.conditions = subGroup.conditions.map((o) => {
          return {
            ...o,
            operand:
              o.operand !== "start" && !o.conditions ? operand : o.operand,
            sub_group_operand:
              o.sub_group_operand !== "start" && o.conditions
                ? operand
                : o.sub_group_operand,
          };
        });
      } else if (action === "collapse") {
        // handles collapsing and expanding a group
        if (level === 1) {
          subGroup.is_collapsed = !subGroup.is_collapsed;
        } else {
          let conditionGroup = subGroup.conditions.find(
            (o) => o.id === groupId
          );
          conditionGroup.is_collapsed = !conditionGroup.is_collapsed;
        }
      }

      cascadeUpdateUp(
        subGroup,
        levels,
        subGroups,
        conditionGroups,
        level,
        true
      );
    },
    [conditionGroups, minId]
  );

  const handleUpdateOperand = useCallback(
    (e, condition, groupId, index) => {
      const type = condition.conditions ? "group" : "row";
      handleAddRemoveCondition(
        groupId,
        type === "group"
          ? condition.sub_group_level
          : condition.sub_group_level + 1,
        {
          grouping_level_1_id: condition.grouping_level_1_id ?? null,
          grouping_level_2_id: condition.grouping_level_2_id ?? null,
          grouping_level_3_id: condition.grouping_level_3_id ?? null,
          grouping_level_4_id: condition.grouping_level_4_id ?? null,
          grouping_level_5_id: condition.grouping_level_5_id ?? null,
          grouping_level_6_id: condition.grouping_level_6_id ?? null,
        },
        index,
        "replace",
        type,
        e.target.value
      );
    },
    [conditionGroups]
  );

  const handleCollapse = useCallback(
    (shouldCollapse, conditionGroup, index) => {
      handleAddRemoveCondition(
        conditionGroup.id,
        conditionGroup.sub_group_level,
        {
          grouping_level_1_id: conditionGroup.grouping_level_1_id ?? null,
          grouping_level_2_id: conditionGroup.grouping_level_2_id ?? null,
          grouping_level_3_id: conditionGroup.grouping_level_3_id ?? null,
          grouping_level_4_id: conditionGroup.grouping_level_4_id ?? null,
          grouping_level_5_id: conditionGroup.grouping_level_5_id ?? null,
          grouping_level_6_id: conditionGroup.grouping_level_6_id ?? null,
        },
        index,
        "collapse"
      );
    },
    [conditionGroups]
  );

  const handleChangeSelectedCondition = useCallback(
    (e, groupId, condition) => {
      e.persist();
      const displayedCondition = displayedConditions.find(
        (o) => o.display === e.target.value
      );

      if (!displayedCondition) return;

      const newCondition = conditions.find(
        (c) => c.id === displayedCondition.id
      );

      // top level group - level 1
      const group = conditionGroups.find(
        (c) => c.id === condition.grouping_level_1_id
      );

      // conditions and groups can be nested to n levels so find group at each level
      let subGroup = group;
      const subGroups = [];
      for (let i = 1; i < condition.sub_group_level + 1; i++) {
        if (i > 1) {
          subGroup = subGroup.conditions.find((o) => {
            return o.id === condition[`grouping_level_${i}_id`];
          });
        }
        subGroups[i] = subGroup;
      }

      // update to only affect sent condition id
      let updatedCondition = subGroup.conditions.find(
        (o) => o.id === condition.id
      );
      updatedCondition = {
        ...updatedCondition,
        work_item_condition_id: newCondition.id,
        comparison: newCondition.comparison,
        value: newCondition.value,
        work_item_column_name: newCondition.work_item_column_name,
      };
      subGroup.conditions.splice(
        subGroup.conditions.findIndex((o) => o.id === condition.id),
        1,
        updatedCondition
      );

      cascadeUpdateUp(
        subGroup,
        condition,
        subGroups,
        conditionGroups,
        condition.sub_group_level + 1
      );
    },
    [conditionGroups]
  );

  const handleSave = (e) => {
    if (actionType === "CREATE") {
      setConditionsToAdd(conditionGroups);
    } else {
      logger.info("Stage Conditions Changed", {
        user_id: userId,
        username: userInfo.username,
        id: stageId,
        name: stageName,
        conditions: conditionGroups,
      });
      dispatch(handleSaveWorkStageConditions(stageId, conditionGroups))
        .then(() => {
          toggleHasChanges(true);
        })
        .catch(() => {
          toggleHasChanges(true); // make sure the save button is available again  on a failure
        });
    }

    toggleHasChanges(false);
  };

  return (
    <Modal open={open} handleClose={() => {}}>
      <div className="conditions-wrapper">
        <h2 className="title">Conditions</h2>
        <Button
          className="add-sub-group"
          onClick={() =>
            handleAddRemoveCondition(
              0,
              1,
              null,
              conditionGroups.length,
              "add",
              "group"
            )
          }
        >
          Add Group
        </Button>
        <div className="conditions" id="conditions">
          <div className="content">
            {conditionGroups.map((c, i) => (
              <ConditionGroup
                key={c.id}
                open={open}
                handleClose={handleClose}
                displayedOperands={displayedOperands}
                displayedConditions={displayedConditions}
                conditionGroup={c}
                conditionGroups={conditionGroups.filter((o) => !!o.conditions)}
                onUpdateOperand={handleUpdateOperand}
                index={i}
                onAddRemoveCondition={handleAddRemoveCondition}
                onCollapse={handleCollapse}
                onChangeSelectedCondition={handleChangeSelectedCondition}
              />
            ))}
          </div>
        </div>
        <div className="button-wrapper">
          <Button className="cancel" onClick={handleClose}>
            {!hasChanges ? "Close" : "Cancel"}
          </Button>
          <Button
            className="submit"
            onClick={(e) => handleSave(e)}
            disabled={!hasChanges}
          >
            Save
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default Conditions;
