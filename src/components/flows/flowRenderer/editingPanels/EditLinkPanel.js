// NPM PACKAGE IMPORTS
import React, { useState, useEffect, useMemo } from "react";
import { BsArrowReturnRight } from "react-icons/bs";
import Button from "msuite_storybook/dist/button/Button";
import Select from "msuite_storybook/dist/select/Select";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import { handleFetchItemOpenConditions } from "../../flowsActions";

// TRANSLATION IMPORTS
import useTranslations from "../../../../hooks/useTranslations";
import flowsTranslations from "../../flowsTranslations.json";

const EditLinkPanel = ({ selectedLink, deleteLink, updateSelectedLink }) => {
  const [itemOpenCondition, setItemOpenCondition] = useState("DEFAULT");

  const translate = useTranslations(flowsTranslations);
  const dispatch = useDispatch();
  const { itemOpenConditions } = useSelector((state) => state.flowsData);

  const drawingLevelLink = useMemo(() => {
    return selectedLink.data.drawingLevelLink;
  }, [selectedLink]);

  useEffect(() => {
    dispatch(handleFetchItemOpenConditions());
  }, []);

  useEffect(() => {
    setItemOpenCondition(selectedLink.data.itemOpenConditionId);
  }, [selectedLink]);

  const displayedItemOpenConditions = useMemo(() => {
    return itemOpenConditions.map((c) => ({
      id: c.id,
      value: c.id,
      display: translate(c.name),
    }));
  }, [itemOpenConditions]);

  return (
    <div className="edit-link-panel">
      <h1 className="name">
        {selectedLink.data.sourceStageName}
        <br></br>
        <span className="selected-stage">
          <BsArrowReturnRight />
        </span>
        {selectedLink.data.targetStageName}
      </h1>
      <div className="content">
        {drawingLevelLink ? (
          <p className="readonly-open-condition">
            {translate("Specific Item Open")}
          </p>
        ) : (
          <>
            <Select
              options={displayedItemOpenConditions}
              value={itemOpenCondition}
              onInput={(e) => {
                setItemOpenCondition(parseInt(e.target.value));
                updateSelectedLink(parseInt(e.target.value));
              }}
              placeholder={translate("-- Open Condition --")}
            />
            {itemOpenCondition !== selectedLink.data.itemOpenConditionId && (
              <p className="change-notification">
                {translate(
                  "Changes won't take effect until the work flow is saved!"
                )}
              </p>
            )}
          </>
        )}
      </div>
      <div className="action-buttons">
        {!drawingLevelLink && (
          <Button className="delete" onClick={deleteLink}>
            {translate("Remove from flow")}
          </Button>
        )}
      </div>
    </div>
  );
};

export default EditLinkPanel;
