@import "../../../styles/colors.scss";
@import "../../../styles/sizes.scss";

div.edit-stage-panel,
div.edit-link-panel {
  position: fixed;
  right: 0;
  top: 136px;
  bottom: 50px;
  background-color: $grey;
  border-left: 1px solid $lighterSlate;
  box-sizing: border-box;
  padding: 15px;
  z-index: 300;

  & > h1.name {
    margin: 0;
    cursor: pointer;
    font-size: 1.4rem;
    font-weight: normal;
    width: calc(300px - 30px);
    color: $lighterSlate;
  }

  & > div.action-buttons {
    display: flex;
    justify-content: flex-end;
    column-gap: 10px;
    flex: 0 1;

    & > button {
      height: 40px;
      border: 1px solid #333;

      &.save {
        font-size: 20px;
        padding: 5px 0 0 0;
        background-color: $fabProBlue;
        color: #fff;
        width: 50px;

        &:not(:disabled):hover {
          cursor: pointer;
          background-color: darken($fabProBlue, 10%);
        }
      }

      &.delete {
        background-color: darken($red, 10%);
        color: #fff;
        font-size: 0.9rem;
        width: 80px;
        padding: 0;
        cursor: pointer;

        &:hover {
          background-color: darken($red, 20%);
        }
      }

      &.cancel {
        background-color: $nestingYellow;
        color: #fff;
        font-size: 0.9rem;
        padding: 0;
        width: 80px;
        cursor: pointer;

        &:hover {
          background-color: darken($nestingYellow, 20%);
        }
      }
    }

    & > svg.delete-icon {
      color: darken($red, 10%);
      cursor: pointer;
      height: 30px;
      width: 50px;
      margin-top: auto;
      margin-bottom: 5px;

      &:hover {
        color: $red;
      }
    }
  }

  & > div.logic-gate-btn-wrapper {
    margin: auto 0 0 0;
  }
}

div.edit-link-panel {
  width: 300px;
  display: grid;
  grid-template-rows: 60px calc(100vh - 265px - 2.5rem) 30px;
  grid-template-columns: 70% 30%;

  & > h1.name {
    cursor: default;
    width: 100%;
    padding-bottom: 10px;
    font-size: 1.2rem;
    grid-column: 1/-1;

    & > svg {
      height: 20px;
      width: 20px;
      padding-right: 10px;
    }

    & > span.selected-stage {
      color: $fabProBlue;
    }
  }

  & > div.content {
    display: flex;
    flex-direction: column;
    row-gap: 5px;
    height: calc(100vh - 271.5px - 2.5rem);
    grid-column: 1/-1;

    & > div {
      height: 30px;

      & > div {
        height: 30px;

        & > select {
          height: 30px;
          font-size: 1rem;
        }
      }
    }

    & > p.readonly-open-condition {
      margin: 0;
      padding: 0;
      font-size: 1rem;
      color: $lighterSlate;
      height: 100%;
    }

    & > p.change-notification {
      padding: 0;
      margin: 0;
      color: $yellow;
      font-size: 0.7rem;
    }
  }

  & > div.action-buttons {
    grid-row: 3/-1;
    grid-column: 2/-1;
  }
}

div.edit-stage-panel {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  width: 500px;

  & select {
    height: 30px;
    font-size: 1rem;
  }

  input.name-input {
    width: calc(600px - 30px - 30px);
    height: 100%;
    border: none;
    border-bottom: 2px solid $blue;
    background-color: transparent;
    padding: 0 0 0 10px;
    box-sizing: border-box;
    font-size: 1.4rem;
    font-family: inherit;
    color: #fff;

    &:focus {
      outline: none;
    }
  }

  input.create-stage-name {
    width: 100%;
    height: 26px;
    padding-top: 5px 0 10px;
    margin-bottom: 20px;
  }

  & h1.name {
    margin-bottom: 20px;
    color: $fabProBlue;
    display: flex;
    justify-content: space-between;
    width: 100%;

    & span.name-value {
      height: 100%;
      padding-top: 5px;
      border-bottom: 2px solid transparent;
      width: calc(600px - 30px - 10px - 30px);
    }

    & > svg {
      padding-top: 5px;
    }

    &:hover {
      color: lighten($fabProBlue, 10%);
    }
  }

  & div.status-group {
    z-index: 2;
    display: flex;
    align-items: center;
    color: $lighterSlate;
    margin-bottom: 20px;

    & svg.required {
      color: #ff0000;
      height: 15px;
      border: none;
    }

    & select {
      height: 30px;
      font-size: 0.9rem;
      border-radius: 3px;
      margin-left: 10px;
    }

    & > label {
      padding-right: 10px;
    }

    & > span.search-wrapper {
      display: grid;
      grid-template-columns: 20px 150px;

      border: 1px solid #333;
      border-radius: 3px;

      & > span {
        background-color: $fabProBlue;
        color: #fff;
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
        border-right: 1px solid #333;

        display: flex;
        align-items: center;
        justify-content: center;
      }

      & > input {
        height: 20px;
        border-top-right-radius: 3px;
        border-bottom-right-radius: 3px;
        border: none;
      }
    }

    & > div.dropdown-wrapper {
      position: fixed;
      right: 206.5px;
      top: 220px;

      width: 170px;
      border: 1px solid #333;
      box-sizing: border-box;

      & > ul {
        padding: 0;
        margin: 0;
        list-style: none;
        background-color: #fff;
        width: 170px;
        color: #333;
        max-height: 400px;
        overflow-y: scroll;

        &::-webkit-scrollbar {
          width: 10px;
          background-color: #f5f5f5;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 2px;
          background-color: #555;
        }

        & > li {
          padding: 5px 0 5px 5px;
          height: 20px;
          line-height: 20px;
          cursor: pointer;

          &:hover {
            color: #fff;
            background-color: $fabProBlue;
          }
        }
      }
    }
  }

  & div.duplicate-stage-select {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    padding: 0 0 10px;
    color: $lighterSlate;
    // margin-bottom: 5px;

    & button {
      padding: 0 10px;
      margin-top: 3px; // line button up with dropdown
      height: 32px;
      background-color: $fabProBlue;
      color: #fff;
      border: 1px solid #333;
      font-size: 1rem;

      &:disabled {
        background-color: darken($fabProBlue, 20%);
      }
    }
  }

  & div.checkbox-wrapper {
    margin-bottom: 10px;
  }

  & div.columns {
    grid-column: 1/-1;
    position: relative;
    margin-bottom: 20px;

    & > div.selectable-filter-wrapper div.filter-button {
      font-size: 1rem;

      & > p {
        color: $lighterSlate;
        margin-left: 0;
      }

      & > svg {
        font-size: 1rem;
      }
    }

    & .selectable-filter-wrapper {
      display: inline-flex;

      & .filter-button p {
        // font-weight: normal;
        font-size: 0.9rem;
      }
    }

    & label.columns-label {
      color: $lighterSlate;
      display: inline-block;
      margin-right: 15px;
    }
  }

  & label.holdpoint,
  & label.nestable,
  & label.rejectable,
  & label.joint-log-toggle,
  & label.starting-stage,
  & label.groupable {
    // display: inline-flex;
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    height: 35px;
    color: $lighterSlate;
    width: min-content !important;
    white-space: nowrap;

    & > div {
      grid-column: 1/2;

      height: 25px;
      width: 25px;
      margin-right: 5px;
      display: inline-block;

      & > input {
        height: 25px;
        width: 25px;
        margin: 0;
        cursor: pointer;
      }
    }
  }

  & label.holdpoint {
    align-items: center;
  }

  & label.nestable,
  & label.rejectable {
    align-items: flex-end;
    padding-bottom: 5px;
  }

  & div.groupable {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    padding: 0 15px 0 0;
    color: $lighterSlate;
    font-size: 0.9rem;
    position: relative;

    & label.groupable {
      align-self: flex-start;
      position: relative;
      align-items: center;
    }
    & label.groupable-select {
      display: flex;
      align-items: center;
      position: relative;

      & svg.required {
        color: #ff0000;
        height: 40px;
        border: none;
      }

      & > div.selectable-filter-wrapper div.filter-button {
        color: $lighterSlate;

        & > svg {
          color: $fabProBlue;
        }
      }
    }
  }

  & > label.holdpoint {
    color: $lighterSlate;

    & > div {
      box-sizing: border-box;
    }
  }

  & > div.metric-rejectable-nestable {
    display: flex;
    grid-column: 1/-1;
    padding: 0 0 10px;
    justify-content: space-between;
    color: $lighterSlate;
    margin-bottom: 20px;

    & > label.metric {
      & svg.required {
        color: #ff0000;
        height: 15px;
        border: none;
      }

      & > div {
        height: 30px;
        & > div {
          height: 30px;
        }
      }
    }
  }

  & div.in-flow-inputs {
    margin-bottom: 20px;
  }

  & label.joint-log-toggle {
    font-size: 1rem;
    display: inline-flex;
    align-items: center;

    & div {
      position: initial;
      height: 25px;
    }

    & input[type="checkbox"] {
      height: 25px;
      width: 25px;
      margin: 0 5px 0 0;
    }
  }

  & div.stage-cost-code-wrapper {
    z-index: 1;
    position: relative;
    width: 250px;

    & > span {
      color: $lighterSlate;
    }

    & > div {
      height: 30px;

      & > input {
        height: 30px;
        font-size: 1rem;
      }
    }

    & > ul {
      position: absolute;
      top: 47px;
      left: 0;
      padding: 0;
      margin: 0;
      list-style: none;
      width: 100%;
      box-sizing: border-box;
      background-color: #fff;
      border: 1px solid #333;
      color: #333;

      & > li {
        padding: 5px;
        font-size: 1rem;
        cursor: pointer;
      }

      & > li:hover {
        background-color: $fabProBlue;
        color: #fff;
      }
    }
  }

  & > div.read-only-conditions {
    color: $lighterSlate;
    margin-top: 10px;
    padding-bottom: 10px;
    flex: 1;

    & > h2.title {
      margin: 0;
      padding: 0;
      font-size: 1.2rem;

      display: flex;
      justify-content: space-between;
      align-items: center;

      & > button {
        padding: 0 10px;
        width: 70px;
        height: 30px;
        line-height: 30px;
        background-color: $fabProBlue;
        color: #fff;
        border: 1px solid #333;
        font-size: 1rem;
      }
    }

    & > div.content {
      overflow-y: scroll;
      min-height: 180px;
      height: calc(100% - 40px);

      &::-webkit-scrollbar {
        width: 10px;
        background-color: #f5f5f5;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 2px;
        background-color: #555;
      }
    }

    & > div.content > div.condition-sub-group:not(:last-of-type) {
      padding-bottom: 10px;
      border-bottom: 2px solid $lighterSlate;
    }

    & > div.content > div.condition-sub-group {
      display: grid;
      grid-template-columns: 40px 1fr;
      row-gap: 5px;
      column-gap: 10px;
      font-size: 1.1rem;
      padding-top: 10px;
    }
  }

  & div.action-buttons {
    // flex: 1;
    align-content: flex-end;
    grid-row: 8/-1;
    grid-column: 2/-1;

    & button {
      margin-top: auto;
    }
  }
}

div.stage-conditions-wrapper {
  width: 500px;
  background-color: #fff;
  position: relative;

  & > h2.title {
    color: #fff;
    background-color: $fabProBlue;
    margin: 0;
    font-weight: normal;
    font-size: 1.2rem;
    padding: 0 10px;
    height: 40px;
    line-height: 40px;
  }

  & > button {
    position: absolute;
    right: 10px;
    top: 5px;
    height: 32px;
    padding: 0;
    font-size: 30px;
    border-radius: 5px;
    border: 1px solid #333;

    & > svg {
      color: $green;
    }

    &:hover {
      color: lighten($fabProBlue, 10%);
    }
  }

  & > div.conditions {
    color: $lighterSlate;
    padding: 10px;
    height: calc(100vh - 110px - 100px);
    min-height: 50px;
    max-height: 500px;
    overflow-y: scroll;

    &::-webkit-scrollbar {
      width: 10px;
      background-color: #f5f5f5;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background-color: #555;
    }

    & > div.content {
      display: grid;
      row-gap: 10px;

      & > div.new-condition-group {
        display: grid;
        grid-template-columns: 75px 1fr 60px;
        grid-template-rows: 30px 1fr;
        column-gap: 5px;
        padding-bottom: 10px;

        &:not(:last-of-type) {
          border-bottom: 2px solid #333;
        }

        & > div.sub-group-operand {
          grid-column: 1/2;
          grid-row: 1/2;

          & > div {
            height: 30px;
            & > div {
              height: 30px;
              & > select {
                font-size: 1rem;
                height: 30px;
              }
            }
          }
        }

        & > span.sub-group-operand-label {
          grid-column: 2/3;
          grid-row: 1/2;
          line-height: 30px;
          height: 30px;
        }

        & > svg.sub-group-delete {
          cursor: pointer;
          grid-column: 3/-1;
          align-self: center;

          &:hover {
            color: darken($red, 10%);
          }
        }

        & > div.condition-operand {
          & > div {
            height: 30px;
            & > div {
              height: 30px;
              & > select {
                font-size: 1rem;
                height: 30px;
              }
            }
          }
        }

        & > div.conditions-select {
          grid-column: 2/3;

          &:not(:last-of-type) {
            margin-bottom: 5px;
          }

          & > div {
            height: 30px;
            & > input {
              font-size: 1rem;
              height: 30px;
            }
          }
        }

        & > div.actions {
          grid-column: 3/-1;

          & > div.actions-wrapper {
            display: flex;
            column-gap: 5px;
            align-items: center;
            height: 30px;

            & > svg {
              cursor: pointer;

              &:hover {
                color: $fabProBlue;
              }
            }
          }
        }
      }
    }
  }

  & > div.button-wrapper {
    display: flex;
    justify-content: space-evenly;
    padding-bottom: 10px;

    & > button {
      padding: 0 10px;
      font-size: 0.8rem;
      width: 100px;
      height: 30px;
      border: 1px solid #333;
    }

    & > button.cancel {
      background-color: #fff;
      color: #333;

      &:hover {
        background-color: darken(#fff, 10%);
      }
    }

    & > button.submit {
      background-color: $green;
      color: #fff;

      &:not(:disabled):hover {
        background-color: darken($green, 10%);
      }
    }
  }
}
