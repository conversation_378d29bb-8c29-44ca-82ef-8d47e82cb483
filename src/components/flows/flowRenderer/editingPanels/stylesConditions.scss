@import "../../../styles/colors.scss";
@import "../../../styles/sizes.scss";

div.conditions-wrapper {
  width: calc(100vw - 155px);
  height: calc(100vh - 110px - 10px);
  background-color: #222;
  position: relative;
  display: flex;
  flex-direction: column;
  max-width: 850px;

  & > h2.title {
    color: #fff;
    background-color: $fabProBlue;
    margin: 0;
    font-weight: normal;
    font-size: 1.2rem;
    padding: 0 10px;
    height: 40px;
    line-height: 40px;
  }

  & > button {
    position: absolute;
    right: 10px;
    top: 5px;
    height: 32px;
    padding: 0;
    font-size: 1rem;
    padding: 0 5px;
    background-color: $green;
    color: white;
    border-radius: 5px;
    border: 1px solid #333;

    &:hover {
      color: lighten($fabProBlue, 10%);
    }
  }

  & > div.button-wrapper {
    display: flex;
    justify-content: space-evenly;
    padding-bottom: 10px;
    margin-top: auto;

    & > button {
      padding: 0 10px;
      font-size: 0.8rem;
      width: 100px;
      height: 30px;
      border: 1px solid #333;
      margin-top: 10px;
    }

    & > button.cancel {
      background-color: #fff;
      color: #333;

      &:hover {
        background-color: darken(#fff, 10%);
      }
    }

    & > button.submit {
      background-color: $green;
      color: #fff;

      &:not(:disabled):hover {
        background-color: darken($green, 10%);
      }
    }
  }

  & > div.conditions {
    color: $lighterSlate;
    padding: 10px;
    height: calc(100vh - 110px - 110px);
    min-height: 50px;
    overflow-y: scroll;
    display: flex;
    flex-direction: column;
    flex: 1;

    &::-webkit-scrollbar {
      width: 10px;
      background-color: #f5f5f5;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background-color: #555;
    }

    & > div.content {
      display: grid;
      & div.condition-group:first-child {
        & > div.row {
          margin-left: 65px;
        }
      }
      & div.condition-group.collapsed {
        & > div.row:not(:first-child) {
          display: none;
        }
      }
      & div.condition-group {
        padding: 30px 0px 0px;
        display: flex;
        flex-direction: column;

        &:not(:first-of-type) > div.row:not(:first-of-type) {
          margin-left: 65px;
        }

        & div.row {
          display: flex;
          align-items: flex-end;
        }

        & div.actions {
          grid-column: 3/-1;

          & > div.actions-wrapper {
            display: flex;
            column-gap: 10px;
            align-items: center;
            height: 25px;

            & > svg {
              cursor: pointer;

              &:hover {
                color: $fabProBlue;
              }
              &.add {
                margin: 0px 5px;
                color: $green;
              }
              &.cancel {
                color: $red;
              }
            }
          }
        }

        & div.group-header {
          align-items: center;
          height: 50px;
          padding-left: 5px;
          width: 100%;

          & > h4 {
            cursor: pointer;
            display: flex;

            & > svg {
              padding-left: 10px;
              align-self: flex-end;

              &:hover {
                color: $fabProBlue;
              }
            }
          }

          & .add-sub-group {
            font-size: 0.9rem;
            height: 25px;
            padding: 0 5px;
            background-color: $green;
            color: white;
          }
          & div.actions-wrapper {
            column-gap: 20px;
            padding-right: 10px;
          }
        }

        & div.condition-group-operand {
          margin-right: 5px;
          & > div {
            height: 25px;
            & > div {
              height: 25px;
              width: 60px;
              & > select {
                -webkit-appearance: none;
                -moz-appearance: none;
                font-size: 1rem;
                height: 25px;
                border: none;
                color: $fabProBlue;
                background-color: inherit;
                cursor: pointer;
              }
            }
          }
        }
        & > div > span {
          font-size: 1rem;
          height: 25px;
          min-width: 60px;
          margin-right: 5px;
        }

        & div.condition-wrapper {
          width: 500px;
          display: grid;
          grid-template-columns: 75px 1fr 60px;
          grid-template-rows: 25px 1fr;
          column-gap: 5px;
          padding: 5px 5px 0px 5px;

          & > div.condition-select {
            grid-column: 2/3;

            &:not(:last-of-type) {
              margin-bottom: 5px;
            }

            & > div {
              height: 25px;
              & > input {
                font-size: 1rem;
                height: 25px;
                border: none;
              }
            }
          }
          & > span {
            font-size: 1rem;
            padding-left: 5px;
          }

          & select.condition-operand {
            font-size: 1rem;
            height: 25px;
            border: none;
            color: $fabProBlue;
            background-color: inherit;
            -webkit-appearance: none;
            -moz-appearance: none;
            cursor: pointer;
          }

          & > span.sub-group-operand-label {
            grid-column: 2/3;
            grid-row: 1/2;
            line-height: 30px;
            height: 30px;
          }
        }
      }
    }
  }

  .level1 {
    background-color: #191919;
    width: 100%;
  }
  .level2 {
    background-color: #242424;
    width: 100%;
    & div.actions {
      margin-left: auto;
      margin-right: 100px;
    }
  }
  .level3 {
    background-color: #333;
    width: 100%;
    & div.actions {
      margin-left: auto;
      margin-right: 80px;
    }
  }
  .level4 {
    background-color: #444;
    width: 100%;
    & div.actions {
      margin-left: auto;
      margin-right: 60px;
    }
  }
  .level5 {
    background-color: #555;
    width: 100%;
    & div.actions {
      margin-left: auto;
      margin-right: 40px;
    }
  }
  .level6 {
    background-color: #666;
    width: 100%;
    & div.actions {
      margin-left: auto;
      margin-right: 20px;
    }
  }
  .level7 {
    background-color: #777;
    width: 100%;
    & div.actions {
      margin-left: auto;
      margin-right: 0;
    }
  }
}
