// NPM PACKAGE IMPORTS
import React, { useMemo, useState } from "react";
import { <PERSON>le } from "react-flow-renderer";

// STYLE IMPORTS
import { fabProBlue } from "../../../styles/colors";

const StartingNode = ({ data, ...props }) => {
  const [showHandles, toggleShowHandles] = useState(false);

  const className = useMemo(() => {
    let result = "node starting-node";

    if (data.holdpoint) {
      result += " holdpoint";
    }

    if (props.selected) result += " selected";

    return result;
  }, [data, props.selected]);

  return (
    <div
      className={className}
      onMouseEnter={() => toggleShowHandles(true)}
      onMouseLeave={() => toggleShowHandles(false)}
    >
      {data.holdpoint ? (
        <h2 className="shape-modifier">{data.label}</h2>
      ) : (
        data.label
      )}
      <Handle
        type="source"
        position="bottom"
        style={{
          width: 20,
          height: 20,
          backgroundColor: showHandles ? fabProBlue : "transparent",
          borderColor: showHandles ? "#FFF" : "transparent",
          bottom: "-10px",
          cursor: "pointer",
        }}
        onClick={data.onHandleClick}
      />
      {!data.logicGate ? (
        <span
          className="status-group-indicator"
          style={{
            backgroundColor: data.stageStatusGroupColor || "#000000",
          }}
          onClick={(e) => {
            e.persist();
            data.onStatusGroupClick({
              position: { x: e.clientX + 2.5, y: e.clientY + 2.5 },
              statusGroup: data.stageStatusGroupName,
              statusColor: data.stageStatusGroupColor,
            });
          }}
          onMouseEnter={data.onStatusColorMouseEnter}
          onMouseLeave={data.onStatusColorMouseLeave}
        ></span>
      ) : (
        <></>
      )}
    </div>
  );
};

export default StartingNode;
