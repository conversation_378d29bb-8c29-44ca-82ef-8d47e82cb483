// NPM PACKAGE IMPORTS
import React, { useMemo, useState } from "react";
import { Handle } from "react-flow-renderer";

// STYLE IMPORTS
import { fabProBlue } from "../../../styles/colors";

const StandardNode = ({ data, ...props }) => {
  const [showHandles, toggleShowHandles] = useState(false);

  const className = useMemo(() => {
    let result = "node";

    if (data.holdpoint) {
      result += " holdpoint";
    }

    if (data.logicGate === 1) {
      result += " and-logic-gate";
    } else if (data.logicGate === 2) {
      result += " or-logic-gate";
    }

    if (data.shippingBlockPosition) {
      result += " shipping-block-node";
    }

    if (props.selected) result += " selected";

    return result;
  }, [data, props.selected]);

  return (
    <div
      className={className}
      onMouseEnter={() => toggleShowHandles(true)}
      onMouseLeave={() => toggleShowHandles(false)}
    >
      {data.logicGate || data.holdpoint ? (
        <div className="border">
          <h2 className="shape-modifier">{data.label}</h2>
        </div>
      ) : (
        data.label
      )}
      <Handle
        type="target"
        position="top"
        style={{
          width: 20,
          height: 20,
          backgroundColor: showHandles ? fabProBlue : "transparent",
          borderColor: showHandles ? "#FFF" : "transparent",
          top: "-10px",
          cursor: "pointer",
        }}
      />
      {data.shippingBlockPosition !== 3 && (
        <Handle
          type="source"
          position="bottom"
          style={{
            width: 20,
            height: 20,
            backgroundColor: showHandles ? fabProBlue : "transparent",
            borderColor: showHandles ? "#FFF" : "transparent",
            bottom: "-10px",
            cursor: "pointer",
          }}
          onClick={data.onHandleClick}
        />
      )}
      {!data.logicGate ? (
        <span
          className="status-group-indicator"
          style={{
            backgroundColor: data.stageStatusGroupColor || "#000000",
          }}
          onClick={(e) => {
            e.persist();
            data.onStatusGroupClick({
              position: { x: e.clientX + 2.5, y: e.clientY + 2.5 },
              statusGroup: data.stageStatusGroupName,
              statusColor: data.stageStatusGroupColor,
            });
          }}
          onMouseEnter={data.onStatusColorMouseEnter}
          onMouseLeave={data.onStatusColorMouseLeave}
        ></span>
      ) : (
        <></>
      )}
    </div>
  );
};

export default StandardNode;
