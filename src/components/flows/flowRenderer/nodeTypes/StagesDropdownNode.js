// NPM PACKAGE IMPORTS
import React, { useState, useMemo, useRef } from "react";
import styled from "styled-components";
import { FaSearch } from "react-icons/fa";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../../hooks/useOutsideClick";
import { escapeRegExp } from "../../../../_utils";

// STYLE IMPORTS
import { blue, lighterGrey, lighterSlate, grey } from "../../../styles/colors";

const Wrapper = styled.div`
  background-color: ${lighterGrey};
  border: 2px solid rgba(0, 0, 0, 1);
  border-radius: 3px;
  color: ${blue};
  padding: 0;
  max-height: 330px;
  width: 200px;
  box-sizing: border-box;

  display: grid;
  grid-template-columns: 30px 1fr;

  & svg {
    height: 30px;
    width: 20px;
    margin: 0 auto;
  }

  & input {
    background-color: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    height: 30px;
    color: rgba(255, 255, 255, 1);

    &:focus {
      border-color: ${blue};
      outline: none;
    }
  }
`;

const StyledDropdown = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
  border-top: 2px solid rgba(0, 0, 0, 1);
  border-bottom-left-radius: 1px;
  border-bottom-right-radius: 1px;
  max-height: 290px;
  overflow-y: scroll;
  grid-column: 1/-1;
  box-sizing: border-box;

  & li {
    background-color: ${grey};
    color: ${lighterSlate};
    transition: background-color 250ms ease;
    padding: 10px;
    cursor: pointer;

    &:hover {
      background-color: ${lighterGrey};
    }

    &:last-of-type {
      border-bottom-left-radius: 3px;
      border-bottom-right-radius: 3px;
    }
  }
`;

const StagesDropdownNode = ({ data }) => {
  const [searchInput, updateSearchInput] = useState("");
  const [clickStart, setClickStart] = useState(0);

  const dropdownRef = useRef(null);
  useOutsideClick(dropdownRef, data.handleClose);

  const handleOnMouseUp = (stage) => {
    const diff = Date.now() - clickStart;

    if (diff < 500) {
      data.handleSelection(stage, stage.id === 0);
    }

    setClickStart(0);
  };

  const displayedStages = useMemo(() => {
    const pattern = new RegExp(escapeRegExp(searchInput), "i");
    return (data.stages || []).filter((s) =>
      pattern.test(`${s.id}. ${s.name}`)
    );
  }, [searchInput]);

  return (
    <Wrapper ref={dropdownRef} style={data.position}>
      <FaSearch />
      <input
        type="text"
        value={searchInput}
        onChange={(e) => updateSearchInput(e.target.value)}
        onKeyDown={(e) => e.stopPropagation()}
      />
      <StyledDropdown>
        {displayedStages.map((s) => {
          return (
            <li
              key={s.id}
              onMouseDown={() => setClickStart(Date.now())}
              onMouseUp={() => handleOnMouseUp(s)}
            >
              {s.name}
            </li>
          );
        })}
      </StyledDropdown>
    </Wrapper>
  );
};

export default StagesDropdownNode;
