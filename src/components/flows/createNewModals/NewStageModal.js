// NPM PACKAGE IMPORTS
import React, { useState, useCallback, useEffect, useMemo } from "react";
import Modal from "msuite_storybook/dist/modal/Modal";
import Input from "msuite_storybook/dist/input/Input";
import Select from "msuite_storybook/dist/select/Select";
import Button from "msuite_storybook/dist/button/Button";
import { CgImport } from "react-icons/cg";
import { FaTrashAlt, FaAsterisk } from "react-icons/fa";
import { BiPlus, BiMinus } from "react-icons/bi";
import { BsPlusSquareFill } from "react-icons/bs";
import { RiSave3Fill } from "react-icons/ri";
import { GiCancel } from "react-icons/gi";
import { useDispatch, useSelector } from "react-redux";
import ReactSelect from "react-select";

// REDUX IMPORTS
import {
  handleFetchWorkItemColumns,
  handleSaveCondition,
  handleFetchConditions,
  handleFetchWorkStageConditions,
  handleCreateWorkStage,
  handleFetchWorkStagesGet,
  handleFetchWorkStageColumns,
  handleFetchWorkStageGroupableColumns,
} from "../flowsActions";

// COMPONENT IMPORTS
import Condition from "../conditions/Condition";
import WorkItemColumns from "../WorkItemColumns";
import { ConditionObj, ConditionGroupObj } from "../conditions/utils";
import Filter from "../../reusable/filter/Filter";

const NewStageModal = ({ open, handleClose, translate }) => {
  const [nameValue, setNameValue] = useState("");
  const [stageToDuplicate, setStageToDuplicate] = useState(null);
  const [conditionToSave, setConditionToSave] = useState(ConditionObj(null));
  const [conditionsToAdd, setConditionsToAdd] = useState([]);
  const [importing, toggleImporting] = useState(false);
  const [saving, toggleSaving] = useState(false);
  const [holdpoint, setHoldpoint] = useState(false);
  const [selectedColumns, setSelectedColumns] = useState([]);
  const [selectedMetric, setSelectedMetric] = useState(null);
  const [nestable, setNestable] = useState(false);
  const [rejectable, setRejectable] = useState(false);
  const [groupable, setGroupable] = useState(false);
  const [selectedGroupableColumns, setSelectedGroupableColumns] = useState([]);
  const [materialNameObj, setMaterialNameObj] = useState(null);
  const [displayColumnSearch, setDisplayColumnSearch] = useState("");

  const dispatch = useDispatch();
  const { workItemColumns, conditions, workStages } = useSelector(
    (state) => state.flowsData
  );

  useEffect(() => {
    dispatch(handleFetchWorkItemColumns("work_items"));
    dispatch(handleFetchConditions());
    setSelectedColumns([]);
  }, []);

  useEffect(() => {
    if (workItemColumns && workItemColumns.length) {
      const materialName = workItemColumns.find(
        (c) => c.normal_name === "material_name"
      );

      if (materialName) setMaterialNameObj(materialName);
    }
  }, [workItemColumns]);

  useEffect(() => {
    if (materialNameObj) {
      const initialSelected = [materialNameObj];
      if (!selectedColumns.length) setSelectedColumns(initialSelected);
      if (!selectedGroupableColumns.length)
        setSelectedGroupableColumns(initialSelected);
    }
  }, [materialNameObj]);

  useEffect(() => {
    if (selectedMetric) {
      if (!selectedColumns.find((c) => c.id === selectedMetric)) {
        setSelectedMetric(null);
      }
    }
  }, [selectedColumns, selectedMetric]);

  const onChange = useCallback(
    (value, field) => {
      let newConditionObj = { ...conditionToSave };

      newConditionObj[field] = value;

      setConditionToSave(newConditionObj);
    },
    [conditionToSave]
  );

  const handleSave = useCallback(() => {
    dispatch(handleSaveCondition(conditionToSave)).then((res) => {
      if (!res.error) {
        setConditionToSave(ConditionObj(null));
        dispatch(handleFetchConditions());
        let newCondId = res[0].id;
        if (conditionsToAdd.length) {
          let lastCond =
            conditionsToAdd &&
            conditionsToAdd[conditionsToAdd.length - 1].conditions;
          let lastCondId = lastCond && lastCond[lastCond.length - 1].id;
          conditionsToAdd[conditionsToAdd.length - 1].conditions.push({
            id: lastCondId + 1,
            operand: "or",
            work_item_condition_id: newCondId,
          });
        } else {
          let cnc = {
            sub_group: 1,
            sub_group_operand: "start",
            conditions: [
              {
                id: 1,
                operand: "or",
                work_item_condition_id: newCondId,
              },
            ],
          };
          setConditionsToAdd([cnc]);
        }
      }
    });
  }, [conditionToSave, conditionsToAdd]);

  const displayedOperands = useMemo(() => {
    return [
      { id: 1, value: "or", display: "OR" },
      { id: 2, value: "and", display: "AND" },
    ];
  }, []);

  const updateConditionsToAdd = useCallback(
    (e, groupId, conditionId, property, conditionObjId) => {
      let updatedGroup = conditionsToAdd.find(
        (cta) => cta.sub_group === groupId
      );
      if (property === "sub_group_operand") {
        updatedGroup[property] = e.target.value;
      } else {
        updatedGroup.conditions.find((c) =>
          c.work_item_condition_id
            ? c.work_item_condition_id === conditionId
            : c.id === conditionObjId
        )[property] =
          property === "work_item_condition_id"
            ? parseInt(e.value)
            : e.target.value;
      }

      setConditionsToAdd(
        [
          ...conditionsToAdd.filter((cta) => cta.sub_group !== groupId),
          updatedGroup,
        ].sort((a, b) => a.sub_group - b.sub_group)
      );
    },
    [conditionsToAdd]
  );

  const handleDeleteSubGroup = useCallback(
    (groupId) => {
      setConditionsToAdd(
        conditionsToAdd.filter((cta) => cta.sub_group !== groupId)
      );
    },
    [conditionsToAdd]
  );

  const displayedConditions = useMemo(() => {
    let result = [];

    if (conditions) result = conditions;

    return result
      .map((c) => ({
        id: c.id,
        value: c.id,
        display: `${c.work_item_column_name} ${c.comparison} ${c.value}`,
      }))
      .sort((a, b) => {
        if (a.display.toLowerCase() < b.display.toLowerCase()) {
          return -1;
        } else return 1;
      });
  }, [conditions]);

  const handleAddConditionToGroup = useCallback(
    (groupId) => {
      let updatedGroup = {
        ...conditionsToAdd.find((cta) => cta.sub_group === groupId),
      };
      const id = updatedGroup.conditions.length + 1;
      updatedGroup.conditions.push({
        id,
        operand: "or",
        work_item_condition_id: null,
      });

      setConditionsToAdd(
        [
          ...conditionsToAdd.filter((cta) => cta.sub_group !== groupId),
          updatedGroup,
        ].sort((a, b) => a.sub_group - b.sub_group)
      );
    },
    [conditionsToAdd]
  );

  const handleRemoveConditionFromGroup = useCallback(
    (groupId, idx) => {
      let updatedGroup = {
        ...conditionsToAdd.find((cta) => cta.sub_group === groupId),
      };

      updatedGroup.conditions.splice(idx, 1);
      updatedGroup.conditions = updatedGroup.conditions.map((c, idx) => ({
        ...c,
        id: idx + 1,
      }));

      setConditionsToAdd(
        [
          ...conditionsToAdd.filter((cta) => cta.sub_group !== groupId),
          updatedGroup,
        ].sort((a, b) => a.sub_group - b.sub_group)
      );
    },
    [conditionsToAdd]
  );

  const handleAddConditionGroup = useCallback(() => {
    let subGroup = 1;
    let subGroupOperand = "start";

    let existingSubGroups = [];
    if (conditionsToAdd && conditionsToAdd.length) {
      subGroupOperand = "or";
      existingSubGroups = conditionsToAdd.map((cta) => cta.sub_group);
    }

    subGroup = existingSubGroups.length
      ? Math.max(...existingSubGroups) + 1
      : 1;
    setConditionsToAdd(
      [...conditionsToAdd, ConditionGroupObj(subGroup, subGroupOperand)].sort(
        (a, b) => a.sub_group - b.sub_group
      )
    );
  }, [conditionsToAdd]);

  const displayedWorkStages = useMemo(() => {
    if (workStages) {
      return workStages
        .map((ws) => {
          return {
            id: ws.id,
            value: JSON.stringify(ws),
            display: ws.name,
          };
        })
        .sort((a, b) => {
          if (a.display.toLowerCase() < b.display.toLowerCase()) return -1;
          else return 1;
        });
    } else return [];
  }, [workStages]);

  const handleDuplicate = useCallback(async () => {
    toggleImporting(true);
    await dispatch(handleFetchWorkStageConditions(stageToDuplicate.id)).then(
      (res) => {
        if (!res.error) {
          setConditionsToAdd(res);
        }
        return res;
      }
    );
    await dispatch(
      handleFetchWorkStageColumns([stageToDuplicate.id]),
      true
    ).then((res) => {
      if (!res.error) {
        if (res[0].work_item_columns)
          setSelectedColumns(res[0].work_item_columns);
      }
    });
    await dispatch(
      handleFetchWorkStageGroupableColumns([stageToDuplicate.id], 1)
    ).then((res) => {
      if (!res.error) {
        if (res[0].work_item_columns)
          setSelectedGroupableColumns(res[0].work_item_columns);
      }
    });
    setHoldpoint(stageToDuplicate.shape === "Diamond");
    setSelectedMetric(stageToDuplicate.metric_work_item_column_id);
    setNestable(!!stageToDuplicate.nestable);
    setRejectable(!!stageToDuplicate.rejectable);
    setGroupable(!!stageToDuplicate.groupable);
    toggleImporting(false);
  }, [stageToDuplicate]);

  const handleStageCreation = useCallback(() => {
    toggleSaving(true);
    dispatch(
      handleCreateWorkStage({
        name: nameValue,
        conditions: conditionsToAdd,
        holdpoint,
        stageToDuplicate,
        columns: selectedColumns,
        metric: selectedMetric,
        nestable,
        rejectable,
        groupable,
        selectedGroupableColumns,
      })
    ).then((res) => {
      toggleSaving(false);
      if (!res.error) {
        setNameValue("");
        setStageToDuplicate(null);
        setConditionToSave(ConditionObj(null));
        setConditionsToAdd([]);
        toggleImporting(false);
        setHoldpoint(false);
        setSelectedColumns([]);
        setSelectedMetric(null);
        setNestable(false);
        setRejectable(false);
        setGroupable(false);
        setSelectedGroupableColumns(materialNameObj ? [materialNameObj] : []);
        dispatch(handleFetchWorkStagesGet());
        handleClose();
      }
    });
  }, [
    nameValue,
    conditionsToAdd,
    selectedColumns,
    holdpoint,
    stageToDuplicate,
    selectedMetric,
    nestable,
    rejectable,
    groupable,
    selectedGroupableColumns,
  ]);

  const displayedSelectedColumns = useMemo(() => {
    return selectedColumns
      .filter((sc) => sc.usable_for_conditions === 1 && sc.is_custom === 0)
      .map((sc) => ({ id: sc.id, value: sc.id, display: sc.display_name }))
      .sort((a, b) =>
        a.display.toUpperCase() > b.display.toUpperCase() ? 1 : -1
      );
  }, [selectedColumns]);

  const displayedGroupableColumns = useMemo(() => {
    return selectedColumns
      .filter((sc) => sc.groupable)
      .sort((a, b) =>
        a.display_name.toUpperCase() > b.display_name.toUpperCase() ? 1 : -1
      );
  }, [selectedGroupableColumns, selectedColumns]);

  const selectGroupableColumns = useCallback(
    (newSelected) => {
      if (
        materialNameObj &&
        !newSelected.find((c) => c.id === materialNameObj.id)
      ) {
        setSelectedGroupableColumns([materialNameObj, ...newSelected]);
      } else setSelectedGroupableColumns(newSelected);
    },
    [displayedGroupableColumns, selectedGroupableColumns, materialNameObj]
  );

  const selectColumns = useCallback(
    (newSelected) => {
      let colsToSelect = [];
      if (
        materialNameObj &&
        !newSelected.find((c) => c.id === materialNameObj.id)
      ) {
        colsToSelect = [materialNameObj, ...newSelected];
      } else colsToSelect = newSelected;

      colsToSelect = colsToSelect.map((c, i) => {
        return {
          ...c,
          position: i + 1,
        };
      });

      setSelectedColumns(colsToSelect);
    },
    [workItemColumns, selectedColumns, materialNameObj]
  );

  const defaultCondValue = (c) => {
    let value = displayedConditions.filter(
      (dc) => dc.value === c.work_item_condition_id
    );
    return value.length
      ? { label: value[0].display, value: value[0].value }
      : null;
  };

  const customStyles = {
    control: (provided) => ({
      ...provided,
      minHeight: "30px",
    }),

    valueContainer: (provided) => ({
      ...provided,
      minHeight: "30px",
      padding: "0 6px",
    }),

    input: (provided) => ({
      ...provided,
      margin: "0px",
      position: "absolute",
      left: "4px",
      color: "black",
    }),
    indicatorSeparator: () => ({
      display: "none",
    }),
    indicatorsContainer: (provided) => ({
      ...provided,
      height: "30px",
    }),
    menuPortal: (base) => ({
      ...base,
      top: "462px",
      left: "89px",
    }),
    singleValue: () => ({
      whiteSpace: "wrap",
      color: "black",
    }),
  };

  return (
    <Modal open={open} handleClose={handleClose}>
      <div className="new-stage-modal" id="new-stage-modal">
        <h2>
          {translate("Create New Stage:")}
          <div className="actions">
            <Button
              onClick={handleStageCreation}
              disabled={
                !nameValue ||
                (nameValue && !nameValue.trim()) ||
                !selectedColumns.length ||
                !selectedMetric ||
                (groupable && !selectedGroupableColumns.length)
              }
            >
              <RiSave3Fill />
            </Button>
            <Button onClick={handleClose}>
              <GiCancel />
            </Button>
          </div>
        </h2>

        <div className="content">
          {saving ? (
            <></>
          ) : (
            <>
              <label>
                {translate("Name:")}
                <Input
                  value={nameValue}
                  onChange={(e) => setNameValue(e.target.value)}
                  placeholder={translate("-- Name --")}
                  required
                />
              </label>
              <div className="duplicate">
                <label>
                  {translate("Duplicate from existing stage:")}
                  <Select
                    options={displayedWorkStages}
                    value={
                      stageToDuplicate ? JSON.stringify(stageToDuplicate) : null
                    }
                    onInput={(e) =>
                      setStageToDuplicate(JSON.parse(e.target.value))
                    }
                    placeholder={translate("-- Existing Stages --")}
                  />
                </label>
                <Button
                  onClick={handleDuplicate}
                  disabled={!stageToDuplicate || importing}
                >
                  <CgImport />
                </Button>
              </div>
              <div className="holdpoint-and-columns">
                <div className="columns-wrapper">
                  <FaAsterisk className="required" />
                  <WorkItemColumns
                    workItemColumnsList={workItemColumns}
                    selectedColumns={selectedColumns}
                    setSelectedColumns={selectColumns}
                  />
                </div>
              </div>
              <div className="metric-wrapper">
                <label className="metric">
                  {translate("Metric:")}
                  <Select
                    options={displayedSelectedColumns}
                    value={selectedMetric}
                    onInput={(e) => setSelectedMetric(parseInt(e.target.value))}
                    placeholder={translate("-- Selected Columns --")}
                    required
                  />
                </label>
              </div>
              <div className="checkbox-wrapper">
                <div className="groupable">
                  <label className="groupable">
                    <Input
                      type="checkbox"
                      checked={groupable}
                      onChange={() => setGroupable(!groupable)}
                    />
                    {translate("Groupable?")}
                  </label>
                  {groupable && (
                    <label className="groupable-select">
                      <FaAsterisk className="required" />
                      <Filter
                        nameKey="display_name"
                        idKey="id"
                        type={translate("Columns To Group By")}
                        list={displayedGroupableColumns}
                        selected={selectedGroupableColumns}
                        setSelected={selectGroupableColumns}
                        handleParentSelect={selectGroupableColumns}
                        toggleAllSelections={(f) => f}
                        selectAll
                        orientation="HORIZONTAL"
                        smallView
                        alwaysCollapsed
                        preventFormat
                        searchInput={displayColumnSearch}
                        setSearchInput={setDisplayColumnSearch}
                      />
                    </label>
                  )}
                </div>
                <label className="holdpoint">
                  <Input
                    type="checkbox"
                    checked={holdpoint}
                    onChange={() => setHoldpoint(!holdpoint)}
                  />
                  {translate("Holdpoint?")}
                </label>
                <label className="nestable">
                  <Input
                    type="checkbox"
                    checked={nestable}
                    onChange={() => setNestable(!nestable)}
                  />
                  {translate("Nestable?")}
                </label>
                <label className="rejectable">
                  <Input
                    type="checkbox"
                    checked={rejectable}
                    onChange={() => setRejectable(!rejectable)}
                  />
                  {translate("Rejectable?")}
                </label>
              </div>
              <div className="new-condition">
                <h3>{translate("Create new condition:")}</h3>{" "}
                <Condition
                  key="new-condition"
                  conditionObj={conditionToSave}
                  columns={workItemColumns}
                  onChange={onChange}
                  translate={translate}
                  handleSave={handleSave}
                />
              </div>
              <div className="existing-conditions">
                <h3>
                  {translate("Add existing conditions:")}
                  <Button onClick={handleAddConditionGroup}>
                    <BsPlusSquareFill />
                  </Button>
                </h3>
                <div className="conditions">
                  {!importing ? (
                    conditionsToAdd && conditionsToAdd.length ? (
                      conditionsToAdd.map((cta, idx) => {
                        return (
                          <div
                            className="new-condition-group"
                            key={cta.sub_group}
                          >
                            {idx !== 0 && (
                              <>
                                <div className="sub-group-operand">
                                  <Select
                                    options={displayedOperands}
                                    value={cta.sub_group_operand}
                                    placeholder={translate(
                                      "-- Group Operand --"
                                    )}
                                    onInput={(e) =>
                                      updateConditionsToAdd(
                                        e,
                                        cta.sub_group,
                                        null,
                                        "sub_group_operand"
                                      )
                                    }
                                  />
                                </div>
                                <span className="sub-group-operand-label">
                                  {translate("< Group Operand")}
                                </span>
                              </>
                            )}
                            <FaTrashAlt
                              className="sub-group-delete"
                              onClick={() =>
                                handleDeleteSubGroup(cta.sub_group)
                              }
                            />
                            {cta.conditions.length ? (
                              cta.conditions.map((c, cidx) => (
                                <React.Fragment key={c.id}>
                                  <div className="condition-operand">
                                    {c.id > 1 && (
                                      <Select
                                        options={displayedOperands}
                                        value={c.operand}
                                        placeholder={translate("-- Operand --")}
                                        onInput={(e) =>
                                          updateConditionsToAdd(
                                            e,
                                            cta.sub_group,
                                            c.work_item_condition_id,
                                            "operand",
                                            c.id
                                          )
                                        }
                                      />
                                    )}
                                  </div>
                                  <div className="conditions-select">
                                    <ReactSelect
                                      options={displayedConditions
                                        .filter(
                                          (dc) =>
                                            !cta.conditions.find(
                                              (ctac) =>
                                                ctac.work_item_condition_id ===
                                                dc.id
                                            )
                                        )
                                        .map((c) => ({
                                          label: c.display,
                                          value: c.value,
                                        }))}
                                      onChange={(e) =>
                                        updateConditionsToAdd(
                                          e,
                                          cta.sub_group,
                                          c.work_item_condition_id,
                                          "work_item_condition_id",
                                          c.id
                                        )
                                      }
                                      menuPortalTarget={
                                        document.getElementsByClassName(
                                          "storybook-modal-content fade-in"
                                        )[0]
                                      }
                                      styles={customStyles}
                                      menuPlacement="top"
                                      isSearchable
                                      placeholder={translate("-- Condition --")}
                                      value={defaultCondValue(c)}
                                    />
                                  </div>
                                  <div className="actions">
                                    {c.id === cta.conditions.length && (
                                      <BiPlus
                                        onClick={() =>
                                          handleAddConditionToGroup(
                                            cta.sub_group
                                          )
                                        }
                                      />
                                    )}
                                    {cta.conditions.length > 1 && (
                                      <BiMinus
                                        onClick={() =>
                                          handleRemoveConditionFromGroup(
                                            cta.sub_group,
                                            cidx
                                          )
                                        }
                                      />
                                    )}
                                  </div>
                                </React.Fragment>
                              ))
                            ) : (
                              <></>
                            )}
                          </div>
                        );
                      })
                    ) : (
                      <></>
                    )
                  ) : (
                    <></>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default NewStageModal;
