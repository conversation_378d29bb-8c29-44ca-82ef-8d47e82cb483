// NPM PACKAGE IMPORTS
import React, { useCallback, useMemo, useState } from "react";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import Select from "msuite_storybook/dist/select/Select";
import { RiSave3Fill } from "react-icons/ri";
import { GiCancel } from "react-icons/gi";
import Input from "msuite_storybook/dist/input/Input";
import { useDispatch } from "react-redux";

// REDUX IMPORTS
import { handleCreateWorkFlow } from "../flowsActions";

const NewFlowModal = ({
  open,
  handleClose,
  translate,
  setSelectedFlow,
  flows,
}) => {
  const [nameValue, setNameValue] = useState("");
  const [defaultFlowValue, setDefaultFlowValue] = useState(false);
  const [flowToDuplicate, setFlowToDuplicate] = useState("(Blank Flow)");

  const dispatch = useDispatch();

  const handleFlowCreation = useCallback(() => {
    if (nameValue) {
      dispatch(
        handleCreateWorkFlow({
          name: nameValue,
          default: defaultFlowValue ? 1 : 0,
          flow_to_duplicate:
            flowToDuplicate === "(Blank Flow)"
              ? null
              : parseInt(flowToDuplicate),
        })
      ).then((res) => {
        if (!res.error) {
          setSelectedFlow(res.find((f) => f.name === nameValue));
          handleClose();
        }
      });
    }
  }, [nameValue, defaultFlowValue, flowToDuplicate]);

  const displayedFlows = useMemo(() => {
    if (flows) {
      return [
        { id: null, value: null, display: "(Blank Flow)" },
        ...flows.map((f) => ({ id: f.id, value: f.id, display: f.name })),
      ];
    } else return [{ id: null, value: null, display: "(Blank Flow)" }];
  }, [flows]);

  return (
    <Modal open={open} handleClose={handleClose}>
      <div className="new-flow-modal">
        <h2>
          {translate("Create New Flow:")}
          <div className="actions">
            <Button onClick={handleFlowCreation} disabled={!nameValue}>
              <RiSave3Fill />
            </Button>
            <Button onClick={handleClose}>
              <GiCancel />
            </Button>
          </div>
        </h2>

        <div className="content">
          <label>
            {translate("Name:")}
            <Input
              value={nameValue}
              onChange={(e) => setNameValue(e.target.value)}
              required
            />
          </label>
          <label className="default-flow">
            <Input
              type="checkbox"
              checked={defaultFlowValue}
              onChange={() => setDefaultFlowValue(!defaultFlowValue)}
            />
            {translate("Default Flow?")}
          </label>
          <label>
            {translate("Duplicate From:")}
            <Select
              options={displayedFlows}
              onInput={(e) => setFlowToDuplicate(e.target.value)}
              value={flowToDuplicate}
            />
          </label>
        </div>
      </div>
    </Modal>
  );
};

export default NewFlowModal;
