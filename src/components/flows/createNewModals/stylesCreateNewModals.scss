@import "../../styles/colors.scss";

div.new-stage-modal,
div.new-flow-modal {
  width: 600px;
  height: 500px;
  background-color: #fff;
  color: #333;
  box-sizing: border-box;

  display: grid;
  grid-template-rows: 30px 1fr;
  row-gap: 10px;
  column-gap: 10px;

  & > h2 {
    background-color: $fabProBlue;
    color: #fff;
    font-size: 1.2rem;
    margin: 0;
    padding: 0 15px 0 5px;
    line-height: 30px;
    border-bottom: 1px solid #333;

    display: flex;
    justify-content: space-between;
    align-items: center;

    & > div.actions {
      display: flex;
      column-gap: 10px;

      & > button {
        background-color: #fff;
        height: 25px;
        width: 25px;
        font-size: 1.3rem;
        padding: 1px 0 0;
        border: 1px solid #333;

        &:not(:disabled):hover {
          background-color: darken(#fff, 10%);
        }

        &:first-of-type {
          color: darken($green, 10%);
        }

        &:last-of-type {
          color: darken($red, 10%);
        }
      }
    }
  }

  & > div.content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 45px 30px 45px 60px 45px 1fr;
    justify-content: center;
    align-items: center;
    row-gap: 12px;
    position: relative;
    overflow-y: scroll;

    &::-webkit-scrollbar {
      width: 10px;
      background-color: #f5f5f5;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background-color: #555;
    }

    & > div.loader-wrapper {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
    }

    & > label,
    div.duplicate {
      padding: 0 10px;
      font-size: 0.8rem;

      display: grid;
      align-self: start;
    }

    &
      > label:not(.holdpoint):not(.default-flow):not(.nestable):not(.rejectable) {
      grid-template-rows: 16px 1fr;

      & > div {
        height: 30px;
        grid-row: 2/-1;
        grid-column: 1/2;

        & > input {
          height: 30px;
          font-size: 1rem;
        }
      }
    }

    & > div.metric-wrapper {
      display: flex;
      grid-column: 1/-1;
      padding: 0 10px;
      justify-content: space-between;

      & > label.metric {
        & > div > div {
          height: 30px;

          & > select {
            height: 30px;
            font-size: 1rem;
          }
        }
      }
    }

    & > div.duplicate {
      grid-template-columns: 1fr 30px;
      column-gap: 5px;

      & > label {
        grid-column: 1/2;

        & > div {
          height: 30px;

          & > div {
            height: 30px;

            & > select {
              height: 30px;
              font-size: 1rem;
            }
          }
        }
      }

      & > button {
        width: 25px;
        height: 25px;
        padding: 1.5px 0 0;
        margin-bottom: 2.5px;
        background-color: $fabProBlue;
        color: #fff;
        align-self: end;
        border: 1px solid #333;

        &:not(:disabled):hover {
          background-color: darken($fabProBlue, 10%);
        }
      }
    }

    & > div.holdpoint-and-columns {
      grid-column: 1/-1;

      display: flex;
      justify-content: space-between;

      padding: 0 15px 0 10px;

      & > div.columns-wrapper {
        display: flex;

        & > svg.required {
          color: #ff0000;
          height: 40px;
          border: none;
        }

        & > div.selectable-filter-wrapper div.filter-button {
          font-size: 1rem;

          & > p {
            color: #333;
          }

          & > svg {
            font-size: 1rem;
          }
        }
      }
    }

    & div.checkbox-wrapper {
      padding: 8px 15px;
      grid-column: 1/-1;
      display: grid;
      grid-template-rows: repeat(2, 25px);
      grid-template-columns: repeat(3, 1fr);
      row-gap: 10px;
      justify-content: space-between;
      height: 60px;

      & label {
        color: $darkGrey;
        width: auto;
      }
    }

    & div.groupable {
      grid-column: 1/-1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 25px;

      & > label.groupable {
        position: relative;
        align-self: flex-start;
        align-items: center;
      }

      & > label.groupable-select {
        display: flex;
        position: relative;
        align-items: center;

        & > svg.required {
          color: #ff0000;
          border: none;
        }
      }
    }

    & > div.new-condition {
      grid-column: 1/-1;
      grid-row: 5/6;
      padding: 0 15px 0 10px;

      display: flex;
      flex-direction: column;

      & > h3 {
        margin: 0;
        font-weight: normal;
        font-size: 0.8rem;

        grid-column: 1/-1;
      }

      & > div.condition {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 25px;
        column-gap: 10px;

        & > div {
          height: 30px;

          & > input {
            height: 30px;
            font-size: 1rem;
          }

          & > div {
            height: 30px;

            & > select {
              height: 30px;
              font-size: 1rem;
            }
          }
        }

        & > button {
          width: 25px;
          height: 25px;
          margin-bottom: 2.5px;
          align-self: end;
          padding: 2px 0 0;
          background-color: $fabProBlue;
          color: #fff;
          border: 1px solid #333;
        }
      }
    }

    & > div.existing-conditions {
      grid-row: 6/-1;
      grid-column: 1/-1;
      align-self: start;

      padding: 0 0 0 10px;

      & > h3 {
        margin: 0;
        font-weight: normal;
        font-size: 0.8rem;
        position: relative;
        display: flex;
        justify-content: space-between;
        padding: 0 0 10px;
        line-height: 20px;

        & > button {
          height: 24.9px;
          padding: 0;
          font-size: 23px;
          border-radius: 5px;
          color: $fabProBlue;
          border: 1px solid #333;
          margin-right: 15px;

          &:hover {
            color: lighten($fabProBlue, 10%);
          }
        }
      }

      & > div.conditions {
        display: flex;
        flex-direction: column;

        & > div.new-condition-group {
          display: grid;
          grid-template-columns: 75px 1fr 40px;
          grid-template-rows: 30px 1fr;
          column-gap: 5px;
          border-bottom: 1px solid #333;
          padding: 5px 0;

          &:first-of-type {
            border-top: 1px solid #333;
          }

          &:nth-of-type(odd) {
            background-color: #eee;
          }

          & > div.sub-group-operand {
            grid-column: 1/2;
            grid-row: 1/2;

            & > div {
              height: 30px;
              & > div {
                height: 30px;
                & > select {
                  font-size: 1rem;
                  height: 30px;
                }
              }
            }
          }

          & > span.sub-group-operand-label {
            grid-column: 2/3;
            grid-row: 1/2;
            line-height: 30px;
            height: 30px;
          }

          & > svg.sub-group-delete {
            cursor: pointer;
            grid-column: 3/-1;
            align-self: center;

            &:hover {
              color: darken($red, 10%);
            }
          }

          & > div.condition-operand {
            & > div {
              height: 30px;
              & > div {
                height: 30px;
                & > select {
                  font-size: 1rem;
                  height: 30px;
                }
              }
            }
          }

          & > div.conditions-select {
            grid-column: 2/3;

            &:not(:last-of-type) {
              padding-bottom: 5px;
            }

            & > div {
              min-height: 30px;
              & > div {
                min-height: 30px;
                & > select {
                  font-size: 1rem;
                  min-height: 30px;
                }
              }
            }
          }

          & > div.actions {
            grid-column: 3/-1;

            display: flex;
            column-gap: 5px;
            align-items: center;

            & > svg {
              cursor: pointer;

              &:hover {
                color: $fabProBlue;
              }
            }
          }
        }
      }
    }
  }

  & label.holdpoint,
  & label.default-flow,
  & label.nestable,
  & label.rejectable,
  & label.groupable,
  & label.starting-stage {
    display: inline-flex;
    align-items: center;
    color: $lighterSlate;
    width: min-content !important;
    white-space: nowrap;

    & > div {
      grid-column: 1/2;

      height: 25px;
      width: 25px;
      margin-right: 5px;
      display: inline-block;

      & > input {
        height: 25px;
        width: 25px;
        margin: 0;
        cursor: pointer;
      }
    }
  }
}

div.new-flow-modal {
  height: unset;
  width: 400px;

  & > div.content {
    grid-template-rows: 1fr;
    padding-bottom: 10px;
    box-sizing: border-box;

    & > label.default-flow {
      display: flex;
      margin-top: 18px;
    }

    & > label div {
      height: 30px;
    }

    & > label > div > div > select {
      height: 30px;
      font-size: 1rem;
    }
  }
}
