import React from "react";
import useMsuiteAPI from "../../hooks/useMsuiteAPI";
import { Button } from "@mui/material";

const AxiosHookTestUI = (props) => {
  const [results, fetch, cancel] = useMsuiteAPI(
    {
      url: "v2/drawings",
      method: "GET",
    },
    {
      autoCancel: false,
      manual: true,
    }
  );

  const fetchRandomPath = () => {
    const randomNumbers = Array.from({ length: 3 }, () =>
      Math.floor(Math.random() * 1000)
    );
    fetch({
      url: `v2/drawings/${randomNumbers.join(",")}`,
    });
  };

  const fetchRandomError = () => {
    const randomString = Math.random().toString(36).substring(7);
    fetch({
      url: `v2/drawing?invalid=${randomString}`,
    });
  };

  const fetchBadPackageId = () => {
    const badId = 1000000000;
    fetch({
      url: `packages/${badId}`,
    });
  };

  const fetchItemsAtMaterialStage = () => {
    fetch({
      url: `items/fetch`,
      method: "POST",
      data: {
        stage_ids: "4",
        app_type: "fab",
        // job_ids: '2,3,4,5,6,7,8,9,13,14,16,69,81,87,89,90,91,92,131,132,133,134,141,142,143,144,154,157,176,193,194,249,251,256,257,258,271,275,278,293,294,305,430,462,463,477,526,527',
      },
    });
  };

  const fetchItemsAtCutStage = () => {
    fetch({
      url: `items/fetch`,
      method: "POST",
      data: {
        stage_ids: "5",
        app_type: "fab",
      },
    });
  };

  return (
    <div
      style={{
        padding: "25px",
        display: "flex",
        flexDirection: "column",
        gap: "10px",
      }}
    >
      <div style={{ display: "flex", gap: "10px" }}>
        <Button
          variant="contained"
          style={{ width: "fit-content" }}
          onClick={() => {
            fetch({});
          }}
        >
          Fetch /drawings
        </Button>
        <Button
          variant="contained"
          style={{ width: "fit-content" }}
          onClick={fetchRandomPath}
        >
          Fetch /drawings/[randomId]
        </Button>
        <Button
          variant="contained"
          style={{ width: "fit-content" }}
          onClick={fetchRandomError}
        >
          Fetch /drawings with invalid query
        </Button>
        <Button
          variant="contained"
          style={{ width: "fit-content" }}
          onClick={fetchBadPackageId}
        >
          Fetch /packages/[badId]
        </Button>
        <Button
          variant="contained"
          style={{ width: "fit-content" }}
          onClick={fetchItemsAtMaterialStage}
        >
          Fetch /items at Mat Receiving
        </Button>
        <Button
          variant="contained"
          style={{ width: "fit-content" }}
          onClick={fetchItemsAtCutStage}
        >
          Fetch /items at Cut
        </Button>
        <br />

        <Button
          variant="contained"
          style={{ width: "fit-content" }}
          disabled={!results.loading}
          onClick={() => {
            cancel();
          }}
        >
          Cancel()
        </Button>
      </div>
      <div style={{ color: "white", marginTop: "20px" }}>
        <br />
        <p style={{ margin: "5px 0" }}>
          <strong>.loading</strong> {results.loading + ""}
        </p>
        <p style={{ margin: "5px 0" }}>
          <strong>.error:</strong> {results.error + ""}
        </p>
        <p style={{ margin: "5px 0" }}>
          <strong>.canceled</strong> {results.canceled + ""}
        </p>
        <p style={{ margin: "5px 0" }}>
          <strong>.data:</strong>
        </p>

        {/* only display text area if data isn't too long, otherwise chrome will crash */}
        {results?.data?.length < 100 ? (
          <textarea
            readOnly
            value={
              results.data
                ? JSON.stringify(results.data, null, 2)
                : results.data + ""
            }
            style={{
              width: "100%",
              minHeight: "100px",
              backgroundColor: "#333",
              color: "white",
              padding: "8px",
            }}
          />
        ) : (
          <p>Number of items returned: {results?.data?.length}</p>
        )}
      </div>
    </div>
  );
};

export default AxiosHookTestUI;
