import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { getCookie, removeCookie } from "../../_utils";
import { dataDogLogger } from "../../utils/_dataDogLogger";
import SplashScreen from "../reusable/splashScreen/SplashScreen";
import jwt from "jsonwebtoken";

import {
  handleReceivePermissions,
  handleReceiveUserId,
  handleReceiveRole,
  handleReceiveUserSettings,
  handleFetchSystemSettings,
  handleFetchUserInfo,
  handleReceiveToken,
  handleFetchShiftNow,
  handleClockIn,
  handleReceiveFeatures,
  handleCheckReleasesPageNotification,
  handleFetchViewingAll,
  handleReceiveZohoContactId,
} from "../profile/profileActions";
import { handleSetLanguage } from "../../redux/generalActions";
import { handleFetchActiveTimer } from "../timers/timersActions";
import { handleFetchPendingDrawingsExist } from "../drawings/drawingsActions";
import { handleLogout } from "./authActions";

import { identifyMixPanelUser } from "../../utils/_mixPanelUtils";

const AuthContext = React.createContext();

const AuthProvider = ({ children }) => {
  const [waitAuthCheck, setWaitAuthCheck] = useState(true);
  const dispatch = useDispatch();
  const newToken = window.location.search.split("token=")[1];

  const decodeToken = (_token) => {
    return jwt.decode(_token).data;
  };

  const setUserInfoFromToken = (_token) => {
    const {
      id,
      permissions,
      roleID,
      userSettings,
      features,
      zohoContactId,
      username,
    } = decodeToken(_token.trim());

    const company = username?.split("@")[1].split(".")[0];
    dataDogLogger.setUser({
      id: id,
      username: username,
      client: company,
    });
    dispatch(handleReceiveToken(_token));
    dispatch(handleReceiveUserId(id));
    dispatch(handleReceivePermissions(permissions));
    dispatch(handleReceiveRole(roleID));
    dispatch(handleReceiveUserSettings(userSettings));
    dispatch(handleReceiveZohoContactId(zohoContactId));
    dispatch(handleReceiveFeatures(features));
    dispatch(handleFetchSystemSettings(zohoContactId));
    dispatch(handleFetchShiftNow).then((res) => {
      if (
        res.error &&
        permissions &&
        !permissions.includes(289) &&
        !permissions.includes(296)
      ) {
        dispatch(handleClockIn());
      }
    });
    dispatch(handleFetchActiveTimer);
    dispatch(handleFetchPendingDrawingsExist(true));
    dispatch(handleCheckReleasesPageNotification);
    dispatch(handleFetchViewingAll);
    dispatch(handleFetchUserInfo(id));
  };

  useEffect(() => {
    window.ChurnZero.push(["setAppKey", process.env.REACT_APP_CHURN_APP_KEY]);

    // can this be done differently
    // #####
    if (!getCookie("PHPSESSID") && window.location.hostname !== "localhost")
      handleLogout();
    setInterval(() => {
      if (!getCookie("PHPSESSID") && window.location.hostname !== "localhost")
        handleLogout();
    }, 300000);
    // #####

    if (newToken) {
      setUserInfoFromToken(newToken);
      setWaitAuthCheck(false);
    } else {
      if (localStorage["msuite_token"] || getCookie("msuite_token")) {
        let token = localStorage["msuite_token"] || getCookie("msuite_token");

        if (
          getCookie("msuite_token") &&
          localStorage["msuite_token"] !== getCookie("msuite_token")
        ) {
          localStorage.removeItem("msuite_token");
          removeCookie("msuite_token");
          token = getCookie("msuite_token");
        }
        if (token) {
          setUserInfoFromToken(token);
          setWaitAuthCheck(false);
          // MixPanel Identify - ran when logging in using local method of hitting react login URL
          const { features, username } = jwt.decode(token)?.data;
          const initFinished = localStorage.getItem(`initFinished`);

          if (
            (initFinished && initFinished === "false" && username) ||
            (!initFinished && username)
          ) {
            identifyMixPanelUser(username, features);
            localStorage.setItem(`initFinished`, "true");
          }
        } else {
          // redirect to the login screen
          window.location.replace(`${process.env.REACT_APP_LOGIN}`);
        }
      } else {
        window.location.replace(`${process.env.REACT_APP_LOGIN}`);
      }
    }

    dispatch(handleSetLanguage(window.navigator.language));
  }, [dispatch]);

  return waitAuthCheck ? (
    <SplashScreen />
  ) : (
    <AuthContext.Provider>{children}</AuthContext.Provider>
  );
};

const useAuth = () => {
  const context = React.useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within a AuthProvider");
  }
  return context;
};

export { AuthProvider, useAuth };
