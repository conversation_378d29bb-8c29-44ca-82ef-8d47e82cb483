@import "../../styles/colors.scss";

div.create-labels-modal {
  display: grid;
  grid-template-rows: 30px 1fr;
  background-color: #fff;

  & > h2.title {
    background-color: $fabProBlue;
    color: #fff;
    line-height: 30px;
    padding: 0 10px;
    margin: 0;
    font-size: 1rem;
  }

  & > div.content {
    padding: 10px;

    display: flex;
    flex-direction: column;
    row-gap: 10px;

    & > form {
      display: flex;
      column-gap: 10px;

      & > button {
        font-size: 1rem;
        border: 1px solid #333;
        height: 30px;
        padding: 0 10px;

        display: flex;
        align-items: center;
        justify-content: center;
        column-gap: 5px;

        &.cancel {
          background-color: #fff;
          color: #333;

          &:hover {
            background-color: darken(#fff, 10%);
          }
        }

        &.submit {
          background-color: $fabProBlue;
          color: #fff;

          &:hover {
            background-color: darken($fabProBlue, 10%);
          }
        }
      }
    }
  }
}
