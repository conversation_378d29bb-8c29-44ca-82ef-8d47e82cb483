// NPM PACKAGE IMPORTS
import React, { useState, useMemo } from "react";
import { useSelector } from "react-redux";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import { FiTag } from "react-icons/fi";

// STYLES IMPORTS
import "./stylesLabelsModal.scss";
import { trackMixPanelEvent } from "../../../utils/_mixPanelUtils";

const Labels = ({
  currentTable,
  drawingIds,
  packageIds,
  translate,
  cutlist,
  handleClose,
}) => {
  const { userId, userInfo } = useSelector((state) => state.profileData);
  const handleSubmit = () => {
    if (window && window.ChurnZero) {
      window.ChurnZero.push([
        "trackEvent",
        "FP Labels Printed",
        "Labels Printed",
        1,
        {
          Product: "FabPro",
          SubGroup: "Labels",
          Version: process.env.REACT_APP_ENVIRONMENT,
          UserName: userInfo.username,
          UserId: userId,
        },
      ]);
      // unable to track duration and quantity here
      trackMixPanelEvent("Labels Printed", null, "Labels");
    }
    handleClose();
  };
  return (
    <form
      method="post"
      action={`${process.env.REACT_APP_FABPRO}/exports/drawing-label.php`}
      target="_blank"
    >
      <input type="hidden" name="Action" value="create_labels" />
      <input
        type="hidden"
        name={currentTable === "DRAWINGS" ? "DrawingIDs" : "PackageIDs"}
        value={currentTable === "DRAWINGS" ? drawingIds : packageIds}
      />
      <input type="hidden" name="cutlist" value={cutlist ? 1 : 0} />
      <Button className="cancel" type="button" onClick={handleClose}>
        {translate("Cancel")}
      </Button>
      <Button
        className="submit"
        type="submit"
        onClick={() => setTimeout(() => handleSubmit(), 10)}
      >
        <FiTag />
        {translate("Labels")}
      </Button>
    </form>
  );
};

const LabelsModal = ({
  open,
  handleClose,
  translate,
  currentTable,
  selectedRows,
}) => {
  const [includeCutlist, toggleIncludeCutlist] = useState(false);

  const drawingIds = useMemo(() => {
    if (currentTable === "DRAWINGS")
      return selectedRows.map((r) => r.id).join(",");
    else return "";
  }, [selectedRows, currentTable]);

  const packageIds = useMemo(() => {
    if (currentTable === "PACKAGES")
      return selectedRows.map((r) => r.id).join(",");
    else
      return Array.from(new Set(selectedRows.map((r) => r.package_id))).join(
        ","
      );
  }, [selectedRows, currentTable]);

  return (
    <Modal open={open} handleClose={handleClose}>
      <div className="create-labels-modal">
        <h2 className="title">{translate("Create Labels")}</h2>
        <div className="content">
          <label>
            <input
              type="checkbox"
              onChange={() => toggleIncludeCutlist(!includeCutlist)}
              defaultChecked={includeCutlist}
            />
            {translate("Include Cuts?")}
          </label>
          <Labels
            translate={translate}
            currentTable={currentTable}
            drawingIds={drawingIds}
            packageIds={packageIds}
            cutlist={includeCutlist}
            handleClose={handleClose}
          />
        </div>
      </div>
    </Modal>
  );
};

export default LabelsModal;
