@import "../../styles/colors.scss";

.more-info-modal-wrapper {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  z-index: 30;
  animation: fadein 0.2s linear;
  border-radius: 3px;
  box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);
  height: 350px;
  width: 600px;

  header {
    padding: 0 20px;
    height: 40px;
    background-color: $blue;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
    font-size: 0.9rem;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;

    h4 {
      margin: 0;
      font-weight: normal;
    }
    & svg {
      font-size: 0.8rem;
      cursor: pointer;
    }
  }
}

section.more-info-wrapper {
  padding: 10px 20px 30px;

  & > div.status-tracker,
  & > p.no-status-tracker {
    border-bottom: 1px dashed #333;
    padding: 10px 0;
  }

  & > div.status-tracker {
    height: 80px;
    overflow-x: scroll;
    overflow-y: hidden;

    &::-webkit-scrollbar {
      height: 10px;
      background-color: #f5f5f5;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background-color: #555;
    }
  }

  & > p.no-status-tracker {
    margin: 0;
  }

  & div.jobs-info {
    display: flex;
    align-items: baseline;
    justify-content: space-between;
  }

  & span {
    color: $offGrey;
  }

  & span.more-info-span {
    display: flex;
    align-items: center;
    font-size: 0.8rem;
    font-weight: 500;
    height: 40px;

    & p {
      padding-left: 10px;
      font-size: 0.9rem;
      font-weight: bold;
    }

    & p.foreman {
      color: $orange;
    }
  }

  & button.laydown-location {
    background-color: transparent;
    border: none;
    color: $blue;
    font-size: 0.8rem;
    text-decoration: underline;
    font-weight: bold;
    cursor: pointer;
    margin: 10px 0 0 -5px;
  }

  div.packages-info {
    display: flex;
    justify-content: space-between;

    & textarea {
      height: 60px;
      font-size: 0.8rem;
      width: 100%;
      border-radius: 3px;
    }
  }

  & div.file-viewer-row {
    display: flex;
    justify-content: flex-start;
    margin-top: 20px;

    & button:first-of-type {
      margin-right: 10px;
    }

    & button {
      border: 1px solid $blue;
      background-color: transparent;
      color: $blue;
      height: 28px;
      width: 80px;
      border-radius: 3px;
      font-size: 0.8rem;
      font-weight: bold;
      cursor: pointer;

      &:hover {
        background-color: $blue;
        color: white;
      }
    }
  }
}

.header-row {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;

  & button:focus {
    border: none;
    outline: none;
  }

  & button:first-of-type {
    margin-right: 20px;
  }

  & p,
  button.selected {
    font-size: 0.9rem;
    font-weight: bold;
    border: none;
    border-bottom: 2px solid $blue;
    color: $blue;
    background-color: transparent;
  }

  button.normal {
    cursor: pointer;
    border: none;
    color: $darkGrey;
    font-size: 0.9rem;
    font-weight: bold;
    background-color: transparent;
  }

  button.view-drawings-button {
    border: none;
    background-color: transparent;
    color: $blue;
    font-size: 0.7rem;
    font-weight: bold;
    padding: 5px 8px;
    cursor: pointer;
    margin-left: auto;

    & a {
      color: inherit;
      text-decoration: none;
    }
  }
}
