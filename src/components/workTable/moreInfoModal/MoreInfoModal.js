// NPM PACKAGE IMPORTS
import React, { useRef, useEffect, useState, useMemo } from "react";
import Tracker from "msuite_storybook/dist/tracker/Tracker";
import { useSelector, useDispatch } from "react-redux";

// REDUX IMPORTS
import { handleFetchStatusTrackerStages as handleFetchPackagesStatusTrackerStages } from "../../packages/packagesActions";

// COMPONENT IMPORTS
import PercentComplete from "../../reusable/percentComplete/PercentComplete";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../hooks/useOutsideClick";

// STYLE IMPORTS
import "./stylesMoreInfoModal.scss";

const MoreInfoModal = ({
  showModal,
  title,
  onClose,
  type,
  rowInfo,
  togglePDFViewer,
  toggleForgeViewer,
}) => {
  const [parentData, setParentData] = useState(null);
  const [currentData, setCurrentData] = useState(rowInfo);
  const [modalType, setModalType] = useState(type);
  const [modalTitle, setModalTitle] = useState(title);
  const [onHoldCheck, toggleOnHoldCheck] = useState(false);

  const dispatch = useDispatch();
  const { jobs } = useSelector((state) => state.jobsData);
  const { statusTracker: packagesStatusTracker } = useSelector(
    (state) => state.packagesData
  );

  const wrapperRef = useRef(null);

  useEffect(() => {
    setParentData(null);
    toggleOnHoldCheck(false);
  }, []);

  useEffect(() => {
    if (rowInfo) {
      if (type === "PACKAGES")
        dispatch(handleFetchPackagesStatusTrackerStages(rowInfo.id));
    }
  }, [type, rowInfo]);

  useEffect(() => {
    if (type === "PACKAGES") {
      const job = jobs.find((job) => job.job_name === rowInfo.job_name);
      setParentData(job);
    }
  }, [type, jobs, rowInfo]);

  useEffect(() => {
    if (type === "PACKAGES" && modalType === "JOBS") {
      setModalTitle(parentData.job_name);
      setCurrentData(parentData);
    } else if (modalType === "PACKAGES") {
      setCurrentData(rowInfo);
      const on_hold = currentData.on_hold;
      toggleOnHoldCheck(on_hold === 0 ? false : true);
      setModalTitle(rowInfo.package_name);
    }
  }, [modalType, type, parentData, currentData, rowInfo]);

  const displayedPackagesStatusTracker = useMemo(() => {
    return packagesStatusTracker.map((pst) => ({
      ...pst,
      stage_id: pst.stage_status_group_name || "Ungrouped",
      stage_name: pst.stage_status_group_name || "Ungrouped",
    }));
  }, [packagesStatusTracker]);

  const JobsSection = () => (
    <section className="more-info-wrapper">
      <div className="header-row">
        <button
          className={modalType === "JOBS" ? `selected` : "normal"}
          disabled={modalType === "JOBS"}
        >
          Job
        </button>
        {type === "PACKAGES" && (
          <button className="normal" onClick={() => setModalType("PACKAGES")}>
            Package
          </button>
        )}
      </div>
      <div className="jobs-info">
        <div>
          <span className="more-info-span">
            Foreman:{" "}
            <p className="foreman">
              {currentData.foreman && `${currentData.foreman}`}
            </p>
          </span>
          <span className="more-info-span">
            Address:
            {currentData.address && (
              <p>{`${currentData.address}, ${currentData.city}, ${currentData.state}, ${currentData.zip}`}</p>
            )}
          </span>
          <button className="laydown-location">
            Manage Jobsite Laydown Locations
          </button>
        </div>
        <PercentComplete value={currentData.percent_complete} />
      </div>
    </section>
  );

  const PackagesSection = () => (
    <section className="more-info-wrapper">
      <div className="header-row">
        <button
          className="normal"
          disabled={!parentData}
          onClick={() => setModalType("JOBS")}
        >
          Job
        </button>
        <button className={modalType === "PACKAGES" ? `selected` : ""}>
          Package
        </button>

        <button className="view-drawings-button">
          <a
            href={`${process.env.REACT_APP_FABPRO}/exports/view_all_drawings.php?PackageID=${currentData.id}&archived=0`}
          >
            View All Drawings
          </a>
        </button>
      </div>
      {displayedPackagesStatusTracker.length ? (
        <div className="status-tracker">
          <Tracker data={displayedPackagesStatusTracker} />
        </div>
      ) : (
        <p className="no-status-tracker">No tracker to display.</p>
      )}
      <div className="packages-info">
        <div>
          <span className="more-info-span">Special Delivery Instructions</span>
          <textarea className="delivery-textarea" />
        </div>
        <div>
          <span className="more-info-span">On Hold Status</span>
          <input
            type="checkbox"
            onChange={() => toggleOnHoldCheck(!onHoldCheck)}
            checked={onHoldCheck}
          />
        </div>
        <PercentComplete value={currentData.percent_complete} />
      </div>
      <div className="file-viewer-row">
        <button
        // onClick={() => {
        //   toggleForgeViewer();
        //   onClose();
        // }}
        >
          Forge
        </button>
        <button
          onClick={() => {
            togglePDFViewer();
            onClose();
          }}
        >
          PDF
        </button>
      </div>
    </section>
  );

  const DrawingsSection = () => (
    <section className="drawings-more-info-wrapper">
      <p>Drawings</p>
    </section>
  );

  useOutsideClick(wrapperRef, () => {
    onClose && onClose();
  });

  return showModal ? (
    <>
      <div ref={wrapperRef} className="more-info-modal-wrapper">
        <header>
          <h4>{modalTitle}</h4>
        </header>
        {modalType === "JOBS" && <JobsSection />}
        {modalType === "PACKAGES" && <PackagesSection />}
        {modalType === "DRAWINGS" && <DrawingsSection />}
      </div>
      <div className="modal-overlay" />
    </>
  ) : (
    <></>
  );
};

export default MoreInfoModal;
