// NPM PACKAGE IMPORTS
import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
} from "react";
import { v4 as uuid } from "uuid";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import { handleChangeItemWorkflow } from "../flows/flowsActions";
import { handleFetchContainers } from "../drawings/drawingsActions";

// COMPONENT IMPORTS
import AgTable from "../reusable/agTable/AgTable";
import TableContainer from "../reusable/tableContainer/TableContainer";
import WorkflowModal from "../reusable/workflowModal/WorkflowModal";
import InfiniteScrollTable from "../reusable/infiniteScrollTable/InfiniteScrollTable";

// CONSTANTS IMPORTS
import { frameworkComponents, generateTabs } from "../jobs/jobsConstants";
import { drawingsColumnDefs } from "../drawings/drawingsConstants";

// HELPER FUNCTION IMPORTS
import {
  permissionLock,
  onDisplayedColumnsChanged,
  generateTime,
  escapeRegExp,
  formatDate,
} from "../../_utils";
import {
  sortAndFilter,
  searchTable,
  columnsToExcludeFromGlobalSearch,
  associateCustomColumnData,
} from "../../utils/agTable";
import {
  handleEnqueueSpinner,
  handleDequeueSpinner,
} from "../reusable/generalSpinner/generalSpinnerActions";

import "../styles/tables.scss";
import {
  fetchCustomColumnDataMultiItem,
  fetchCustomColumnDataPerItem,
} from "../../_services";
import usePrevious from "../../hooks/usePrevious";
import axios from "axios";

const CUSTOM_COLUMNS_FEATURE_ID = 53;

// EXPORTS
const DrawingsTable = ({
  showAllWork,
  toggleShowAllWork,
  setStatusTrackerPopup,
  selectedDrawings,
  toggleMoreInfo,
  moreInfoClick,
  rowInfo,
  selectedRows,
  setSelectedRows,
  setRowInfo,
  showWorkflowModal,
  toggleWorkflowModal,
  showScroll,
  setFile,
  togglePDFViewer,
  setCurrentRowOrder,
  handleSaveSortState,
  sortState,
  storeDrawingsGridOptionsApi,
  onDrawingsCellValueChanged,
  onCellEditingStarted,
  drawingCustomColumns,
  associateCustomColumnData,
  setDisplayedPdf,
  setSelectedDrawings,
}) => {
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [searchInput, setSearchInput] = useState("");
  const [totalRowsCount, setTotalRowsCount] = useState("");
  const [columnApi, setColumnApi] = useState(null);

  const showAllWorkRef = useRef(false);

  const dispatch = useDispatch();
  const { drawings, savedColumnState, containers } = useSelector(
    (state) => state.drawingsData
  );

  const { flows } = useSelector((state) => state.flowsData);
  const { features, token } = useSelector((state) => state.profileData);

  useEffect(() => {
    dispatch(handleFetchContainers());
    return () => {
      setGridOptionsApi(null);
    };
  }, []);

  // whenever the currentTable changes, refresh table
  useEffect(() => {
    if (gridOptionsApi) {
      drawingsAfterSearch.current = selectedDrawings;
      gridOptionsApi.stopEditing();
      refreshTable();
    }
  }, [gridOptionsApi, drawings, selectedDrawings, dispatch, showAllWork]);

  useEffect(() => {
    showAllWorkRef.current = showAllWork;
  }, [showAllWork]);

  const changeWorkflowSubmit = useCallback(
    (newWorkflow) => {
      dispatch(
        handleChangeItemWorkflow(
          rowInfo.id,
          rowInfo.name ? "drawing" : "package",
          newWorkflow
        )
      ).then((res) => {
        if (!res.error) {
          const newRowInfo = {
            ...rowInfo,
            work_flow_id: newWorkflow,
            work_flow_name: flows.find((f) => f.id === newWorkflow).name,
          };
          const currentNode = gridOptionsApi.getRowNode(newRowInfo.id);
          if (currentNode) {
            currentNode.setData(newRowInfo);
            gridOptionsApi.refreshCells({
              rowNodes: [currentNode],
              force: true,
            });
          }
          setRowInfo(newRowInfo);
          toggleWorkflowModal(false);
          setSelectedDrawings((prevValue) => {
            let newValue = prevValue;
            if (newValue.length) {
              const index = newValue.findIndex((k) => k.id === newRowInfo.id);
              newValue[index] = { ...newRowInfo };
              return newValue;
            }
          });
          drawings.map((k) => {
            if (k.id === newRowInfo.id) {
              k["work_flow_id"] = newRowInfo["work_flow_id"];
              k["work_flow_name"] = newRowInfo["work_flow_name"];
            }
            return k;
          });
        }
      });
    },
    [rowInfo]
  );
  const hasCustomColumnFeature = features?.includes(CUSTOM_COLUMNS_FEATURE_ID);
  const refreshTable = () => {
    if (selectedDrawings && selectedDrawings.length) {
      const result = [];

      if (hasCustomColumnFeature) {
        gridOptionsApi?.refreshInfiniteCache();
        // Remove the filtered out items from the selection if there is any there
        if (hasCustomColumnFeature) {
          setSelectedRows((prevState) => {
            const selectedDrwingIds = selectedDrawings.map((o) => o.id);
            return [
              ...prevState.filter((item) =>
                selectedDrwingIds.includes(item.id)
              ),
            ];
          });
        }
      } else {
        gridOptionsApi?.setRowData(selectedDrawings);
        gridOptionsApi?.forEachNodeAfterFilterAndSort((n) => {
          result.push(n?.data);
        });
      }

      setCurrentRowOrder(result);
    } else {
      const result = [];
      if (hasCustomColumnFeature) {
        gridOptionsApi?.refreshInfiniteCache();
      } else {
        gridOptionsApi?.setRowData(drawings);
        gridOptionsApi?.forEachNodeAfterFilterAndSort((n) => {
          result.push(n?.data);
        });
      }
      setCurrentRowOrder(result);
    }

    // TODO - possible performance improvement here if we move this logic inside the forEachNodeAfterFilterAndSort above
    !hasCustomColumnFeature &&
      gridOptionsApi.forEachNode((n) => {
        if (selectedRows.find((r) => r.id === n.id)) n.setSelected(true);
      });
  };

  const permissionLockedColumns = useMemo(() => {
    if (savedColumnState && containers)
      return permissionLock(
        drawingsColumnDefs(
          savedColumnState,
          containers,
          moreInfoClick,
          toggleMoreInfo,
          togglePDFViewer,
          sortState,
          drawingCustomColumns,
          null,
          setDisplayedPdf
        )
      );
    else return null;
  }, [savedColumnState, containers]);

  const onSortChanged = (params) => {
    if (!hasCustomColumnFeature) {
      params.api.redrawRows();

      const result = [];
      params.api.forEachNodeAfterFilterAndSort((n) => {
        result.push(n.data);
      });
      setCurrentRowOrder(result);
    }

    const sortedColumn = params.columnApi.getAllColumns().find((c) => c.sort);
    dispatch(
      handleSaveSortState(
        sortedColumn ? sortedColumn.colId : null,
        sortedColumn ? sortedColumn.sort : null,
        "DRAWINGS"
      )
    );
  };
  const onFilterChanged = (params) => {
    const result = [];
    params.api.forEachNodeAfterFilterAndSort((n) => {
      result.push(n.data);
    });
    setCurrentRowOrder(result);
  };
  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };
  const onSelectionChanged = (params) => {
    let rows = params.api.getSelectedRows();
    return setSelectedRows(rows);
  };

  const containersRef = useRef(null);

  useEffect(() => {
    if (!containers) containersRef.current = null;
    containersRef.current = containers;
  }, [containers]);

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
    storeDrawingsGridOptionsApi(params.api);
    setColumnApi(params.columnApi);
    const drawingMatch = /(?!(\?|^|\&))drawing_id=\d{1,}(?=(\&|$))/.exec(
      window.location.search
    );
    const viewerMatch = /(?!(\?|^|\&))viewer=1(?=(\&|$))/.exec(
      window.location.search
    );
    if (drawingMatch && viewerMatch) {
      const drawingId = parseInt(drawingMatch[0].split("=")[1]);
      const viewerBool = !!parseInt(viewerMatch[0].split("=")[1]);

      if (viewerBool) {
        const rowNode = params.api.getRowNode(drawingId);
        setRowInfo(rowNode.data);
        togglePDFViewer();
        setDisplayedPdf(rowNode.data);
      }
    }

    const result = [];
    params.api.forEachNodeAfterFilterAndSort((n) => {
      result.push(n.data);
    });
    setCurrentRowOrder(result);
  };

  const onInfiniteGridReady = (params) => {
    storeDrawingsGridOptionsApi(params.api);
    setGridOptionsApi(params.api);
    setColumnApi(params.columnApi);

    const dataSource = {
      rowCount: selectedDrawings?.length,
      getRows: async (params) => {
        const spinnerId = uuid();
        dispatch(handleEnqueueSpinner(spinnerId, 2000));

        const dataAfterSortAndFilter = sortAndFilter(
          drawingsAfterSearch?.current,
          params.sortModel,
          params.filterModel
        );

        const rowsThisPage = dataAfterSortAndFilter.slice(
          params.startRow,
          params.endRow
        );
        let lastRow = -1;

        if (dataAfterSortAndFilter.length <= params.endRow) {
          lastRow = dataAfterSortAndFilter.length;
        }

        let rowsWithCustomData = [];

        if (selectedDrawings.length <= params.endRow)
          lastRow = selectedDrawings.length;

        // Fetch the custom column data for all the rows on this page as a hashmap
        // and add that data to each row
        const rowIds = rowsThisPage.map((x) => x.id);
        const customColumnData = await fetchCustomColumnDataMultiItem(
          "drawings",
          rowIds
        );
        for (let row of rowsThisPage) {
          if (row.id in customColumnData) {
            const formattedCustomColumnData = associateCustomColumnData(
              customColumnData[row.id],
              drawingCustomColumns
            );
            rowsWithCustomData.push({ ...formattedCustomColumnData, ...row });
          } else {
            rowsWithCustomData.push({ ...row });
          }
        }

        // pass updated data with custom column data into grid
        params.successCallback(rowsWithCustomData, lastRow);
        dispatch(handleDequeueSpinner(spinnerId));
      },
    };

    params.api.setDatasource(dataSource);
  };

  const gridOptions = {
    rowData:
      selectedDrawings && !selectedDrawings.length
        ? drawings
        : selectedDrawings,
    columnDefs: permissionLockedColumns,
    frameworkComponents: frameworkComponents(setStatusTrackerPopup),
    reactNext: true,
    rowClassRules,
    onSelectionChanged,
    onSortChanged,
    onFilterChanged,
    onGridReady,
    rowSelection: "multiple",
    suppressRowClickSelection: true,
    defaultColDef: {
      cellClass: ["no-border", "custom-wrap"],
      wrapText: true,
      suppressSizeToFit: showScroll ? true : false,
    },
    tabToNextCell: () => null,
    pagination: true,
    paginationPageSize: 100,
    editType: "fullRow",
    getRowNodeId: (data) => data.id,
    onDisplayedColumnsChanged: (params) =>
      onDisplayedColumnsChanged("DRAWINGS", params),
    onCellValueChanged: (params) =>
      onDrawingsCellValueChanged(params, containersRef),
    onCellEditingStarted,
    stopEditingWhenGridLosesFocus: true,
    getContextMenuItems: (params) => {
      const defaultItems = params.defaultItems;
      const customItems = defaultItems.filter((item) => {
        return (
          item !== "export" &&
          item !== "exportCsv" &&
          item !== "exportExcel" &&
          item !== "separator"
        );
      });
      return customItems;
    },
  };

  const infiniteGridOptions = {
    rowModelType: "infinite",
    onGridReady: onInfiniteGridReady,

    // display lines per page
    cacheBlockSize: 30,

    // how many rows to seek ahead when unknown data size.
    cacheOverflowSize: 0,

    // how many concurrent data requests are allowed.
    maxConcurrentDatasourceRequests: 1,

    // how many rows to initially allow scrolling to in the grid.
    infiniteInitialRowCount: 100,

    // how many pages to hold in the cache.
    maxBlocksInCache: 2,

    blockLoadDebounceMillis: 200,

    columnDefs: permissionLockedColumns,
    frameworkComponents: frameworkComponents(setStatusTrackerPopup),
    defaultColDef: {
      cellClass: ["no-border", "custom-wrap"],
      wrapText: true,
      suppressSizeToFit: showScroll ? true : false,
    },
    suppressRowClickSelection: true,
    onDisplayedColumnsChanged: (params) =>
      onDisplayedColumnsChanged("DRAWINGS", params),
    rowData:
      selectedDrawings && !selectedDrawings.length
        ? drawings
        : selectedDrawings,
    reactNext: true,
    rowClassRules,
    onSortChanged,
    rowSelection: "multiple",
    tabToNextCell: () => null,
    pagination: false,
    editType: "fullRow",
    getRowNodeId: (data) => data.id,
    onCellValueChanged: (params) =>
      onDrawingsCellValueChanged(params, containersRef),
    onCellEditingStarted,
    stopEditingWhenGridLosesFocus: true,
    getContextMenuItems: (params) => {
      const defaultItems = params.defaultItems;
      const customItems = defaultItems.filter((item) => {
        return (
          item !== "export" &&
          item !== "exportCsv" &&
          item !== "exportExcel" &&
          item !== "separator"
        );
      });
      return customItems;
    },
  };

  const exportParams = useMemo(() => {
    if (!permissionLockedColumns || !permissionLockedColumns.length) return;

    const exportedColumns = permissionLockedColumns
      .filter((col) => col.field && !col.hide)
      .map((col) => col.field);

    return {
      columnKeys: exportedColumns,
      fileName: "drawings",
      processCellCallback: (params) => {
        const { colId } = params.column;
        if (colId === "due_date" || colId === "fab_completed_on") {
          if (!params.value) return "";

          const f_date =
            colId === "fab_completed_on"
              ? generateTime(params.value * 1000, false, true, "-")
              : formatDate(params.value);

          return f_date;
        } else if (colId === "percent_complete") {
          if (!params.value) return;
          return params.value.toFixed(1);
        } else if (typeof params.value === "number" && colId !== "id") {
          if (!params.value) return;
          return params.value.toFixed(2);
        } else return params.value;
      },
    };
  }, [permissionLockedColumns]);

  const handleExcelExport = () => {
    if (gridOptionsApi) gridOptionsApi.exportDataAsExcel(exportParams);
  };
  const handleCSVExport = () => {
    if (gridOptionsApi) gridOptionsApi.exportDataAsCsv(exportParams);
  };

  /**
   *  Call the export API to trigger a download
   *  Have to use axios directly because useApiCall() doesn't support any of this
   */
  const handleExportTable = async (outputFormat = "xlsx") => {
    const apiBaseURL = process.env.REACT_APP_API;
    const drawing_ids = drawingsAfterSearch.current.map(
      (drawing) => drawing.id
    );
    const method = "POST";
    const query = new URLSearchParams({ outputFormat }).toString();
    const apiPath = `drawings/export?${query}`;

    const config = {
      method,
      url: apiPath,
      baseURL: apiBaseURL,
      headers: { Authorization: `Bearer ${token}` },
      responseType: "arraybuffer", // Ensure binary data is handled properly
      data: { drawing_ids },
    };

    try {
      const response = await axios(config);
      // trigger the browser download
      const mimeType =
        outputFormat === "csv"
          ? "text/csv"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      const blob = new Blob([response.data], { type: mimeType });
      const href = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `drawings-export.${outputFormat}`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
    } catch (error) {
      console.error("Export failed:", error);
      alert("Export failed. Please try again.");
    }
  };

  const drawingsAfterSearch = useRef(selectedDrawings);
  const previousSearchInput = usePrevious(searchInput);

  useEffect(() => {
    if (!gridOptionsApi || !columnApi || previousSearchInput === searchInput)
      return;

    const isInfiniteGrid = hasCustomColumnFeature;

    const visibleColumns = columnApi
      .getAllDisplayedColumns()
      ?.filter(
        (col) =>
          col.colDef.colId &&
          !columnsToExcludeFromGlobalSearch.includes(col.colDef.colId)
      );

    if (!searchInput) {
      drawingsAfterSearch.current = selectedDrawings;
      isInfiniteGrid
        ? gridOptionsApi.purgeInfiniteCache()
        : gridOptionsApi.setRowData(selectedDrawings);
    } else {
      const itemsAfterSearch = searchTable(
        escapeRegExp(searchInput),
        selectedDrawings,
        visibleColumns
      );

      drawingsAfterSearch.current = itemsAfterSearch;
      isInfiniteGrid
        ? gridOptionsApi.purgeInfiniteCache()
        : gridOptionsApi.setRowData(itemsAfterSearch);
    }
  }, [searchInput, gridOptionsApi, columnApi, selectedDrawings]);

  return (
    <>
      {drawings && permissionLockedColumns && (
        <TableContainer
          tabs={generateTabs("EXISTING_JOBS", [], showAllWork)}
          handleToggle={() => toggleShowAllWork(!showAllWork)}
          handleExcel={
            hasCustomColumnFeature
              ? () => handleExportTable("xlsx")
              : handleExcelExport
          }
          handleCSV={
            hasCustomColumnFeature
              ? () => handleExportTable("csv")
              : handleCSVExport
          }
          searchInput={searchInput}
          setSearchInput={setSearchInput}
          currentTable={"DRAWINGS"}
          selectedRows={selectedRows}
          totalRows={totalRowsCount}
          isInfiniteGrid={hasCustomColumnFeature}
        >
          {hasCustomColumnFeature ? (
            <InfiniteScrollTable
              totalNumberOfRows={drawingsAfterSearch.current?.length || 0}
              gridOptions={infiniteGridOptions}
              overrideCheckboxSelectColumn={true}
              setSelectedRows={setSelectedRows}
              selectedRowIds={selectedRows.map((item) => item.id)}
              onSelectAll={(isSelected) => {
                if (isSelected)
                  setSelectedRows(() => [...drawingsAfterSearch.current]);
                else setSelectedRows(() => []);
              }}
            />
          ) : (
            <AgTable gridOptions={gridOptions} />
          )}
        </TableContainer>
      )}
      {showWorkflowModal && (
        <WorkflowModal
          open={showWorkflowModal}
          handleClose={() => toggleWorkflowModal(false)}
          handleSubmit={changeWorkflowSubmit}
          selectedItem={rowInfo}
        />
      )}
    </>
  );
};

export default DrawingsTable;
