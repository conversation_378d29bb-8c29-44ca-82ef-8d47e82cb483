// NPM PACKAGE IMPORTS
import React, { useState, useEffect, useMemo, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { v4 as uuid } from "uuid";

// REDUX IMPORTS
import { handleFetchPackages } from "../packages/packagesActions";
import { handleFetchDrawings } from "../drawings/drawingsActions";

// COMPONENT IMPORTS
import AgTable from "../reusable/agTable/AgTable";
import TableContainer from "../reusable/tableContainer/TableContainer";
import InfiniteScrollTable from "../reusable/infiniteScrollTable/InfiniteScrollTable";

// CONSTANTS IMPORTS
import {
  jobsColumnDefs,
  frameworkComponents,
  generateTabs,
} from "../jobs/jobsConstants";

// HELPER FUNCTION IMPORTS
import {
  permissionLock,
  onDisplayedColumnsChanged,
  generateTime,
  escapeRegExp,
} from "../../_utils";
import {
  sortAndFilter,
  associateCustomColumnData,
  searchTable,
  columnsToExcludeFromGlobalSearch,
} from "../../utils/agTable";
import { fetchCustomColumnDataMultiItem } from "../../_services";
import usePrevious from "../../hooks/usePrevious";
import {
  handleEnqueueSpinner,
  handleDequeueSpinner,
} from "../reusable/generalSpinner/generalSpinnerActions";
import axios from "axios";

const CUSTOM_COLUMNS_FEATURE_ID = 53;

// EXPORTS
const JobsTable = ({
  toggleMoreInfo,
  selectedJobs,
  showAllWork,
  toggleShowAllWork,
  setSelectedJobs,
  setCurrentTable,
  moreInfoClick,
  selectedRows,
  setSelectedRows,
  showScroll,
  setCurrentRowOrder,
  handleSaveSortState,
  sortState,
  storeJobsGridOptionsApi,
  onJobsCellValueChanged,
  onCellEditingStarted,
  handlePackageFilterSelection,
  handleDrawingFilterSelection,
  customColumns,
}) => {
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [searchInput, setSearchInput] = useState("");
  const [totalRowsCount, setTotalRowsCount] = useState("");
  const [columnApi, setColumnApi] = useState(null);

  const dispatch = useDispatch();

  const { jobs, savedColumnState } = useSelector((state) => state.jobsData);
  const { features, token } = useSelector((state) => state.profileData);

  const hasCustomColumnFeature = features?.includes(CUSTOM_COLUMNS_FEATURE_ID);

  // whenever the currentTable changes, refresh table
  useEffect(() => {
    if (!gridOptionsApi) return;
    refreshTable();
  }, [gridOptionsApi, selectedJobs, dispatch, showAllWork, jobs]);

  useEffect(() => {
    if (selectedJobs?.length) {
      displayedJobsRef.current = selectedJobs;
    } else displayedJobsRef.current = jobs;
  }, [selectedJobs, jobs]);

  const handleChildSelection = (type, rowData) => {
    setSelectedJobs([rowData]);
    dispatch(handleFetchPackages([rowData.id], showAllWork)).then(
      async (res) => {
        if (res.error) return;

        const packagesToSelect = res;
        await handlePackageFilterSelection(packagesToSelect, [rowData]);
        if (type === "DRAWINGS") {
          return dispatch(
            handleFetchDrawings(
              packagesToSelect.map((p) => p.id),
              showAllWork
            )
          ).then(async (res) => {
            if (res.error) return;
            return handleDrawingFilterSelection(res, packagesToSelect, [
              rowData,
            ]);
          });
        }
        setCurrentRowOrder(null);
        return setCurrentTable("PACKAGES");
      }
    );
  };

  const refreshTable = () => {
    if (selectedJobs.length) {
      if (hasCustomColumnFeature) {
        gridOptionsApi.purgeInfiniteCache();
        // Remove the filtered out items from the selection if there is any there
        setSelectedRows((prevState) => {
          const selectedJobsIds = selectedJobs.map((o) => o.id);
          return [
            ...prevState.filter((item) => selectedJobsIds.includes(item.id)),
          ];
        });
      } else {
        gridOptionsApi.setRowData(selectedJobs || []);
      }
    } else {
      if (hasCustomColumnFeature) {
        gridOptionsApi.purgeInfiniteCache();
      } else {
        gridOptionsApi.setRowData(jobs || []);
      }
    }
    hasCustomColumnFeature &&
      gridOptionsApi.forEachNode((n) => {
        if (selectedRows.find((r) => r.id === n.id)) n.setSelected(true);
      });
  };

  const permissionLockedColumns = useMemo(() => {
    if (savedColumnState)
      return permissionLock(
        jobsColumnDefs(
          savedColumnState,
          moreInfoClick,
          toggleMoreInfo,
          handleChildSelection,
          sortState,
          null,
          hasCustomColumnFeature && customColumns
        )
      );
    else return null;
  }, [savedColumnState]);

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
    storeJobsGridOptionsApi(params.api);
    setColumnApi(params.columnApi);
  };
  const onSortChanged = (params) => {
    // params.api.redrawRows();
    const sortedColumn = params.columnApi.getAllColumns().find((c) => c.sort);

    dispatch(
      handleSaveSortState(
        sortedColumn ? sortedColumn.colId : null,
        sortedColumn ? sortedColumn.sort : null,
        "JOBS"
      )
    );
  };
  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };

  const onSelectionChanged = (params) => {
    let rows = params.api.getSelectedRows();
    return setSelectedRows(rows);
  };

  const gridOptions = {
    rowData: selectedJobs && !selectedJobs.length ? jobs : selectedJobs,
    frameworkComponents: frameworkComponents(),
    reactNext: true,
    rowClassRules,
    onSelectionChanged,
    onGridReady,
    rowSelection: "multiple",
    suppressRowClickSelection: true,
    defaultColDef: {
      cellClass: ["no-border", "custom-wrap"],
      wrapText: true,
      suppressSizeToFit: false,
      // @TODO - these width properties don't seem to be applyied for whatever reason, could be case for all default defs
      minWidth: 120,
      maxWidth: 140,
      autoHeight: true,
    },
    editType: "fullRow",
    columnDefs: permissionLockedColumns,
    tabToNextCell: () => null,
    pagination: true,
    paginationPageSize: 100,
    getRowNodeId: (data) => data.id,
    onDisplayedColumnsChanged: (params) =>
      onDisplayedColumnsChanged("JOBS", params),
    onCellValueChanged: onJobsCellValueChanged,
    onCellEditingStarted,
    stopEditingWhenGridLosesFocus: true,
    onSortChanged,
    getContextMenuItems: (params) => {
      const defaultItems = params.defaultItems;
      const customItems = defaultItems.filter((item) => {
        return (
          item !== "export" &&
          item !== "exportCsv" &&
          item !== "exportExcel" &&
          item !== "separator"
        );
      });
      return customItems;
    },
  };

  const selectedJobsRef = useRef(null);
  const displayedJobsRef = useRef(null);

  useEffect(() => {
    selectedJobsRef.current = selectedJobs || [];
  }, [selectedJobs]);

  const onInfiniteGridReady = (params) => {
    storeJobsGridOptionsApi(params.api);
    setGridOptionsApi(params.api);
    setColumnApi(params.columnApi);

    const dataSource = {
      rowCount: displayedJobsRef?.current?.length,
      getRows: async (params) => {
        if (displayedJobsRef?.current === null) {
          params.successCallback([], 0);
          return;
        }

        const spinnerId = uuid();
        dispatch(handleEnqueueSpinner(spinnerId, 2000));
        const dataAfterSortAndFilter = sortAndFilter(
          displayedJobsRef?.current,
          params.sortModel,
          params.filterModel
        );

        const rowsThisPage = dataAfterSortAndFilter.slice(
          params.startRow,
          params.endRow
        );
        let lastRow = -1;

        if (dataAfterSortAndFilter.length <= params.endRow) {
          lastRow = dataAfterSortAndFilter.length;
        }

        let rowsWithCustomData = [];

        // Fetch the custom column data for all the rows on this page as a hashmap
        // and add that data to each row
        const rowIds = rowsThisPage.map((x) => x.id);
        const customColumnData = await fetchCustomColumnDataMultiItem(
          "jobs",
          rowIds
        );
        for (let row of rowsThisPage) {
          if (row.id in customColumnData) {
            const formattedCustomColumnData = associateCustomColumnData(
              customColumnData[row.id],
              customColumns
            );
            rowsWithCustomData.push({ ...formattedCustomColumnData, ...row });
          } else {
            rowsWithCustomData.push({ ...row });
          }
        }

        params.successCallback(rowsWithCustomData, lastRow);
        dispatch(handleDequeueSpinner(spinnerId));
      },
    };

    params.api.setDatasource(dataSource);
  };

  /**
   * @type {import('ag-grid-community').GridOptions}
   */
  const gridOptionsInfinite = {
    rowModelType: "infinite",
    onGridReady: onInfiniteGridReady,

    // display lines per page
    cacheBlockSize: 30,

    // how many rows to seek ahead when unknown data size.
    cacheOverflowSize: 0,

    // how many concurrent data requests are allowed.
    maxConcurrentDatasourceRequests: 1,

    // how many rows to initially allow scrolling to in the grid.
    infiniteInitialRowCount: 100,
    // how many pages to hold in the cache.
    maxBlocksInCache: 2,

    blockLoadDebounceMillis: 200,

    columnDefs: permissionLockedColumns,
    frameworkComponents: frameworkComponents(),
    defaultColDef: {
      cellClass: ["no-border", "custom-wrap"],
      wrapText: true,
      suppressSizeToFit: false,
      sortable: true,
    },
    suppressRowClickSelection: true,
    editType: "fullRow",
    getRowNodeId: (data) => data.id,
    onDisplayedColumnsChanged: (params) =>
      onDisplayedColumnsChanged("JOBS", params),
    reactNext: true,
    rowClassRules,
    onSortChanged,
    rowSelection: "multiple",
    onCellValueChanged: onJobsCellValueChanged,
    onCellEditingStarted,
    stopEditingWhenGridLosesFocus: true,
    getContextMenuItems: (params) => {
      const defaultItems = params.defaultItems;
      const customItems = defaultItems.filter((item) => {
        return (
          item !== "export" &&
          item !== "exportCsv" &&
          item !== "exportExcel" &&
          item !== "separator"
        );
      });
      return customItems;
    },
  };

  const exportParams = useMemo(() => {
    if (!permissionLockedColumns || !permissionLockedColumns.length) return;

    const exportedColumns = permissionLockedColumns
      .filter((col) => col.field && !col.hide)
      .map((col) => col.field);

    return {
      columnKeys: exportedColumns,
      fileName: `jobs`,
      processCellCallback: (params) => {
        const { colId } = params.column;

        if (colId === "unix_target_date") {
          if (!params.value) return "";

          const f_date = generateTime(params.value * 1000, false, true, "-");

          return f_date;
        } else if (colId === "percent_complete") {
          if (!params.value) return;
          return params.value.toFixed(1); // @ToDo: We are already formatting? Is this a duplicate?
        } else if (typeof params.value === "number" && colId !== "id") {
          if (!params.value) return;
          return params.value.toFixed(2);
        } else return params.value;
      },
    };
  }, [permissionLockedColumns]);

  const handleExcelExport = () => {
    if (gridOptionsApi) gridOptionsApi.exportDataAsExcel(exportParams);
  };
  const handleCSVExport = () => {
    if (gridOptionsApi) gridOptionsApi.exportDataAsCsv(exportParams);
  };

  /**
   *  Call the export API to trigger a download
   *  Have to use axios directly because useApiCall() doesn't support any of this
   */
  const handleExportTable = async (outputFormat = "xlsx") => {
    const apiBaseURL = process.env.REACT_APP_API;
    const job_ids = displayedJobsRef.current.map((job) => job.id);
    const method = "POST";
    const query = new URLSearchParams({ outputFormat }).toString();
    const apiPath = `jobs/export?${query}`;

    const config = {
      method,
      url: apiPath,
      baseURL: apiBaseURL,
      headers: { Authorization: `Bearer ${token}` },
      responseType: "arraybuffer", // Ensure binary data is handled properly
      data: { job_ids },
    };

    try {
      const response = await axios(config);
      // trigger the browser download
      const mimeType =
        outputFormat === "csv"
          ? "text/csv"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      const blob = new Blob([response.data], { type: mimeType });
      const href = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `jobs-export.${outputFormat}`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
    } catch (error) {
      console.error("Export failed:", error);
      alert("Export failed. Please try again.");
    }
  };

  const previousSearchInput = usePrevious(searchInput);
  useEffect(() => {
    if (!gridOptionsApi || !columnApi || previousSearchInput === searchInput)
      return;

    const isInfiniteGrid = hasCustomColumnFeature;

    const visibleColumns = columnApi
      .getAllDisplayedColumns()
      ?.filter(
        (col) =>
          col.colDef.colId &&
          !columnsToExcludeFromGlobalSearch.includes(col.colDef.colId)
      );

    if (!searchInput) {
      // if we have selected jobs reset table to that state, otherwise reset using all jobs
      if (selectedJobsRef?.current?.length) {
        displayedJobsRef.current = selectedJobsRef.current;
      } else displayedJobsRef.current = jobs;

      isInfiniteGrid
        ? gridOptionsApi.purgeInfiniteCache()
        : gridOptionsApi.setRowData(displayedJobsRef.current);
    } else {
      const itemsAfterSearch = searchTable(
        escapeRegExp(searchInput),
        selectedJobs?.length ? selectedJobs : jobs,
        visibleColumns
      );

      displayedJobsRef.current = itemsAfterSearch;

      isInfiniteGrid
        ? gridOptionsApi.purgeInfiniteCache()
        : gridOptionsApi.setRowData(itemsAfterSearch);
    }
  }, [searchInput, gridOptionsApi, columnApi, selectedJobs, jobs]);

  return (
    <>
      <TableContainer
        tabs={generateTabs("EXISTING_JOBS", [], showAllWork)}
        handleToggle={() => toggleShowAllWork(!showAllWork)}
        handleExcel={
          hasCustomColumnFeature
            ? () => handleExportTable("xlsx")
            : handleExcelExport
        }
        handleCSV={
          hasCustomColumnFeature
            ? () => handleExportTable("csv")
            : handleCSVExport
        }
        searchInput={searchInput}
        setSearchInput={setSearchInput}
        currentTable={"JOBS"}
        selectedRows={selectedRows}
        totalRows={totalRowsCount}
        isInfiniteGrid={hasCustomColumnFeature}
      >
        {permissionLockedColumns &&
          (hasCustomColumnFeature ? (
            <InfiniteScrollTable
              totalNumberOfRows={displayedJobsRef.current?.length || 0}
              gridOptions={gridOptionsInfinite}
              overrideCheckboxSelectColumn={true}
              setSelectedRows={setSelectedRows}
              selectedRowIds={selectedRows.map((item) => item.id)}
              onSelectAll={(isSelected) => {
                if (isSelected)
                  setSelectedRows(() => [...displayedJobsRef.current]);
                else setSelectedRows(() => []);
              }}
            />
          ) : (
            <AgTable gridOptions={gridOptions} />
          ))}
      </TableContainer>
    </>
  );
};

export default JobsTable;
