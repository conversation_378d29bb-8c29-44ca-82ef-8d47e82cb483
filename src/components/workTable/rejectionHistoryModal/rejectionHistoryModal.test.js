import { rejectionColumnDefs } from "./rejectionHistoryModalConstants";

describe("Item Rejections", () => {
  describe("Column Defs", () => {
    const defaultHeaders = [
      "Category",
      "Type",
      "Sent Back To",
      "Description",
      "Time Rejected",
      "Rejected By",
      "Resolved",
      "Time Resolved",
      "Resolved By",
    ];

    let populatedColumns;

    beforeEach(() => {
      populatedColumns = rejectionColumnDefs;
    });

    it("Headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);
      expect(columnHeaders).toEqual(defaultHeaders);
    });

    describe("TIME REJECTED", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Time Rejected");
      });

      const params = {
        data: {
          time_rejected: 1616188850,
        },
        value: 1616188850,
      };

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(1616188850);
      });
      it("valueFormatter", () => {
        expect(column.valueFormatter(params)).toEqual("03-19-21 04:20 pm");
      });
    });

    describe("RESOLVED", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Resolved");
      });

      const params = {
        data: {
          resolved: 1,
        },
        value: 1,
      };

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(1);
      });
      it("valueFormatter", () => {
        expect(column.valueFormatter(params)).toEqual("Yes");
      });
    });

    describe("TIME RESOLVED", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Time Resolved");
      });

      const params = {
        data: {
          time_resolved: 1616188850,
        },
        value: 1616188850,
      };

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual(1616188850);
      });
      it("valueFormatter", () => {
        expect(column.valueFormatter(params)).toEqual("03-19-21 04:20 pm");
      });
    });
  });
});
