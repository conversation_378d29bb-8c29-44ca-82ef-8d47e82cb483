// NPM PACKAGE IMPORTS
import React, { useCallback, useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";

// REDUX IMPORTS
import {
  handleFetchRejectionHistory,
  handleResolveRejection,
} from "../../items/itemsActions";

// CONSTANTS IMPORTS
import { rejectionColumnDefs } from "./rejectionHistoryModalConstants";

// COMPONENTS IMPORT
import AgTable from "../../reusable/agTable/AgTable";

// STYLE IMPORTS
import "./stylesRejectionHistoryModal.scss";

const RejectionHistoryModal = ({
  open,
  handleClose,
  rowInfo,
  translate,
  updateMainTable,
}) => {
  const [gridOptionsApi, setGridOptionsApi] = useState(null);

  const dispatch = useDispatch();
  const { rejectionHistory } = useSelector((state) => state.itemsData);

  useEffect(() => {
    if (rowInfo && rowInfo.id)
      dispatch(handleFetchRejectionHistory(rowInfo.id));
  }, [rowInfo]);

  useEffect(() => {
    if (gridOptionsApi) gridOptionsApi.setRowData(rejectionHistory);
  }, [rejectionHistory]);

  const resolve = useCallback(() => {
    dispatch(handleResolveRejection(rowInfo.id)).then((res) => {
      if (!res.error) {
        dispatch(handleFetchRejectionHistory(rowInfo.id));
        updateMainTable({ ...rowInfo, rejected: 0 });
      }
    });
  }, [rowInfo]);

  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };
  const gridOptions = {
    rowData: rejectionHistory,
    defaultColDef: {
      wrapText: true,
      cellClass: "custom-wrap",
    },
    columnDefs: rejectionColumnDefs,
    rowClassRules,
    pagination: true,
    paginationPageSize: 100,
    getRowNodeId: (data) => data.id,
    onGridReady: (params) => {
      setGridOptionsApi(params.api);
    },
  };

  return (
    <Modal open={open} handleClose={handleClose}>
      <div className="rejection-history-container">
        <h2 className="title">
          <span>{translate("Rejection History")}</span>
          <Button onClick={resolve}>{translate("Resolve")}</Button>
        </h2>
        {rejectionHistory ? <AgTable gridOptions={gridOptions} /> : <></>}
      </div>
    </Modal>
  );
};

export default RejectionHistoryModal;
