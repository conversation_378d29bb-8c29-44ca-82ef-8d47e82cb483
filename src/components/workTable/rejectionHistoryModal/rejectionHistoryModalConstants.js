import { generateTime, naturalSort } from "../../../_utils";

export const rejectionColumnDefs = [
  {
    headerName: "Category",
    field: "rejection_category_name",
    filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["reset"],
    },
    menuTabs: ["filterMenuTab"],
  },
  {
    headerName: "Type",
    field: "type",
    filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["reset"],
    },
    menuTabs: ["filterMenuTab"],
  },
  {
    headerName: "Sent Back To",
    field: "sent_back_to_stage_name",
    filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["reset"],
    },
    menuTabs: ["filterMenuTab"],
  },
  {
    headerName: "Description",
    field: "description",
    filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["reset"],
    },
    menuTabs: ["filterMenuTab"],
    autoHeight: true,
    comparator: (valueA, valueB) =>
      valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
  },
  {
    headerName: "Time Rejected",
    valueGetter: (params) => params.data.time_rejected,
    valueFormatter: (params) =>
      generateTime(params.value * 1000, false, false, null, null, true).format(
        "MM-DD-YY hh:mm a"
      ),
    filter: "agDateColumnFilter",
    filterParams: {
      buttons: ["reset"],
      comparator: (filterLocalDateAtMidnight, cellValue) => {
        const cellDate = cellValue
          ? typeof cellValue === "number"
            ? new Date(
                generateTime(
                  cellValue * 1000,
                  false,
                  true,
                  "-",
                  null,
                  true
                ).format("MM-DD-YY hh:mm a")
              )
            : new Date(cellValue)
          : "-";

        return cellDate < filterLocalDateAtMidnight
          ? -1
          : cellDate > filterLocalDateAtMidnight
          ? 1
          : 0;
      },
    },
    menuTabs: ["filterMenuTab"],
  },
  {
    headerName: "Rejected By",
    field: "rejected_by",
    filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["reset"],
    },
    menuTabs: ["filterMenuTab"],
    comparator: (valueA, valueB) =>
      valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
  },
  {
    headerName: "Resolved",
    valueGetter: (params) => params.data.resolved,
    valueFormatter: (params) => (params.value === 1 ? "Yes" : "No"),
    filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["reset"],
    },
    menuTabs: ["filterMenuTab"],
  },
  {
    headerName: "Time Resolved",
    valueGetter: (params) => params.data.time_resolved,
    valueFormatter: (params) =>
      params.value
        ? generateTime(
            params.value * 1000,
            false,
            false,
            null,
            null,
            true
          ).format("MM-DD-YY hh:mm a")
        : "",
    filter: "agDateColumnFilter",
    filterParams: {
      buttons: ["reset"],
      comparator: (filterLocalDateAtMidnight, cellValue) => {
        const cellDate = cellValue
          ? typeof cellValue === "number"
            ? new Date(
                generateTime(
                  cellValue * 1000,
                  false,
                  true,
                  "-",
                  null,
                  true
                ).format("MM-DD-YY hh:mm a")
              )
            : new Date(cellValue)
          : "-";

        return cellDate < filterLocalDateAtMidnight
          ? -1
          : cellDate > filterLocalDateAtMidnight
          ? 1
          : 0;
      },
    },
    menuTabs: ["filterMenuTab"],
  },
  {
    headerName: "Resolved By",
    field: "resolved_by",
    filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["reset"],
    },
    menuTabs: ["filterMenuTab"],
    comparator: (valueA, valueB) =>
      valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
  },
];
