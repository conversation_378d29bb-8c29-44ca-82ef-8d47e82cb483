@import "../../styles/colors.scss";
@import "../../styles/sizes.scss";

div.rejection-history-container {
  width: 800px;
  border: 1px solid #333;

  & > h2.title {
    line-height: 30px;
    font-size: 1rem;
    background-color: $fabProBlue;
    color: #fff;
    padding: 0 10px 0;
    margin: 0;

    display: flex;
    justify-content: space-between;
    align-items: center;

    & > button {
      padding: 0 5px;
      height: 25px;
      border: 1px solid #333;
      background-color: $lightGreen;
      font-size: 1rem;
      color: #fff;
    }
  }

  & > .custom-ag-styles.ag-theme-balham-dark {
    width: 100%;
    // 30 modal title, 10 for modal outside, 28 bottom button
    height: calc(100vh - #{$headerFooter} - 30px - 10px - 28px);
    @supports (-webkit-touch-callout: none) {
      height: calc(
        100vh - #{$headerFooter} - #{$iosAddressBar} - 30px - 10px - 28px
      );
    }

    .ag-cell {
      min-height: 60px;
      white-space: normal;
      line-height: unset;
    }
    & .ag-side-buttons {
      display: none;
    }
    & .ag-tool-panel-wrapper {
      background-color: transparent;
      width: 0;
    }
  }
}
