/**
 *  Hook to handle the new drawing API.  Ultimately we will have a hook called useWorkTableData()
 *  or something like that which uses this hook to handle the drawing API.
 *  All the data from jobs/drawing/etc will end up in there in a nice hash table as it is here.
 */
import { useEffect, useState } from "react";
import useMsuiteAPI from "../../../hooks/useMsuiteAPI";

const useDrawingsAPI = (selectedDrawingIds) => {
  const [loading, setLoading] = useState(false);
  const [drawings, setDrawings] = useState({});
  const [detailedDrawings, setDetailedDrawings] = useState({});

  const [allDrawingsQuery, executeFetchAll] = useMsuiteAPI({
    url: "v2/drawings",
    method: "GET",
  });

  const [fetchByIdDrawingsQuery, executeFetchById] = useMsuiteAPI({
    url: `v2/drawings/:ids`,
    method: "GET",
  });

  useEffect(() => {
    setDrawings({});
    setDetailedDrawings({});
  }, [selectedDrawingIds]);

  const fetchAllDrawings = async () => {
    setLoading(true);
    try {
      const response = await executeFetchAll();
      const drawingsById = response.data.reduce((acc, drawing) => {
        acc[drawing.id] = drawing;
        return acc;
      }, {});
      setDrawings(drawingsById);
    } catch (error) {
      console.error("Error fetching all drawings:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchDrawingById = async (id) => {
    try {
      const response = await executeFetchById({ url: `v2/drawings/${id}` });
      if (!response.data || !Array.isArray(response.data)) {
        throw `${id} isn't a valid drawing from API`;
      }

      // Hashmaps by ID !
      setDrawings((prev) => {
        const newDrawings = { ...prev };
        newDrawings[id] = {
          ...newDrawings[id],
          ...response.data[0],
        };
        return newDrawings;
      });

      setDetailedDrawings((prev) => ({
        ...prev,
        [id]: response.data[0],
      }));

      return response.data;
    } catch (error) {
      console.error("Error fetching drawing by ID:", error);
    }
  };

  const fetchDrawingsByIds = async (ids) => {
    let result = [];
    for (const id of ids) {
      const data = await fetchDrawingById(id);
      result.push(data[0]);
    }
    return result;
  };

  const getDrawings = () => {
    return Object.values(drawings);
  };

  const getDetailedDrawings = () => {
    return Object.values(detailedDrawings);
  };

  return {
    loading,
    drawings, // Object with all drawings, keyed by ID
    detailedDrawings, // Object with detailed drawings, keyed by ID
    fetchAllDrawings,
    fetchDrawingById, // Function to fetch a single drawing by ID
    fetchDrawingsByIds, // Function to fetch multiple drawings by IDs
    getDrawings,
    getDetailedDrawings,
  };
};

export default useDrawingsAPI;
