import React, { createContext, useState } from "react";

// Create Context Object
export const DrawingsTableV2Context = createContext();

// Create a provider for components to consume and subscribe to changes
export const DrawingsTableV2ContextProvider = (props) => {
  const [state, setState] = useState("testing context");

  const contextData = {
    state,
    setState,
  };

  return (
    <DrawingsTableV2Context.Provider value={contextData}>
      {props.children}
    </DrawingsTableV2Context.Provider>
  );
};
