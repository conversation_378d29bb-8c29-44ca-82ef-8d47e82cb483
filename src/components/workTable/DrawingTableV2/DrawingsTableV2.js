import React, { useContext, useEffect, useMemo, useRef, useState } from "react";
import { DrawingsTableV2Context } from "./DrawingsTableV2Context";
import useMount from "../../../hooks/useMount";
import { frameworkComponents, generateTabs } from "../../jobs/jobsConstants";
import { onDisplayedColumnsChanged, permissionLock } from "../../../_utils";
import { drawingsColumnDefs } from "../../drawings/drawingsConstants";
import { useDispatch, useSelector } from "react-redux";
import InfiniteScrolTable from "../../reusable/infiniteScrollTable/InfiniteScrollTable";
import useDrawingsAPI from "./useDrawingsAPI";
import { handleFetchContainers } from "../../drawings/drawingsActions";
import TableContainer from "../../reusable/tableContainer/TableContainer";

const CUSTOM_COLUMNS_FEATURE_ID = 53;

const DrawingsTableV2 = (props) => {
  const {
    setStatusTrackerPopup,
    toggleMoreInfo,
    moreInfoClick,
    setSelectedRows,
    showScroll,
    togglePDFViewer,
    sortState,
    storeDrawingsGridOptionsApi,
    onDrawingsCellValueChanged,
    onCellEditingStarted,
    drawingCustomColumns,
    setDisplayedPdf,
  } = props;

  const context = useContext(DrawingsTableV2Context);

  const { savedColumnState, containers } = useSelector(
    (state) => state.drawingsData
  );

  const { features } = useSelector((state) => state.profileData);

  const [gridOptionsApi, setGridOptionsApi] = useState(null);

  const dispatch = useDispatch();

  const drawingsAPI = useDrawingsAPI(props.selectedDrawings);

  useMount(() => {
    dispatch(handleFetchContainers());
  });

  const selectedDrawingIds = props.selectedDrawings.map((o) => o.id);

  const hasCustomColumnFeature = features?.includes(CUSTOM_COLUMNS_FEATURE_ID);

  const permissionLockedColumns = useMemo(() => {
    if (savedColumnState)
      return permissionLock(
        drawingsColumnDefs(
          savedColumnState,
          containers,
          moreInfoClick,
          toggleMoreInfo,
          togglePDFViewer,
          sortState,
          drawingCustomColumns,
          null,
          setDisplayedPdf
        )
      );
    else return null;
  }, [savedColumnState, containers]);

  const onSortChanged = (params) => {};

  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };

  const onSelectionChanged = (params) => {
    let rows = params.api.getSelectedRows();
    return setSelectedRows(rows);
  };

  const containersRef = useRef(null);

  useEffect(() => {
    if (!containers) containersRef.current = null;
    containersRef.current = containers;
  }, [containers]);

  const onInfiniteGridReady = (params) => {
    storeDrawingsGridOptionsApi(params.api);
    setGridOptionsApi(params.api);

    const dataSource = {
      rowCount: props.selectedRows.length,
      getRows: async (params) => {
        let lastRow = -1;
        const drawingIdsThisPage = selectedDrawingIds.slice(
          params.startRow,
          params.endRow
        );
        const blockSize = params.endRow - params.startRow;
        const updatedRows = await drawingsAPI.fetchDrawingsByIds(
          drawingIdsThisPage
        );
        if (updatedRows.length < blockSize)
          // then this is the end of the scroll
          lastRow = params.startRow + updatedRows.length;
        params.successCallback(updatedRows, lastRow);
      },
    };

    params.api.setDatasource(dataSource);
  };

  const infiniteGridOptions = {
    rowModelType: "infinite",
    onGridReady: onInfiniteGridReady,
    //display lines per page
    cacheBlockSize: 10,
    //how many rows to seek ahead when unknown data size
    cacheOverflowSize: 2,
    //how many concurrent data requests are allowed
    maxConcurrentDatasourceRequests: 1,
    // how many rows to initially allow scrolling to in the grid.
    infiniteInitialRowCount: 1000,
    // how many pages to hold in the cache.
    maxBlocksInCache: 10,
    blockLoadDebounceMillis: 200,
    columnDefs: permissionLockedColumns,
    frameworkComponents: frameworkComponents(setStatusTrackerPopup),
    defaultColDef: {
      cellClass: ["no-border", "custom-wrap"],
      wrapText: true,
      suppressSizeToFit: showScroll ? true : false,
    },
    suppressRowClickSelection: true,
    onDisplayedColumnsChanged: (params) =>
      onDisplayedColumnsChanged("DRAWINGS", params),
    rowData: selectedDrawingIds.map((o) => {
      return { id: +o };
    }),
    reactNext: true,
    rowClassRules,
    onSelectionChanged,
    onSortChanged,
    rowSelection: "multiple",
    tabToNextCell: () => null,
    pagination: false,
    editType: "fullRow",
    getRowNodeId: (data) => data.id,
    onCellValueChanged: (params) =>
      onDrawingsCellValueChanged(params, containersRef),
    onCellEditingStarted,
    stopEditingWhenGridLosesFocus: true,
  };

  return (
    <div style={{ border: "1px dashed lightgreen" }}>
      <TableContainer
        tabs={generateTabs("EXISTING_JOBS", [], false)}
        handleToggle={() => null}
        handleExcel={() => null}
        handleCSV={() => null}
        searchInput={""}
        setSearchInput={() => null}
        currentTable={"DRAWINGS"}
        selectedRows={[]}
        totalRows={props.selectedDrawings.length}
        isInfiniteGrid={true}
      >
        {permissionLockedColumns && (
          <InfiniteScrolTable gridOptions={infiniteGridOptions} />
        )}
      </TableContainer>
    </div>
  );
};

export default DrawingsTableV2;
