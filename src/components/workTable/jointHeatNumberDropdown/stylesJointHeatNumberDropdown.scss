@import "../../styles/colors.scss";

div.display-block {
  display: block !important;
  position: static;
}
div.joint-heat-number-dropdown-wrapper {
  position: absolute;
  background-color: white;
  width: 150px;
  border-radius: 3px;
  min-height: 50px;
  max-height: 200px;

  & span.search-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 32px;
    width: 150px;
    box-sizing: border-box;
    padding: 0 3px;

    & input {
      width: 100%;
      outline: none;
      border: none;
      margin-left: 3px;
    }

    & svg {
      color: $blue;
      stroke-width: 2px;
      font-size: 1.2rem;
    }
  }

  & input.heat-number-input {
    width: 100%;
    height: 32px;
    box-sizing: border-box;
    font-size: 0.8rem;
    border: none;
    border-top: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    padding-left: 5px;

    &:focus {
      border: 1px solid $blue;
      outline: none;
    }
  }

  & ul {
    margin: 0;
    padding: 0 0 0 0;
    max-height: 100px;
    overflow-y: auto;
    & li {
      font-size: 0.8rem;
      padding: 8px 5px;
      list-style-type: none;
      cursor: pointer;

      &:hover {
        background-color: darken(white, 10%);
      }
    }

    & .selected {
      background-color: $blue;
      color: white;
    }
  }

  & p {
    color: black;
    text-align: center;
    font-size: 0.8rem;
  }
}
