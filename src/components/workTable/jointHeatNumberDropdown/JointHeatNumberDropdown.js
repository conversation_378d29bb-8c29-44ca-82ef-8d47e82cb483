// NPM PACKAGE IMPORTS
import React, { useRef, useEffect, useState, useMemo } from "react";
import { useSelector } from "react-redux";
import { FiSearch } from "react-icons/fi";

// REDUX IMPORTS

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../hooks/useOutsideClick";

// STYLES IMPORTS
import "./stylesJointHeatNumberDropdown.scss";

const JointHeatNumberDropdown = ({
  toggleCellDropdown,
  cellDropdownLocation,
  params,
  handleCellEdit,
}) => {
  const [locationData, setLocationData] = useState(null);
  const [searchInput, setSearchInput] = useState("");
  const [createInput, setCreateInput] = useState("");
  const [displayedOptions, setDisplayedOptions] = useState([]);
  const [selectedHeatNumber, setSelectedHeatNumber] = useState(null);

  const { navExpanded } = useSelector((state) => state.generalData);
  const { heatNumbers } = useSelector((state) => state.drawingsData);

  const dropdownRef = useRef(null);
  useOutsideClick(dropdownRef, () => {
    toggleCellDropdown && toggleCellDropdown(false);
  });

  useEffect(() => {
    if (
      !cellDropdownLocation ||
      !cellDropdownLocation.x ||
      !cellDropdownLocation.y
    )
      return;

    if (cellDropdownLocation.y > 550) {
      navExpanded
        ? setLocationData({
            top: `${cellDropdownLocation.y - 145}px`,
            left: `${cellDropdownLocation.x - 305}px`,
          })
        : setLocationData({
            top: `${cellDropdownLocation.y - 145}px`,
            left: `${cellDropdownLocation.x - 110}px`,
          });
    } else {
      navExpanded
        ? setLocationData({
            top: `${cellDropdownLocation.y - 20}px`,
            left: `${cellDropdownLocation.x - 305}px`,
          })
        : setLocationData({
            top: `${cellDropdownLocation.y - 20}px`,
            left: `${cellDropdownLocation.x - 110}px`,
          });
    }
  }, [cellDropdownLocation, navExpanded]);

  const handleSelection = (heatNumber) => {
    if (selectedHeatNumber === heatNumber) setSelectedHeatNumber(null);
    setSelectedHeatNumber(heatNumber);
    handleCellEdit(params, "joint_heat_numbers", heatNumber);
    toggleCellDropdown(false);
  };

  const handleInputCreation = (event) => {
    if (event.key !== "Enter") return;

    handleCellEdit(params, "joint_heat_numbers", createInput);
    toggleCellDropdown(false);
  };

  const f_heatNumbers = useMemo(() => {
    if (!heatNumbers || !heatNumbers.length) return;
    if (!cellDropdownLocation) return heatNumbers;
    if (params) {
      return heatNumbers.filter((heatNumber) => {
        return params.valueFormatted.value !== heatNumber.heat_number;
      });
    }
  }, [heatNumbers, cellDropdownLocation]);

  useEffect(() => {
    if (!f_heatNumbers) return;
    if (!searchInput) return setDisplayedOptions(f_heatNumbers);

    const f_options = f_heatNumbers.filter((hn) =>
      hn.heat_number.toLowerCase().includes(searchInput.toLowerCase())
    );
    setDisplayedOptions(f_options);
  }, [searchInput, f_heatNumbers]);

  return (
    <div
      style={locationData ? locationData : {}}
      className={
        locationData
          ? "joint-heat-number-dropdown-wrapper"
          : "joint-heat-number-dropdown-wrapper block-display"
      }
      // className="joint-heat-number-dropdown-wrapper"
      ref={dropdownRef}
    >
      <span className="search-wrapper">
        <FiSearch />
        <input onChange={(e) => setSearchInput(e.target.value)} type="text" />
      </span>
      <input
        placeholder="New heat number"
        className="heat-number-input"
        type="text"
        onChange={(e) => setCreateInput(e.target.value)}
        onKeyDown={(e) => handleInputCreation(e)}
      />
      {displayedOptions && displayedOptions.length ? (
        <ul>
          {displayedOptions.map((hn) => (
            <li
              key={hn.heat_number}
              className={
                selectedHeatNumber === hn.heat_number ? "selected" : ""
              }
              onClick={() => handleSelection(hn.heat_number)}
            >
              {hn.heat_number}
            </li>
          ))}
        </ul>
      ) : (
        <p>No heat numbers</p>
      )}
    </div>
  );
};

export default JointHeatNumberDropdown;
