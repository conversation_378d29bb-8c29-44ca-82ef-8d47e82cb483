// NPM PACKAGE IMPORTS
import React, { useState, useEffect, useMemo, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { v4 as uuid } from "uuid";

// REDUX IMPORTS
import {
  handleFetchWorkStageColumns,
  handleFetchWorkItemColumns,
} from "../flows/flowsActions";
import {
  handleUpdateItems,
  handleFetchJoiningProcedures,
  handleFetchMaterialTypes,
  handleFetchVisibleColumns,
  handleFetchWorkTableItems,
  handleUpdateItemNoRefresh,
} from "../items/itemsActions";
import {
  handleFetchContainers,
  handleUpdateDrawings,
} from "../drawings/drawingsActions";
import { handleUpdatePackages } from "../packages/packagesActions";
import { handleUpdateJobs } from "../jobs/jobsActions";
import { handleFetchLaydownLocations } from "../shipping/shippingActions";
import { handleUpdateCustomColumnData } from "../customColumns/customColumnsActions";
import { notify } from "../reusable/alertPopup/alertPopupActions";

// COMPONENT IMPORTS
import AgTable from "../reusable/agTable/AgTable";
import TableContainer from "../reusable/tableContainer/TableContainer";
import InfiniteScrollTable from "../reusable/infiniteScrollTable/InfiniteScrollTable";

// CONSTANTS IMPORTS
import { frameworkComponents, generateTabs } from "../jobs/jobsConstants";

import { itemsTableColumnDefs } from "./itemsTableColumnDefs";
import { objectColumns, escapeRegExp } from "../../_utils";
import { associateCustomColumnData, sortAndFilter } from "../../utils/agTable";

// HELPER FUNCTION IMPORTS
import {
  permissionLock,
  onDisplayedColumnsChanged,
  generateTime,
  dateStringToUnix,
} from "../../_utils";
import {
  searchTable,
  columnsToExcludeFromGlobalSearch,
} from "../../utils/agTable";
import {
  fetchCustomColumnDataMultiItem,
  fetchCustomColumnDataPerItem,
} from "../../_services";
import usePrevious from "../../hooks/usePrevious";
import {
  handleEnqueueSpinner,
  handleDequeueSpinner,
} from "../reusable/generalSpinner/generalSpinnerActions";
import axios from "axios";

const CUSTOM_COLUMNS_FEATURE_ID = 53;

// EXPORTS
const ItemsTable = ({
  selectedRows,
  showAllWork,
  toggleShowAllWork,
  setStatusTrackerPopup,
  selectedStages,
  setSelectedRows,
  toggleMoreInfo,
  moreInfoClick,
  storeItemsGridOptionsApi,
  groupingColumns,
  selectedGrouping,
  toggleHeatNumbersModal,
  setRowInfo,
  selectedJobs,
  selectedPackages,
  selectedDrawings,
  togglePDFViewer,
  setCurrentRowOrder,
  handleSaveSortState,
  sortState,
  displayedColumns,
  toggleMaterialTypesModal,
  toggleJoiningProceduresModal,
  toggleLaydownLocationsModal,
  setDisplayedPdf,
}) => {
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [searchInput, setSearchInput] = useState("");
  const [totalRowsCount, setTotalRowsCount] = useState("");
  const [columnApi, setColumnApi] = useState(null);

  const dispatch = useDispatch();

  const {
    filteredItems,
    savedColumnState,
    joiningProcedures,
    materialTypes,
    visibleColumns,
  } = useSelector((state) => state.itemsData);
  const { workStageColumns, workItemColumns } = useSelector(
    (state) => state.flowsData
  );
  const { containers } = useSelector((state) => state.drawingsData);
  const { laydown_locations } = useSelector((state) => state.shippingData);
  const { features, token } = useSelector((state) => state.profileData);
  const { drawingFiles } = useSelector((state) => state.filesData);

  const workStageColumnsRef = useRef(null);

  const displayedJoiningProcedures = useMemo(() => {
    if (joiningProcedures) return joiningProcedures.map((jp) => jp.name);
    else return null;
  }, [joiningProcedures]);

  const displayedMaterialTypes = useMemo(() => {
    if (materialTypes) return materialTypes.map((mt) => mt.name);
    else return null;
  }, [materialTypes]);

  useEffect(() => {
    dispatch(handleFetchContainers());
    dispatch(handleFetchJoiningProcedures);
    dispatch(handleFetchMaterialTypes());
    dispatch(handleFetchVisibleColumns);
    dispatch(handleFetchLaydownLocations("materials"));
  }, []);

  const hasCustomColumnFeature = features?.includes(CUSTOM_COLUMNS_FEATURE_ID);
  const displayedItemsRef = useRef(filteredItems);
  // We want to use old table and export when in "grouped mode" always for now
  const isUsingInfinateTable = hasCustomColumnFeature && selectedGrouping !== 1;

  useEffect(() => {
    if (!gridOptionsApi) return;

    displayedItemsRef.current = filteredItems;
    gridOptionsApi.stopEditing();
    refreshTable();
  }, [
    gridOptionsApi,
    selectedStages,
    displayedColumns,
    filteredItems,
    selectedGrouping,
  ]);

  useEffect(() => {
    setSelectedRows([]);
  }, [selectedGrouping]);

  useEffect(() => {
    workStageColumnsRef.current = workStageColumns || [];
  }, [workStageColumns]);

  const displayNotification = (message) => {
    dispatch(
      notify({
        id: Date.now(),
        type: "ERROR",
        message,
      })
    );
  };

  const refreshTable = () => {
    // update columns that are displayed to handle grouped column selection change
    gridOptionsApi.setColumnDefs(permissionLockedColumns);

    if (selectedGrouping === 0 && hasCustomColumnFeature) {
      // The item level is more complex.  If they change filters lets just reset the filter for now to prevent a mistake.
      setSelectedRows([]);
      gridOptionsApi.purgeInfiniteCache();
    }

    if (filteredItems) {
      gridOptionsApi.setRowData(filteredItems);
    } else gridOptionsApi.setRowData([]);

    gridOptionsApi.forEachNode((n) => {
      if (selectedRows.find((r) => r.id === n.id)) n.setSelected(true);
    });
  };

  useEffect(() => {
    if (!selectedStages?.length) return;

    if (selectedStages?.map((s) => s.id).includes(0)) {
      dispatch(handleFetchWorkItemColumns());
    } else {
      dispatch(
        handleFetchWorkStageColumns(
          selectedStages.map((stage) => stage.id),
          true
        )
      );
    }
  }, [selectedStages, dispatch]);

  const permissionLockedColumns = useMemo(() => {
    if (
      savedColumnState &&
      containers &&
      displayedJoiningProcedures &&
      displayedMaterialTypes &&
      visibleColumns &&
      laydown_locations
    ) {
      const stageIds = selectedStages.map((stage) => stage.id);

      let ungroupedColumnsToDisplay;
      stageIds.includes(0)
        ? (ungroupedColumnsToDisplay = workItemColumns)
        : (ungroupedColumnsToDisplay = workStageColumns);

      let colDefs = itemsTableColumnDefs(
        savedColumnState,
        selectedGrouping === 0 ? ungroupedColumnsToDisplay : groupingColumns,
        selectedGrouping === 0 ? "ungrouped" : "grouped", // selected grouping
        sortState,
        moreInfoClick,
        toggleMoreInfo,
        togglePDFViewer,
        containers,
        (r) => {
          toggleMaterialTypesModal(true);
          setRowInfo(r);
        },
        (r) => {
          toggleLaydownLocationsModal(true);
          setRowInfo(r);
        },
        (r) => {
          toggleJoiningProceduresModal(true);
          setRowInfo(r);
        },
        (r) => {
          toggleHeatNumbersModal(true);
          setRowInfo(r);
        },
        displayNotification,
        null,
        filteredItems,
        hasCustomColumnFeature,
        "JOBS",
        null,
        null,
        null,
        null,
        setDisplayedPdf
      );

      return permissionLock(colDefs);
    } else return null;
  }, [
    savedColumnState,
    containers,
    displayedJoiningProcedures,
    displayedMaterialTypes,
    selectedGrouping,
    groupingColumns,
    visibleColumns,
    laydown_locations,
    togglePDFViewer,
  ]);

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
    setColumnApi(params.columnApi);
    const result = [];
    params.api.forEachNodeAfterFilterAndSort((n) => {
      result.push(n.data);
    });
    setCurrentRowOrder(result);
  };
  const onSortChanged = (params) => {
    const result = [];

    if (selectedGrouping === "1" || !hasCustomColumnFeature) {
      params.api.forEachNodeAfterFilterAndSort((n) => {
        result.push(n.data);
      });
      params.api.redrawRows();
      setCurrentRowOrder(result);
    }

    const sortedColumn = params.columnApi.getAllColumns().find((c) => c.sort);

    dispatch(
      handleSaveSortState(
        sortedColumn ? sortedColumn.colId : null,
        sortedColumn ? sortedColumn.sort : null,
        "ITEMS",
        selectedGrouping ? selectedGrouping + 1 : null,
        selectedStages.map((s) => s.id).join(",")
      )
    );
  };
  const onSelectionChanged = (params) => {
    let rows = params.api.getSelectedRows();
    return setSelectedRows(rows);
  };

  const onFilterChanged = (params) => {
    params.api.redrawRows();
  };

  const customColumnsRef = useRef(null);
  useEffect(() => {
    let customColumns;
    if (workStageColumns.length)
      customColumns = workStageColumns?.filter((col) => col.is_custom);
    else
      customColumns = workItemColumns.filter(
        (col) => col.is_custom && col.table_target === "work_items"
      );
    if (customColumns.length) {
      customColumnsRef.current = customColumns;
    }
  }, [workStageColumns, workItemColumns]);

  const onCellValueChanged = (params) => {
    // will populate if updated column is custom
    const customColumn =
      customColumnsRef?.current?.find(
        (col) => col.name === params.colDef.field
      ) || null;

    if (!params.newValue && !params.oldValue) return;

    if (params.colDef.field === "length") {
      if (params.newValue.display === params.oldValue.display) return;
    }

    if (params.newValue !== params.oldValue) {
      if (/joint_heat_number/.test(params.colDef.field)) {
        return params.node.setData({
          ...params.data,
          [params.colDef.field]: params.oldValue,
        });
      } else if (customColumn) {
        dispatch(
          handleUpdateCustomColumnData(
            "items",
            params.data.id,
            customColumn.id,
            params.newValue
          )
        );
      } else {
        const property = (field) => {
          switch (field) {
            case "container_name":
              return "shipping_container_id";
            case "joining_procedure_name":
              return "joining_procedure_id";
            case "material_name":
              return "material_type_id";
            case "laydown_location_name":
              return "laydown_location_id";
            default:
              return field;
          }
        };

        const value = (field) => {
          if (objectColumns.includes(field)) return params.newValue.decimal;

          switch (field) {
            case "container_name":
              return (
                (
                  containers.find(
                    (c) =>
                      c.name === params.newValue &&
                      c.job_id === params.data.job_id
                  ) || {}
                ).id || null
              );
            case "joining_procedure_name":
              return (
                (
                  joiningProcedures.find((jp) => jp.name === params.newValue) ||
                  {}
                ).id || null
              );
            case "material_name":
              return (
                (materialTypes.find((m) => m.name === params.newValue) || {})
                  .id || null
              );
            case "laydown_location_name":
              return (
                (
                  laydown_locations.find((ll) => ll.name === params.newValue) ||
                  {}
                ).id || null
              );
            default:
              return params.newValue || null;
          }
        };

        if (
          [
            "joining_procedure_id",
            "insulation_specification",
            "material_type_id",
            "material_name",
            "joining_procedure_name",
          ].includes(property(params.colDef.field)) &&
          !value(params.colDef.field)
        )
          return;

        if (objectColumns.includes(params.colDef.field)) {
          const newValue = params.newValue,
            oldValue = params.oldValue;

          if (
            newValue.decimal === oldValue.decimal ||
            newValue.display === oldValue.display
          )
            return;
        }

        if (
          params.colDef.field === "drawing_name" ||
          params.colDef.field === "drawing_due_date"
        ) {
          const updatedData =
            params.colDef.field === "drawing_name"
              ? { original_name: params.newValue }
              : {
                  [params.colDef.field.slice(8)]:
                    new Date(params.newValue * 1000).getTime() / 1000,
                };
          dispatch(
            handleUpdateDrawings(
              params.data.drawing_id,
              updatedData,
              "update",
              []
            )
          ).then((res) => {
            if (!res.error) {
              dispatch(
                handleFetchWorkTableItems(
                  selectedJobs.map((j) => j.id),
                  selectedPackages.map((p) => p.id),
                  selectedDrawings.map((d) => d.id),
                  selectedStages.map((s) => s.id),
                  showAllWork,
                  selectedGrouping !== 0 && selectedStages.length === 1
                    ? selectedStages[0].groupable
                    : 0,
                  groupingColumns
                )
              );
            }
          });
        } else if (
          [
            "package_name",
            "package_number",
            "package_due_date",
            "package_area",
          ].includes(params.colDef.field)
        ) {
          const updatedData =
            params.colDef.field === "package_name"
              ? { package_name: params.newValue }
              : {
                  [params.colDef.field.slice(8)]:
                    params.colDef.field === "package_due_date"
                      ? new Date(params.newValue * 1000).getTime() / 1000
                      : params.newValue,
                };

          dispatch(
            handleUpdatePackages(params.data.package_id, updatedData, [])
          ).then((res) => {
            if (!res.error) {
              dispatch(
                handleFetchWorkTableItems(
                  selectedJobs.map((j) => j.id),
                  selectedPackages.map((p) => p.id),
                  selectedDrawings.map((d) => d.id),
                  selectedStages.map((s) => s.id),
                  showAllWork,
                  selectedGrouping !== 0 && selectedStages.length === 1
                    ? selectedStages[0].groupable
                    : 0,
                  groupingColumns
                )
              );
            }
          });
        } else if (["job_name", "job_number"].includes(params.colDef.field)) {
          const updatedData =
            params.colDef.field === "job_name"
              ? { job_name: params.newValue }
              : { job_number: params.newValue };

          dispatch(handleUpdateJobs(params.data.job_id, updatedData, [])).then(
            (res) => {
              dispatch(
                handleFetchWorkTableItems(
                  selectedJobs.map((j) => j.id),
                  selectedPackages.map((p) => p.id),
                  selectedDrawings.map((d) => d.id),
                  selectedStages.map((s) => s.id),
                  showAllWork,
                  selectedGrouping !== 0 && selectedStages.length === 1
                    ? selectedStages[0].groupable
                    : 0,
                  groupingColumns
                )
              );
            }
          );
        } else {
          dispatch(
            handleUpdateItems(
              selectedGrouping > 0
                ? typeof params.data.work_item_ids === "string"
                  ? params.data.work_item_ids.split(",")
                  : [params.data.work_item_ids]
                : params.data.id,
              {
                [property(params.colDef.field)]:
                  value(params.colDef.field) ||
                  (objectColumns.includes(params.colDef.field) ? 0 : null),
              },
              null,
              null,
              params.data.quantity
            )
          ).then(async (res) => {
            const rowNode = params.api.getRowNode(params.data.id);

            if (res.error) {
              return rowNode.setData({
                ...rowNode.data,
                [params.colDef.field]: params.oldValue,
              });
            }

            // update redux store to reflect inline edit
            dispatch(handleUpdateItemNoRefresh(rowNode.data));

            // grouped PUT call doesn't return updated row so need to use previous row data stored in ag-grid to re-populate row data
            if (selectedGrouping > 0 || !hasCustomColumnFeature) {
              return rowNode.setData({
                ...rowNode.data,
                [params.colDef.field]: params.newValue,
              });
            }

            const customColumnData = await fetchCustomColumnDataPerItem(
              "items",
              params.data.id
            );

            const formattedCustomData = associateCustomColumnData(
              customColumnData,
              customColumnsRef.current
            );

            const rowData = { ...formattedCustomData, ...rowNode.data };

            rowNode.setData(rowData);
          });
        }
      }
    }
  };

  const displayedFrameworkComponents = useMemo(() => {
    return frameworkComponents(setStatusTrackerPopup);
  }, [setStatusTrackerPopup]);

  const getRowStyle = (params) => {
    if (!params.data) return;

    if (params.data.completed) {
      return { background: "#9b9ea4 !important" };
    } else if (params.data.rejected === 1) {
      return { background: "#b50751 !important" };
    } else if (
      params.data.identifier === 1 &&
      workStageColumnsRef.current &&
      workStageColumnsRef.current
        .map((c) => c.normal_name)
        .includes("identifier")
    ) {
      const column = workStageColumnsRef.current.find(
        (c) => c.normal_name === "identifier"
      );
      if (column && column.color && JSON.parse(column.color)) {
        let colorParsed = JSON.parse(column.color);
        return {
          background: `rgb(${colorParsed.R}, ${colorParsed.G}, ${colorParsed.B}) !important`,
        };
      }
    } else if (params.node.childIndex % 2 === 1) {
      return { background: "#363d49 !important" };
    } else if (params.node.childIndex % 2 === 0) {
      return { background: "#48525f !important" };
    }
  };

  const gridOptionsClient = {
    rowData: filteredItems,
    columnDefs: permissionLockedColumns,
    frameworkComponents: displayedFrameworkComponents,
    reactNext: true,
    getRowStyle,
    onSelectionChanged,
    onFilterChanged,
    onSortChanged,
    onGridReady: (params) => {
      storeItemsGridOptionsApi(params.api);
      onGridReady(params);
    },
    onRowDataChanged: (params) => params.api.redrawRows(),
    rowSelection: "multiple",
    suppressRowClickSelection: true,
    defaultColDef: {
      cellClass: ["no-border", "custom-wrap"],
      wrapText: true,
      autoHeight: true,
      flex: 1,
    },
    tabToNextCell: () => null,
    pagination: true,
    paginationPageSize: 100,
    editType: "fullRow",
    getRowNodeId: (data) => data.id,
    onDisplayedColumnsChanged: (params) =>
      onDisplayedColumnsChanged(
        "ITEMS",
        params,
        selectedStages.map((s) => s.id)
      ),
    onCellValueChanged,
    stopEditingWhenGridLosesFocus: true,
    getContextMenuItems: (params) => {
      const defaultItems = params.defaultItems;
      const customItems = defaultItems.filter((item) => {
        return (
          item !== "export" &&
          item !== "exportCsv" &&
          item !== "exportExcel" &&
          item !== "separator"
        );
      });
      return customItems;
    },
  };

  // takes in response from fetchCustomColumnDataPerItem as data and array of custom columns associated with current stage(s)
  const associateCustomColumnData = (data, customColumns) => {
    let result = {};
    for (let colData of data) {
      const columnObj = customColumns.find(
        (col) => col.id === colData.custom_columns_id
      );
      if (columnObj) {
        result[columnObj.normal_name] = colData.data;
      }
    }

    return result;
  };

  const onInfiniteGridReady = (params) => {
    storeItemsGridOptionsApi(params.api);
    setGridOptionsApi(params.api);
    setColumnApi(params.columnApi);

    const dataSource = {
      rowCount: undefined,
      getRows: async (params) => {
        const spinnerId = uuid();
        dispatch(handleEnqueueSpinner(spinnerId, 2000));
        const dataAfterSortAndFilter = sortAndFilter(
          displayedItemsRef.current,
          params.sortModel,
          params.filterModel
        );

        const rowsThisPage = dataAfterSortAndFilter.slice(
          params.startRow,
          params.endRow
        );
        let lastRow = -1;

        if (dataAfterSortAndFilter.length <= params.endRow) {
          lastRow = dataAfterSortAndFilter.length;
        }

        let rowsWithCustomData = [];

        // Fetch the custom column data for all the rows on this page as a hashmap
        // and add that data to each row
        const rowIds = rowsThisPage.map((x) => x.id);
        const customColumnData = await fetchCustomColumnDataMultiItem(
          "items",
          rowIds
        );
        for (let row of rowsThisPage) {
          if (row.id in customColumnData) {
            const formattedCustomColumnData = associateCustomColumnData(
              customColumnData[row.id],
              customColumnsRef?.current || []
            );
            rowsWithCustomData.push({ ...formattedCustomColumnData, ...row });
          } else {
            rowsWithCustomData.push({ ...row });
          }
        }

        // pass updated data with custom column data into grid
        params.successCallback(rowsWithCustomData, lastRow);
        dispatch(handleDequeueSpinner(spinnerId));
      },
    };

    params.api.setDatasource(dataSource);
  };

  const gridOptionsInfinite = {
    rowModelType: "infinite",
    onGridReady: onInfiniteGridReady,

    // display lines per page
    cacheBlockSize: 30,

    // how many rows to seek ahead when unknown data size.
    cacheOverflowSize: 0,

    // how many concurrent data requests are allowed.
    maxConcurrentDatasourceRequests: 1,

    // how many rows to initially allow scrolling to in the grid.
    infiniteInitialRowCount: 100,

    // how many pages to hold in the cache.
    maxBlocksInCache: 2,

    blockLoadDebounceMillis: 200,

    columnDefs: permissionLockedColumns,
    frameworkComponents: displayedFrameworkComponents,
    defaultColDef: {
      cellClass: ["no-border", "custom-wrap"],
      wrapText: true,
      autoHeight: true,
      flex: 1,
    },
    suppressRowClickSelection: true,
    onDisplayedColumnsChanged: (params) =>
      onDisplayedColumnsChanged(
        "ITEMS",
        params,
        selectedStages.map((s) => s.id)
      ),
    onCellValueChanged,
    onSortChanged,
    getRowStyle,
    getRowNodeId: (data) => data.id,
    pagination: false,
    editType: "fullRow",
    rowSelection: "multiple",
    stopEditingWhenGridLosesFocus: true,
    getContextMenuItems: (params) => {
      const defaultItems = params.defaultItems;
      const customItems = defaultItems.filter((item) => {
        return (
          item !== "export" &&
          item !== "exportCsv" &&
          item !== "exportExcel" &&
          item !== "separator"
        );
      });
      return customItems;
    },
  };

  const exportParams = useMemo(() => {
    if (!permissionLockedColumns || !permissionLockedColumns.length) return;

    const exportedColumns = permissionLockedColumns
      .filter((col) => col.field && !col.hide)
      .map((col) => col.field);

    return {
      columnKeys: exportedColumns,
      fileName: "items",
      processCellCallback: (params) => {
        if (
          params.column.colId === "drawing_due_date" ||
          params.column.colId === "package_due_date"
        ) {
          if (!params.value) return "";

          // @TODO This looks like a bug because dateStringToUnix already returns back * 1000
          const unixDate = dateStringToUnix(params.value);
          const f_date = generateTime(unixDate * 1000, false, true, "-");

          return f_date;
        } else if (params.column.colId === "length") {
          if (typeof params.value === "string") return params.value;
          return params.value.display ? params.value.display : "";
        } else if (typeof params.value === "number") {
          if (!params.value && params.node.data.is_cut === 1)
            return params.node.data.work_item_ids.split(",").length;
          if (!params.value) return;
          return params.value.toFixed(2);
        } else return params.value;
      },
    };
  }, [permissionLockedColumns]);

  const handleExcelExport = () => {
    if (!gridOptionsApi) return;
    if (selectedGrouping === 1 || !hasCustomColumnFeature)
      gridOptionsApi.exportDataAsExcel(exportParams);
  };
  const handleCSVExport = () => {
    if (!gridOptionsApi) return;
    if (selectedGrouping === 1 || !hasCustomColumnFeature)
      gridOptionsApi.exportDataAsCsv(exportParams);
  };

  /**
   *  Call the export API to trigger a download
   *  Have to use axios directly because useApiCall() doesn't support any of this
   */
  const handleExportTable = async (outputFormat = "xlsx") => {
    const apiBaseURL = process.env.REACT_APP_API;
    const item_ids = displayedItemsRef.current.map((job) => job.id);
    const stage_ids = selectedStages.map((stage) => stage.id).join(",");
    const method = "POST";
    const query = new URLSearchParams({ outputFormat }).toString();
    const apiPath = `items/export?${query}`;

    const config = {
      method,
      url: apiPath,
      baseURL: apiBaseURL,
      headers: { Authorization: `Bearer ${token}` },
      responseType: "arraybuffer", // Ensure binary data is handled properly
      data: { item_ids, stage_ids },
    };

    dispatch(
      notify({
        id: Date.now(),
        type: "SUCCESS",
        message: (
          <>
            Your download will start
            <br /> in a few moments.
          </>
        ),
      })
    );

    try {
      const response = await axios(config);
      // trigger the browser download
      const mimeType =
        outputFormat === "csv"
          ? "text/csv"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      const blob = new Blob([response.data], { type: mimeType });
      const href = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `items-export.${outputFormat}`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
    } catch (error) {
      console.error("Export failed:", error);
      alert("Export failed. Please try again.");
    }
  };

  // global search handling
  const previousSearchInput = usePrevious(searchInput);
  useEffect(() => {
    if (!gridOptionsApi || !columnApi || previousSearchInput === searchInput)
      return;

    const isInfiniteGrid = hasCustomColumnFeature && selectedGrouping === 0;

    const visibleColumns = columnApi
      .getAllDisplayedColumns()
      ?.filter(
        (col) =>
          col.colDef.colId &&
          !columnsToExcludeFromGlobalSearch.includes(col.colDef.colId)
      );

    if (!searchInput) {
      displayedItemsRef.current = filteredItems;

      isInfiniteGrid
        ? gridOptionsApi.purgeInfiniteCache()
        : gridOptionsApi.setRowData(filteredItems);
    } else {
      const itemsAfterSearch = searchTable(
        escapeRegExp(searchInput),
        filteredItems,
        visibleColumns
      );

      displayedItemsRef.current = itemsAfterSearch;

      isInfiniteGrid
        ? gridOptionsApi.purgeInfiniteCache()
        : gridOptionsApi.setRowData(displayedItemsRef?.current);
    }
  }, [searchInput, gridOptionsApi, columnApi, filteredItems]);

  return (
    <>
      {permissionLockedColumns && permissionLockedColumns.length ? (
        <TableContainer
          tabs={generateTabs("EXISTING_JOBS", [], showAllWork)}
          handleToggle={() => toggleShowAllWork(!showAllWork)}
          handleExcel={
            isUsingInfinateTable
              ? () => handleExportTable("xlsx")
              : handleExcelExport
          }
          handleCSV={
            isUsingInfinateTable
              ? () => handleExportTable("csv")
              : handleCSVExport
          }
          searchInput={searchInput}
          setSearchInput={setSearchInput}
          currentTable={"ITEMS"}
          selectedRows={selectedRows}
          totalRows={totalRowsCount}
          handleSearch={searchTable}
          isInfiniteGrid={isUsingInfinateTable}
        >
          {isUsingInfinateTable ? (
            <InfiniteScrollTable
              totalNumberOfRows={displayedItemsRef.current?.length || 0}
              gridOptions={{ ...gridOptionsInfinite }}
              overrideCheckboxSelectColumn={true}
              setSelectedRows={setSelectedRows}
              selectedRowIds={selectedRows.map((item) => item.id)}
              onSelectAll={(isSelected) => {
                if (isSelected)
                  setSelectedRows(() => [...displayedItemsRef.current]);
                else setSelectedRows(() => []);
              }}
            />
          ) : (
            <AgTable gridOptions={gridOptionsClient} />
          )}
        </TableContainer>
      ) : null}
    </>
  );
};

export default ItemsTable;
