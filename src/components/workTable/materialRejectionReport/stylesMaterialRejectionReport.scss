@import "../../styles/colors.scss";
@import "../../styles/sizes.scss";

div.material-rejection-report {
  width: 85vw;
  min-width: 600px;

  & h6 {
    font-size: 0.9rem;
    margin: 0 0 10px;
    font-weight: normal;
    color: white;
  }

  & div.ag-theme-balham-dark.custom-ag-styles {
    width: 100%;
    // 40 modal title, 40 padding, 27 heading, 52 search, 10 for modal outside, 28 bottom button
    height: calc(
      100vh - #{$headerFooter} - 40px - 40px - 27px - 52px - 10px - 28px
    );
    @supports (-webkit-touch-callout: none) {
      height: calc(
        100vh - #{$headerFooter} - #{$iosAddressBar} - 40px - 40px - 27px - 52px -
          10px - 28px
      );
    }
    max-height: 500px;

    & div.ag-cell {
      min-height: 60px;
      white-space: normal;
    }
  }
}

div.table-action-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;

  & .left {
    display: flex;
    align-items: center;
    width: 400px;

    & button {
      height: 28px;
      border-radius: 3px;
      background-color: $blue;
      color: white;
      border: none;
      font-size: 0.8rem;
      margin-left: 10px;
      cursor: pointer;

      &:focus {
        outline: none;
        border: none;
      }
      &:disabled {
        background-color: darken($blue, 10%);
        color: darken(white, 10%);
        cursor: default;
      }
    }

    & button.group-btn {
      width: 120px;
    }
    & button.all {
      width: 100px;
    }
  }

  & span {
    display: flex;
    align-items: center;
    border-radius: 3px;
    padding: 2px;

    & input {
      background-color: white;
      height: 28px;
      border: none;
      border-radius: 3px;
      outline: none;
      padding: 3px 5px;
      color: black;
      box-sizing: border-box;
    }
  }
}

button.send-email {
  padding: 0 8px;
  height: 28px;
  border-radius: 3px;
  border: none;
  background-color: $blue;
  color: white;
  cursor: pointer;

  &:focus {
    outline: none;
    border: none;
  }
}
span.loading-wrapper {
  height: 500px;
}

// FORM

div.materials-rejection-form {
  height: 280px;
  width: 500px;
  display: flex;
  flex-direction: column;

  & textarea {
    height: 50px;
    font-size: 1rem;
    padding: 3px;
  }

  & button.submit {
    margin-top: 40px;
    margin-left: auto;
    background-color: $blue;
    color: white;
    height: 32px;
    width: 100px;
    outline: none;
    border: none;
    border-radius: 3px;
    cursor: pointer;

    &:disabled {
      background-color: darken($blue, 10%) !important;
      cursor: default;
    }
  }
}

div.email-input-wrapper {
  height: 240px !important;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
