// NPM PACKAGE IMPORTS
import React, { useState } from "react";
import { useDispatch } from "react-redux";

// REDUX IMPORTS
import { notify } from "../../reusable/alertPopup/alertPopupActions";

// HELPER FUNCTION IMPORTS
import { sendMaterialReport } from "../../../_services";

// COMPONENT IMPORTS
import Modal from "../../reusable/modal/Modal";

// STYLE IMPORTS
import "./stylesMaterialRejectionReport.scss";

const RejectionForm = ({
  showModal,
  onClose,
  rowInfo,
  isGrouped,
  sendResolved,
}) => {
  const [emails, setEmails] = useState("");
  const [subject, setSubject] = useState("");
  const [body, setBody] = useState("");

  const dispatch = useDispatch();

  const onSubmit = () => {
    const emailArr = emails.split(",");

    const emailListRegex = /^([\w+-.%]+@[\w-.]+\.[A-Za-z]{2,4},?)+$/;
    const validEmail = emailListRegex.exec(emails);

    if (!validEmail) {
      return dispatch(
        notify({
          id: Date.now(),
          type: "ERROR",
          message: "Incorrect Email Format",
        })
      );
    }

    sendMaterialReport(
      emailArr,
      subject,
      body,
      rowInfo.id,
      rowInfo.package_name,
      rowInfo.number,
      isGrouped ? 1 : 0,
      sendResolved ? 1 : 0
    )
      .then((res) => {
        onClose();
        if (!res || !res.status || !res.message) return;
        dispatch(
          notify({
            id: Date.now(),
            type: res.status.toUpperCase(),
            message: res.message,
          })
        );
      })
      .catch((error) => {
        return dispatch(
          notify({
            id: Date.now(),
            type: "ERROR",
            message: error.message,
          })
        );
      });
  };

  return (
    <Modal
      onSubmit={onSubmit}
      title="Rejection Report Email Form"
      showModal={showModal}
      onClose={onClose}
    >
      <div className="materials-rejection-form">
        <div className="email-input-wrapper">
          <textarea
            value={emails}
            onChange={(e) => setEmails(e.target.value)}
            placeholder="Comma-separated emails (ex. <EMAIL>,<EMAIL>)"
          ></textarea>
          <textarea
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
            placeholder="Subject"
          ></textarea>
          <textarea
            value={body}
            onChange={(e) => setBody(e.target.value)}
            placeholder="Body"
          ></textarea>
        </div>
        <button
          type="button"
          onClick={onSubmit}
          disabled={!emails && !subject && !body ? true : false}
          className="submit"
        >
          Send Report
        </button>
      </div>
    </Modal>
  );
};

export default RejectionForm;
