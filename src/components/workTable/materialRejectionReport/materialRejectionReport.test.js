import { materialRejectionColumnDefs } from "./materialRejectionReportConstants";

describe("Material Rejection Report", () => {
  const defaultHeaders = [
    "Package",
    "Package ID",
    "Package #",
    "Material",
    "Size",
    "Category",
    "Description",
    "Stage",
    "Time Rejected",
    "Rejected By",
    "Resolved",
    "Time Resolved",
    "Resolved By",
  ];

  const testStore = {
    getState: () => ({ profileData: testStore.profileData }),
    profileData: {
      systemSettings: {
        date_display: "MM-DD-YYYY",
        timezone: "America/Chicago",
      },
    },
  };

  describe("Column Defs", () => {
    let populatedColumns = materialRejectionColumnDefs(false, testStore);

    it("Headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);

      expect(columnHeaders).toEqual(defaultHeaders);
    });

    describe("PACKAGE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Package");
      });

      const params = {
        data: {
          package_name: "test package",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test package");
      });
    });

    describe("PACKAGE ID", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Package ID");
      });

      const params = {
        data: {
          package_id: 1,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(1);
      });
    });

    describe("PACKAGE #", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Package #");
      });

      const params = {
        data: {
          package_number: "100",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("100");
      });
    });

    describe("MATERIAL", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Material");
      });

      const params = {
        data: {
          material_name: "test mat",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test mat");
      });
    });

    describe("SIZE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Size");
      });

      const params = {
        data: {
          size: "10",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("10");
      });
    });

    describe("CATEGORY", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Category");
      });

      const params = {
        data: {
          rejection_category: "test cat",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test cat");
      });
    });

    describe("DESCRIPTION", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Description");
      });

      const params = {
        data: {
          description: "test desc",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test desc");
      });
    });

    describe("STAGE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Stage");
      });

      const params = {
        data: {
          rejected_from_stage: "test stage",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test stage");
      });
    });

    describe("TIME REJECTED", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Time Rejected");
      });

      const params = {
        data: {
          rejected_on: 1613402290,
        },
        value: 1613402290,
      };

      it("valueFormatter", () => {
        expect(column.valueFormatter(params)).toEqual("02-15-2021");
      });

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("02-15-2021");
      });
    });

    describe("REJECTED BY", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Rejected By");
      });

      const params = {
        data: {
          rejected_by: "test user",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test user");
      });
    });

    describe("RESOLVED", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Resolved");
      });

      const params = {
        data: {
          resolved_on: 1613402290,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("Yes");
      });
      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("Yes");
      });
    });

    describe("TIME RESOLVED", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Time Resolved");
      });

      const params = {
        data: {
          resolved_on: 1613402290,
        },
        value: 1613402290,
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("02-15-2021");
      });
      it("valueFormatter", () => {
        expect(column.valueFormatter(params)).toEqual("02-15-2021");
      });
    });

    describe("RESOLVED BY", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Resolved By");
      });

      const params = {
        data: {
          resolved_by: "test user",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test user");
      });
    });
  });

  describe("Grouped Column Defs", () => {
    let populatedColumns = materialRejectionColumnDefs(true, testStore);

    it("Headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);

      expect(columnHeaders).toEqual(["Quantity", ...defaultHeaders]);
    });
  });
});
