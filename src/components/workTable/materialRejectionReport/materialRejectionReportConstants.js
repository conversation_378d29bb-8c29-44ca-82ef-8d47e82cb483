// HELPER FUNCTION IMPORTS
import { generateTime, naturalSort } from "../../../_utils";

// STYLE IMPORTS
import "../../styles/tables.scss";

export const materialRejectionColumnDefs = (showGrouped, testStore = null) => {
  let columns = [
    {
      headerName: "Package",
      field: "package_name",
      minWidth: 100,
      width: 120,
      getQuickFilterText: (params) => params.data.package_name,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_name",
      autoHeight: true,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Package ID",
      field: "package_id",
      minWidth: 100,
      width: 120,
      getQuickFilterText: (params) => params.data.package_id,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_id",
    },
    {
      headerName: "Package #",
      field: "package_number",
      minWidth: 100,
      width: 120,
      getQuickFilterText: (params) => params.data.package_number,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_number",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Drawing",
      field: "drawing_name",
      minWidth: 100,
      width: 120,
      getQuickFilterText: (params) => params.data.drawing_name,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "drawing_name",
      autoHeight: true,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Drawing ID",
      field: "drawing_id",
      minWidth: 100,
      width: 120,
      getQuickFilterText: (params) => params.data.drawing_id,
      filter: showGrouped ? "agTextColumnFilter" : "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "drawing_id",
    },
    {
      headerName: "Material",
      autoHeight: true,
      field: "material_name",
      minWidth: 120,
      width: 140,
      getQuickFilterText: (params) => params.data.material_name,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "material_name",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Size",
      field: "size",
      minWidth: 80,
      width: 100,
      getQuickFilterText: (params) => params.data.size,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "size",
    },
    {
      headerName: "Category",
      field: "rejection_category",
      minWidth: 100,
      width: 120,
      getQuickFilterText: (params) => params.data.rejection_category,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "rejection_category",
    },
    {
      headerName: "Description",
      field: "rejection_description",
      minWidth: 120,
      width: 140,
      getQuickFilterText: (params) => params.data.description,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "rejection_description",
      autoHeight: true,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Stage",
      field: "rejected_from_stage",
      minWidth: 100,
      width: 120,
      getQuickFilterText: (params) => params.data.rejected_from_stage,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "rejected_from_stage",
      autoHeight: true,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Time Rejected",
      field: "rejected_on",
      valueFormatter: (params) => {
        return params.value
          ? typeof params.value === "number"
            ? generateTime(params.value * 1000, false, true, "-", testStore)
            : params.value
          : "-";
      },
      minWidth: 100,
      width: 120,
      getQuickFilterText: (params) => {
        return params.value
          ? typeof params.value === "number"
            ? generateTime(params.value * 1000, false, true, "-", testStore)
            : params.value
          : "-";
      },
      filter: "agDateColumnFilter",
      filterParams: {
        buttons: ["reset"],
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = cellValue
            ? typeof cellValue === "number"
              ? new Date(
                  generateTime(cellValue * 1000, false, true, "-", testStore)
                )
              : new Date(cellValue)
            : "-";

          return cellDate < filterLocalDateAtMidnight
            ? -1
            : cellDate > filterLocalDateAtMidnight
            ? 1
            : 0;
        },
      },
      menuTabs: ["filterMenuTab"],
      colId: "rejected_on",
    },
    {
      headerName: "Rejected By",
      field: "rejected_by",
      minWidth: 100,
      width: 120,
      getQuickFilterText: (params) => params.data.rejected_by,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "rejected_by",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Resolved",
      valueGetter: (params) => (params.data.resolved_on ? "Yes" : "No"),
      minWidth: 100,
      width: 120,
      getQuickFilterText: (params) => (params.data.resolved_on ? "Yes" : "No"),
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "resolved",
    },
    {
      headerName: "Time Resolved",
      field: "resolved_on",
      valueFormatter: (params) => {
        return params.value
          ? typeof params.value === "number"
            ? generateTime(params.value * 1000, false, true, "-", testStore)
            : params.value
          : null;
      },
      minWidth: 100,
      width: 120,
      getQuickFilterText: (params) => {
        return params.value
          ? typeof params.value === "number"
            ? generateTime(params.value * 1000, false, true, "-", testStore)
            : params.value
          : "-";
      },
      filter: "agDateColumnFilter",
      filterParams: {
        buttons: ["reset"],
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = cellValue
            ? typeof cellValue === "number"
              ? new Date(
                  generateTime(cellValue * 1000, false, true, "-", testStore)
                )
              : new Date(cellValue)
            : "-";

          return cellDate < filterLocalDateAtMidnight
            ? -1
            : cellDate > filterLocalDateAtMidnight
            ? 1
            : 0;
        },
      },
      menuTabs: ["filterMenuTab"],
      colId: "resolved_on",
    },
    {
      headerName: "Resolved By",
      field: "resolved_by",
      minWidth: 100,
      width: 120,
      getQuickFilterText: (params) => params.data.resolved_by,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "resolved_by",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
  ];

  if (showGrouped) {
    columns.unshift({
      headerName: "Quantity",
      field: "quantity",
      minWidth: 100,
      width: 100,
      getQuickFilterText: (params) => params.data.quantity,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "quantity",
      pinned: "left",
    });
  }

  return columns;
};
