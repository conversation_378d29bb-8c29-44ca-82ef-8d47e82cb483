// NPM PACKAGE IMPORTS
import React, { useState, useEffect, useRef, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import { handleFetchRejectionsByPackage } from "../../items/itemsActions";

// CONTANTS IMPORTS
import { materialRejectionColumnDefs } from "./materialRejectionReportConstants";

// HELPER FUNCTION IMPORTS
import { generateTime } from "../../../_utils";

// COMPONENT IMPORTS
import AgTable from "../../reusable/agTable/AgTable";
import Modal from "../../reusable/modal/Modal";
import RejectionForm from "./RejectionForm";
import TableExportDropdown from "../../reusable/tableExportDropdown/TableExportDropdown";

// STYLE IMPORTS
import "./stylesMaterialRejectionReport.scss";

const MaterialRejectionReport = ({ showModal, handleClose, rowInfo }) => {
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [searchValue, setSearchValue] = useState("");
  const [showForm, toggleForm] = useState(false);
  const [showGrouped, toggleGrouped] = useState(false);
  const [showAll, toggleAll] = useState(false);
  const [displayedRejections, setDisplayedRejections] = useState([]);

  const dispatch = useDispatch();

  const { rejectionsByPackage, isLoading, error } = useSelector(
    (state) => state.itemsData
  );

  const searchInputRef = useRef(null);

  const displayedColumns = useMemo(() => {
    return materialRejectionColumnDefs(showGrouped);
  }, [showGrouped]);

  useEffect(() => {
    dispatch(
      handleFetchRejectionsByPackage(
        rowInfo.id,
        showGrouped ? 1 : 0,
        showAll ? 1 : 0
      )
    );
  }, [rowInfo, showGrouped, showAll]);

  useEffect(() => {
    if (isLoading) return;
    if (!error) {
      setDisplayedRejections(rejectionsByPackage);
    } else setDisplayedRejections([]);
  }, [rejectionsByPackage, error]);

  useEffect(() => {
    if (!gridOptionsApi) return;

    gridOptionsApi.setRowData(displayedRejections);
  }, [gridOptionsApi, displayedRejections]);

  useEffect(() => {
    if (showGrouped) toggleAll(false);
    if (!gridOptionsApi) return;

    gridOptionsApi.setColumnDefs(displayedColumns);
  }, [showGrouped]);

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
  };
  const onSortChanged = (params) => {
    params.api.redrawRows();
  };
  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };
  const gridOptions = {
    defaultColDef: {
      cellClass: ["custom-wrap"],
      wrapText: true,
    },
    rowData: rejectionsByPackage,
    // frameworkComponents
    reactNext: true,
    rowClassRules,
    onSortChanged,
    onGridReady,
    columnDefs: displayedColumns,
    pagination: true,
    paginationPageSize: 100,
    getRowNodeId: (data) => data.id,
  };

  const exportParams = useMemo(() => {
    if (!displayedColumns) return;

    return {
      fileName: `${rowInfo.package_name}_rejection_report`.replace(/\./g, " "),
      processCellCallback: (params) => {
        const { colId } = params.column;

        if (colId === "rejected_on" || colId === "resolved_on") {
          if (!params.value) return "";

          const f_date = generateTime(params.value * 1000, false, true, "-");

          return f_date;
        } else return params.value;
      },
    };
  }, [displayedColumns]);

  const handleCSV = () => {
    if (gridOptionsApi) gridOptionsApi.exportDataAsCsv(exportParams);
  };
  const handleExcel = () => {
    if (gridOptionsApi) gridOptionsApi.exportDataAsExcel(exportParams);
  };

  useEffect(() => {
    if (!gridOptionsApi) return;
    gridOptionsApi.setQuickFilter(searchValue);
  }, [searchValue, gridOptionsApi]);

  const onClose = () => {
    if (showForm) {
      toggleForm();
    } else {
      setDisplayedRejections([]);
      handleClose();
    }
  };

  return (
    <>
      <Modal
        title="Rejection Information"
        showModal={showModal}
        onClose={onClose}
        dark={true}
        onSubmit={() => null}
      >
        <div className="material-rejection-report">
          <h6>Rejected Materials</h6>
          <div className="table-action-row">
            <div className="left">
              <span>
                <input
                  ref={searchInputRef}
                  placeholder="Search table"
                  type="text"
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                />
              </span>
              <button
                type="button"
                onClick={() => toggleGrouped(!showGrouped)}
                className="group-btn"
                disabled={showAll}
              >
                {showGrouped ? "View Ungrouped" : "View Grouped"}
              </button>
              <button
                type="button"
                onClick={() => toggleAll(!showAll)}
                className="all"
                disabled={showGrouped}
              >
                {showAll ? "View Active" : "View All"}
              </button>
            </div>
            <TableExportDropdown
              handleCSV={handleCSV}
              handleExcel={handleExcel}
            />
          </div>
          <AgTable gridOptions={gridOptions} />
          <button
            className="send-email"
            type="button"
            onClick={() => toggleForm(true)}
          >
            Send Rejection Report Email
          </button>
        </div>
        {showForm && (
          <RejectionForm
            showModal={showForm}
            onClose={() => toggleForm(false)}
            rowInfo={rowInfo}
            isGrouped={showGrouped}
            sendResolved={showAll}
          />
        )}
      </Modal>
    </>
  );
};

export default MaterialRejectionReport;
