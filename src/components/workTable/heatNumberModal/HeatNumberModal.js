// NPM PACKAGE IMPORTS
import React, { useEffect, useMemo, useState, useRef } from "react";
import { useDispatch } from "react-redux";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import Input from "msuite_storybook/dist/input/Input";
import { FaArrowRight } from "react-icons/fa";

// REDUX IMPORTS
import { handleFetchItemHeatNumbers } from "../../drawings/drawingsActions";
import {
  handleUpdateJointHeatNumber,
  handleUpdateItems,
  handleUpdateGroupedItems,
  handleFetchItemsById,
  handleUpdateItemNoRefresh,
} from "../../items/itemsActions";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../hooks/useOutsideClick";
import { titleize } from "../../../_utils";

// STYLE IMPORTS
import "./stylesHeatNumberModal.scss";

const HeatNumberModal = ({
  heatNumbers,
  rowInfo,
  open,
  handleClose,
  translate,
  gridOptionsApi,
  isGrouped,
  quantity,
  callback,
  tableType = "JOBS",
  selectedStages,
}) => {
  const [selected, setSelected] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const [showDropdown, toggleDropdown] = useState(false);

  const dispatch = useDispatch();

  const dropdownRef = useRef(null);
  useOutsideClick(dropdownRef, () => toggleDropdown(false));

  useEffect(() => {
    if (rowInfo && rowInfo._column && rowInfo.size) {
      dispatch(
        handleFetchItemHeatNumbers(
          rowInfo._column === "heat_number" ? "" : "JOINT",
          [rowInfo.job_id],
          rowInfo.size,
          rowInfo.material_name,
          [rowInfo.drawing_id]
        )
      );
    }
  }, [rowInfo]);

  // need to know if the value has been updated and NOT auto populate value when clicking into dropdown
  useEffect(() => {
    if (!gridOptionsApi) return;
    if (rowInfo) {
      setSearchValue("");
      toggleDropdown(false);

      if (rowInfo[rowInfo._column]) setSelected(rowInfo[rowInfo._column]);
      else if (
        rowInfo.joint_heat_numbers &&
        Array.isArray(JSON.parse(rowInfo.joint_heat_numbers))
      )
        setSelected(
          JSON.parse(rowInfo.joint_heat_numbers).find(
            (j) => j["position"] === parseInt(rowInfo._column.slice(-1))
          )
            ? JSON.parse(rowInfo.joint_heat_numbers).find(
                (j) => j["position"] === parseInt(rowInfo._column.slice(-1))
              ).heat_number
            : ""
        );
    }
  }, [rowInfo, gridOptionsApi]);

  const handleSubmit = () => {
    if (selected !== rowInfo[rowInfo._column]) {
      if (tableType === "WIZARD") {
        const value = selected
          ? selected.trim()
          : searchValue
          ? searchValue.trim()
          : null;

        if (rowInfo._column.includes("joint")) {
          dispatch(
            handleUpdateJointHeatNumber({
              work_item_ids: [rowInfo.id],
              position: parseInt(rowInfo._column.slice(-1)),
              heat_number: value,
            })
          ).then((res) => {
            if (!res.error) {
              if (gridOptionsApi) {
                dispatch(handleFetchItemsById([rowInfo.id])).then((res) => {
                  if (!res.error) {
                    const rowNode = gridOptionsApi.getRowNode(rowInfo.id);
                    rowNode.setData(res[0]);
                  }
                });
              }
            }
          });
        } else if (rowInfo._column === "heat_number") {
          dispatch(
            handleUpdateItems([rowInfo.id], {
              heat_number: selected.trim() === "" ? null : selected.trim(),
            })
          ).then((res) => {
            if (!res.error) {
              if (gridOptionsApi) {
                const rowNode = gridOptionsApi.getRowNode(rowInfo.id);
                rowNode.setDataValue(rowInfo._column, value);
              }
            }
          });
        }

        handleClose();
        gridOptionsApi.setFocusedCell(0, "container_name");
        gridOptionsApi.startEditingCell({
          rowIndex: 0,
          colKey: "container_name",
        });
        typeof callback === "function" && callback();
        return;
      }
      if (rowInfo._column === "heat_number") {
        if (isGrouped && quantity) {
          dispatch(
            handleUpdateGroupedItems(
              rowInfo.work_item_ids,
              { heat_number: selected.trim() === "" ? null : selected.trim() },
              parseFloat(quantity)
            )
          ).then((res) => {
            if (!res.error) {
              gridOptionsApi.stopEditing();
              const rowNode = gridOptionsApi.getRowNode(rowInfo.id);
              rowNode.setData({
                ...rowInfo,
                [rowInfo._column]: selected
                  ? selected.trim()
                  : searchValue.trim(),
              });
              handleClose();
              typeof callback === "function" && callback();
            }
          });
        } else {
          dispatch(
            handleUpdateItems([rowInfo.work_item_ids], {
              heat_number: selected.trim() === "" ? null : selected.trim(),
            })
          ).then((res) => {
            gridOptionsApi.stopEditing();
            if (!res.error && gridOptionsApi) {
              const rowNode = gridOptionsApi.getRowNode(rowInfo.id);
              rowNode.setData({
                ...rowInfo,
                heat_number: res[0].heat_number,
              });
            }
            handleClose();
            typeof callback === "function" && callback();
          });
        }
      } else {
        dispatch(
          handleUpdateJointHeatNumber({
            work_item_ids: [rowInfo.id],
            position: parseInt(rowInfo._column.slice(-1)),
            heat_number: selected ? selected.trim() : null,
          })
        ).then((res) => {
          if (!res.error) {
            if (gridOptionsApi) {
              const selectedStageIds = selectedStages
                .map((x) => x.id)
                .join(",");
              dispatch(
                handleFetchItemsById([rowInfo.id], {
                  stage_id: selectedStageIds,
                })
              ).then((res) => {
                if (!res.error) {
                  const rowNode = gridOptionsApi.getRowNode(rowInfo.id);

                  // have to pass in rowInfo or else custom columns will be lost
                  rowNode.setData({
                    ...rowInfo,
                    ...res[0],
                  });

                  dispatch(handleUpdateItemNoRefresh(rowNode.data));

                  // TODO: may want to move this up as well to be before rowNode.setData
                  // to avoid second "edit" if it has this issue
                  gridOptionsApi.stopEditing();
                  handleClose();
                }
              });
            }
          }
        });
      }
    }
  };

  const displayedHeatNumbers = useMemo(() => {
    if (rowInfo._column === "heat_number") {
      if (!heatNumbers) return [];
      if (searchValue.length) {
        return heatNumbers.filter(
          (hn) => hn.heatNumber && hn.heat_number.includes(searchValue)
        );
      } else return heatNumbers;
    } else if (heatNumbers && searchValue)
      return heatNumbers.filter(
        (hn) =>
          hn.material_name === rowInfo.material_name &&
          hn.heat_number &&
          hn.heat_number.includes(searchValue)
      );
    else if (heatNumbers)
      return heatNumbers.filter(
        (hn) => hn.material_name === rowInfo.material_name && hn.heat_number
      );
    else return [];
  }, [heatNumbers, selected, rowInfo, searchValue]);

  const disableSubmit = useMemo(() => {
    if (!rowInfo || !gridOptionsApi) return true;

    if (rowInfo._column === "heat_number") {
      // check if value is empty or null
      if (selected === rowInfo[rowInfo._column]) return true;
      if (searchValue === rowInfo[rowInfo._column]) return true;
    } else if (rowInfo._column === "joint_heat_number_1") {
      const currentValue = rowInfo.empty
        ? null
        : (
            JSON.parse(rowInfo.joint_heat_numbers).find(
              (i) => i["position"] === 1
            ) || {}
          ).heat_number || "";

      // check if searchValue and currentValue are both empty
      if (
        currentValue === searchValue.trim() &&
        (!selected || selected.trim() === "")
      )
        return true;

      if (selected === currentValue) return true;
      if ((!searchValue || !searchValue.trim()) && !selected && !currentValue)
        return true;
    } else if (rowInfo._column === "joint_heat_number_2") {
      const currentValue = rowInfo.empty
        ? null
        : (
            JSON.parse(rowInfo.joint_heat_numbers).find(
              (i) => i["`position`"] === 2
            ) || {}
          ).heat_number || "";

      // check if searchValue and currentValue are both empty
      if (
        currentValue === searchValue.trim() &&
        (!selected || selected.trim() === "")
      )
        return true;

      if (selected === currentValue) return true;
      if ((!searchValue || !searchValue.trim()) && !selected && !currentValue)
        return true;
    }

    return false;
  }, [selected, searchValue, rowInfo, gridOptionsApi]);

  const isFieldPopulated = useMemo(() => {
    if (!rowInfo || rowInfo.empty || !gridOptionsApi) return false;

    let rowNodeData = gridOptionsApi.getRowNode(rowInfo.id).data;

    if (rowInfo._column.includes("joint")) {
      let joints = JSON.parse(rowNodeData.joint_heat_numbers);
      const value = joints.find((j) => {
        return j["position"] === parseInt(rowInfo._column.slice(-1));
      });

      return value ? !!value.heat_number : false;
    } else return !!rowNodeData.heat_number;
  }, [rowInfo, gridOptionsApi]);

  return (
    <Modal open={open} handleClose={handleClose}>
      <div className="heat-number-modal">
        <h2 className="title">{titleize(rowInfo._column)}</h2>
        <div className="content">
          <label className="label">
            {translate("Select existing or enter new:")}
          </label>
          <Input
            className="input"
            value={selected}
            onChange={(e) => {
              e.persist();
              setSearchValue(e.target.value);
              setSelected(e.target.value);
            }}
            onFocus={() => toggleDropdown(true)}
            autoFocus
          />
          {showDropdown && (
            <ul ref={dropdownRef} className="dropdown">
              {isFieldPopulated && (
                <li
                  className="dropdown-option"
                  key="clear value"
                  onClick={() => {
                    setSearchValue("");
                    setSelected("");
                    toggleDropdown(false);
                  }}
                ></li>
              )}
              {displayedHeatNumbers.map((hn) => (
                <li
                  className="dropdown-option"
                  key={hn.heat_number}
                  onClick={() => {
                    setSearchValue("");
                    setSelected(hn.heat_number);
                    toggleDropdown(false);
                  }}
                >
                  {hn.heat_number}
                </li>
              ))}
            </ul>
          )}
        </div>
        <div className="actions">
          <Button className="cancel" onClick={handleClose}>
            {translate("Cancel")}
          </Button>
          <Button
            className="submit"
            onClick={handleSubmit}
            disabled={disableSubmit}
            // disabled={
            //   selected === rowInfo[rowInfo._column] ||
            //   (!selected && rowInfo._column === "heat_number")
            // }
          >
            {translate("Submit")} <FaArrowRight />
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default HeatNumberModal;
