// NPM PACKAGE IMPORTS
import React, { useEffect, useMemo, useState } from "react";
import Tracker from "msuite_storybook/dist/tracker/Tracker";
import Loading from "msuite_storybook/dist/loading/Loading";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import { handleFetchStatusTrackerStages as handleFetchPackagesStatusTrackerStages } from "../../packages/packagesActions";
import { handleFetchStatusTrackerStages as handleFetchDrawingsStatusTrackerStages } from "../../drawings/drawingsActions";
import { handleFetchAvailableStages } from "../../items/itemsActions";

// STYLE IMPORTS
import "./stylesStatusTrackerPopup.scss";

const StatusTrackerPopup = ({ selectedItem: { type, id, position } }) => {
  const [loading, toggleLoading] = useState(false);

  const dispatch = useDispatch();
  const { statusTracker: packagesStatusTracker } = useSelector(
    (state) => state.packagesData
  );
  const { statusTracker: drawingsStatusTracker } = useSelector(
    (state) => state.drawingsData
  );
  const { availableStages } = useSelector((state) => state.itemsData);

  useEffect(() => {
    if (!loading) {
      if (["PACKAGE", "DRAWING", "ITEM"].includes(type)) toggleLoading(true);
    }
  }, [type, id]);

  useEffect(() => {
    if (loading) {
      if (type === "PACKAGE")
        dispatch(handleFetchPackagesStatusTrackerStages(id)).then(() =>
          toggleLoading(false)
        );
      else if (type === "DRAWING")
        dispatch(handleFetchDrawingsStatusTrackerStages(id)).then(() =>
          toggleLoading(false)
        );
      else if (type === "ITEM")
        dispatch(handleFetchAvailableStages(id)).then(() =>
          toggleLoading(false)
        );
    }
  }, [loading]);

  const displayedTrackerData = useMemo(() => {
    if (type === "PACKAGE") {
      return packagesStatusTracker.map((pst) => ({
        ...pst,
        stage_id: pst.stage_status_group_name || "Ungrouped",
        stage_name: pst.stage_status_group_name || "Ungrouped",
      }));
    } else if (type === "DRAWING") return drawingsStatusTracker;
    else if (type === "ITEM")
      return availableStages
        ? availableStages.map((s) => ({
            stage_id: s.id,
            stage_name: s.name,
            items_complete: s.completed,
            total_items: 1,
          }))
        : [];
  }, [packagesStatusTracker, type, drawingsStatusTracker, availableStages]);

  const popupPosition = useMemo(
    () => ({
      top: position.y,
      left:
        type === "ITEM"
          ? position.x - (loading ? 30 : 295 / (availableStages ? 1 : 7 / 3))
          : position.x -
            (loading ? -100 : !displayedTrackerData?.length ? -20 : 315 / 2),
    }),
    [position, type, availableStages, loading, displayedTrackerData]
  );

  return (
    <>
      {displayedTrackerData ? (
        <div className="status-tracker-popup" style={popupPosition}>
          <div
            className={`internal-wrapper ${
              displayedTrackerData.length && !loading ? "" : "no-display"
            }`}
          >
            <div className="arrow"></div>
            {loading ? (
              <Loading />
            ) : displayedTrackerData.length ? (
              <Tracker data={displayedTrackerData} small />
            ) : (
              "No work item stages to display."
            )}
          </div>
        </div>
      ) : (
        <></>
      )}
    </>
  );
};

export default StatusTrackerPopup;
