@import "../../styles/colors.scss";

div.status-tracker-popup {
  position: fixed;
  background-color: $grey;
  border: 2px solid #000;
  border-radius: 4px;
  padding: 0 0 2px;

  & > div.internal-wrapper {
    position: relative;
    background-color: $grey;
    min-width: 600px;

    &.no-display {
      min-width: unset;
      color: #fff;
      padding: 10px;
    }

    & > div.arrow {
      position: absolute;
      left: 50%;
      top: calc(-25% - 2px);

      width: 30px;
      height: 30px;
      border-top: 2px solid #000;
      border-right: 2px solid #000;
      border-top-right-radius: 3px;
      background-color: $grey;
      z-index: -1;
      transform: rotateZ(-45deg) translate(-11px, -11px);
    }

    &:not(.no-display) > div.arrow {
      transform: rotateZ(-45deg) translate(-50%, -50%);
    }

    &:not(.no-display) > div.loader-wrapper div.loader {
      margin-left: 30px;
    }

    & > div.loader-wrapper div.loader {
      height: 30px;
      width: 30px;
      border-width: 8px;
    }
  }
}
