// NPM PACKAGE IMPORTS
import React, { useEffect, useMemo, useState } from "react";
import Select from "msuite_storybook/dist/select/Select";
import Input from "msuite_storybook/dist/input/Input";
import Button from "msuite_storybook/dist/button/Button";
import Modal from "msuite_storybook/dist/modal/Modal";
import { FaArrowRight } from "react-icons/fa";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import {
  handleFetchRejectionCategories,
  handleFetchSentBackStages,
  handleRejectItems,
} from "../../items/itemsActions";

// STYLE IMPORTS
import "./stylesRejectionModal.scss";

const RejectionModal = ({
  open,
  handleClose,
  translate,
  rowInfo,
  selectedStage,
  postRejectionSuccess,
}) => {
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedScrapOrRework, setSelectedScrapOrRework] = useState(null);
  const [selectedSentBackStage, setSelectedSentBackStage] = useState(null);
  const [description, setDescription] = useState("");

  const dispatch = useDispatch();
  const { sentBackStages, rejectionCategories } = useSelector(
    (state) => state.itemsData
  );

  useEffect(() => {
    dispatch(handleFetchRejectionCategories);
  }, []);

  useEffect(() => {
    if (rowInfo && selectedStage && selectedStage.id !== 0)
      dispatch(
        handleFetchSentBackStages(rowInfo.id.toString(), selectedStage.id)
      );
  }, [rowInfo, selectedStage]);

  useEffect(() => {
    if (!sentBackStages.length) setSelectedScrapOrRework("SCRAP");
  }, [sentBackStages]);

  const displayedCategories = useMemo(() => {
    return rejectionCategories.map((rc) => ({
      id: rc.id,
      value: rc.id,
      display: translate(rc.name),
    }));
  }, [rejectionCategories]);

  const displayedScrapOrRework = useMemo(() => {
    const fullArr = [
      { value: "SCRAP", display: translate("Scrap") },
      { value: "REWORK", display: translate("Rework") },
    ];

    if (!sentBackStages.length) return fullArr.slice(0, -1);
    else return fullArr;
  }, [sentBackStages]);

  const displayedSentBackStages = useMemo(() => {
    return sentBackStages
      .map((s) => ({ id: s.id, value: s.id, display: s.name }))
      .sort((a, b) =>
        a.display.toLowerCase() > b.display.toLowerCase() ? 1 : -1
      );
  }, [sentBackStages]);

  return (
    <Modal open={open} handleClose={handleClose}>
      <div className="rejection-modal-wrapper">
        <h2 className="title">
          {translate("Reject Item:")}{" "}
          {rowInfo.package_id +
            " > " +
            rowInfo.drawing_name +
            " > " +
            rowInfo.material_name}
        </h2>
        <div
          className="content"
          style={
            selectedScrapOrRework === "REWORK"
              ? { gridTemplateRows: "50px 60px 50px 100px" }
              : { gridTemplateRows: "50px 60px 100px" }
          }
        >
          <label>
            <span>{translate("Category:")}</span>
            <Select
              options={displayedCategories}
              placeholder={translate("-- Select a category --")}
              value={selectedCategory}
              onInput={(e) => setSelectedCategory(parseInt(e.target.value))}
              required
            />
          </label>
          <label>
            <span>{translate("Scrap or Rework?")}</span>
            <Select
              options={displayedScrapOrRework}
              value={selectedScrapOrRework}
              onInput={(e) => setSelectedScrapOrRework(e.target.value)}
              required
            />
            {selectedScrapOrRework === "SCRAP" ? (
              <p>
                {translate(
                  "Resets the whole drawing. Does not need to be re-approved."
                )}
              </p>
            ) : (
              <></>
            )}
          </label>
          {selectedScrapOrRework === "REWORK" ? (
            <label>
              <span>{translate("Send back to stage:")}</span>
              <Select
                options={displayedSentBackStages}
                value={selectedSentBackStage}
                onInput={(e) =>
                  setSelectedSentBackStage(parseInt(e.target.value))
                }
                required
              />
            </label>
          ) : (
            <></>
          )}
          <label>
            <span>{translate("Description:")}</span>
            <Input
              as="textarea"
              placeholder={translate("Enter a description")}
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />
          </label>
        </div>
        <div className="actions">
          <Button className="cancel" onClick={handleClose}>
            {translate("Cancel")}
          </Button>
          <Button
            className="submit"
            disabled={
              !selectedCategory ||
              !selectedScrapOrRework ||
              (selectedScrapOrRework === "REWORK" && !selectedSentBackStage)
            }
            onClick={() =>
              dispatch(
                handleRejectItems(
                  rowInfo.id,
                  selectedCategory,
                  selectedScrapOrRework.toLowerCase(),
                  selectedStage.id,
                  selectedSentBackStage,
                  description
                )
              ).then((res) => {
                if (!res.error) {
                  postRejectionSuccess({ ...rowInfo, rejected: 1 });
                  handleClose();
                }
              })
            }
          >
            {translate("Submit")}
            <FaArrowRight />
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default RejectionModal;
