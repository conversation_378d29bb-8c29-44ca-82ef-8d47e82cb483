@import "../../styles/colors.scss";

div.rejection-modal-wrapper {
  display: grid;
  grid-template-rows: 30px 1fr 30px;
  row-gap: 10px;

  border: 1px solid #333;
  background-color: #fff;
  width: 400px;
  padding-bottom: 10px;

  & > h2.title {
    margin: 0;
    padding: 0 0 0 10px;
    background-color: $fabProBlue;
    color: #fff;
    font-size: 1rem;
    line-height: 30px;
  }

  & > div.content {
    padding: 0 10px;

    display: grid;
    row-gap: 5px;

    & > label {
      display: flex;
      flex-direction: column;
      font-size: 0.8rem;

      & > div {
        & > textarea {
          height: 80px;
          font-size: 1rem;
        }

        & > div {
          height: 30px;

          & > select {
            height: 30px;
            font-size: 1rem;
          }
        }
      }

      & > p {
        margin: 0;
        padding: 0;
        font-size: 0.8rem;
        color: darken($red, 10%);
      }
    }
  }

  & > div.actions {
    display: grid;
    grid-template-columns: 10px 1fr 1fr 10px;
    column-gap: 10px;

    & > button {
      height: 30px;
      font-size: 1rem;
      padding: 0;
      border: 1px solid #333;
    }

    & > button.cancel {
      grid-column: 2/3;
      color: #333;
      background-color: #fff;

      &:hover {
        background-color: darken(#fff, 10%);
      }
    }

    & button.submit {
      grid-column: 3/4;
      color: #fff;
      background-color: $lightGreen;

      display: flex;
      justify-content: center;
      align-items: center;
      column-gap: 5px;
    }
  }
}
