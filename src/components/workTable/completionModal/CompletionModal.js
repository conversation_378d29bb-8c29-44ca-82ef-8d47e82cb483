// NPM PACKAGE IMPORTS
import React, { useState, useEffect } from "react";
import Button from "msuite_storybook/dist/button/Button";
import Modal from "msuite_storybook/dist/modal/Modal";
import { CgArrowRight, CgArrowLeft } from "react-icons/cg";

// STYLE IMPORTS
import "./stylesCompletionModal.scss";

const CompletionModal = ({
  open,
  handleClose,
  handleSubmit,
  selectedItem,
  selectedStage,
  completing,
}) => {
  const [isPending, setIsPending] = useState(false);

  useEffect(() => {
    setIsPending(selectedItem.drawing_pending_approval);
  }, [selectedItem]);

  return (
    <Modal open={open} handleClose={handleClose}>
      <div className="completion-modal">
        <h2 className="title">
          {selectedItem.completed ? "Unc" : "C"}omplete Item
        </h2>
        <p>
          {selectedItem.completed ? "Unc" : "C"}omplete{" "}
          <span className="selected-item">
            {selectedItem.material_name || selectedItem.drawing_name}
          </span>{" "}
          at <span className="selected-stage">{selectedStage.name}</span> or all
          available stages?
        </p>
        {isPending ? <span>Parent drawing is pending approval.</span> : ""}
        <Button className="cancel" onClick={handleClose} disabled={completing}>
          <CgArrowLeft />
          Cancel
        </Button>
        <Button
          className="selected"
          onClick={() => handleSubmit(false)}
          disabled={isPending || completing}
        >
          {selectedStage.name}
          <CgArrowRight />
        </Button>
        <Button
          className="all"
          onClick={() => handleSubmit(true, "UNCOMPLETE")}
          disabled={isPending || completing}
        >
          <CgArrowLeft />
          Uncomplete All Available
        </Button>
        <Button
          className="all"
          onClick={() => handleSubmit(true, "COMPLETE")}
          disabled={isPending || completing}
        >
          Complete All Available
          <CgArrowRight />
        </Button>
      </div>
    </Modal>
  );
};

export default CompletionModal;
