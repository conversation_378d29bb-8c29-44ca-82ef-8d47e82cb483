@import "../../styles/colors.scss";

div.completion-modal {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: 30px 1fr 30px 30px;
  row-gap: 10px;

  padding-bottom: 10px;
  background-color: #fff;

  & > h2.title {
    background-color: $fabProBlue;
    color: #fff;
    padding: 0 0 0 10px;
    margin: 0;
    font-size: 1rem;
    line-height: 30px;

    grid-column: 1/-1;
  }

  & > p,
  & > span {
    grid-column: 1/-1;

    padding: 10px;
    text-align: center;

    & > span.selected-item {
      color: $fabProBlue;
    }

    & > span.selected-stage {
      color: $lightGreen;
    }
  }

  & > span {
    color: $redWashDark;
  }

  & > button {
    padding: 0;
    height: 30px;
    margin: 0 5px;
    border: 1px solid #333;
    color: #333;
    font-size: 0.8rem;

    display: flex;
    align-items: center;
    justify-content: center;
    column-gap: 5px;

    &:nth-of-type(odd) {
      margin-left: 10px;
    }

    &:nth-of-type(even) {
      margin-right: 10px;
    }

    &.cancel:hover {
      background-color: darken(#fff, 10%);
    }

    &.all,
    &.selected {
      color: #fff;
    }

    &.all {
      background-color: $fabProBlue;

      &:not(:disabled):hover {
        background-color: darken($fabProBlue, 10%);
      }
    }

    &.selected {
      background-color: $lightGreen;

      &:not(:disabled):hover {
        background-color: darken($lightGreen, 10%);
      }
    }
  }
}
