@import "../../styles/colors.scss";
@import "../../styles/sizes.scss";

div.bom-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  z-index: 30;
  animation: fadein 0.2s linear;
  border-radius: 3px;
  box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);

  min-width: 400px;
  height: 815px;
  width: 90vw;
  background-color: $darkGrey;

  & header {
    padding: 0 20px;
    height: 40px;
    background-color: $blue;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
    font-size: 0.9rem;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;

    h4 {
      margin: 0;
      font-weight: normal;
    }
    & svg {
      font-size: 0.8rem;
      cursor: pointer;
    }
  }

  & div.main-content {
    padding: 20px;

    & label.quantity-label {
      color: $lighterSlate;
      margin-left: 30px;
      font-size: 0.9rem;

      & input {
        border-radius: 3px;
        border: none;
        outline: none;
        height: 28px;
        margin-left: 10px;
        padding-left: 5px;
      }
    }

    & div.info-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      & span.parent-title {
        color: $blue;
        font-weight: bold;
      }
      & button.grouped {
        background-color: $green;
        border: none;
        border-radius: 3px;
        outline: none;
        color: white;
        height: 28px;
        margin-left: 30px;
        cursor: pointer;

        &:disabled {
          cursor: default;
          background-color: darken($green, 10%);
        }
      }

      & div.table-export-wrapper {
        column-gap: 0 !important;
        grid-column-gap: 0 !important;
      }
    }

    & p {
      color: $red;
      text-align: center;
    }

    & .bom-table-wrapper {
      // max-height: 600px;
      margin-top: 20px;

      & .ag-center-cols-viewport {
        overflow-x: hidden;
      }
    }

    & input.grid-search {
      height: 28px;
      border-radius: 3px;
      border: none;
      outline: none;
      padding-left: 5px;
      margin-bottom: 10px;

      &:focus {
        border: none;
        outline: none;
      }
    }
  }

  div.ag-theme-balham-dark.custom-ag-styles {
    // height: 560px;
    // 40 modal title, 40 padding, 38 bom info, 40 search, 10 for modal outside
    height: calc(100vh - #{$headerFooter} - 40px - 40px - 38px - 40px - 10px);
    width: 100%;
    @supports (-webkit-touch-callout: none) {
      height: calc(
        100vh - #{$headerFooter} - #{$iosAddressBar} - 40px - 40px - 38px - 40px -
          10px
      );
    }

    & .ag-root-wrapper {
      // 815 full modal height, 40 modal title, 40 padding, 38 bom info, 40 search, 10 for modal outside
      height: calc(815px - 40px - 40px - 38px - 40px - 10px);
    }

    & .ag-center-cols-viewport {
      overflow-x: hidden;
    }

    & div.ag-cell {
      white-space: normal !important;
      min-height: 60px;
    }

    & div.ag-cell-inline-editing {
      height: 100%;
      padding: 15px 0;
      background-color: transparent;
      border: none;
      outline: none;
    }
  }
}

div.overlay {
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 19;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
}
