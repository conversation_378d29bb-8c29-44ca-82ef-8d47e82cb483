import { BOMColumnDefs } from "./bomConstants";

describe("Bill Of Materials", () => {
  const testMaterials = [
    {
      id: 693,
      name: "-PVDF",
      isogrid: 0,
      is_cut: 0,
      is_bomable: 0,
      standard_stock_length: null,
      rounding_rule_id: null,
      round_direction_id: 2,
    },
  ];
  const testLaydownLocations = [
    {
      id: 42,
      name: "test laydown",
      description: "",
      type: "Shop",
      type_id: null,
    },
  ];
  const testContainers = [
    {
      id: 1,
      name: "test cont",
      description: "",
      job_id: 1,
      job_name: "JK Test Data",
      job_number: "12345",
      laydown_location: null,
      created_by: 158,
      created_on: "2019-02-27T23:52:33.000Z",
      deleted_by: null,
      deleted_on: null,
      deleted: 0,
      locked: 0,
      location_name: null,
      location_id: null,
      drawing_ids: null,
      on_hold: 0,
      loaded_date: null,
      unavailable: 1,
    },
  ];

  const sortState = { sorting_column_name: "id", sorting_method: "asc" };
  const moreInfoClick = jest.fn();
  const togglePDFViewer = jest.fn();
  const heatNumbers = jest.fn();
  const handleLaydownLocations = jest.fn();
  const defaultHeaders = [
    "Job Name",
    "Job Number",
    "Drawing Name",
    "Tag #",
    "Laydown Location",
    "Material",
    "Size",
    "Length",
    "Heat #",
    "Container",
    "Vendor",
  ];

  describe("Jobs Column Defs", () => {
    const jobsHeaders = ["Package Name", "Package ID", "Package Number"];

    let populatedColumns;

    beforeEach(() => {
      populatedColumns = BOMColumnDefs(
        null,
        false,
        heatNumbers,
        testContainers,
        testLaydownLocations,
        testMaterials,
        "JOBS",
        moreInfoClick,
        togglePDFViewer,
        sortState
      );
    });

    it("Headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);

      expect(columnHeaders).toEqual([...jobsHeaders, ...defaultHeaders]);
    });

    describe("PACKAGE NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Package Name");
      });

      const params = {
        data: {
          package_name: "test package",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test package");
      });
    });

    describe("PACKAGE ID", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Package ID");
      });

      const params = {
        data: {
          package_id: 1,
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(1);
      });
    });

    describe("Drawing Name", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Drawing Name");
      });

      const params = {
        data: {
          drawing_name: "test drawing",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test drawing");
      });

      it("cellRendererParams", () => {
        expect(column.cellRendererParams()).toEqual({
          moreInfoClick,
          togglePDFViewer,
        });
      });

      it("value parser", () => {
        const newValueParams = {
          oldValue: "oldValue",
          newValue: "newValue",
          data: {
            name: "test",
            id: 1,
          },
        };
        const oldValueParams = {
          oldValue: "oldValue",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };
        expect(column.valueParser(oldValueParams)).toEqual("oldValue");
        expect(column.valueParser(newValueParams)).toEqual("newValue");
      });
    });

    describe("TAG #", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Tag #");
      });

      const params = {
        data: {
          tag_number: "100",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("100");
      });

      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });

    describe("LAYDOWN LOCATION", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Laydown Location"
        );
      });

      const params = {
        data: {
          laydown_location_name: "test laydown",
        },
        value: "test laydown",
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test laydown");
      });

      it("cellEditorParams", () => {
        expect(JSON.stringify(column.cellEditorParams(params))).toEqual(
          JSON.stringify({
            value: "test laydown",
            params,
            toggleModal: () =>
              handleLaydownLocations({
                ...params.data,
                _column: "laydown_location_name",
              }),
          })
        );
      });

      it("valueParser", () => {
        const newValueParams = {
          oldValue: "oldValue",
          newValue: "newValue",
          data: {
            name: "test",
            id: 1,
          },
        };
        const oldValueParams = {
          oldValue: "oldValue",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };
        expect(column.valueParser(oldValueParams)).toEqual(null);
        expect(column.valueParser(newValueParams)).toEqual("newValue");
      });
    });

    describe("MATERIAL", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Material");
      });

      const params = {
        data: {
          material_name: "test mat",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test mat");
      });

      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual("old");
      });

      it("cellEditorParams", () => {
        expect(column.cellEditorParams()).toEqual({ values: testMaterials });
      });
    });

    describe("SIZE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Size");
      });

      const params = {
        data: {
          size: "100",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("100");
      });

      it("valueParser", () => {
        const params = {
          oldValue: "12",
          newValue: "12 1/2",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "10",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("12 1/2");
        expect(column.valueParser(badParams)).toEqual("");
      });

      it("Comparator", () => {
        const values = ["10", "10.50", "8.50", "9", "3"];
        expect(values.sort(column.comparator)).toEqual([
          "3",
          "8.50",
          "9",
          "10",
          "10.50",
        ]);
      });
    });

    describe("LENGTH", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Length");
      });

      it("getQuickFilterText", () => {
        const params = {
          data: {
            length: {
              decimal: 10,
              display: "10",
            },
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("10");
      });

      it("valueFormatter", () => {
        const params = {
          data: {
            length: {
              decimal: 12.5,
              display: "12 1/2",
            },
          },
        };

        expect(column.valueFormatter(params)).toEqual("12 1/2");
      });

      if (
        ("valueFormatter",
        () => {
          const params = {
            value: `20"`,
          };

          expect(column.valueFormatter(params)).toEqual(`20"`);
        })
      )
        it("valueParser", () => {
          const params = {
            oldValue: {
              decimal: 10,
              display: `10"`,
            },
            newValue: `12"`,
            data: {
              name: "test",
              id: 1,
            },
          };
          const badParams = {
            oldValue: {
              decimal: 10,
              display: `10"`,
            },
            newValue: "",
            data: {
              name: "test",
              id: 1,
            },
          };

          expect(column.valueParser(params)).toEqual({
            decimal: 12,
            display: `12"`,
          });
          expect(column.valueParser(badParams)).toEqual({
            decimal: 0,
            display: "",
          });
        });

      it("Comparator", () => {
        const values = [
          {
            data: {
              length: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              length: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
          {
            data: {
              length: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
        ];
        expect(
          values.sort((a, b) => column.comparator(null, null, a, b))
        ).toEqual([
          {
            data: {
              length: {
                decimal: 1,
                display: `1"`,
              },
            },
          },
          {
            data: {
              length: {
                decimal: 5,
                display: `5"`,
              },
            },
          },
          {
            data: {
              length: {
                decimal: 10,
                display: `10"`,
              },
            },
          },
        ]);
      });
    });

    describe("HEAT #", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Heat #");
      });

      const params = {
        data: {
          heat_number: "test hn",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test hn");
      });

      it("cellEditorParams ", () => {
        const params = {
          data: { id: 1 },
          value: "test hn",
        };

        expect(JSON.stringify(column.cellEditorParams(params))).toEqual(
          JSON.stringify({
            value: "test hn",
            params,
            toggleModal: () =>
              heatNumbers({ ...params.data, _column: "heat_number" }),
          })
        );
      });
    });

    describe("CONTAINER", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Container");
      });

      const params = {
        data: {
          container_name: "test cont",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("test cont");
      });

      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });

      it("cellEditorParams ", () => {
        const params = {
          data: {
            job_id: 1,
          },
        };
        expect(column.cellEditorParams(params)).toEqual({
          values: ["test cont"],
        });
      });
    });

    describe("VENDOR", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Vendor");
      });

      const params = {
        data: {
          vendor: "100",
        },
      };

      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("100");
      });

      it("valueParser", () => {
        const params = {
          oldValue: "old",
          newValue: "new",
          data: {
            name: "test",
            id: 1,
          },
        };
        const badParams = {
          oldValue: "old",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };

        expect(column.valueParser(params)).toEqual("new");
        expect(column.valueParser(badParams)).toEqual(null);
      });
    });
  });

  describe("Packages Column Defs", () => {
    let populatedColumns;
    beforeEach(() => {
      populatedColumns = BOMColumnDefs(
        null,
        false,
        heatNumbers,
        testContainers,
        testLaydownLocations,
        testMaterials,
        "PACKAGES",
        moreInfoClick,
        togglePDFViewer,
        sortState
      );
    });

    it("Headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);

      expect(columnHeaders).toEqual(defaultHeaders);
    });
  });

  describe("Grouped Column Defs", () => {
    const defaultHeaders = [
      "Quantity",
      "Job Name",
      "Job Number",
      "Package Name",
      "Package ID",
      "Package Number",
      "Material",
      "Heat #",
      "Size",
      "Vendor",
    ];
    let populatedColumns;
    beforeEach(() => {
      populatedColumns = BOMColumnDefs(
        null,
        true,
        heatNumbers,
        testContainers,
        testLaydownLocations,
        testMaterials,
        "PACKAGES",
        moreInfoClick,
        togglePDFViewer,
        sortState
      );
    });

    it("Headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);

      expect(columnHeaders).toEqual(defaultHeaders);
    });
  });
});
