// NPM PACKAGE IMPORTS
import React, { useRef, useEffect, useState, useMemo } from "react";
import { FaTimes } from "react-icons/fa";
import { useSelector, useDispatch } from "react-redux";

// REDUX IMPORTS
import {
  handleFetchSortState,
  handleSaveSortState,
  handleFetchColumnState,
} from "../../../redux/generalActions";
import {
  handleFetchBOM,
  handleUpdateItems,
  handleUpdateGroupedItems,
  handleFetchMaterialTypes,
} from "../../items/itemsActions";
import {
  handleFetchContainers,
  handleFetchItemHeatNumbers,
} from "../../drawings/drawingsActions";
import { handleFetchLaydownLocations } from "../../shipping/shippingActions";
import { notify } from "../../reusable/alertPopup/alertPopupActions";

// COMPONENT IMPORTS
import AgTable from "../../reusable/agTable/AgTable";
import TableExportDropdown from "../../reusable/tableExportDropdown/TableExportDropdown";
import HeatNumberModal from "../heatNumberModal/HeatNumberModal";
import CreateSelectModal from "../../reusable/createSelectModal/CreateSelectModal";

// TRANSLATION IMPORTS
import useTranslations from "../../../hooks/useTranslations";
import jobsTranslations from "../../jobs/jobsTranslations.json";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../hooks/useOutsideClick";

// CONSTANTS IMPORTS
import { BOMColumnDefs, frameworkComponents } from "./bomConstants";
import { objectColumns, deepArraysAreEqual } from "../../../_utils";

// STYLES IMPORTS
import "./stylesBomModal.scss";

const BOMModal = ({
  showModal,
  onClose,
  rowInfo,
  currentTable,
  moreInfoClick,
  togglePDFViewer,
  setCurrentRowOrder,
  selectedFilterIds,
  setDisplayedPdf,
}) => {
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [selectedRow, setSelectedRow] = useState(null);
  const [isGrouped, toggleGrouped] = useState(false);
  const [showHeatNumberModal, toggleHeatNumberModal] = useState(false);
  const [updatedQuantity, setUpdatedQuantity] = useState(null);
  const [isEditing, toggleEditing] = useState(false);
  const [searchInput, setSearchInput] = useState("");
  const [rendering, toggleRendering] = useState(false);
  const [showLaydownLocationsModal, toggleLaydownLocationsModal] = useState(
    false
  );

  const { bom, materialTypes } = useSelector((state) => state.itemsData);
  let bomColumnState = isGrouped
    ? JSON.parse(localStorage.getItem("groupedBOMColumnState"))
    : JSON.parse(localStorage.getItem("ungroupedBOMColumnState"));
  const { containers, heatNumbers } = useSelector(
    (state) => state.drawingsData
  );
  const { laydown_locations } = useSelector((state) => state.shippingData);
  const { sortState } = useSelector((state) => state.generalData);

  const wrapperRef = useRef(null);
  const quantityRef = useRef(null);

  const dispatch = useDispatch();

  const translate = useTranslations(jobsTranslations);

  useOutsideClick(wrapperRef, onClose);

  useEffect(() => {
    dispatch(handleFetchColumnState("BOM", null));
    dispatch(handleFetchLaydownLocations());
    dispatch(handleFetchMaterialTypes());
    dispatch(handleFetchContainers());
  }, [dispatch]);

  useEffect(() => {
    if (localStorage.getItem("groupedBOMColumnState") === null)
      localStorage.setItem("groupedBOMColumnState", "{}");
    if (localStorage.getItem("ungroupedBOMColumnState") === null)
      localStorage.setItem("ungroupedBOMColumnState", "{}");
  }, []);

  useEffect(() => {
    if (!gridOptionsApi) return;
    setCurrentRowOrder(bom);
    gridOptionsApi.setRowData(bom);
  }, [bom, gridOptionsApi, setCurrentRowOrder]);

  const getRowId = (data) => {
    // NOTE - BOM is shown on multiple levels, therefore, we need to get the correct id...
    // if the type_id is not available it is because it is the original record where id = type_id; not item level yet
    return currentTable === "JOBS"
      ? data.job_id ?? data.id
      : currentTable === "PACKAGES"
      ? data.package_id ?? data.id
      : currentTable === "DRAWINGS"
      ? data.drawing_id ?? data.id
      : data.id;
  };

  const showHeatNumberDropdown = useMemo(() => {
    if (
      !selectedRow ||
      selectedRow.size === null ||
      selectedRow.material_name === null
    )
      return false;
    else return true;
  }, [selectedRow]);

  useEffect(() => {
    toggleRendering(true);
    dispatch(handleFetchSortState("BOM", isGrouped ? 2 : 1));
    setTimeout(() => toggleRendering(false), 50);
  }, [dispatch, isGrouped]);

  useEffect(() => {
    if (rowInfo) {
      dispatch(handleFetchBOM([getRowId(rowInfo)], currentTable, isGrouped));

      if (showHeatNumberDropdown)
        dispatch(
          handleFetchItemHeatNumbers(
            [rowInfo.job_id],
            rowInfo.size,
            rowInfo.material_name
          )
        );
    }
  }, [isGrouped]);

  const refreshData = () => {
    dispatch(handleFetchBOM([getRowId(rowInfo)], currentTable, isGrouped));
    onRowEditingStopped();
  };

  const itemName = useMemo(() => {
    if (!rowInfo) return null;
    switch (currentTable) {
      case "JOBS":
        return rowInfo.job_name;
      case "PACKAGES":
        return rowInfo.package_name;
      case "DRAWINGS":
        return rowInfo.name;
      default:
        return null;
    }
  }, [rowInfo, currentTable]);

  const populateBreadcrumbTitle = () => {
    // ToDo - Update API returns to have consistent field names!!!!
    // the values are returned differently between the JOB vs ITEM API data... (job.prop ?? item.prop)
    if (currentTable === "JOBS") return rowInfo.job_title ?? rowInfo.job_name;
    if (currentTable === "PACKAGES")
      // yay packages data always matches the item data...
      return `${rowInfo.job_name} > ${rowInfo.package_name}`;
    if (currentTable === "DRAWINGS")
      // the values are returned differently between the DRAWING vs ITEM API data... (drawing.prop ?? item.prop)
      return `${rowInfo.job_title ?? rowInfo.job_name} > ${
        rowInfo.package_name
      } > ${rowInfo.name ?? rowInfo.drawing_name}`;
  };

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
  };

  const onSortChanged = (params) => {
    params.api.redrawRows();
    const sortedColumn = params.columnApi.getAllColumns().find((c) => c.sort);

    dispatch(
      handleSaveSortState(
        sortedColumn ? sortedColumn.colId : null,
        sortedColumn ? sortedColumn.sort : null,
        "BOM",
        isGrouped ? 2 : 1
      )
    );
  };
  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };

  const displayNotification = (message) => {
    dispatch(
      notify({
        id: Date.now(),
        type: "ERROR",
        message,
      })
    );
  };
  const permissionLockedColumns = useMemo(() => {
    let materialNames = [];
    if (materialTypes) materialNames = materialTypes.map((m) => m.name);
    if (sortState && containers && materialTypes && laydown_locations) {
      return BOMColumnDefs(
        bomColumnState,
        isGrouped,
        (r) => {
          toggleHeatNumberModal(true);
          setSelectedRow(r);
        },
        containers,
        laydown_locations || [],
        materialNames,
        currentTable,
        moreInfoClick,
        togglePDFViewer,
        sortState,
        displayNotification,
        (r) => {
          setSelectedRow(r);
          toggleLaydownLocationsModal(true);
        },
        setDisplayedPdf
      );
    }
  }, [
    isGrouped,
    bomColumnState,
    sortState,
    materialTypes,
    laydown_locations,
    containers,
  ]);

  useEffect(() => {
    if (!gridOptionsApi) return;
    gridOptionsApi.setQuickFilter(searchInput);
    gridOptionsApi.redrawRows();
  }, [searchInput, gridOptionsApi]);

  const onCellValueChanged = (params) => {
    if (!params.newValue && !params.oldValue) return;

    if (params.newValue !== params.oldValue) {
      const property = (field) => {
        switch (field) {
          case "container_name":
            return "shipping_container_id";
          case "laydown_location_name":
            return "laydown_location_id";
          case "material_name":
            return "material_type_id";
          default:
            return field;
        }
      };

      const value = (field) => {
        if (objectColumns.includes(field)) return params.newValue.decimal;

        switch (field) {
          case "container_name":
            if (params.newValue === "") return null;
            return containers.find((c) => c.name === params.newValue).id || {};
          case "material_name":
            if (params.newValue === "") return null;
            return (
              materialTypes.find((m) => m.name === params.newValue).id || {}
            );
          case "laydown_location_name":
            if (params.newValue === "") return null;
            return (
              (laydown_locations || []).find(
                (l) => l.name === params.newValue
              ) || {}
            ).id;
          default:
            return params.newValue || null;
        }
      };

      if (
        ["material_type_id"].includes(property(params.colDef.field)) &&
        !value(params.colDef.field)
      )
        return;

      if (objectColumns.includes(params.colDef.field)) {
        const newValue = params.newValue,
          oldValue = params.oldValue;

        if (
          newValue.decimal === oldValue.decimal ||
          newValue.display === oldValue.display
        )
          return;
      }

      if (property(params.colDef.field) === "material_type_id") {
        // let user know the item may "disappear" from this view
        dispatch(
          notify({
            id: Date.now(),
            type: "WARN",
            message:
              "If selected material is not bommable, the item will no longer be visible in this view",
          })
        );
      }

      if (isGrouped) {
        dispatch(
          handleUpdateGroupedItems(
            params.data.work_item_ids,
            {
              [property(params.colDef.field)]:
                value(params.colDef.field) || null,
            },
            parseFloat(quantityRef.current)
          )
        ).then((res) => {
          if (
            parseInt(quantityRef.current) !== parseInt(params.data.quantity) ||
            params.colDef.field === "heat_number"
          )
            refreshData();
        });
      } else {
        dispatch(
          handleUpdateItems([params.data.id], {
            [property(params.colDef.field)]: value(params.colDef.field) || null,
          })
        ).then((res) => {
          if (!res.error) {
            const rowNode = params.api.getRowNode(params.data.id);
            rowNode && rowNode.setData(res[0]);
          }
        });
      }
    }
  };

  const onRowEditingStarted = (params) => {
    toggleEditing(true);
    setSelectedRow(params.data);
  };
  const onRowEditingStopped = (params) => {
    toggleEditing(false);
    setUpdatedQuantity(null);
    quantityRef.current = null;
    // unset selected row to not trigger heat number fetch on grouping change
    setSelectedRow(null);
  };

  useEffect(() => {
    if (isGrouped && selectedRow) {
      if (!selectedRow.length) {
        setUpdatedQuantity(selectedRow.quantity || "");
        quantityRef.current = selectedRow.quantity || "";
      } else if (selectedRow.length && selectedRow.quantity) {
        setUpdatedQuantity(selectedRow.quantity.toFixed(1) || "");
        quantityRef.current = selectedRow.quantity.toFixed(1) || "";
      }
    }
  }, [selectedRow, isGrouped]);

  const onFilterChanged = (params) => {
    params.api.redrawRows();
  };

  const gridOptions = {
    rowData: bom,
    columnDefs: permissionLockedColumns,
    frameworkComponents: frameworkComponents,
    reactNext: true,
    rowClassRules,
    onSortChanged,
    onFilterChanged,
    onDisplayedColumnsChanged: (params) => {
      const localStorageKey = isGrouped
        ? "groupedBOMColumnState"
        : "ungroupedBOMColumnState";
      const newColumnState = params.columnApi
        .getAllColumns()
        .sort((a, b) => {
          return a.left - b.left;
        })
        .map((c, idx) => ({
          header_name: c.colDef.headerName,
          pinned: c.pinned,
          visible: c.visible ? 1 : 0,
          position: idx,
        }))
        .filter((c) => c.header_name !== "");
      // This craziness is needed because a.left is null when the column is hidden
      // So to prevent all hidden columns from snapping to the 1st column, we have to find the original position
      // from when the column was still active, if its not anymore
      const currentValues =
        JSON.parse(localStorage.getItem(localStorageKey)) || [];
      newColumnState.forEach((c) => {
        if (!c.visible) {
          const oldColumnState = currentValues.find(
            (x) => x.header_name === c.header_name
          );
          if (oldColumnState) c.position = oldColumnState.position;
        }
      });
      localStorage.setItem(localStorageKey, JSON.stringify(newColumnState));
    },
    onGridReady,
    editType: "fullRow",
    pagination: false,
    tabToNextCell: null,
    defaultColDef: {
      cellClass: ["no-border", "custom-wrap"],
      wrapText: true,
      autoHeight: true,
    },
    onRowEditingStarted: onRowEditingStarted,
    onRowEditingStopped: onRowEditingStopped,
    getRowNodeId: (data) => data.id,
    suppressRowClickSelection: true,
    onCellValueChanged,
  };

  const exportParams = useMemo(() => {
    if (!permissionLockedColumns || !permissionLockedColumns.length) return;

    return {
      fileName: `${itemName}_bill_of_materials`.replace(/\./g, " "),
      processCellCallback: (params) => {
        const { colId } = params.column;

        if (!params.value) return "";

        if (colId === "length") return params.node.data.length.display;

        if (typeof params.value === "number" && colId !== "tag_number") {
          return params.value.toFixed(2);
        } else return params.value;
      },
    };
  }, [permissionLockedColumns, itemName]);

  const handleExcelExport = () => {
    if (gridOptionsApi) gridOptionsApi.exportDataAsExcel(exportParams);
  };
  const handleCsvExport = () => {
    if (gridOptionsApi) gridOptionsApi.exportDataAsCsv(exportParams);
  };

  return showModal ? (
    <>
      <div className="bom-modal" ref={wrapperRef}>
        <header>
          <h4>Bill of Materials</h4>
          <FaTimes onClick={onClose} />
        </header>
        <div className="main-content">
          <div className="info-row">
            <div className="">
              <span className="parent-title">{populateBreadcrumbTitle()}</span>
              <button
                className="grouped"
                onClick={() => toggleGrouped(!isGrouped)}
                disabled={isEditing}
              >
                {isGrouped ? "View Ungrouped" : "View Grouped"}
              </button>
            </div>
            <TableExportDropdown
              handleCSV={handleCsvExport}
              handleExcel={handleExcelExport}
            />
          </div>
          <input
            placeholder="Search table"
            className="grid-search"
            type="text"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
          />
          {isEditing && isGrouped && (
            <label className="quantity-label">
              Qty/Length to update
              <input
                onChange={(e) => {
                  setUpdatedQuantity(e.target.value);
                  quantityRef.current = e.target.value;
                }}
                value={updatedQuantity}
                maximum={updatedQuantity}
                className="quantity-input"
                type="number"
              />
            </label>
          )}
          <div className="content-wrapper">
            {rendering || !permissionLockedColumns ? (
              <></>
            ) : (
              <AgTable gridOptions={gridOptions} />
            )}
          </div>
        </div>
        {showHeatNumberModal && (
          <HeatNumberModal
            open={showHeatNumberModal}
            handleClose={() => toggleHeatNumberModal(false)}
            rowInfo={selectedRow}
            heatNumbers={heatNumbers}
            gridOptionsApi={gridOptionsApi}
            translate={translate}
            isGrouped={isGrouped}
            quantity={updatedQuantity}
            callback={refreshData}
          />
        )}
        {showLaydownLocationsModal && (
          <CreateSelectModal
            items={laydown_locations}
            rowInfo={selectedRow}
            open={showLaydownLocationsModal}
            handleClose={() => toggleLaydownLocationsModal(false)}
            gridOptionsApi={gridOptionsApi}
            isGrouped={isGrouped}
            tableType="BOM"
            handleUpdateItem={handleUpdateItems}
            idKey="laydown_location_id"
            nameKey="laydown_location_name"
            selectedFilterIds={selectedFilterIds}
          />
        )}
      </div>
      <div className="overlay" />
    </>
  ) : (
    <></>
  );
};

export default BOMModal;
