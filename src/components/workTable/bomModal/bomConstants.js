// STYLE IMPORTS
import "../../styles/tables.scss";

// COMPONENT IMPORTS
import TextInput<PERSON><PERSON><PERSON>enderer from "../../reusable/frameworkComponents/TextInputCellRenderer";
import NumberInputCellRenderer from "../../reusable/frameworkComponents/NumberInputCellRenderer";
import DropdownEditorRenderer from "../../reusable/frameworkComponents/DropdownEditorRenderer";
import DropdownInputEditorRenderer from "../../reusable/frameworkComponents/DropdownInputEditorRenderer";
import FractionalEditorRenderer from "../../reusable/frameworkComponents/FractionalEditorRenderer";
import drawingNameCellRenderer from "../../reusable/frameworkComponents/DrawingNameCellRenderer";

// HELPER FUNCTION IMPORTS
import {
  convertFracToDec,
  fractionalRegex,
  multidimensionOnlyX,
  multidimensionRegex,
  naturalSort,
} from "../../../_utils";

// EXPORTS
export const BOMColumnDefs = (
  savedColumnState,
  isGrouped,
  heatNumbers = (f) => f,
  containers = [],
  laydownLocations = [],
  materials = [],
  currentTable,
  moreInfoClick,
  togglePDFViewer,
  sortState,
  displayNotification,
  handleLaydownLocations,
  setDisplayedPdf
) => {
  const jobColumns = [
    {
      headerName: "Job Name",
      field: "job_name",
      getQuickFilterText: (params) => params.data.job_name,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "job_name",
      autoHeight: true,
      minWidth: 100,
      sort:
        sortState.sorting_column_name === "job_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Job Number",
      field: "job_number",
      getQuickFilterText: (params) => params.data.job_number,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "job_number",
      autoHeight: true,
      minWidth: 80,
      sort:
        sortState.sorting_column_name === "job_number"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
  ];

  const packageColumns = [
    {
      headerName: "Package Name",
      field: "package_name",
      getQuickFilterText: (params) => params.data.package_name,
      minWidth: 100,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_name",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "package_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Package ID",
      field: "package_id",
      getQuickFilterText: (params) => params.data.package_id,
      minWidth: 80,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_id",
      sort:
        sortState.sorting_column_name === "package_id"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Package Number",
      field: "package_number",
      getQuickFilterText: (params) => params.data.package_number,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_number",
      sort:
        sortState.sorting_column_name === "package_number"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
  ];

  if (!isGrouped) {
    let columns = [
      ...jobColumns,
      {
        headerName: "Drawing Name",
        field: "drawing_name",
        minWidth: 80,
        getQuickFilterText: (params) => params.data.drawing_name,
        valueParser: (params) => {
          if (params.newValue === "" || !params.newValue)
            return params.oldValue;

          return params.newValue;
        },
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "drawing_name",
        autoHeight: true,
        cellRendererParams: (params) => {
          return {
            moreInfoClick,
            togglePDFViewer,
            setDisplayedPdf,
          };
        },
        cellRenderer: "drawingNameCellRenderer",
        sort:
          sortState.sorting_column_name === "drawing_name"
            ? sortState.sorting_method
            : null,
        comparator: (valueA, valueB) =>
          valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
      },
      {
        headerName: "Tag #",
        field: "tag_number",
        minWidth: 80,
        editable: true,
        getQuickFilterText: (params) => params.data.tag_number,
        valueParser: (params) => {
          if (params.newValue === "" || !params.newValue) return null;

          return params.newValue;
        },
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "tag_number",
        sort:
          sortState.sorting_column_name === "tag_number"
            ? sortState.sorting_method
            : null,
        comparator: (valueA, valueB) =>
          valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
      },
      {
        headerName: "Laydown Location",
        field: "laydown_location_name",
        minWidth: 100,
        editable: true,
        cellEditorParams: (params) => ({
          value: params.value,
          params,
          toggleModal: () => {
            handleLaydownLocations({
              ...params.data,
              _column: "laydown_location_name",
            });
          },
        }),
        valueParser: (params) => {
          if (params.newValue === "" || params.newValue === undefined) {
            return null;
          }

          return params.newValue;
        },
        cellEditor: "dropdownInputEditorRenderer",
        getQuickFilterText: (params) => params.data.laydown_location_name,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "laydown_location_name",
        autoHeight: true,
        sort:
          sortState.sorting_column_name === "laydown_location_name"
            ? sortState.sorting_method
            : null,
        comparator: (valueA, valueB) =>
          valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
      },
      {
        headerName: "Material",
        field: "material_name",
        minWidth: 100,
        valueParser: (params) => {
          if (params.newValue === "" || params.newValue === undefined)
            return params.oldValue;

          return params.newValue;
        },
        editable: true,
        cellEditor: "dropdownEditorRenderer",
        cellEditorParams: (params) => {
          return {
            values: materials,
          };
        },
        getQuickFilterText: (params) => params.data.material_name,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "material_name",
        autoHeight: true,
        sort:
          sortState.sorting_column_name === "material_name"
            ? sortState.sorting_method
            : null,
        comparator: (valueA, valueB) =>
          valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
      },
      {
        headerName: "Size",
        field: "size",
        minWidth: 60,
        valueParser: (params) => {
          if (params.newValue === "" || params.newValue === undefined)
            return params.oldValue === null ? params.oldValue : "";
          if (
            !multidimensionRegex.test(params.newValue) ||
            !multidimensionOnlyX.test(params.newValue)
          ) {
            displayNotification(
              "Value not saved due to invalid format. Please provide a valid fraction."
            );
            return params.oldValue;
          }

          return params.newValue;
        },
        getQuickFilterText: (params) => params.data.size,
        editable: true,
        comparator: (valueA, valueB) => {
          if (!valueA) return -1;
          if (!valueB) return 1;
          return valueA.localeCompare(valueB, undefined, {
            numeric: true,
            sensitivity: "base",
          });
        },
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "size",
        sort:
          sortState.sorting_column_name === "size"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "Length",
        field: "length",
        filterValueGetter: (params) => {
          return params.data?.length ? params.data.length.display : "";
        },
        valueFormatter: (params) =>
          params.data.length ? params.data.length.display : "",
        valueParser: (params) => {
          if (params.newValue === "" || params.newValue === undefined)
            return params.oldValue === null
              ? params.oldValue &&
                  params.oldValue.decimal &&
                  params.oldValue.decimal === 0
              : params.oldValue.decimal === null
              ? { decimal: null, display: null }
              : { decimal: 0, display: "" };
          if (params.oldValue === convertFracToDec(params.newValue))
            return params.oldValue;

          if (!fractionalRegex.test(params.newValue)) {
            displayNotification(
              `Value not saved due to invalid format. Please provide a valid fraction (ex. 2'-1/2")`
            );
            return params.oldValue;
          }

          return {
            decimal: convertFracToDec(params.newValue),
            display: params.newValue,
          };
        },
        comparator: (valueA, valueB, nodeA, nodeB) => {
          const nodeAValue = nodeA.data.length ? nodeA.data.length.decimal : 0;
          const nodeBValue = nodeB.data.length ? nodeB.data.length.decimal : 0;
          return nodeAValue - nodeBValue;
        },
        getQuickFilterText: (params) => {
          if (params.data.length) {
            return params.data.length.display;
          } else return 0;
        },
        minWidth: 100,
        editable: true,
        cellEditor: "fractionalEditorRenderer",
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "length",
        sort:
          sortState.sorting_column_name === "length"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "Heat #",
        field: "heat_number",
        valueParser: (params) => {
          if (params.newValue === "" || params.newValue === undefined)
            return null;

          return params.newValue;
        },
        minWidth: 80,
        editable: true,
        cellEditor: "dropdownInputEditorRenderer",
        cellEditorParams: (params) => ({
          value: params.value,
          params,
          toggleModal: () =>
            heatNumbers({ ...params.data, _column: "heat_number" }),
        }),
        getQuickFilterText: (params) => params.data.heat_number,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "heat_number",
        sort:
          sortState.sorting_column_name === "heat_number"
            ? sortState.sorting_method
            : null,
        comparator: (valueA, valueB) =>
          valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
      },
      {
        headerName: "Container",
        field: "container_name",
        minWidth: 100,
        editable: true,
        cellEditor: "dropdownEditorRenderer",
        cellEditorParams: (params) => {
          if (!containers)
            return {
              values: [],
            };
          return {
            values: containers
              .filter((c) => c.job_id === params.data.job_id)
              .map((c) => c.name),
          };
        },
        valueParser: (params) => {
          if (params.newValue === "" || params.newValue === undefined)
            return null;

          return params.newValue;
        },
        getQuickFilterText: (params) => params.data.container_name,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "container_name",
        autoHeight: true,
        sort:
          sortState.sorting_column_name === "container_name"
            ? sortState.sorting_method
            : null,
        comparator: (valueA, valueB) =>
          valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
      },
      {
        headerName: "Vendor",
        field: "vendor",
        getQuickFilterText: (params) => params.data.vendor,
        minWidth: 100,
        width: 120,
        editable: true,
        valueParser: (params) => {
          if (params.newValue === "" || params.newValue === undefined)
            return null;

          return params.newValue;
        },
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "vendor",
        autoHeight: true,
        sort:
          sortState.sorting_column_name === "vendor"
            ? sortState.sorting_method
            : null,
        comparator: (valueA, valueB) =>
          valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
      },
    ];

    if (currentTable === "JOBS") {
      columns.unshift(...packageColumns);
    }

    if (savedColumnState && savedColumnState.length) {
      let result = [];

      for (let i = 0; i < columns.length; i++) {
        let savedDef = savedColumnState.find(
          (c) => c.header_name === columns[i].headerName
        );

        if (savedDef) {
          result.push({
            ...columns[i],
            pinned: savedDef.pinned,
            hide: savedDef.visible ? false : true,
            position: savedDef.position || null,
          });
        } else result.push(columns[i]);
      }
      result = result.sort((a, b) => {
        if (a.position === b.position) {
          if (a.headerName.toLowerCase() > b.headerName.toLowerCase()) return 1;
          else return -1;
        } else return a.position - b.position;
      });
      return result;
    } else return columns;
  } else {
    let columns = [
      {
        headerName: "Quantity",
        field: "quantity",
        valueFormatter: (params) => {
          if (params.value && params.data.is_cut === 1) {
            return params.value.toFixed(2);
          } else return params.value;
        },
        minWidth: 90,
        width: 90,
        getQuickFilterText: (params) => params.data.quantity,
        filter: "agNumberColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "quantity",
        pinned: "left",
        sort:
          sortState.sorting_column_name === "quantity"
            ? sortState.sorting_method
            : null,
      },
      ...jobColumns,
      ...packageColumns,
      {
        headerName: "Material",
        field: "material_name",
        valueParser: (params) => {
          if (params.newValue === "" || params.newValue === undefined)
            return params.oldValue;

          return params.newValue;
        },
        editable: true,
        cellEditor: "dropdownEditorRenderer",
        cellEditorParams: (params) => {
          return {
            values: materials,
          };
        },
        minWidth: 80,
        getQuickFilterText: (params) => params.data.material_name,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "material_name",
        autoHeight: true,
        sort:
          sortState.sorting_column_name === "material_name"
            ? sortState.sorting_method
            : null,
        comparator: (valueA, valueB) =>
          valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
      },
      {
        headerName: "Heat #",
        field: "heat_number",
        valueParser: (params) => {
          if (params.newValue === "" || params.newValue === undefined)
            return null;

          return params.newValue;
        },
        editable: true,
        cellEditor: "dropdownInputEditorRenderer",
        cellEditorParams: (params) => ({
          value: params.value,
          params,
          toggleModal: () =>
            heatNumbers({ ...params.data, _column: "heat_number" }),
        }),
        minWidth: 80,
        getQuickFilterText: (params) => params.data.heat_number,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "heat_number",
        sort:
          sortState.sorting_column_name === "heat_number"
            ? sortState.sorting_method
            : null,
        comparator: (valueA, valueB) =>
          valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
      },
      {
        headerName: "Size",
        field: "size",
        valueParser: (params) => {
          if (params.newValue === "" || params.newValue === undefined)
            return params.oldValue === null ? params.oldValue : "";
          if (
            !multidimensionRegex.test(params.newValue) ||
            !multidimensionOnlyX.test(params.newValue)
          )
            return params.oldValue;

          return params.newValue;
        },
        minWidth: 60,
        getQuickFilterText: (params) => params.data.size,
        editable: true,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "size",
        sort:
          sortState.sorting_column_name === "size"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "Vendor",
        field: "vendor",
        getQuickFilterText: (params) => params.data.vendor,
        editable: true,
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
        },
        menuTabs: ["filterMenuTab"],
        colId: "vendor",
        autoHeight: true,
        minWidth: 80,
        sort:
          sortState.sorting_column_name === "vendor"
            ? sortState.sorting_method
            : null,
        comparator: (valueA, valueB) =>
          valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
      },
    ];
    if (savedColumnState && savedColumnState.length) {
      let result = [];

      for (let i = 0; i < columns.length; i++) {
        let savedDef = savedColumnState.find(
          (c) => c.header_name === columns[i].headerName
        );

        if (savedDef) {
          result.push({
            ...columns[i],
            pinned: savedDef.pinned,
            hide: savedDef.visible ? false : true,
            position: savedDef.position || null,
          });
        } else result.push(columns[i]);
      }
      result = result.sort((a, b) => {
        if (a.position === b.position) {
          if (a.headerName.toLowerCase() > b.headerName.toLowerCase()) return 1;
          else return -1;
        } else return a.position - b.position;
      });
      return result;
    } else return columns;
  }
};

export const frameworkComponents = {
  textInputCellRenderer: TextInputCellRenderer,
  numberInputCellRenderer: NumberInputCellRenderer,
  dropdownEditorRenderer: DropdownEditorRenderer,
  dropdownInputEditorRenderer: DropdownInputEditorRenderer,
  fractionalEditorRenderer: FractionalEditorRenderer,
  drawingNameCellRenderer: drawingNameCellRenderer,
};
