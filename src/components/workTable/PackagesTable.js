// NPM PACKAGE IMPORTS
import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
} from "react";
import { v4 as uuid } from "uuid";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import { handleFetchDrawings } from "../drawings/drawingsActions";
import { handleChangeItemWorkflow } from "../flows/flowsActions";

// COMPONENT IMPORTS
import AgTable from "../reusable/agTable/AgTable";
import TableContainer from "../reusable/tableContainer/TableContainer";
import WorkflowModal from "../reusable/workflowModal/WorkflowModal";
import InfiniteScrollTable from "../reusable/infiniteScrollTable/InfiniteScrollTable";

// CONSTANTS IMPORTS
import { frameworkComponents, generateTabs } from "../jobs/jobsConstants";
import { packagesColumnDefs } from "../packages/packagesConstants";
import { handleUpdatePackageNoRefresh } from "../packages/packagesActions";

// HELPER FUNCTION IMPORTS
import {
  permissionLock,
  onDisplayedColumnsChanged,
  generateTime,
  escapeRegExp,
} from "../../_utils";
import {
  sortAndFilter,
  associateCustomColumnData,
  searchTable,
  columnsToExcludeFromGlobalSearch,
} from "../../utils/agTable";
import {
  fetchCustomColumnDataMultiItem,
  fetchCustomColumnDataPerItem,
} from "../../_services";
import {
  handleEnqueueSpinner,
  handleDequeueSpinner,
} from "../reusable/generalSpinner/generalSpinnerActions";
import usePrevious from "../../hooks/usePrevious";
import axios from "axios";

const CUSTOM_COLUMNS_FEATURE_ID = 53;

// EXPORTS
const PackagesTable = ({
  selectedPackages,
  showAllWork,
  toggleShowAllWork,
  setStatusTrackerPopup,
  toggleMoreInfo,
  moreInfoClick,
  rowInfo,
  showPriorities,
  setSelectedPackages,
  setFile,
  selectedRows,
  setSelectedRows,
  setRowInfo,
  showWorkflowModal,
  toggleWorkflowModal,
  showScroll,
  handleSaveSortState,
  sortState,
  showBOM,
  storePackagesGridOptionsApi,
  onPackagesCellValueChanged,
  onCellEditingStarted,
  handleDrawingFilterSelection,
  customColumns,
}) => {
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [searchInput, setSearchInput] = useState("");
  const [totalRowsCount, setTotalRowsCount] = useState("");
  const [columnApi, setColumnApi] = useState(null);

  const dispatch = useDispatch();

  const {
    isLoading: isPackagesLoading,
    packages,
    savedColumnState,
  } = useSelector((state) => state.packagesData);
  const { flows } = useSelector((state) => state.flowsData);
  const { features, token } = useSelector((state) => state.profileData);
  const { drawings } = useSelector((state) => state.drawingsData);

  const hasCustomColumnFeature = features?.includes(CUSTOM_COLUMNS_FEATURE_ID);

  // whenever the currentTable changes, refresh table
  useEffect(() => {
    if (!gridOptionsApi) return;
    selectedPackagesRef.current = selectedPackages;
    refreshTable();
  }, [gridOptionsApi, selectedPackages, dispatch, showAllWork, packages]);

  useEffect(() => {
    if (selectedPackages?.length) {
      displayedPackagesRef.current = selectedPackages;
    } else displayedPackagesRef.current = packages;
  }, [selectedPackages, packages]);

  // TODO - not working as expected
  const changeWorkflowSubmit = useCallback(
    (newWorkflow) => {
      dispatch(
        handleChangeItemWorkflow(
          rowInfo.id,
          rowInfo.name ? "drawing" : "package",
          newWorkflow
        )
      ).then((res) => {
        if (!res.error) {
          const newRowInfo = { ...rowInfo, work_flow_id: newWorkflow };

          if (
            !rowInfo.work_flow_names ||
            rowInfo.work_flow_id !== newWorkflow
          ) {
            newRowInfo.work_flow_names = flows.find(
              (f) => f.id === newWorkflow
            ).name;
          }
          const currentNode = gridOptionsApi.getRowNode(newRowInfo.id);
          if (currentNode) {
            currentNode.setData(newRowInfo);
            gridOptionsApi.refreshCells({
              rowNodes: [currentNode],
              force: true,
            });
          }
          setRowInfo(newRowInfo);
          toggleWorkflowModal(false);
          setSelectedPackages((prevValue) => {
            let newValue = prevValue;
            if (newValue.length) {
              const index = newValue.findIndex((k) => k.id === newRowInfo.id);
              newValue[index] = { ...newRowInfo };
              return newValue;
            }
          });
          drawings.map((k) => {
            if (k.package_id === newRowInfo.id) {
              k["work_flow_id"] = newRowInfo["work_flow_id"];
              k["work_flow_name"] = newRowInfo["work_flow_names"];
            }
            return k;
          });
          dispatch(handleUpdatePackageNoRefresh(packages, newRowInfo));
        }
      });
    },
    [rowInfo]
  );

  const handleChildSelection = async (type, rowData) => {
    setSelectedPackages([rowData]);

    await dispatch(handleFetchDrawings([rowData.id], showAllWork)).then(
      async (res) => {
        if (res.error) return;
        await handleDrawingFilterSelection(res, [rowData]);
      }
    );
  };

  const refreshTable = () => {
    if (selectedPackages.length && !showPriorities) {
      hasCustomColumnFeature
        ? gridOptionsApi.refreshInfiniteCache()
        : gridOptionsApi.setRowData(selectedPackages || []);

      // Remove the filtered out items from the selection if there is any there
      if (hasCustomColumnFeature) {
        setSelectedRows((prevState) => {
          const selectedPackageIds = selectedPackages.map((o) => o.id);
          return [
            ...prevState.filter((item) => selectedPackageIds.includes(item.id)),
          ];
        });
      }

      // dont want to refetch drawings (that happens in priorities modal)
      if (showPriorities || isPackagesLoading) return;
    } else {
      hasCustomColumnFeature
        ? gridOptionsApi.refreshInfiniteCache()
        : gridOptionsApi.setRowData(packages);
    }

    !hasCustomColumnFeature &&
      gridOptionsApi.forEachNode((n) => {
        if (selectedRows.find((r) => r.id === n.id)) n.setSelected(true);
      });
  };

  const permissionLockedColumns = useMemo(() => {
    if (savedColumnState)
      return permissionLock(
        packagesColumnDefs(
          savedColumnState,
          moreInfoClick,
          toggleMoreInfo,
          handleChildSelection,
          sortState,
          null,
          customColumns
        )
      );
    else return null;
  }, [savedColumnState]);

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
    storePackagesGridOptionsApi(params.api);
    setColumnApi(params.columnApi);
  };
  const onSortChanged = (params) => {
    const sortedColumn = params.columnApi.getAllColumns().find((c) => c.sort);

    dispatch(
      handleSaveSortState(
        sortedColumn ? sortedColumn.colId : null,
        sortedColumn ? sortedColumn.sort : null,
        "PACKAGES"
      )
    );
  };
  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };
  const onSelectionChanged = (params) => {
    let rows = params.api.getSelectedRows();
    return setSelectedRows(rows);
  };

  const gridOptions = {
    rowData:
      selectedPackages && !selectedPackages.length
        ? packages
        : selectedPackages,
    columnDefs: permissionLockedColumns,
    frameworkComponents: frameworkComponents(setStatusTrackerPopup),
    reactNext: true,
    rowClassRules,
    onSelectionChanged,
    onSortChanged,
    onGridReady,
    rowSelection: "multiple",
    suppressRowClickSelection: true,
    defaultColDef: {
      cellClass: ["no-border", "custom-wrap"],
      wrapText: true,
      suppressSizeToFit: showScroll ? true : false,
    },
    tabToNextCell: () => null,
    pagination: true,
    paginationPageSize: 100,
    editType: "fullRow",
    getRowNodeId: (data) => data.id,
    onDisplayedColumnsChanged: (params) =>
      onDisplayedColumnsChanged("PACKAGES", params),
    onCellValueChanged: onPackagesCellValueChanged,
    onCellEditingStarted,
    stopEditingWhenGridLosesFocus: true,
    getContextMenuItems: (params) => {
      const defaultItems = params.defaultItems;
      const customItems = defaultItems.filter((item) => {
        return (
          item !== "export" &&
          item !== "exportCsv" &&
          item !== "exportExcel" &&
          item !== "separator"
        );
      });
      return customItems;
    },
  };

  const selectedPackagesRef = useRef(selectedPackages);
  const displayedPackagesRef = useRef(null);

  const onInfiniteGridReady = (params) => {
    storePackagesGridOptionsApi(params.api);
    setGridOptionsApi(params.api);
    setColumnApi(params.columnApi);

    const dataSource = {
      rowCount: displayedPackagesRef?.current?.length,
      getRows: async (params) => {
        const spinnerId = uuid();
        dispatch(handleEnqueueSpinner(spinnerId, 2000));
        const dataAfterSortAndFilter = sortAndFilter(
          displayedPackagesRef?.current,
          params.sortModel,
          params.filterModel
        );

        const rowsThisPage = dataAfterSortAndFilter.slice(
          params.startRow,
          params.endRow
        );
        let lastRow = -1;

        if (dataAfterSortAndFilter.length <= params.endRow) {
          lastRow = dataAfterSortAndFilter.length;
        }

        let rowsWithCustomData = [];

        // Fetch the custom column data for all the rows on this page as a hashmap
        // and add that data to each row
        const rowIds = rowsThisPage.map((x) => x.id);
        const customColumnData = await fetchCustomColumnDataMultiItem(
          "packages",
          rowIds
        );
        for (let row of rowsThisPage) {
          if (row.id in customColumnData) {
            const formattedCustomColumnData = associateCustomColumnData(
              customColumnData[row.id],
              customColumns
            );
            rowsWithCustomData.push({ ...formattedCustomColumnData, ...row });
          } else {
            rowsWithCustomData.push({ ...row });
          }
        }

        params.successCallback(rowsWithCustomData, lastRow);
        dispatch(handleDequeueSpinner(spinnerId));
      },
    };

    params.api.setDatasource(dataSource);
  };

  const gridOptionsInfinite = {
    rowModelType: "infinite",
    onGridReady: onInfiniteGridReady,

    // display lines per page
    cacheBlockSize: 30,

    // how many rows to seek ahead when unknown data size.
    cacheOverflowSize: 0,

    // how many concurrent data requests are allowed.
    maxConcurrentDatasourceRequests: 1,

    // how many rows to initially allow scrolling to in the grid.
    infiniteInitialRowCount: 100,

    // how many pages to hold in the cache.
    maxBlocksInCache: 2,

    blockLoadDebounceMillis: 200,

    columnDefs: permissionLockedColumns,
    frameworkComponents: frameworkComponents(setStatusTrackerPopup),
    defaultColDef: {
      cellClass: ["no-border", "custom-wrap"],
      wrapText: true,
      suppressSizeToFit: showScroll ? true : false,
      sortable: true,
    },
    suppressRowClickSelection: true,
    editType: "fullRow",
    getRowNodeId: (data) => data.id,
    onDisplayedColumnsChanged: (params) =>
      onDisplayedColumnsChanged("PACKAGES", params),
    reactNext: true,
    rowClassRules,
    onSortChanged,
    rowSelection: "multiple",
    onCellValueChanged: onPackagesCellValueChanged,
    onCellEditingStarted,
    stopEditingWhenGridLosesFocus: true,
    getContextMenuItems: (params) => {
      const defaultItems = params.defaultItems;
      const customItems = defaultItems.filter((item) => {
        return (
          item !== "export" &&
          item !== "exportCsv" &&
          item !== "exportExcel" &&
          item !== "separator"
        );
      });
      return customItems;
    },
  };

  const exportParams = useMemo(() => {
    if (!permissionLockedColumns || !permissionLockedColumns.length) return;

    const exportedColumns = permissionLockedColumns
      .filter((col) => col.field && !col.hide)
      .map((col) => col.field);

    return {
      columnKeys: exportedColumns,
      fileName: "packages",
      processCellCallback: (params) => {
        const { colId } = params.column;
        if (colId === "due_date_unix" || colId === "fab_completed_on") {
          if (!params.value) return "";

          const f_date = generateTime(params.value * 1000, false, true, "-");

          return f_date;
        } else if (colId === "percent_complete") {
          if (!params.value) return;
          return params.value.toFixed(1);
        } else if (typeof params.value === "number" && colId !== "id") {
          if (!params.value) return;
          return params.value.toFixed(2);
        } else return params.value;
      },
    };
  }, [permissionLockedColumns]);

  const handleExcelExport = () => {
    if (gridOptionsApi) gridOptionsApi.exportDataAsExcel(exportParams);
  };
  const handleCSVExport = () => {
    if (gridOptionsApi) gridOptionsApi.exportDataAsCsv(exportParams);
  };

  /**
   *  Call the export API to trigger a download
   *  Have to use axios directly because useApiCall() doesn't support any of this
   */
  const handleExportTable = async (outputFormat = "xlsx") => {
    const apiBaseURL = process.env.REACT_APP_API;
    const package_ids = displayedPackagesRef.current.map((job) => job.id);
    const method = "POST";
    const query = new URLSearchParams({ outputFormat }).toString();
    const apiPath = `packages/export?${query}`;

    const config = {
      method,
      url: apiPath,
      baseURL: apiBaseURL,
      headers: { Authorization: `Bearer ${token}` },
      responseType: "arraybuffer", // Ensure binary data is handled properly
      data: { package_ids },
    };

    try {
      const response = await axios(config);
      // trigger the browser download
      const mimeType =
        outputFormat === "csv"
          ? "text/csv"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      const blob = new Blob([response.data], { type: mimeType });
      const href = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `packages-export.${outputFormat}`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
    } catch (error) {
      console.error("Export failed:", error);
      alert("Export failed. Please try again.");
    }
  };

  const previousSearchInput = usePrevious(searchInput);

  useEffect(() => {
    if (!gridOptionsApi || !columnApi || previousSearchInput === searchInput)
      return;

    const isInfiniteGrid = hasCustomColumnFeature;

    const visibleColumns = columnApi
      .getAllDisplayedColumns()
      ?.filter(
        (col) =>
          col.colDef.colId &&
          !columnsToExcludeFromGlobalSearch.includes(col.colDef.colId)
      );

    if (!searchInput) {
      // if we have selected packages reset table to that state, otherwise reset using all packages
      if (selectedPackagesRef?.current?.length) {
        displayedPackagesRef.current = selectedPackagesRef.current;
      } else displayedPackagesRef.current = packages;

      isInfiniteGrid
        ? gridOptionsApi.purgeInfiniteCache()
        : gridOptionsApi.setRowData(displayedPackagesRef.current);
    } else {
      const itemsAfterSearch = searchTable(
        escapeRegExp(searchInput),
        selectedPackages,
        visibleColumns
      );
      displayedPackagesRef.current = itemsAfterSearch;
      isInfiniteGrid
        ? gridOptionsApi.purgeInfiniteCache()
        : gridOptionsApi.setRowData(itemsAfterSearch);
    }
  }, [searchInput, gridOptionsApi, columnApi, selectedPackages]);

  useEffect(() => {
    return () => {
      setGridOptionsApi(null);
    };
  }, []);

  return (
    <>
      {packages && permissionLockedColumns && (
        <TableContainer
          tabs={generateTabs("EXISTING_JOBS", [], showAllWork)}
          handleToggle={() => toggleShowAllWork(!showAllWork)}
          handleExcel={
            hasCustomColumnFeature
              ? () => handleExportTable("xlsx")
              : handleExcelExport
          }
          handleCSV={
            hasCustomColumnFeature
              ? () => handleExportTable("csv")
              : handleCSVExport
          }
          searchInput={searchInput}
          setSearchInput={setSearchInput}
          currentTable={"PACKAGES"}
          selectedRows={selectedRows}
          totalRows={totalRowsCount}
          isInfiniteGrid={hasCustomColumnFeature}
        >
          {hasCustomColumnFeature ? (
            <InfiniteScrollTable
              totalNumberOfRows={displayedPackagesRef.current?.length || 0}
              gridOptions={gridOptionsInfinite}
              overrideCheckboxSelectColumn={true}
              setSelectedRows={setSelectedRows}
              selectedRowIds={selectedRows.map((item) => item.id)}
              onSelectAll={(isSelected) => {
                if (isSelected)
                  setSelectedRows(() => [...displayedPackagesRef.current]);
                else setSelectedRows(() => []);
              }}
            />
          ) : (
            <AgTable gridOptions={gridOptions} />
          )}
        </TableContainer>
      )}
      {showWorkflowModal && (
        <WorkflowModal
          open={showWorkflowModal}
          handleClose={() => toggleWorkflowModal(false)}
          handleSubmit={changeWorkflowSubmit}
          selectedItem={rowInfo}
        />
      )}
    </>
  );
};

export default PackagesTable;
