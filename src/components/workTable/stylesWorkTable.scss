@import "../styles/colors.scss";

div.work-table-filter {
  max-height: 100px;
  display: flex;
  box-sizing: border-box;
  width: calc(100vw - 50px);
  min-width: 500px;
  overflow-x: auto;
  padding: 20px 20px 0px 20px;

  &::-webkit-scrollbar {
    width: 10px;
    background-color: #f5f5f5;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #555;
  }

  & .selectable-filter-wrapper {
    min-width: 200px;

    & .selected-options {
      grid-gap: 5px;
      max-height: 100px;

      & .selected-item {
        width: 100px;
      }
    }
  }

  & .filter-button p {
    font-size: 0.9rem;
  }

  & .vertical-filter {
    margin-left: 10px;
  }

  & > button.clear-all-filters {
    height: 30px;
    font-size: 0.7rem;
    padding: 0 10px;
    background-color: transparent;
    color: $fabProBlue;
    border: 1px solid $fabProBlue;

    &:not(:disabled):hover {
      background-color: #555;
    }
  }
}

div.action-row {
  display: flex;
  margin: 0 0 8px;
  padding: 2px 15px 2px;
  background-color: $fabProBlue;
  justify-content: flex-end;
  align-items: center;
  height: 30px;
  margin: 0px 20px;

  & div.current-table-message {
    margin-right: auto;

    & p {
      color: white;
      font-weight: 500;
      font-size: 0.9rem;
    }
  }

  & div.table-search-wrapper {
    margin-right: auto;

    & input {
      height: 28px;
      background-color: $darkGrey;
      padding-left: 10px;
      border-radius: 3px;
      border: none;
      color: white;
      font-size: 0.8rem;
      box-sizing: border-box;
      width: 120px;

      &:focus {
        border: none;
        outline: none;
      }
    }

    & button {
      background-color: $darkGrey;
      margin-left: 10px;
      cursor: pointer;
      border-radius: 3px;
      border: none;
      height: 28px;
      width: 50px;
      color: white;
      font-size: 0.7rem;
      padding: 2px 6px;
      box-sizing: border-box;
    }
  }

  & span.action-buttons {
    display: flex;

    & label {
      height: 30px;
      display: flex;
      align-items: center;
      font-size: 1rem;
    }

    & button.disabled {
      opacity: 0.5 !important;
      color: white !important;
      cursor: default;

      & svg {
        color: white !important;
      }
    }

    & button {
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      margin: 1px 5px;
      padding: 0 8px;
      height: 28px;
      min-width: 75px;
      background-color: $darkGrey;
      color: white;
      border: none;
      border-radius: 3px;
      font-size: 0.7rem;

      &:not(.disabled):hover {
        background-color: lighten($darkGrey, 10%);
        cursor: pointer;
      }

      & svg {
        margin-right: 5px;
        font-size: 0.9rem;
      }
    }

    & > div.grouping-dropdown {
      display: flex;
      align-items: center;
      column-gap: 5px;
      height: 30px;

      & > div:not(.selectable-filter-wrapper) {
        height: 25px;

        & > div {
          height: 25px;

          & > select {
            height: 25px;
            font-size: 0.8rem;

            & > option {
              background-color: $darkGrey;
              color: #fff;
            }
          }
        }
      }

      & > div.selectable-filter-wrapper div.filter-button {
        color: #fff;

        & > p.show-more-hover {
          color: #fff;
        }
      }
    }
  }

  & .edit-row-buttons {
    display: flex;
    margin-left: 40px;

    & button.save {
      background-color: $green;
      &:hover {
        background-color: darken($green, 10%) !important;
      }
    }
    & button.cancel {
      background-color: $red;
      &:hover {
        background-color: darken($red, 10%) !important;
      }
    }
  }

  & .export-wrapper {
    margin-left: auto;
  }
}

.layout-body {
  & .table-container-content > div.ag-theme-balham-dark.custom-ag-styles {
    width: calc(100vw - 95px);
  }
  &.narrow {
    & .table-container-content > div.ag-theme-balham-dark.custom-ag-styles {
      width: calc(100vw - 295px);
    }
  }

  @supports (-webkit-touch-callout: none) {
    /* CSS specific to iOS devices */
    height: calc(100vh - 120px);
  }
}

p.no-table-data-message {
  text-align: center;
  margin-top: 100px;
  font-size: 1.3rem;
  color: $blue;
}
