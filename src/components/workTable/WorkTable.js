// NPM PACKAGE IMPORTS
import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
} from "react";
import Select from "msuite_storybook/dist/select/Select";
import Button from "msuite_storybook/dist/button/Button";
import { useDispatch, useSelector } from "react-redux";
import { FiLayers } from "react-icons/fi";
import { MdLowPriority } from "react-icons/md";
import moment from "moment";
import "moment-timezone";
import { logger } from "./../../utils/_dataDogUtils";

// REDUX IMPORTS
import {
  handleFetchColumnState,
  handleSetPageTitle,
  handleSaveSortState,
  handleFetchSortState,
  handleFetchForgeModelInfo,
} from "../../redux/generalActions";
import {
  handleFetchJobs,
  handleArchiveJob,
  handleUpdateJobs,
  handleDeleteJob,
  handleFetchForgeModelsByJob,
  handleClearJobsSavedColumnState,
  handleResetJobsList,
  handleSetJobsList,
  handleUpdateJobsNoRefresh,
} from "../jobs/jobsActions";
import {
  handleFetchPackages,
  handleArchivePackage,
  handleUpdatePackages,
  handleDeletePackage,
  handleClearPackagesSavedColumnState,
  handleSetPackagesList,
  handleResetPackagesList,
  handleUpdatePackageNoRefresh,
} from "../packages/packagesActions";
import {
  handleFetchDrawings,
  handleArchiveDrawing,
  handleUpdateDrawings,
  handleDeleteDrawings,
  handleClearDrawingsSavedColumnState,
  handleSetDrawingsList,
  handleResetDrawingsList,
  handleUpdateDrawingsNoRefresh,
} from "../drawings/drawingsActions";
import {
  handleFetchWorkTableItems,
  handleFilterItems,
  handleUpdateItems,
  // handleArchiveItem,
  handleDeleteItem,
  handleUpdateJointHeatNumber,
  handleCompleteUncomplete,
  handleClearItemsSavedColumnState,
} from "../items/itemsActions";
import {
  handleFetchWorkStages,
  handleFetchWorkStageGroupableColumns,
  handleClearWorkStageGroupableColumns,
  handleFetchFlows,
} from "../flows/flowsActions";
import {
  handleClearTableFilters,
  handleFetchTableFilters,
  handleSaveTableFilters,
  handleUpdateTableViewSettings,
} from "../profile/profileActions";
import {
  handleFetchCustomColumns,
  handleUpdateCustomColumnData,
} from "../customColumns/customColumnsActions";
import {
  handleFetchDrawingFiles,
  handleFetchPackageFile,
} from "../files/filesActions";
import { showBulkArchiveModal } from "../reusable/generalModal/generalModalActions";

import { notify } from "../reusable/alertPopup/alertPopupActions";

// TRANSLATION IMPORTS
import useTranslations from "../../hooks/useTranslations";
import jobsTranslations from "../jobs/jobsTranslations.json";

// COMPONENT IMPORTS
import MoreInfoDropdown from "../reusable/moreInfoDropdown/MoreInfoDropdown";
import MassUpdate from "../reusable/massUpdate/MassUpdate";
import PDFModal from "../reusable/pdfModal";
import PrioritiesModal from "../reusable/priorities/PrioritiesModal";
import MoreInfoModal from "./moreInfoModal/MoreInfoModal";
import ForgeModal from "../reusable/forge/ForgeModal";
import ConfirmationModal from "../reusable/confirmationModal/ConfirmationModal";
import BOMModal from "./bomModal/BOMModal";
import Filter from "../reusable/filter/Filter";
import CompletionModal from "./completionModal/CompletionModal";
import StatusTrackerPopup from "./statusTrackerPopup/StatusTrackerPopup";
import RejectionModal from "./rejectionModal/RejectionModal";
import RejectionHistoryModal from "./rejectionHistoryModal/RejectionHistoryModal";
import LabelsModal from "./labelsModal/LabelsModal";
import HeatNumberModal from "./heatNumberModal/HeatNumberModal";
import MaterialRejectionReport from "./materialRejectionReport/MaterialRejectionReport";
import AssignmentsModal from "../reusable/assignmentsModal/AssignmentsModal";
import CreateSelectModal from "../reusable/createSelectModal/CreateSelectModal";
import MoveDrawingsModal from "../reusable/moveDrawingsModal/MoveDrawingsModal";
import ForgeModelSelector from "../reusable/forgeModelSelector/ForgeModelSelector";
import SubmitRevisionModal from "../reusable/submitRevisionModal/SubmitRevisionModal";
import ManageMAJModal from "../reusable/manageMAJModal/ManageMAJModal";
import DownloadMAJsButton from "../reusable/downloadMAJsButton/DownloadMAJsButton";

import JobsTable from "./JobsTable";
import PackagesTable from "./PackagesTable";
import ItemsTable from "./ItemsTable";

// HELPER FUNCTION IMPORTS
import {
  permissionLock,
  escapeRegExp,
  checkForNewTableFilterSelections,
} from "../../_utils";
import usePrevious from "../../hooks/usePrevious";
import { fetchCustomColumnDataPerItem } from "../../_services";

// STYLE IMPORTS
import "./stylesWorkTable.scss";
import DrawingsTableV2 from "./DrawingTableV2/DrawingsTableV2";
import { DrawingsTableV2ContextProvider } from "./DrawingTableV2/DrawingsTableV2Context";
import DrawingsTable from "./DrawingsTable";
import SystemFeatures from "../../utils/system-features";
import withTooltip from "../reusable/tootltipHOC/withTooltip";

const modalTitle = {
  JOBS: "job_name",
  PACKAGES: "package_name",
  DRAWINGS: "name",
  ITEMS: "id",
};

// EXPORTS
const WorkTable = () => {
  const [currentTable, setCurrentTable] = useState("JOBS");
  const [showAllWork, toggleShowAllWork] = useState(null);
  const [selectedJobs, setSelectedJobs] = useState([]);
  const [selectedStages, setSelectedStages] = useState([]);
  const [selectedPackages, setSelectedPackages] = useState([]);
  const [selectedDrawings, setSelectedDrawings] = useState([]);
  const [showMoreInfo, toggleMoreInfo] = useState(false);
  const [showMoreInfoModal, toggleMoreInfoModal] = useState(false);
  const [moreInfoLocation, setMoreInfoLocation] = useState(null);
  const [showWorkflowModal, toggleWorkflowModal] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [rowInfo, setRowInfo] = useState(null);
  const [editedFields, setEditedFields] = useState([]);
  const [showMassUpdate, toggleMassUpdate] = useState(false);
  const [pdfViewer, togglePDFViewer] = useState(false);
  const [showPriorities, togglePriorities] = useState(false);
  const [forgeViewer, toggleForgeViewer] = useState(false);
  const [forgeModelSelector, toggleForgeModelSelector] = useState(false);
  const [showBOM, toggleBOM] = useState(false);
  const [confirmationModal, toggleConfirmationModal] = useState(null);
  const [file, setFile] = useState(null);
  const [showCompletionModal, toggleCompletionModal] = useState(false);
  const [statusTrackerPopup, setStatusTrackerPopup] = useState(null);
  const [jobsFilterSet, updateJobsFilterSet] = useState(false);
  const [packagesFilterSet, updatePackagesFilterSet] = useState(false);
  const [drawingsFilterSet, updateDrawingsFilterSet] = useState(false);
  const [workStagesFilterSet, updateWorkStagesFilterSet] = useState(false);
  const [allFiltersSet, updateAllFiltersSet] = useState(false);
  const [showRejectionModal, toggleRejectionModal] = useState(false);
  const [windowWidth, setWindowWidth] = useState(null);
  const [selectedGrouping, setSelectedGrouping] = useState(0);
  const [showRejectionHistoryModal, toggleRejectionHistoryModal] = useState(
    false
  );
  const [showLabelsModal, toggleShowLabelsModal] = useState(false);
  const [itemsGridOptionsApi, storeItemsGridOptionsApi] = useState(null);
  const [jobsGridOptionsApi, storeJobsGridOptionsApi] = useState(null);
  const [packagesGridOptionsApi, storePackagesGridOptionsApi] = useState(null);
  const [drawingsGridOptionsApi, storeDrawingsGridOptionsApi] = useState(null);
  const [selectedGroupableColumns, setSelectedGroupableColumns] = useState(
    null
  );
  const [materialNameObj, setMaterialNameObj] = useState(null);
  const [showHeatNumberModal, toggleHeatNumberModal] = useState(false);
  const [showAssignmentsModal, toggleAssignmentsModal] = useState(false);
  const [showMaterialsReportModal, toggleMaterialsReportModal] = useState(
    false
  );
  const [jobsSearchInput, setJobsSearchInput] = useState("");
  const [packagesSearchInput, setPackagesSearchInput] = useState("");
  const [drawingsSearchInput, setDrawingsSearchInput] = useState("");
  const [stagesSearchInput, setStagesSearchInput] = useState("");
  const [currentRowOrder, setCurrentRowOrder] = useState(null);
  const [completing, toggleCompleting] = useState(false);
  const [showMaterialTypesModal, toggleMaterialTypesModal] = useState(false);
  const [showJoiningProceduresModal, toggleJoiningProceduresModal] = useState(
    false
  );
  const [showLaydownLocationsModal, toggleLaydownLocationsModal] = useState(
    false
  );
  // eslint-disable-next-line no-unused-vars
  const [packagesSelectAll, setPackagesSelectAll] = useState(0);
  const [drawingsSelectAll, setDrawingsSelectAll] = useState(0);
  const [stagesSelectAll, setStagesSelectAll] = useState(0);
  const [submitRevisionInfo, setSubmitRevisionInfo] = useState(null);
  const [showMoveDrawingsModal, toggleMoveDrawingsModal] = useState(false);
  const [showManageMAJModal, toggleManageMAJModal] = useState(false);
  const [stageToEntityMap, setStageToEntityMap] = useState({
    job_ids: [],
    package_ids: [],
    drawing_ids: [],
  });
  const [isFilterDataLoading, setFilterDataLoading] = useState(true);
  const [respectSearchParams, setRespectSearchParams] = useState(true);
  const CUSTOM_COLUMNS_FEATURE_ID = 53;

  const translate = useTranslations(jobsTranslations);
  const dispatch = useDispatch();

  const { sortState } = useSelector((state) => state.generalData);
  const {
    isLoading: isJobsLoading,
    forgeModelsByJob,
    storedListOfJobs,
  } = useSelector((state) => state.jobsData);
  const {
    isLoading: isPackagesLoading,
    storedListOfPackages,
    packages,
  } = useSelector((state) => state.packagesData);
  const {
    isLoading: isDrawingsLoading,
    error: drawingsError,
    heatNumbers,
    storedListOfDrawings,
  } = useSelector((state) => state.drawingsData);
  const {
    items,
    filteredItems,
    isLoading: isItemsLoading,
    materialTypes,
    joiningProcedures,
  } = useSelector((state) => state.itemsData); // where we store the items data
  const {
    workStages,
    workStageColumns,
    workStageGroupableColumnsOptions,
  } = useSelector((state) => state.flowsData);
  const {
    tableFilters,
    permissions,
    features,
    viewingAll,
    userId,
    userInfo,
  } = useSelector((state) => state.profileData);
  const { laydown_locations } = useSelector((state) => state.shippingData);
  const { customColumns } = useSelector((state) => state.customColumns);
  const { packageFiles, drawingFiles } = useSelector(
    (state) => state.filesData
  );

  const formattedCustomColumns = customColumns?.filter(
    (cc) => cc.table_target === currentTable?.toLowerCase()
  );
  const hasCustomColumnFeature = features?.includes(CUSTOM_COLUMNS_FEATURE_ID);
  const handleWindowResize = () => setWindowWidth(window.innerWidth);

  const selectedJobsRef = useRef(null);
  const selectedPackagesRef = useRef(null);
  const selectedDrawingsRef = useRef(null);

  useEffect(() => {
    selectedJobsRef.current = selectedJobs;
    selectedPackagesRef.current = selectedPackages;
    selectedDrawingsRef.current = selectedDrawings;
  }, [selectedJobs, selectedPackages, selectedDrawings]);

  const ArchiveButton = () => {
    return (
      <button
        disabled={!selectedRows?.length || selectedRows?.length > 15}
        className={
          !selectedRows?.length || selectedRows?.length > 15 ? "disabled" : ""
        }
        onClick={() =>
          dispatch(
            showBulkArchiveModal(selectedRows, "packages", archivePackages)
          )
        }
      >
        Archive
      </button>
    );
  };

  const TooltipArchiveButton = withTooltip(ArchiveButton);

  /*--- START: Page load/refresh functions ---*/
  /** Reusable function for get search params from URL */
  const getURLSearchCriteria = () => {
    let searchCriteria = {};
    const jobMatch = /(?!(\?|^|&))job_id=\d{1,}(?=(&|$))/.exec(
      window.location.search
    );
    if (jobMatch) searchCriteria["jobId"] = parseInt(jobMatch[0].split("=")[1]);

    const forgeModelIdMatch = /(?!(\?|^|&))forge_model_id=\d{1,}(?=(&|$))/.exec(
      window.location.search
    );
    if (forgeModelIdMatch)
      searchCriteria["forgeModelId"] = parseInt(
        forgeModelIdMatch[0].split("=")[1]
      );

    const packageMatch = /(?!(\?|^|&))package_id=\d{1,}(?=(&|$))/.exec(
      window.location.search
    );
    if (packageMatch)
      searchCriteria["packageId"] = parseInt(packageMatch[0].split("=")[1]);

    const drawingMatch = /(?!(\?|^|&))drawing_id=\d{1,}(?=(&|$))/.exec(
      window.location.search
    );
    if (drawingMatch)
      searchCriteria["drawingId"] = parseInt(drawingMatch[0].split("=")[1]);

    const viewForgeMatch = /(?!(\?|^|&))view_forge=1(?=(&|$))/.exec(
      window.location.search
    );
    if (viewForgeMatch)
      searchCriteria["viewForgeMatch"] = parseInt(
        viewForgeMatch[0].split("=")[1]
      );

    const viewDrawingsMatch = /(?!(\?|^|&))view_drawings=1(?=(&|$))/.exec(
      window.location.search
    );
    if (viewDrawingsMatch)
      searchCriteria["viewDrawingsMatch"] = parseInt(
        viewDrawingsMatch[0].split("=")[1]
      );

    const viewItemsMatch = /(?!(\?|^|&))view_items=1(?=(&|$))/.exec(
      window.location.search
    );
    if (viewItemsMatch)
      searchCriteria["viewItemsMatch"] = parseInt(
        viewItemsMatch[0].split("=")[1]
      );

    return searchCriteria;
  };

  /**
   * Reusable function to load all objects related to j/p/d/s for the filtering.
   * @param {*} viewingAllWork Is all work selected?
   */
  const fetchAllJobFilterData = async (localShowAllWork) => {
    setFilterDataLoading(true);
    updateAllFiltersSet(false);
    updateJobsFilterSet(false);
    updatePackagesFilterSet(false);
    updateDrawingsFilterSet(false);
    updateWorkStagesFilterSet(false);
    softClearAllFilters();

    let stagesLoaded = false,
      jobsLoaded = false,
      packagesLoaded = false,
      drawingsLoaded = false;
    await dispatch(handleFetchWorkStages(false, [], [], [])).then((res) => {
      stagesLoaded = true;
    });
    await dispatch(handleFetchJobs(localShowAllWork, [])).then((res) => {
      if (!res.error) dispatch(handleSetJobsList(res));
      else if (res.error.status === 404) dispatch(handleSetJobsList([]));
      jobsLoaded = true;
    });
    await dispatch(handleFetchPackages([], localShowAllWork, null)).then(
      (res) => {
        if (!res.error) dispatch(handleSetPackagesList(res));
        else if (res.error.status === 404) dispatch(handleSetPackagesList([]));
        packagesLoaded = true;
      }
    );
    await dispatch(handleFetchDrawings([], localShowAllWork)).then((res) => {
      if (!res.error) dispatch(handleSetDrawingsList(res));
      else if (res.error.status === 404) dispatch(handleSetDrawingsList([]));
      drawingsLoaded = true;
    });

    if (stagesLoaded && jobsLoaded && packagesLoaded && drawingsLoaded)
      setFilterDataLoading(false);
  };

  /**
   * Apply the filters loaded from the database on initial load/showAllWork state changes.
   * @param {*} filters
   * @param {*} showAllWork
   */
  const applyTableFilters = async (filters = tableFilters, showAllWork) => {
    const jobsFilter = filters?.find((f) => f.filter_type === "jobs");
    const packagesFilter = filters?.find((f) => f.filter_type === "packages");
    const drawingsFilter = filters?.find((f) => f.filter_type === "drawings");
    const workStagesFilter = filters?.find(
      (f) => f.filter_type === "workStages"
    );

    let jobsToSelect = [],
      packagesToSelect = [],
      drawingsToSelect = [],
      workStagesToSelect = [];

    if (jobsFilter) {
      jobsToSelect =
        jobsFilter.select_all === 1
          ? storedListOfJobs
          : storedListOfJobs.filter((j) =>
              jobsFilter.item_ids_array.includes(j.id)
            );
    }

    if (packagesFilter) {
      packagesToSelect =
        packagesFilter.select_all === 1
          ? storedListOfPackages.filter((p) => {
              return jobsToSelect.length
                ? !!jobsToSelect.find((j) => j.id === p.job_id)
                : true;
            })
          : storedListOfPackages.filter((p) => {
              return (
                packagesFilter.item_ids_array.includes(p.id) &&
                (jobsToSelect.length
                  ? !!jobsToSelect.find((j) => j.id === p.job_id)
                  : true)
              );
            });
      setPackagesSelectAll(packagesFilter.select_all);
    }

    if (packagesToSelect.length && drawingsFilter) {
      drawingsToSelect = drawingsFilter
        ? drawingsFilter.select_all === 1
          ? storedListOfDrawings.filter((d) => {
              return packagesToSelect
                ? !!packagesToSelect.find((p) => p.id === d.package_id)
                : true;
            })
          : storedListOfDrawings.filter((d) => {
              return (
                drawingsFilter.item_ids_array.includes(d.id) &&
                (packagesToSelect
                  ? !!packagesToSelect.find((p) => p.id === d.package_id)
                  : true)
              );
            })
        : [];
      setDrawingsSelectAll(drawingsFilter.select_all);
    }

    // if stage selected, fetch groupable columns then items
    // then fetch all jobs /pkgs / drawings and filter based on ids from items
    if (workStagesFilter) {
      workStagesToSelect =
        workStagesFilter.select_all === 1
          ? workStages
          : workStagesFilter.item_ids === "0"
          ? [{ name: "Items w/o stages", id: 0 }]
          : workStages.filter((ws) =>
              workStagesFilter.item_ids_array.includes(ws.id)
            );

      let newSelectedGrouping = 0;
      let newSelectedColumns = null;
      if (workStagesToSelect.length) {
        setSelectedStages(workStagesToSelect);

        if (
          workStagesToSelect.length === 1 &&
          workStagesToSelect[0].id !== 0 &&
          workStagesToSelect[0].groupable === 1
        ) {
          newSelectedGrouping = 1;
          setSelectedGrouping(newSelectedGrouping);
          // call this twice because the second param sets different variables
          dispatch(
            handleFetchWorkStageGroupableColumns(workStagesToSelect[0].id)
          );
          await dispatch(
            handleFetchWorkStageGroupableColumns(workStagesToSelect[0].id, true)
          ).then((res) => {
            if (res.error) return;
            setSelectedGroupableColumns(res[0].work_item_columns);
            newSelectedColumns = res[0].work_item_columns;
          });
        } else {
          dispatch(handleClearWorkStageGroupableColumns);
        }

        // fetch items regardless of grouping
        dispatch(
          handleFetchWorkTableItems(
            jobsToSelect.map((j) => j.id),
            packagesToSelect.map((p) => p.id),
            drawingsToSelect.map((d) => d.id),
            workStagesToSelect.map((s) => s.id),
            showAllWork,
            newSelectedGrouping,
            newSelectedColumns
          )
        );
      }

      setStagesSelectAll(workStagesFilter.select_all);
    }

    // set the filters
    if (jobsFilter) {
      setSelectedJobs(jobsToSelect);
      // logic for loading a URL with view_forge and forge_model_id params
      let forgeFilter = filters?.find((f) => f.filter_type === "forge_models");
      if (jobsToSelect.length && forgeFilter && forgeFilter.item_ids_array) {
        let forgeModelId = forgeFilter.item_ids_array[0];
        setForgeInfo(forgeModelId, jobsToSelect);
      }
    } else {
      setSelectedJobs([]);
    }

    if (!packagesFilter) {
      setCurrentTable("JOBS");
      setSelectedPackages([]);
    } else {
      setSelectedPackages(packagesToSelect);
      if (!drawingsFilter) {
        setCurrentTable("PACKAGES");
        setCurrentRowOrder(null);
      }
    }

    if (packagesToSelect.length && drawingsFilter && drawingsToSelect.length) {
      setSelectedDrawings(drawingsToSelect);
      setCurrentTable("DRAWINGS");
    } else {
      setSelectedDrawings([]);
    }

    if (workStagesFilter && workStagesToSelect.length) {
      setSelectedStages(workStagesToSelect);
      setCurrentTable("ITEMS");
      setCurrentRowOrder(null);
    } else {
      setSelectedStages([]);
      setSelectedGrouping(0);
      setSelectedGroupableColumns([]);
    }

    updateAllFiltersSet(true);
  };

  /**
   * Handle switching between My Work/All Work - reusable functions reload the data for j/p/d/s
   * and applying saved filters back to the page.
   * @param {*} newShowAllWork
   */
  const handleToggleShowAllWork = (newShowAllWork) => {
    toggleShowAllWork(newShowAllWork);
    dispatch(handleUpdateTableViewSettings(newShowAllWork ? 1 : 0));
    // refresh all the stored data between j/p/d/s and the saved filters in the system
    setRespectSearchParams(false);
    fetchAllJobFilterData(newShowAllWork ? 1 : 0);
  };
  /*--- END: Page load/refresh functions ---*/

  /*--- START: Page load/refresh hooks ---*/
  const showScroll = useMemo(() => {
    return windowWidth <= 1100;
  }, [windowWidth]);

  useEffect(() => {
    dispatch(handleSetPageTitle());
    dispatch(handleFetchFlows());
    dispatch(handleFetchCustomColumns);
    window.addEventListener("resize", handleWindowResize);

    // fetch j/p/d/s data for page when there are search headers
    if (window.location.search !== "") {
      toggleShowAllWork(true);
      fetchAllJobFilterData(true);
    }

    return () => {
      dispatch(handleClearJobsSavedColumnState);
      dispatch(handleClearPackagesSavedColumnState);
      dispatch(handleClearDrawingsSavedColumnState);
      dispatch(handleClearItemsSavedColumnState);

      window.removeEventListener("resize", handleWindowResize);
      dispatch(handleClearTableFilters);
    };
  }, []);

  // apply filters on page load/refresh
  useEffect(() => {
    if (isFilterDataLoading) return;
    if (window.location.search === "" || !respectSearchParams) {
      dispatch(handleFetchTableFilters("jobs", showAllWork ? 1 : 0)).then(
        (res) => {
          if (!res.error) applyTableFilters(res, !!showAllWork);
          else updateAllFiltersSet(true);
        }
      );
    } else {
      // apply search criteria
      const {
        jobId,
        packageId,
        drawingId,
        forgeModelId,
        viewForgeMatch,
        viewDrawingsMatch,
        viewItemsMatch,
      } = getURLSearchCriteria();

      const filterData = [];
      if (jobId)
        filterData.push({
          filter_type: "jobs",
          select_all: 0,
          item_ids_array: [jobId],
        });
      if (viewForgeMatch && forgeModelId)
        filterData.push({
          filter_type: "forge_models",
          select_all: 0,
          item_ids_array: [forgeModelId],
        });
      if (!viewForgeMatch) {
        if (packageId)
          filterData.push({
            filter_type: "packages",
            select_all: 0,
            item_ids_array: [packageId],
          });
        if (drawingId)
          filterData.push({
            filter_type: "drawings",
            select_all: 0,
            item_ids_array: [drawingId],
          });
        if (viewDrawingsMatch)
          filterData.push({ filter_type: "drawings", select_all: 1 });
        if (viewItemsMatch)
          filterData.push({ filter_type: "workStages", select_all: 1 });
      }

      setRespectSearchParams(false); // toggle off since we are loading the search params once
      applyTableFilters(filterData, true);
    }
  }, [isFilterDataLoading]);

  useEffect(() => {
    if (viewingAll !== null && showAllWork === null) {
      toggleShowAllWork(!!viewingAll);
      // func will refresh all stored j/p/d/s data and apply filters
      fetchAllJobFilterData(viewingAll);
    }
  }, [viewingAll]);
  /*--- END: Page load/refresh hooks ---*/

  /*--- START: Filtering functions ---*/
  /**--- ALL ---**/
  // when we dont want to reset database filters
  const softClearAllFilters = () => {
    setSelectedStages([]);
    setSelectedDrawings([]);
    setSelectedPackages([]);
    setSelectedJobs([]);
    setSelectedRows([]);
    setCurrentTable("JOBS");
    setPackagesSelectAll(0);
    setDrawingsSelectAll(0);
    setStagesSelectAll(0);
    setStageToEntityMap({
      job_ids: [],
      package_ids: [],
      drawing_ids: [],
    });
  };

  const clearAllFilters = () => {
    setSelectedStages([]);
    setSelectedDrawings([]);
    setSelectedPackages([]);
    setSelectedJobs([]);
    setSelectedRows([]);
    setCurrentTable("JOBS");
    performFilterSave([], [], [], [], true, { type: "all", selectAll: 0 });
    setPackagesSelectAll(0);
    setDrawingsSelectAll(0);
    setStagesSelectAll(0);
    setStageToEntityMap({
      job_ids: [],
      package_ids: [],
      drawing_ids: [],
    });

    // dispatch(handleResetJobsList);
    // dispatch(handleResetPackagesList);
    // dispatch(handleResetDrawingsList);
    dispatch(handleFetchWorkStages());
  };

  // used when the user changes a filter in the ui (not for page load or toggle show all)
  const performFilterSave = (
    selectedJobs,
    selectedPackages,
    selectedDrawings,
    selectedStages,
    force,
    newSelectAll = {}
  ) => {
    if ((tableFilters && allFiltersSet) || force) {
      let filters = [];
      const filterObj = (type, itemsArray, selectAll = 0) => ({
        filter_type: type,
        item_ids: selectAll ? "" : itemsArray.map((i) => i.id).join(","),
        select_all: selectAll ? 1 : 0,
      });

      if (selectedJobs && selectedJobs.length) {
        filters.push(
          filterObj(
            "jobs",
            selectedJobs,
            ["all", "jobs"].includes(newSelectAll.type)
              ? newSelectAll.selectAll
              : 0
          )
        );
      }
      if (selectedPackages && selectedPackages.length) {
        filters.push(
          filterObj(
            "packages",
            selectedPackages,
            ["all", "packages"].includes(newSelectAll.type)
              ? newSelectAll.selectAll
              : 0
          )
        );
      }
      if (
        selectedPackages &&
        selectedPackages.length &&
        selectedDrawings &&
        selectedDrawings.length
      ) {
        filters.push(
          filterObj(
            "drawings",
            selectedDrawings,
            ["all", "drawings"].includes(newSelectAll.type)
              ? newSelectAll.selectAll
              : 0
          )
        );
      }
      if (selectedStages && selectedStages.length) {
        filters.push(
          filterObj(
            "workStages",
            selectedStages,
            ["all", "stages"].includes(newSelectAll.type)
              ? newSelectAll.selectAll
              : 0
          )
        );
      }
      dispatch(handleSaveTableFilters("jobs", filters, showAllWork ? 1 : 0));

      if (currentTable === "ITEMS") {
        if (!isItemsLoading)
          dispatch(
            handleFilterItems(
              selectedJobs.map((j) => j.id),
              selectedPackages.map((p) => p.id),
              selectedDrawings.map((d) => d.id),
              selectedStages.map((s) => s.id),
              selectedGrouping,
              selectedGroupableColumns
            )
          );
      }
    }
  };

  /**--- Jobs ---**/
  const prevSelectedJobIds = usePrevious(
    selectedJobs ? selectedJobs.map((j) => j.id) : []
  );
  const handleJobFilterDeselection = (updatedJobs) => {
    let filteredPackages = [];
    let filteredDrawings = [];
    const updatedJobIds = updatedJobs.map((j) => j.id);
    if (selectedPackages?.length) {
      filteredPackages = selectedPackages.filter((p) =>
        updatedJobIds.includes(p.job_id)
      );
    }

    if (selectedDrawings?.length) {
      filteredDrawings = selectedDrawings.filter((d) =>
        updatedJobIds.includes(d.job_id)
      );
    }

    return [filteredPackages, filteredDrawings];
  };

  /**
   * Split out from filter updates to refresh the selected stage data when filter is changed...
   * @param {Array} job_ids selected jobs ids to filter stages by
   * @param {Array} package_ids selected packages ids to filter stages by
   * @param {Array} drawing_ids selected drawings ids to filter stages by
   * @returns the selected stages that were set in the filters
   */
  const handleUpdatingSelectedStages = async (
    job_ids = [],
    package_ids = [],
    drawing_ids = []
  ) => {
    let newSelectedStages = [], // default is none...
      newSelectedGrouping = 0, // default is 0...
      newSelectedGroupableColumns = null;

    const newStagesFetched = await dispatch(
      handleFetchWorkStages(false, job_ids, package_ids, drawing_ids)
    );

    // process any errors from fetching and filter down the selection
    if (stagesSelectAll) {
      newSelectedStages = newStagesFetched.error ? [] : newStagesFetched;
    } else {
      // else we're not in select all, try to drill down based on our selected filters...
      newSelectedStages = selectedStages.filter((ss) =>
        newStagesFetched.find((ns) => ns.id === ss.id)
      );
    }

    // if they match, do nothing but refresh the data...
    const isSameStage =
      newSelectedStages.length === 1 &&
      selectedStages.length === 1 &&
      newSelectedStages[0].id === selectedStages[0].id;

    setSelectedStages(newSelectedStages); // always reset, grouping reloads data choice...

    // if this is already selected, just refresh
    if (isSameStage) newSelectedGrouping = selectedGrouping;

    // if we only have a different stage that is groupable selected, default to grouped and load new data...
    if (
      !isSameStage &&
      newSelectedStages.length === 1 &&
      newSelectedStages[0].groupable === 1
    ) {
      newSelectedGrouping = 1;
      // get the groupable columns to set...
      await dispatch(
        handleFetchWorkStageGroupableColumns(newSelectedStages[0].id, true)
      ).then((res) => {
        if (res.error) {
          newSelectedGroupableColumns = [materialNameObj];
        } else {
          newSelectedGroupableColumns = [
            materialNameObj,
            ...res.filter((c) => c.id === materialNameObj.id),
          ];
        }
        setSelectedGroupableColumns(newSelectedGroupableColumns);
      });
    }

    setSelectedGrouping(newSelectedGrouping);

    return newSelectedStages;
  };

  // TODO: move out duplicated logic to reusable functions - July 2025 update mostly done except for the filtering hierarchy....
  const handleJobFilterSelection = async (newSelectedJobs, newAllSelected) => {
    let newSelectedPackages = null,
      newSelectedDrawings = null,
      newSelectedStages = null;

    const addedToSelection = checkForNewTableFilterSelections(
      prevSelectedJobIds,
      newSelectedJobs?.map((j) => j.id)
    );

    const jobIds = (newSelectedJobs ?? []).map((j) => j.id);
    // if new selections have been made make the necessary calls to API
    if (addedToSelection) {
      newSelectedPackages =
        selectedPackages.length && !packagesSelectAll
          ? selectedPackages.filter((p) => jobIds.includes(p.job_id))
          : packagesSelectAll
          ? storedListOfPackages.filter((p) => jobIds.includes(p.job_id))
          : null;

      if (newSelectedPackages && selectedPackages.length) {
        newSelectedDrawings =
          selectedDrawings.length && !drawingsSelectAll
            ? selectedDrawings.filter((d) =>
                newSelectedPackages.find((p) => p.id === d.package_id)
              )
            : drawingsSelectAll
            ? storedListOfDrawings.filter((d) =>
                newSelectedPackages.find((p) => p.id === d.package_id)
              )
            : null;
      }
    } else {
      // if only deselections were made, filter on necessary items
      const [filteredPackages, filteredDrawings] = handleJobFilterDeselection(
        newSelectedJobs
      );
      newSelectedPackages = filteredPackages;
      newSelectedDrawings = filteredDrawings;
    }

    if (newSelectedPackages) {
      setSelectedPackages(newSelectedPackages);
    }
    if (newSelectedDrawings) {
      setSelectedDrawings(newSelectedDrawings);
    }

    // fetch and update the stage selection and items data...
    newSelectedStages = await handleUpdatingSelectedStages(
      jobIds,
      newSelectedPackages?.map((p) => p.id),
      newSelectedDrawings?.map((d) => d.id)
    );

    setSelectedJobs(newSelectedJobs);

    logger.info("Filters Changed", {
      user_id: userId,
      username: userInfo.username,
      name: "Jobs Filter",
      selections: newSelectedJobs,
      select_all: newAllSelected ? 1 : 0,
    });
    performFilterSave(
      newSelectedJobs,
      newSelectedPackages || selectedPackages,
      newSelectedDrawings || selectedDrawings,
      newSelectedStages || selectedStages,
      true,
      {
        type: "jobs",
        selectAll: newAllSelected ? 1 : 0,
      }
    );
    if (selectedStages.length) {
      currentTable !== "ITEMS" && setCurrentTable("ITEMS");
      setCurrentRowOrder(null);
    } else if ((newSelectedDrawings || selectedDrawings).length) {
      currentTable !== "DRAWINGS" && setCurrentTable("DRAWINGS");
      setCurrentRowOrder(null);
    } else if ((newSelectedPackages || selectedPackages).length) {
      currentTable !== "PACKAGES" && setCurrentTable("PACKAGES");
      setCurrentRowOrder(null);
    } else if (
      !(newSelectedPackages || selectedPackages).length &&
      !selectedStages.length
    ) {
      currentTable !== "JOBS" && setCurrentTable("JOBS");
      setCurrentRowOrder(null);
    }
  };

  /**--- Packages ---**/
  const prevSelectedPackageIds = usePrevious(
    selectedPackages ? selectedPackages.map((p) => p.id) : []
  );
  const handlePackageFilterDeselection = (updatedPackages) => {
    let filteredDrawings = [];
    const updatedPackageIds = updatedPackages.map((p) => p.id);
    if (selectedDrawings?.length) {
      filteredDrawings = selectedDrawings.filter((d) =>
        updatedPackageIds.includes(d.package_id)
      );
    }
    return filteredDrawings;
  };

  const handlePackageFilterSelection = async (
    newSelectedPackages,
    newAllSelected,
    selectedJobsToUse = null
  ) => {
    setPackagesSelectAll(newAllSelected ? 1 : 0);
    let newSelectedDrawings = [],
      newSelectedStages = null;

    const addedToSelections = checkForNewTableFilterSelections(
      prevSelectedPackageIds,
      newSelectedPackages?.map((p) => p.id)
    );
    const pkgIds = (newSelectedPackages ?? []).map((p) => p.id);
    if (addedToSelections) {
      if (newSelectedPackages.length) {
        if (selectedDrawings.length && !drawingsSelectAll) {
          newSelectedDrawings = selectedDrawings.filter((d) =>
            newSelectedPackages.find((p) => p.id === d.package_id)
          );
        } else if (drawingsSelectAll) {
          newSelectedDrawings = storedListOfDrawings.filter((d) =>
            pkgIds.includes(d.package_id)
          );
        }
      }
    } else {
      newSelectedDrawings = handlePackageFilterDeselection(newSelectedPackages);
    }
    setSelectedDrawings(newSelectedDrawings);

    // update the stage selection and items data...
    newSelectedStages = await handleUpdatingSelectedStages(
      (selectedJobsToUse || selectedJobs).map((j) => j.id),
      newSelectedPackages?.map((p) => p.id),
      newSelectedDrawings?.map((d) => d.id)
    );

    setSelectedPackages(newSelectedPackages);

    logger.info("Filters Changed", {
      user_id: userId,
      username: userInfo.username,
      name: "Packages Filter",
      selections: newSelectedPackages,
      select_all: newAllSelected ? 1 : 0,
    });
    performFilterSave(
      selectedJobsToUse || selectedJobs,
      newSelectedPackages,
      newSelectedDrawings || selectedDrawings,
      newSelectedStages || selectedStages,
      true,
      {
        type: "packages",
        selectAll: newAllSelected ? 1 : 0,
      }
    );

    if (!newSelectedPackages.length && !selectedStages.length) {
      setCurrentTable("JOBS");
      setCurrentRowOrder(null);
    } else if (
      !(newSelectedDrawings && selectedDrawings).length &&
      !selectedStages.length
    ) {
      setCurrentTable("PACKAGES");
      setCurrentRowOrder(null);
    }
  };

  /**--- Drawings ---**/
  const handleDrawingFilterSelection = async (
    newSelectedDrawings,
    newAllSelected,
    selectedPackagesToUse = null,
    selectedJobsToUse = null
  ) => {
    setDrawingsSelectAll(newAllSelected ? 1 : 0);
    let newSelectedStages = null;

    // update the stage selection and items data...
    newSelectedStages = await handleUpdatingSelectedStages(
      (selectedJobsToUse || selectedJobs).map((j) => j.id),
      (selectedPackagesToUse || selectedPackages).map((p) => p.id),
      newSelectedDrawings?.map((d) => d.id)
    );

    setSelectedDrawings(newSelectedDrawings);

    logger.info("Filters Changed", {
      user_id: userId,
      username: userInfo.username,
      name: "Drawings Filter",
      selections: newSelectedDrawings,
      select_all: newAllSelected ? 1 : 0,
    });
    performFilterSave(
      selectedJobsToUse || selectedJobs,
      selectedPackagesToUse || selectedPackages,
      newSelectedDrawings,
      newSelectedStages || selectedStages,
      true,
      {
        type: "drawings",
        selectAll: newAllSelected ? 1 : 0,
      }
    );

    if (
      !(newSelectedDrawings || selectedDrawings).length &&
      !selectedStages.length
    ) {
      setCurrentTable("PACKAGES");
      setCurrentRowOrder(null);
    } else if (!selectedStages.length) {
      setCurrentTable("DRAWINGS");
      setCurrentRowOrder(null);
    }
  };

  /**--- Stages/Items ---**/
  const handleStageFilterSelection = async (
    newSelectedStages,
    newAllSelected
  ) => {
    setStagesSelectAll(newAllSelected ? 1 : 0);
    // logic for item w/o stages within stage filter
    const ids = newSelectedStages.map((item) => item.id);
    const selectedIds = selectedStages.map((item) => item.id);

    if (ids.includes(0) && selectedStages.length) return;
    if (selectedIds.includes(0) && newSelectedStages.length) return;

    setSelectedStages(newSelectedStages);

    if (newSelectedStages.length) {
      let newSelectedGrouping = 0,
        newSelectedGroupableColumns = [];

      if (
        newSelectedStages.length === 1 &&
        newSelectedStages[0].id !== 0 &&
        newSelectedStages[0].groupable === 1
      ) {
        newSelectedGrouping = 1;
        await dispatch(
          handleFetchWorkStageGroupableColumns(newSelectedStages[0].id, true)
        ).then((res) => {
          if (!res.error)
            newSelectedGroupableColumns = res[0]?.work_item_columns;
        });
        dispatch(handleFetchWorkStageGroupableColumns(newSelectedStages[0].id));
      } else dispatch(handleClearWorkStageGroupableColumns);

      setSelectedGrouping(newSelectedGrouping);
      setSelectedGroupableColumns(newSelectedGroupableColumns);

      dispatch(
        handleFetchWorkTableItems(
          selectedJobs.map((j) => j.id),
          selectedPackages.map((p) => p.id),
          selectedDrawings.map((d) => d.id),
          newSelectedStages.map((s) => s.id),
          showAllWork,
          newSelectedGrouping,
          newSelectedGroupableColumns
        )
      );

      if (currentTable !== "ITEMS") {
        setCurrentTable("ITEMS");
        setCurrentRowOrder(null);
      }
    } else {
      dispatch(handleResetJobsList);
      dispatch(handleResetPackagesList);
      dispatch(handleResetDrawingsList);

      if (selectedDrawings.length) setCurrentTable("DRAWINGS");
      else if (selectedPackages.length) setCurrentTable("PACKAGES");
      else setCurrentTable("JOBS");

      setCurrentRowOrder(null);
    }

    logger.info("Filters Changed", {
      user_id: userId,
      username: userInfo.username,
      name: "Stages Filter",
      selections: newSelectedStages,
      select_all: newAllSelected ? 1 : 0,
    });
    performFilterSave(
      selectedJobs,
      selectedPackages,
      selectedDrawings,
      newSelectedStages,
      true,
      {
        type: "stages",
        selectAll: newAllSelected ? 1 : 0,
      }
    );
  };

  const changeSelectedGrouping = async (newSelectedGrouping) => {
    setSelectedGrouping(newSelectedGrouping);

    logger.info("Grouping Changed", {
      user_id: userId,
      username: userInfo.username,
      name: "Grouping Changed",
      selections: {
        jobs: selectedJobs,
        packages: selectedPackages,
        drawings: selectedDrawings,
        stages: selectedStages,
        showAllWork,
        selectedGrouping: newSelectedGrouping,
        selectedGroupableColumns,
      },
    });

    if (!isItemsLoading)
      await dispatch(
        handleFilterItems(
          selectedJobs.map((j) => j.id),
          selectedPackages.map((p) => p.id),
          selectedDrawings.map((d) => d.id),
          selectedStages.map((s) => s.id),
          newSelectedGrouping,
          selectedGroupableColumns
        )
      );

    dispatch(
      handleFetchWorkTableItems(
        selectedJobs.map((j) => j.id),
        selectedPackages.map((p) => p.id),
        selectedDrawings.map((d) => d.id),
        selectedStages.map((s) => s.id),
        showAllWork,
        newSelectedGrouping,
        selectedGroupableColumns
      )
    );
  };

  const selectGroupableColumns = (newSelected) => {
    let columnsToUse = newSelected;

    if (
      !columnsToUse ||
      !columnsToUse.length ||
      !columnsToUse.find((c) => c.id === materialNameObj.id)
    )
      columnsToUse = [materialNameObj, ...(columnsToUse || [])];

    setSelectedGroupableColumns(columnsToUse);

    if (!isItemsLoading)
      dispatch(
        handleFilterItems(
          selectedJobs.map((j) => j.id),
          selectedPackages.map((p) => p.id),
          selectedDrawings.map((d) => d.id),
          selectedStages.map((s) => s.id),
          selectedGrouping,
          columnsToUse
        )
      );
  };
  /*--- END: Filtering functions ---*/

  /*--- START: Filtering hooks ---*/
  const displayedGroupingOptions = useMemo(() => {
    return [
      { value: 0, display: translate("Ungrouped") },
      { value: 1, display: translate("Grouped") },
      // { value: 2, display: translate("Grouped By Drawing") }
    ];
  }, []);

  useEffect(() => {
    if (currentTable !== "ITEMS") dispatch(handleClearItemsSavedColumnState);
    setSelectedRows([]);
  }, [currentTable]);

  // todo - do we need this still after the other fixes?
  useEffect(() => {
    if (window.location.search !== "") updateAllFiltersSet(true);
  }, [
    tableFilters,
    jobsFilterSet,
    packagesFilterSet,
    drawingsFilterSet,
    workStagesFilterSet,
    allFiltersSet,
  ]);

  useEffect(() => {
    if (allFiltersSet) {
      dispatch(
        handleFetchColumnState(
          currentTable,
          selectedStages.length ? selectedStages.map((s) => s.id) : null
        )
      );
      dispatch(
        handleFetchSortState(
          currentTable,
          currentTable === "ITEMS"
            ? selectedGrouping
              ? selectedGrouping + 1
              : null
            : null,
          currentTable === "ITEMS"
            ? selectedStages.map((s) => s.id).join(",")
            : null
        )
      );
    }
  }, [currentTable, allFiltersSet]);

  const jobsAllSelected = useMemo(() => {
    if (storedListOfJobs && tableFilters && allFiltersSet) {
      let jobsTableFilter = tableFilters.find((f) => f.filter_type === "jobs");
      if (jobsTableFilter) return jobsTableFilter.select_all === 1;
    } else if (!allFiltersSet) {
      return false;
    }
  }, [tableFilters, allFiltersSet]);

  // TODO: map filters to just data that's needed (name / id / number)
  const jobsDisplayedOptions = useMemo(() => {
    let jobsToDisplay = [];
    if (storedListOfJobs.length) {
      if (jobsSearchInput !== "") {
        const pattern = new RegExp(escapeRegExp(jobsSearchInput), "i");
        jobsToDisplay = [
          ...selectedJobs.filter((s) =>
            pattern.test(`(${s.job_number}) ${s.job_name}`)
          ),
          ...storedListOfJobs
            .filter(
              (o) =>
                pattern.test(`(${o.job_number}) ${o.job_name}`) &&
                !selectedJobs.find((j) => j.id === o.id)
            )
            .sort((a, b) => (a.job_name > b.job_name ? 1 : -1)),
        ];
      } else {
        jobsToDisplay = [
          ...selectedJobs,
          ...storedListOfJobs
            .filter((o) => !selectedJobs.find((j) => j.id === o.id))
            .sort((a, b) => (a.job_name > b.job_name ? 1 : -1)),
        ];
      }
    }
    // update the filters to only show the relevant entries for the selected stages
    if (currentTable === "ITEMS")
      jobsToDisplay = jobsToDisplay.filter((j) =>
        stageToEntityMap.job_ids.includes(j.id)
      );

    return jobsToDisplay;
  }, [
    storedListOfJobs,
    jobsSearchInput,
    selectedJobs,
    showAllWork,
    stageToEntityMap,
    currentTable,
  ]);

  const packagesAllSelected = useMemo(() => {
    if (storedListOfPackages.length && tableFilters && allFiltersSet) {
      let packagesTableFilter = tableFilters.find(
        (f) => f.filter_type === "packages"
      );
      if (packagesTableFilter) return packagesTableFilter.select_all === 1;
    } else if (!allFiltersSet) {
      return false;
    }
  }, [tableFilters, allFiltersSet]);

  const packagesDisplayedOptions = useMemo(() => {
    let pkgsToDisplay = [];
    const selectedJobIds = selectedJobs.map((j) => j.id);
    if (storedListOfPackages.length) {
      if (packagesSearchInput !== "") {
        const pattern = new RegExp(escapeRegExp(packagesSearchInput), "i");
        pkgsToDisplay = selectedJobIds.length
          ? [
              ...selectedPackages.filter(
                (s) =>
                  pattern.test(`(${s.id}) ${s.package_name}`) &&
                  selectedJobIds.includes(s.job_id)
              ),
              ...storedListOfPackages
                .filter(
                  (p) =>
                    pattern.test(`(${p.id}) ${p.package_name}`) &&
                    selectedJobIds.includes(p.job_id) &&
                    !selectedPackages.find((o) => o.id === p.id)
                )
                .sort((a, b) => (a.package_name > b.package_name ? 1 : -1)),
            ]
          : [
              ...selectedPackages.filter((s) =>
                pattern.test(`(${s.id}) ${s.package_name}`)
              ),
              ...storedListOfPackages
                .filter(
                  (o) =>
                    pattern.test(`(${o.id}) ${o.package_name}`) &&
                    !selectedPackages.find((p) => p.id === o.id)
                )
                .sort((a, b) => (a.package_name > b.package_name ? 1 : -1)),
            ];
      } else {
        pkgsToDisplay = selectedJobIds.length
          ? [
              ...selectedPackages.filter((p) =>
                selectedJobIds.includes(p.job_id)
              ),
              ...storedListOfPackages
                .filter(
                  (p) =>
                    selectedJobIds.includes(p.job_id) &&
                    !selectedPackages.find((o) => o.id === p.id)
                )
                .sort((a, b) => (a.package_name > b.package_name ? 1 : -1)),
            ]
          : [
              ...selectedPackages,
              ...storedListOfPackages.filter(
                (o) => !selectedPackages.find((j) => j.id === o.id)
              ),
            ];
      }
    }
    // update the filters to only show the relevant entries for the selected stages
    if (currentTable === "ITEMS")
      pkgsToDisplay = pkgsToDisplay.filter((p) =>
        stageToEntityMap.package_ids.includes(p.id)
      );

    return pkgsToDisplay;
  }, [
    storedListOfPackages,
    packagesSearchInput,
    selectedPackages,
    showAllWork,
    selectedJobs,
    stageToEntityMap,
    currentTable,
  ]);

  const drawingsAllSelected = useMemo(() => {
    if (storedListOfDrawings.length && tableFilters && allFiltersSet) {
      let drawingsTableFilter = tableFilters.find(
        (f) => f.filter_type === "drawings"
      );
      if (drawingsTableFilter) return drawingsTableFilter.select_all === 1;
    } else if (!allFiltersSet) {
      return false;
    }
  }, [tableFilters, allFiltersSet]);

  const drawingsDisplayedOptions = useMemo(() => {
    let drawingsToDisplay = [];
    const selectedPkgIds = selectedPackages.map((p) => p.id);
    if (drawingsSearchInput !== "") {
      const pattern = new RegExp(escapeRegExp(drawingsSearchInput), "i");
      drawingsToDisplay = selectedPkgIds
        ? [
            ...selectedDrawings.filter(
              (d) =>
                pattern.test(d.name) && selectedPkgIds.includes(d.package_id)
            ),
            ...(Array.isArray(storedListOfDrawings) ? storedListOfDrawings : [])
              .filter(
                (o) =>
                  pattern.test(o.name) &&
                  !selectedDrawings.find((d) => d.id === o.id) &&
                  selectedPkgIds.includes(o.package_id)
              )
              .sort((a, b) => (a.name > b.name ? 1 : -1)),
          ]
        : [
            ...selectedDrawings.filter((s) => pattern.test(s.name)),
            ...storedListOfDrawings
              .filter(
                (o) =>
                  pattern.test(o.name) &&
                  !selectedDrawings.find((d) => d.id === o.id)
              )
              .sort((a, b) => (a.name > b.name ? 1 : -1)),
          ];
    } else {
      drawingsToDisplay = selectedPkgIds
        ? [
            ...selectedDrawings.filter((d) =>
              selectedPkgIds.includes(d.package_id)
            ),
            ...(Array.isArray(storedListOfDrawings) ? storedListOfDrawings : [])
              .filter(
                (o) =>
                  !selectedDrawings.find((d) => d.id === o.id) &&
                  selectedPkgIds.includes(o.package_id)
              )
              .sort((a, b) => (a.name > b.name ? 1 : -1)),
          ]
        : [
            ...selectedDrawings,
            ...storedListOfDrawings.filter(
              (o) =>
                !selectedDrawings.find((d) => d.id === o.id) &&
                selectedPkgIds.includes(o.package_id)
            ),
          ];

      const newSelectedDrawings = drawingsToDisplay.filter((n) =>
        selectedDrawings.find((s) => n.id === s.id)
      );

      if (
        selectedDrawings.length &&
        JSON.stringify(selectedDrawings) !== JSON.stringify(newSelectedDrawings)
      )
        setSelectedDrawings(newSelectedDrawings);
    }
    // update the filters to only show the relevant entries for the selected stages
    if (currentTable === "ITEMS")
      drawingsToDisplay = drawingsToDisplay.filter(
        (d) =>
          stageToEntityMap.drawing_ids.includes(d.id) &&
          selectedPackages.map((p) => p.id).includes(d.package_id)
      );
    return drawingsToDisplay;
  }, [
    storedListOfDrawings,
    drawingsSearchInput,
    selectedDrawings,
    selectedPackages,
    drawingsError,
    showAllWork,
    stageToEntityMap,
    currentTable,
  ]);

  const workStagesAllSelected = useMemo(() => {
    if (workStages && tableFilters && allFiltersSet) {
      let workStagesTableFilter = tableFilters.find(
        (f) => f.filter_type === "workStages"
      );
      if (workStagesTableFilter) {
        if (
          workStagesTableFilter.select_all === 1 &&
          workStagesTableFilter.item_ids_array[0] !== 0
        )
          return true;
        else return false;
      }
    } else if (!allFiltersSet) {
      return false;
    }
  }, [tableFilters, allFiltersSet]);

  const stagesDisplayedOptions = useMemo(() => {
    if (workStages && workStages.length) {
      if (stagesSearchInput !== "") {
        const pattern = new RegExp(escapeRegExp(stagesSearchInput), "i");
        return [
          { name: "Items w/o stages", id: 0 },
          ...selectedStages.filter((s) => pattern.test(s.name) && s.id !== 0),
          ...workStages
            .filter(
              (o) =>
                pattern.test(o.name) &&
                o.id !== 0 &&
                !selectedStages.find((s) => s.id === o.id)
            )
            .sort((a, b) => (a.name > b.name ? 1 : -1)),
        ];
      } else {
        return [
          { name: "Items w/o stages", id: 0 },
          ...selectedStages.filter((i) => i && i.id !== 0),
          ...workStages
            .filter((o) => !selectedStages.find((s) => s.id === o.id))
            .sort((a, b) => (a.name > b.name ? 1 : -1)),
        ];
      }
    } else return [{ name: "Items w/o stages", id: 0 }];
  }, [workStages, stagesSearchInput, selectedStages, showAllWork]);

  const displayedGroupingColumnOptions = useMemo(() => {
    if (
      selectedStages &&
      selectedStages.length === 1 &&
      selectedStages[0].id !== 0
    ) {
      return workStageGroupableColumnsOptions;
    } else return [];
  }, [workStageGroupableColumnsOptions, selectedStages]);

  useEffect(() => {
    if (currentTable === "ITEMS")
      dispatch(
        handleFetchSortState(
          "ITEMS",
          selectedGrouping ? selectedGrouping + 1 : null,
          selectedStages.map((s) => s.id).join(",")
        )
      );
  }, [selectedGrouping]);

  useEffect(() => {
    if (!materialNameObj && workStageColumns && workStageColumns.length) {
      const materialName = workStageColumns.find(
        (c) => c.normal_name === "material_name"
      );
      if (materialName) setMaterialNameObj(materialName);
    }
  }, [workStageColumns]);

  const displayedColumns = useMemo(() => {
    let result = {};

    for (let column of workStageColumns) {
      if (!result[column.id]) {
        result[column.id] = column;
      }
    }
    return Object.values(result);
  }, [workStageColumns]);
  /*--- END: Filtering hooks ---*/

  /*--- START: Grid data change/load functions ---*/
  const clearTableData = () => {
    setSelectedStages([]);
    setSelectedPackages([]);
    setSelectedDrawings([]);
    setCurrentTable("JOBS");
    setCurrentRowOrder(null);
  };

  const handleSaveEditedRow = () => {
    switch (currentTable) {
      case "JOBS":
        dispatch(handleUpdateJobs(rowInfo.id, editedFields));
        break;
      case "PACKAGES":
        dispatch(handleUpdatePackages(rowInfo.id, editedFields));
        break;
      case "DRAWINGS":
        dispatch(handleUpdateDrawings(rowInfo.id, editedFields));
        break;
      case "ITEMS":
        const editedCols = editedFields.map((column) => Object.keys(column)[0]);
        const newEditedFields = editedFields.filter((column) => {
          if (column.joint_heat_numbers) return false;
          else return true;
        });

        if (editedCols.includes("joint_heat_numbers")) {
          let newJointHeatNumber = editedFields.find(
            (column) => Object.keys(column)[0] === "joint_heat_numbers"
          ).joint_heat_numbers;

          const updatedJointHeatNumber = {
            work_item_ids: rowInfo.id,
            position: newJointHeatNumber.position,
            heat_number: newJointHeatNumber.heat_number,
          };

          // May to want refactor these actions to be performed althother so we only re-fetch items once
          dispatch(
            handleUpdateJointHeatNumber(
              updatedJointHeatNumber,
              selectedIdsInFilters
            )
          ).then(() => {
            if (newEditedFields.length)
              dispatch(
                // TODO - updating items grid
                handleUpdateItems(
                  rowInfo.id,
                  newEditedFields,
                  selectedIdsInFilters
                )
              );
          });
        } else
          dispatch(
            // TODO - updating items grid
            handleUpdateItems(rowInfo.id, newEditedFields, selectedIdsInFilters)
          );
        break;
      default:
        return null;
    }
    toggleConfirmationModal(null);
    setEditedFields([]);
  };

  const handleClosePDFViewer = () => {
    if (!showBOM) setRowInfo(null);
    togglePDFViewer(false);
  };

  const archivePackages = (ids) => {
    let newSelectedJobs = selectedJobs;
    dispatch(handleArchivePackage(ids)).then((res) => {
      return dispatch(handleFetchJobs(showAllWork, []))
        .then((newJobs) => {
          if (!newJobs.error) {
            newSelectedJobs = newJobs.filter(
              (nj) => !!selectedJobs.find((sj) => sj.id === nj.id)
            );
            dispatch(handleSetJobsList(newJobs));
          }
          return res;
        })
        .then((res) => {
          if (res.error) return;

          const newSelectedPackages = selectedPackages.filter(
            (p) => ids.indexOf(p.id) < 0
          );
          setSelectedPackages(newSelectedPackages);
          if (newSelectedPackages.length) {
            dispatch(handleFetchPackages([], showAllWork)).then((res) => {
              if (!res.error) {
                setSelectedJobs(newSelectedJobs);
                dispatch(handleSetPackagesList(res));
              }
            });
          }
        });
    });
  };

  const onOptionClick = (target) => {
    switch (target) {
      case "SAVE":
        handleSaveEditedRow();
        return;
      // case "MORE_INFO":
      //   toggleMoreInfo(false);
      //   toggleMoreInfoModal(true);
      //   return;
      case "VIEW_BOM":
        toggleBOM(true);
        toggleMoreInfo(false);
        break;
      case "VIEW_ASSIGNMENTS":
        toggleAssignmentsModal(true);
        toggleMoreInfo(false);
        break;
      case "MATERIALS_REJECTION_REPORT":
        toggleMaterialsReportModal(true);
        toggleMoreInfo(false);
        break;
      case "ARCHIVE":
        toggleMoreInfo(false);
        // TODO: can likely just update state to exclude the archived row in each case
        // instead of refetching data from api

        switch (currentTable) {
          case "JOBS":
            dispatch(handleArchiveJob(rowInfo.id)).then((res) => {
              if (res.error) return;

              setSelectedJobs(selectedJobs.filter((j) => j.id !== rowInfo.id));
              dispatch(handleFetchJobs(showAllWork, [])).then((res) => {
                if (!res.error) dispatch(handleSetJobsList(res));
              });
            });
            break;
          case "PACKAGES":
            let newSelectedJobs = selectedJobs;
            dispatch(handleArchivePackage(rowInfo.id)).then((res) => {
              return dispatch(handleFetchJobs(showAllWork, []))
                .then((newJobs) => {
                  if (!newJobs.error) {
                    newSelectedJobs = newJobs.filter(
                      (nj) => !!selectedJobs.find((sj) => sj.id === nj.id)
                    );
                    dispatch(handleSetJobsList(newJobs));
                  }
                  return res;
                })
                .then((res) => {
                  if (res.error) return;

                  const newSelectedPackages = selectedPackages.filter(
                    (p) => p.id !== rowInfo.id
                  );
                  setSelectedPackages(newSelectedPackages);
                  if (newSelectedPackages.length) {
                    dispatch(handleFetchPackages([], showAllWork)).then(
                      (res) => {
                        if (!res.error) {
                          setSelectedJobs(newSelectedJobs);
                          dispatch(handleSetPackagesList(res));
                        }
                      }
                    );
                  }
                });
            });
            break;
          case "DRAWINGS":
            dispatch(handleArchiveDrawing(rowInfo.id)).then((res) => {
              if (res.error) return;

              const newSelectedDrawings = selectedDrawings.filter(
                (drawing) => drawing.id !== rowInfo.id
              );
              setSelectedDrawings(newSelectedDrawings);
              if (newSelectedDrawings.length) {
                return dispatch(handleFetchDrawings([], showAllWork)).then(
                  (res) => {
                    if (!res.error) dispatch(handleSetDrawingsList(res));
                  }
                );
              }
            });
            break;
            // TODO: currently can't archive an item in the system
            // case "ITEMS":
            //   dispatch(handleArchiveItem(rowInfo.id)).then((res) => {
            //     if (res.error) return;

            //     setSelectedRows(selectedRows.filter((r) => r.id !== rowInfo.id));
            //     let groupableColumnsToSend = selectedGroupableColumns;

            //     if (
            //       selectedGrouping !== 0 &&
            //       materialNameObj &&
            //       !groupableColumnsToSend.find((c) => c.id === materialNameObj.id)
            //     ) {
            //       groupableColumnsToSend.push(materialNameObj);
            //     }
            //     const newSelectedGrouping = selectedGrouping !== 0 && selectedGroupableColumns.length
            //     ? selectedGrouping
            //     : 0;

            // fetch filtered stage data
            //     return dispatch(
            //       handleFetchWorkTableItems(
            //         selectedJobs.map((j) => j.id),
            //         selectedPackages.map((p) => p.id),
            //         selectedDrawings.map((d) => d.id),
            //         selectedStages.map((s) => s.id),
            //         showAllWork,
            //         newSelectedGrouping,
            //         groupableColumnsToSend
            //       )
            //     );
            // });
            break;
          default:
            break;
        }
        toggleConfirmationModal(false);
        return;
      case "DELETE":
        // TODO: can likely just update state to exclude the deleted row in each case
        // instead of refetching data from api
        toggleMoreInfo(false);

        switch (currentTable) {
          case "JOBS":
            dispatch(handleDeleteJob(rowInfo.id)).then((res) => {
              if (res.error) return;

              setSelectedJobs(selectedJobs.filter((j) => j.id !== rowInfo.id));
              dispatch(handleFetchJobs(showAllWork, [])).then((res) => {
                if (!res.error) dispatch(handleSetJobsList(res));
              });
            });
            break;
          case "PACKAGES":
            let newSelectedJobs = selectedJobs;

            dispatch(handleDeletePackage(rowInfo.id)).then((res) => {
              return dispatch(handleFetchJobs(showAllWork, []))
                .then((newJobs) => {
                  if (!newJobs.error) {
                    newSelectedJobs = newJobs.filter(
                      (nj) => !!selectedJobs.find((sj) => sj.id === nj.id)
                    );
                    dispatch(handleSetJobsList(newJobs));
                  }

                  return res;
                })
                .then((res) => {
                  if (res.error) return;

                  const newSelectedPackages = selectedPackages.filter(
                    (p) => p.id !== rowInfo.id
                  );
                  setSelectedPackages(newSelectedPackages);
                  dispatch(handleFetchPackages([], showAllWork)).then((res) => {
                    if (!res.error) {
                      setSelectedJobs(newSelectedJobs);
                      dispatch(handleSetPackagesList(res));
                    }
                  });
                });
            });
            break;
          case "DRAWINGS":
            dispatch(handleDeleteDrawings(rowInfo.id)).then((res) => {
              if (res.error) return;

              const newSelectedDrawings = selectedDrawings.filter(
                (drawing) => drawing.id !== rowInfo.id
              );
              setSelectedDrawings(newSelectedDrawings);
              if (newSelectedDrawings.length)
                return dispatch(handleFetchDrawings([], showAllWork)).then(
                  (res) => {
                    if (!res.error) dispatch(handleSetDrawingsList(res));
                  }
                );
            });
            break;
          case "ITEMS":
            dispatch(handleDeleteItem(rowInfo.id)).then((res) => {
              if (res.error) return;

              setSelectedRows(selectedRows.filter((r) => r.id !== rowInfo.id));
              let groupableColumnsToSend = selectedGroupableColumns;

              if (
                selectedGrouping !== 0 &&
                materialNameObj &&
                !groupableColumnsToSend.find((c) => c.id === materialNameObj.id)
              ) {
                groupableColumnsToSend.push(materialNameObj);
              }
              const newSelectedGrouping =
                selectedGrouping !== 0 && selectedGroupableColumns.length
                  ? selectedGrouping
                  : 0;

              return dispatch(
                handleFetchWorkTableItems(
                  selectedJobs.map((j) => j.id),
                  selectedPackages.map((p) => p.id),
                  selectedDrawings.map((d) => d.id),
                  selectedStages.map((s) => s.id),
                  showAllWork,
                  newSelectedGrouping,
                  groupableColumnsToSend
                )
              );
            });
            break;
          default:
            break;
        }
        toggleConfirmationModal(false);
        return;
      default:
        return;
    }
  };

  // used for the PDF files that we fetch from AWS S3 service
  const packageFilesRef = useRef(null);
  useEffect(() => {
    packageFilesRef.current = packageFiles;
  }, [packageFiles]);

  const drawingFilesRef = useRef(null);
  useEffect(() => {
    drawingFilesRef.current = drawingFiles;
  }, [drawingFiles]);

  // TODO - Consolidate to a reusable function, duplicated code!
  // common code for setting the package map as the file
  const getPackagePDFs = (packageId, updateFile = false) => {
    if (!packageId) return;
    const files = packageFilesRef?.current;
    if (!files?.hasOwnProperty(packageId)) {
      dispatch(handleFetchPackageFile(packageId)).then((res) => {
        if (res?.[packageId] && updateFile) {
          setFile(res[packageId]);
        }
      });
    } else if (updateFile) {
      // update the file if it already exists and we want to update...
      setFile(files[packageId]);
    }
    // else we exit because we already know the drawing package info
  };

  // TODO - Consolidate to a reusable function, duplicated code!
  const getDrawingPDFs = (id, rowData) => {
    if (!id) return;
    // if the file has no files to fetch, return (kept separate for logic simplicity)
    if (!(rowData.has_original ?? 0) && !(rowData.has_package_map ?? 0)) return;
    const files = drawingFilesRef?.current;
    if (files?.hasOwnProperty(id)) {
      // if drawing doesn't have original check for map
      if (files?.[id]?.original) {
        setFile(files[id].original);
      }
      // load package PDF and set if original was missing
      if (rowData.has_package_map) {
        getPackagePDFs(rowData.package_id, !files?.[id]?.original);
      }
    } else if (rowData?.has_original) {
      dispatch(handleFetchDrawingFiles(id)).then((res) => {
        // if drawing doesn't have original check for map
        if (res?.[id]?.original) {
          setFile(res[id].original);
        }
        // load package PDF and set if original was missing
        if (rowData.has_package_map) {
          getPackagePDFs(rowData.package_id, !res?.[id]?.original);
        }
      });
    } else if (rowData?.has_package_map) {
      // load package PDF and set
      getPackagePDFs(rowData.package_id, true);
    } // else do nothing... nothing to load
  };

  const setDisplayedPdf = (rowData) => {
    let id = rowData.drawing_id ?? rowData.id;
    switch (currentTable) {
      case "ITEMS": // rowData.drawing_id
        id = rowData.drawing_id;
      case "DRAWINGS":
        getDrawingPDFs(id, rowData);
        break;
      case "PACKAGES":
        if (!id || !rowData?.has_package_map) return;
        getPackagePDFs(id, true);
        break;
      default:
      // do nothing
    }
  };

  /**
   * Sets the PDF to be displayed specifically for drawings, either from drawing files or package maps.
   * This is a specialized version of setDisplayedPdf that only handles drawing PDFs and is used
   * primarily by the BOM Modal component.
   *
   * Key differences from setDisplayedPdf:
   * - Only handles drawings (doesn't handle packages or items)
   * - Takes either a drawing object or an item with drawing_id
   * - Logic flow focused just on drawings
   * - Used specifically by BOM Modal while setDisplayedPdf is used more generally
   *
   * @param {Object} rowData - The drawing data object or item with drawing_id
   * @param {number} rowData.drawing_id - Drawing ID if coming from an item
   * @param {number} rowData.id - Drawing ID if coming from a drawing object
   * @param {number} rowData.package_id - Package ID the drawing belongs to
   * @param {boolean} rowData.has_package_map - Whether drawing has an associated package map
   */
  const setDrawingDisplayedPdf = (rowData) => {
    let id = rowData?.drawing_id ? rowData?.drawing_id : rowData.id;
    getDrawingPDFs(id, rowData);
  };

  const moreInfoClick = (event, toggleMoreInfo, rowData) => {
    event.persist();
    setMoreInfoLocation({ x: event.clientX, y: event.clientY });
    setRowInfo(rowData); // ToDo - This is why data changes in the BOM modal pop
    setDisplayedPdf(rowData); // ToDo - Gets called again in the DrawingNameCellRenderer so API called twice...
    toggleMoreInfo(true);
  };

  const completionSubmit = useCallback(
    (all, type) => {
      toggleCompleting(true);
      const handleSubmission = () => {
        const direction =
          type === "UNCOMPLETE"
            ? 0
            : type === "COMPLETE"
            ? 1
            : rowInfo.completed
            ? 0
            : 1;

        // todo - validate the items filter works properly
        return dispatch(
          handleCompleteUncomplete(
            direction,
            selectedStages[0].id,
            rowInfo.work_item_ids ? rowInfo.work_item_ids : rowInfo.id,
            all
          )
        );
      };

      handleSubmission().then((res) => {
        if (
          typeof res === "string" ||
          (typeof res === "object" && !res.error)
        ) {
          toggleCompletionModal(false);
          let groupableColumnsToSend = selectedGroupableColumns;

          if (
            selectedGrouping !== 0 &&
            materialNameObj &&
            !groupableColumnsToSend.find((c) => c.id === materialNameObj.id)
          ) {
            groupableColumnsToSend.push(materialNameObj);
          }
          const newSelectedGrouping =
            selectedGrouping !== 0 && selectedGroupableColumns.length
              ? selectedGrouping
              : 0;

          dispatch(
            handleFetchWorkTableItems(
              selectedJobs.map((j) => j.id),
              selectedPackages.map((p) => p.id),
              selectedDrawings.map((d) => d.id),
              selectedStages.map((s) => s.id),
              showAllWork,
              newSelectedGrouping,
              groupableColumnsToSend
            )
          );
          toggleCompleting(false);
        }
      });
    },
    [
      rowInfo,
      selectedStages,
      selectedJobs,
      selectedPackages,
      selectedDrawings,
      showAllWork,
      selectedGrouping,
      selectedGroupableColumns,
    ]
  );

  const prioritiesStatus = useMemo(() => {
    if (currentTable !== "DRAWINGS") return false;
    if (!selectedJobs.length && !selectedPackages.length) return false;
    return true;
  }, [selectedJobs, selectedPackages, currentTable]);

  // This is used for refreshing the work table when mass updating items
  const selectedIdsInFilters = useMemo(() => {
    const jobIds =
      selectedJobs && selectedJobs.length && selectedJobs.map((j) => j.id);
    const packageIds =
      selectedPackages &&
      selectedPackages.length &&
      selectedPackages.map((p) => p.id);
    const drawingIds =
      selectedDrawings &&
      selectedDrawings.length &&
      selectedDrawings.map((d) => d.id);
    const stageIds =
      selectedStages &&
      selectedStages.length &&
      selectedStages.map((s) => s.id);

    return [
      jobIds || [],
      packageIds || [],
      drawingIds || [],
      stageIds || [],
      showAllWork,
    ];
  }, [
    selectedJobs,
    selectedPackages,
    selectedDrawings,
    selectedStages,
    showAllWork,
  ]);

  const priorityPermission = permissionLock([{ permissions: [278] }]);

  const isEditingRef = useRef(false);

  const onCellEditingStarted = (params) => {
    isEditingRef.current = true;
    setRowInfo(params.data);
  };

  const customColumnsRef = useRef(null);

  if (customColumns) {
    customColumnsRef.current = customColumns;
  }

  // TODO: make generic function to handle shared logic for j/p/d
  const onJobsCellValueChanged = (params) => {
    // check if we are inline editing and not editing via mass update
    if (!isEditingRef.current) {
      return;
    }

    // will populate if edited column was custom
    const customColumn =
      customColumnsRef?.current?.find(
        (col) => col.name === params.colDef.field && col.table_target === "jobs"
      ) || null;

    if (customColumn?.id) {
      return dispatch(
        handleUpdateCustomColumnData(
          "jobs",
          params.data.id,
          customColumn.id,
          params.newValue
        )
      );
    }

    if (params.colDef.field === "unix_target_date") {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const utcOffset =
        (moment.tz(timezone).utcOffset() -
          moment.tz("America/New_York").utcOffset()) *
        60;
      if (params.newValue + utcOffset === params.oldValue) return;
    }
    dispatch(
      handleUpdateJobs([params.data.id], {
        [params.colDef.field === "unix_target_date"
          ? "target_date"
          : params.colDef.field]: params.newValue,
      })
    ).then(async (res) => {
      const rowNode = params.api.getRowNode(params.data.id);

      if (res.error) {
        return rowNode.setData({
          ...rowNode.data,
          [params.colDef.field]: params.oldValue,
        });
      }

      // update redux store to reflect inline edit
      dispatch(handleUpdateJobsNoRefresh([rowNode.data]));
      // update selectedJobs to reflect inline edit
      if (selectedJobsRef.current?.length)
        handleSelectedFilterInlineEdit(
          selectedJobsRef.current,
          rowNode.data,
          setSelectedJobs
        );

      const customColumnData = await fetchCustomColumnDataPerItem(
        "jobs",
        params.data.id
      );

      const formattedCustomData = associateCustomColumnData(
        customColumnData,
        customColumns
      );

      const drawingCount =
        rowNode.data.assigned_drawings_count ||
        rowNode.data.drawings_count ||
        0;
      const workItemCount =
        params.data.assigned_work_item_count ||
        params.data.work_item_count ||
        0;

      rowNode.setData({
        ...res[0],
        ...formattedCustomData,
        work_item_count: workItemCount,
        drawings_count: drawingCount,
      });
    });
  };

  const onPackagesCellValueChanged = (params) => {
    // check if we are inline editing and not editing via mass update
    if (!isEditingRef.current) {
      return;
    }

    // will populate if edited column was custom
    const customColumn =
      customColumns.find(
        (col) =>
          col.name === params.colDef.field && col.table_target === "packages"
      ) || null;

    if (customColumn?.id) {
      return dispatch(
        handleUpdateCustomColumnData(
          "packages",
          params.data.id,
          customColumn.id,
          params.newValue
        )
      );
    }

    if (params.colDef.field === "due_date_unix") {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const utcOffset =
        (moment.tz(timezone).utcOffset() -
          moment.tz("America/New_York").utcOffset()) *
        60;
      if (params.newValue + utcOffset === params.oldValue) return;
    }
    dispatch(
      handleUpdatePackages([params.data.id], {
        [params.colDef.field === "due_date_unix"
          ? "due_date"
          : params.colDef.field]: params.newValue,
      })
    ).then(async (res) => {
      const rowNode = params.api.getRowNode(params.data.id);

      if (res.error)
        return rowNode.setData({
          ...rowNode.data,
          [params.colDef.field]: params.oldValue,
        });

      // update redux store to reflect inline edit
      dispatch(handleUpdatePackageNoRefresh(packages, rowNode.data));
      // update selectedPackages to reflect inline edit
      if (selectedPackagesRef.current?.length)
        handleSelectedFilterInlineEdit(
          selectedPackagesRef.current,
          rowNode.data,
          setSelectedPackages
        );

      if (selectedGrouping > 0) {
        return rowNode.setData(res[0]);
      }

      const customColumnData = await fetchCustomColumnDataPerItem(
        "packages",
        params.data.id
      );
      const formattedCustomData = associateCustomColumnData(
        customColumnData,
        customColumns
      );
      const rowData = { ...formattedCustomData, ...res[0] };

      rowNode.setData(rowData);
    });
  };

  const associateCustomColumnData = (data, customColumns) => {
    let result = {};

    for (let colData of data) {
      const columnObj = customColumns.find(
        (col) => col.id === colData.custom_columns_id
      );
      if (columnObj) result[columnObj.normal_name] = colData.data;
    }

    return result;
  };

  const onDrawingsCellValueChanged = (params, containers) => {
    // check if we are inline editing and not editing via mass update
    if (!isEditingRef.current) {
      return;
    }

    const customColumn =
      customColumns?.find(
        (col) =>
          col.name === params.colDef.field && col.table_target === "drawings"
      ) || null;

    let updateData = {
      [params.colDef.field === "name"
        ? "original_name"
        : params.colDef.field === "drawing_area"
        ? "area"
        : params.colDef.field === "container_name"
        ? "container_id"
        : params.colDef.field]:
        params.colDef.field === "container_name"
          ? (
              containers.current.find(
                (c) =>
                  c.name === params.newValue && c.job_id === params.data.job_id
              ) || {}
            ).id || null
          : params.newValue,
    };

    if (
      Object.values(updateData)[0] === undefined ||
      (params.newValue === undefined &&
        params.colDef.field === "container_name")
    )
      return;

    if (params.colDef.field === "container_name") {
      if (Object.values(updateData)[0] === null) {
        updateData = {
          ...updateData,
          stage_id: null,
          area: "work-items",
        };
      } else {
        updateData.area = "work-items";
      }
    }
    if (customColumn && hasCustomColumnFeature) {
      dispatch(
        handleUpdateCustomColumnData(
          "drawings",
          params.data.id,
          customColumn.id,
          params.newValue
        )
      );
    } else {
      dispatch(
        handleUpdateDrawings(
          [params.data.id],
          updateData,
          params.colDef.field === "container_name"
            ? Object.values(updateData)[0] === null
              ? "remove"
              : "add"
            : "update",
          params.colDef.field === "container_name"
            ? [[params.data.package_id], showAllWork]
            : null
        )
      ).then(async (res) => {
        if (res.error) return;
        const customColumnData = await fetchCustomColumnDataPerItem(
          "drawings",
          params.data.id
        );

        const formattedCustomData = associateCustomColumnData(
          customColumnData,
          customColumns
        );

        const rowData = { ...formattedCustomData, ...res[0] };

        const rowNode = params.api.getRowNode(params.data.id);
        rowNode.setData(rowData);

        // update redux store to reflect inline edit
        dispatch(handleUpdateDrawingsNoRefresh([rowNode.data]));
        // update selectedDrawings to reflect inline edit
        if (selectedDrawingsRef.current?.length)
          handleSelectedFilterInlineEdit(
            selectedDrawingsRef.current,
            rowNode.data,
            setSelectedDrawings
          );
      });
    }
  };

  // TODO: why not refresh the drawing they're looking at
  // todo why did we remove the options for fetching the drawings?
  const refreshPdfModal = () => {
    dispatch(handleFetchDrawings([], showAllWork)).then((res) => {
      if (res.error) return;

      dispatch(handleSetDrawingsList(res));
      setSelectedDrawings(
        res.filter((d) => selectedDrawings.find((i) => i.id === d.id))
      );
      setRowInfo(res.find((d) => d.id === rowInfo.id));
    });
  };

  const handleJointUpdate = (newValue, itemIds) => {
    const updatedHeatNumber = {
      work_item_ids: itemIds,
      position: newValue.position,
      heat_number: newValue.heat_number,
    };
    return dispatch(
      handleUpdateJointHeatNumber(updatedHeatNumber, selectedIdsInFilters)
    );
  };

  const setForgeInfo = async (forgeModelId, selectedJobs) => {
    try {
      const forgeModelInfo = await dispatch(
        handleFetchForgeModelInfo(forgeModelId)
      );

      if (forgeModelInfo && !forgeModelInfo.error && forgeModelInfo.length) {
        setRowInfo({
          ...selectedJobs[0],
          forge_urn: forgeModelInfo[0].urn,
          model_name: forgeModelInfo[0].model_name,
        });
        toggleForgeViewer(true);
      }
    } catch (error) {
      console.log(error);
    }
  };
  /*--- END: Grid data change/load functions ---*/

  /*--- START: Grid data change/load hooks ---*/

  useEffect(() => {
    if (currentTable === "JOBS" && rowInfo)
      dispatch(handleFetchForgeModelsByJob(rowInfo.id));
  }, [currentTable, rowInfo]);

  useEffect(() => {
    // if items are fetched, table filters should exist at least on stage
    if (!isItemsLoading && currentTable === "ITEMS" && items.length) {
      const job_ids = [],
        package_ids = [],
        drawing_ids = [];
      // todo - can we do this without the if statements and for sets
      items.map((i) => {
        if (!job_ids.includes(i.job_id)) job_ids.push(i.job_id);
        if (!package_ids.includes(i.package_id)) package_ids.push(i.package_id);
        if (!drawing_ids.includes(i.drawing_id)) drawing_ids.push(i.drawing_id);
      });
      setStageToEntityMap({ job_ids, package_ids, drawing_ids });
      updateAllFiltersSet(true);
    }
    // no need to filter again here because the filters will be update if the grouping is pulled
  }, [items]);

  useEffect(() => {
    // send error if no data for items
    // todo need to figure out how to check if this is the initial page load or if there really are NO filters
    if (
      selectedStages.length > 0 &&
      !isItemsLoading &&
      filteredItems.length === 0
    ) {
      dispatch(
        notify({
          id: Math.round(Date.now() / Math.pow(10, 5)), // was attempting to get less notifications
          type: "ERROR",
          message: "No items found.",
        })
      );
    }
  }, [filteredItems]);

  /*--- END: Grid data change/load hooks ---*/

  let DrawingsTableComponent = null;
  if (currentTable === "DRAWINGS") {
    if (features.includes(SystemFeatures.NEW_TABLES_POC)) {
      DrawingsTableComponent = (
        <DrawingsTableV2ContextProvider>
          <DrawingsTableV2
            showAllWork={showAllWork}
            toggleShowAllWork={handleToggleShowAllWork}
            setStatusTrackerPopup={setStatusTrackerPopup}
            selectedDrawings={selectedDrawings}
            toggleMoreInfo={toggleMoreInfo}
            moreInfoClick={moreInfoClick}
            rowInfo={rowInfo}
            selectedRows={selectedRows}
            setSelectedRows={setSelectedRows}
            setRowInfo={setRowInfo}
            showWorkflowModal={showWorkflowModal}
            toggleWorkflowModal={toggleWorkflowModal}
            showScroll={showScroll}
            togglePDFViewer={() => togglePDFViewer(true)}
            setFile={setFile}
            setCurrentRowOrder={setCurrentRowOrder}
            handleSaveSortState={handleSaveSortState}
            sortState={sortState}
            storeDrawingsGridOptionsApi={storeDrawingsGridOptionsApi}
            onDrawingsCellValueChanged={onDrawingsCellValueChanged}
            onCellEditingStarted={onCellEditingStarted}
            drawingCustomColumns={
              hasCustomColumnFeature ? formattedCustomColumns : []
            }
            associateCustomColumnData={associateCustomColumnData}
            setDisplayedPdf={setDisplayedPdf}
          />
        </DrawingsTableV2ContextProvider>
      );
    } else {
      DrawingsTableComponent = (
        <DrawingsTable
          showAllWork={showAllWork}
          toggleShowAllWork={handleToggleShowAllWork}
          setStatusTrackerPopup={setStatusTrackerPopup}
          selectedDrawings={selectedDrawings}
          toggleMoreInfo={toggleMoreInfo}
          moreInfoClick={moreInfoClick}
          rowInfo={rowInfo}
          selectedRows={selectedRows}
          setSelectedRows={setSelectedRows}
          setRowInfo={setRowInfo}
          showWorkflowModal={showWorkflowModal}
          toggleWorkflowModal={toggleWorkflowModal}
          showScroll={showScroll}
          togglePDFViewer={() => togglePDFViewer(true)}
          setFile={setFile}
          setCurrentRowOrder={setCurrentRowOrder}
          handleSaveSortState={handleSaveSortState}
          sortState={sortState}
          storeDrawingsGridOptionsApi={storeDrawingsGridOptionsApi}
          onDrawingsCellValueChanged={onDrawingsCellValueChanged}
          onCellEditingStarted={onCellEditingStarted}
          drawingCustomColumns={
            hasCustomColumnFeature ? formattedCustomColumns : []
          }
          associateCustomColumnData={associateCustomColumnData}
          setDisplayedPdf={setDisplayedPdf}
          setSelectedDrawings={setSelectedDrawings}
        />
      );
    }
  }

  // Used to update the state of selected filters (jobs/packages/drawings table filters) after an inline edit. This will keep the table data fresh between refreshes. (called inside on[level]CellValueChanged events)
  const handleSelectedFilterInlineEdit = (
    currentSelectedItems,
    updatedSelectedItem,
    callback
  ) => {
    const updateIndex = currentSelectedItems.findIndex(
      (item) => item.id === updatedSelectedItem.id
    );

    if (updateIndex !== -1) {
      currentSelectedItems[updateIndex] = updatedSelectedItem;
    }

    callback(currentSelectedItems);
  };

  return (
    <>
      <div className="work-table-filter">
        <Filter
          nameKey="job_name"
          idKey="job_number"
          type="Jobs"
          list={jobsDisplayedOptions}
          selected={selectedJobs}
          setSelected={handleJobFilterSelection}
          handleParentSelect={setSelectedJobs}
          selectAll={true}
          orientation="HORIZONTAL"
          smallView={true}
          allSelected={jobsAllSelected}
          searchInput={jobsSearchInput}
          setSearchInput={setJobsSearchInput}
          isDisabled={
            isJobsLoading ||
            isPackagesLoading ||
            isDrawingsLoading ||
            isItemsLoading
          }
        />
        <Filter
          nameKey="package_name"
          idKey="id"
          type="Packages"
          list={packagesDisplayedOptions}
          setSelected={handlePackageFilterSelection}
          handleParentSelect={setSelectedPackages}
          toggleAllSelections={(bool) => {
            setPackagesSelectAll(bool ? 1 : 0);
            if (!bool) setDrawingsSelectAll(0);
          }}
          selected={selectedPackages || []}
          selectAll={true}
          orientation="HORIZONTAL"
          smallView={true}
          allSelected={packagesAllSelected}
          searchInput={packagesSearchInput}
          setSearchInput={setPackagesSearchInput}
          isDisabled={
            isJobsLoading ||
            isPackagesLoading ||
            isDrawingsLoading ||
            isItemsLoading
          }
        />
        <span
          style={
            selectedPackages && selectedPackages.length > 0
              ? {}
              : { opacity: 0, cursor: "default" }
          }
        >
          <Filter
            nameKey="name"
            type="Drawings"
            list={drawingsDisplayedOptions}
            setSelected={handleDrawingFilterSelection}
            handleParentSelect={setSelectedDrawings}
            toggleAllSelections={(bool) => setDrawingsSelectAll(bool ? 1 : 0)}
            selected={selectedDrawings || []}
            selectAll={true}
            orientation="HORIZONTAL"
            smallView={true}
            allSelected={drawingsAllSelected}
            searchInput={drawingsSearchInput}
            setSearchInput={setDrawingsSearchInput}
            isDisabled={
              isJobsLoading ||
              isPackagesLoading ||
              isDrawingsLoading ||
              isItemsLoading
            }
          />
        </span>
        <Filter
          nameKey="name"
          type="Stages"
          list={jobsDisplayedOptions.length ? stagesDisplayedOptions : []}
          selected={selectedStages}
          selectAll={true}
          setSelected={handleStageFilterSelection}
          handleParentSelect={setSelectedStages}
          toggleAllSelections={(bool) => setStagesSelectAll(bool ? 1 : 0)}
          orientation="HORIZONTAL"
          smallView={true}
          allSelected={workStagesAllSelected}
          searchInput={stagesSearchInput}
          setSearchInput={setStagesSearchInput}
          isDisabled={
            isJobsLoading ||
            isPackagesLoading ||
            isDrawingsLoading ||
            isItemsLoading
          }
        />
        <Button
          onClick={clearAllFilters}
          className="clear-all-filters"
          disabled={
            isJobsLoading ||
            isPackagesLoading ||
            isDrawingsLoading ||
            isItemsLoading
          }
        >
          Clear All Filters
        </Button>
      </div>
      <div className="action-row">
        <div className="current-table-message">
          <p>{`${currentTable.slice(0, 1)}${currentTable
            .slice(1)
            .toLowerCase()}`}</p>
        </div>
        <span className="action-buttons">
          {currentTable === "JOBS" && permissions && permissions.includes(279) && (
            <Button
              className="create-job-button"
              onClick={() =>
                window.location.assign(
                  `${process.env.REACT_APP_FABPRO}/new-job/`
                )
              }
            >
              {translate("Create Job")}
            </Button>
          )}
          {(currentTable === "DRAWINGS" || currentTable === "PACKAGES") &&
            permissions &&
            permissions.includes(18) && (
              <Button
                className={`create-labels-button ${
                  !selectedRows.length ? "disabled" : ""
                }`}
                onClick={() => toggleShowLabelsModal(true)}
                disabled={!selectedRows.length}
              >
                {translate("Create Labels")}
              </Button>
            )}
          {currentTable === "ITEMS" &&
            selectedStages.length === 1 &&
            selectedStages[0].id !== 0 &&
            selectedStages[0].groupable === 1 && (
              <div className="grouping-dropdown">
                <Select
                  options={displayedGroupingOptions}
                  placeholder={translate("-- Grouping --")}
                  value={selectedGrouping.toString()}
                  onInput={(e) =>
                    changeSelectedGrouping(parseInt(e.target.value))
                  }
                />
                {selectedGrouping !== 0 && (
                  <Filter
                    nameKey="display_name"
                    idKey="id"
                    type="Grouping Columns"
                    list={displayedGroupingColumnOptions}
                    selected={selectedGroupableColumns || []}
                    setSelected={selectGroupableColumns}
                    handleParentSelect={selectGroupableColumns}
                    orientation="HORIZONTAL"
                    selectAll
                    smallView
                    alwaysCollapsed
                    requiredSelectedItem={materialNameObj}
                  />
                )}
              </div>
            )}

          {currentTable === "DRAWINGS" &&
            priorityPermission &&
            priorityPermission.length > 0 && (
              <button
                disabled={!prioritiesStatus}
                className={!prioritiesStatus ? `disabled` : null}
                onClick={() => togglePriorities(true)}
              >
                <MdLowPriority />
                {translate("Priorities")}
              </button>
            )}
          {currentTable === "DRAWINGS" && (
            <>
              <button
                onClick={() => toggleMoveDrawingsModal(true)}
                disabled={!selectedRows?.length}
                className={!selectedRows?.length ? "disabled" : ""}
              >
                Move Drawings
              </button>
              <DownloadMAJsButton
                selectedDrawings={selectedRows}
                className={
                  !selectedRows?.some((d) => d.has_maj) ? "disabled" : ""
                }
              />
            </>
          )}
          <button
            onClick={() => toggleMassUpdate(true)}
            className={!selectedRows || !selectedRows.length ? "disabled" : ""}
            disabled={!selectedRows || !selectedRows.length}
          >
            <FiLayers />
            {translate("Mass Update")}
          </button>
          {currentTable === "PACKAGES" &&
            permissions &&
            permissions.includes(101) && (
              <TooltipArchiveButton tooltipText={"Select up to 15 packages."} />
            )}
        </span>
      </div>
      {allFiltersSet && sortState && (
        <>
          {currentTable === "JOBS" && (
            <JobsTable
              moreInfoClick={moreInfoClick}
              selectedStages={selectedStages}
              selectedJobs={selectedJobs}
              showAllWork={showAllWork}
              toggleShowAllWork={handleToggleShowAllWork}
              setSelectedJobs={setSelectedJobs}
              setCurrentTable={setCurrentTable}
              toggleMoreInfo={toggleMoreInfo}
              selectedRows={selectedRows}
              setSelectedRows={setSelectedRows}
              showScroll={showScroll}
              setCurrentRowOrder={setCurrentRowOrder}
              handleSaveSortState={handleSaveSortState}
              sortState={sortState}
              storeJobsGridOptionsApi={storeJobsGridOptionsApi}
              onJobsCellValueChanged={onJobsCellValueChanged}
              onCellEditingStarted={onCellEditingStarted}
              handlePackageFilterSelection={(newSelected, selectedJobsToUse) =>
                handlePackageFilterSelection(
                  newSelected,
                  false,
                  selectedJobsToUse
                )
              }
              handleDrawingFilterSelection={(
                newSelected,
                selectedPackagesToUse,
                selectedJobsToUse
              ) =>
                handleDrawingFilterSelection(
                  newSelected,
                  false,
                  selectedPackagesToUse,
                  selectedJobsToUse
                )
              }
              customColumns={formattedCustomColumns}
            />
          )}
          {currentTable === "PACKAGES" && (
            <PackagesTable
              showAllWork={showAllWork}
              selectedPackages={selectedPackages}
              toggleShowAllWork={handleToggleShowAllWork}
              setStatusTrackerPopup={setStatusTrackerPopup}
              toggleMoreInfo={toggleMoreInfo}
              moreInfoClick={moreInfoClick}
              rowInfo={rowInfo}
              showPriorities={showPriorities}
              setSelectedPackages={setSelectedPackages}
              setFile={setFile}
              selectedRows={selectedRows}
              setSelectedRows={setSelectedRows}
              setRowInfo={setRowInfo}
              showWorkflowModal={showWorkflowModal}
              toggleWorkflowModal={toggleWorkflowModal}
              showScroll={showScroll}
              handleSaveSortState={handleSaveSortState}
              sortState={sortState}
              showBOM={showBOM}
              storePackagesGridOptionsApi={storePackagesGridOptionsApi}
              onPackagesCellValueChanged={onPackagesCellValueChanged}
              onCellEditingStarted={onCellEditingStarted}
              handleDrawingFilterSelection={(
                newSelected,
                selectedPackagesToUse
              ) =>
                handleDrawingFilterSelection(
                  newSelected,
                  false,
                  selectedPackagesToUse
                )
              }
              customColumns={formattedCustomColumns}
              setDisplayedPdf={setDisplayedPdf}
            />
          )}
          {DrawingsTableComponent}
          {currentTable === "ITEMS" && (
            <ItemsTable
              selectedRows={selectedRows}
              showAllWork={showAllWork}
              toggleShowAllWork={handleToggleShowAllWork}
              setStatusTrackerPopup={setStatusTrackerPopup}
              selectedStages={selectedStages}
              setSelectedRows={setSelectedRows}
              toggleMoreInfo={toggleMoreInfo}
              moreInfoClick={moreInfoClick}
              rowInfo={rowInfo}
              showScroll={showScroll}
              storeItemsGridOptionsApi={storeItemsGridOptionsApi}
              groupingColumns={selectedGroupableColumns || []}
              selectedGrouping={selectedGrouping}
              setRowInfo={setRowInfo}
              toggleHeatNumbersModal={toggleHeatNumberModal}
              toggleMaterialTypesModal={toggleMaterialTypesModal}
              toggleJoiningProceduresModal={toggleJoiningProceduresModal}
              toggleLaydownLocationsModal={toggleLaydownLocationsModal}
              selectedJobs={selectedJobs}
              selectedPackages={selectedPackages}
              selectedDrawings={selectedDrawings}
              // selectedGroupableColumns={selectedGroupableColumns || []}
              togglePDFViewer={() => togglePDFViewer(true)}
              setCurrentRowOrder={setCurrentRowOrder}
              setFile={setFile}
              handleSaveSortState={handleSaveSortState}
              sortState={sortState}
              displayedColumns={displayedColumns}
              setDisplayedPdf={setDisplayedPdf}
            />
          )}
        </>
      )}
      {showMoreInfo && moreInfoLocation && (
        <MoreInfoDropdown
          moreInfoLocation={moreInfoLocation}
          onOptionClick={onOptionClick}
          currentTable={currentTable}
          rowInfo={rowInfo}
          toggleMoreInfo={toggleMoreInfo}
          // toggleMoreInfoModal={toggleMoreInfoModal}
          togglePDFViewer={() => togglePDFViewer(true)}
          toggleForgeViewer={() => toggleForgeViewer(true)}
          toggleConfirmationModal={toggleConfirmationModal}
          toggleCompletionModal={toggleCompletionModal}
          toggleWorkflowModal={toggleWorkflowModal}
          toggleRejectionModal={toggleRejectionModal}
          selectedStages={selectedStages}
          toggleRejectionHistoryModal={toggleRejectionHistoryModal}
          toggleAssignmentsModal={toggleAssignmentsModal}
          toggleMaterialsReportModal={toggleMaterialsReportModal}
          toggleForgeModelSelector={() => toggleForgeModelSelector(true)}
          forgeModelsByJob={forgeModelsByJob}
          setSubmitRevisionInfo={setSubmitRevisionInfo}
          toggleManageMAJModal={() => toggleManageMAJModal(true)}
          setDisplayedPdf={setDisplayedPdf}
        />
      )}
      {pdfViewer && rowInfo && (
        <PDFModal
          pdfViewer={pdfViewer}
          togglePDFViewer={handleClosePDFViewer}
          selectedItem={rowInfo}
          itemId={
            // if item level and showing drawing, send drawing id
            !["DRAWINGS", "PACKAGES"].includes(currentTable) || showBOM
              ? rowInfo.drawing_id
              : rowInfo.id
          }
          itemType={
            !showBOM && currentTable === "PACKAGES" ? "PACKAGE" : "DRAWING"
          }
          file={file}
          setFile={setFile}
          currentRowOrder={
            currentTable === "PACKAGES" || showBOM ? false : currentRowOrder
          }
          setCurrentRowOrder={setCurrentRowOrder}
          refresh={
            // if you're viewing a package map, bom, or do not have selected drawings - dummy function
            /map/.test(file) || selectedDrawings?.length < 1 || showBOM
              ? (f) => f
              : refreshPdfModal // if you have drawings selected...
          }
          area="VIEWER"
          setSelectedItem={setRowInfo}
          setDisplayedPdf={setDisplayedPdf}
        />
      )}
      {showMoreInfoModal && rowInfo && (
        <MoreInfoModal
          title={rowInfo[modalTitle[currentTable]]}
          showModal={showMoreInfoModal}
          onClose={() => toggleMoreInfoModal(false)}
          type={currentTable}
          rowInfo={rowInfo}
          togglePDFViewer={() => togglePDFViewer(true)}
          toggleForgeViewer={() => toggleForgeViewer(true)}
        />
      )}
      {["JOBS", "PACKAGES"].includes(currentTable) && forgeModelSelector && (
        <ForgeModelSelector
          forgeModelSelector={forgeModelSelector}
          toggleForgeModelSelector={toggleForgeModelSelector}
          toggleForgeViewer={toggleForgeViewer}
          rowInfo={rowInfo}
          setRowInfo={setRowInfo}
          currentTable={currentTable}
          forgeModelsByJob={forgeModelsByJob}
        />
      )}
      {forgeViewer && (
        <ForgeModal
          forgeViewer={forgeViewer}
          toggleForgeViewer={() => {
            if (["JOBS", "PACKAGES"].includes(currentTable))
              toggleForgeModelSelector(true);
            toggleForgeViewer(false);
          }}
          selectedItem={rowInfo}
          type={currentTable}
          sheetCreation
          gridOptionsApi={drawingsGridOptionsApi}
          setCurrentRowOrder={setCurrentRowOrder}
          area="VIEWER"
          refresh={currentTable === "PACKAGES" ? (f) => f : refreshPdfModal}
        />
      )}
      {showPriorities && (
        <PrioritiesModal
          showModal={showPriorities}
          toggleModal={(updatedPriorities) => {
            if (drawingsGridOptionsApi) {
              for (let i = 0; i < updatedPriorities.length; i++) {
                const rowNode = drawingsGridOptionsApi.getRowNode(
                  updatedPriorities[i].id
                );

                if (rowNode)
                  rowNode.setDataValue(
                    "priority",
                    updatedPriorities[i].new_priority
                  );
              }
            }
            togglePriorities(false);
          }}
          initialJobs={selectedJobs}
          initialPackages={selectedPackages}
          initialDrawings={selectedDrawings}
          showAllWork={showAllWork}
        />
      )}
      {showBOM && (
        <BOMModal
          showModal={showBOM}
          onClose={() => {
            if (!pdfViewer) toggleBOM(false);
          }}
          rowInfo={rowInfo}
          currentTable={currentTable}
          // toggleHeatNumberModal={toggleHeatNumberModal}
          togglePDFViewer={() => togglePDFViewer(true)}
          moreInfoClick={moreInfoClick}
          setCurrentRowOrder={setCurrentRowOrder}
          selectedFilterIds={selectedIdsInFilters}
          setDisplayedPdf={setDrawingDisplayedPdf}
        />
      )}
      {showMassUpdate && (
        <MassUpdate
          currentTable={currentTable}
          showModal={showMassUpdate}
          toggleModal={toggleMassUpdate}
          clearTableData={clearTableData}
          setSelectedJobs={setSelectedJobs}
          editedFields={editedFields}
          setEditedFields={setEditedFields}
          selectedRows={selectedRows}
          setSelectedRows={setSelectedRows}
          selectedIdsInFilters={selectedIdsInFilters}
          displayedStageColumns={displayedColumns}
          handleJointUpdate={handleJointUpdate}
          showAllWork={showAllWork}
          selectedStages={selectedStages}
          selectedJobs={selectedJobs}
          selectedPackages={selectedPackages}
          selectedDrawings={selectedDrawings}
          selectedGrouping={selectedGrouping}
          selectedGroupableColumns={selectedGroupableColumns || []}
          jobsGridOptionsApi={jobsGridOptionsApi}
          packagesGridOptionsApi={packagesGridOptionsApi}
          drawingsGridOptionsApi={drawingsGridOptionsApi}
          itemsGridOptionsApi={itemsGridOptionsApi}
          setSelectedPackages={setSelectedPackages}
          setSelectedDrawings={setSelectedDrawings}
          rowInfo={rowInfo}
        />
      )}
      {confirmationModal && (
        <ConfirmationModal
          showModal={!!confirmationModal}
          toggleModal={() => toggleConfirmationModal(null)}
          action={confirmationModal.action}
          item={confirmationModal.item}
          handleClick={() => onOptionClick(confirmationModal.action)}
        />
      )}
      {showCompletionModal && (
        <CompletionModal
          open={showCompletionModal}
          handleClose={() => toggleCompletionModal(false)}
          handleSubmit={(all, type) => completionSubmit(all, type)}
          selectedItem={rowInfo}
          selectedStage={selectedStages[0]}
          completing={completing}
        />
      )}
      {statusTrackerPopup && (
        <StatusTrackerPopup selectedItem={statusTrackerPopup} />
      )}
      {showRejectionModal && (
        <RejectionModal
          open={showRejectionModal}
          handleClose={() => toggleRejectionModal(false)}
          translate={translate}
          rowInfo={rowInfo}
          selectedStage={selectedStages[0]}
          postRejectionSuccess={(newData) => {
            if (itemsGridOptionsApi) {
              const node = itemsGridOptionsApi.getRowNode(newData.id);
              if (node) {
                node.setData(newData);
                itemsGridOptionsApi.redrawRows([node]);
              }
            }
          }}
        />
      )}
      {showRejectionHistoryModal && (
        <RejectionHistoryModal
          open={showRejectionHistoryModal}
          handleClose={() => toggleRejectionHistoryModal(false)}
          translate={translate}
          rowInfo={rowInfo}
          updateMainTable={(dataObj) => {
            const node = itemsGridOptionsApi.getRowNode(dataObj.id);

            if (node) {
              node.setData(dataObj);
              itemsGridOptionsApi.redrawRows([node]);
            }
          }}
        />
      )}
      {(currentTable === "DRAWINGS" || currentTable === "PACKAGES") &&
        showLabelsModal && (
          <LabelsModal
            open={showLabelsModal}
            handleClose={() => toggleShowLabelsModal(false)}
            translate={translate}
            currentTable={currentTable}
            selectedRows={selectedRows}
          />
        )}
      {showHeatNumberModal && (
        <HeatNumberModal
          open={showHeatNumberModal}
          handleClose={() => toggleHeatNumberModal(false)}
          rowInfo={rowInfo}
          heatNumbers={heatNumbers}
          gridOptionsApi={itemsGridOptionsApi}
          translate={translate}
          selectedStages={selectedStages}
        />
      )}
      {showMaterialTypesModal && (
        <CreateSelectModal
          items={materialTypes}
          rowInfo={rowInfo}
          open={showMaterialTypesModal}
          handleClose={() => toggleMaterialTypesModal(false)}
          gridOptionsApi={itemsGridOptionsApi}
          isGrouped={selectedGrouping === 0 ? false : true}
          quantity={rowInfo.quantity}
          tableType="JOBS"
          handleUpdateItem={handleUpdateItems}
          idKey="material_type_id"
          nameKey="material_name"
          selectedFilterIds={selectedIdsInFilters}
          groupingColumns={selectedGroupableColumns}
        />
      )}
      {showJoiningProceduresModal && (
        <CreateSelectModal
          items={joiningProcedures}
          rowInfo={rowInfo}
          open={showJoiningProceduresModal}
          handleClose={() => toggleJoiningProceduresModal(false)}
          gridOptionsApi={itemsGridOptionsApi}
          isGrouped={selectedGrouping === 0 ? false : true}
          quantity={rowInfo.quantity}
          tableType="JOBS"
          handleUpdateItem={handleUpdateItems}
          idKey="joining_procedure_id"
          nameKey="joining_procedure_name"
          selectedFilterIds={selectedIdsInFilters}
          groupingColumns={selectedGroupableColumns}
        />
      )}
      {showLaydownLocationsModal && (
        <CreateSelectModal
          items={laydown_locations}
          rowInfo={rowInfo}
          open={showLaydownLocationsModal}
          handleClose={() => toggleLaydownLocationsModal(false)}
          gridOptionsApi={itemsGridOptionsApi}
          isGrouped={selectedGrouping === 0 ? false : true}
          quantity={rowInfo.quantity}
          tableType="JOBS"
          handleUpdateItem={handleUpdateItems}
          idKey="laydown_location_id"
          nameKey="laydown_location_name"
          selectedFilterIds={selectedIdsInFilters}
          groupingColumns={selectedGroupableColumns}
        />
      )}
      {showAssignmentsModal && (
        <AssignmentsModal
          handleClose={() => toggleAssignmentsModal(false)}
          selectedItem={rowInfo}
          currentTable={currentTable}
          open={showAssignmentsModal}
        />
      )}
      {showMaterialsReportModal && (
        <MaterialRejectionReport
          rowInfo={rowInfo}
          showModal={showMaterialsReportModal}
          handleClose={() => toggleMaterialsReportModal(false)}
        />
      )}
      {submitRevisionInfo && (
        <SubmitRevisionModal
          submitRevisionInfo={submitRevisionInfo}
          handleClose={() => setSubmitRevisionInfo(null)}
          updateRow={(updatedInfo) => {
            if (drawingsGridOptionsApi) {
              const rowNode = drawingsGridOptionsApi.getRowNode(
                submitRevisionInfo.id
              );
              if (rowNode) {
                rowNode.setData({ ...rowNode.data, ...updatedInfo });
                // reset row info and row order to make
                // sure it doesn't try to show annotated.pdf
                setRowInfo(updatedInfo);
                setCurrentRowOrder(null);
                drawingsGridOptionsApi.redrawRows([rowNode]);
              }
            }
          }}
        />
      )}
      {showMoveDrawingsModal && (
        <MoveDrawingsModal
          drawingsToMove={selectedRows}
          clearSelectedRows={() => setSelectedRows([])}
          selectedDrawings={selectedDrawings}
          selectedDrawingsInFilter={selectedDrawings}
          setSelectedDrawingsInFilter={setSelectedDrawings}
          showModal={showMoveDrawingsModal}
          toggleModal={toggleMoveDrawingsModal}
          selectedFilterIds={selectedIdsInFilters}
          setCurrentTable={setCurrentTable}
        />
      )}
      {showManageMAJModal && (
        <ManageMAJModal
          drawingOrPackage={rowInfo}
          showModal={showManageMAJModal}
          handleClose={() => toggleManageMAJModal(false)}
          currentTable={currentTable}
        />
      )}
    </>
  );
};

export default WorkTable;
