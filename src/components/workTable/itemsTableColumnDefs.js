// NPM PACKAGE IMPORTS
import moment from "moment";
import "moment-timezone";

// REDUX IMPORTS
import store from "../../redux/store";

// CONSTANTS
import {
  objectColumns,
  transformDrawingPriorityToRank,
  generateTime,
  naturalSort,
  objectColumnDefs,
  decimalRegex,
  multidimensionOnlyX,
  multidimensionRegex,
  fractionalRegex,
  convertFracToDec,
} from "../../_utils";

const requiredItemColumnsOnCreation = ["Material Name", "Drawing Name"];

export const itemsTableColumnDefs = (
  savedColumnState,
  workStageColumns,
  groupingType,
  sortState,
  moreInfoClick,
  toggleMoreInfo,
  togglePDFViewer,
  // inline editing data
  containers,
  materialTypes,
  handleLaydownLocations,
  handleJoiningProcedures,
  heatNumbers,
  displayNotification,
  testStore,
  items,
  hasCustomColumnFeature,
  tableType = "JOBS",
  drawings,
  toggleDrawingValid,
  toggleMaterialValid,
  isNewRow,
  setDisplayedPdf
) => {
  const {
    systemSettings,
    features,
    permissions,
  } = store.getState().profileData;
  let dateFormatting = systemSettings && systemSettings.date_display;

  if (!workStageColumns?.length) return [];

  let columns = [];

  for (let i = 0; i < workStageColumns.length; i++) {
    let col = workStageColumns[i];

    // Skip joint heat number columns (will be added later) and bend columns if necessary features are inactive
    if (
      col?.display_name?.includes("Joint Heat Number") ||
      (col?.normal_name?.includes("bend") &&
        !features.includes(37) &&
        !features.includes(38))
    )
      continue;

    // Skip custom columns if in Wizard
    if (tableType === "WIZARD_ITEMS" && col.is_custom) {
      continue;
    }

    let columnDef = {
      autoHeight: true,
      suppressSizeToFit: false,
      headerName: col.display_name,
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        if (col.data_type === "decimal") {
          if (!decimalRegex.test(params.newValue)) {
            displayNotification("Value must be a number");
            return params.oldValue;
          }
        }

        return params.newValue;
      },
      cellClass: "wrap-text",
      minWidth: 125,
      editable: col.editable ? true : false,
      sortable: col.is_custom ? false : true,
      pinned:
        tableType === "WIZARD_ITEMS" &&
        requiredItemColumnsOnCreation.includes(col.display_name)
          ? "left"
          : null,
      getQuickFilterText: (params) =>
        // updated because if value is missing in dataset then the display is not available
        params.data[col.normal_name] && objectColumns.includes(col.normal_name)
          ? params.data[col.normal_name].display
          : params.data[col.normal_name],
      menuTabs: ["filterMenuTab"],
      filterParams: {
        buttons: ["reset"],
        suppressAndOrCondition: true,
      },
      filter: "agTextColumnFilter",
      colId: col.normal_name,
      sort:
        sortState.sorting_column_name === col.normal_name
          ? sortState.sorting_method
          : null,
    };

    // need field present to be able to export heat numbers
    // wizard doesn't allow export and generates two notifications for heat number if set
    if (tableType !== "WIZARD_ITEMS" || col.normal_name !== "heat_number")
      columnDef.field = col.normal_name;

    // don't add valueGetter to length columnDef because it will break inline editing
    if (col.normal_name !== "length") {
      columnDef.valueGetter = (
        params // updated because if value is missing in dataset then the display is not available
      ) => {
        if (!params.data) return;

        return params.data?.[col.normal_name] &&
          objectColumns.includes(col.normal_name)
          ? params.data[col.normal_name].display
          : params.data[col.normal_name];
      };
    }

    if (col.data_type === "integer") {
      columnDef.filter = "agNumberColumnFilter";
    }
    if (col.data_type === "string") {
      columnDef.filter = "agTextColumnFilter";
      columnDef.comparator = (valueA, valueB) => {
        return valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1;
      };
    }
    if (col.data_type === "date") {
      columnDef.filter = "agDateColumnFilter";
      columnDef.filterParams = {
        buttons: ["reset"],
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = cellValue
            ? typeof cellValue === "number"
              ? new Date(generateTime(cellValue * 1000, false, true, "-"))
              : new Date(cellValue)
            : "-";

          return cellDate < filterLocalDateAtMidnight
            ? -1
            : cellDate > filterLocalDateAtMidnight
            ? 1
            : 0;
        },
      };
      columnDef.getQuickFilterText = (params) => {
        let value;
        const date = params.data?.[col.normal_name]
          ? new Date(params.data?.[col.normal_name])
          : null;

        const timezone = Intl.DateTimeFormat().resolvedOptions().timezone;
        if (!date) value = "N/A";
        else value = moment.tz(date, timezone).format(dateFormatting);
        return value;
      };

      // TODO - update once we standardize date handling
      if (col.is_custom) {
        columnDef.valueFormatter = (params) => {
          return params.value
            ? typeof params.value === "number"
              ? generateTime(params.value * 1000, false, true, "-", testStore)
              : params.value
            : "-";
        };
      } else {
        columnDef.valueFormatter = (params) => {
          // Due dates come from API as unix time in seconds
          // But this will also handle ISO as well
          if (!params.value) return "-";
          const unixTimeSeconds =
            typeof params.value === "number"
              ? params.value
              : moment(params.value).unix();
          return generateTime(
            unixTimeSeconds * 1000,
            false,
            true,
            "-",
            testStore
          );
        };
      }

      if (col.editable) {
        columnDef.cellEditor = "dueDateEditorRenderer";

        // TODO - update once we standardize date handling
        if (col.is_custom) {
          columnDef.valueParser = (params) => {
            if (params.newValue === "" || params.newValue === undefined)
              return params.oldValue;
            if (params.newValue === 0) return 0;

            const defaultRegex = new RegExp("\\d{2}-\\d{2}-\\d{4}");
            const dateFormattingRegex = dateFormatting
              ? /-/.test(dateFormatting)
                ? new RegExp(
                    dateFormatting
                      .split("-")
                      .map((part) => `\\d{${part.length}}`)
                      .join("\\-")
                  )
                : /\//.test(dateFormatting)
                ? new RegExp(
                    dateFormatting
                      .split("/")
                      .map((part) => `\\d{${part.length}}`)
                      .join("\\/")
                  )
                : defaultRegex
              : defaultRegex;
            if (!dateFormattingRegex.test(params.newValue)) {
              return params.oldValue;
            } else return params.newValue; //new Date(params.newValue);
          };
        } else {
          columnDef.valueParser = (params) => {
            // Parse the date BACK INTO A UNIX TIME from the edit format
            if (params.newValue + "".trim() === "" || !params.newValue)
              return params.oldValue;
            const likelyFormats = ["MM-DD-YYYY", "MM/DD/YYYY"];
            if (moment(params.newValue, likelyFormats).isValid())
              return moment(params.newValue, likelyFormats).unix();
            else if (moment(params.newValue).isValid())
              // Check standard ISO
              return moment(params.newValue).unix();
            else return params.oldValue;
          };
        }
      }
    }

    if (objectColumns.includes(col.normal_name)) {
      columnDef.comparator = (valueA, valueB, nodeA, nodeB) => {
        // make sure that the values exist before comparing
        return nodeA.data[col.normal_name]
          ? nodeA.data[col.normal_name].decimal
          : 0 > nodeB.data[col.normal_name]
          ? nodeB.data[col.normal_name].decimal
          : 0
          ? 1
          : -1;
      };
      columnDef.getQuickFilterText = objectColumnDefs.getQuickFilterText(
        col.normal_name
      );
      columnDef.valueFormatter = objectColumnDefs.valueFormatter(
        col.normal_name
      );
      columnDef.filterValueGetter = objectColumnDefs.filterValueGetter(
        col.normal_name
      );
    }

    if (!col.comparator && col.filter === "agTextColumnFilter") {
      col.comparator = (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1;
    }

    if (col.normal_name === "weight") {
      columnDef.headerName = "Weight (lbs)";
    }

    // add in custom text wrap cell style
    if (col.normal_name === "product_code") {
      columnDef.cellClass = "custom-wrap";
    }

    // add in custom properties (ex. cell renderers/editors)
    if (col.normal_name === "drawing_name") {
      columnDef.cellRenderer = "drawingNameCellRenderer";
      columnDef.cellRendererParams = (params) => ({
        moreInfoClick,
        togglePDFViewer: () => togglePDFViewer(params),
        setDisplayedPdf,
      });

      if (tableType === "WIZARD_ITEMS") {
        columnDef.cellEditor = "dropdownEditorRenderer";
        columnDef.cellEditorParams = (params) => {
          return {
            values: drawings,
            toggleValid: toggleDrawingValid,
            required: !params.data.drawing_name ? true : false,
          };
        };
        columnDef.lockVisible = true;
      }
    }
    if (col.normal_name === "package_name") {
      columnDef.cellRenderer = "packageNameCellRenderer";
      columnDef.valueFormatter = () => ({
        // not in wizard so no need to permission lock
        wizardPermission:
          permissions &&
          (permissions.includes(279) ||
            permissions.includes(280) ||
            permissions.includes(281)),
      });
    }
    if (col.normal_name === "job_title") {
      columnDef.cellRenderer = "jobNameCellRenderer";
      columnDef.valueFormatter = (params) => ({
        // not in wizard so no need to permission lock
        wizardPermission:
          permissions &&
          (permissions.includes(279) ||
            permissions.includes(280) ||
            permissions.includes(281)),
      });
    }
    if (col.normal_name === "size") {
      columnDef.comparator = (valueA, valueB) => {
        if (!valueA) return -1;
        if (!valueB) return 1;
        return valueA.localeCompare(valueB, undefined, {
          numeric: true,
          sensitivity: "base",
        });
      };
      columnDef.valueParser = (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue === null ? params.oldValue : "";
        if (
          !multidimensionRegex.test(params.newValue) ||
          !multidimensionOnlyX.test(params.newValue)
        ) {
          displayNotification(
            "Value not saved due to invalid format. Please provide a valid fraction."
          );
          return params.oldValue;
        }

        return params.newValue;
      };
    }
    if (col.normal_name === "length") {
      columnDef.valueParser = (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue === null
            ? params.oldValue && params.oldValue.decimal
            : params.oldValue.decimal === null
            ? { decimal: null, display: null }
            : { decimal: 0, display: "" };
        if (params.oldValue === convertFracToDec(params.newValue))
          return params.oldValue;

        if (!fractionalRegex.test(params.newValue)) {
          displayNotification(
            `Value not saved due to invalid format. Please provide a valid fraction (ex. 2'-1/2")`
          );
          return params.oldValue;
        }

        return {
          decimal: convertFracToDec(params.newValue),
          display: params.newValue,
        };
      };
      columnDef.cellEditor = "fractionalEditorRenderer";
      columnDef.comparator = objectColumnDefs.comparator("length");
    }
    if (col.normal_name === "container_name") {
      columnDef.editable = tableType === "WIZARD_ITEMS" ? false : true;
      columnDef.cellEditor =
        tableType === "WIZARD_ITEMS" ? null : "dropdownEditorRenderer";
      columnDef.cellEditorParams = (params) => {
        return {
          values: containers
            .filter((c) => c.job_id === params.data.job_id)
            .map((c) => c.name),
        };
      };
    }
    if (col.normal_name === "material_name") {
      columnDef.cellEditor = "dropdownInputEditorRenderer";
      columnDef.cellEditorParams = (params) => ({
        value: params.value,
        params,
        required: !params.data.material_name ? true : false,
        toggleValid: toggleMaterialValid,
        toggleModal: () => {
          materialTypes({ ...params.data, _column: "material_name" });
        },
      });
    }
    if (col.normal_name === "laydown_location_name") {
      columnDef.cellEditor = "dropdownInputEditorRenderer";
      columnDef.cellEditorParams = (params) => ({
        value: params.value,
        params,
        toggleModal: () =>
          handleLaydownLocations({
            ...params.data,
            _column: "laydown_location_name",
          }),
      });
    }
    if (col.normal_name === "drawing_priority") {
      columnDef.editable = false;
      columnDef.valueGetter = (params) => {
        let value;
        const ranks = transformDrawingPriorityToRank(items);
        ranks.forEach((rank, index) => {
          if (rank.id === params?.data?.drawing_id)
            value = rank.drawing_priority;
        });
        return value;
      };
      columnDef.getQuickFilterText = (params) => {
        let allValues = [];
        let value;
        params.node.gridApi.forEachNode((rowNode) => {
          allValues.push(rowNode.data);
        });
        if (params.api) {
          // todo - will need to rework this to work with infinite row model
          const ranks = transformDrawingPriorityToRank(allValues);
          for (let i = 0; i <= ranks.length; i++) {
            if (ranks[i] && ranks[i].id === params.data.drawing_id) {
              value = ranks[i].drawing_priority;
              break;
            }
          }
        }
        return value;
      };
    }
    if (col.normal_name === "joining_procedure_name") {
      columnDef.cellEditor = "dropdownInputEditorRenderer";
      columnDef.cellEditorParams = (params) => ({
        value: params.value,
        params,
        toggleModal: () =>
          handleJoiningProcedures({
            ...params.data,
            _column: "joining_procedure_name",
          }),
      });
    }
    if (col.normal_name === "heat_number") {
      columnDef.cellEditor = "dropdownInputEditorRenderer";
      columnDef.cellEditorParams = (params) => ({
        value: params.value,
        params,
        toggleModal: () =>
          heatNumbers({ ...params.data, _column: "heat_number" }),
      });
    }

    if (col.is_custom) {
      // todo - any specific custom column logic can go here
      columnDef.filter = false;
      columnDef.menuTabs = [];
    }

    columns.push(columnDef);
  }

  if (
    workStageColumns.find((col) => col.display_name === "Joint Heat Number")
  ) {
    for (let i = 0; i < 2; i++) {
      const columnDef = {
        suppressSizeToFit: true,
        headerName: `Joint Heat Number ${i + 1}`,
        cellClass: "wrap-text",
        minWidth: 150,
        valueGetter: (params) => {
          if (!params.data) return;

          const { joint_heat_numbers } = params.data;
          let value;
          if (!joint_heat_numbers || !JSON.parse(joint_heat_numbers).length)
            value = "";
          else {
            JSON.parse(joint_heat_numbers).forEach((hn) => {
              if (hn.position === i + 1) value = hn.heat_number;
            });
          }
          return value;
        },
        valueParser: (params) => {
          if (params.newValue === undefined) return null;

          return params.newValue;
        },
        getQuickFilterText: (params) => {
          const { joint_heat_numbers } = params.data;
          if (!JSON.parse(joint_heat_numbers).length) return;
          if (
            JSON.parse(joint_heat_numbers)[i] &&
            JSON.parse(joint_heat_numbers)[i].heat_number
          )
            return JSON.parse(joint_heat_numbers)[i].heat_number;
        },
        colId: `joint_heat_number_${i + 1}`,
        editable: true,
        sortable: true,
        menuTabs: ["filterMenuTab"],
        filter: "agTextColumnFilter",
        filterParams: {
          buttons: ["reset"],
          suppressAndOrCondition: true,
        },
        cellEditor: "dropdownInputEditorRenderer",
        cellEditorParams: (params) => ({
          value: params.value,
          params,
          toggleModal: () =>
            heatNumbers({
              ...params.data,
              _column: `joint_heat_number_${i + 1}`,
            }),
        }),
        field: `joint_heat_number_${i + 1}`,
        sort:
          sortState.sorting_column_name === `joint_heat_number_${i + 1}`
            ? sortState.sorting_method
            : null,
      };

      columns.push(columnDef);
    }
  }

  /*
      Update columns array based off savedColumns state

      - Add position property
      - Sort columns by position
    */
  if (savedColumnState && savedColumnState.length) {
    let savedStateResult = [];

    columns.forEach((c) => {
      let savedDef = savedColumnState.find(
        (sc) => sc.header_name === c.headerName
      );

      if (savedDef) {
        savedStateResult.push({
          ...c,
          pinned: savedDef.pinned,
          hide: savedDef.visible === 1 ? false : true,
          position: savedDef.position,
        });
      } else savedStateResult.push(c);
    });

    savedStateResult = savedStateResult
      .sort((a, b) => {
        if (a.position === b.position) {
          if (a.headerName.toLowerCase() > b.headerName.toLowerCase()) return 1;
          else return -1;
        } else return a.position - b.position;
      })
      .map((col) => {
        if (col.position !== undefined) delete col.position;
        return col;
      });

    columns = savedStateResult;
  } else {
    let savedStateResult = [];

    columns.forEach((c) => {
      let defaultPosition = workStageColumns.find(
        (sc) =>
          sc.display_name === c.headerName ||
          (sc.display_name === "Joint Heat Number" &&
            c.headerName.includes("Joint Heat Number"))
      );

      if (defaultPosition) {
        savedStateResult.push({
          ...c,
          position: defaultPosition.position,
        });
      } else savedStateResult.push(c);
    });

    savedStateResult = savedStateResult
      .sort((a, b) => {
        if (a.position === b.position) {
          if (a.headerName.toLowerCase() > b.headerName.toLowerCase()) return 1;
          else return -1;
        } else return a.position - b.position;
      })
      .map((col) => {
        if (col.position !== undefined) delete col.position;
        return col;
      });

    columns = savedStateResult;
  }

  // The following columns are not tracked in DB
  const onHoldColDef = {
    headerName: "",
    minWidth: 50,
    maxWidth: 50,
    width: 50,
    suppressMenu: true,
    sortable: true,
    comparator: (valueA, valueB, nodeA, nodeB) => {
      if (!nodeA || !nodeB) return;
      return nodeA.data.on_hold - nodeB.data.on_hold;
    },
    cellClassRules: {
      "cell-yellow": (params) => params.data?.on_hold === 1,
      "cell-green": (params) => params.data?.on_hold === 0,
    },
    pinned: "left",
    suppressColumnsToolPanel: true,
    colId: "status-indicator",
    headerClass: "status-indicator-button",
    sort:
      sortState.sorting_column_name === "status-indicator"
        ? sortState.sorting_method
        : null,
  };
  const checkboxColDef = {
    headerName: "",
    width: 50,
    maxWidth: 50,
    minWidth: 50,
    checkboxSelection: true,
    suppressMenu: true,
    suppressColumnsToolPanel: true,
    pinned: "left",
    colId: "checkbox",
  };

  if (!hasCustomColumnFeature) {
    checkboxColDef.headerCheckboxSelection = true;
  }
  if (groupingType === "grouped") {
    checkboxColDef.headerCheckboxSelection = true;
    checkboxColDef.headerCheckboxSelectionFilteredOnly = true;
  }

  const moreInfoDropdownColDef = {
    headerName: "Manage",
    sortable: false,
    minWidth: 60,
    width: 60,
    valueFormatter: (params) => ({
      moreInfoClick,
      toggleMoreInfo,
    }),
    cellRenderer: "moreInfoCellRenderer",
    suppressMenu: true,
    pinned: "right",
    suppressColumnsToolPanel: true,
    suppressSizeToFit: true,
    lockVisible: true,
    suppressMovable: true,
    colId: "manage",
  };
  const statusColDef = {
    headerName: "Status",
    field: "completedness",
    minWidth: 120,
    cellRenderer: "itemStatusCellRenderer",
    getQuickFilterText: (params) => params.data.completedness,
    filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["reset"],
    },
    menuTabs: ["filterMenuTab"],
    colId: "completedness",
    sort:
      sortState.sorting_column_name === "completedness"
        ? sortState.sorting_method
        : null,
    comparator: (valueA, valueB) =>
      valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
  };
  const quantityColDef = {
    headerName: "Quantity",
    field: "quantity",
    valueFormatter: (params) => {
      if (params.value && typeof params.value === "object")
        return params.value.display;
      else if (params.value) return params.value;
      else if (typeof params.data.work_item_ids === "string")
        return params.data.work_item_ids.split(",").length;
      else if (typeof params.data.work_item_ids === "number") return 1;
      else return params.value || "-";
    },
    width: 100,
    minWidth: 80,
    menuTabs: ["filterMenuTab"],
    filterParams: {
      buttons: ["reset"],
    },
    filter: "agNumberColumnFilter",
    colId: "quantity",
    pinned: "left",
  };

  const creationQuantityColDef = {
    headerName: "Quantity",
    width: 100,
    valueGetter: (params) => {
      if (params.data.quantityToCreate) return params.data.quantityToCreate;
      else return null;
    },
    valueSetter: (params) => {
      if (
        params.newValue === 0 ||
        params.newValue === "" ||
        isNaN(params.newValue) ||
        params.newValue < 1
      )
        return false;
      params.data.quantityToCreate = params.newValue;
      return true;
    },
    maxWidth: 100,
    minWidth: 100,
    editable: true,
    colId: "quantityToCreate",
    pinned: "left",
  };

  if (tableType === "WIZARD_ITEMS") {
    // if adding a new row in wizard items table, display the quantity column that tracks the number of items to create
    if (isNewRow) columns.unshift(creationQuantityColDef);
  } else {
    columns.unshift(checkboxColDef);
  }

  // shown only on ungrouped table
  if (groupingType === "ungrouped") {
    columns.unshift(onHoldColDef);
    columns.push(statusColDef);
    columns.push(moreInfoDropdownColDef);
  } else {
    columns.push(quantityColDef);
  }

  return columns;
};
