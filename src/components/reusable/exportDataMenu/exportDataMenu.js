import { Fragment, useState, useEffect } from "react";
import { Box, Menu, MenuItem, IconButton, Tooltip } from "@mui/material";
import { Download } from "@mui/icons-material";
import { getTodaysDate } from "./../../../_utils";

// ag-grid imports
import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-balham-dark.css";
import "../agTable/stylesAgTable.scss";
import "../../dashboardAnalytics/stylesDashboardGrids.scss";
import { LicenseManager } from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";

LicenseManager.setLicenseKey(process.env.REACT_APP_AG_GRID_LICENSE);

const ExportDataMenu = ({ title, data, disabled = false }) => {
  const [expandDownloadMenu, setExpandDownloadMenu] = useState(null);
  const [gridApi, setGridApi] = useState(null);
  const [columnApi, setColumnApi] = useState(null);
  const [gridColumnDefs, setColumnDefs] = useState([]);
  const [gridRowData, setRowData] = useState([]);
  const [allowDownload, setAllowDownload] = useState(false);

  const openMenu = Boolean(expandDownloadMenu);
  const handleDownloadMenuClick = (event) => {
    setExpandDownloadMenu(event.currentTarget);
  };
  const handleCloseDownloadMenu = () => {
    setExpandDownloadMenu(null);
  };

  // download functions
  const getColumns = () => {
    // generate some simple headers for the data...
    const keys = Object.keys(data[0]) ?? [];
    return keys.map((key) => ({ field: key }));
  };

  // Used so we can store the grid api once it loads
  const onGridReady = (params) => {
    setGridApi(params.api);
    setColumnApi(params.columnApi); // note - this is deprecated in the later versions, all functions in gridApi
  };

  // get the download params for the gridApi to use
  const getParams = () => {
    const formattedTitle = title ? title.replaceAll(" ", "_") : "export";
    return {
      fileName: `${formattedTitle}-${getTodaysDate()}`, // passed id without spaces and today's date
    };
  };

  // handle the download of the grid data, uses the ag-grid instance
  const handleDownloadCSV = () => {
    gridApi.exportDataAsCsv(getParams());
  };

  // handle the download of the grid data, uses the ag-grid instance
  const handleDownloadExcel = () => {
    gridApi.exportDataAsExcel(getParams());
  };

  // on load, update the grid data...
  useEffect(() => {
    const allow = Boolean(data && data.length > 0);
    const columns = allow ? getColumns() : [];
    if (allow && columns.length > 0) {
      setColumnDefs(columns);
      setRowData(data);
      setAllowDownload(allow);
    }
  }, [data]);

  // need to have the data grid shown even if not displayed in order to export...
  return (
    <Fragment>
      {allowDownload && (
        <div style={{ display: "none" }}>
          <div
            className="ag-theme-balham-dark"
            style={{ width: "100%", height: "300px" }}
          >
            {/* using the basic implementation since we're not showing the chart and need to get back the ongridready stuff */}
            <AgGridReact
              id={title}
              gridOptions={{
                columnDefs: gridColumnDefs,
                rowData: gridRowData,
                onGridReady: onGridReady,
              }}
            />
          </div>
        </div>
      )}
      <Box sx={{ display: "flex", alignItems: "center", textAlign: "center" }}>
        <Tooltip title={`Download ${title}`}>
          <IconButton
            onClick={handleDownloadMenuClick}
            sx={{ ml: 2 }}
            aria-controls={expandDownloadMenu ? "download-menu" : undefined}
            aria-haspopup="true"
            aria-expanded={expandDownloadMenu ? "true" : undefined}
            disabled={!allowDownload || disabled}
            size="small"
          >
            <Download fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>
      <Menu
        anchorEl={expandDownloadMenu}
        id="download-menu"
        open={openMenu}
        onClose={handleCloseDownloadMenu}
        onClick={handleCloseDownloadMenu}
      >
        <MenuItem onClick={handleDownloadCSV}>CSV</MenuItem>
        <MenuItem onClick={handleDownloadExcel}>Excel</MenuItem>
      </Menu>
    </Fragment>
  );
};

export default ExportDataMenu;
