@import "../../styles/colors.scss";

div.pdf-viewer-wrapper {
  & div.loader-wrapper {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    & > div.loader {
      height: 150px;
      width: 150px;
    }
  }

  & > div.traversal-buttons {
    grid-row: 1/2;
    grid-column: 1/-1;
    padding: 0 10px;

    display: grid;
    column-gap: 10px;
    grid-template-columns: 1fr 1fr;

    & > button#previous {
      grid-column: 1/2;
      justify-self: start;
    }

    & > button#next {
      grid-column: 2/-1;
      justify-self: end;
    }
  }

  &.fullscreen {
    width: calc(100vw - 50px);
    transform: translate(30px, 5px);

    &.narrow {
      width: calc(100vw - 250px);
      transform: translate(125px, 5px);
    }
  }

  @media screen and (max-width: 600px) and (orientation: portrait) {
    height: 100vw;
    width: 100vw;
  }

  @media screen and (max-height: 600px) and (orientation: landscape) {
    height: 100vh;
    width: 100vh;
  }
}
