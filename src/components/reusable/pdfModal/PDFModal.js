// NPM PACKAGE IMPORTS
import React, { useState } from "react";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";

// STYLE IMPORTS
import "./stylesPDFModal.scss";
import "./stylesPDFModal.css";
import PDFViewer from "../pdfViewer/PDFViewer";
import ErrorBoundary from "../errorBoundary/ErrorBoundary";

const PDFModal = ({
  pdfViewer,
  togglePDFViewer,
  selectedItem,
  setSelectedItem,
  itemId,
  itemType,
  file,
  setFile,
  refresh,
  area,
  storeInstanceOutside = null,
  currentRowOrder,
}) => {
  const [showExitPrompt, toggleExitPrompt] = useState(false);

  return (
    <Modal
      open={pdfViewer}
      handleClose={() => (showExitPrompt ? {} : toggleExitPrompt(true))}
    >
      {showExitPrompt && (
        <Modal
          open={showExitPrompt}
          handleClose={() => toggleExitPrompt(false)}
        >
          <div className="forge-viewer-modal-exit-prompt">
            <p>Are you sure you want to close the viewer?</p>
            <Button onClick={() => toggleExitPrompt(false)} className="cancel">
              No
            </Button>
            <Button onClick={togglePDFViewer} className="submit">
              Yes
            </Button>
          </div>
        </Modal>
      )}
      <ErrorBoundary message="PDFViewer Error" maxRetryCount={3}>
        <PDFViewer
          selectedItem={selectedItem}
          setSelectedItem={setSelectedItem}
          itemId={itemId}
          itemType={itemType}
          file={file}
          setFile={setFile}
          refresh={refresh}
          area={area}
          storeInstanceOutside={storeInstanceOutside}
          currentRowOrder={currentRowOrder}
        />
      </ErrorBoundary>
    </Modal>
  );
};

export default PDFModal;
