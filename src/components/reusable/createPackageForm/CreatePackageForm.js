// NPM PACKAGE IMPORTS
import React, { useState } from "react";
import Input from "msuite_storybook/dist/input/Input";
import Select from "msuite_storybook/dist/select/Select";
import Toggle from "msuite_storybook/dist/toggle/Toggle";
import Button from "msuite_storybook/dist/button/Button";
import DatePicker from "react-datepicker";
import moment from "moment";

// STYLES IMPORTS
import "./stylesCreatePackageForm.scss";

const CreatePackageForm = ({
  displayedFlows,
  createButtonText,
  createCallback,
  toggleCreationForm,
}) => {
  const [packageName, setPackageName] = useState("");
  const [excludeFloatMode, toggleExcludeFloatMode] = useState(false);
  const [selectedFlowId, setSelectedFlowId] = useState(null);
  const [dueDate, setDueDate] = useState(null);

  const handleClose = (e) => {
    e.preventDefault();
    setPackageName("");
    toggleExcludeFloatMode(false);
    setSelectedFlowId(null);
    setDueDate(null);
    toggleCreationForm && toggleCreationForm(false);
  };

  const handleCreate = (e) => {
    e.preventDefault();
    const data = {
      package_name: packageName.trim(),
      exclude_float_mode: excludeFloatMode ? 1 : 0,
      work_flow_id: selectedFlowId,
      due_date: moment(dueDate).format("YYYY-MM-DD"),
    };
    createCallback && createCallback(data);
  };

  return (
    <div className="create-package-form">
      <h4>Create New Package</h4>
      <label className="input-label text">
        <span>
          Package Name <span style={{ color: "red" }}>*</span>
        </span>
        <Input
          placeholder="Package Name"
          value={packageName}
          onChange={(e) => setPackageName(e.target.value)}
        />
      </label>
      <label className="input-label toggle">
        <span>Exclude from Float Mode?</span>
        <Toggle
          name="float-mode-toggle"
          text={["yes", "no"]}
          defaultChecked={excludeFloatMode}
          onToggleChanged={() => toggleExcludeFloatMode(!excludeFloatMode)}
        />
      </label>
      <label className="input-label dropdown">
        <span>
          Work Flow <span style={{ color: "red" }}>*</span>
        </span>
        <Select
          options={displayedFlows}
          value={selectedFlowId}
          onInput={(e) => setSelectedFlowId(e.target.value)}
          placeholder="Select Flow"
        />
      </label>
      <label className="input-label date-picker">
        <span>
          Due Date <span style={{ color: "red" }}>*</span>
        </span>
        <DatePicker
          className="date-picker"
          selected={dueDate}
          onChange={setDueDate}
          placeholderText=""
        />
      </label>
      <div className="button-row">
        <Button className="button" onClick={(e) => handleClose(e)}>
          Cancel
        </Button>
        <Button
          onClick={(e) => handleCreate(e)}
          disabled={!packageName.length || !selectedFlowId || !dueDate}
          className="button"
        >
          {createButtonText ? createButtonText : `Create Package`}
        </Button>
      </div>
    </div>
  );
};

export default CreatePackageForm;
