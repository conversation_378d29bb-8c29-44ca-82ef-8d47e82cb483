@import "../../styles/colors.scss";

div.create-package-form {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 10px;
  width: 100%;
  margin: 10px 0;

  & h4 {
    margin: 0;
  }

  & label.input-label {
    color: black;

    & input,
    select {
      height: 30px;
      font-size: 0.9rem;

      &::placeholder {
        font-size: 0.9rem;
      }
    }

    & .react-datepicker-wrapper {
      width: 315px;
    }

    & input.date-picker {
      margin-left: 10px;
      box-sizing: border-box;
      width: 100%;
    }
  }

  & label.input-label.toggle {
    display: flex;
    align-items: center;
    margin-top: -10px;

    & div.toggle-switch {
      margin-left: 10px;
    }
  }

  & div.button-row {
    margin-top: 30px;
    display: flex;
    justify-content: space-between;
  }
}
