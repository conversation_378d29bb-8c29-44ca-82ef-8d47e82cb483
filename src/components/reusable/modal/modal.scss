@import "../../styles/colors.scss";

.modal-container.dark {
  background-color: $darkGrey;
}

.modal-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  min-width: 300px;
  background-color: white;
  padding: 1px;
  z-index: 30;
  animation: fadein 0.2s linear;
  border-radius: 3px;
  box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);

  header {
    padding: 0 20px;
    height: 40px;
    background-color: $blue;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
    font-size: 0.9rem;

    h3 {
      margin: 0;
      font-weight: bold;
    }
    & svg {
      font-size: 0.8rem;
    }

    .header--close {
      cursor: pointer;
    }
  }

  .modal-content {
    padding: 20px;
    // box-shadow: 0 2px 2px #bbb;

    .tab-nav {
      button {
        border: none;
        outline: none;
        background-color: transparent;
        padding: 5px 8px;
        height: 40px;
      }
    }
  }

  footer {
    height: 50px;
    width: 100%;
    padding: 0 25px;
    box-sizing: border-box;

    span {
      font-size: 0.9rem;
      cursor: pointer;
      float: right;
      line-height: 50px;
    }
  }
}

// ANIMATIONS
@keyframes fadein {
  0% {
    opacity: 0;
    top: 10%;
  }
  100% {
    opacity: 1;
    top: 50%;
  }
}

@keyframes fadeout {
  0% {
    opacity: 1;
    top: 50%;
  }
  100% {
    opacity: 0;
    top: 10%;
  }
}
