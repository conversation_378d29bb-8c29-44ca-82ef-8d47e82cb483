// NPM PACKAGE IMPORTS
import React, { useRef } from "react";
import PropTypes from "prop-types";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../hooks/useOutsideClick";

// STYLE IMPORTS
import "./modal.scss";

// EXPORTS
const Modal = ({
  showModal = true,
  title,
  onSubmit,
  onClose,
  children,
  showFooter,
  dark,
  suppressForm,
  showExit,
}) => {
  const wrapperRef = useRef(null);
  useOutsideClick(wrapperRef, () => {
    onClose && onClose();
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    const error = onSubmit(e);
    if (!error) onClose();
  };

  return showModal ? (
    <>
      <section
        ref={wrapperRef}
        className={dark ? "modal-container dark" : "modal-container"}
      >
        {title && (
          <header>
            <h3>{title}</h3>
            {(!suppressForm || showExit) && (
              <FontAwesomeIcon
                className="header--close"
                onClick={onClose}
                icon={faTimes}
              />
            )}
          </header>
        )}
        <div className="modal-content">
          {suppressForm ? (
            <div>{children}</div>
          ) : (
            <form onSubmit={(e) => handleSubmit(e)}>{children}</form>
          )}
        </div>
        {showFooter && (
          <footer>
            <span onClick={onClose}>Close</span>
          </footer>
        )}
      </section>
      <div className="modal-overlay" />
    </>
  ) : (
    <></>
  );
};

export default Modal;

Modal.propTypes = {
  showModal: PropTypes.bool,
  title: PropTypes.string,
  onSubmit: PropTypes.func,
  onClose: PropTypes.func,
  showFooter: PropTypes.bool,
  showExit: PropTypes.bool,
  dark: PropTypes.bool,
  suppressForm: PropTypes.bool,
};
