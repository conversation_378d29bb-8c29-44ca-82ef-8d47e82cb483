// NPM PACKAGE IMPORTS
import React from "react";
import { FiSearch } from "react-icons/fi";

// STYLES IMPORTS
import "./stylesFilter.scss";

const Menu = ({
  menuRef,
  isSearchable,
  selectAll,
  isMulti,
  displayedOptions,
  selected,
  type,
  isSelectAll,
  handleSelectAll,
  handleUnselectAll,
  handleSelection,
  formatName,
  searchInput,
  setSearchInput,
  onMouseLeave,
  f_selected,
}) => {
  return (
    <div className="menu" ref={menuRef} onMouseLeave={onMouseLeave}>
      {isSearchable && (
        <div className="search-wrapper">
          <FiSearch />
          <input
            type="text"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
          />
        </div>
      )}
      <div className="menu-list">
        {selectAll && isMulti && (
          <>
            <div className="select-all" onClick={handleSelectAll}>
              Select All
            </div>
            <div className="select-all" onClick={handleUnselectAll}>
              Unselect All
            </div>
          </>
        )}
        {f_selected && f_selected.length ? (
          f_selected.map((item) => (
            <div
              className="selected menu-item"
              key={item.id}
              onClick={() => handleSelection(item)}
            >
              {formatName(item)}
            </div>
          ))
        ) : (
          <></>
        )}
        {displayedOptions && displayedOptions.length ? (
          displayedOptions
            .filter((o) => !selected.find((s) => s.id === o.id))
            .map((item) => (
              <div
                className="menu-item"
                key={item.id}
                onClick={() => handleSelection(item)}
              >
                {formatName(item)}
              </div>
            ))
        ) : (
          <></>
        )}
        {(!displayedOptions || !displayedOptions.length) &&
        (!selected || !selected.length) ? (
          <p className="no-items-message">{`No ${type}`}</p>
        ) : (
          <></>
        )}
      </div>
    </div>
  );
};

export default Menu;
