// NPM PACKAGE IMPORTS
import React, { useState, useRef, useEffect, useMemo } from "react";
import { <PERSON><PERSON><PERSON>, FiSearch, FiX } from "react-icons/fi";

// COMPONENT IMPORTS
import Menu from "./Menu";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../hooks/useOutsideClick";
import { escapeRegExp } from "../../../_utils";

// STYLES IMPORTS
import "./stylesFilter.scss";

const Filter = ({
  type,
  list,
  selected,
  setSelected,
  handleParentSelect,
  toggleAllSelections = (f) => f,
  nameKey,
  idKey,
  orientation = "VERTICAL",
  isDisabled,
  isSearchable = true,
  isMulti = true,
  selectAll = false,
  smallView = false,
  allSelected,
  alwaysCollapsed = false,
  preventFormat = false,
  searchInput = "",
  setSearchInput = (f) => f,
  requiredSelectedItem = null,
  customKey,
  sectionTitles = [],
  sectionKey = "", // key to use to check item is in given section, use with sectionTitles
}) => {
  const [showMenu, toggleMenu] = useState(false);
  const [isSelectAll, toggleSelectAll] = useState(false);
  const [internalSelected, setInternalSelected] = useState(selected);

  useEffect(() => {
    setInternalSelected(selected);
  }, [selected]);

  const menuRef = useRef(null);

  useOutsideClick(menuRef, () => {
    setSearchInput("");
    toggleMenu(false);
  });

  useEffect(() => {
    toggleSelectAll(!!allSelected);
  }, [allSelected]);

  const handleRemoveSelected = (item, menuSelection) => {
    if (
      isDisabled ||
      (requiredSelectedItem && item.id === requiredSelectedItem.id)
    )
      return;
    const newSelected = internalSelected.filter(
      (option) => option.id !== item.id
    );
    // if the selection is coming from menu NOT THE CHIP add to internal
    menuSelection ? setInternalSelected(newSelected) : setSelected(newSelected);
    toggleSelectAll(false);
  };

  const formatName = (item) => {
    if (nameKey && idKey) {
      if (!item[idKey]) return `${item[nameKey]}`;
      return `(${item[idKey]}) ${item[nameKey]}`;
    } else if (nameKey && customKey) {
      return `${item[nameKey]}${
        item[customKey] ? ` (${item[customKey]})` : ""
      } `;
    } else if (nameKey) return `${item[nameKey]}`;
  };

  const handleSelection = (item) => {
    if (isDisabled) return;

    if (!isMulti) {
      if (
        internalSelected.length &&
        internalSelected[0].id === item.id &&
        (!sectionKey || internalSelected[0][sectionKey] === item[sectionKey])
      ) {
        // already selected, remove item
        handleRemoveSelected(item, true);
        toggleMenu(false);
        return;
      } else {
        toggleMenu(false);
        setInternalSelected([item]);
        return;
      }
    }
    const selectedIds = internalSelected.map((i) => i.id);

    // check if the item is already selected
    if (internalSelected.length && selectedIds.includes(item.id))
      return handleRemoveSelected(item, true);
    else if (item.id === 0 && selectedIds.length) return;
    else if (selectedIds.includes(0) && item.id !== 0) return;

    setInternalSelected([...internalSelected, item]);
  };

  useEffect(() => {
    // check if the internal selections are different from external (a selection has been made)
    if (
      !showMenu &&
      JSON.stringify(selected) !== JSON.stringify(internalSelected) &&
      !isSelectAll
    ) {
      setSelected(internalSelected);
    }
  }, [showMenu]);

  const handleUnselectAll = () => {
    if (isDisabled) return;

    setInternalSelected([]);
    setSelected([], false);
    toggleSelectAll(false);
    toggleAllSelections(0);
  };

  const handleSelectAll = () => {
    if (isDisabled) return;

    toggleSelectAll(true);
    toggleAllSelections(1);
    type !== "Stages"
      ? setInternalSelected(list)
      : setInternalSelected(list.slice(1));
    type !== "Stages"
      ? setSelected(list, true)
      : setSelected(list.slice(1), true);
    toggleMenu(false);
  };

  // if isSelectAll then listen for changes to list and add to selected
  useEffect(() => {
    if (isSelectAll && allSelected !== undefined) {
      handleParentSelect(list.filter((s) => s.id !== 0));
    }
  }, [isSelectAll]);

  useEffect(() => {
    if (!selected.length) {
      toggleSelectAll(false);
    }
  }, [selected]);

  const sortedDisplayedOptions = useMemo(() => {
    const pattern = new RegExp(escapeRegExp(searchInput), "i");
    return list.filter((o) => !searchInput || pattern.test(formatName(o)));
  }, [list, searchInput]);

  const expandableName = useMemo(() => {
    return `${selected.length} Selected ${
      preventFormat || (selected && selected.length > 1)
        ? type
        : type.slice(0, -1)
    }`;
  }, [selected, type]);

  const f_selected = useMemo(() => {
    if (!searchInput) return internalSelected;

    const pattern = new RegExp(escapeRegExp(searchInput), "i");
    return internalSelected.filter(
      (o) => !searchInput || pattern.test(formatName(o))
    );
  }, [internalSelected, searchInput]);

  if (smallView) {
    return (
      <div
        className="selectable-filter-wrapper"
        onMouseLeave={() => toggleMenu(false)}
      >
        <div
          className={isDisabled ? "disabled filter-button" : "filter-button"}
          onClick={() => (isDisabled ? false : toggleMenu(true))}
        >
          {internalSelected.length <= 2 && !alwaysCollapsed ? (
            <p>{type}</p>
          ) : (
            <p
              onMouseEnter={() => {
                if (isDisabled) return;
                toggleMenu(true);
              }}
              className="show-more-hover"
            >{`${expandableName}`}</p>
          )}
          <FiPlus />
        </div>
        {internalSelected.length > 0 &&
          internalSelected.length <= 2 &&
          !alwaysCollapsed && (
            <div className="selected-options horizontal-items">
              {internalSelected.map((item) => (
                <div
                  data-filtertype={type}
                  key={`${item.id}`}
                  className="selected-item"
                >
                  <p>{formatName(item)}</p>
                  <FiX
                    className={isDisabled ? "disabled" : ""}
                    onClick={() => {
                      if (isDisabled) return;
                      handleRemoveSelected(item, false);
                    }}
                  />
                </div>
              ))}
            </div>
          )}
        {showMenu && (
          <Menu
            menuRef={menuRef}
            isSearchable={isSearchable}
            selectAll={selectAll}
            isMulti={isMulti}
            displayedOptions={sortedDisplayedOptions}
            selected={internalSelected}
            type={type}
            isSelectAll={isSelectAll}
            handleSelectAll={handleSelectAll}
            handleUnselectAll={handleUnselectAll}
            handleSelection={handleSelection}
            formatName={formatName}
            searchInput={searchInput}
            setSearchInput={setSearchInput}
            f_selected={f_selected}
          />
        )}
      </div>
    );
  }

  return (
    <>
      {orientation === "VERTICAL" ? (
        <div className="selectable-filter-wrapper">
          <div
            onClick={() => {
              if (isDisabled) return;
              toggleMenu(!showMenu);
            }}
            className={isDisabled ? "disabled filter-button" : "filter-button"}
          >
            <p>{type}</p>
            <FiPlus />
          </div>
          {showMenu && (
            <div ref={menuRef} className="menu">
              {isSearchable && (
                <div className="search-wrapper">
                  <FiSearch />
                  <input
                    type="text"
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                  />
                </div>
              )}
              <div className="menu-list">
                {selectAll && isMulti && (
                  <div className="select-all" onClick={handleSelectAll}>
                    {isSelectAll ? `Unselect All` : `Select All`}
                  </div>
                )}
                {internalSelected &&
                internalSelected.length &&
                !sectionTitles?.length ? (
                  internalSelected.map((item) => (
                    <div
                      className="selected menu-item"
                      key={item.id}
                      onClick={() => handleSelection(item)}
                    >
                      {formatName(item)}
                    </div>
                  ))
                ) : (
                  <></>
                )}
                {sectionTitles?.length && list?.length ? (
                  sectionTitles.map((title) => (
                    <div key={title.name}>
                      <div className="menu-section-title" key={title.name}>
                        {title.name}
                      </div>
                      {list
                        .filter((o) => o[sectionKey] === title.id)
                        .map((item) => (
                          <div
                            className={`${
                              internalSelected.find(
                                (o) =>
                                  item.id === o.id &&
                                  item[sectionKey] === o[sectionKey]
                              )
                                ? "selected "
                                : ""
                            }menu-item`}
                            key={item.id}
                            onClick={() => handleSelection(item)}
                          >
                            {formatName(item)}
                          </div>
                        ))}
                    </div>
                  ))
                ) : list?.length ? (
                  list
                    .filter((o) => !selected.find((s) => s.id === o.id))
                    .map((item) => (
                      <div
                        className="menu-item"
                        key={item.id}
                        onClick={() => handleSelection(item)}
                      >
                        {formatName(item)}
                      </div>
                    ))
                ) : (
                  <></>
                )}
                {(!list || !list.length) && (!selected || !selected.length) ? (
                  <p className="no-items-message">{`No ${type}`}</p>
                ) : (
                  <></>
                )}
              </div>
            </div>
          )}
          {!smallView && internalSelected.length > 0 && (
            <div
              className={
                orientation === "vertical"
                  ? `selected-options`
                  : `selected-options horizontal-items`
              }
            >
              {internalSelected.map((item) => (
                <div
                  data-filtertype={type}
                  key={item.id}
                  className="selected-item"
                >
                  <p>{formatName(item)}</p>
                  <FiX
                    className={isDisabled ? "disabled" : ""}
                    onClick={() => {
                      if (isDisabled) return;
                      handleRemoveSelected(item);
                    }}
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      ) : (
        <div className="inline-selectable-filter-wrapper">
          <div
            onClick={() => {
              if (isDisabled) return;
              toggleMenu(!showMenu);
            }}
            className={
              isDisabled
                ? "disabled inline-filter-button"
                : "inline-filter-button"
            }
          >
            <p>{type}</p>
            <FiPlus />
          </div>
          <div className="inline-selected-options">
            {selected.length > 0 &&
              selected.map((item) => (
                <div
                  data-filtertype={type}
                  key={item.id}
                  className="selected-item"
                >
                  <p>{formatName(item)}</p>
                  <FiX
                    className={isDisabled ? "disabled" : ""}
                    onClick={() => {
                      if (isDisabled) return;
                      handleRemoveSelected(item);
                    }}
                  />
                </div>
              ))}
          </div>
          {showMenu && (
            <div ref={menuRef} className="menu">
              {isSearchable && (
                <div className="search-wrapper">
                  <FiSearch />
                  <input
                    type="text"
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                  />
                </div>
              )}
              <div className="menu-list">
                {selectAll && isMulti && (
                  <div className="select-all" onClick={handleSelectAll}>
                    {isSelectAll ? `Unselect All` : `Select All`}
                  </div>
                )}
                {selected && selected.length ? (
                  selected.map((item) => (
                    <div
                      className="selected menu-item"
                      key={item.id}
                      onClick={() => handleSelection(item)}
                    >
                      {formatName(item)}
                    </div>
                  ))
                ) : (
                  <></>
                )}
                {list && list.length ? (
                  list
                    .filter((o) => !selected.find((s) => s.id === o.id))
                    .map((item) => (
                      <div
                        className="menu-item"
                        key={item.id}
                        onClick={() => handleSelection(item)}
                      >
                        {formatName(item)}
                      </div>
                    ))
                ) : (
                  <></>
                )}
                {(!list || !list.length) && (!selected || !selected.length) ? (
                  <p className="no-items-message">{`No ${type}`}</p>
                ) : (
                  <></>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default Filter;
