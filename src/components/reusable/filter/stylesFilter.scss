@import "../../styles/colors.scss";

div.selectable-filter-wrapper {
  display: flex;
  align-items: center;
  margin-right: 20px;
  & .disabled {
    opacity: 0.9;
    cursor: default;
  }
}

div.filter-button {
  display: inline-flex;
  align-items: center;
  height: 35px;
  color: $blue;
  background-color: transparent;
  cursor: pointer;

  & p {
    margin: 0 5px 0;
    padding: 0;
    font-weight: 600;
  }
  & svg {
    font-size: 1.1rem;
  }
}

div.show-more-wrapper {
  color: $blue;
}

p.show-more-hover {
  position: relative;
  color: $blue;
  font-weight: bold;
  font-size: 0.9rem;
  margin: 5px 5px 0;
  padding-bottom: 3px;
  height: auto;
  height: 100%;
  text-decoration: underline;
  display: flex;
  align-items: center;
  cursor: pointer;

  & svg {
    color: $red;
    margin-left: 3px;
    cursor: pointer;
    font-size: 0.9rem;
  }
}
div.selected-hover-popup {
  position: absolute;
  max-height: 140px;
  width: 200px;
  display: flex;
  flex-direction: column;
  z-index: 20;
  background-color: #293037;
  overflow-x: auto;
  border-radius: 3px;

  & span {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 28px;
    font-size: 0.8rem;
    color: $lighterSlate;
    padding: 5px;
    border-bottom: 1px solid #3b465e;
    box-sizing: border-box;

    &:last-of-type {
      border-bottom: none;
    }
    & p {
      max-width: 160px;
      overflow-x: hidden;
      white-space: nowrap;
      cursor: default;
    }
    & svg {
      cursor: pointer;
      color: $lighterSlate;
      font-size: 0.9rem;
    }
  }

  &::-webkit-scrollbar {
    width: 10px;
    background-color: #f5f5f5;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #555;
  }
}

div.selected-options {
  // display: grid;
  // grid-gap: 5px;
  // grid-template-columns: repeat(autofill, 1fr);
  // grid-template-rows: repeat(autofill, 125px);
  display: flex;

  max-height: 100px;
  overflow-x: auto;
  margin: 0 5px;
  padding-top: 0;
  // padding-top: 7px;

  &::-webkit-scrollbar {
    width: 10px;
    background-color: #f5f5f5;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #555;
  }

  & .selected-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: white;
    height: 22px;
    width: 125px;
    background-color: #4d5971;
    padding: 0px 5px;
    font-size: 0.7rem;
    white-space: nowrap;
    border-radius: 3px;

    & p {
      overflow: hidden;
      // max-width: 105px;
      max-width: 80px;
      margin: 0;
    }

    & svg {
      cursor: pointer;
    }
    & .disabled {
      cursor: default;
      color: $lighterSlate !important;
    }
  }
}

div.menu {
  position: absolute;
  top: 35px;
  display: flex;
  flex-direction: column;
  max-width: 200px;
  min-width: 180px;
  z-index: 30;
  background-color: #293037;
  border-radius: 3px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);

  & .search-wrapper {
    display: flex;
    align-items: center;
    padding-left: 8px;
    height: 28px;
    background-color: $lighterGrey;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    border-top: 1px solid #333;
    border-right: 1px solid #333;
    border-left: 1px solid #333;

    & svg {
      color: $blue;
      font-size: 0.9rem;
      min-width: 12px;
    }
    & input[type="text"] {
      background: transparent;
      color: white;
      margin-left: 10px;
      flex-grow: 1;
      border: none;

      &:focus {
        border: none;
        outline: none;
      }
    }
  }

  & .menu-list {
    max-height: 240px;
    overflow-y: auto;
    overflow-x: hidden;

    &::-webkit-scrollbar {
      width: 10px;
      background-color: #555;
      border-radius: 3px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background-color: #f5f5f5;
    }

    & div.select-all {
      display: flex;
      align-items: center;
      height: 30px;
      color: $lighterSlate;
      font-size: 0.8rem;
      padding-left: 8px;
      border-bottom: 1px solid #3b465e;
      cursor: pointer;
      box-sizing: border-box;
      font-weight: bold;
    }

    & div.menu-section-title {
      color: $fabProBlue;
      padding: 10px 0px 10px 5px;
    }

    & div.menu-item {
      display: flex;
      align-items: center;
      width: 100%;
      height: auto;
      min-height: 30px;
      border-bottom: 1px solid #3b465e;
      color: $lighterSlate;
      font-size: 0.8rem;
      cursor: pointer;
      box-sizing: border-box;
      padding: 2px 0 2px 8px;

      &:last-of-type {
        border-bottom: none;
      }
    }

    & .no-items-message {
      color: $lighterSlate;
      margin: 0;
      padding: 5px;
    }

    & .selected {
      background-color: $blue;
      color: white !important;
      border: 1px solid $blue !important;
    }
  }
}

// INLINE
// div.inline-selectable-filter-wrapper {
//   display: flex;
//   // min-width: 140px;
//   // max-width: 250px;

//   & .disabled {
//     opacity: 0.7;
//     cursor: default;
//   }
// }

// div.inline-filter-button {
//   display: flex;
//   align-items: center;
//   height: 30px;
//   color: $blue;
//   background-color: transparent;
//   cursor: pointer;

//   & p {
//     margin: 0 5px 0;
//     padding: 0;
//     font-weight: 600;
//   }
//   & svg {
//     font-size: 1.1rem;
//   }
// }

// div.inline-selected-options {
//   display: flex;
//   align-items: center;
//   margin-left: 10px;
//   overflow-x: auto;

//   &::-webkit-scrollbar {
//     width: 10px;
//     background-color: #f5f5f5;
//     border-radius: 3px;
//   }
//   &::-webkit-scrollbar-thumb {
//     border-radius: 2px;
//     background-color: #555;
//   }

//   & .selected-item {
//     display: flex;
//     align-items: center;
//     justify-content: space-between;
//     color: white;
//     width: 125px;
//     height: 18px;
//     background-color: #4d5971;
//     padding: 2px 5px 2px 5px;
//     font-size: 0.7rem;
//     white-space: nowrap;
//     margin: 0 5px;
//     border-radius: none;

//     & p {
//       overflow: hidden;
//       max-width: 105px;
//       margin: 0;
//     }

//     & svg {
//       cursor: pointer;
//       margin-left: 5px;
//     }
//   }
// }
