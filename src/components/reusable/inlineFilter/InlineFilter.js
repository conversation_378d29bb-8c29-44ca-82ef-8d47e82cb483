// NPM PACKAGE IMPORTS
import React, { useState, useRef, useEffect, useMemo } from "react";
import { <PERSON>P<PERSON>, FiSearch, FiX } from "react-icons/fi";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../hooks/useOutsideClick";

// STYLES IMPORTS
import "./stylesInlineFilter.scss";

const InlineFilter = ({
  type,
  list,
  selected,
  handleParentSelect,
  nameKey,
  idKey,
  isMulti = true,
  selectAll = true,
  isSearchable = true,
  isDisabled,
  noSort = false,
  toggleAllSelections = (f) => f,
  canCreate = false,
  handleCreateClick,
}) => {
  const [showMenu, toggleMenu] = useState(false);
  const [searchInput, setSearchInput] = useState("");
  const [displayedOptions, setDisplayedOptions] = useState(list || []);
  const [allSelected, toggleAll] = useState(false);

  const menuRef = useRef(null);
  useOutsideClick(menuRef, () => {
    setSearchInput("");
    toggleMenu(false);
  });

  const selectedIds = useMemo(() => {
    if (!selected.length) return [];
    return selected.map((item) => item.id);
  }, [selected]);

  useEffect(() => {
    if (!selected.length) return;
    if (selected.length === displayedOptions.length) {
      toggleAll(true);
    } else {
      toggleAll(false);
    }
  }, [selected, displayedOptions]);

  useEffect(() => {
    if (!list) return;
    let f_list;

    if (searchInput) {
      f_list = list
        .filter((item) =>
          `(${item[idKey]}) ${item[nameKey]}`
            .toLowerCase()
            .includes(searchInput.toLowerCase())
        )
        .filter((item) => !selectedIds.includes(item.id));

      return setDisplayedOptions([...selected, ...f_list]);
    }

    if (selectedIds.length) {
      f_list = list.filter((item) => !selectedIds.includes(item.id));
    } else if (!noSort)
      f_list = list.sort((a, b) => a[nameKey].localeCompare(b[nameKey]));
    else f_list = list;

    setDisplayedOptions([...selected, ...f_list]);
  }, [list, searchInput, nameKey, idKey, type, selectedIds]);

  const handleRemoveSelected = (item) => {
    const newSelected = selected.filter((option) => option.id !== item.id);
    handleParentSelect(newSelected);
  };

  const formatName = (item) => {
    if (nameKey && idKey) {
      if (!item[idKey]) return `${item[nameKey]}`;
      return `(${item[idKey]}) ${item[nameKey]}`;
    } else if (nameKey) return `${item[nameKey]}`;
  };

  const handleSelection = (item) => {
    if (!isMulti) {
      if (selected.length && selected[0].id === item.id) {
        // already selected, remove item
        handleRemoveSelected(item);
        toggleMenu(false);
        return;
      } else {
        toggleMenu(false);
        handleParentSelect([item]);
        return;
      }
    }
    const selectedIds = selected.map((i) => i.id);
    // check if the item is already selected
    if (selected.length && selectedIds.includes(item.id)) {
      handleRemoveSelected(item);
      return;
    }
    handleParentSelect([...selected, item]);
  };

  const handleSelectAll = () => {
    if (allSelected) {
      handleParentSelect([]);
      toggleAll(false);
      toggleAllSelections(false);
    } else {
      handleParentSelect(displayedOptions);
      toggleAll(true);
      toggleAllSelections(true);
    }
  };

  // if we want the filter to auto add selections
  // useEffect(() => {
  //   if (isAllSelected) {
  //     handleParentSelect([...displayedOptions]);
  //   }
  // }, [isAllSelected, displayedOptions]);

  useEffect(() => {
    if (!selected.length && allSelected) toggleAll(false);
  }, [selected, allSelected]);

  return (
    <>
      <div className="inline-selectable-filter-wrapper">
        <div
          onClick={() => {
            if (isDisabled) return;
            toggleMenu(!showMenu);
          }}
          className={
            isDisabled
              ? "disabled inline-filter-button"
              : "inline-filter-button"
          }
        >
          <p>{type}</p>
          <FiPlus />
        </div>
        <div className="inline-selected-options">
          {selected &&
            selected.length > 0 &&
            selected.map((item) => (
              <div key={item.id} className="selected-item">
                <p>{formatName(item)}</p>
                <FiX
                  className={isDisabled ? "disabled" : ""}
                  onClick={() => {
                    if (isDisabled) return;
                    handleRemoveSelected(item);
                  }}
                />
              </div>
            ))}
        </div>
        {showMenu && (
          <div ref={menuRef} className="menu">
            {isSearchable && (
              <div className="search-wrapper">
                <FiSearch />
                <input
                  type="text"
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                />
              </div>
            )}
            <div className="menu-list">
              {selectAll && isMulti && list.length > 0 && (
                <div className="select-all" onClick={handleSelectAll}>
                  {allSelected ? `Unselect All` : `Select All`}
                </div>
              )}
              {selected && selected.length ? (
                selected.map((item) => (
                  <div
                    className="selected menu-item"
                    key={item.id}
                    onClick={() => handleSelection(item)}
                  >
                    {formatName(item)}
                  </div>
                ))
              ) : (
                <></>
              )}
              {canCreate && (
                <div
                  className="menu-item action"
                  key="create"
                  onClick={() => {
                    toggleMenu(false);
                    handleCreateClick();
                  }}
                >
                  Create New Package
                </div>
              )}
              {displayedOptions && displayedOptions.length ? (
                displayedOptions
                  .filter((o) => !selected.find((s) => s.id === o.id))
                  .map((item) => (
                    <div
                      className="menu-item"
                      key={item.id}
                      onClick={() => handleSelection(item)}
                    >
                      {formatName(item)}
                    </div>
                  ))
              ) : (
                <></>
              )}
              {(!displayedOptions || !displayedOptions.length) &&
              (!selected || !selected.length) ? (
                <p className="no-items-message">{`No ${type}`}</p>
              ) : (
                <></>
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default InlineFilter;
