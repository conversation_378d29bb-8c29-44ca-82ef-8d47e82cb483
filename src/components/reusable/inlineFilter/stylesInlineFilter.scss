@import "../../styles/colors.scss";

div.inline-selectable-filter-wrapper {
  display: flex;
  position: relative;

  & .disabled {
    opacity: 0.7;
    cursor: default;
  }

  & div.menu {
    position: fixed;
    display: flex;
    flex-direction: column;
    width: 200px;
    min-height: 40px;
    max-height: 180px;
    z-index: 101;
    background-color: $bgDark !important;
    border-radius: 3px;
    border: 1px solid $blue;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);

    & .search-wrapper {
      display: flex;
      align-items: center;
      padding-left: 8px;
      height: 28px;
      background-color: $lighterGrey;
      border-top-left-radius: 3px;
      border-top-right-radius: 3px;

      & svg {
        color: $blue;
      }
      & input[type="text"] {
        background: transparent;
        color: white;
        margin-left: 10px;
        flex-grow: 1;
        border: none;

        &:focus {
          border: none;
          outline: none;
        }
      }
    }

    & .menu-list {
      background-color: $bgDark;
      max-height: 150px;
      overflow-y: auto;
      overflow-x: hidden;

      &::-webkit-scrollbar {
        height: 8px;
        width: 10px;
        background-color: #555;
        border-radius: 3px;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 2px;
        background-color: #f5f5f5;
      }

      & div.select-all {
        display: flex;
        align-items: center;
        height: 30px;
        color: $lighterSlate;
        font-size: 0.8rem;
        padding-left: 8px;
        border-bottom: 1px solid #3b465e;
        cursor: pointer;
        box-sizing: border-box;
        font-weight: bold;
      }

      & div.menu-item {
        display: flex;
        align-items: center;
        width: 100%;
        border-bottom: 1px solid #3b465e;
        color: $lighterSlate;
        font-size: 0.8rem;
        padding-left: 8px;
        cursor: pointer;
        box-sizing: border-box;

        &:last-of-type {
          border-bottom: none;
          // background-color: $bgDark;
        }
      }

      & div.menu-item.action {
        color: $fabProBlue;
      }

      & .no-items-message {
        color: $lighterSlate;
        margin: 0;
        padding: 5px;
      }

      & .selected {
        background-color: $blue;
        color: white !important;
        border: 1px solid $blue !important;
      }
    }
  }
}

div.inline-filter-button {
  display: flex;
  align-items: center;
  height: 35px;
  color: $blue;
  background-color: transparent;
  cursor: pointer;

  & p {
    margin: 0 5px 0;
    padding: 0;
    font-weight: normal;
    font-size: 0.8rem;
  }
  & svg {
    font-size: 1.1rem;
  }
}

div.inline-selected-options {
  display: flex;
  align-items: center;
  margin-left: 10px;
  overflow-x: auto;

  &::-webkit-scrollbar {
    height: 8px;
    width: 10px;
    background-color: #f5f5f5;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #555;
  }

  & .selected-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: white;
    width: 125px;
    // height: 18px !important;
    padding: 3px 5px;
    background-color: #4d5971;
    // padding: 0;
    font-size: 0.7rem;
    white-space: nowrap;
    margin: 0 5px;

    border-radius: 0;

    & p {
      overflow: hidden;
      max-width: 105px;
      margin: 0;
    }

    & svg {
      cursor: pointer;
      margin-left: 5px;
    }
  }
}

div.inline-selectable-filter-wrapper div.menu {
  position: absolute;
  display: flex;
  flex-direction: column;
  max-width: 200px;
  min-width: 180px;
  // min-height: 100px;
  // top: 80px;
  background-color: $bgDark !important;
  border-radius: 3px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);

  & .search-wrapper {
    display: flex;
    align-items: center;
    padding-left: 8px;
    height: 28px;
    background-color: $lighterGrey;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;

    & svg {
      color: $blue;
    }
    & input[type="text"] {
      background: transparent;
      color: white;
      margin-left: 10px;
      flex-grow: 1;
      border: none;

      &:focus {
        border: none;
        outline: none;
      }
    }
  }

  & .menu-list {
    background-color: $bgDark;
    max-height: 150px;
    overflow-y: auto;
    overflow-x: hidden;

    &::-webkit-scrollbar {
      width: 10px;
      background-color: #555;
      border-radius: 3px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background-color: #f5f5f5;
    }

    & div.select-all {
      display: flex;
      align-items: center;
      height: 30px;
      color: $lighterSlate;
      font-size: 0.8rem;
      padding-left: 8px;
      border-bottom: 1px solid #3b465e;
      cursor: pointer;
      box-sizing: border-box;
      font-weight: bold;
    }

    & div.menu-item {
      display: flex;
      align-items: center;
      width: 100%;
      min-height: 30px;
      border-bottom: 1px solid #3b465e;
      color: $lighterSlate;
      font-size: 0.8rem;
      padding-left: 8px;
      cursor: pointer;
      white-space: normal;
      box-sizing: border-box;

      &:last-of-type {
        border-bottom: none;
        // background-color: $bgDark;
      }
    }

    & .no-items-message {
      color: $lighterSlate;
      margin: 0;
      padding: 5px;
    }

    & .selected {
      background-color: $blue;
      color: white !important;
      border: 1px solid $blue !important;
    }
  }
}
