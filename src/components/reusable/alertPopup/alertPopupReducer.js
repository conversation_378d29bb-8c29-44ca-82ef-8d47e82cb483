import Queue from "../queue/Queue";

const initialState = {
  messages: new Queue("MESSAGE"),
};

export default function reducer(state = initialState, { type, payload }) {
  let newMessages;
  switch (type) {
    case "ENQUEUE_MESSAGE":
      newMessages = state.messages;
      newMessages.enqueue(payload);
      return { ...state, messages: newMessages };
    case "DEQUEUE_MESSAGE":
      newMessages = state.messages;
      newMessages.dequeue(payload);
      return { ...state, messages: newMessages };
    default:
      return state;
  }
}
