// NPM PACKAGE IMPORTS
import React from "react";
import { useSelector } from "react-redux";

// COMPONENT IMPORTS
import AlertPopup from "./AlertPopup";

// STYLE IMPORTS
import "./stylesAlertPopup.scss";

// EXPORTS
const AlertPopupContainer = () => {
  const { messages } = useSelector((state) => state.alertPopupData);

  return (
    <div className="alert-popup-container">
      {messages.data.map((message) => {
        if (message.shown)
          return <React.Fragment key={message.id}></React.Fragment>;
        return <AlertPopup alert={message} key={message.id} />;
      })}
    </div>
  );
};

export default AlertPopupContainer;
