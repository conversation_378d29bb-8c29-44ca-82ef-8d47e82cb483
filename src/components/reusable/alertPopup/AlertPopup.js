// NPM PACKAGE IMPORTS
import React, { useState, useEffect, useCallback } from "react";
import PropTypes from "prop-types";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import { useDispatch } from "react-redux";

// REDUX IMPORTS
import { handleDequeueMessage } from "./alertPopupActions";

// EXPORTS
const AlertPopup = ({ alert }) => {
  const [pinned, setPinned] = useState(false);
  const [show, setShow] = useState(true);
  const [closeTimeout, setCloseTimeout] = useState(null);

  const dispatch = useDispatch();

  const startTimeout = useCallback(() => {
    if (pinned) return;
    return setCloseTimeout(
      setTimeout(() => {
        setShow(false);
        dispatch(handleDequeueMessage(alert.id));
      }, 6500)
    );
  }, [dispatch, pinned]);

  const removeTimeout = () => clearTimeout(closeTimeout);

  useEffect(() => {
    startTimeout();
  }, [startTimeout]);

  const alertType = () => {
    switch (alert.type) {
      case "SUCCESS":
        return "success";
      case "WARN":
        return "warn";
      case "ERROR":
        return "error";
      default:
        return "";
    }
  };
  const className = `alert-popup ${pinned ? "pinned" : ""} ${alertType()}`;
  return (
    <>
      {show && (
        <div
          className={className}
          key={alert.id}
          onMouseEnter={removeTimeout}
          onMouseLeave={startTimeout}
        >
          <button
            onClick={() => {
              const currPinned = pinned;
              setPinned(!currPinned);
              if (!currPinned) return removeTimeout();
              return startTimeout();
            }}
            className={`alert-popup-pin ${pinned ? "pinned" : ""}`}
          ></button>
          <FontAwesomeIcon
            icon={faTimes}
            className="alert-popup-close"
            onClick={() => setShow(false)}
          />
          <p className="alert-popup-message">{alert.message}</p>
        </div>
      )}
    </>
  );
};

AlertPopup.propTypes = {
  alert: PropTypes.shape({
    id: PropTypes.string,
    message: PropTypes.string,
    type: PropTypes.string,
    shown: PropTypes.bool,
  }),
};

export default React.memo(AlertPopup);
