@import "../../styles/colors.scss";

@keyframes fade {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

div.alert-popup-container {
  position: fixed;
  top: 10px;
  right: 10px;
  z-index: 100;

  display: grid;
  grid-row-gap: 5px;
  width: 200px;

  & div.alert-popup {
    font-size: 0.8rem;
    padding: 5px 10px;
    border-radius: 3px;
    border: 1px solid $grey;

    position: relative;

    &.success {
      background-color: $lightGreen;
      color: #fff;

      & button.alert-popup-pin {
        border-radius: 50%;
        border: 1px solid #fff;
      }
      & svg.alert-popup-close:hover {
        color: darken(#fff, 15%);
      }
    }
    &.warn {
      background-color: $yellow;
      color: #000;

      & svg.alert-popup-close:hover {
        color: lighten(#000, 15%);
      }
    }
    &.error {
      background-color: $red;
      color: #000;

      & svg.alert-popup-close:hover {
        color: lighten(#000, 15%);
      }
    }

    &:not(.pinned):not(:hover) {
      animation: fade 4s 3s 1;
    }

    & button.alert-popup-pin {
      border-radius: 50%;
      border: 1px solid #000;
      width: 15px;
      height: 15px;
      background-color: transparent;
      transform: scale(0.8);
      cursor: pointer;

      position: absolute;
      right: 20px;

      &.pinned {
        background-color: $fabProBlue;
      }
    }

    & svg.alert-popup-close {
      position: absolute;
      right: 5px;

      font-size: 15px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    & p.alert-popup-message {
      margin: 15px 0 0;
    }
  }
}
