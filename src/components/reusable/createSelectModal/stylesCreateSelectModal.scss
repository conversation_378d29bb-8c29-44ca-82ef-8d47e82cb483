@import "../../styles/colors.scss";

div.create-select-dropdown-modal {
  display: grid;
  row-gap: 10px;
  background-color: white;

  & > h2.title {
    color: #fff;
    background-color: $fabProBlue;
    margin: 0;
    padding: 0 0 0 10px;
    font-size: 0.9rem;
    line-height: 30px;
  }

  & > div.content {
    position: relative;
    padding: 0 20px;

    & > label.label {
      line-height: 20px;
    }

    & > div {
      height: 30px;

      & > input {
        height: 30px;
        font-size: 1rem;
      }
    }

    & > ul.dropdown {
      position: absolute;
      top: 50px;
      left: 20px;
      min-width: calc(100% - 40px);
      margin: 0;
      padding: 0;
      background-color: $lighterGrey;
      color: #fff;
      max-height: 300px;
      overflow-y: scroll;
      z-index: 1;

      &::-webkit-scrollbar {
        width: 10px;
        background-color: #f5f5f5;
        border-radius: 3px;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 2px;
        background-color: #555;
      }

      & > li.dropdown-option {
        cursor: pointer;
        padding: 5px;

        &:hover {
          background-color: $fabProBlue;
        }
      }
    }
  }

  & > div.actions {
    padding: 0 10px 10px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    column-gap: 10px;

    & > button {
      border: 1px solid #333;
      padding: 0;
      height: 30px;
    }

    & > button.cancel {
      background-color: #fff;
      color: #333;

      &:hover {
        background-color: darken(#fff, 10%);
      }
    }

    & > button.submit {
      background-color: $lightGreen;
      color: #fff;

      display: flex;
      justify-content: space-around;
      align-items: center;

      &:hover {
        background-color: darken($lightGreen, 10%);
      }
    }
  }
}
