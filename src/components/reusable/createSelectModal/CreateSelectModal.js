// NPM PACKAGE IMPORTS
import React, { useEffect, useMemo, useState, useRef } from "react";
import { useDispatch } from "react-redux";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import Input from "msuite_storybook/dist/input/Input";
import { FaArrowRight } from "react-icons/fa";

import {
  handleUpdateItems,
  handleUpdateGroupedItems,
  handleFetchItems,
  handleFetchMaterialTypes,
  handleCreateLaydownLocation,
  handleUpdateItemNoRefresh,
} from "../../items/itemsActions";
import { notify } from "../alertPopup/alertPopupActions";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../hooks/useOutsideClick";
import { escapeRegExp, naturalSort, titleize } from "../../../_utils";

// STYLE IMPORTS
import "./stylesCreateSelectModal.scss";

const CreateSelectModal = ({
  items,
  rowInfo,
  open,
  handleClose,
  gridOptionsApi,
  isGrouped,
  quantity,
  callback,
  tableType = "WIZARD",
  idKey,
  nameKey,
  selectedFilterIds,
  groupingColumns = null,
}) => {
  const [selected, setSelected] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const [showDropdown, toggleDropdown] = useState(false);
  const [isNewValue, toggleNewValue] = useState(false);

  const dispatch = useDispatch();

  const labelRef = useRef(null);
  const dropdownRef = useRef(null);
  useOutsideClick(dropdownRef, () => toggleDropdown(false));

  useEffect(() => {
    if (!rowInfo) return;
    setSearchValue("");
    toggleDropdown(false);
    if (rowInfo[rowInfo._column]) setSelected(rowInfo[rowInfo._column]);
  }, [rowInfo]);

  useEffect(() => {
    if (labelRef.current) {
      toggleDropdown(true);
    }
  }, [labelRef.current]);

  const handleSubmit = () => {
    if (selected === rowInfo[rowInfo._column]) return;
    const rowNode = gridOptionsApi.getRowNode(rowInfo.id);

    if (
      isNewValue &&
      (tableType === "JOBS" || tableType === "BOM" || !rowInfo.empty)
    ) {
      if (idKey === "laydown_location_id") {
        if (searchValue === "") {
          dispatch(
            handleUpdateItems([rowInfo.id], { laydown_location_id: null })
          ).then((res) => {
            if (!res.error && !rowInfo.empty && tableType === "WIZARD") {
              rowNode.setData(res[0]);
              dispatch(handleUpdateItemNoRefresh(rowNode.data));
            }
          });
        } else
          dispatch(
            handleCreateLaydownLocation(
              rowNode.id ? [rowNode.id] : rowNode.work_item_ids.split(","),
              searchValue
            )
          );
      } else if (idKey === "joining_procedure_id" && searchValue === "") {
        if (isGrouped && quantity) {
          dispatch(
            handleUpdateGroupedItems(
              rowInfo.work_item_ids,
              { joining_procedure_name: null },
              parseFloat(quantity)
            )
          ).then(() => {
            dispatch(
              handleFetchItems(...selectedFilterIds, false, 1, groupingColumns)
            );
            handleClose();
          });
        } else {
          dispatch(
            handleUpdateItems([rowInfo.id], {
              joining_procedure_name: null,
            })
          ).then((res) => {
            if (!res.error) {
              if (!rowInfo.empty && tableType === "WIZARD") {
                rowNode.setData(res[0]);
                dispatch(handleUpdateItemNoRefresh(rowNode.data));
              }
            }
          });
        }
      } else if (searchValue.trim() !== "") {
        // inline new material
        if (nameKey === "material_name") {
          dispatch(
            notify({
              id: Date.now(),
              type: "WARN",
              message:
                "If selected material does not match the conditions for this stage, the item may no longer be available at this stage.",
            })
          );
        }
        if (isGrouped && quantity) {
          dispatch(
            handleUpdateGroupedItems(
              rowInfo.work_item_ids,
              {
                [nameKey]: searchValue.trim(),
              },
              parseFloat(quantity)
            )
          ).then(() => {
            dispatch(
              handleFetchItems(...selectedFilterIds, false, 1, groupingColumns)
            );
            handleClose();
          });
        } else {
          dispatch(
            handleUpdateItems([rowInfo.id], {
              [nameKey]: searchValue.trim(),
            })
          ).then((res) => {
            if (!res.error) {
              rowNode.setData(res[0]);
              dispatch(handleUpdateItemNoRefresh(rowNode.data));
            }
          });
        }
      }
    } else if (
      !isNewValue &&
      (tableType === "JOBS" || tableType === "BOM" || !rowInfo.empty)
    ) {
      // inline - existing values
      if (idKey === "material_type_id") {
        dispatch(
          notify({
            id: Date.now(),
            type: "WARN",
            message:
              "If selected material does not match the conditions for this stage, the item may no longer be available at this stage.",
          })
        );
      }
      if (isGrouped && quantity) {
        dispatch(
          handleUpdateGroupedItems(
            rowInfo.work_item_ids,
            {
              [idKey]: items.find((i) => i.name === selected)?.id || null,
            },
            parseFloat(quantity)
          )
        ).then(() => {
          dispatch(
            handleFetchItems(...selectedFilterIds, false, 1, groupingColumns)
          );
          handleClose();
        });
      } else {
        dispatch(
          handleUpdateItems([rowInfo.id], {
            [idKey]: items.find((i) => i.name === selected)?.id || null,
          })
        ).then((res) => {
          if (!res.error && rowNode) {
            gridOptionsApi.stopEditing();
            rowNode.setData(res[0]);
            dispatch(handleUpdateItemNoRefresh(rowNode.data));
          }
        });
      }
    }

    if (
      tableType === "JOBS" ||
      tableType === "BOM" ||
      (tableType === "WIZARD" && !rowInfo.empty)
    ) {
      gridOptionsApi.stopEditing();
      // setData doesn't trigger onCellValueChanged which we want in this case or else success notification will display twice
      rowNode.setData({
        ...rowInfo,
        [rowInfo._column]: selected ? selected.trim() : searchValue.trim(),
      });
      // only want to update cell or else previous cell updated will be lost
    } else
      rowNode.setDataValue(
        rowInfo._column,
        selected ? selected.trim() : searchValue
      );

    if (rowInfo._column === "material_name") {
      dispatch(handleFetchMaterialTypes());
    }

    handleClose();

    if (tableType === "WIZARD" && rowInfo.empty) {
      const rowNode = gridOptionsApi.getRowNode(rowInfo.id);
      gridOptionsApi.setFocusedCell(0, rowInfo._column);
      gridOptionsApi.startEditingCell({
        rowIndex: rowNode.rowIndex,
        colKey: rowInfo._column,
      });
    }
    typeof callback === "function" && callback();
  };

  const disableSubmit = useMemo(() => {
    if (!rowInfo) return true;
    if (selected === rowInfo[rowInfo._column]) return true;
    if (searchValue && searchValue === rowInfo[rowInfo._column]) return true;

    return false;
  }, [rowInfo, selected, searchValue]);

  const displayedItems = useMemo(() => {
    const pattern = new RegExp(escapeRegExp(searchValue), "i");
    return items
      .filter((i) => pattern.test(i.name))
      .sort((a, b) => naturalSort(a.name, b.name));
  }, [items, searchValue]);

  return (
    <Modal open={open} handleClose={handleClose}>
      <div className="create-select-dropdown-modal">
        <h2 className="title">{titleize(rowInfo._column)}</h2>
        <div className="content">
          <label className="label" ref={labelRef}>
            Select existing or enter new:
          </label>
          <Input
            className="input"
            value={selected}
            onChange={(e) => {
              e.persist();
              toggleNewValue(true);
              setSearchValue(e.target.value);
              setSelected(e.target.value);
            }}
            onFocus={() => toggleDropdown(true)}
            autoFocus={true}
          />
          {showDropdown && (
            <ul ref={dropdownRef} className="dropdown">
              {displayedItems.map((i) => (
                <li
                  className="dropdown-option"
                  key={i.id}
                  onClick={() => {
                    toggleNewValue(false);
                    setSearchValue("");
                    setSelected(i.name);
                    toggleDropdown(false);
                  }}
                >
                  {i.name}
                </li>
              ))}
            </ul>
          )}
        </div>
        <div className="actions">
          <Button className="cancel" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            className="submit"
            onClick={handleSubmit}
            disabled={disableSubmit}
          >
            Submit <FaArrowRight />
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default CreateSelectModal;
