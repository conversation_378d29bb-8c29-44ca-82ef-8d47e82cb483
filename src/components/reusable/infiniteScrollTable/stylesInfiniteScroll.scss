@import "../../styles/colors.scss";

// Taken from tables.scss in msuite_web repo

.custom-ag-styles.ag-theme-balham-dark {
  font-size: 16px;
  height: 80vh;
  width: 90%;
  margin: auto;
  .ag-header-row {
    font-size: 14px;

    &:first-of-type {
      background-color: transparent;
      color: white;
    }
  }
  .ag-input-wrapper input:not([type]) {
    border-radius: 3px;
    font-size: 20px;
    color: black;
    background-color: white;
    border-color: $blue;
  }
  .custom-ag-row {
    .ag-cell-wrapper.ag-row-group {
      align-items: center;
    }
    .ag-cell {
      display: flex;
      align-items: center;
    }
  }

  .ag-side-bar .ag-side-buttons .ag-selected button,
  .ag-side-bar .ag-side-buttons .ag-selected .ag-icon {
    color: white;
  }

  .ag-side-bar .ag-side-buttons {
    background-color: $darkBlue;
    width: 40px;
    button {
      color: white;
      font-size: 16px;
      .ag-icon {
        color: white;
        font-size: 16px;
      }
    }

    .ag-paging-panel {
      height: 10%;
      font-size: 16px;
    }
    .ag-popup-editor {
      input {
        font-size: 16px;
        height: 45px;
        border-radius: 3px;
        background-color: white;
        color: black;
      }
    }
  }

  .ag-tool-panel-wrapper {
    .ag-primary-cols-list-panel {
      .ag-column-tool-panel-column {
        height: 40px;
      }
    }
  }
}
