/**
 * SelectAllHeader
 * Note: it takes a isSelectAll prop that returns the state of the checkbox
 */
import { useEffect, useState } from "react";

const SelectAllHeader = ({ api, onSelectAll, checkboxStateRef }) => {
  const [isChecked, setIsChecked] = useState(false);

  useEffect(() => {
    if (checkboxStateRef.current === 1) setIsChecked(true);
    else setIsChecked(false);
  }, [checkboxStateRef.current]);

  const handleSelectAll = (event) => {
    const checked = event.target.checked;
    setIsChecked(checked);

    // Select all visible rows in the grid
    api.forEachNode((node) => node.setSelected(checked));

    // Notify parent about the select all state
    if (onSelectAll) {
      onSelectAll(checked);
    }
  };

  let className = "";
  if (checkboxStateRef.current === 1) className = "ag-checked";
  if (checkboxStateRef.current === 2) className = "ag-indeterminate";

  return (
    <div
      className={`ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper ${className}`}
    >
      <input
        className="ag-input-field-input ag-checkbox-input "
        type="checkbox"
        checked={isChecked}
        onChange={handleSelectAll}
        title="Select All"
      />
    </div>
  );
};

export default SelectAllHeader;
