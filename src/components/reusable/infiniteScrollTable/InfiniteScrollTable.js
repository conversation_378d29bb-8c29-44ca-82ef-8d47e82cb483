// react imports
import React, { useEffect, useRef } from "react";
// ag-grid imports
import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-balham-dark.css";
import { LicenseManager } from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";

// util imports
import { rowClassRules } from "../../../_utils";

// style imports
import "../../styles/tables.scss";
import "./stylesInfiniteScroll.scss";
import SelectAllHeader from "./SelectAllHeader";

LicenseManager.setLicenseKey(process.env.REACT_APP_AG_GRID_LICENSE);

const InfiniteScrollTable = ({
  gridOptions,
  onSelectAll,
  selectedRowIds,
  totalNumberOfRows,
  setSelectedRows,
  overrideCheckboxSelectColumn,
}) => {
  // props are supposed to be immutable but we don't have typescript enforcement here so adding this
  overrideCheckboxSelectColumn = !!overrideCheckboxSelectColumn;
  totalNumberOfRows = totalNumberOfRows || 0;

  const isSelectAllCheckedRef = useRef(false);
  const selectedRowIdsRef = useRef([]);
  const gridAPI = useRef(null);
  // Thiu is passed to the SelectAll header component in ag-grid to update the checkbox state
  const headerCheckboxStateRef = useRef(0);
  // 0 = no   1 = yes   2 = partial

  // This useEfffect handles the select all checkbox state of the infinate grid
  useEffect(() => {
    if (Array.isArray(selectedRowIds)) {
      selectedRowIdsRef.current = [...selectedRowIds];
      if (totalNumberOfRows === 0 || selectedRowIds.length === 0)
        headerCheckboxStateRef.current = 0;
      else if (selectedRowIds.length >= totalNumberOfRows)
        headerCheckboxStateRef.current = 1;
      else headerCheckboxStateRef.current = 2; // partial
      // Here we have to refresh the grid headers, all of them annoyingly so that the checkbox gets updated
      gridAPI.current && gridAPI.current.refreshHeader();
    }
  }, [selectedRowIds, totalNumberOfRows]);

  const sideBar = {
    toolPanels: [
      {
        id: "columns",
        labelDefault: "Columns",
        labelKey: "columns",
        iconKey: "columns",
        toolPanel: "agColumnsToolPanel",
        toolPanelParams: {
          suppressRowGroups: true,
          suppressValues: true,
          suppressPivots: true,
          suppressPivotMode: true,
          suppressSideButtons: true,
          suppressColumnFilter: true,
          suppressColumnSelectAll: true,
          suppressColumnExpandAll: true,
        },
      },
    ],
  };

  /**
   * @type {import('ag-grid-community').GridOptions}
   */
  const computedGridOptions = {
    ...gridOptions,
    rowModelType: "infinite",
    rowClassRules,
    suppressRowHoverHighlight: true,
    sideBar,
  };

  // If the option is on, then lets set up the custom header checkbox
  if (overrideCheckboxSelectColumn === true) {
    // Make sure the table maintains the selected state as you scroll
    computedGridOptions.onViewportChanged = (params) => {
      gridAPI.current = params.api;
      params.api.forEachNode((node) => {
        if (selectedRowIdsRef.current.includes(node.data?.id)) {
          node.setSelected(true);
        } else {
          node.setSelected(false);
        }
      });
    };

    // Capture when a single row is selected/unselected and update the selectRowsState (with function)
    computedGridOptions.onRowSelected = (event) => {
      const rowId = event.node.id;
      setSelectedRows((prevState) => {
        if (event.node.isSelected()) {
          if (!prevState.some((item) => item.id === rowId)) {
            return [...prevState, event.node.data];
          }
          return prevState;
        } else {
          return prevState.filter((item) => item.id !== rowId);
        }
      });
    };

    // Add the select all checkbox
    computedGridOptions.frameworkComponents = {
      ...gridOptions.frameworkComponents,
      SelectAllHeader: SelectAllHeader,
    };

    // Update the definition of the header checkbox column;
    const headerCheckboxColumn = computedGridOptions.columnDefs.find(
      (columnDef) => columnDef.checkboxSelection === true
    );
    if (!headerCheckboxColumn) {
      console.error("Can't find the checkbox column on this table");
    }
    if (headerCheckboxColumn) {
      headerCheckboxColumn.colId = "checkbox";
      headerCheckboxColumn.headerComponent = "SelectAllHeader";
      headerCheckboxColumn.headerComponentParams = {
        // Pass our ref in so we can control the checkbox
        checkboxStateRef: headerCheckboxStateRef,
        onSelectAll: (isChecked) => {
          isSelectAllCheckedRef.current = isChecked;
          return onSelectAll(isChecked);
        },
      };
    }
  }

  /// Dont even bother rendering the table is we don't have column definitions, just causes problems otherwise
  if (
    overrideCheckboxSelectColumn &&
    (!computedGridOptions.columnDefs || !computedGridOptions.columnDefs.length)
  ) {
    return null;
  }

  return (
    <div className="ag-theme-balham-dark custom-ag-styles">
      <AgGridReact
        gridOptions={computedGridOptions}
        rowClass="custom-ag-row"
        rowHeight={60}
      />
    </div>
  );
};

export default InfiniteScrollTable;
