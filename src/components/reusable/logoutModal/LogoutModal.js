// NPM PACKAGE IMPORTS
import React from "react";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import { BsExclamationCircle } from "react-icons/bs";

// STYLE IMPORTS
import "./logoutModal.scss";

// EXPORTS
const LogoutModal = ({ showModal, onClose, fullLogout, logout }) => (
  <Modal open={showModal} handleClose={onClose}>
    <div className="logout-modal__wrapper">
      <BsExclamationCircle />
      <p className="sub-title">Are you ending your session?</p>
      <p>
        If you are ending your session as well as logging out, select Log out
        and end session, if not select Log out
      </p>
      <span className="button-row">
        <Button className="cancel" onClick={onClose}>
          Cancel
        </Button>
        <Button className="submit" onClick={fullLogout}>
          Log out and End Session
        </Button>
        <Button className="submit" onClick={() => logout(0)}>
          Log out
        </Button>
      </span>
    </div>
  </Modal>
);

export default LogoutModal;
