@import "../../styles/colors.scss";

.logout-modal__wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  color: #333;
  background-color: #fff;

  & > svg {
    font-size: 6rem;
    color: $yellow;
    background-color: #fff;
    border-radius: 50%;
  }

  & > p.sub-title {
    font-size: 1.3rem;
  }

  & > p {
    text-align: center;
    font-size: 0.9rem;
    margin: 10px;
    width: 400px;
  }

  & > span.button-row {
    display: flex;
    justify-content: space-evenly;
    width: 95%;
    margin: 20px auto 0;

    & > button {
      border: none;
      padding: 8px 14px;
      color: #fff;
      font-weight: bold;
      border-radius: 3px;
      height: 38px;
      cursor: pointer;
      font-size: 12px;
    }

    & > button.cancel {
      background-color: #fff;
      color: #333;
      border: 1px solid #333;

      &:hover {
        background-color: darken(#fff, 10%);
      }
    }

    & > button.submit {
      background-color: $red;

      &:hover {
        background-color: darken($red, 10%);
      }

      &:last-of-type {
        background-color: $fabProBlue;

        &:hover {
          background-color: darken($fabProBlue, 10%);
        }
      }
    }
  }
}
