@import "../../styles/colors.scss";
@import "../../styles/sizes.scss";

// forge - 3D / 2D / Dual styling when opened from drawing name
div.viewer {
  & div {
    height: calc(100% - 5px);

    & iframe.narrow {
      width: 100%;
      height: 100%;
      transform: none;
    }
  }

  & > div.forge-dual {
    display: flex;
    width: 100%;

    iframe.forge-viewer-iframe {
      width: 50%;
      transform: none;
    }

    & > div.forge-pspdfkit-wrapper {
      width: 50%;
    }
  }

  & > div.forge-2d {
    display: flex;

    iframe.forge-viewer-iframe {
      height: 0;
      width: 0;
      transform: none;
    }

    & > div.forge-pspdfkit-wrapper {
      height: 100%;
      width: 100%;
    }

    & + button.close-forge-modal {
      display: none;
    }

    & button.close-forge-2d {
      position: absolute;
      right: 25vw;
      top: 39px;
      color: #fff;
      background-color: $fabProBlue;
      border: none;
      padding: 10px;

      @media screen and (max-width: 1500px) and (min-width: 1052px) {
        right: 40vw;
      }
      @media screen and (max-width: 920px) and (min-width: 770px) {
        right: 33vw;
      }
      @media screen and (max-width: 770px) and (min-width: 745px) {
        right: 35vw;
      }
      @media screen and (max-width: 745px) {
        top: 89px;
        right: calc(50vw - #{$navCollapsed});
      }

      &:hover {
        cursor: pointer;
      }
    }
  }

  & div.forge-pspdfkit-wrapper {
    & > section {
      height: calc(100vh - #{$headerFooter});
    }
  }
}

// Close 2D Viewer btn placement
div.narrow > div.viewer > div.forge-2d button.close-forge-2d {
  right: 25vw;

  @media screen and (max-width: 1500px) and (min-width: 1280px) {
    right: 35vw;
  }
  @media screen and (max-width: 970px) {
    top: 89px;
    right: calc(50vw - (#{$navExpanded} / 2));
  }
}

// general
iframe.forge-viewer-iframe {
  height: calc(100vh - #{$headerFooter});
  width: calc(100vw - #{$navCollapsed});
  transform: translate(28px, 7px);
  border: none;

  &.narrow {
    width: calc(100vw - #{$navExpanded});
    transform: translate(127px, 7px);
  }

  &.modal-layout {
    width: 100%;
    height: 100%;
    transform: none;
  }

  @media screen and (max-width: 600px) and (orientation: portrait) {
    height: 100vw;
    width: 100vw;
  }

  @media screen and (max-height: 600px) and (orientation: landscape) {
    height: 100vh;
    width: 100vh;
  }
}

div.forge-viewer {
  & > iframe.forge-viewer-iframe {
    height: 100%;

    &.narrow {
      width: 100%;
      transform: none;
    }
  }
}

div.forge-viewer-modal-exit-prompt {
  display: grid;
  grid-template-areas:
    "p0 p0"
    "b1 b2";
  background-color: #fff;
  padding: 10px;

  & > p {
    grid-area: p0;
    font-size: 1.2rem;
    padding: 10px;
  }

  & > button {
    width: 100px;
    font-size: 1rem;
    padding: 0;
    justify-self: center;
    border: 1px solid #333;
    font-weight: 600;
  }

  & > button.cancel {
    grid-area: b1;
    background-color: #fff;
    &:hover {
      background-color: darken(#fff, 10%);
    }
  }

  & > button.submit {
    grid-area: b2;
    background-color: $green;
    color: #fff;
    &:hover {
      background-color: darken($green, 10%);
    }
  }
}

// forge - 3D / 2D / Dual styling when opened from Manage/3D Model
div.forge-viewer-modal {
  position: relative;

  & > button.close-forge-modal {
    background-color: transparent;
    color: grey;
    padding: 0;
    position: absolute;
    top: 10px;
    left: 35px;
    box-shadow: none;
    font-size: 35px;

    &.far {
      left: 135px;
    }

    &:hover {
      color: darken(grey, 20%);
      cursor: pointer;
    }

    &:active {
      box-shadow: none;
    }
  }

  & > div.forge-dual {
    display: flex;
    width: calc(100vw - #{$navCollapsed});
    justify-content: space-between;
    transform: translate(28px, 7px);

    iframe.forge-viewer-iframe {
      width: calc(50vw - (#{$navCollapsed} / 2));
      transform: none;
    }

    & > div.forge-pspdfkit-wrapper {
      width: calc(50vw - (#{$navCollapsed} / 2));
    }
  }

  & > div.forge-2d {
    display: flex;
    width: calc(100vw - #{$navCollapsed});
    transform: translate(25px, 5px);

    iframe.forge-viewer-iframe {
      height: 0;
      width: 0;
      transform: none;
    }

    & > div.forge-pspdfkit-wrapper {
      width: 100vw;
    }

    & + button.close-forge-modal {
      display: none;
    }

    & button.close-forge-2d {
      position: absolute;
      right: 25vw;
      top: 5px;
      color: #fff;
      background-color: $fabProBlue;
      border: none;
      padding: 10px;

      @media screen and (max-width: 1500px) and (min-width: 990px) {
        right: 35vw;
      }
      @media screen and (max-width: 990px) and (min-width: 650px) {
        right: 23vw;
      }
      @media screen and (max-width: 650px) {
        top: 50px;
        right: calc(50vw - #{$navCollapsed});
      }

      &:hover {
        cursor: pointer;
      }
    }
  }

  & div.forge-pspdfkit-wrapper {
    & > section {
      height: calc(100vh - #{$headerFooter});
    }
  }
}

div.narrow {
  div.forge-viewer-modal {
    iframe.forge-viewer-iframe {
      width: calc(100vw - #{$navExpanded});
    }
    div.forge-dual {
      width: calc(100vw - #{$navExpanded});
      transform: translate(130px, 5px);

      & > div.forge-pspdfkit-wrapper {
        width: calc(50vw - (#{$navExpanded} / 2));
      }

      iframe.forge-viewer-iframe {
        width: calc(50vw - (#{$navExpanded} / 2));
      }
    }

    div.forge-2d {
      width: calc(100vw - #{$navExpanded});

      & > div.forge-pspdfkit-wrapper {
        width: 100vw;
        transform: translate(100px, 0px);
      }

      & button.close-forge-2d {
        @media screen and (max-width: 1290px) and (min-width: 990px) {
          right: 20vw;
        }
        @media screen and (max-width: 900px) {
          top: 50px;
          right: calc(50vw - #{$navExpanded});
        }
      }
    }
  }
}
