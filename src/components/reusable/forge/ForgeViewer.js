// NPM PACKAGE IMPORTS
import React, { useCallback, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import PSPDFKitViewer from "../pdfViewer/PSPDFKitViewer";
import jwt from "jsonwebtoken";
import PropTypes from "prop-types";

// HELPER FUNCTIONS IMPORTS
import { DISPLAY_LENGTH } from "./../../../_utils";

const ForgeViewer = ({
  appType = "fab",
  token,
  userId,
  systemSettings,
  permissions,
  spool = {},
  toggleCloudSheetCreation = (f) => f,
  setCloudSheetSpoolImage = (f) => f,
  setCloudSheetItems = (f) => f,
  setSheetCallback = (f) => f,
  useModalLayout = false,
  navExpanded = false,
  origin = "web",
  area,
  storeInstanceOutside,
  userInfo,
  refresh,
}) => {
  const [viewMode, setViewMode] = useState(0);
  const [currentRow, setCurrentRow] = useState({});

  const { drawingFiles } = useSelector((state) => state.filesData);

  let zohoContactId = 1;
  let systemFeatures = [];
  if (token) {
    let tokenData = jwt.decode(token).data;
    zohoContactId = tokenData.zohoContactId;
    systemFeatures = tokenData.features;
  }

  const onIframeLoaded = useCallback(() => {
    const forgeViewerIframe = document.getElementById("forge-viewer-iframe");

    // pass in only values we want to set, defaults has fallbacks
    forgeViewerIframe.contentWindow.postMessage(
      {
        function: `
      createScripts({
        jwt: '${token}',
        api_path: '${process.env.REACT_APP_API}',
        drawing: { 
          ...${JSON.stringify(spool)},
          id: ['PACKAGES', 'JOBS'].includes('${spool.type}') 
            ? null 
            : '${spool.type}' === 'DRAWINGS'
              ? ${JSON.stringify(spool.id)}
              : ${JSON.stringify(spool.drawing_id)}, 
          name: ['PACKAGES', 'JOBS'].includes('${spool.type}') 
            ? null 
            : '${spool.type}' !== 'DRAWINGS'
              ? '${spool.drawing_name}'
              : '${spool.name}',
          forge_urn: '${spool.forge_urn}',
          model_name: '${spool.model_name}',
          package_id: '${spool.type}' === 'JOBS' ? null : '${
          spool.type
        }' === 'PACKAGES' ? ${JSON.stringify(spool.id)} : ${JSON.stringify(
          spool.package_id
        )},
          job_id: '${spool.type}' === 'JOBS' ? ${JSON.stringify(
          spool.id
        )} : ${JSON.stringify(spool.job_id)},
        type: '${spool.type}'
        },
        appType: '${appType}',
        env: '${process.env.REACT_APP_ENVIRONMENT}',
        origin: '${origin}',
        systemFeatures: ${JSON.stringify(systemFeatures)},
        userId: ${JSON.stringify(userId)},
        permissions: ${JSON.stringify(permissions)},
        fabPath: '${process.env.REACT_APP_FABPRO}',
        zoho_account_id: ${JSON.stringify(systemSettings.zoho_account_id)},
        zohoContactId: ${JSON.stringify(zohoContactId)},
        churn_app_key: '${process.env.REACT_APP_CHURN_APP_KEY}',
        forgeModelId: ${spool.forge_model_id},
        userBehaviorTrackingToken: '${
          process.env.REACT_APP_USER_BEHAVIOR_TRACKING_TOKEN
        }',
      });
    `,
      },
      process.env.REACT_APP_FABPRO
    );
  }, []);

  // FOR FAB
  const onSnapshotCreated = ({ image, items }) => {
    setCloudSheetSpoolImage(image);
    const newCloudSheetItems = items
      .map((i) => ({
        id: i.id,
        drawing_id: i.drawing_id,
        tag_number: !!i.tag_number ? parseInt(i.tag_number) : "",
        description: i.description,
        weight: !!i.weight ? i.weight.toFixed(2) : "",
        material_name: i.material_name,
        length: DISPLAY_LENGTH(i.length || 0, 16, true),
        end_prep_1: i.end_prep_1,
        end_prep_2: i.end_prep_2,
        size: i.size,
        quantity: 1,
      }))
      .reduce((acc, curr) => {
        let existingObject = acc.find((o) => {
          if (o.tag_number !== curr.tag_number) return false;
          if (o.description !== curr.description) return false;
          if (o.weight !== curr.weight) return false;
          if (o.material_name !== curr.material_name) return false;
          if (o.length !== curr.length) return false;
          if (o.end_prep_1 !== curr.end_prep_1) return false;
          if (o.end_prep_2 !== curr.end_prep_2) return false;

          return true;
        });

        if (existingObject) existingObject.quantity++;
        else acc.push(curr);

        return acc;
      }, [])
      .sort((a, b) => {
        if (a.tag_number && b.tag_number) {
          return parseInt(a.tag_number) - parseInt(b.tag_number);
        } else if (a.tag_number) {
          return -1;
        } else return 1;
      });

    setCloudSheetItems(newCloudSheetItems);
    setSheetCallback((d) => (d) => sheetCreationCallback(d));
    toggleCloudSheetCreation(true);
  };

  // update forge with new drawing info so view mode btns work
  const sheetCreationCallback = useCallback((drawing) => {
    setCurrentRow(drawing);

    const forgeViewerIframe = document.getElementById("forge-viewer-iframe");

    // refresh model on selection change
    if (drawing && forgeViewerIframe) {
      forgeViewerIframe.contentWindow.postMessage(
        {
          function: `
        defaults.drawing = ${JSON.stringify(drawing)};
      `,
        },
        process.env.REACT_APP_FABPRO
      );
    }
  }, []);

  const messageCallback = useCallback((e) => {
    if (/^onSnapshotCreated:\s/.test(e.data)) {
      const data = JSON.parse(e.data.split("onSnapshotCreated: ")[1]);
      onSnapshotCreated(data);
    } else if (e.data?.includes("viewMode")) {
      const data = JSON.parse(e.data);
      setViewMode(data.viewMode);
      setCurrentRow(data.selectedDrawing ?? {});
    }
  }, []);

  const close2DViewer = () => {
    const forgeViewerIframe = document.getElementById("forge-viewer-iframe");

    forgeViewerIframe.contentWindow.postMessage(
      {
        function: `document.getElementById('3d-view-selection')?.click();`,
      },
      process.env.REACT_APP_FABPRO
    );
  };

  useEffect(() => {
    window.addEventListener("message", messageCallback, false);

    const forgeViewerIframe = document.getElementById(
      "work-forge-viewer-iframe"
    );

    return () => {
      window.removeEventListener("message", messageCallback, false);
      forgeViewerIframe?.contentWindow?.postMessage({
        function: `if (viewer) {
          viewer.finish();
          viewer = null;
        }`,
      });
    };
  }, []);

  return (
    <div
      className={
        !!currentRow.has_original && viewMode === 0
          ? "forge-dual"
          : !!currentRow.has_original && viewMode === 1
          ? "forge-2d"
          : ""
      }
    >
      <iframe
        src={`${process.env.REACT_APP_FABPRO}/js/Forge/core.html`}
        title="Forge Viewer"
        name="Forge Viewer"
        className={`forge-viewer-iframe ${
          navExpanded ? "narrow" : useModalLayout ? "modal-layout" : ""
        }`}
        id="forge-viewer-iframe"
        onLoad={onIframeLoaded}
      ></iframe>
      {viewMode !== 2 && !!currentRow.has_original ? (
        <div className="forge-pspdfkit-wrapper">
          <PSPDFKitViewer
            selectedItem={{
              ...currentRow,
              type: "DRAWING",
            }}
            itemId={currentRow.id}
            itemType={"DRAWING"}
            fileType={"ANNOTATED"}
            file={drawingFiles?.[currentRow.id].original}
            showAnnotations={!!currentRow.has_annotated}
            area={area}
            storeInstanceOutside={storeInstanceOutside}
            token={token}
            userInfo={userInfo}
            refresh={refresh}
            hasPermission={permissions?.includes(111)}
            isArchived={currentRow.archived ? true : false}
          />
          {viewMode === 1 && (
            <button className="close-forge-2d" onClick={() => close2DViewer()}>
              Close 2D Viewer
            </button>
          )}
        </div>
      ) : null}
    </div>
  );
};

export default ForgeViewer;

ForgeViewer.propTypes = {
  // GENERAL
  appType: PropTypes.oneOf(["fab", "field", "myWork"]),
  token: PropTypes.string, // REQUIRED
  userId: PropTypes.number, // REQUIRED
  systemSettings: PropTypes.shape({
    // REQUIRED
    zoho_account_id: PropTypes.string,
  }),
  permissions: PropTypes.array, // REQUIRED
  origin: PropTypes.oneOf(["web", "app", "field"]),

  spool: PropTypes.shape({
    type: PropTypes.string,
    id: PropTypes.number,
    drawing_id: PropTypes.number,
    name: PropTypes.string,
    drawing_name: PropTypes.string,
    forge_urn: PropTypes.string,
    model_name: PropTypes.string,
    job_id: PropTypes.number,
    package_id: PropTypes.number,
    forge_model_id: PropTypes.number,
  }),
  toggleCloudSheetCreation: PropTypes.func,
  setCloudSheetSpoolImage: PropTypes.func,
  setCloudSheetItems: PropTypes.func,
  useModalLayout: PropTypes.bool,
  navExpanded: PropTypes.bool,
};
