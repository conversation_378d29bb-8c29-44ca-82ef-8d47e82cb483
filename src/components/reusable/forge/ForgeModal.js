// NPM PACKAGE IMPORTS
import React, { useCallback, useMemo, useState } from "react";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import { AiFillCloseSquare } from "react-icons/ai";
import { useSelector } from "react-redux";

// COMPONENT IMPORTS
import ForgeViewer from "./ForgeViewer";
import CloudSheetCreation from "../cloudSheetCreation/CloudSheetCreation";

// STYLES IMPORTS
import "./stylesForgeViewer.scss";

const ForgeModal = ({
  forgeViewer,
  toggleForgeViewer,
  selectedItem,
  type,
  sheetCreation = false,
  gridOptionsApi = null,
  setCurrentRowOrder = (f) => f,
  refresh = (f) => f,
  area,
}) => {
  const [showCloudSheetCreation, toggleCloudSheetCreation] = useState(false);
  const [cloudSheetSpoolImage, setCloudSheetSpoolImage] = useState(null);
  const [cloudSheetItems, setCloudSheetItems] = useState(null);
  const [sheetCallback, setSheetCallback] = useState(null);
  const [showExitPrompt, toggleExitPrompt] = useState(false);

  const { token, userId, systemSettings, permissions, userInfo } = useSelector(
    (state) => state.profileData
  );
  const { navExpanded } = useSelector((state) => state.generalData);

  const handleToggleForgeViewer = useCallback(() => {
    if (showCloudSheetCreation) return;

    toggleForgeViewer();
  }, [toggleForgeViewer, showCloudSheetCreation]);

  const sheetCreationProps = useMemo(() => {
    return sheetCreation
      ? {
          toggleCloudSheetCreation,
          setCloudSheetSpoolImage,
          setCloudSheetItems,
          setSheetCallback,
        }
      : {};
  }, [sheetCreation]);

  return (
    <Modal
      open={forgeViewer}
      handleClose={() => (showExitPrompt ? {} : toggleExitPrompt(true))}
    >
      {showExitPrompt && (
        <Modal
          open={showExitPrompt}
          handleClose={() => toggleExitPrompt(false)}
        >
          <div className="forge-viewer-modal-exit-prompt">
            <p>Are you sure you want to close the viewer?</p>
            <Button onClick={() => toggleExitPrompt(false)} className="cancel">
              No
            </Button>
            <Button onClick={handleToggleForgeViewer} className="submit">
              Yes
            </Button>
          </div>
        </Modal>
      )}
      <div className="forge-viewer-modal">
        <ForgeViewer
          appType="fab"
          token={token}
          userId={userId}
          systemSettings={systemSettings}
          permissions={permissions}
          origin="web"
          spool={{ ...selectedItem, type }}
          useModalLayout={false}
          navExpanded={navExpanded}
          area={area}
          userInfo={userInfo}
          refresh={refresh}
          {...sheetCreationProps}
        />
        <Button
          onClick={handleToggleForgeViewer}
          className={`close-forge-modal ${navExpanded ? "far" : ""}`}
        >
          <AiFillCloseSquare />
        </Button>
      </div>
      {showCloudSheetCreation && (
        <CloudSheetCreation
          open={showCloudSheetCreation}
          handleClose={() => {
            setCloudSheetSpoolImage(null);
            toggleCloudSheetCreation(false);
          }}
          cloudSheetSpoolImage={cloudSheetSpoolImage}
          cloudSheetItems={cloudSheetItems}
          drawingsGridOptionsApi={gridOptionsApi}
          setCurrentRowOrder={setCurrentRowOrder}
          sheetCallback={sheetCallback}
        />
      )}
    </Modal>
  );
};

export default ForgeModal;
