import React from "react";
import { DataGrid } from "@mui/x-data-grid";
const MUIGrid = ({ data, columns, options, autosize = false }) => {
  let gridOptions = options;

  // does not work right with 2 grids...
  // const autosizeOptions = {
  //   includeHeaders: true,
  //   includeOutliers: true,
  //   outliersFactor: 1.5,
  //   expand: true,
  //   columns: columns.map((c) => {
  //     return c.field;
  //   }),
  // };

  // if (autosize)
  //   gridOptions = {
  //     ...gridOptions,
  //     autosizeOnMount: true,
  //     autosizeOptions: autosizeOptions,
  //   };

  return <DataGrid rows={data} columns={columns} {...gridOptions} />;
};

export default MUIGrid;
