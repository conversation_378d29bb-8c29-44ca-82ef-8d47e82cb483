export const showErrorModal = (message) => (dispatch) => {
  dispatch({
    type: "SHOW_ERROR_MODAL",
    payload: message,
  });
};

export const showConfirmArchiveModal = (name, accountId, onArchive) => (
  dispatch
) => {
  dispatch({
    type: "SHOW_CONFIRM_ARCHIVE_MODAL",
    payload: {
      message:
        "Caution: Archiving this account will prevent updates to the associated 3D models. However, all existing connected jobs will continue to have access to these 3D models.",
      accountName: name,
      accountId: accountId,
      onClick: onArchive,
    },
  });
};

export const showArchivedAccountsModal = () => (dispatch) => {
  dispatch({
    type: "SHOW_ARCHIVED_ACCOUNTS_MODAL",
    payload: {},
  });
};

export const showBulkArchiveModal = (selectedItems, level, onArchive) => (
  dispatch
) => {
  dispatch({
    type: "SHOW_BULK_ARCHIVE_MODAL",
    payload: {
      selectedItems: selectedItems,
      level: level,
      onClick: onArchive,
    },
  });
};

export const clearModal = () => (dispatch) => dispatch({ type: "CLEAR_MODAL" });
