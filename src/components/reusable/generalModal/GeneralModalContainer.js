// NPM PACKAGE IMPORTS
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { ModalLibrary } from "./ModalLibrary";

// REDUX IMPORTS
import { clearModal } from "./generalModalActions";

// EXPORTS
const GeneralModalContainer = () => {
  const dispatch = useDispatch();
  const {
    showModal,
    modalTitle,
    modalMessage,
    modalType,
    accountName,
    accountId,
    onClick,
    selectedItems,
    level,
  } = useSelector((state) => state.modalData);
  const onClose = () => dispatch(clearModal());
  const ModalComponent = ModalLibrary[modalType];

  return (
    <div id="general-modal-container">
      {showModal ? (
        <ModalComponent
          message={modalMessage}
          title={modalTitle}
          accountName={accountName}
          accountId={accountId}
          onClose={onClose}
          onClick={onClick}
          selectedItems={selectedItems}
          level={level}
        />
      ) : null}
    </div>
  );
};

export default GeneralModalContainer;
