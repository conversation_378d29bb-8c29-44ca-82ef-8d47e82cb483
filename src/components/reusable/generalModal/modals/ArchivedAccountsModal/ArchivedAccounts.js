// NPM PACKAGE IMPORTS
import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import {
  handleFetchAutodeskAccounts,
  handleUpdateAutodeskAccounts,
} from "../../../../autodeskAccountSettings/autodeskSettingsActions";

// COMPONENT IMPORTS
import Modal from "../../../modal/Modal";
import "./ArchivedAccounts.scss";
import Button from "msuite_storybook/dist/button/Button";

export const ArchivedAccountsModal = ({ onClose }) => {
  const dispatch = useDispatch();
  const { archivedAccounts } = useSelector(
    (state) => state.autodeskAccountSettings
  );
  const [selectedAccounts, setSelectedAccounts] = useState({});
  const onSelect = (id) => {
    const selectedAccountsCopy = structuredClone(selectedAccounts);
    if (selectedAccountsCopy[id]) delete selectedAccountsCopy[id];
    else selectedAccountsCopy[id] = true;
    setSelectedAccounts(selectedAccountsCopy);
  };
  const onUnarchive = () => {
    dispatch(
      handleUpdateAutodeskAccounts(Object.keys(selectedAccounts).toString(), 0)
    ).then((res) => {
      if (!res.error) {
        dispatch(handleFetchAutodeskAccounts());
        onClose();
      }
    });
  };
  return (
    <Modal onClose={onClose} title={"Archived Accounts"} suppressForm dark>
      <div className="general-modal-content-wrapper-archived">
        {archivedAccounts?.length ? (
          <>
            <section>
              <div className="row">
                <div className="checkbox-col" />
                <p className="col title">Autodesk Account Name</p>
                <p className="col title">GUID</p>
              </div>
              <div
                className={
                  archivedAccounts?.length > 5 ? "account-list-scroll" : ""
                }
              >
                {archivedAccounts?.map((account) => {
                  return (
                    <div
                      className="row"
                      key={account.id}
                      onClick={() => onSelect(account.id)}
                    >
                      <div className="checkbox-col">
                        <input
                          checked={selectedAccounts[account.id]}
                          type="checkbox"
                        />
                      </div>
                      <p className="col">{account.name}</p>
                      <p className="col">{account.account_guid}</p>
                    </div>
                  );
                })}
              </div>
            </section>
            <footer>
              <Button className="cancel" onClick={onClose} type="button">
                Cancel
              </Button>
              <Button
                className="unarchive-button"
                onClick={onUnarchive}
                type="button"
                disabled={!Object.keys(selectedAccounts).length}
              >
                Unarchive Selected
              </Button>
            </footer>
          </>
        ) : (
          <div className="empty-modal">
            <h1 title>There are no Archived Accounts at this time.</h1>
            <Button className="close" onClick={onClose} type="button">
              Close
            </Button>
          </div>
        )}
      </div>
    </Modal>
  );
};
