@import "../../../../styles/colors.scss";

.general-modal-content-wrapper-archived {
  display: flex;
  width: 700px;
  flex-direction: column;

  & .account-list-scroll {
    max-height: 180px;
    overflow-y: scroll;
  }

  & .account-list-scroll::-webkit-scrollbar {
    width: 10px;
  }

  & .account-list-scroll::-webkit-scrollbar-track {
    background-color: white;
  }

  & .account-list-scroll::-webkit-scrollbar-thumb {
    background-color: $fabProBlue;
  }

  & p.title {
    color: white;
    font-weight: 500;
    margin: 15px 0 10px;
  }

  & input {
    width: 30px;
    height: 20px;
  }

  & .row {
    display: flex;
    align-items: center;
  }

  & .checkbox-col {
    display: flex;
    flex: 1;
    margin: 2.5px;
    align-items: center;
  }

  & .col {
    display: inline-block;
    align-items: center;
    margin: 5px 2px;
    flex: 7;
  }

  & p {
    color: $fabProBlue;
    font-weight: 500;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  & footer {
    display: flex;
    justify-content: space-around;
    margin: 15px 0;

    & .unarchive-button {
      background-color: $fabProBlue;
      color: white;
      width: 30%;
      font-size: 18px;
    }
    & .cancel {
      font-size: 18px;
      width: 30%;
    }
  }
  & .empty-modal {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    & h1 {
      color: white;
    }
    & .close {
      width: 30%;
      margin-bottom: 20px;
      cursor: pointer;
    }
  }
}
