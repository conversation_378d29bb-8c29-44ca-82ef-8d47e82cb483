@import "../../../../styles/colors.scss";

.general-modal-content-wrapper-archive {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;

  & > svg {
    font-size: 6rem;
    color: $orange;
    border-radius: 50%;
  }

  & > p {
    color: $textLight;
    text-align: center;
    font-size: 0.8rem;
    margin: 10px;
    width: 400px;
  }

  & > h4 {
    color: $textLight;
    margin: 0;
  }

  & > .confirmIcon {
    color: $orange;
    margin-bottom: 10px;
  }

  & span.emphasized {
    color: $orange;
  }

  & > .accountName {
    color: $fabProBlue;
  }
}

footer.archive {
  display: flex;
  justify-content: space-around;
  margin-bottom: 15px;

  & button {
    cursor: pointer;
    width: 150px;
  }

  & .archiveButton {
    background-color: $orange;
  }
}
