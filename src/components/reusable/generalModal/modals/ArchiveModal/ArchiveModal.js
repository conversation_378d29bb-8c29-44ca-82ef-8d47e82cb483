import React from "react";
import Modal from "../../../modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import { BsExclamationCircle } from "react-icons/bs";
import "./ArchiveModal.scss";

export const ArchiveModal = ({
  message,
  title,
  accountName,
  accountId,
  onClose,
  onClick,
}) => {
  const confirmClick = () => {
    onClick(accountId, 1);
    onClose();
  };
  return (
    <Modal onClose={onClose} title={title} suppressForm dark>
      <div className="general-modal-content-wrapper-archive">
        <BsExclamationCircle className="confirmIcon" />
        <h4>
          Are you sure you want to
          <span className="emphasized"> ARCHIVE</span>
        </h4>
        <h4 className="accountName">{accountName}</h4>
        <p>{message}</p>
      </div>
      <footer className="archive">
        <Button className="cancel" onClick={onClose} type="button">
          Cancel
        </Button>
        <Button className="archiveButton" type="button" onClick={confirmClick}>
          Archive
        </Button>
      </footer>
    </Modal>
  );
};
