// NPM PACKAGE IMPORTS
import React, { useEffect, useState } from "react";

// COMPONENT IMPORTS
import Modal from "../../../modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import { BsExclamationCircle } from "react-icons/bs";

// REDUX IMPORTS
import { useDispatch, useSelector } from "react-redux";
import { handleFetchAllTimers } from "../../../../timers/timersActions";

//STYLES IMPORTS
import "./BulkArchiveModal.scss";

export const BulkArchiveModal = ({
  onClose,
  selectedItems,
  level,
  onClick,
}) => {
  const [itemsWithActiveTimers, setItemsWithActiveTimers] = useState([]);
  const [archiveItems, setArchiveItems] = useState([]);
  const dispatch = useDispatch();
  const { allTimers } = useSelector((state) => state.timerData);

  const levelMap = {
    items: "work_item_id",
    drawings: "drawing_id",
    packages: "package_id",
    jobs: "job_id",
  };

  const handleClick = () => {
    onClick(archiveItems);
    onClose();
  };

  useEffect(() => {
    dispatch(handleFetchAllTimers("running"));
  }, []);

  useEffect(() => {
    const itemsWithTimers = [];
    const itemsToArchive = [];
    if (allTimers?.length && selectedItems) {
      const timerItemIds = allTimers.map((timer) => timer[levelMap[level]]);
      for (let item of selectedItems) {
        if (timerItemIds.indexOf(item.id) > -1) itemsWithTimers.push(item);
        else itemsToArchive.push(item.id);
      }
      setItemsWithActiveTimers(itemsWithTimers);
      setArchiveItems(itemsToArchive);
    } else if (selectedItems) {
      setArchiveItems(selectedItems.map((item) => item.id));
    }
  }, [allTimers]);

  return (
    <Modal onClose={onClose} suppressForm>
      <div className="bulk-archive-modal-content-wrapper">
        <BsExclamationCircle className="confirmIcon" />
        {!!itemsWithActiveTimers.length && (
          <div className="active-timers">
            <p className="active-timer-warning">
              The following {level} have active timers and will not be archived:
            </p>
            {itemsWithActiveTimers.map((item) => {
              return <p className="item">PKG-{item.id}</p>;
            })}
          </div>
        )}
        {!!archiveItems.length && (
          <p className="confirm-message">
            Are you sure you want to <span className="action">archive</span>{" "}
            {archiveItems.length} package(s)?
          </p>
        )}
        <Button className="cancel" onClick={onClose}>
          Cancel
        </Button>
        <Button
          className={`submit`}
          onClick={handleClick}
          disabled={!archiveItems.length}
        >
          Archive
        </Button>
      </div>
    </Modal>
  );
};
