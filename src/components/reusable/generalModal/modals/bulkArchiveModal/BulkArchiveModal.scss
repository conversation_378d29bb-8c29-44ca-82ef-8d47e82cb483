@import "../../../../styles/colors.scss";

.bulk-archive-modal-content-wrapper {
  background-color: #fff;
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 15px;
  row-gap: 15px;
  justify-items: center;
  padding: 15px;
  width: 400px;

  & > svg {
    font-size: 6rem;
    color: $orange;
    grid-column: 1/-1;
  }
  & .active-timers {
    display: grid;
    grid-column: 1/-1;
    justify-items: center;
    & p.item {
      color: $fabProBlue;
      margin: 0;
    }
  }
  & p.confirm-message {
    grid-column: 1/-1;
  }
  & > p span.action {
    font-weight: 600;
    color: $orange;
  }

  & > button {
    width: 150px;
    height: 40px;
    padding: 0;
    font-size: 1rem;

    &.cancel {
      background-color: #fff;
      border: 1px solid #333;
      color: #333;

      &:hover {
        background-color: darken(#fff, 10%);
      }
    }

    &.submit {
      border: 1px solid #333;
      color: white;
      background-color: $orange;
      &:hover {
        background-color: darken($orange, 10%);
      }
    }
  }
}
