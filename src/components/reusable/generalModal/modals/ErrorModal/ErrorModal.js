import React from "react";
import { BsExclamationCircle } from "react-icons/bs";
import Modal from "../../../modal/Modal";
import "./ErrorModal.scss";

export const ErrorModal = ({ message, onClose }) => {
  return (
    <Modal onClose={onClose} suppressForm>
      <div class="general-modal-content-wrapper">
        <BsExclamationCircle />
        <p className="sub-modalTitle">Error</p>
        <p>{message}</p>
      </div>
      <footer>
        <span onClick={onClose}>Close</span>
      </footer>
    </Modal>
  );
};
