const initialState = {
  showModal: false,
  // modalTitle: not implemented in intital state, but a valid value to set a title for a modal
  modalMessage: "",
  modalType: "info",
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "SHOW_ERROR_MODAL":
      return {
        ...state,
        showModal: true,
        modalMessage: payload,
        modalType: "error",
      };
    case "SHOW_CONFIRM_ARCHIVE_MODAL": {
      return {
        ...state,
        showModal: true,
        modalMessage: payload.message,
        modalType: "archive",
        modalTitle: "Warning - Archiving Autodesk Account",
        accountName: payload.accountName,
        accountId: payload.accountId,
        onClick: payload.onClick,
      };
    }
    case "SHOW_ARCHIVED_ACCOUNTS_MODAL":
      return {
        ...state,
        showModal: true,
        modalType: "archivedAccounts",
      };
    case "SHOW_BULK_ARCHIVE_MODAL":
      return {
        ...state,
        showModal: true,
        modalType: "bulkArchiveModal",
        onClick: payload.onClick,
        selectedItems: payload.selectedItems,
        level: payload.level,
      };
    case "CLEAR_MODAL":
      return { ...state, ...initialState };
    default:
      return state;
  }
}
