@import "../../styles/colors.scss";

div.forge-model-selector-modal {
  width: 500px;
  display: grid;
  grid-template-rows: 30px 1fr;
  row-gap: 10px;
  background-color: #fff;
}

div.forge-model-selector-modal > h2 {
  height: 30px;
  font-size: 1rem;
  background-color: $fabProBlue;
  padding: 0 5px;
  margin: 0;
  color: #fff;

  display: flex;
  justify-content: space-between;
  align-items: center;

  & > button.close-forge-selector-modal {
    padding: 0;
    height: 25px;
    width: 25px;
    background-color: transparent;
    box-shadow: none;
    color: #fff;

    display: flex;
    align-items: center;
    justify-content: center;
  }
}

div.forge-model-selector-modal > div.forge-model-selector-models {
  padding: 0 10px 10px 20px;
  max-height: 450px;
  overflow-y: scroll;

  display: grid;
  row-gap: 10px;

  &::-webkit-scrollbar {
    width: 10px;
    background-color: #f5f5f5;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #555;
  }

  & > button {
    padding: 0;
    min-height: 30px;
    height: unset;
    line-height: unset;
    width: 100%;
    margin: auto;
    background-color: $fabProBlue;
    color: #fff;
  }

  & > button:hover {
    background-color: darken($fabProBlue, 10%);
  }
}
