// NPM PACKAGE IMPORTS
import React, { useMemo } from "react";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import { FaTimes } from "react-icons/fa";

// STYLE IMPORTS
import "./stylesForgeModelSelector.scss";

const ForgeModelSelector = ({
  forgeModelSelector,
  toggleForgeModelSelector,
  toggleForgeViewer,
  rowInfo,
  setRowInfo,
  currentTable,
  forgeModelsByJob,
}) => {
  const modelsToDisplay = useMemo(() => {
    if (currentTable === "JOBS") return forgeModelsByJob;
    if (currentTable === "PACKAGES")
      return rowInfo.forge_models ? JSON.parse(rowInfo.forge_models) : [];
    return [];
  }, [currentTable, forgeModelsByJob, rowInfo]);

  return (
    <Modal
      open={forgeModelSelector}
      handleClose={() => toggleForgeModelSelector(false)}
    >
      <div className="forge-model-selector-modal">
        <h2 className="title">
          <span>
            Forge Models -{" "}
            {currentTable === "JOBS"
              ? rowInfo.job_number
                ? `(${rowInfo.job_number})`
                : ""
              : `(${rowInfo.id || rowInfo.package_id})`}
            {currentTable === "JOBS" ? rowInfo.job_name : rowInfo.package_name}
          </span>
          <Button
            onClick={() => toggleForgeModelSelector(false)}
            className="close-forge-selector-modal"
          >
            <FaTimes />
          </Button>
        </h2>
        <div className="forge-model-selector-models">
          {modelsToDisplay.map((m) => {
            return (
              <Button
                key={currentTable === "JOBS" ? m.id : m.forge_model_id}
                onClick={() => {
                  setRowInfo({
                    ...rowInfo,
                    forge_urn: currentTable === "JOBS" ? m.urn : m.forge_urn,
                    model_name: m.model_name,
                    forge_model_id:
                      currentTable === "JOBS" ? m.id : m.forge_model_id,
                  });
                  toggleForgeViewer(true);
                  toggleForgeModelSelector(false);
                }}
              >
                {m.model_name}
              </Button>
            );
          })}
        </div>
      </div>
    </Modal>
  );
};

export default ForgeModelSelector;
