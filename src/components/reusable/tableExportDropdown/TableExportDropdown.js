// NPM PACKAGE IMPORTS
import React, { useState, useRef } from "react";
import { FiChevronDown } from "react-icons/fi";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../hooks/useOutsideClick";

// STYLE IMPORTS
import "./stylesTableExportDropdown.scss";

const TableExportDropdown = ({ handleCSV, handleExcel, handlePDF }) => {
  const [showDropdown, toggleDropdown] = useState(false);

  const dropdownRef = useRef(null);

  useOutsideClick(dropdownRef, () => toggleDropdown(false));

  const handleDropdownClick = () => {
    toggleDropdown(!showDropdown);
  };

  return (
    <div className="export-dropdown-wrapper">
      <div onClick={handleDropdownClick} className="export-button">
        <button
          type="button"
          onClick={(showDropdown) => toggleDropdown(!showDropdown)}
        >
          <span>Export</span> <FiChevronDown />
        </button>
      </div>
      {showDropdown && (
        <div ref={dropdownRef} className="export-dropdown">
          <ul>
            <li
              onClick={() => {
                toggleDropdown(false);
                handleCSV();
              }}
            >
              CSV
            </li>
            <li
              onClick={() => {
                toggleDropdown(false);
                handleExcel();
              }}
            >
              Excel
            </li>
            {/* ag-grid currently does not have the ability to export to PDF
            <li
              onClick={() => {
                toggleDropdown(false);
                handlePDF();
              }}
            >
              PDF
            </li> */}
          </ul>
        </div>
      )}
    </div>
  );
};

export default TableExportDropdown;
