@import "../../styles/colors.scss";

div.export-dropdown-wrapper {
  position: fixed;
  right: 2%;
  z-index: 1;
}

div.export-button {
  & button {
    width: 70px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid $blue;
    padding: 5px;
    background-color: transparent;
    cursor: pointer;
    color: white;
    border: none;
    border-radius: 3px;
    font-size: 0.8rem;

    &:focus {
      background-color: $lighterGrey;
      border: none;
      outline: none;
    }

    & svg {
      font-size: 1.1rem;
    }
  }
}

div.export-dropdown {
  position: absolute;
  top: 28px;
  right: 2%;
  background-color: $darkGrey;
  display: flex;
  flex-direction: column;
  border: 1px solid $blue;
  border-radius: 3px;

  & ul {
    list-style-type: none;
    width: 100px;
    height: 60px;
    margin: 0;
    padding: 0;

    & li {
      color: $lighterSlate;
      height: 30px;
      font-size: 0.8rem;
      display: flex;
      align-items: center;
      padding-left: 10px;
      border-bottom: 1px solid $lighterGrey;
      cursor: pointer;
    }
  }
}
