import Queue from "../queue/Queue";

const initialState = {
  spinners: new Queue("SPINNER"),
};

export default function reducer(state = initialState, { type, payload }) {
  let newSpinners;
  switch (type) {
    case "ENQUEUE_SPINNER":
      newSpinners = state.spinners;
      newSpinners.enqueue(payload);
      return { ...state, spinners: newSpinners };
    case "DEQUEUE_SPINNER":
      newSpinners = state.spinners;
      newSpinners.dequeue(payload);
      return { ...state, spinners: newSpinners };
    case "SHOW_SPINNER":
      newSpinners = state.spinners;
      newSpinners.updateQueue(payload, { pending: false });
      return { ...state, spinners: newSpinners };
    default:
      return state;
  }
}
