// NPM PACKAGE IMPORTS
import React from "react";
import { useSelector } from "react-redux";
import Loading from "msuite_storybook/dist/loading/Loading";

// STYLE IMPORTS
// import "./stylesGeneralSpinner.scss";

// EXPORTS
const GeneralSpinnerContainer = () => {
  const { spinners } = useSelector((state) => state.generalSpinnerData);

  return (
    <div className="alert-popup-container">
      {spinners.data.map((spinner) => {
        if (spinner.shown || spinner.pending)
          return <React.Fragment key={spinner.id}></React.Fragment>;
        return (
          <React.Fragment key={spinner.id}>
            <Loading full />
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default GeneralSpinnerContainer;
