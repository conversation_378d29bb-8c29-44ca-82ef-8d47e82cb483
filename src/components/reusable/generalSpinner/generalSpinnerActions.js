const enqueueSpinner = (id) => ({
  type: "ENQUEUE_SPINNER",
  payload: id,
});
const dequeueSpinner = (id) => ({ type: "DEQUEUE_SPINNER", payload: id });
const showSpinner = (id) => ({
  type: "SHOW_SPINNER",
  payload: id,
});

export const handleEnqueueSpinner = (id, timeout = 3000) => {
  return function (dispatch) {
    dispatch(enqueueSpinner(id));
    setTimeout(() => {
      dispatch(showSpinner(id));
    }, timeout);
  };
};

export const handleDequeueSpinner = (id) => (dispatch) =>
  dispatch(dequeueSpinner(id));
