import React from "react";

const Tooltip = ({ isVisible, text, position }) => {
  if (!isVisible) return null;

  const tooltipStyles = {
    position: "absolute",
    backgroundColor: "#333",
    color: "black",
    padding: "5px 10px",
    fontSize: "0.8rem",
    zIndex: 99,
    whiteSpace: "nowrap",
    ...getPosition(position),
    backgroundColor: "white",
    borderRadius: "10px",
  };

  return <div style={tooltipStyles}>{text}</div>;
};

const getPosition = (position) => {
  switch (position) {
    case "top":
      return {
        bottom: "100%",
        left: "50%",
        transform: "translateX(-50%)",
        marginBottom: "8px",
      };
    case "bottom":
      return {
        top: "100%",
        left: "50%",
        transform: "translateX(-50%)",
        marginTop: "8px",
      };
    case "left":
      return {
        top: "50%",
        right: "100%",
        transform: "translateY(-50%)",
        marginRight: "8px",
      };
    case "right":
      return {
        top: "50%",
        left: "100%",
        transform: "translateY(-50%)",
        marginLeft: "8px",
      };
    default:
      return {
        top: "100%",
        left: "50%",
        transform: "translateX(-50%)",
        marginTop: "8px",
      };
  }
};

export default Tooltip;
