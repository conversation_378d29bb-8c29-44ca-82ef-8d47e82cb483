import React, { useState } from "react";
import Tooltip from "../../reusable/tootltipHOC/Tooltip";

const withTooltip = (WrappedComponent) => {
  return (props) => {
    const [isTooltipVisible, setIsTooltipVisible] = useState(false);

    const showTooltip = () => setIsTooltipVisible(true);
    const hideTooltip = () => setIsTooltipVisible(false);

    return (
      <div
        style={{ position: "relative", display: "inline-block" }}
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
      >
        <WrappedComponent {...props} />
        <Tooltip
          isVisible={isTooltipVisible}
          text={props.tooltipText}
          position="top"
        />
      </div>
    );
  };
};

export default withTooltip;
