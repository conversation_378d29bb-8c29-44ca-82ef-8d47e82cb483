// NPM PACKAGE IMPORTS
import React from "react";
import { Link } from "react-router-dom";

// STYLES IMPORTS
import "./stylesBreadcrumbs.scss";

const Breadcrumbs = ({ crumbs }) => {
  let displayedCrumbs = [];
  let currentStep = (crumbs.find((crumb) => crumb.isSelected) || {}).step;

  // dont display crumbs that are higher than the current step
  for (let i = 0; i < currentStep; i++) {
    displayedCrumbs.push(crumbs[i]);
  }

  return (
    <div className="breadcrumbs-wrapper">
      {displayedCrumbs?.length > 0 &&
        displayedCrumbs.map((item, index) => (
          <div key={item.value || index}>
            <Link
              to={item.path}
              className={
                item.isSelected ? "selected" : item.isDisabled ? "disabled" : ""
              }
            >
              {`${item.display}`}
            </Link>
            {index !== displayedCrumbs.length - 1 && (
              <span className="separator"> / </span>
            )}
          </div>
        ))}
    </div>
  );
};

export default Breadcrumbs;
