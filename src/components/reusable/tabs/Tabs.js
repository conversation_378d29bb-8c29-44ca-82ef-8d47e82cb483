import React from "react";

// STYLES IMPORTS
import "./stylesTabs.scss";

const Tabs = ({ options, currentTabIndex, isDisabled, onValueChanged }) => {
  return (
    <div className={isDisabled ? "inline-tabs disabled" : "inline-tabs"}>
      {options &&
        options.length > 0 &&
        options.map((item, index) => (
          <div
            key={index}
            className={
              currentTabIndex === index
                ? isDisabled
                  ? "selected-tab disabled"
                  : "selected-tab"
                : isDisabled
                ? "inline-tab disabled"
                : "inline-tab"
            }
            onClick={() => {
              if (isDisabled) return;
              onValueChanged(item, index);
            }}
          >
            {item}
          </div>
        ))}
    </div>
  );
};

export default Tabs;
