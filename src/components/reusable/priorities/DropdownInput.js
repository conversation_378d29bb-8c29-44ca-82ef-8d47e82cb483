// NPM PACKAGE IMPORTS
import React, { useState, useEffect, useRef } from "react";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../hooks/useOutsideClick";

const DropdownInput = ({
  priorities,
  handlePriorityUpdate,
  location,
  params,
  toggleDropdown,
}) => {
  const [input, setInput] = useState(params.data.priority);
  const [activeSuggestion, setActiveSuggestion] = useState(0);
  const [filteredSuggestions, setFilteredSuggestions] = useState(priorities);
  const [showSuggestions, toggleSuggestions] = useState(true);

  const inputRef = useRef(null);
  const wrapperRef = useRef(null);
  useOutsideClick(wrapperRef, () => toggleDropdown(false));

  useEffect(() => {
    inputRef.current.focus();
  }, []);

  const selectFromDropdown = (e = null) => {
    const priority = e
      ? parseInt(e.currentTarget.dataset.priority)
      : filteredSuggestions[activeSuggestion].priority;

    handlePriorityUpdate(priority, params);
    setActiveSuggestion(0);
    setFilteredSuggestions([]);
    toggleSuggestions(false);
    setInput("");
    toggleDropdown(false);
  };

  const onChange = (e) => {
    setInput(e.target.value);
    const filtered = priorities
      .filter((d) => {
        if (d.priority.toString().includes(e.target.value.toString())) {
          return true;
        } else return false;
      })
      .sort((a, b) => a.priority - b.priority);
    setActiveSuggestion(0);
    setFilteredSuggestions(filtered);
    toggleSuggestions(true);
  };

  const onClick = (e) => {
    selectFromDropdown(e);
  };

  const onKeyDown = (e) => {
    // ENTER KEY
    if (e.keyCode === 13) {
      e.preventDefault();
      selectFromDropdown();
      // LEFT ARROW
    } else if (e.keyCode === 38) {
      if (activeSuggestion === 0) {
        return;
      }
      setActiveSuggestion(activeSuggestion - 1);
      // RIGHT ARROW
    } else if (e.keyCode === 40) {
      if (activeSuggestion - 1 === filteredSuggestions.length) {
        return;
      }
      setActiveSuggestion(activeSuggestion + 1);
    }
  };

  const NoSuggestions = () => (
    <div className="no-suggestions">
      <em>No Suggestions...</em>
    </div>
  );

  const Suggestions = () => (
    <ul className="suggestions">
      {filteredSuggestions.map((suggestion, index) => {
        let className;
        if (index === activeSuggestion) {
          className = "suggestion-active";
        }

        return (
          <li
            data-priority={suggestion.priority}
            className={className}
            key={index}
            onClick={(e) => onClick(e)}
          >
            {suggestion.priority}
          </li>
        );
      })}
    </ul>
  );

  return (
    <div style={location} ref={wrapperRef} className="suggestion-search">
      <input
        onClick={() => {
          setFilteredSuggestions(priorities);
          toggleSuggestions(!showSuggestions);
        }}
        value={input}
        onChange={onChange}
        onKeyDown={onKeyDown}
        ref={inputRef}
        type="text"
      />
      {showSuggestions && <Suggestions />}
      {}
      {showSuggestions &&
        ((priorities && !priorities.length) ||
          (input && !filteredSuggestions.length)) && <NoSuggestions />}
    </div>
  );
};

export default DropdownInput;
