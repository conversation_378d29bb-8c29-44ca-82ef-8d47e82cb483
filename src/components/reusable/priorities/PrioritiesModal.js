// NPM PACKAGE IMPORTS
import React, { useState, useEffect, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";

// REDUX IMPORTS
import { handleUpdateDrawingPriorities } from "../../drawings/drawingsActions";

// COMPONENT IMPORTS
import AgTable from "../agTable/AgTable";
import Modal from "../modal/Modal";
import DropdownInput from "./DropdownInput";

// CONSTANTS IMPORTS
import { frameworkComponents } from "../../jobs/jobsConstants";
import { prioritiesModalColumnDefs } from "./prioritiesModalColumnDefs";

// HELPER FUNCTION IMPORTS
import { permissionLock } from "../../../_utils";

// STYLES IMPORTS
import "./prioritiesModal.scss";

const PrioritiesModal = ({
  showModal,
  toggleModal,
  initialJobs,
  initialPackages,
  initialDrawings,
  showAllWork,
}) => {
  const [tableData, setTableData] = useState([]);
  const [params, setParams] = useState(null);
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [selectedJobs, setSelectedJobs] = useState([]);
  const [selectedPackages, setSelectedPackages] = useState([]);
  const [priorities, setPriorities] = useState([]);
  const [updatedPriorities, setUpdatedPriorities] = useState([]);
  const [showDropdown, toggleDropdown] = useState(false);
  const [dropdownLocation, setDropdownLocation] = useState(null);

  const { packages } = useSelector((state) => state.packagesData);
  const { drawings } = useSelector((state) => state.drawingsData);
  const { systemSettings } = useSelector((state) => state.profileData);

  const dispatch = useDispatch();

  useEffect(() => {
    return () => {
      setSelectedJobs([]);
      setSelectedPackages([]);
      setTableData([]);
      setGridOptionsApi(null);
    };
  }, [dispatch]);

  useEffect(() => {
    setSelectedJobs(initialJobs);
  }, [initialJobs]);
  useEffect(() => {
    setSelectedPackages(initialPackages);
  }, [initialPackages]);

  // MAY BE ABLE TO REMOVE
  const filteredPackages = useMemo(() => {
    const jobIds = selectedJobs.map((job) => job.id);
    return packages.filter((p) => jobIds.includes(p.job_id));
  }, [selectedJobs, packages]);

  const filteredDrawings = useMemo(() => {
    if (initialDrawings && initialDrawings.length)
      return initialDrawings.filter((d) => !!d.priority);
    const packageIds = selectedPackages.length
      ? selectedPackages.map((p) => p.id)
      : [];
    return drawings.filter((drawing) =>
      packageIds.includes(drawing.package_id)
    );
  }, [selectedPackages, drawings, initialDrawings]);

  useEffect(() => {
    if (selectedPackages && selectedPackages.length) {
      setUpdatedPriorities([]);
      setTableData(filteredDrawings);
      const validPriorities = filteredDrawings
        .filter((d) => !!d.priority)
        .map((d) => ({ id: d.id, priority: d.priority }));

      setPriorities(validPriorities);
    }
  }, [selectedJobs, filteredPackages, selectedPackages, filteredDrawings]);

  const dateFormatting = systemSettings && systemSettings.date_display;

  useEffect(() => {
    if (tableData && gridOptionsApi) {
      gridOptionsApi.setRowData(tableData);
      gridOptionsApi.redrawRows();
    }
  }, [tableData, gridOptionsApi]);

  const handleSubmit = () => {
    dispatch(
      handleUpdateDrawingPriorities(
        updatedPriorities,
        selectedPackages.map((p) => p.id),
        showAllWork
      )
    ).then((res) => {
      if (!res.error) {
        toggleModal(updatedPriorities);
      }
    });
  };
  const handleDropdownClick = (event, params) => {
    event.persist();

    setDropdownLocation({
      top: event.pageY - 100,
      left: 100,
    });

    setParams(params);
    toggleDropdown(true);
  };

  // PRIORITY UPDATING LOGIC
  const removeStaleChanges = (priorities, updatedItemIds) => {
    return priorities.filter((item) => !updatedItemIds.includes(item.id));
  };

  const populateOldPriority = (updatedItem) => {
    let initialItem = priorities.find((item) => item.id === updatedItem.id);
    return initialItem.priority;
  };

  const handlePriorityUpdate = (value, params) => {
    let currentArr = [];
    let sectionToShift;
    let shiftedSection;
    let updatedItems;

    params.api.forEachNodeAfterFilterAndSort((rowNode) =>
      currentArr.push(rowNode.data)
    );

    const indexOfUpdatedItem = currentArr.findIndex(
      (item) => item.id === params.data.id
    );
    const indexOfNewValue = currentArr.findIndex(
      (item) => item.priority === parseInt(value)
    );

    if (indexOfUpdatedItem < indexOfNewValue) {
      // moving item DOWN in priority
      sectionToShift = currentArr.slice(
        indexOfUpdatedItem + 1,
        indexOfNewValue + 1
      );
      shiftedSection = sectionToShift.map((item, index) => {
        if (index === 0) {
          return { ...item, priority: currentArr[indexOfUpdatedItem].priority };
        } else {
          return { ...item, priority: sectionToShift[index - 1].priority };
        }
      });
    } else {
      // moving item UP in priority
      sectionToShift = currentArr.slice(indexOfNewValue, indexOfUpdatedItem);

      shiftedSection = sectionToShift.map((item, index) => {
        return {
          ...item,
          priority: currentArr[indexOfNewValue + index + 1].priority,
        };
      });
    }

    let updatedItem = currentArr[indexOfUpdatedItem];
    updatedItem.priority = parseInt(value);

    updatedItems = [...shiftedSection, updatedItem];
    params.api.applyTransaction({ update: updatedItems });
    setUpdatedPriorities([
      ...removeStaleChanges(
        updatedPriorities,
        updatedItems.map((item) => item.id)
      ),
      ...updatedItems.map((item) => ({
        id: item.id,
        new_priority: item.priority,
        old_priority: populateOldPriority(item),
      })),
    ]);
  };

  const sortedPriorities = useMemo(() => {
    return priorities.sort((a, b) => a.priority - b.priority);
  }, [priorities]);

  // GRID STUFF
  useEffect(() => {
    if (gridOptionsApi)
      gridOptionsApi.setColumnDefs(
        permissionLock(
          prioritiesModalColumnDefs(dateFormatting, handleDropdownClick)
        )
      );
  }, [
    gridOptionsApi,
    dateFormatting,
    updatedPriorities,
    setUpdatedPriorities,
    priorities,
    tableData,
  ]);

  // const permissionLockedColumns = permissionLock(
  //   prioritiesModalColumnDefs(dateFormatting)
  // );

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
    params.api.redrawRows();
  };
  const onSortChanged = (params) => {
    params.api.redrawRows();
  };
  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };
  const getRowHeight = (params) => 60;

  // const getRowStyle = params => {
  //   if (updatedPriorities.map(item => item.id).includes(params.data.id)) {
  //     return {'background-color': '#356a9a'}
  //   }
  // };

  const gridOptions = {
    rowData: tableData || [],
    // columnDefs: permissionLockedColumns,
    frameworkComponents: frameworkComponents(),
    rowClassRules,
    getRowHeight,
    onSortChanged,
    onGridReady,
    suppressRowClickSelection: true,
    // use the item's actual ID as the ag-grid row ID to get around object reference ===
    getRowNodeId: (data) => data.id,
    // used for styling the rows that have been updated
    // getRowStyle,
    rowBuffer: 5,
    sideBar: false,
  };

  return (
    <>
      {showModal && (
        <Modal
          title="Update Priorities"
          showModal={showModal}
          onClose={() => toggleModal([])}
          onSubmit={handleSubmit}
        >
          <div className="priorities-wrapper">
            {selectedPackages &&
            selectedPackages.length > 0 &&
            sortedPriorities &&
            sortedPriorities.length > 0 ? (
              <div>
                <AgTable gridOptions={gridOptions} />
              </div>
            ) : !selectedPackages.length ? (
              <p className="no-packages-message">Please select a package</p>
            ) : (
              <p className="no-data-message">No Items</p>
            )}
            <div className="button-row">
              <button
                className={!updatedPriorities.length ? "disabled" : "submit"}
                disabled={!updatedPriorities.length}
                type="submit"
              >
                Submit
              </button>
            </div>
            {showDropdown && dropdownLocation && params && sortedPriorities && (
              <DropdownInput
                priorities={sortedPriorities}
                params={params}
                handlePriorityUpdate={handlePriorityUpdate}
                location={dropdownLocation}
                toggleDropdown={toggleDropdown}
              />
            )}
          </div>
        </Modal>
      )}
    </>
  );
};

export default PrioritiesModal;
