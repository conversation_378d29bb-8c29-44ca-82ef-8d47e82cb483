import { prioritiesModalColumnDefs } from "./prioritiesModalColumnDefs";

describe("Priorities", () => {
  const defaultHeaders = [
    "Priority",
    "Status",
    "Drawing Name",
    "Package Name",
    "Due Date",
  ];

  const handleDropdownClick = jest.fn();
  const dateFormatting = "MM-DD-YYYY";

  describe("Column Defs", () => {
    let populatedColumns;
    const testStore = {
      getState: () => ({ profileData: testStore.profileData }),
      profileData: {
        systemSettings: {
          date_display: "MM-DD-YYYY",
          timezone: "America/Chicago",
        },
      },
    };

    beforeEach(() => {
      populatedColumns = prioritiesModalColumnDefs(
        dateFormatting,
        handleDropdownClick,
        testStore
      );
    });

    it("Headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);
      expect(columnHeaders).toEqual(defaultHeaders);
    });

    describe("PRIORITY", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Priority");
      });
      it("valueFormatter", () => {
        expect(column.valueFormatter()).toEqual({ handleDropdownClick });
      });
    });

    describe("DUE DATE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Due Date");
      });

      it("valueFormatter", () => {
        const params = {
          value: 1621018878,
        };
        expect(column.valueFormatter(params)).toEqual("05-14-2021");
      });
    });
  });
});
