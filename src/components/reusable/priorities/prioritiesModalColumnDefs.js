// HELPER FUNCTION IMPORTS
import { generateTime } from "../../../_utils";

export const prioritiesModalColumnDefs = (
  dateFormatting,
  handleDropdownClick,
  testStore = null
) => {
  return [
    {
      headerName: "Priority",
      field: "priority",
      width: 80,
      sort: "asc",
      valueFormatter: () => ({
        handleDropdownClick,
      }),
      cellRenderer: "priorityDropdownCellRenderer",
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
    },
    {
      headerName: "Status",
      field: "stage_name",
      width: 80,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
    },
    {
      headerName: "Drawing Name",
      field: "name",
      width: 100,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
    },
    {
      headerName: "Package Name",
      field: "package_name",
      width: 100,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
    },
    {
      headerName: "Due Date",
      field: "due_date",
      width: 100,
      valueFormatter: (params) => {
        return params.value
          ? typeof params.value === "number"
            ? generateTime(params.value * 1000, false, true, "-", testStore)
            : params.value
          : "-";
      },
      filter: "agDateColumnFilter",
      filterParams: {
        buttons: ["reset"],
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = cellValue
            ? typeof cellValue === "number"
              ? new Date(
                  generateTime(cellValue * 1000, false, true, "-", testStore)
                )
              : new Date(cellValue)
            : "-";

          return cellDate < filterLocalDateAtMidnight
            ? -1
            : cellDate > filterLocalDateAtMidnight
            ? 1
            : 0;
        },
      },
      menuTabs: ["filterMenuTab"],
    },
  ];
};
