@import "../../styles/colors.scss";

div.priorities-wrapper {
  width: 70vw;
  min-width: 600px;
  height: 70vh;
  max-height: 800px;

  & .filter-section {
    margin-top: 8px;
    min-height: 100px;

    & .selected-item {
      height: 16px !important;

      & svg {
        cursor: default;
        color: $lighterSlate !important;
      }
    }

    & .inline-filter-button p {
      font-size: 0.9rem;
    }
  }

  & .button-row {
    float: right;

    & button.submit {
      height: 28px;
      width: 100px;
      background-color: $blue;
      border: none;
      border-radius: 3px;
      color: white;
      cursor: pointer;
      margin: 20px 0;

      &:hover {
        background-color: darken($blue, 10%);
      }
    }

    & button:disabled {
      height: 28px;
      width: 100px;
      background-color: $blue;
      border: none;
      border-radius: 3px;
      margin: 10px 0;
    }

    & .disabled {
      opacity: 0.7;
      cursor: default;
      color: white !important;
    }
  }

  & .no-data-message {
    color: $blue;
    text-align: center;
    margin: 0 0 30px;
  }

  & p.no-packages-message {
    color: $red;
    font-size: 0.9rem;
    font-weight: bold;
    text-align: center;
  }

  & .table-wrapper {
    margin-bottom: 30px;

    & .custom-ag-styles.ag-theme-balham-dark {
      & div.ag-cell {
        min-height: 60px !important;
      }
    }
    & div.ag-paging-panel {
      border-top: none !important;
    }
  }
  & .custom-ag-styles.ag-theme-balham-dark {
    height: 70vh;
    background-color: transparent;

    & .ag-center-cols-viewport {
      overflow-x: hidden;
    }
  }
}

.results-wrapper {
  max-height: 400px;
}

div.suggestion-search {
  position: fixed;
  max-height: 200px;
  width: 120px;
  z-index: 50;

  & input[type="text"] {
    height: 32px;
    width: 100%;
    box-sizing: border-box;
  }

  & ul.suggestions {
    background-color: white;
    border-top: #ccc;
    margin: 0;
    padding: 0;
    max-height: 100px;
    overflow-y: auto;
    cursor: pointer;

    & li {
      list-style-type: none;
      padding: 5px 5px;
      font-size: 0.9rem;

      &:hover {
        background-color: #c2ddf2;
      }
    }

    & .suggestion-active {
      background-color: $blue;
      color: white;
    }
  }

  div.no-suggestions {
    background-color: white;

    & em {
      padding: 5px;
    }
  }
}
.ag-layout-normal {
  overflow-y: auto !important;
}
