// NPM PACKAGE IMPORTS
import React, { useState } from "react";
import Modal from "msuite_storybook/dist/modal/Modal";

// COMPONENT IMPORTS
import Assignments from "../assignments/Assignments";
import ManageCrews from "../manageCrews/ManageCrews";

// STYLES IMPORTS
import "./stylesAssignmentsModal.scss";

const AssignmentsModal = ({
  open,
  handleClose,
  selectedItem,
  currentTable,
}) => {
  const [showManageCrewsModal, toggleManageCrewsModal] = useState(false);

  return (
    <Modal open={open} handleClose={handleClose}>
      <div className="assignments-modal">
        <Assignments
          selectedItem={selectedItem}
          currentTable={currentTable}
          openManageCrews={() => toggleManageCrewsModal(true)}
        />
      </div>
      {showManageCrewsModal && (
        <ManageCrews
          open={showManageCrewsModal}
          handleClose={() => toggleManageCrewsModal(false)}
        />
      )}
    </Modal>
  );
};

export default AssignmentsModal;
