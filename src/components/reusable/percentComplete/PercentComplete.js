// NPM PACKAGE IMPORTS
import React from "react";
import { Doughnut } from "react-chartjs-2";

// STYLES IMPORTS
import "./stylesPercentComplete.scss";

const PercentComplete = (params) => {
  const { value } = params;
  const data = {
    datasets: [
      {
        data: [value, 100 - value],
        backgroundColor: ["#2196F3", "#E8E8E8"],
        borderWidth: 0,
      },
    ],
  };

  const options = {
    cutoutPercentage: 80,
    tooltips: {
      enabled: false,
    },
  };

  const formattedValue = !!value ? Number(value).toFixed(0) : "";
  return (
    <div className="non-table-percent-complete-wrapper">
      <span className="non-table-percent-label">Percent Complete</span>
      <div className="non-table-doughnut-wrapper">
        <span
          className={`non-table-doughnut-text ${
            isNaN(value) ? "" : "non-table-value"
          }`}
        >
          {formattedValue}
          {isNaN(parseInt(value)) ? "0%" : "%"}
        </span>
        {/* {value !== null && ( */}
        <Doughnut data={data} options={options} width={200} />
        {/* )} */}
      </div>
    </div>
  );
};

export default PercentComplete;
