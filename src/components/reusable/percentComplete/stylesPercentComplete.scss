@import "../../styles/colors.scss";

div.non-table-percent-complete-wrapper {
  width: 120px;
  height: 120px;
}
div.non-table-doughnut-wrapper {
  width: 100px;
  height: 100px;
  margin-top: -10px;

  display: grid;
  grid-template-rows: 1fr;
  grid-template-columns: 1fr;
  align-items: center;

  & span.non-table-doughnut-text,
  canvas {
    grid-row: 1/-1;
    grid-column: 1/-1;
  }

  & span.non-table-doughnut-text {
    justify-self: center;

    &.non-table-value {
      color: black;
      font-size: 0.6rem;
    }
  }

  & canvas {
    margin-top: -10px;
  }

  & p {
    font-size: 0.8rem;
    margin-top: 0;
  }
}

// span.non-table-percent-label {
//   margin-left: -10px;
// }
