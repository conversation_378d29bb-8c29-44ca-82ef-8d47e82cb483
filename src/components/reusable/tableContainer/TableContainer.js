// NPM PACKAGE IMPORTS
import React, { useState, useEffect } from "react";

// COMPONENT IMPORTS
import TableExportDropdown from "../tableExportDropdown/TableExportDropdown";

// HELPER FUNCTION IMPORTS
import { permissionLock } from "../../../_utils";
import { useDebounce } from "../../../hooks/useDebounce";
import { trackMixPanelEvent } from "../../../utils/_mixPanelUtils";

// STYLE IMPORTS
import "./stylesTableContainer.scss";

// EXPORTS
const TableContainer = ({
  tabs,
  handleToggle,
  handleExcel,
  handlePDF,
  handleCSV,
  searchInput,
  setSearchInput,
  currentTable,
  selectedRows,
  totalRows,
  showExport = true,
  children,
  isInfiniteGrid = false,
}) => {
  const permissionLockedTabs = permissionLock(tabs);
  const [searchTerm, setSearchTerm] = useState(searchInput);
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  useEffect(() => {
    const updateSearchInput = async () => {
      trackMixPanelEvent(
        `${currentTable} filter search input`,
        0,
        "Filters",
        Date.now(),
        `${currentTable} search input: ${debouncedSearchTerm}`
      );
      setSearchInput(debouncedSearchTerm);
    };

    updateSearchInput();
  }, [debouncedSearchTerm]);

  const onExportEvent = (exportType) => {
    trackMixPanelEvent(
      `${currentTable} ${exportType} export`,
      0,
      "TableExport",
      Date.now(),
      `${currentTable} ${exportType} export clicked`
    );

    switch (exportType) {
      case "CSV":
        if (handleCSV) {
          handleCSV();
        }
        break;
      case "Excel":
        if (handleExcel) {
          handleExcel();
        }
        break;
      case "PDF":
        if (handlePDF) {
          handlePDF();
        }
        break;
      default:
        break;
    }
  };

  const clearSearch = () => {
    setSearchTerm("");
    setSearchInput("");
  };
  return (
    <div
      className={
        currentTable === "Cost Codes"
          ? "table-container cost-codes-tabs"
          : "table-container"
      }
    >
      <div className="table-container-content">
        <div className="table-container-tabs">
          <span>
            {permissionLockedTabs?.map((t, idx) => {
              return (
                <div
                  key={t.name}
                  onClick={() => {
                    if (!t.selected) {
                      if (t.onClick) t.onClick();
                      handleToggle(t.name);
                    }
                  }}
                  className={`table-container-tab ${
                    t.selected ? "selected" : ""
                  }`}
                >
                  {t.name}
                </div>
              );
            })}
            <>
              <div className="table-search-wrapper">
                <input
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  type="text"
                  placeholder={`Search ${
                    currentTable ? currentTable.toLowerCase() : ""
                  }`}
                />
                {searchTerm.length > 0 && (
                  <button onClick={clearSearch}>Clear</button>
                )}
              </div>
              <span className="selected-rows-count">
                {selectedRows.length > 1
                  ? selectedRows.length +
                    " rows selected of " +
                    totalRows +
                    " total."
                  : ""}
              </span>
            </>
          </span>
          {showExport && (
            <TableExportDropdown
              handleCSV={() => onExportEvent("CSV")}
              handleExcel={() => onExportEvent("Excel")}
              handlePDF={() => onExportEvent("PDF")}
            />
          )}
        </div>
        {children}
        {/* {Array.isArray(children) ? children[shownChild] : children} */}
      </div>
    </div>
  );
};

export default TableContainer;
