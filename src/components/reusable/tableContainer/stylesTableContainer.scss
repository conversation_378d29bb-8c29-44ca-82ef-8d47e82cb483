@import "../../styles/colors.scss";

div.table-container {
  border-radius: 3px;
  height: calc(100vh - 218px);
  padding: 20px 20px 0px 20px;

  @supports (-webkit-touch-callout: none) {
    /* CSS specific to iOS devices */
    // 100 for header and footer, 60 for address bar
    height: calc(100vh - 270px);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  display: grid;
  grid-template-rows: [content-s] 1fr [content-e];
  grid-row-gap: 15px;

  &-content {
    height: 100%;

    display: grid;
    grid-template-rows: [tabs-s] 28px [tabs-e table-s] 1fr [table-e];

    & div.ag-theme-balham-dark {
      background-color: transparent !important;
    }

    & div.ag-theme-balham-dark.custom-ag-styles {
      height: inherit;
    }

    & div.ag-cell {
      white-space: normal;
      // height: 60px !important;
      background-color: transparent;
      line-height: unset;

      & div.ag-cell-edit-wrapper {
        height: 30px;
      }
    }

    & .ag-row div {
      animation: fadeIn 0.25s ease-in-out forwards;
    }
  }

  &-tabs {
    grid-row: tabs-s/tabs-e;
    display: flex;
    height: 32px;
    justify-content: space-between;
    border-bottom: 1px solid $lighterGrey;

    & div.table-search-wrapper {
      display: inline-block;
      margin-left: 20px;

      & input[type="text"] {
        height: 24px;
        background-color: white;
        padding-left: 10px;
        border-radius: 3px;
        border: none;
        color: black;
        font-size: 0.8rem;
        box-sizing: border-box;
        width: 140px;

        &::placeholder {
          font-size: 0.7rem;
        }

        &:focus {
          outline: none;
          border: none;
        }
      }

      & button {
        background-color: transparent;
        margin-left: 10px;
        cursor: pointer;
        border-radius: 3px;
        border: 1px solid $blue;
        height: 24px;
        width: 50px;
        color: white;
        font-size: 0.7rem;
        padding: 2px 6px;
        box-sizing: border-box;
      }
    }

    & div.export-wrapper {
      color: $fabProBlue;
      width: 120px;
      display: flex;
      justify-content: space-between;
      margin-right: 3px;
      cursor: pointer;
      & svg {
        font-size: 1.2rem;
        & path {
          stroke: white;
        }

        &:hover {
          & path {
            stroke: darken(white, 20%);
          }
        }
      }
    }

    & span {
      & div.table-container-tab {
        display: inline-block;
        width: 100px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        color: #888;
        font-size: 0.7rem;
        transition: all 0.3s ease;

        &.selected {
          background-color: $blue;
          color: white;
          cursor: default;
          border-top-left-radius: 5px;
          border-top-right-radius: 5px;
        }

        &:not(.selected):hover {
          color: #fff;
          cursor: pointer;
        }
      }

      & > span.selected-rows-count {
        padding-left: 10px;
        font-size: 0.75rem;
        color: $lighterSlate;
      }
    }
  }
}
