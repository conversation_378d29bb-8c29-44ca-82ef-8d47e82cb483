import React from "react";
import Alert from "@mui/material/Alert";
import { Grid2 as Grid } from "@mui/material";

/**
 * HOC to show an error message if an error occurs
 * @param {React.Component} WrappedComponent - The component to wrap
 */
const withError = (WrappedComponent) => {
  return function WithErrorComponent({ error, ...props }) {
    if (error) {
      const errorMessage =
        typeof error === "string"
          ? error
          : error?.message || error?.error?.message || "Something went wrong!";

      return (
        <Grid
          container
          direction="row"
          spacing={2}
          display="flex"
          justifyContent="center"
          alignItems="center"
          height="100%"
        >
          <Alert severity="error">{errorMessage}</Alert>
        </Grid>
      );
    }
    return <WrappedComponent {...props} />;
  };
};

export default withError;
