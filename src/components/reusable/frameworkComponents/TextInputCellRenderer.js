// NPM PACKAGE IMPORTS
import React, { useState, useEffect } from "react";

// EXPORTS
const TextInputCellRenderer = (params) => {
  const {
    editing,
    columnName,
    value,
    handleCellEdit,
    wizardPermission,
  } = params.formatValue();

  const [input, setInput] = useState(value || params.value || "");

  useEffect(() => {
    if (!params.data) {
      // Define the function to refresh the cell
      const refreshCell = () => {
        params.api.refreshCells({
          rowNodes: [params.node],
          columns: [params.column.colId],
          force: true,
        });
      };
      const timer = setTimeout(refreshCell, 2500);
      return () => clearTimeout(timer);
    }
  }, [params]);

  if (!params.data) {
    return null;
  }

  if (!editing && columnName === "job_name" && wizardPermission) {
    return (
      <div
        style={{ textDecoration: "underline", cursor: "pointer" }}
        onClick={() =>
          window.location.replace(
            `${process.env.REACT_APP_FABPRO}/new-job/?JobID=${params.data.id}`
          )
        }
      >
        {params.value}
      </div>
    );
  }

  if (!editing || columnName === "drawing_name") {
    return <span>{value || params.value || ""}</span>;
  }

  return (
    <input
      value={input}
      className="text-input-cell"
      type="text"
      onBlur={() => handleCellEdit(params, columnName, input)}
      onChange={(e) => setInput(e.target.value)}
    />
  );
};

export default TextInputCellRenderer;
