// NPM PACKAGE IMPORTS
import React, { useState, useEffect } from "react";
import { titleize } from "../../../_utils";

const ChildCellRenderer = (params) => {
  const [isDisabled, toggleDisabled] = useState(false);

  const { handleChildSelection, type } = params;

  useEffect(() => {
    if (!params.value) toggleDisabled(true);
  }, []);

  useEffect(() => {
    if (!params.data) {
      // Define the function to refresh the cell
      const refreshCell = () => {
        params.api.refreshCells({
          rowNodes: [params.node],
          columns: [params.column.colId],
          force: true,
        });
      };
      const timer = setTimeout(refreshCell, 2500);
      return () => clearTimeout(timer);
    }
  }, [params]);

  if (!params.data) {
    return null;
  }

  return (
    <button
      disabled={isDisabled}
      onClick={() => handleChildSelection(type, params.data)}
      className="child-button"
    >
      {params.value || 0} {titleize(type)}
    </button>
  );
};

export default ChildCellRenderer;
