// NPM PACKAGE IMPORTS
import React, { useState, useEffect } from "react";
import moment from "moment";
import "moment-timezone";

// HELPER FUNCTION IMPORTS
import { convertStringToUnix, validateDateStringFormat } from "../../../_utils";

// EXPORTS
const TextDateCellRenderer = (params) => {
  const [input, setInput] = useState("");
  const [isValidDate, toggleValidDate] = useState(true);

  const { editing, value, columnName, handleCellEdit } = params.formatValue();

  useEffect(() => {
    if (value === "N/A") return;
    setInput(value);
  }, [value]);

  useEffect(() => {
    if (!params.data) {
      // Define the function to refresh the cell
      const refreshCell = () => {
        params.api.refreshCells({
          rowNodes: [params.node],
          columns: [params.column.colId],
          force: true,
        });
      };
      const timer = setTimeout(refreshCell, 2500);
      return () => clearTimeout(timer);
    }
  }, [params]);

  useEffect(() => {
    if (
      !editing ||
      !input.length ||
      input === moment(params.data[columnName]).format("mm-dd-yyyy")
    )
      return;
    if (validateDateStringFormat(input)) {
      toggleValidDate(true);
    } else toggleValidDate(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [input, editing]);

  if (!editing) {
    return <span>{value || params.value || ""}</span>;
  }

  const onDateChange = () => {
    if (!isValidDate) return;
    const unix = convertStringToUnix(input);
    const newDate = moment.unix(unix).format("MM-DD-YYYY");
    handleCellEdit(params, columnName, newDate, unix);
  };

  if (!params.data) {
    return null;
  }

  return (
    <input
      value={input}
      placeholder={!input ? "MM-DD-YYYY" : ""}
      className={
        isValidDate ? "text-input-cell" : "invalid-date text-input-cell"
      }
      type="text"
      onBlur={onDateChange}
      onChange={(e) => setInput(e.target.value)}
    />
  );
};

export default TextDateCellRenderer;
