// NPM PACKAGE IMPORTS
import React, { useEffect } from "react";

const PackageNameCellRenderer = (params) => {
  const { wizardPermission } = params.formatValue();

  useEffect(() => {
    if (!params.data) {
      // Define the function to refresh the cell
      const refreshCell = () => {
        params.api.refreshCells({
          rowNodes: [params.node],
          columns: [params.column.colId],
          force: true,
        });
      };
      const timer = setTimeout(refreshCell, 2500);
      return () => clearTimeout(timer);
    }
  }, [params]);

  if (!params.data) {
    return null;
  }

  return (
    <div
      style={
        wizardPermission
          ? { textDecoration: "underline", cursor: "pointer" }
          : {}
      }
      onClick={() =>
        wizardPermission
          ? window.location.assign(
              `${process.env.REACT_APP_FABPRO}/new-packages/?JobID=${
                params.data.job_id
              }&PackageID=${params.data.package_id || params.data.id}`
            )
          : {}
      }
    >
      {params.value}
    </div>
  );
};

export default PackageNameCellRenderer;
