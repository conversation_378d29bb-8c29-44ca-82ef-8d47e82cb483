// NPM PACKAGE IMPORTS
import React from "react";
import Button from "msuite_storybook/dist/button/Button";

// STYLE IMPORTS
import "./stylesFrameworkComponents.scss";

const AssignUnassignCellRenderer = (params) => {
  const { assigned = false, assignUnassign = (f) => f } = params.valueFormatted;

  return (
    <Button
      onClick={assignUnassign}
      className={`assign-unassign-button ${assigned ? "assigned" : ""}`}
    >
      {assigned ? "Assigned" : "Unassigned"}
    </Button>
  );
};

export default AssignUnassignCellRenderer;
