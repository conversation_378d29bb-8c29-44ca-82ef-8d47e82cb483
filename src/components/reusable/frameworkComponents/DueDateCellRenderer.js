// NPM PACKAGE IMPORTS
import React from "react";
import DatePicker from "react-datepicker";
import { useSelector } from "react-redux";

// HELPER FUNCTION IMPORTS
import { generateTime } from "../../../_utils";

// STYLE IMPORTS
import "./stylesFrameworkComponents.scss";

// EXPORTS
const DueDateCellRenderer = (params) => {
  const { navExpanded } = useSelector((state) => state.generalData);

  const {
    value,
    editing = false,
    setDueDate = (f) => f,
    placeholder,
  } = params.valueFormatted;

  if (!editing)
    return (
      <div>
        {value
          ? typeof value === "string"
            ? value
            : generateTime(value, false, true)
          : "N/A"}
      </div>
    );

  return (
    <div
      className={`due-date-cell-renderer ${navExpanded ? "nav-expanded" : ""}`}
    >
      <DatePicker
        className="date-picker"
        selected={value ? new Date(value) : ""}
        onChange={setDueDate}
        placeholderText={placeholder ? placeholder : ""}
      />
    </div>
  );
};

export default DueDateCellRenderer;
