// NPM PACKAGE IMPORTS
import React, { useEffect } from "react";
import { FaPen } from "react-icons/fa";

import "../frameworkComponents/stylesFrameworkComponents.scss";

// EXPORTS
const PriorityDropdownCellRenderer = (params) => {
  const { handleDropdownClick } = params.formatValue();

  useEffect(() => {
    if (!params.data) {
      // Define the function to refresh the cell
      const refreshCell = () => {
        params.api.refreshCells({
          rowNodes: [params.node],
          columns: [params.column.colId],
          force: true,
        });
      };
      const timer = setTimeout(refreshCell, 2500);
      return () => clearTimeout(timer);
    }
  }, [params]);

  if (!params.data) {
    return null;
  }

  return (
    <button
      type="button"
      className="priority-dropdown"
      onClick={(e) => handleDropdownClick(e, params)}
    >
      <FaPen />
      {params.data.priority}
    </button>
  );
};

export default PriorityDropdownCellRenderer;
