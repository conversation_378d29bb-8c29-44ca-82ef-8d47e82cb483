// NPM PACKAGE IMPORTS
import React, { useState } from "react";
import Button from "msuite_storybook/dist/button/Button";
// STYLE IMPORTS
import "./stylesFrameworkComponents.scss";
const ToggleButtonCellRenderer = (params) => {
  const [isTruthy, toggleTruthy] = useState(params.value);

  const onClick = (e) => {
    let newValue = !isTruthy;
    toggleTruthy(newValue);
    params.handleToggle(params.data[params.property], newValue);
  };
  return (
    <Button
      className={`allow-toggle-button ${!isTruthy ? "unallowed" : ""}`}
      style={{ width: `${params.width}px` || "150px" }}
      onClick={onClick}
    >
      {isTruthy ? params.labels[0] : params.labels[1]}
    </Button>
  );
};
export default ToggleButtonCellRenderer;
