// NPM PACKAGE IMPORTS
import React, { useEffect } from "react";

const JobStatusCellRenderer = (params) => {
  const isArchived = params.data?.archived ? true : false;

  useEffect(() => {
    if (!params.data) {
      // Define the function to refresh the cell
      const refreshCell = () => {
        params.api.refreshCells({
          rowNodes: [params.node],
          columns: [params.column.colId],
          force: true,
        });
      };
      const timer = setTimeout(refreshCell, 2500);
      return () => clearTimeout(timer);
    }
  }, [params]);

  if (!params.data) {
    return null;
  }

  return (
    <span
      className={`job-status-cell-renderer ${
        isArchived ? "job-status-archived" : ""
      }`}
    >
      {isArchived ? "Archived" : "Active"}
    </span>
  );
};

export default JobStatusCellRenderer;
