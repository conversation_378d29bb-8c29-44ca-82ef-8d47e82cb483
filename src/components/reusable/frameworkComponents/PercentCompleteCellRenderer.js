// NPM PACKAGE IMPORTS
import React, { useC<PERSON>back, useMemo, useEffect } from "react";
// import { Doughnut } from "react-chartjs-2";

// STYLE IMPORTS
import { blue, lighterGrey } from "../../styles/colors";

// EXPORTS
const PercentCompleteCellRenderer = ({
  params,
  setStatusTrackerPopup = null,
}) => {
  useEffect(() => {
    if (!params.data) {
      // Define the function to refresh the cell
      const refreshCell = () => {
        params.api.refreshCells({
          rowNodes: [params.node],
          columns: [params.column.colId],
          force: true,
        });
      };
      const timer = setTimeout(refreshCell, 2500);
      return () => clearTimeout(timer);
    }
  }, [params]);

  const value = params.value;
  const formattedValue = params.formatValue();

  const styles = useMemo(() => {
    return value && !isNaN(formattedValue)
      ? {
          background: `linear-gradient(100deg, ${blue} 0%, ${blue} ${
            value - 1
          }%, ${lighterGrey} ${value + 1}%, ${lighterGrey} 100%)`,
          border: "1px solid #333",
        }
      : {};
  }, [value]);

  const onMouseEnter = useCallback(
    (e) => {
      e.persist();
      if (setStatusTrackerPopup) {
        setStatusTrackerPopup({
          type: params.data?.name
            ? "DRAWING"
            : params.data?.package_name
            ? "PACKAGE"
            : "UNKNOWN",
          id: params.data?.id,
          position: {
            x: e.clientX - 315 / (7 / 3),
            y: e.clientY + 40,
          },
        });
      }
    },
    [params]
  );

  const onMouseMove = useCallback(
    (e) => {
      e.persist();
      if (setStatusTrackerPopup) {
        setStatusTrackerPopup({
          type: params.data?.name
            ? "DRAWING"
            : params.data?.package_name
            ? "PACKAGE"
            : "UNKNOWN",
          id: params.data?.id,
          position: {
            x: e.clientX - 315 / (7 / 3),
            y: e.clientY + 40,
          },
        });
      }
    },
    [params]
  );

  const onMouseLeave = useCallback(
    () => setStatusTrackerPopup && setStatusTrackerPopup(null),
    [setStatusTrackerPopup]
  );

  useEffect(() => {
    if (!params.data) {
      // Define the function to refresh the cell
      const refreshCell = () => {
        params.api.refreshCells({
          rowNodes: [params.node],
          columns: [params.column.colId],
          force: true,
        });
      };
      const timer = setTimeout(refreshCell, 2500);
      return () => clearTimeout(timer);
    }
  }, [params]);

  if (!params.data) {
    return null;
  }

  return (
    <div
      className="bar-wrapper"
      onMouseLeave={onMouseLeave}
      onMouseEnter={onMouseEnter}
      onMouseMove={onMouseMove}
      style={styles}
    >
      {formattedValue}
      {isNaN(formattedValue) ? "" : "%"}
    </div>
  );
};

export default PercentCompleteCellRenderer;
