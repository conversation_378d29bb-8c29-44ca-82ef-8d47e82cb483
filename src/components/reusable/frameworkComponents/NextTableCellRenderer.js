// NPM PACKAGE IMPORTS
import React from "react";
import { Link } from "react-router-dom";
import { useDispatch } from "react-redux";

// REDUX IMPORTS
import { handleReceiveBreadcrumbs } from "../../layout/body/breadcrumbs/breadcrumbsActions";

// EXPORTS
const NextTableCellRenderer = (params) => {
  const dispatch = useDispatch();

  const value = params.formatValue();
  const title = value.split(" ")[1].toLowerCase();

  const to = () => {
    switch (title) {
      case "packages":
        const activeJobID = params.data.id;
        return `jobs/${activeJobID}/packages`;
      default:
        return "/";
    }
  };
  const breadcrumbs = () => {
    switch (title) {
      case "packages":
        const activeJobID = params.data.id;
        return [
          {
            title: "Jobs",
            destination: "/jobs",
            cleanup: true,
          },
          {
            title: params.data.job_name,
            destination: `/jobs/${activeJobID}/packages`,
          },
        ];
      default:
        return {};
    }
  };
  return (
    <Link
      to={to()}
      className="table-cell-link"
      onClick={() => dispatch(handleReceiveBreadcrumbs(breadcrumbs()))}
    >
      {value}
    </Link>
  );
};

export default NextTableCellRenderer;
