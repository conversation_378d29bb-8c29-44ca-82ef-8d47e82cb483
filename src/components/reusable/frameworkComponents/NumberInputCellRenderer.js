// NPM PACKAGE IMPORTS
import React, { useState, useEffect } from "react";

// EXPORTS
const NumberInputCellRenderer = (params) => {
  const {
    editing,
    columnName,
    value,
    handleCellEdit,
    // fractionalColumns
  } = params.formatValue();

  const [input, setInput] = useState(value || params.value || "");

  useEffect(() => {
    if (!params.data) {
      // Define the function to refresh the cell
      const refreshCell = () => {
        params.api.refreshCells({
          rowNodes: [params.node],
          columns: [params.column.colId],
          force: true,
        });
      };
      const timer = setTimeout(refreshCell, 2500);
      return () => clearTimeout(timer);
    }
  }, [params]);

  if (!params.data) {
    return null;
  }

  if (!editing) {
    return <span>{value || params.value || ""}</span>;
  }

  return (
    <input
      type="number"
      value={input}
      className="text-input-cell"
      onBlur={() => handleCellEdit(params, columnName, input)}
      onChange={(e) => setInput(e.target.value)}
    />
  );
};

export default NumberInputCellRenderer;
