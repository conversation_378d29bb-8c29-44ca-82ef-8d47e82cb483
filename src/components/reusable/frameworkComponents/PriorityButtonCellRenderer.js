// NPM PACKAGE IMPORTS
import React, { forwardRef, useState, useImperativeHandle } from "react";

import DropdownInput from "../priorities/DropdownInput";

export default forwardRef((props, ref) => {
  const [value, setValue] = useState("");
  const [inputValue, setInputValue] = useState("");

  const onChangeHandler = (e, value) => setValue(value);
  const onInputChangeHandler = (e, inputValue) => setInputValue(inputValue);

  useImperativeHandle(ref, () => {
    return {
      getValue: () => value,
      afterGuiAttached: () => setValue(props.value),
    };
  });

  return (
    <div style={{ display: "inline-block" }}>
      <DropdownInput
        onChange={onChangeHandler}
        value={value}
        inputValue={inputValue}
        onInputChange={onInputChangeHandler}
        priorities={props.options}
        updatedPriorities={props.updatedPriorities}
        setUpdatedPriorities={props.setUpdatedPriorities}
      />
    </div>
  );
});
