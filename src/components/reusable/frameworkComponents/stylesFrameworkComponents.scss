@import "../../styles/colors.scss";

a.table-cell-link {
  color: #fff;
}
span.est-hours-span {
  border-radius: 30px;
  background-color: $neutral;
  display: block;
  width: 40px;
  text-align: center;

  &.positive {
    background-color: #176b2e;
    color: white;
    // color: #52f57f;
  }
  &.negative {
    background-color: #670808;
    color: white;
    // color: #f17575;
  }
}
div.bar-wrapper {
  min-width: 80px;
  height: 30px;

  display: grid;
  align-items: center;
  justify-content: center;
}

div.ag-row.--custom-grid-even {
  & div.more-info-cell-dropdown {
    border-left: 2px solid $grey;
  }
}

div.ag-row.--custom-grid-odd {
  & div.more-info-cell-dropdown {
    border-left: 2px solid $darkGrey;
  }
}

div.more-info-cell-container {
  display: grid;
  align-items: center;
  // grid-template-columns: 40px 1fr;
  width: 100%;

  &:hover {
    cursor: pointer;
  }

  & div.more-info-cell-icon-wrapper {
    display: grid;
    justify-content: center;
    align-items: center;
    // height: 60px;
    width: 50px;
    color: $blue;
    transform: translateX(-10px);

    & svg {
      font-size: 1.4rem;

      &:hover {
        color: darken($blue, 10%);
      }
    }
  }

  & div.more-info-cell-dropdown {
    // height: 60px;
    border: 1px solid $blue;
    border-radius: 3px;
    background-color: black;

    & div.more-info-cell-option {
      padding: 0 5px;
      height: 30px;

      &:hover {
        color: darken($blue, 10%);
      }
    }
  }
}
button.cell-category-button {
  background-color: $green;
  color: white;
  height: 30px;
  width: 95%;
  border-radius: 3px;
  font-size: 0.7rem;
  border: none;
  cursor: pointer;

  &:hover {
    background-color: darken($green, 10%);
  }
}
select.dropdown-cell-input {
  width: 100%;
  height: 30px;
  border-radius: 3px;
  border: 1px solid $fabProBlue;
  cursor: pointer;
  background-color: $lighterGrey;
  color: white;
  max-height: 300px;
  font-size: 0.7rem;

  & optgroup {
    max-height: 200px;
  }
}
input.text-input-cell {
  background-color: white;
  color: black;
  height: 30px;
  width: 95%;
  border-radius: 3px;
  border: #555;
  font-size: 0.8rem;
  padding-left: 5px;

  &:hover {
    border: 1px solid $blue;
  }

  &:focus {
    border: 2px solid $blue;
    outline: none;
  }
}
input.invalid-date {
  background-color: $red;
}
button.assign-unassign-button {
  height: 30px;
  padding: 0 5px;
  background-color: $red;
  color: #fff;
  font-size: 0.9rem;
  width: 120px;

  &.assigned {
    background-color: $green;
  }
}

div.due-date-cell-renderer {
  & div.react-datepicker-popper {
    position: fixed !important;
    z-index: 50 !important;
    // TODO
  }
  & input.date-picker {
    background-color: $lighterGrey;
    border: 1px solid $fabProBlue;
    height: 30px;
    width: 90%;
    padding-left: 10px;
    border-radius: 3px;
    color: white;
  }
}
div.due-date-editor-renderer {
  & div.react-datepicker-popper {
    position: fixed !important;
    z-index: 50 !important;
    // TODO
  }
  & :focus {
    outline: none;
  }
  background-color: $lighterGrey;
  border: 1px solid $fabProBlue;
  height: 30px;
  width: 90%;
  border-radius: 3px;
  color: white;
}
button.cell-dropdown-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  color: black;
  border: none;
  border-radius: 3px;
  height: 32px;
  width: 100%;
  cursor: pointer;
  overflow: hidden;

  &:focus {
    border: 2px solid $blue;
    outline: none;
  }
}
button.child-button,
button.drawing-name-button {
  background-color: transparent;
  border: none;
  padding: 0;
  text-decoration: underline;
  color: #f5f5f5;
  font-size: 0.8rem;
  font-weight: normal;
  cursor: pointer;

  &:focus {
    border: none;
    outline: none;
  }
}
button.child-button:disabled {
  color: darken(#f5f5f5, 50%);
}
button.drawing-name-button:disabled {
  text-decoration: none;
  cursor: default;
  color: darken(#f5f5f5, 20%);
}
button.drawing-name-button:not(:disabled).special-color {
  color: $fabProBlue;
  font-weight: 600;
}

button.priority-dropdown {
  cursor: pointer;
  background-color: transparent;
  border: none;
  color: white;
  display: flex;
  align-items: center;

  & svg {
    color: $blue;
    margin-right: 10px;
  }
}

div.on-hold-cell-indicator {
  width: 5px;
  // height: 60px;
}

div.dropdown-editor-renderer,
div.due-date-editor-renderer,
div.dropdown-input-editor-renderer,
div.fractional-editor-renderer {
  & > select,
  & > input,
  & > div,
  & > div input {
    background-color: #2d3436;
    color: #fff;
    height: 30px;
    width: 100% !important;
  }

  & > input,
  & > div input {
    border: 1px solid #fff;
    width: 100%;
    padding: 0 5px;
    box-sizing: border-box;
  }

  & > div input {
    font-size: 0.8rem;
  }
}

span.assignments-full-name {
  display: flex;
  align-items: center;
  & svg {
    margin-right: 3px;
    font-size: 1.1rem;
  }
}

.required {
  border: 1px solid $red;
}

div.action-cell-renderer {
  display: flex;
  column-gap: 5px;

  & > button.action-button {
    padding: 0 10px;
    border: 1px solid $fabProBlue;
    color: $fabProBlue;
    background-color: darken($lighterGrey, 10%);
    height: 30px;
    min-width: 80px;
    width: 100%;
    font-size: 0.8rem;

    &:hover {
      background-color: darken($darkGrey, 10%);
    }
  }

  & > button.warning {
    border: 1px solid $orange;
    color: $orange;
  }
}

div.toggle-cell-editor-container {
  display: flex;
  align-items: center;

  &.even {
    background-color: $grey;
  }

  &.odd {
    background-color: $darkGrey;
  }
}

div.username-cell-renderer {
  & p {
    margin: 5px 0;
  }
  & p.username {
    color: $fabProBlue;
    font-weight: bold;
  }
}

button.cell-link {
  background: transparent;
  border: none;
  color: $fabProBlue;
  text-decoration: underline;
  cursor: pointer;
  font-weight: bold;
}

input.username-cell-editor {
  background-color: #303538;
  border: 1px solid #ccc;
  border-radius: 0;
  color: white;
  height: 25px;
  width: 100%;
}

button.allow-toggle-button {
  height: 32px;
  background-color: $green;
  color: white;
  font-size: 0.9rem;
  padding: 8px 20px;
  width: 150px;
}
button.unallowed {
  background-color: $red;
}

div.custom-cell-renderer-container {
  display: flex;
  justify-content: center;
}

button.submit-column {
  background-color: $green;
  color: white;
  margin: 0 0 0 auto;
  height: 30px;
  width: 150px;
  font-size: 0.9rem;
  padding: 0;
}

span.job-status-cell-renderer {
  color: $green;
  font-weight: bold;
}
span.job-status-archived {
  color: $red;
}
