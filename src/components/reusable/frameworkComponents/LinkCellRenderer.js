// NPM PACKAGE IMPORTS
import React from "react";

const LinkCellRenderer = (params) => {
  const { handleClick, setSelectedRow, text } = params;

  const handleLinkClick = () => {
    setSelectedRow(params.data);
    handleClick(params);
  };

  return (
    <button className="cell-link" onClick={handleLinkClick}>{`${params.value} ${
      text || ""
    }`}</button>
  );
};

export default LinkCellRenderer;
