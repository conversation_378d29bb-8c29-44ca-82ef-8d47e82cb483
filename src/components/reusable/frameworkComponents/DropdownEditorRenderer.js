// NPM PACKAGE IMPORTS
import React from "react";

import "./stylesFrameworkComponents.scss";

const unclearableHeaders = ["Material", "Role", "Tier", "Title"];

export default class DropdownEditorRenderer extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      selected: undefined,
    };

    this.setSelected = this.setSelected.bind(this);
  }

  componentDidMount() {
    this.setState({ selected: this.props.value || undefined });
  }

  getValue() {
    return this.props.parseValue(this.state.selected);
  }

  setSelected(selected) {
    this.setState({ selected });

    // used for items table in wizard
    if (this.props.toggleValid) {
      this.props.toggleValid(true);
    }
  }

  render() {
    return (
      <div
        ref="container"
        tabIndex={1}
        className={`${
          this.props.required ? "required" : ""
        } dropdown-editor-renderer`}
      >
        <select
          value={this.state.selected}
          onChange={(e) => {
            this.setSelected(
              /^\d+$/.test(e.target.value) &&
                this.props.colDef.cellRenderer !== "drawingNameCellRenderer"
                ? parseInt(e.target.value)
                : e.target.value
            );
          }}
          style={{ width: this.props.colDef.width }}
        >
          {!unclearableHeaders.includes(this.props.colDef.headerName) &&
            !this.props.suppressClearing && <option value="">{``}</option>}
          {this.props.values &&
            this.props.values.map((v) => (
              <option key={v.id || v} value={v.id || v}>
                {v[this.props.propertyKey] || v.name || v.display || v}
              </option>
            ))}
        </select>
      </div>
    );
  }
}
