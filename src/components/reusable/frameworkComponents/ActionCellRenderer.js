// NPM PACKAGE IMPORTS
import React from "react";
import Button from "msuite_storybook/dist/button/Button";

// STYLE IMPORTS
import "./stylesFrameworkComponents.scss";

const ActionCellRenderer = (params) => {
  const { actions = [], btnClass, isDisabled } = params.valueFormatted;

  return (
    <div className="action-cell-renderer">
      {actions.map(({ action, actionName }) => {
        return (
          <Button
            disabled={isDisabled}
            onClick={action}
            className={`action-button ${btnClass ? btnClass : ""}`}
            key={actionName}
          >
            {actionName}
          </Button>
        );
      })}
    </div>
  );
};

export default ActionCellRenderer;
