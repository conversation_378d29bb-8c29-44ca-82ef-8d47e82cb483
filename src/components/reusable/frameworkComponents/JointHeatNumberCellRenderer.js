// NPM PACKAGE IMPORTS
import React from "react";
import { FaChevronDown } from "react-icons/fa";

const JointHeatNumberCellRenderer = (params) => {
  const { editing, value, toggleCellDropdown } = params.formatValue();

  if (!editing) {
    return <span>{value || params.value || ""}</span>;
  }

  return (
    <button
      className="cell-dropdown-button"
      onClick={(e) => {
        toggleCellDropdown(e, params);
      }}
    >
      <p>{value === "N/A" ? "" : params.value}</p>
      <FaChevronDown />
    </button>
  );
};

export default JointHeatNumberCellRenderer;
