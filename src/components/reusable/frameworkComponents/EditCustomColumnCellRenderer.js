// NPM PACKAGE IMPORTS
import React, { useEffect } from "react";

//STYLE IMPORTS
import "./stylesFrameworkComponents.scss";

//COMPONENT IMPORTS
import <PERSON><PERSON> from "msuite_storybook/dist/button/Button";

//STYLE IMPORTS
import "./stylesFrameworkComponents.scss";

//REDUX IMPORTS
import { useDispatch } from "react-redux";
import {
  handleSaveCustomColumn,
  handleFetchCustomColumns,
} from "../../customColumns/customColumnsActions";
import { customColDataTypes } from "../../customColumns/CustomColumns";

const EditCustomColumnCellRenderer = ({ params, sendErrorNotification }) => {
  const dispatch = useDispatch();
  const handleCreateCustomColumn = (displayName, dataType) => {
    const { table_type, toggleAddColumn } = params.data;
    dispatch(handleSaveCustomColumn(displayName, dataType, table_type)).then(
      (res) => {
        if (res.error) return;

        toggleAddColumn(false);
        dispatch(handleFetchCustomColumns);
      }
    );
  };
  const handleClick = () => {
    // need to manually stop editing in order for params.data to update to new values
    params.api.stopEditing();
    const { display_name, data_type } = params.data;

    if (!display_name || !data_type) {
      const rowIndex = params.node.rowIndex;
      sendErrorNotification();
      params.api.setFocusedCell(rowIndex, "display_name");
      params.api.startEditingCell({
        rowIndex,
        colKey: "display_name",
      });
      return;
    }

    // convert back to string as data type before making api call
    const type = data_type === "text" ? "string" : data_type;
    handleCreateCustomColumn(display_name, type);
  };

  useEffect(() => {
    if (!params.data) {
      // Define the function to refresh the cell
      const refreshCell = () => {
        params.api.refreshCells({
          rowNodes: [params.node],
          columns: [params.column.colId],
          force: true,
        });
      };
      const timer = setTimeout(refreshCell, 2500);
      return () => clearTimeout(timer);
    }
  }, [params]);

  if (!params.data) {
    return null;
  }

  return (
    <div className="custom-cell-renderer-container">
      {params.data.addRow && (
        <Button className="submit-column" onClick={handleClick}>
          Add
        </Button>
      )}
    </div>
  );
};

export default EditCustomColumnCellRenderer;
