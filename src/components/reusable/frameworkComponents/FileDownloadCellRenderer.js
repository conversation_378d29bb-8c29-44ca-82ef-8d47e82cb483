// NPM PACKAGE IMPORTS
import React from "react";
import { FaFileDownload } from "react-icons/fa";

const FileDownloadCellRenderer = (params) => {
  const { handleDownloadFile } = params.formatValue();

  return (
    <div className="more-info-cell-container">
      <div className="more-info-cell-icon-wrapper" onClick={handleDownloadFile}>
        <FaFileDownload />
      </div>
    </div>
  );
};

export default FileDownloadCellRenderer;
