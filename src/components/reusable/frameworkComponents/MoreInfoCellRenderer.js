// NPM PACKAGE IMPORTS
import React, { useEffect } from "react";
import { BsThreeDots } from "react-icons/bs";

const MoreInfoCellRenderer = (params) => {
  useEffect(() => {
    if (!params.data) {
      // Define the function to refresh the cell
      const refreshCell = () => {
        params.api.refreshCells({
          rowNodes: [params.node],
          columns: [params.column.colId],
          force: true,
        });
      };
      const timer = setTimeout(refreshCell, 5000);
      return () => clearTimeout(timer);
    }
  }, [params]);

  if (!params.data) {
    return null;
  }

  const { moreInfoClick, toggleMoreInfo } = params.formatValue();

  const rowData = { ...params.data };

  return (
    <div className="more-info-cell-container">
      <div
        className="more-info-cell-icon-wrapper"
        onClick={(e) => moreInfoClick(e, toggleMoreInfo, rowData)}
      >
        <BsThreeDots />
      </div>
    </div>
  );
};

export default MoreInfoCellRenderer;
