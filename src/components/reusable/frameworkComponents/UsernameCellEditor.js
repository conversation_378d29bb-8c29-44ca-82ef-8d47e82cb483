import React, {
  forwardRef,
  useEffect,
  useImperative<PERSON>andle,
  useRef,
  useState,
} from "react";

const KEY_BACKSPACE = "Backspace";
const KEY_DELETE = "Delete";

export default forwardRef((props, ref) => {
  const createInitialState = () => {
    let startValue;

    let [value, domain] = props.value.split("@");

    if (props.key === KEY_BACKSPACE || props.key === KEY_DELETE) {
      // if backspace or delete pressed, we clear the cell
      startValue = "";
    } else if (props.charPress) {
      // if a letter was pressed, we start with the letter
      startValue = props.charPress;
    } else {
      // otherwise we start with the current value
      startValue = value;
    }

    return {
      value: startValue,
      domain,
    };
  };

  const initialState = createInitialState();
  const [value, setValue] = useState(initialState.value);
  const refInput = useRef(null);

  // focus on the input
  useEffect(() => {
    // get ref from React component
    window.setTimeout(() => {
      const eInput = refInput.current;
      eInput.focus();
    });
  }, []);

  /* Utility Methods */
  const cancelBeforeStart = false;

  const isLeftOrRight = (event) => {
    return ["ArrowLeft", "ArrowRight"].indexOf(event.key) > -1;
  };

  const deleteOrBackspace = (event) => {
    return [KEY_DELETE, KEY_BACKSPACE].indexOf(event.key) > -1;
  };

  const onKeyDown = (event) => {
    if (isLeftOrRight(event) || deleteOrBackspace(event)) {
      event.stopPropagation();
      return;
    }
  };

  /* Component Editor Lifecycle methods */
  useImperativeHandle(ref, () => {
    return {
      // the final value to send to the grid, on completion of editing
      getValue() {
        return `${value}@${initialState.domain}`;
      },

      // Gets called once before editing starts, to give editor a chance to
      // cancel the editing before it even starts.
      isCancelBeforeStart() {
        return cancelBeforeStart;
      },

      // Gets called once when editing is finished (eg if Enter is pressed).
      // If you return true, then the result of the edit will be ignored.
      isCancelAfterEnd() {
        // will reject the number if it greater than 1,000,000
        // not very practical, but demonstrates the method.
        return false;
      },
    };
  });

  return (
    <input
      ref={refInput}
      className={"username-cell-editor"}
      value={value}
      onChange={(event) => setValue(event.target.value)}
      onKeyDown={(event) => onKeyDown(event)}
    />
  );
});
