// NPM PACKAGE IMPORTS
import React from "react";

//EXPORTS
const DropdownCellRenderer = (params) => {
  let {
    handleClick = (f) => f,
    options = [],
    editing,
    editedFields,
    columnName,
  } = params.valueFormatted;
  if (!editing) {
    return <span>{params.value || ""}</span>;
  }

  const handleChange = (e) => {
    if (columnName === "stage_name") columnName = "stage_id";
    const { value } = e.target;
    handleClick([...editedFields, { [columnName]: value }]);
  };

  if (!editing) {
    return <span>{params.value}</span>;
  }

  return (
    <select
      className="dropdown-cell-input"
      onChange={(e) => handleChange(e)}
      value={params.value}
    >
      <option defaultValue>{params.value}</option>
      {options &&
        options.length &&
        options.map((option) => (
          <option
            selected={params.value === option.name}
            key={option.id}
            value={option}
          >
            {option.name}
          </option>
        ))}
    </select>
  );
};

export default DropdownCellRenderer;
