// NPM PACKAGE IMPORTS
import React from "react";
import DatePicker from "react-datepicker";
import moment from "moment";

// STYLE IMPORTS
import "./stylesFrameworkComponents.scss";

export default class DueDateEditorRenderer extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: "",
    };

    this.setValue = this.setValue.bind(this);
  }

  componentDidMount() {
    this.setState({ value: this.props.colDef.valueFormatter(this.props) });
  }

  getValue() {
    return this.props.colDef.valueParser({
      ...this.props,
      newValue: this.state.value,
      oldValue: this.props.value,
    });
  }

  setValue(value) {
    this.setState({ value });
  }
  // this is to correctly style it inline in ag grid
  myContainer = ({ className, children }) => {
    return (
      <div
        className={className}
        style={{ position: "relative", left: this.props.column.left }}
      >
        {children}
      </div>
    );
  };

  render() {
    return (
      <div ref="container" tabIndex={1} className="due-date-editor-renderer">
        <DatePicker
          className="date-picker"
          selected={
            !this.state.value || this.state.value === "-"
              ? ""
              : new Date(this.state.value)
          }
          onChange={(e) => {
            if (!e) this.setValue(0);
            // clear out due date option
            else this.setValue(moment(new Date(e)).format("MM-DD-yyyy"));
          }}
          placeholderText={`${
            !this.state.value || this.state.value === "-"
              ? "Click to select a date"
              : ""
          }`}
          shouldCloseOnSelect={false}
          calendarContainer={this.myContainer}
        />
      </div>
    );
  }
}
