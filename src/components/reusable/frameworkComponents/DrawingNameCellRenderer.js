// NPM PACKAGE IMPORTS
import React, { useState, useEffect } from "react";

const DrawingNameCellRenderer = (params) => {
  const [isDisabled, toggleDisabled] = useState(false);

  const {
    moreInfoClick,
    togglePDFViewer,
    specialColor = false,
    setDisplayedPdf,
  } = params;
  useEffect(() => {
    if (!params?.data?.has_original) toggleDisabled(true);
  }, []);

  useEffect(() => {
    if (!params.data) {
      // Define the function to refresh the cell
      const refreshCell = () => {
        params.api.refreshCells({
          rowNodes: [params.node],
          columns: [params.column.colId],
          force: true,
        });
      };
      const timer = setTimeout(refreshCell, 2500);
      return () => clearTimeout(timer);
    }
  }, [params]);

  if (!params.data) {
    return null;
  }

  return (
    <button
      disabled={isDisabled}
      onClick={(e) => {
        if (!isDisabled) {
          moreInfoClick(e, (f) => f, params.data);
          setDisplayedPdf(params.data); // more info click usually calls this, but not always...
          togglePDFViewer(true);
        }
      }}
      className={`drawing-name-button ${specialColor ? "special-color" : ""}`}
    >
      {params.value}
    </button>
  );
};

export default DrawingNameCellRenderer;
