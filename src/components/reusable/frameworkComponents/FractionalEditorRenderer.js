// NPM PACKAGE IMPORTS
import React from "react";

// STYLE IMPORTS
import "./stylesFrameworkComponents.scss";

export default class FractionalEditorRenderer extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: "",
    };

    this.setValue = this.setValue.bind(this);
  }

  componentDidMount() {
    this.setState({ value: this.props.colDef.valueFormatter(this.props) });
  }

  getValue() {
    return this.props.colDef.valueParser({
      ...this.props,
      newValue: this.state.value,
      oldValue: this.props.value,
    });
  }

  setValue(value) {
    this.setState({ value });
  }

  render() {
    return (
      <div ref="container" tabIndex={1} className="fractional-editor-renderer">
        <input
          type="text"
          value={this.state.value}
          onChange={(e) => this.setValue(e.target.value)}
        />
      </div>
    );
  }
}
