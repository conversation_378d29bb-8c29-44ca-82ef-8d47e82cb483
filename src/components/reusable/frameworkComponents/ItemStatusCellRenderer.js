// NPM PACKAGE IMPORTS
import React, { useCallback, useEffect } from "react";

// EXPORTS
const ItemStatusCellRenderer = ({ params, setStatusTrackerPopup = null }) => {
  const value = params.value;

  const onMouseEnter = useCallback(
    (e) => {
      e.persist();
      if (setStatusTrackerPopup) {
        setStatusTrackerPopup({
          type: "ITEM",
          id: params.data?.id,
          position: {
            x: e.clientX,
            y: e.clientY + 40,
          },
        });
      }
    },
    [params]
  );

  const onMouseMove = useCallback(
    (e) => {
      e.persist();
      if (setStatusTrackerPopup) {
        setStatusTrackerPopup({
          type: "ITEM",
          id: params.data?.id,
          position: {
            x: e.clientX,
            y: e.clientY + 40,
          },
        });
      }
    },
    [params]
  );

  const onMouseLeave = useCallback(
    () => setStatusTrackerPopup && setStatusTrackerPopup(null),
    [setStatusTrackerPopup]
  );

  useEffect(() => {
    if (!params.data) {
      // Define the function to refresh the cell
      const refreshCell = () => {
        params.api.refreshCells({
          rowNodes: [params.node],
          columns: [params.column.colId],
          force: true,
        });
      };
      const timer = setTimeout(refreshCell, 2500);
      return () => clearTimeout(timer);
    }
  }, [params]);

  if (!params.data) {
    return null;
  }

  return (
    <div
      className="bar-wrapper"
      onMouseLeave={onMouseLeave}
      onMouseEnter={onMouseEnter}
      onMouseMove={onMouseMove}
    >
      <span className={`bar-text`}>{value || "Not Completed"}</span>
    </div>
  );
};

export default ItemStatusCellRenderer;
