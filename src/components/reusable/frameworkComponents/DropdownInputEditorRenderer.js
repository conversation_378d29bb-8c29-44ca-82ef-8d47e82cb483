// NPM PACKAGE IMPORTS
import React from "react";
import Input from "msuite_storybook/dist/input/Input";

import "./stylesFrameworkComponents.scss";

export default class DropdownInputEditorRenderer extends React.Component {
  constructor(props) {
    super(props);
  }

  getValue() {
    return this.props.value;
  }

  render() {
    return (
      <div
        ref="container"
        tabIndex={1}
        className={`${
          this.props.required ? "required" : ""
        } dropdown-input-editor-renderer`}
      >
        <Input
          value={this.props.value || undefined}
          onFocus={this.props.toggleModal}
          onChange={(f) => f}
        />
      </div>
    );
  }
}
