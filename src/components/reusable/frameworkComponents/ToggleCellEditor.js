// NPM PACKAGES IMPORTS
import React from "react";
import Toggle from "msuite_storybook/dist/toggle/Toggle";

export default class ToggleCellEditor extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      value: props.value,
    };
  }

  onToggle = (e) => {
    e.preventDefault();
    let newValue = !this.state.value;
    this.setState({ value: newValue }, () => {
      this.props.setValue(newValue);
    });
  };

  // need onClick handler on div because
  // Toggle doesn't pass back event on onToggleChanged
  render() {
    return (
      <div
        className={`toggle-cell-editor-container ${
          this.props.even ? "even" : "odd"
        }`}
        style={{ height: this.props.rowHeight }}
        onClick={this.onToggle}
      >
        <Toggle
          name="toggle-cell"
          text={this.props.labels ? this.props.labels : ["yes", "no"]}
          defaultChecked={this.state.value}
          onToggleChanged={() => {}}
          isDisabled={this.props.isDisabled}
        />
      </div>
    );
  }
}
