@import "../../styles/colors.scss";

div.location-filter-wrapper {
  .location-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 65px;
    margin-right: 10px;
    font-size: 0.8rem;
    color: $fabProBlue;
    cursor: pointer;
  }

  & .no-locations {
    @extend .location-button;
    width: 85px;
    color: $red;
    cursor: default;
  }
}

div.location-dropdown {
  position: absolute;
  max-height: 150px;
  width: 150px;
  background-color: #283141;
  border: 1px solid $blue;
  display: flex;
  flex-direction: column;
  overflow: auto;
  z-index: 20;

  & span {
    font-size: 0.8rem;
    padding: 8px 12px;
    height: 24px;
    display: flex;
    align-items: center;
    color: $lighterSlate;
    cursor: pointer;
  }
}

div.selected-locations {
  position: relative;
  display: flex;
  align-items: center;
  height: 22px;
  padding: 0 5px;
  width: auto;
  border: 1px solid $blue;
  border-radius: 3px;
  color: $blue;
  font-size: 0.8rem;

  & span {
    display: flex;
    align-items: center;

    & svg {
      color: $lighterSlate;
      margin: 0 0 0 5px;
      cursor: pointer;
    }
  }
}
