// NPM PACKAGE IMPORTS
import React, { useState, useRef } from "react";
import { FiPlus, FiX } from "react-icons/fi";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../hooks/useOutsideClick";

// STYLES IMPORTS
import "./locationFilter.scss";

const LocationFilter = ({ list, setSelectedLocation, selectedLocation }) => {
  const [showMenu, toggleMenu] = useState(false);

  const wrapperRef = useRef(null);
  useOutsideClick(wrapperRef, () => toggleMenu(false));

  const handleSelectedLocation = (location) => {
    setSelectedLocation(location);
    toggleMenu(false);
  };

  if (!list) {
    return (
      <div className="location-filter-wrapper">
        <span className="no-locations">No Locations</span>
      </div>
    );
  }

  return (
    <>
      <div className="location-filter-wrapper">
        {!selectedLocation && (
          <span
            onClick={() => {
              // only run onClick to open modal
              if (!showMenu) toggleMenu(true);
            }}
            className="location-button"
          >
            <p>Location</p>
            <FiPlus />
          </span>
        )}
        {showMenu && (
          <div ref={wrapperRef} className="location-dropdown">
            {list.map((loc) => (
              <span onClick={() => handleSelectedLocation(loc)} key={loc.id}>
                {loc.site}
              </span>
            ))}
          </div>
        )}
      </div>
      {selectedLocation && (
        <div className="selected-locations">
          <span key={selectedLocation.id}>
            {selectedLocation.site}
            {list.length > 1 && (
              <FiX onClick={() => setSelectedLocation(null)} />
            )}
          </span>
        </div>
      )}
    </>
  );
};

export default LocationFilter;
