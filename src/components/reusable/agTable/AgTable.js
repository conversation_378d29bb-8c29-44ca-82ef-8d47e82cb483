import React from "react";
import PropTypes from "prop-types";
// ag-grid imports
import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-balham-dark.css";
import "./stylesAgTable.scss";
import { LicenseManager } from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
LicenseManager.setLicenseKey(process.env.REACT_APP_AG_GRID_LICENSE);

const AgTable = ({ expandComponent, expandedByDefault, gridOptions }) => {
  // parse the incoming default column options
  // @ToDo: This doesn't ACTUALLY use the incoming gridOptions.defaultColDef...
  const defaultColDef = {
    enableValue: true,
    enableRowGroup: false,
    enablePivot: false,
    sortable: true,
    filter: true,
  };
  const sideBar = {
    toolPanels: [
      {
        id: "columns",
        labelDefault: "Columns",
        labelKey: "columns",
        iconKey: "columns",
        toolPanel: "agColumnsToolPanel",
        toolPanelParams: {
          suppressRowGroups: true,
          suppressValues: true,
          suppressPivots: true,
          suppressPivotMode: true,
          suppressSideButtons: true,
          suppressColumnFilter: true,
          suppressColumnSelectAll: true,
          suppressColumnExpandAll: true,
        },
      },
    ],
  };
  const rowClass = "custom-ag-row";

  const onGridReady = (params) => {
    // Expand all rows by default
    if (expandedByDefault)
      params.api.forEachNode((node) => node.setExpanded(expandedByDefault));
  };
  const frameworkComponents = {
    customExpandDetail: (params) => {
      if (expandComponent) return expandComponent(params);
    },
    ...gridOptions.frameworkComponents,
  };
  const detailCellRenderer = "customExpandDetail";
  const onDisplayedColumnsChanged = (params) => {
    params.api.sizeColumnsToFit();
  };
  const onGridSizeChanged = (params) => {
    params.api.sizeColumnsToFit();
  };
  const onToolPanelVisibleChanged = (params) => {
    params.api.sizeColumnsToFit();
  };
  const formattedGridOptions = {
    ...gridOptions,
    pagination: gridOptions && gridOptions.pagination === false ? false : true,
    // paginationAutoPageSize:
    //   gridOptions && gridOptions.pagination === false ? false : true,
    sideBar: gridOptions && gridOptions.sideBar === false ? false : sideBar,
    rowSelection: gridOptions.rowSelection || "multiple",
    rowMultiSelectWithClick: gridOptions.rowMultiSelectWithClick || "true",
    allowDragFromColumnsToolPanel: false,
    onGridReady: (params) => {
      if (gridOptions.onGridReady) gridOptions.onGridReady(params);
      onGridReady(params);
    },
    onDisplayedColumnsChanged: (params) => {
      if (gridOptions.onDisplayedColumnsChanged)
        gridOptions.onDisplayedColumnsChanged(params);
      onDisplayedColumnsChanged(params);
    },
    onGridSizeChanged: (params) => {
      if (gridOptions.onGridSizeChanged) gridOptions.onGridSizeChanged(params);
      onGridSizeChanged(params);
    },
    onToolPanelVisibleChanged: (params) => {
      if (gridOptions.onToolPanelVisibleChanged)
        gridOptions.onToolPanelVisibleChanged(params);
      onToolPanelVisibleChanged(params);
    },
  };

  return (
    // wrapper div classname for theme and size
    <div className="ag-theme-balham-dark custom-ag-styles">
      <AgGridReact
        defaultColDef={defaultColDef}
        masterDetail
        detailCellRenderer={detailCellRenderer}
        frameworkComponents={frameworkComponents}
        rowClass={rowClass}
        gridOptions={formattedGridOptions}
      />
    </div>
  );
};
AgTable.propTypes = {
  /** SubComponent for expandable columns */
  expandComponent: PropTypes.elementType,
  /** Specifies whether the expandComponents should be shown by default */
  expandedByDefault: PropTypes.bool,
  /** Holds properties for all the things (rowData, columnDefs, onSelectedChanged) */
  gridOptions: PropTypes.object,
};
export default AgTable;
