@import "../../styles/colors.scss";
@import "../../styles/sizes.scss";

div.assignments {
  & div.header-wrapper {
    display: flex;
    flex-direction: column;
    height: 80px;

    & input[type="text"].search-input {
      height: 32px;
      width: 160px;
      border-radius: 3px;
      margin-bottom: 10px;
      box-sizing: border-box;
      outline: none;
      border: 1px solid #333;
      padding-left: 5px;
      z-index: 1;

      &:focus {
        outline: none;
      }
    }
  }
  & h2.title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: $blue;
    color: white;
    margin: 0 0 10px;
    padding: 8px 5px;
    font-size: 1.1rem;

    & .close {
      cursor: pointer;
      margin-right: 5px;
    }

    & > button {
      height: 30px;
      padding: 0 10px;
      font-size: 0.8rem;
      color: #1997c6;
      background-color: $darkFab;
    }
  }

  & div.tables {
    display: flex;
    justify-content: space-between;
    max-height: 700px;
    padding: 0 10px;
  }

  & div.right {
    & h3 {
      margin: 0 0 10px;
      padding: 6px 0;
      font-size: 1rem;
      font-weight: 500;
      color: white;
    }
  }
  & div.table {
    width: 49.5%;
  }

  & div.table.left {
    & div.header {
      display: flex;
      align-items: center;

      height: 40px;
      & select {
        height: 32px;
        font-size: 1.1rem;
        margin-right: 10px;
      }
      & button {
        height: 30px;
        padding: 3px 8px;
        font-size: 0.8rem;
        background-color: $blue;
        color: white;
        margin: 0 5px;
        margin-bottom: 10px;

        &:hover {
          background-color: darken($blue, 10%);
        }
      }
    }
  }

  & div.ag-theme-balham-dark.custom-ag-styles div.ag-body-viewport {
    overflow-y: scroll !important;
  }

  & div.ag-theme-balham-dark {
    max-height: 600px;
    width: 100%;
    // 40 modal title, 40 padding, 27 heading, 52 search, 10 for modal outside, 28 bottom button
    height: calc(
      100vh - #{$headerFooter} - 40px - 40px - 27px - 52px - 10px - 28px
    );
    @supports (-webkit-touch-callout: none) {
      height: calc(
        100vh - #{$headerFooter} - #{$iosAddressBar} - 40px - 40px - 27px - 52px -
          10px - 28px
      );
    }

    & .ag-center-cols-viewport {
      overflow: hidden;
      width: 100%;
    }

    & .ag-cell {
      white-space: normal;
    }

    & .ag-body-horizontal-scroll-viewport,
    .ag-horizontal-right-spacer {
      display: none;
    }

    & .ag-popup {
      position: absolute;
    }
  }
}

div.no-assigned-users {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50%;

  & p {
    font-size: 1rem;
    color: $blue;
    font-weight: bold;
    margin-bottom: 100px;
  }
}
