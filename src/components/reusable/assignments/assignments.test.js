import { columns } from "./assignmentsConstants";

describe("Assignments", () => {
  describe("all users column defs", () => {
    const defaultHeaders = ["Full Name", "Role", "Assign/Unassign"];

    const testAssignedUsers = [
      {
        user_id: 1,
        first_name: "<PERSON>",
        last_name: "<PERSON>",
      },
      {
        user_id: 2,
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON><PERSON>",
      },
    ];

    const assignUnassign = jest.fn();

    let populatedColumns;

    beforeEach(() => {
      populatedColumns = columns("USERS", testAssignedUsers, assignUnassign);
    });

    it("Headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);
      expect(columnHeaders).toEqual(defaultHeaders);
    });

    describe("FULL NAME", () => {
      let column;

      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Full Name");
      });

      const params = {
        data: {
          user_name: "test user",
          first_name: "<PERSON>",
          last_name: "<PERSON>",
        },
      };
      const params2 = {
        data: {
          first_name: "<PERSON>",
          last_name: "<PERSON>",
        },
      };

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test user");
        expect(column.valueGetter(params2)).toEqual("Austin Jess");
      });

      it("comparator", () => {
        const rowNode = {
          data: {
            crew_id: null,
          },
        };
        const values = ["test user", "Austin Jess", "First Last", "Last First"];
        expect(
          values.sort((a, b) => column.comparator(a, b, rowNode))
        ).toEqual(["Last First", "Austin Jess", "First Last", "test user"]);
      });
    });

    describe("ASSIGN/UNASSIGN", () => {
      let column;

      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Assign/Unassign"
        );
      });

      const params = {
        value: "assigned",
        data: {
          user_name: "test user",
          user_id: 1,
          first_name: "Austin",
          last_name: "Jess",
          is_assigned: 1,
        },
      };

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("assigned");
      });

      it("valueFormatter", () => {
        expect(JSON.stringify(column.valueFormatter(params))).toEqual(
          JSON.stringify({
            assigned: true,
            assignUnassign: () => assignUnassign(params.data, "UNASSIGN"),
          })
        );
      });
    });
  });

  describe("assigned users column defs", () => {
    const defaultHeaders = ["Full Name", "Role", "Crew", "Unassign"];

    const testAssignedUsers = [
      {
        id: 1,
        first_name: "Austin",
        last_name: "Jess",
      },
      {
        id: 2,
        first_name: "Rory",
        last_name: "Letteney",
      },
    ];

    const assignUnassign = jest.fn();

    let populatedColumns;

    beforeEach(() => {
      populatedColumns = columns(
        "ASSIGNED_USERS",
        testAssignedUsers,
        assignUnassign
      );
    });

    it("Headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);
      expect(columnHeaders).toEqual(defaultHeaders);
    });

    describe("FULL NAME", () => {
      let column;

      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Full Name");
      });

      const params = {
        data: {
          user_name: "test user",
          first_name: "Austin",
          last_name: "Jess",
        },
      };
      const params2 = {
        data: {
          first_name: "Austin",
          last_name: "Jess",
        },
      };

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("test user");
        expect(column.valueGetter(params2)).toEqual("Austin Jess");
      });
    });

    describe("UNASSIGN", () => {
      let column;

      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Unassign");
      });

      const params = {
        data: {
          user_name: "test user",
          user_id: 1,
          first_name: "Austin",
          last_name: "Jess",
        },
      };

      it("valueGetter", () => {
        expect(column.valueGetter(params)).toEqual("assigned");
      });

      it("valueFormatter", () => {
        expect(JSON.stringify(column.valueFormatter(params))).toEqual(
          JSON.stringify({
            assigned: true,
            assignUnassign: assignUnassign(params.data, "UNASSIGN"),
          })
        );
      });
    });
  });
});
