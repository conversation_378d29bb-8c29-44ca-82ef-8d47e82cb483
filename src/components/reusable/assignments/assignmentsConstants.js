export const personnelFilterOptions = [
  { value: 0, display: "All Personnel" },
  { value: 1, display: "Office Personnel" },
  { value: 2, display: "Field Personnel" },
  { value: 3, display: "Shop Personnel" },
  { value: 4, display: "Client Personnel" },
];

export const columns = (
  table = "USERS",
  assignedUsers = [],
  assignUnassign = (f) => f,
  inCrewsModal = false
) => {
  // custom sort to sort alphabetically by last name
  const comparator = (valueA, valueB, rowNodeA) => {
    const pattern = new RegExp(/(?:\s).*/);

    if (rowNodeA.data.crew_id) return -1;
    if (!pattern.exec(valueA) || !pattern.exec(valueB)) return -1;
    if (pattern.exec(valueA)[0] === pattern.exec(valueB)[0]) return 0;
    else return pattern.exec(valueA)[0] > pattern.exec(valueB)[0] ? 1 : -1;
  };
  let result = [
    {
      headerName: "Full Name",
      valueGetter: (params) => {
        if (params.data.crew_id) {
          return params.data.crew_name;
        }
        return (
          params.data.user_name ||
          [params.data.first_name, params.data.last_name].join(" ")
        );
      },
      cellRenderer: "fullNameCellRenderer",
      cellClass: ["bold", "wrap"],
      sortable: true,
      comparator: comparator,
      colId: "full_name",
      minWidth: 120,
      autoHeight: true,
      suppressMenu: true,
    },
    {
      headerName: "Role",
      cellClass: "bold",
      sortable: true,
      field: "role_name",
      suppressMenu: true,
    },
  ];

  if (table === "USERS") {
    result.push({
      headerName: "Assign/Unassign",

      valueGetter: (params) => {
        if (params.data.user_id || inCrewsModal) {
          const res = assignedUsers.find((u) =>
            inCrewsModal
              ? u.id === params.data.id
              : u.user_id === params.data.user_id
          );
          return res ? "assigned" : "unassigned";
        } else {
          return assignedUsers.find((u) => u.crew_id === params.data.crew_id)
            ? "assigned"
            : "unassigned";
        }
      },
      valueFormatter: (params) => {
        let assigned = params.value === "assigned",
          direction;

        direction = assigned ? "UNASSIGN" : "ASSIGN";

        return {
          assigned,
          assignUnassign: () => assignUnassign(params.data, direction),
        };
      },
      sortable: true,
      suppressMenu: true,
      filter: false,
      cellRenderer: "assignUnassignCellRenderer",
      pinned: "right",
      suppressMovable: true,
      width: 140,
    });
  } else {
    if (!inCrewsModal) {
      result.push({
        headerName: "Crew",
        field: "crew_assigned_name",
        cellClass: "bold",
        sortable: true,
        autoHeight: true,
        suppressMenu: true,
      });
    }
    result.push({
      headerName: "Unassign",
      valueGetter: (params) => "assigned",
      valueFormatter: (params) => {
        const assigned = true;
        const direction = "UNASSIGN";

        return {
          assigned,
          assignUnassign: () => assignUnassign(params.data, direction),
        };
      },
      sortable: false,
      suppressMenu: true,
      filter: false,
      cellRenderer: "assignUnassignCellRenderer",
      pinned: "right",
      suppressMovable: true,
      minWidth: 140,
    });
  }

  return result;
};
