// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useMemo } from "react";
import Button from "msuite_storybook/dist/button/Button";
import Select from "msuite_storybook/dist/select/Select";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import {
  handleClearUsers,
  handleFetchUsersAssignedToItem,
} from "../../wizard/wizardActions";
import { handleUpdateUserAssignment } from "../../../redux/generalActions";

// COMPONENT IMPORTS
import AgTable from "../agTable/AgTable";
import AssignUnassignCellRenderer from "../frameworkComponents/AssignUnassignCellRenderer";
import FullNameCellRenderer from "../frameworkComponents/FullNameCellRenderer";

// CONSTANTS IMPORTS
import { personnelFilterOptions, columns } from "./assignmentsConstants";

import "./stylesAssignments.scss";

const Assignments = ({ selectedItem, currentTable, openManageCrews }) => {
  const [personnelFilter, setPersonnelFilter] = useState("0");
  const [usersGridOptionsApi, setUsersGridOptionsApi] = useState(null);
  const [
    assignedUsersGridOptionsApi,
    setAssignedUsersGridOptionsApi,
  ] = useState(null);
  const [assignedUsers, setAssignedUsers] = useState(null);
  const [allUsers, setAllUsers] = useState(null);
  const [allUsersSearchInput, setAllUsersSearchInput] = useState("");
  const [assignedUsersSearchInput, setAssignedUsersSearchInput] = useState("");

  const dispatch = useDispatch();
  const { userAssignments, isLoading } = useSelector(
    (state) => state.wizardData
  );

  useEffect(() => {
    return () => dispatch(handleClearUsers);
  }, []);

  useEffect(() => {
    if (usersGridOptionsApi) {
      usersGridOptionsApi.setRowData(allUsers);
    }
  }, [allUsers]);

  const f_currentTable = useMemo(() => {
    return currentTable.slice(0, -1).toLowerCase();
  }, [currentTable]);

  useEffect(() => {
    if (!f_currentTable) return;
    dispatch(
      handleFetchUsersAssignedToItem(
        selectedItem.id,
        f_currentTable,
        personnelFilter === "0" ? null : personnelFilter
      )
    );
  }, [selectedItem.id, personnelFilter, f_currentTable]);

  useEffect(() => {
    if (!usersGridOptionsApi) return;
    usersGridOptionsApi.setQuickFilter(allUsersSearchInput);
    usersGridOptionsApi.redrawRows();
  }, [allUsersSearchInput, usersGridOptionsApi]);

  useEffect(() => {
    if (!assignedUsersGridOptionsApi) return;
    assignedUsersGridOptionsApi.setQuickFilter(assignedUsersSearchInput);
    assignedUsersGridOptionsApi.redrawRows();
  }, [assignedUsersSearchInput, assignedUsersGridOptionsApi]);

  const selectedRole = useMemo(() => {
    const rolesMap = {
      1: "Office",
      2: "Field",
      3: "Shop",
      4: "Client",
    };

    return rolesMap[personnelFilter];
  }, [personnelFilter]);

  useEffect(() => {
    if (assignedUsersGridOptionsApi && !isLoading) {
      usersGridOptionsApi.setColumnDefs(
        columns("USERS", assignedUsers, assignUnassign, false)
      );
      assignedUsersGridOptionsApi.setColumnDefs(
        columns("ASSIGNED_USERS", assignedUsers, assignUnassign, false)
      );
    }
  }, [assignedUsers, isLoading, assignedUsersGridOptionsApi]);

  useEffect(() => {
    if (isLoading || !userAssignments) return;

    const assigned = userAssignments.filter((u) => u.is_assigned);
    setAssignedUsers(assigned);
  }, [isLoading, userAssignments]);

  useEffect(() => {
    if (isLoading || !userAssignments) return;

    const newAllUsers =
      personnelFilter === "0"
        ? userAssignments.filter((u) => (u.is_assigned ? !u.crew_id : true))
        : userAssignments.filter(
            (u) =>
              (u.is_assigned ? !u.crew_id : true) &&
              u.role_id === parseInt(personnelFilter)
          );

    setAllUsers(newAllUsers);
  }, [isLoading, userAssignments, personnelFilter]);

  useEffect(() => {
    if (assignedUsersGridOptionsApi) {
      assignedUsersGridOptionsApi.setRowData(assignedUsers);
    }
  }, [assignedUsers]);

  const assignUnassign = (user, direction = "ASSIGN") => {
    const userId = user.user_id ? user.user_id.toString() : null;
    const crewId = user.crew_id ? user.crew_id.toString() : null;
    switch (direction) {
      case "ASSIGN":
        return dispatch(
          handleUpdateUserAssignment(
            "assignTo",
            [selectedItem.id].join(","),
            userId,
            f_currentTable,
            crewId
          )
        ).then((res) => {
          if (!res.error) {
            dispatch(
              handleFetchUsersAssignedToItem(
                selectedItem.id,
                f_currentTable,
                personnelFilter === "0" ? null : personnelFilter
              )
            );

            const rowNode =
              usersGridOptionsApi &&
              usersGridOptionsApi.getRowNode(
                userId ? `user_${userId}` : `crew_${crewId}`
              );
            if (rowNode) {
              rowNode.setData({ ...user, is_assigned: selectedItem.id });
            }
          }
        });
      case "UNASSIGN":
        return dispatch(
          handleUpdateUserAssignment(
            "removeFrom",
            [selectedItem.id].join(","),
            userId,
            f_currentTable,
            crewId
          )
        ).then((res) => {
          if (!res.error) {
            dispatch(
              handleFetchUsersAssignedToItem(
                selectedItem.id,
                f_currentTable,
                personnelFilter === "0" ? null : personnelFilter
              )
            );

            const rowNode =
              usersGridOptionsApi &&
              usersGridOptionsApi.getRowNode(
                userId ? `user_${userId}` : `crew_${crewId}`
              );
            if (rowNode) {
              rowNode.setData({ ...user, is_assigned: null });
            }
          }
        });
      default:
        return;
    }
  };

  const onGridReady = (params, table) => {
    switch (table) {
      case "USERS":
        params.api.setSideBarVisible(false);
        params.api.setSortModel([
          {
            colId: "full_name",
            sort: "asc",
          },
        ]);
        return setUsersGridOptionsApi(params.api);
      case "ASSIGNED_USERS":
        params.api.setSideBarVisible(false);
        params.api.setSortModel([
          {
            colId: "full_name",
            sort: "asc",
          },
        ]);
        return setAssignedUsersGridOptionsApi(params.api);
      default:
        return;
    }
  };

  const onSortChanged = (params) => {
    params.api.redrawRows();
  };
  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };
  const gridOptions = (rowData, table) => ({
    rowData,
    defaultColDef: {
      wrapText: true,
      cellClass: "custom-wrap",
    },
    columnDefs: columns(table, assignedUsers, assignUnassign),
    frameworkComponents: {
      assignUnassignCellRenderer: AssignUnassignCellRenderer,
      fullNameCellRenderer: FullNameCellRenderer,
    },
    rowClassRules,
    suppressRowClickSelection: true,
    onGridReady: (params) => onGridReady(params, table),
    onSortChanged,
    getRowNodeId: (data) =>
      data.user_id ? `user_${data.user_id}` : `crew_${data.crew_id}`,
  });

  const itemName = useMemo(() => {
    let result = "";
    switch (currentTable) {
      case "JOBS":
        result = selectedItem.job_name;
        break;
      case "PACKAGES":
        result = `${selectedItem.job_name} > ${selectedItem.package_name}`;
        break;
      case "DRAWINGS":
        result = `${selectedItem.job_title} > ${selectedItem.package_name} > ${selectedItem.name}`;
        break;
      default:
        return;
    }

    if (result.length > 45) {
      result = `${result.slice(0, 42)}...`;
    }

    return result;
  }, [selectedItem, currentTable]);

  // NEED TO SEPARATE USER AND CREW CALLS WHEN ASSIGNING/UNASSIGNING ALL
  const handleAllAssignment = (direction) => {
    if (direction === "ASSIGN") {
      setAssignedUsers([...new Set([...assignedUsers, ...allUsers])]);
      dispatch(
        handleUpdateUserAssignment(
          "assignTo",
          [selectedItem.id].join(","),
          allUsers
            .filter((u) => u.user_id)
            .map((u) => u.user_id)
            .join(","),
          f_currentTable,
          null
        )
      );
      if (allUsers.find((u) => u.crew_id)) {
        dispatch(
          handleUpdateUserAssignment(
            "assignTo",
            [selectedItem.id].join(","),
            null,
            f_currentTable,
            allUsers
              .filter((u) => u.crew_id)
              .map((u) => u.crew_id)
              .join(",")
          )
        );
      }
    } else {
      setAssignedUsers(
        assignedUsers.filter((u) => {
          if (selectedRole) {
            return u.role_id !== parseInt(personnelFilter)
              ? u.role_id !== parseInt(personnelFilter)
              : false;
          } else return false;
        })
      );
      dispatch(
        handleUpdateUserAssignment(
          "removeFrom",
          [selectedItem.id].join(","),
          allUsers
            .filter((u) => u.user_id)
            .map((u) => u.user_id)
            .join(","),
          f_currentTable,
          null
        )
      );
      if (assignedUsers.find((u) => u.crew_id)) {
        dispatch(
          handleUpdateUserAssignment(
            "removeFrom",
            [selectedItem.id].join(","),
            null,
            f_currentTable,
            assignedUsers
              .filter((u) => u.crew_id)
              .map((u) => u.crew_id)
              .join(",")
          )
        );
      }
    }
  };

  return (
    <div className="assignments">
      <h2 className="title">
        <span>Personnel Assignments</span>
        <Button onClick={openManageCrews}>Manage Crews</Button>
      </h2>
      <div className="tables">
        <div className="table left">
          <div className="header-wrapper">
            <div className="header">
              <Select
                placeholder="---Filter Personnel---"
                options={personnelFilterOptions}
                onInput={(e) => setPersonnelFilter(e.target.value)}
                value={personnelFilter}
                className="personnel-filter"
              />
              <Button
                onClick={() => handleAllAssignment("UNASSIGN")}
              >{`Unassign All ${selectedRole ? selectedRole : ""}`}</Button>
              <Button
                onClick={() => handleAllAssignment("ASSIGN")}
              >{`Assign All ${selectedRole ? selectedRole : ""}`}</Button>
            </div>
            <input
              className="search-input"
              onChange={(e) => setAllUsersSearchInput(e.target.value)}
              value={allUsersSearchInput}
              placeholder={`Search users`}
              type="text"
            />
          </div>
          {allUsers && allUsers.length > 0 && assignedUsers !== null && (
            <AgTable gridOptions={gridOptions(allUsers, "USERS")} />
          )}
        </div>
        <div className="table right">
          <div className="header-wrapper">
            <div className="header">
              <h3 className="title">{`${itemName}'s Assigned Personnel`}</h3>
            </div>
            <input
              className="search-input"
              onChange={(e) => setAssignedUsersSearchInput(e.target.value)}
              value={assignedUsersSearchInput}
              placeholder={`Search assigned users`}
              type="text"
            />
          </div>
          {assignedUsers && assignedUsers.length > 0 ? (
            <AgTable
              gridOptions={gridOptions(assignedUsers, "ASSIGNED_USERS")}
            />
          ) : (
            <div className="no-assigned-users">
              <p>No users assigned</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Assignments;
