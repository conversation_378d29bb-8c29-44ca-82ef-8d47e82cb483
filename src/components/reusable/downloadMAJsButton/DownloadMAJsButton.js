// NPM PACKAGE IMPORTS
import React, { useCallback } from "react";
import { useDispatch } from "react-redux";
import Button from "msuite_storybook/dist/button/Button";

// REDUX IMPORTS
import { handleFetchDrawingsMAJMap } from "../../reusable/manageMAJModal/manageMAJModalActions";
import { handleFetchDrawingFiles } from "../../files/filesActions";
import { notify } from "../../reusable/alertPopup/alertPopupActions";

// HELPFUL FUNCTIONS IMPORTS
import { downloadFilesV2 } from "../../../_utils";

const DownloadMAJsButton = ({ selectedDrawings = [], className = "" }) => {
  const dispatch = useDispatch();

  const errorNotification = () => {
    dispatch(
      notify({
        id: Date.now(),
        type: "ERROR",
        message: "No files to download",
      })
    );
  };

  const downloadFiles = (drawings) => {
    // filter out any rows that do not have maj files...
    // input could be a list of drawing response or maj response based on table level
    // first in the OR is the drawing api properties, second is maj api properties
    let drawingsToFetch = drawings
      .filter((row) => row.has_maj || row.maj_file_id)
      .map((d) => d.id || d.drawing_id);
    // repull ALL the maj drawing urls in case the maj has been udpated since the last call
    // these urls aren't static like the drawing pdfs...
    if (drawingsToFetch.length < 1) {
      errorNotification();
      return;
    }
    dispatch(handleFetchDrawingFiles(drawingsToFetch.join(","), "maj")).then(
      (res) => {
        if (!res.errors) {
          // merge the entries with their maj files for download...
          let files = drawingsToFetch.map((id) => ({ maj_file: res[id]?.maj }));
          // downloads with the same file name as was uploaded...
          downloadFilesV2(files, "maj_file");
        }
      }
    );
  };

  const handleClick = useCallback(() => {
    if (selectedDrawings.length < 1) {
      errorNotification();
      return;
    }
    // NOTE - if this is drawing level, this works great...
    // if its not then we don't have enough information to download yet --
    // if the row contains has_maj property, then this is drawing level, else it is package
    if (!Object.hasOwn(selectedDrawings[0], "has_maj")) {
      // loop through packages and download - APIs are only per package...
      selectedDrawings.forEach((pkg) => {
        let args = [null, pkg.id || pkg.package_id];
        // second API call needed if this is package level to get drawings...
        dispatch(handleFetchDrawingsMAJMap(...args)).then((res) => {
          if (!res.error) {
            // download the files...
            downloadFiles(res);
          }
        });
      });
    } else {
      // download the drawing majs
      downloadFiles(selectedDrawings);
    }
  }, [selectedDrawings]);

  return (
    <Button
      className={className}
      onClick={handleClick}
      // TODO - Currently package entries do not return `has_maj`
      // Enables if you click a single row on package level or if you select valid drawings...
      disabled={
        selectedDrawings?.length < 1 ||
        (selectedDrawings?.some((d) => d.id) &&
          !selectedDrawings?.some((d) => d.has_maj))
      }
    >
      Download MAJ(s)
    </Button>
  );
};

export default DownloadMAJsButton;
