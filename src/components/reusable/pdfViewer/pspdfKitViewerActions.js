import {
  savePdfAnnotations,
  fetchAnnotations,
  fetchPdfUserPrefs,
  savePdfUserPrefs,
} from "../../../_services.js";
import { notify } from "../alertPopup/alertPopupActions.js";
const actions = () => {
  const receiveAnnotationsStarted = () => ({
    type: "RECEIVE_ANNOTATIONS_STARTED",
  });
  const receiveAnnotationsSucceeded = (annotations) => ({
    type: "RECEIVE_ANNOTATIONS_SUCCEEDED",
    payload: annotations,
  });
  const receiveAnnotationsFailed = (err) => ({
    type: "RECEIVE_ANNOTATIONS_FAILED",
    payload: err,
  });
  const saveAnnotationsStarted = () => ({
    type: "SAVE_ANNOTATIONS_STARTED",
  });
  const saveAnnotationsSucceeded = () => ({
    type: "SAVE_ANNOTATIONS_SUCCEEDED",
  });
  const saveAnnotationsFailed = (err) => ({
    type: "SAVE_ANNOTATIONS_FAILED",
    payload: err,
  });
  const receivePdfUserPrefsStarted = () => ({
    type: "RECEIVE_PDF_USER_PREFS_STARTED",
  });
  const receivePdfUserPrefsSucceeded = (pdfUserPrefs) => ({
    type: "RECEIVE_PDF_USER_PREFS_SUCCEEDED",
    payload: pdfUserPrefs,
  });
  const receivePdfUserPrefsFailed = (err) => ({
    type: "RECEIVE_PDF_USER_PREFS_FAILED",
    payload: err,
  });
  const savePdfUserPrefsStarted = () => ({
    type: "SAVE_PDF_USER_PREFS_STARTED",
  });
  const savePdfUserPrefsFailed = (err) => ({
    type: "SAVE_PDF_USER_PREFS_FAILED",
    payload: err,
  });

  const handleFetchAnnotations = (itemId, itemType) => (dispatch) => {
    dispatch(receiveAnnotationsStarted());
    return fetchAnnotations(itemId, itemType).then((res) => {
      if (res.error) return dispatch(receiveAnnotationsFailed(res.error));

      let annotations = res[0].annotations;
      annotations.annotations = annotations.annotations.map((a) => {
        if (!a.id) {
          a.id = a.uuid;
        }
        return a;
      });

      return dispatch(receiveAnnotationsSucceeded(annotations));
    });
  };

  const handleSaveAnnotations = (multipartFormData) => (dispatch) => {
    dispatch(saveAnnotationsStarted());
    dispatch(
      notify({
        id: Date.now(),
        type: "WARN",
        message: "Saving annotations.",
      })
    );
    return savePdfAnnotations(multipartFormData).then((res) => {
      if (res.error) return dispatch(saveAnnotationsFailed(res.error));
      dispatch(
        notify({
          id: Date.now(),
          type: "SUCCESS",
          message: "Annotations saved.",
        })
      );
      return dispatch(saveAnnotationsSucceeded());
    });
  };

  const handlePdfCleanup = (dispatch) => {
    dispatch(receiveAnnotationsSucceeded(null));
  };

  const handleFetchPdfUserPrefs = (dispatch) => {
    dispatch(receivePdfUserPrefsStarted());
    return fetchPdfUserPrefs().then((res) => {
      if (res.error) return dispatch(receivePdfUserPrefsFailed(res.error));
      return dispatch(receivePdfUserPrefsSucceeded(res[0]));
    });
  };

  const handleSavePdfUserPrefs = (prefs) => (dispatch) => {
    dispatch(savePdfUserPrefsStarted());
    return savePdfUserPrefs(prefs).then((res) => {
      if (res.error) return dispatch(savePdfUserPrefsFailed(res.error));
      return dispatch(receivePdfUserPrefsSucceeded(res[0]));
    });
  };

  return {
    handleFetchAnnotations,
    handleFetchPdfUserPrefs,
    handlePdfCleanup,
    handleSaveAnnotations,
    handleSavePdfUserPrefs,
  };
};

export default actions;
