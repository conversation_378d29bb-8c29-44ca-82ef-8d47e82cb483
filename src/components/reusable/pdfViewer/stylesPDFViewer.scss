@import "../../styles/colors.scss";
@import "../../styles/sizes.scss";

div.pdf-viewer-wrapper {
  height: calc(100vh - 110px);
  background-color: $darkGrey;
  position: relative;
  transform: translateY(5px);

  display: grid;
  grid-template-columns: 1fr 100px;
  grid-template-rows: 30px 1fr;
  row-gap: 5px;

  &.narrow {
    width: calc(100vw - #{$navExpanded});
    transform: translate(127px, 5px);
  }

  & > div.viewer {
    height: calc(100vh - 140px);
  }

  & div.no-move-buttons {
    height: calc(100vh - 110px);
  }

  & > div.traversal-buttons {
    grid-row: 1/2;
    grid-column: 1/-1;
    padding: 0 10px;
    height: 35px;

    display: grid;
    column-gap: 10px;
    grid-template-columns: 1fr 1fr 1fr;

    & > button,
    span button {
      padding: 0 10px;
      height: 30px;
      font-size: 0.8rem;
      width: auto;
      background-color: $fabProBlue;
      color: #fff;

      display: inline-flex;
      justify-content: space-around;
      align-items: center;
      gap: 5px;

      &:hover {
        background-color: darken($fabProBlue, 10%);
      }
    }

    & > button#previous {
      grid-column: 1/2;
      justify-self: start;
    }

    & > span {
      grid-column: 3/-1;
      justify-self: end;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      gap: 5px;
    }

    & span button.add-remove {
      width: 160px;
    }

    & p {
      grid-column: 2/3;
      justify-self: center;
      margin: 0;
      color: white;
      font-size: 1.1rem;
      display: flex;
      align-items: center;
      font-weight: bold;
    }
  }

  & section.content-wrapper {
    height: 100%;
    width: 100%;

    & div.error-message {
      height: 100%;
      width: 100%;

      display: grid;
      justify-content: center;
      align-items: center;

      & p {
        margin: 0;
        font-size: 1.2rem;
        color: $fieldProOrange;
      }
    }
  }

  & button.fullscreen-button {
    position: absolute;
    bottom: 15px;
    right: 15px;
    width: 30px;
    height: 30px;
    background-color: $blue;
    color: #fff;
    border: 1px solid $darkGrey;
    border-radius: 3px;
    font-size: 1.2rem;
    cursor: pointer;
    transition: background-color 250ms ease;

    display: grid;
    justify-content: center;
    align-items: center;

    &:hover {
      background-color: darken($blue, 10%);
    }
  }

  & div.file-type {
    padding: 10px;
    height: 200px;

    display: flex;
    flex-direction: column;
    row-gap: 15px;

    & > button {
      width: 100%;
      padding: 0 5px;
      height: 40px;
      background-color: $fabProBlue;
      color: #fff;
      font-size: 0.8rem;

      &:not(:disabled):hover {
        background-color: darken($fabProBlue, 10%);
      }
    }
  }
}
