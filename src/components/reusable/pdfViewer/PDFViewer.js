// NPM PACKAGE IMPORTS
import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from "react";
import PSPDFKitViewer from "./PSPDFKitViewer";
import Button from "msuite_storybook/dist/button/Button";
import { useDispatch, useSelector } from "react-redux";
import { IoMdExpand, IoMdContract } from "react-icons/io";
import { FaArrowLeft, FaArrowRight } from "react-icons/fa";

// COMPONENT IMPORTS
import ForgeViewer from "./../forge/ForgeViewer";

// STYLE IMPORTS
import "./stylesPDFViewer.scss";
import "./stylesPDFViewer.css";
import "../forge/stylesForgeViewer.scss";

// REDUX IMPORTS
import {
  handleFetchDrawingFiles,
  handleFetchPackageFile,
} from "../../files/filesActions";

const PDFViewer = ({
  selectedItem,
  itemId,
  itemType,
  file,
  setFile,
  refresh,
  area,
  storeInstanceOutside = null,
  currentRowOrder,
  genericTimeArea = false,
  handleAddRemoveUpdate, // for g time
  selectedDrawings, // for g time
  setSelectedItem,
  setCurrentRowOrder,
}) => {
  const [fullscreen, toggleFullscreen] = useState(false);
  const [current, setCurrent] = useState(null);
  const [showForge, toggleForge] = useState(false);
  const [fileType, setFileType] = useState("ORIGINAL");
  const [currentRow, setCurrentRow] = useState({});
  const isPackageLevel = itemType === "PACKAGE";

  const { token, userInfo, permissions, systemSettings, userId } = useSelector(
    (state) => state.profileData
  );
  const { packageFiles, drawingFiles } = useSelector(
    (state) => state.filesData
  );

  const { navExpanded } = useSelector((state) => state.generalData);
  const { activeTimer } = useSelector((state) => state.timerData);

  // used for the PDF files that we fetch from AWS S3 service
  const dispatch = useDispatch();

  const packageFilesRef = useRef(null);
  useEffect(() => {
    packageFilesRef.current = packageFiles;
  }, [packageFiles]);

  const drawingFilesRef = useRef(null);
  useEffect(() => {
    drawingFilesRef.current = drawingFiles;
  }, [drawingFiles]);

  // common code for fetching files for the viewer if they aren't available...
  const getFiles = (currentRow) => {
    // drawing_id takes precedence since we could be at item level
    const drawing_id = currentRow?.drawing_id ?? currentRow?.id;
    const package_id = isPackageLevel ? currentRow?.id : currentRow?.package_id;

    if (
      !isPackageLevel &&
      currentRow.has_original &&
      drawing_id &&
      !drawingFilesRef?.current?.hasOwnProperty(drawing_id)
    ) {
      dispatch(handleFetchDrawingFiles(drawing_id));
    } // else we exit because we already know the drawing file

    if (
      currentRow.has_package_map &&
      package_id &&
      !packageFilesRef?.current?.hasOwnProperty(package_id)
    ) {
      dispatch(handleFetchPackageFile(package_id));
    } // else we exit because we already know the package file
  };

  useEffect(() => {
    if (currentRowOrder) {
      let currentIndex = currentRowOrder.findIndex(
        (r) =>
          (r.drawing_id !== undefined ? r.drawing_id : r.id) ===
          (current === null ? itemId : currentRow.id)
      );
      setCurrent(currentIndex > 0 ? currentIndex : 0);
    } else {
      setCurrent(null);
    }
  }, [itemId, currentRowOrder]);

  useEffect(() => {
    if (currentRowOrder && currentRowOrder[current]) {
      const activeRow = currentRowOrder[current];
      setSelectedItem && setSelectedItem(activeRow);
      setCurrentRow(activeRow);
      const drawing_id = isPackageLevel
        ? activeRow?.drawing_id
        : activeRow?.drawing_id ?? activeRow?.id;
      const package_id = isPackageLevel ? activeRow?.id : activeRow?.package_id;
      const originalPdf = drawingFilesRef?.current?.[drawing_id]?.original;
      const packageMap = packageFilesRef?.current?.[package_id];

      // versus rewriting the default file code for showForge on/off, create property...
      const defaultFile = originalPdf || packageMap;
      const forgeFile = activeRow.forge_urn;
      // if we are on forge, try to show forge first, else try to show 2d first then forge last...
      setFile(showForge ? forgeFile || defaultFile : defaultFile || forgeFile);
      // if PDF has annotations, set fileType to annotations so that's shown by default
      setFileType(activeRow.has_annotated ? "ANNOTATED" : "ORIGINAL");

      // TODO - The current viewer doesn't switch states correctly for the only option being 3D Model
      // This is confusing since if we're toggling between previous/next there is a moment where the PDF file is loading
      // Therefore it is taking favor over the forge model which is the issue...
      // if (showForge && forgeFile) setFile(forgeFile);
      // else if (!showForge && !defaultFile && forgeFile) {
      //   setFile(forgeFile);
      //   toggleForge(true);
      // } else {
      //   setFile(defaultFile);
      //   setFileType(activeRow.has_annotated ? "ANNOTATED" : "ORIGINAL"); // for the buttons to work...
      // }
    } else {
      setCurrentRow(selectedItem);
      setFileType(selectedItem.has_annotated ? "ANNOTATED" : "ORIGINAL");
    }
  }, [current, selectedItem, currentRowOrder, drawingFiles]);

  // this is used to toggle to Annotated view by default if annotated file exists WHEN USING NEXT/PREV BUTTONS (selectedItem is used when viewer doesn't have next/prev buttons, whereas currentRow is used when traversing via the next/prev buttons. This is something we can potentially clean up to reduce complexity)
  useEffect(() => {
    // check if we need to load files...
    getFiles(currentRow);

    if (currentRow?.has_annotated && fileType !== "ANNOTATED")
      setFileType("ANNOTATED");
  }, [currentRow]);

  const handleNext = useCallback(() => {
    if (currentRowOrder) {
      setCurrent(current + 1);
    }
  }, [current]);

  const handlePrevious = useCallback(() => {
    if (currentRowOrder) {
      setCurrent(current - 1);
    }
  }, [current]);

  const handleForgeClick = () => {
    setFile(currentRow.forge_urn);
    toggleForge(true);
  };

  const isDrawingInSelection = useMemo(() => {
    if (!selectedDrawings) return false;

    return selectedDrawings.find((sd) => sd.id === currentRow.id)
      ? true
      : false;
  }, [selectedDrawings, currentRow]);

  // Separated to trigger re-rendering...
  const ForgeFrame = () => {
    return (
      <div className="viewer forge-viewer">
        <ForgeViewer
          appType="fab"
          token={token}
          userId={userId}
          systemSettings={systemSettings}
          permissions={permissions}
          origin="web"
          spool={{ ...currentRow, type: itemType + "S" }}
          useModalLayout={true}
          navExpanded={navExpanded}
          area={area}
          storeInstanceOutside={storeInstanceOutside}
          userInfo={userInfo}
          refresh={refresh}
        />
        <button
          className="fullscreen-button"
          onClick={() => toggleFullscreen(!fullscreen)}
        >
          {fullscreen ? <IoMdContract /> : <IoMdExpand />}
        </button>
      </div>
    );
  };

  return (
    <div
      className={`pdf-viewer-wrapper ${fullscreen ? "fullscreen" : ""} ${
        navExpanded ? "narrow" : ""
      }`}
    >
      {currentRowOrder && (
        <div className="traversal-buttons">
          {current > 0 &&
            (!currentRow.drawing_id ||
              (itemType === "PACKAGE" && currentRow.drawing_id)) && (
              <Button onClick={handlePrevious} id="previous">
                <FaArrowLeft />
                Previous
              </Button>
            )}
          {genericTimeArea && (
            <p>{currentRow.original_name ? currentRow.original_name : ""}</p>
          )}
          <span>
            <>
              {genericTimeArea && !activeTimer && (
                <Button
                  onClick={() => {
                    handleAddRemoveUpdate(
                      isDrawingInSelection ? "REMOVE" : "ADD",
                      currentRow
                    );
                  }}
                  className="add-remove"
                >
                  {isDrawingInSelection ? `Remove From` : `Add To`} Selection
                </Button>
              )}
              {current < currentRowOrder.length - 1 &&
                (!currentRow.drawing_id ||
                  (itemType === "PACKAGE" && currentRow.drawing_id)) && (
                  <Button onClick={handleNext} id="next">
                    Next
                    <FaArrowRight />
                  </Button>
                )}
            </>
          </span>
        </div>
      )}
      {showForge && file && !/pdf/.test(file) ? (
        <ForgeFrame />
      ) : (
        <div
          className={
            itemType === "PACKAGE" ? `viewer no-move-buttons` : `viewer`
          }
        >
          <PSPDFKitViewer
            selectedItem={currentRow}
            itemId={
              currentRow.drawing_id && itemType === "DRAWING"
                ? currentRow.drawing_id
                : currentRow.id
                ? currentRow.id
                : itemId
            }
            itemType={itemType}
            file={file}
            showAnnotations={fileType === "ANNOTATED"}
            area={area}
            storeInstanceOutside={storeInstanceOutside}
            token={token}
            userInfo={userInfo}
            refresh={refresh}
            hasPermission={
              itemType !== "PACKAGE" && !/map\.pdf/.test(file)
                ? permissions?.includes(111)
                : false
            }
            isArchived={currentRow.archived ? true : false}
            fileType={fileType}
          />
          <button
            className="fullscreen-button"
            onClick={() => toggleFullscreen(!fullscreen)}
          >
            {fullscreen ? <IoMdContract /> : <IoMdExpand />}
          </button>
        </div>
      )}
      <div className="file-type">
        {currentRow.has_original !== 0 && itemType !== "PACKAGE" && (
          <Button
            onClick={() => {
              setFile(
                drawingFilesRef?.current?.[
                  currentRow.drawing_id ? currentRow.drawing_id : currentRow.id
                ]?.original
              );
              setFileType("ORIGINAL");
              toggleForge(false);
            }}
            className={
              fileType === "ORIGINAL" && !/map\.pdf/.test(file) && !showForge
                ? "selected"
                : ""
            }
            disabled={
              fileType === "ORIGINAL" && !/map\.pdf/.test(file) && !showForge
            }
          >
            Original
          </Button>
        )}
        {currentRow.has_annotated !== 0 && itemType !== "PACKAGE" && (
          <Button
            onClick={() => {
              setFile(
                drawingFilesRef?.current?.[
                  currentRow.drawing_id ? currentRow.drawing_id : currentRow.id
                ]?.original
              );
              setFileType("ANNOTATED");
              toggleForge(false);
            }}
            className={
              fileType === "ANNOTATED" && !/map\.pdf/.test(file) && !showForge
                ? "selected"
                : ""
            }
            disabled={
              fileType === "ANNOTATED" && !/map\.pdf/.test(file) && !showForge
            }
          >
            Annotated
          </Button>
        )}
        {/* show the button only if we have a file OR if we're on package level to indicate no file */}
        {((currentRow &&
          currentRow.has_package_map &&
          (packageFilesRef?.current?.[currentRow.package_id ?? currentRow.id] ||
            isPackageLevel)) ||
          itemType === "PACKAGE") && (
          <Button
            onClick={() => {
              const itemId =
                itemType === "PACKAGE"
                  ? currentRow?.id
                  : currentRow?.package_id;
              setFile(packageFilesRef?.current?.[itemId]);
              toggleForge(false);
            }}
            className={
              /map\.pdf/.test(file) || itemType === "PACKAGE" ? "selected" : ""
            }
            disabled={/map\.pdf/.test(file) || itemType === "PACKAGE"}
          >
            Package Map
          </Button>
        )}
        {currentRow && currentRow.forge_urn && (
          <Button
            onClick={handleForgeClick}
            className={showForge ? "selected" : ""}
            disabled={showForge}
          >
            3D Model
          </Button>
        )}
      </div>
    </div>
  );
};

export default PDFViewer;
