const initialState = {
  isLoading: false,
  error: null,
  pdfPath: null,
  annotations: null,
  pdfUserPrefs: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_PDF_PATH_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_PDF_PATH_SUCCEEDED":
      return { ...state, isLoading: false, error: null, pdfPath: payload };
    case "RECEIVE_PDF_PATH_FAILED":
      return { ...state, isLoading: false, error: payload, pdfPath: null };
    case "RECEIVE_ANNOTATIONS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_ANNOTATIONS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, annotations: payload };
    case "RECEIVE_ANNOTATIONS_FAILED":
      return { ...state, isLoading: false, error: payload, annotations: null };
    case "SAVE_ANNOTATIONS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "SAVE_ANNOTATIONS_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "SAVE_ANNOTATIONS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_PDF_USER_PREFS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_PDF_USER_PREFS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, pdfUserPrefs: payload };
    case "RECEIVE_PDF_USER_PREFS_FAILED":
      return { ...state, isLoading: false, error: payload, pdfUserPrefs: {} };
    case "SAVE_PDF_USER_PREFS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "SAVE_PDF_USER_PREFS_FAILED":
      return { ...state, isLoading: false, error: payload };
    default:
      return state;
  }
}
