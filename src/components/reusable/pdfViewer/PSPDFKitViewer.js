/* eslint-disable react-hooks/exhaustive-deps */
// NPM PACKAGE IMPORTS
import React, { useState, useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import PSPDFKitWeb from "pspdfkit";

// REDUX IMPORTS
import actions from "./pspdfKitViewerActions";

// COMPONENT IMPORTS
import PDFError from "./PDFError";

// HELPER FUNCTION IMPORTS
import { escapeRegExp } from "../../../_utils";

// STYLE IMPORTS
import "./stylesPSPDFKitViewer.scss";

// CONSTANTS
const validItemTypes = ["PACKAGE", "DRAWING", "WORK_ITEM"];

// EXPORTS
const PSPDFKitViewer = ({
  selectedItem,
  itemId,
  itemType,
  file,
  refresh,
  area,
  storeInstanceOutside = null,
  token = null,
  userInfo = {},
  hasPermission = false,
  isArchived = false,
  showAnnotations = true,
  onLoadStarted = null, // callback to run when the loading starts
  onLoadSuccess = null, // callback to run when the loading succeeds
  onLoadFailed = null, // callback to run when the loading fails
  fileType,
}) => {
  const [instance, setInstance] = useState(null);
  const [changedAnnotation, setChangedAnnotation] = useState(false);

  const dispatch = useDispatch();
  const { annotations, pdfUserPrefs } = useSelector(
    (state) => state.pspdfkitData
  );

  const {
    handleFetchAnnotations,
    handleSaveAnnotations,
    handlePdfCleanup,
    handleFetchPdfUserPrefs,
    handleSavePdfUserPrefs,
  } = actions(token);

  const fetchAnnotations = useCallback(async () => {
    await dispatch(
      handleFetchAnnotations(
        itemType === "WORK_ITEM" ? selectedItem.drawing_id : itemId,
        itemType
      )
    );
  }, [dispatch, itemId, itemType]);

  const fetchPdfUserPrefs = useCallback(async () => {
    await dispatch(handleFetchPdfUserPrefs);
  }, [dispatch]);

  const saveAnnotations = useCallback(
    (instance) => async () => {
      if (!instance) return;
      setChangedAnnotation(true);
      // LIST OF ANNOTATIONS
      const instantJSON = await instance.exportInstantJSON();

      if (!instantJSON.annotations) instantJSON.annotations = [];

      instantJSON.pdfId = null;

      // BINARY INFORMATION TO SAVE FILE
      const pdfBuffer = await instance.exportPDF({ flatten: true });
      const pdfBlob = new Blob([pdfBuffer], { type: "application/pdf" });
      const pdfUrl = window.URL.createObjectURL(pdfBlob);

      // FORM DATA EXPECTED BY API
      let multipartFormObject = new FormData();
      multipartFormObject.append("annotations", JSON.stringify(instantJSON));
      multipartFormObject.append("pdf_file", pdfBlob, pdfUrl + ".pdf");

      switch (itemType) {
        case "DRAWING":
          multipartFormObject.append("drawing_id", itemId);
          break;
        case "WORK_ITEM":
          multipartFormObject.append("drawing_id", selectedItem.drawing_id);
          break;
        default:
          return;
      }

      // PERFORM SAVE
      await dispatch(handleSaveAnnotations(multipartFormObject));
      await fetchAnnotations();

      // PERFORM CLEANUP
      window.URL.revokeObjectURL(pdfUrl);

      // PERFORM REFRESH
      // ToDo - Fix here for reloading when a drawing has an annotation...
      if (selectedItem && !selectedItem.has_annotated) refresh();
    },
    [
      instance,
      itemType,
      dispatch,
      fetchAnnotations,
      selectedItem,
      refresh,
      itemId,
      annotations,
      token,
    ]
  );

  // reload pdf on just annotation change or fileType change, so when new ones are fetchhed
  useEffect(() => {
    if (!changedAnnotation) {
      loadPdf();
    }
  }, [annotations, changedAnnotation]);

  const loadPdf = async () => {
    setChangedAnnotation(false);
    if (!file || (itemType !== "PACKAGE" && !pdfUserPrefs)) return;

    const toolbarItems = [
      { type: "sidebar-thumbnails" },
      { type: "sidebar-document-outline" },

      { type: "pager" },
      { type: "pan" },
      { type: "zoom-out" },
      { type: "zoom-in" },
      { type: "zoom-mode" },
      { type: "spacer" },

      { type: "print" },

      { type: "search" },
    ];

    const loadAnnotations =
      ((selectedItem && !selectedItem.has_annotated) ||
        (showAnnotations && fileType === "ANNOTATED")) &&
      itemType !== "PACKAGE" &&
      !/map\.pdf/.test(file) &&
      !isArchived;

    const allowAnnotations = loadAnnotations && hasPermission;

    if (allowAnnotations) {
      toolbarItems.splice(
        8,
        0,
        ...[
          { type: "annotate" },
          { type: "ink" },
          { type: "highlighter" },
          { type: "text-highlighter" },
          { type: "ink-eraser" },
          { type: "signature" },
          { type: "stamp" },
          { type: "text" },
          { type: "line" },
          { type: "arrow" },
          { type: "rectangle" },
          { type: "ellipse" },
          { type: "polygon" },
          { type: "polyline" },
        ]
      );
      toolbarItems.splice(2, 0, { type: "sidebar-annotations" });
    }

    let annotationPresets = {};
    if (pdfUserPrefs) {
      const {
        ink_size,
        ink_color,
        freeform_highlight_color,
        freeform_highlight_size,
        markup_highlight_color,
        text_font,
        text_color,
        text_size,
        line_size,
        line_color,
        arrow_size,
        arrow_color,
        rectangle_size,
        rectangle_color,
        ellipse_size,
        ellipse_color,
        polygon_size,
        polygon_color,
        polyline_size,
        polyline_color,
      } = pdfUserPrefs;

      let ink = {
        strokeColor: ink_color
          ? new PSPDFKitWeb.Color(JSON.parse(ink_color))
          : PSPDFKitWeb.Color.RED,
      };
      if (ink_size) Object.assign(ink, { lineWidth: ink_size });

      let highlighter = {
        strokeColor: freeform_highlight_color
          ? new PSPDFKitWeb.Color(JSON.parse(freeform_highlight_color))
          : PSPDFKitWeb.Color.YELLOW,
      };
      if (freeform_highlight_size)
        Object.assign(highlighter, { lineWidth: freeform_highlight_size });

      let text = {
        fontColor: text_color
          ? new PSPDFKitWeb.Color(JSON.parse(text_color))
          : PSPDFKitWeb.Color.RED,
      };
      if (text_size) Object.assign(text, { fontSize: text_size });
      if (text_font) Object.assign(text, { font: text_font });

      let line = {
        strokeColor: line_color
          ? new PSPDFKitWeb.Color(JSON.parse(line_color))
          : PSPDFKitWeb.Color.RED,
      };
      if (line_size) Object.assign(line, { strokeWidth: line_size });

      let arrow = {
        strokeColor: arrow_color
          ? new PSPDFKitWeb.Color(JSON.parse(arrow_color))
          : PSPDFKitWeb.Color.RED,
      };
      if (arrow_size) Object.assign(arrow, { strokeWidth: arrow_size });

      let rectangle = {
        strokeColor: rectangle_color
          ? new PSPDFKitWeb.Color(JSON.parse(rectangle_color))
          : PSPDFKitWeb.Color.RED,
      };
      if (rectangle_size)
        Object.assign(rectangle, { strokeWidth: rectangle_size });

      let ellipse = {
        strokeColor: ellipse_color
          ? new PSPDFKitWeb.Color(JSON.parse(ellipse_color))
          : PSPDFKitWeb.Color.RED,
      };
      if (ellipse_size) Object.assign(ellipse, { strokeWidth: ellipse_size });

      let polygon = {
        strokeColor: polygon_color
          ? new PSPDFKitWeb.Color(JSON.parse(polygon_color))
          : PSPDFKitWeb.Color.RED,
      };
      if (polygon_size) Object.assign(polygon, { strokeWidth: polygon_size });

      let polyline = {
        strokeColor: polyline_color
          ? new PSPDFKitWeb.Color(JSON.parse(polyline_color))
          : PSPDFKitWeb.Color.RED,
      };
      if (polyline_size)
        Object.assign(polyline, { strokeWidth: polyline_size });

      annotationPresets = {
        ink,
        highlighter,
        "text-highlighter": {
          strokeColor: markup_highlight_color
            ? new PSPDFKitWeb.Color(JSON.parse(markup_highlight_color))
            : PSPDFKitWeb.Color.YELLOW,
        },
        text,
        line,
        arrow,
        rectangle,
        ellipse,
        polygon,
        polyline,
      };
    }

    let config = {
      document: file,
      container: "#pspdfkit",
      toolbarItems,
      licenseKey:
        process.env.REACT_APP_PROD_PSPDFKIT_LICENSE ||
        process.env.REACT_APP_DEV_PSPDFKIT_LICENSE,
      baseUrl: `${window.location.protocol}//${window.location.host}/`,
      annotationPresets,
      // below removes double click to select text options
      // https://pspdfkit.com/guides/web/user-interface/inline-text-selection-toolbar/remove-all-tools/
      inlineTextSelectionToolbarItems: () => {
        return [];
      },
      printMode: PSPDFKitWeb.PrintMode.EXPORT_PDF,
    };

    if (loadAnnotations) {
      Object.assign(config, { instantJSON: annotations });
    }

    PSPDFKitWeb.unload(instance || "#pspdfkit");

    if (onLoadStarted) onLoadStarted();
    await PSPDFKitWeb.load(config)
      .then((_instance) => {
        _instance.setAnnotationCreatorName(
          `${escapeRegExp(userInfo.first_name)}${
            userInfo.last_name ? " " + escapeRegExp(userInfo.last_name) : ""
          }`
        );

        let currentInteractionMode = null;
        const presetMap = {
          TEXT_HIGHLIGHTER: "text-hightlighter",
          INK: "ink",
          INK_SIGNATURE: "signature",
          STAMP_PICKER: "stamp",
          STAMP_CUSTOM: "stamp",
          SHAPE_LINE: "line",
          SHAPE_RECTANGLE: "rectangle",
          SHAPE_ELLIPSE: "ellipse",
          SHAPE_POLYGON: "polygon",
          SHAPE_POLYLINE: "polyline",
          TEXT: "text",
        };

        const interactionModePersister = (viewState) => {
          if (
            (!viewState.interactionMode &&
              (currentInteractionMode ===
                PSPDFKitWeb.InteractionMode.DOCUMENT_EDITOR ||
                currentInteractionMode ===
                  PSPDFKitWeb.InteractionMode.STAMP_PICKER ||
                currentInteractionMode ===
                  PSPDFKitWeb.InteractionMode.STAMP_CUSTOM ||
                currentInteractionMode ===
                  PSPDFKitWeb.InteractionMode.INK_SIGNATURE)) ||
            (viewState.interactionMode &&
              currentInteractionMode !== viewState.interactionMode)
          ) {
            currentInteractionMode = viewState.interactionMode;

            dispatch(
              handleSavePdfUserPrefs({ last_used: viewState.interactionMode })
            );
          }
          if (_instance.currentAnnotationPreset) {
            _instance.removeEventListener(
              "viewState.change",
              interactionModePersister
            );
            try {
              if (presetMap[currentInteractionMode])
                _instance.setCurrentAnnotationPreset(
                  presetMap[currentInteractionMode]
                );
            } catch {}
            _instance.setViewState((s) =>
              s.set(
                "interactionMode",
                PSPDFKitWeb.InteractionMode[currentInteractionMode]
              )
            );
            _instance.addEventListener(
              "viewState.change",
              interactionModePersister
            );
          }
        };

        _instance.addEventListener(
          "viewState.change",
          interactionModePersister
        );

        const pspdfkitPreferencesUpdater = (event) => {
          const {
            currentPreset,
            currentPresetProperties,
            newPresetProperties,
          } = event;

          if (newPresetProperties) {
            if (
              newPresetProperties.strokeColor &&
              JSON.stringify(newPresetProperties.strokeColor) ===
                JSON.stringify(currentPresetProperties.strokeColor)
            )
              return;

            let dataToSend = {};

            const {
              lineWidth,
              strokeColor,
              strokeWidth,
              font,
              fontColor,
              fontSize,
            } = newPresetProperties;
            switch (currentPreset) {
              case "ink":
                if (lineWidth)
                  Object.assign(dataToSend, { ink_size: lineWidth });
                if (strokeColor)
                  Object.assign(dataToSend, {
                    ink_color: JSON.stringify({
                      r: strokeColor.r,
                      g: strokeColor.g,
                      b: strokeColor.b,
                    }),
                  });
                break;
              case "highlighter":
                if (lineWidth)
                  Object.assign(dataToSend, {
                    freeform_highlight_size: lineWidth,
                  });
                if (strokeColor)
                  Object.assign(dataToSend, {
                    freeform_highlight_color: JSON.stringify({
                      r: strokeColor.r,
                      g: strokeColor.g,
                      b: strokeColor.b,
                    }),
                  });
                break;
              case "text-highlighter":
                if (strokeColor)
                  Object.assign(dataToSend, {
                    markup_highlight_color: JSON.stringify({
                      r: strokeColor.r,
                      g: strokeColor.g,
                      b: strokeColor.b,
                    }),
                  });
                break;
              case "text":
                if (fontSize)
                  Object.assign(dataToSend, {
                    text_size: fontSize,
                  });
                if (fontColor)
                  Object.assign(dataToSend, {
                    text_color: JSON.stringify({
                      r: fontColor.r,
                      g: fontColor.g,
                      b: fontColor.b,
                    }),
                  });
                if (font) Object.assign(dataToSend, { text_font: font });
                break;
              case "line":
                if (strokeWidth)
                  Object.assign(dataToSend, {
                    line_size: strokeWidth,
                  });
                if (strokeColor)
                  Object.assign(dataToSend, {
                    line_color: JSON.stringify({
                      r: strokeColor.r,
                      g: strokeColor.g,
                      b: strokeColor.b,
                    }),
                  });
                break;
              case "arrow":
                if (strokeWidth)
                  Object.assign(dataToSend, {
                    arrow_size: strokeWidth,
                  });
                if (strokeColor)
                  Object.assign(dataToSend, {
                    arrow_color: JSON.stringify({
                      r: strokeColor.r,
                      g: strokeColor.g,
                      b: strokeColor.b,
                    }),
                  });
                break;
              case "rectangle":
                if (strokeWidth)
                  Object.assign(dataToSend, {
                    rectangle_size: strokeWidth,
                  });
                if (strokeColor)
                  Object.assign(dataToSend, {
                    rectangle_color: JSON.stringify({
                      r: strokeColor.r,
                      g: strokeColor.g,
                      b: strokeColor.b,
                    }),
                  });
                break;
              case "ellipse":
                if (strokeWidth)
                  Object.assign(dataToSend, {
                    ellipse_size: strokeWidth,
                  });
                if (strokeColor)
                  Object.assign(dataToSend, {
                    ellipse_color: JSON.stringify({
                      r: strokeColor.r,
                      g: strokeColor.g,
                      b: strokeColor.b,
                    }),
                  });
                break;
              case "polygon":
                if (strokeWidth)
                  Object.assign(dataToSend, {
                    polygon_size: strokeWidth,
                  });
                if (strokeColor)
                  Object.assign(dataToSend, {
                    polygon_color: JSON.stringify({
                      r: strokeColor.r,
                      g: strokeColor.g,
                      b: strokeColor.b,
                    }),
                  });
                break;
              case "polyline":
                if (strokeWidth)
                  Object.assign(dataToSend, {
                    polyline_size: strokeWidth,
                  });
                if (strokeColor)
                  Object.assign(dataToSend, {
                    polyline_color: JSON.stringify({
                      r: strokeColor.r,
                      g: strokeColor.g,
                      b: strokeColor.b,
                    }),
                  });
                break;
              default:
                break;
            }

            dispatch(handleSavePdfUserPrefs(dataToSend));
          }
        };

        _instance.addEventListener(
          "viewState.change",
          pspdfkitPreferencesUpdater
        );

        _instance.addEventListener(
          "annotationPresets.update",
          pspdfkitPreferencesUpdater
        );

        // Removed as per the ticket https://sbd-contech.atlassian.net/browse/DEVP1-6122
        // _instance.contentDocument.host.addEventListener("wheel", (e) => {
        //   if (e.deltaY > 0) {
        //     _instance.setViewState((viewState) => viewState.zoomOut());
        //   } else {
        //     _instance.setViewState((viewState) => viewState.zoomIn());
        //   }
        // });

        _instance.setViewState((viewState) =>
          viewState.set("interactionMode", PSPDFKitWeb.InteractionMode.PAN)
        );

        if (!hasPermission || isArchived) {
          _instance.addEventListener("annotations.press", (e) => {
            e.preventDefault();
          });
        }

        if (_instance && hasPermission && !isArchived) {
          _instance.removeEventListener(
            "annotations.didSave",
            saveAnnotations(_instance)
          );
          _instance.addEventListener(
            "annotations.didSave",
            saveAnnotations(_instance)
          );
        }

        setInstance(_instance);
        if (storeInstanceOutside) storeInstanceOutside(_instance);
        if (onLoadSuccess) onLoadSuccess(_instance);
      })
      .catch((error) => {
        if (onLoadFailed) onLoadFailed(error.message);
      });
  };

  const unloadPdf = async () => {
    try {
      PSPDFKitWeb.unload(instance || "#pspdfkit");
      setInstance(null);
      if (storeInstanceOutside) storeInstanceOutside(null);
    } catch {}
  };

  useEffect(() => {
    setInstance(null);
    if (storeInstanceOutside) storeInstanceOutside(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [itemId, file]);

  useEffect(() => {
    if (validItemTypes.includes(itemType)) {
      if (itemType !== "PACKAGE") {
        fetchAnnotations();
        fetchPdfUserPrefs();
      }
    } else {
      try {
        PSPDFKitWeb.unload(instance || "#pspdfkit");
        setInstance(null);
        if (storeInstanceOutside) storeInstanceOutside(null);
      } catch {}
    }
  }, [fetchAnnotations, fetchPdfUserPrefs]);

  useEffect(() => {
    if (!instance && validItemTypes.includes(itemType)) {
      // TODO - this is causing an infinite loop when de-selecting last drawing - file is switching between undefined and a valid URL
      loadPdf();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [instance, itemId, annotations, file, pdfUserPrefs, itemType]);

  useEffect(() => {
    loadPdf();
  }, [fileType]);

  useEffect(() => {
    return async () => {
      unloadPdf();
      dispatch(handlePdfCleanup);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <section className="content-wrapper">
      <div id="pspdfkit" className={!file && "hidden"}></div>
      {!(file && validItemTypes.includes(itemType)) && (
        <PDFError pdfPath={!!file} area={area} />
      )}
    </section>
  );
};

export default (props) => <PSPDFKitViewer {...props} />;
