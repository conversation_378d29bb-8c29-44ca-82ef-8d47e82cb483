@import "../../styles/colors.scss";

div.bst-wrapper {
  height: 55px;
  background-color: $fabProBlue;
  color: #fff;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  box-sizing: border-box;
  border-bottom: 1px solid lighten($fabProBlue, 10%);
  clear: both;
  overflow: hidden;

  & div.bst-tab {
    display: inline-block;
    padding: 0 10px;
    height: 54px;
    line-height: 54px;

    &.selected {
      background-color: $fabProBlue;
      cursor: default;
    }

    &.clickable:not(.selected):hover {
      cursor: pointer;
    }

    &:first-of-type {
      border-top-left-radius: 3px;
    }
  }

  & div.bst-header-buttons {
    float: right;
    padding: 0 10px 0 0;
    box-sizing: border-box;

    display: flex;
    align-items: center;
    justify-content: space-evenly;

    height: 54px;

    & a.bst-header-button {
      background-color: $darkGrey;
      color: $fabProBlue;
      text-decoration: none;
      font-size: 0.8rem;
      height: 35px;
      line-height: 35px;
      padding: 0 10px;

      &:not(:first-of-type) {
        margin-left: 5px;
      }
    }
  }
}
