// NPM PACKAGE IMPORTS
import React from "react";
import Button from "msuite_storybook/dist/button/Button";

// HELPER FUNCTION IMPORTS
import { permissionLock } from "../../../_utils";

// STYLE IMPORTS
import "./stylesBodySectionTabs.scss";

// EXPORTS
const BodySectionTabs = ({
  tabs,
  selectedTab,
  setSelectedTab,
  headerButtons,
}) => {
  const permissionLockedTabs = permissionLock(tabs);
  const permissionLockedHeaderButtons = permissionLock(headerButtons);

  return (
    <div className="bst-wrapper">
      {permissionLockedTabs.map((t, idx) => {
        return (
          <div
            key={t.name}
            onClick={() => {
              if (idx !== selectedTab) {
                if (t.onClick) t.onClick();
                if (setSelectedTab) setSelectedTab(idx);
              }
            }}
            className={`bst-tab ${idx === selectedTab ? "selected" : ""} ${
              setSelectedTab ? "clickable" : ""
            }`}
          >
            {t.name}
          </div>
        );
      })}
      <div className="bst-header-buttons">
        {headerButtons ? (
          permissionLockedHeaderButtons.map((b) => {
            return (
              <Button
                className="bst-header-button"
                key={b.id}
                as={b.as ? b.as : Button}
                href={b.destination}
              >
                {b.title}
              </Button>
            );
          })
        ) : (
          <></>
        )}
      </div>
    </div>
  );
};

export default BodySectionTabs;
