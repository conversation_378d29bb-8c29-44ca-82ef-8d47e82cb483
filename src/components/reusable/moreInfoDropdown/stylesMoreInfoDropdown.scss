@import "../../styles/colors.scss";

div.more-info-cell-dropdown {
  position: absolute;
  background-color: $offGrey;
  z-index: 50;
  width: 150px;
  border-radius: 3px;
  border: 1px solid $blue;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.25);
  z-index: 100;

  & .footer-buttons {
    display: flex;
    justify-content: center;

    & div {
      padding: 10px 0;
      width: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 1.1rem;
    }

    & .archive {
      color: #ff9900;
      border-right: 1px solid $dropdownBlue;
    }
    & .delete {
      color: $red;
    }
  }

  & div.more-info-cell-option {
    display: flex;
    align-items: center;
    padding: 5px;
    padding-left: 10px;
    height: 25px;
    color: $lighterSlate;
    border-bottom: 1px solid $dropdownBlue;
    font-size: 0.8rem;
    cursor: pointer;

    & a {
      color: inherit;
      text-decoration: none;
    }

    &:hover {
      color: $blue;
    }

    & button {
      background-color: transparent;
      border: none;
      color: $lighterSlate;
      padding: 0;

      &:hover {
        color: $blue;
        cursor: pointer;
      }
    }
  }
}

div.disabled {
  color: darken($lighterSlate, 10%);
  cursor: default !important;
  pointer-events: none;

  &:hover {
    color: darken($lighterSlate, 10%) !important;
  }
}

div.delete-archive-confirmation-modal {
  background-color: #fff;
  width: 400px;
  padding: 15px;

  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 15px;
  row-gap: 15px;
  justify-items: center;

  & > svg,
  & > p {
    grid-column: 1/-1;
  }

  & > svg {
    font-size: 6rem;

    &.DELETE {
      color: $red;
    }

    &.ARCHIVE {
      color: $yellow;
    }
  }

  & > p span.action {
    font-weight: 600;

    &.DELETE {
      color: $red;
    }

    &.ARCHIVE {
      color: $yellow;
    }
  }

  & > p span.item {
    color: $fabProBlue;
  }

  & > button {
    width: 150px;
    height: 40px;
    padding: 0;
    font-size: 1rem;

    &.cancel {
      background-color: #fff;
      border: 1px solid #333;
      color: #333;

      &:hover {
        background-color: darken(#fff, 10%);
      }
    }

    &.submit {
      color: #fff;
      border: 1px solid #333;

      &.DELETE {
        background-color: $red;

        &:hover {
          background-color: darken($red, 10%);
        }
      }

      &.ARCHIVE {
        background-color: $yellow;

        &:hover {
          background-color: darken($yellow, 10%);
        }
      }
    }
  }
}

svg.stage-completion {
  padding-right: 5px;
  font-size: 1.2rem;
}
