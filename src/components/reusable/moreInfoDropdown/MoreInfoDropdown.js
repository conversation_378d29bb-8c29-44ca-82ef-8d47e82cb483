// NPM PACKAGE IMPORTS
import React, { useRef, useMemo, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FiArchive, FiTrash2 } from "react-icons/fi";
import { RiCheckboxLine, RiCheckboxBlankLine } from "react-icons/ri";

// REDUX IMPORTS
import { handleFetchDrawingFiles } from "./../../files/filesActions";

// HELPER FUNCTION IMPORTS
import useOutsideClick from "../../../hooks/useOutsideClick";
import { downloadFilesV2 } from "../../../_utils";

// STYLES IMPORTS
import "./stylesMoreInfoDropdown.scss";

const itemNames = {
  JOBS: "job_name",
  PACKAGES: "package_name",
  DRAWINGS: "name",
  ITEMS: "id",
  WIZARD_ITEMS: "id",
  DRAWINGS_PENDING_APPROVAL: "name",
  DRAWINGS_SENT_BACK: "name",
};

const MoreInfoDropdown = ({
  moreInfoLocation,
  onOptionClick,
  currentTable,
  rowInfo,
  toggleMoreInfo,
  toggleForgeViewer,
  togglePDFViewer,
  toggleConfirmationModal,
  toggleCompletionModal,
  toggleWorkflowModal,
  toggleRejectionModal,
  toggleAssignmentsModal,
  selectedStages,
  toggleRejectionHistoryModal,
  toggleMaterialsReportModal,
  toggleForgeModelSelector,
  forgeModelsByJob,
  setSubmitRevisionInfo,
  toggleAllInOne,
  toggleManageMAJModal,
  setDisplayedPdf,
}) => {
  const [locationData, setLocationData] = useState({
    top: "",
    left: "",
  });
  const { navExpanded } = useSelector((state) => state.generalData);
  const { permissions, features } = useSelector((state) => state.profileData);
  const { drawingFiles, packageFiles } = useSelector(
    (state) => state.filesData
  );

  const dispatch = useDispatch();

  const dropdownRef = useRef(null);
  useOutsideClick(dropdownRef, (event) => {
    if (
      event.target.nodeName === "svg" &&
      event.target.parentElement.className === "more-info-cell-icon-wrapper"
    )
      return;
    if (
      event.target.nodeName === "path" &&
      event.target.parentElement.nodeName === "svg" &&
      event.target.parentElement.parentElement.className ===
        "more-info-cell-icon-wrapper"
    )
      return;

    toggleMoreInfo(false);
  });

  const maxYCoord = window.innerHeight - 400;

  // const timerLink = useMemo(() => {
  //   if (!rowInfo) return "";
  //   let baseURL = `${process.env.REACT_APP_FABPRO}/analytics/timer_history.php?unit=`;
  //   if (currentTable === "JOBS") return (baseURL += `Job&unitID=${rowInfo.id}`);
  //   else if (currentTable === "PACKAGES")
  //     return (baseURL += `Package&unitID=${rowInfo.id}`);
  //   else return (baseURL += `Spool&unitID=${rowInfo.id}`);
  // }, [currentTable, rowInfo]);

  useEffect(() => {
    if (!moreInfoLocation.x || !moreInfoLocation.y) return;

    if (currentTable === "WIZARD_ITEMS") {
      return setLocationData({
        top: `${moreInfoLocation.y - 150}px`,
        left: `${moreInfoLocation.x - 110}px`,
      });
    }
    if (
      moreInfoLocation.y > 600 &&
      navExpanded &&
      !["PACKAGES", "PACKAGES_PENDING_APPROVAL", "PACKAGES_SENT_BACK"].includes(
        currentTable
      )
    ) {
      setLocationData({
        top: `${maxYCoord - 140}px`,
        left: `${moreInfoLocation.x - 420}px`,
      });
    } else if (moreInfoLocation.y > 600) {
      setLocationData({
        top: `${maxYCoord - 140}px`,
        left: `${moreInfoLocation.x - 160}px`,
      });
    } else if (navExpanded) {
      setLocationData({
        top: `${moreInfoLocation.y - 140}px`,
        left: `${moreInfoLocation.x - 380}px`,
      });
    } else {
      setLocationData({
        top: `${moreInfoLocation.y - 140}px`,
        left: `${moreInfoLocation.x - 160}px`,
      });
    }

    if (
      ["PACKAGES", "PACKAGES_PENDING_APPROVAL", "PACKAGES_SENT_BACK"].includes(
        currentTable
      )
    ) {
      if (moreInfoLocation.y > 600) {
        if (navExpanded) {
          setLocationData({
            top: `${maxYCoord - 160}px`,
            left: `${moreInfoLocation.x - 380}px`,
          });
        } else
          setLocationData({
            top: `${maxYCoord - 160}px`,
            left: `${moreInfoLocation.x - 160}px`,
          });
      } else if (navExpanded) {
        setLocationData({
          top: `${moreInfoLocation.y - 160}px`,
          left: `${moreInfoLocation.x - 380}px`,
        });
      } else
        setLocationData({
          top: `${moreInfoLocation.y - 160}px`,
          left: `${moreInfoLocation.x - 160}px`,
        });
    }
  }, [moreInfoLocation, maxYCoord, navExpanded]);

  const formattedName = useMemo(() => {
    if (
      currentTable === "ITEMS" &&
      rowInfo.material_name &&
      rowInfo.tag_number
    ) {
      return `${rowInfo.material_name} - ${rowInfo.tag_number}`;
    } else if (currentTable === "ITEMS" && rowInfo.material_name) {
      return `${rowInfo.material_name}`;
    } else if (currentTable === "WIZARD_ITEMS" && rowInfo.empty) {
      return `empty row`;
    }
    return `${rowInfo[itemNames[currentTable]]}`;
  }, [currentTable, rowInfo]);

  const viewTimersUnit = useMemo(() => {
    switch (currentTable) {
      case "JOBS":
        return "Job";
      case "PACKAGES":
        return "Package";
      case "DRAWINGS":
        return "Spool";
      default:
        return "";
    }
  }, [currentTable]);

  return (
    <>
      {locationData.top === "" || locationData.left === "" ? (
        <></>
      ) : (
        <div
          style={locationData}
          className="more-info-cell-dropdown"
          ref={dropdownRef}
        >
          {/* <div
          className="more-info-cell-option"
          onClick={() => onOptionClick("MORE_INFO")}
        >
          More Info
        </div> */}
          {["JOBS", "PACKAGES"].includes(currentTable) && (
            <>
              <div className="more-info-cell-option">
                <a
                  href={`${
                    process.env.REACT_APP_FABPRO
                  }/exports/joint-traveler.php${
                    currentTable === "JOBS" ? `?JobID=` : `?PackageID=`
                  }${rowInfo.id}&show-original-or-annotated=original`}
                  target="_blank"
                  rel="noreferrer"
                >{`Joint Log (original)`}</a>
              </div>
              <div className="more-info-cell-option">
                <a
                  href={`${
                    process.env.REACT_APP_FABPRO
                  }/exports/joint-traveler.php${
                    currentTable === "JOBS" ? `?JobID=` : `?PackageID=`
                  }${rowInfo.id}&show-original-or-annotated=annotated`}
                  target="_blank"
                  rel="noreferrer"
                >{`Joint Log (annotated)`}</a>
              </div>
            </>
          )}
          {["DRAWINGS_PENDING_APPROVAL", "DRAWINGS_SENT_BACK"].includes(
            currentTable
          ) && (
            <div
              className="more-info-cell-option"
              onClick={() => {
                toggleAllInOne();
                toggleMoreInfo(false);
                setDisplayedPdf(rowInfo);
              }}
            >
              Manage
            </div>
          )}
          {[
            "DRAWINGS",
            "PACKAGES",
            "PACKAGES_PENDING_APPROVAL",
            "PACKAGES_SENT_BACK",
            "DRAWINGS_PENDING_APPROVAL",
            "DRAWINGS_SENT_BACK",
          ].includes(currentTable) && (
            <>
              {currentTable !== "DRAWINGS_PENDING_APPROVAL" &&
                permissions &&
                permissions.includes(279) &&
                permissions.includes(280) && (
                  <div
                    className="more-info-cell-option"
                    onClick={() => {
                      toggleWorkflowModal(true);
                      toggleMoreInfo(false);
                    }}
                  >
                    Change Workflow
                  </div>
                )}
              <div
                className={
                  rowInfo &&
                  (/PACKAGES/.test(currentTable)
                    ? packageFiles[rowInfo.id || rowInfo.package_id] || null
                    : drawingFiles[rowInfo.id]?.original ||
                      packageFiles[rowInfo?.package_id] ||
                      null)
                    ? "more-info-cell-option"
                    : "disabled more-info-cell-option"
                }
                onClick={() => {
                  togglePDFViewer();
                  toggleMoreInfo(false);
                }}
              >
                PDF
              </div>
            </>
          )}
          <div
            className={
              (currentTable === "JOBS" &&
                forgeModelsByJob &&
                forgeModelsByJob.length) ||
              ([
                "PACKAGES",
                "PACKAGES_PENDING_APPROVAL",
                "PACKAGES_SENT_BACK",
              ].includes(currentTable) &&
                rowInfo &&
                rowInfo.forge_models &&
                JSON.parse(rowInfo.forge_models).length > 0) ||
              (rowInfo && rowInfo.forge_urn)
                ? "more-info-cell-option"
                : "disabled more-info-cell-option"
            }
            onClick={() => {
              if (
                [
                  "JOBS",
                  "PACKAGES",
                  "PACKAGES_PENDING_APPROVAL",
                  "PACKAGES_SENT_BACK",
                ].includes(currentTable)
              ) {
                toggleForgeModelSelector();
                toggleMoreInfo(false);
              } else if (rowInfo && rowInfo.forge_urn) {
                toggleForgeViewer();
                toggleMoreInfo(false);
              }
            }}
          >
            3D Model
          </div>
          {currentTable === "PACKAGES" && rowInfo && (
            <>
              <div className="more-info-cell-option">
                <a
                  href={`${process.env.REACT_APP_FABPRO}/exports/view_all_drawings.php?PackageID=${rowInfo.id}&archived=0`}
                  target="_blank"
                  rel="noreferrer"
                >
                  View all drawings
                </a>
              </div>
              <div
                onClick={() => onOptionClick("MATERIALS_REJECTION_REPORT")}
                className="more-info-cell-option"
              >
                Rejected Materials
              </div>
            </>
          )}
          {![
            "ITEMS",
            "WIZARD_ITEMS",
            "PACKAGES_PENDING_APPROVAL",
            "PACKAGES_SENT_BACK",
            "DRAWINGS_PENDING_APPROVAL",
            "DRAWINGS_SENT_BACK",
          ].includes(currentTable) &&
            rowInfo &&
            permissions &&
            permissions.includes(19) && (
              <div
                className="more-info-cell-option"
                onClick={() =>
                  window.location.assign(
                    `${process.env.REACT_APP_FABPRO}/analytics/timer_history.php?unit=${viewTimersUnit}&unitID=${rowInfo.id}`
                  )
                }
              >
                Work History
              </div>
            )}
          {[
            "JOBS",
            "PACKAGES",
            "PACKAGES_PENDING_APPROVAL",
            "PACKAGES_SENT_BACK",
            "DRAWINGS",
            "DRAWINGS_PENDING_APPROVAL",
            "DRAWINGS_SENT_BACK",
          ].includes(currentTable) && (
            <>
              {![
                "DRAWINGS_PENDING_APPROVAL",
                "DRAWINGS_SENT_BACK",
                "PACKAGES_PENDING_APPROVAL",
                "PACKAGES_SENT_BACK",
              ].includes(currentTable) && (
                <div
                  onClick={() => onOptionClick("VIEW_BOM")}
                  className="more-info-cell-option"
                >
                  View BOM
                </div>
              )}
              {permissions && permissions.includes(28) && (
                <div
                  onClick={() => onOptionClick("VIEW_ASSIGNMENTS")}
                  className="more-info-cell-option"
                >
                  Assignments
                </div>
              )}
            </>
          )}
          {currentTable === "ITEMS" &&
            rowInfo &&
            !rowInfo.drawing_pending_approval &&
            Object.prototype.hasOwnProperty.call(rowInfo, "completed") &&
            permissions &&
            permissions.includes(146) && (
              <div
                className="more-info-cell-option"
                onClick={() => {
                  toggleMoreInfo(false);
                  toggleCompletionModal(true);
                }}
              >
                {rowInfo.completed === 1 ? (
                  <RiCheckboxLine className="stage-completion" />
                ) : (
                  <RiCheckboxBlankLine className="stage-completion" />
                )}
                Complete
              </div>
            )}
          {currentTable === "ITEMS" &&
            selectedStages.length === 1 &&
            selectedStages[0].rejectable === 1 &&
            rowInfo &&
            permissions &&
            permissions.includes(94) && (
              <div
                className="more-info-cell-option"
                onClick={() => {
                  toggleMoreInfo(false);
                  toggleRejectionModal(true);
                }}
              >
                Reject
              </div>
            )}
          {currentTable === "ITEMS" && (
            <div
              className="more-info-cell-option"
              onClick={() => {
                toggleMoreInfo(false);
                toggleRejectionHistoryModal(true);
              }}
            >
              Rejection History
            </div>
          )}
          {[
            "DRAWINGS",
            "DRAWINGS_PENDING_APPROVAL",
            "DRAWINGS_SENT_BACK",
          ].includes(currentTable) && (
            <div
              className="more-info-cell-option"
              onClick={() => setSubmitRevisionInfo(rowInfo)}
            >
              Submit a Revised PDF
            </div>
          )}
          {permissions &&
            permissions.includes(302) &&
            features &&
            features.includes(41) && (
              <>
                {[
                  "PACKAGES",
                  "PACKAGES_PENDING_APPROVAL",
                  "PACKAGES_SENT_BACK",
                  "DRAWINGS",
                  "DRAWINGS_PENDING_APPROVAL",
                  "DRAWINGS_SENT_BACK",
                ].includes(currentTable) && (
                  <div
                    className="more-info-cell-option"
                    onClick={() => {
                      toggleManageMAJModal();
                      toggleMoreInfo(false);
                    }}
                  >
                    Manage MAJ(s)
                  </div>
                )}
                {[
                  "DRAWINGS",
                  "DRAWINGS_PENDING_APPROVAL",
                  "DRAWINGS_SENT_BACK",
                ].includes(currentTable) && (
                  <div
                    className={`more-info-cell-option ${
                      rowInfo.has_maj ? "" : "disabled"
                    }`}
                    onClick={() => {
                      if (!rowInfo.has_maj) return;
                      // repull ALL the maj drawing urls in case the maj has been updated since the last call
                      // these urls aren't static like the drawing pdfs...
                      return dispatch(
                        handleFetchDrawingFiles(rowInfo.id, "maj")
                      ).then((res) => {
                        if (!res.error) {
                          // merge the entries with their maj files for download...
                          let files = [
                            { ...rowInfo, maj_file: res[rowInfo.id]?.maj },
                          ];
                          // downloads with the same file name as was uploaded...
                          downloadFilesV2(files, "maj_file");
                          toggleMoreInfo(false);
                        }
                      });
                    }}
                  >
                    Download MAJ
                  </div>
                )}
              </>
            )}
          {currentTable === "WIZARD_ITEMS" && (
            <div
              style={locationData}
              className="more-info-dropdown"
              ref={dropdownRef}
            >
              <div
                onClick={() => {
                  toggleMoreInfo(false);
                  toggleConfirmationModal({
                    action: "DELETE",
                    item: formattedName,
                  });
                }}
                className="more-info-cell-option"
              >
                Delete
              </div>
            </div>
          )}
          {currentTable !== "WIZARD_ITEMS" && (
            <div className="footer-buttons">
              {![
                "ITEMS",
                "DRAWINGS",
                "PACKAGES_PENDING_APPROVAL",
                "PACKAGES_SENT_BACK",
                "DRAWINGS_PENDING_APPROVAL",
                "DRAWINGS_SENT_BACK",
              ].includes(currentTable) &&
                permissions &&
                permissions.includes(101) && (
                  <div
                    onClick={() =>
                      toggleConfirmationModal({
                        action: "ARCHIVE",
                        item: formattedName,
                      })
                    }
                    className="archive"
                  >
                    <FiArchive />
                  </div>
                )}
              {permissions &&
                ((currentTable === "JOBS" && permissions.includes(22)) ||
                  ([
                    "PACKAGES",
                    "DRAWINGS",
                    "ITEMS",
                    "DRAWINGS_PENDING_APPROVAL",
                    "DRAWINGS_SENT_BACK",
                  ].includes(currentTable) &&
                    permissions.includes(23))) && (
                  <div
                    onClick={() =>
                      toggleConfirmationModal({
                        action: "DELETE",
                        item: formattedName,
                        id: rowInfo.id,
                      })
                    }
                    className="delete"
                  >
                    <FiTrash2 />
                  </div>
                )}
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default MoreInfoDropdown;
