// NPM PACKAGE IMPORTS
import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockAdapter from "axios-mock-adapter";
import thunk from "redux-thunk";

// REDUX IMPORTS
import {
  // ACTION CREATORS
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  // ACTION HANDLERS
  handleCleanup,
  handleFetchDrawingInfo,
  handleCreateNewSheet,
  handleSavePdfAnnotations,
} from "./cloudSheetCreationActions";

describe("CLOUD SHEET CREATION", () => {
  let store, httpMock;

  const testError = (mes) => ({
    error: { status: 404, message: mes },
  });

  beforeEach(() => {
    httpMock = new MockAdapter(axios);
    const mockStore = configureMockStore([thunk]);
    store = mockStore();
  });

  describe("handleCleanup", () => {
    it("should clear set state", () => {
      store.dispatch(handleCleanup);

      const expectedActions = [
          receiveSucceeded("SHEET_CREATION_DRAWING_INFO", null),
        ],
        receivedActions = store.getActions();

      expect(receivedActions).toEqual(expectedActions);
    });
  });

  describe("handleFetchDrawingInfo", () => {
    it("should fetch info for the specified drawing", async () => {
      httpMock
        .onGet("drawings/1")
        .replyOnce(200, [{ id: 1 }])
        .onGet("drawings/1")
        .replyOnce(404, testError("Drawing not found"));

      const type = "SHEET_CREATION_DRAWING_INFO";
      let response = await store.dispatch(handleFetchDrawingInfo(1));

      let expectedActions = [
          receiveStarted(type),
          receiveSucceeded(type, { id: 1 }),
        ],
        receivedActions = store.getActions();

      expect(response).toEqual([{ id: 1 }]);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleFetchDrawingInfo(1));

      expectedActions = [
        receiveStarted(type),
        receiveFailed(type, testError("Drawing not found").error),
      ];
      receivedActions = store.getActions();

      expect(response).toEqual(testError("Drawing not found"));
      expect(receivedActions).toEqual(expectedActions);
    });
  });

  describe("handleCreateNewSheet", () => {
    it("should create a sheet for the specified drawing", async () => {
      httpMock.onPut("drawings/new-pdf").replyOnce(200, [{ id: 1 }]);

      let testBody = new FormData();
      testBody.append("package_id", 1);
      testBody.append("drawing_id", 1);

      let response = await store.dispatch(handleCreateNewSheet(testBody));

      let expectedActions = [],
        receivedActions = store.getActions();

      expect(response).toEqual([{ id: 1 }]);
      expect(httpMock.history.put[0].data).toEqual(testBody);
      expect(receivedActions).toEqual(expectedActions);
    });
  });

  describe("handleSavePdfAnnotations", () => {
    it("should save original annotations for the specified drawing pdf", async () => {
      httpMock.onPost("pdf-annotations").replyOnce(200, [{ id: 1 }]);

      let testBody = new FormData();
      testBody.append("package_id", 1);
      testBody.append("drawing_id", 1);
      testBody.append("annotation_type", "original");

      let response = await store.dispatch(handleSavePdfAnnotations(testBody));

      let expectedActions = [],
        receivedActions = store.getActions();

      expect(response).toEqual([{ id: 1 }]);
      expect(httpMock.history.post[0].data).toEqual(testBody);
      expect(receivedActions).toEqual(expectedActions);
    });
  });
});
