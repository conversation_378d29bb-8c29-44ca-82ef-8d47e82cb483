import React, { useEffect, useState, useRef, useMemo } from "react";

// import LOGO from "../../../../assets/lee_logo.png";
// import LOGO from "../../../../assets/logo.png";

const Default = ({
  createTableAnnotation,
  createImageAnnotation,
  whiteToTransparent,
  removeBlanks,
  instance,
  cloudSheetSpoolImage,
  cloudSheetItems,
  drawingInfo,
  userInfo,
  systemSettings,
}) => {
  const [pageDimensions, setPageDimensions] = useState(null);
  const [logoDimensions, setLogoDimensions] = useState(null);
  const [itemListHeight, setItemListHeight] = useState(null);

  const titleBlockRef = useRef(null);
  const itemListRef = useRef(null);
  const itemListRefScalar = useMemo(() => 0.7, []);

  useEffect(() => {
    if (instance) {
      const pageInfo = instance.pageInfoForIndex(0);
      setPageDimensions({ w: pageInfo.width, h: pageInfo.height });

      return async () => {
        const pagesAnnotations = await Promise.all(
          Array.from({ length: instance.totalPageCount }).map((_, pageIndex) =>
            instance.getAnnotations(pageIndex)
          )
        );
        const annotationIds = pagesAnnotations.flatMap((pageAnnotations) =>
          pageAnnotations.map((annotation) => annotation.id).toArray()
        );
        await instance.delete(annotationIds);
      };
    }
  }, [instance]);

  useEffect(() => {
    if (
      instance &&
      pageDimensions &&
      drawingInfo &&
      userInfo &&
      logoDimensions
    ) {
      createTableAnnotation(
        titleBlockRef.current,
        {
          top: pageDimensions.h - titleBlockRef.current?.clientHeight,
          left: 0,
        },
        {
          w: titleBlockRef.current?.clientWidth,
          h: titleBlockRef.current?.clientHeight,
          pixelDensity: 3,
          scale: 1,
        },
        "Title Block"
      );
    }
  }, [instance, pageDimensions, drawingInfo, userInfo, logoDimensions]);

  useEffect(() => {
    if (instance && pageDimensions) {
      const height =
        itemListRef.current.clientHeight +
        (cloudSheetItems.length + 2) * 10 +
        44; // plus padding
      createTableAnnotation(
        itemListRef.current,
        null,
        {
          w: itemListRef.current.clientWidth,
          h: height,
          pixelDensity: 3,
          scale: itemListRefScalar,
        },
        "Item List"
      );
      setItemListHeight(height);
    }
  }, [instance, pageDimensions]);

  useEffect(() => {
    if (
      instance &&
      cloudSheetSpoolImage &&
      pageDimensions &&
      drawingInfo &&
      userInfo &&
      logoDimensions
    ) {
      (async function () {
        const img = new Image();
        img.onload = async () => {
          const croppedPng = await removeBlanks(img);

          const croppedImg = new Image();
          croppedImg.onload = async () => {
            const transparentPng = await whiteToTransparent(croppedImg);
            let height = croppedImg.height,
              width = croppedImg.width,
              top =
                (itemListHeight ?? itemListRef.current.clientHeight) *
                  itemListRefScalar +
                10,
              left = 20,
              verticalWhiteSpace =
                pageDimensions.h -
                (itemListHeight ?? itemListRef.current.clientHeight) *
                  itemListRefScalar -
                titleBlockRef.current.clientHeight -
                20,
              horizontalWhiteSpace =
                pageDimensions.w -
                itemListRef.current.clientWidth * itemListRefScalar -
                20;

            if (width >= height && verticalWhiteSpace >= 250) {
              // if width is > page width, go based on page width
              // and adjust height to scale
              if (width > pageDimensions.w - 20) {
                left = 10;
                width = pageDimensions.w - 20;
                height *= width / croppedImg.width;

                if (height > verticalWhiteSpace) {
                  // use the height between item list and titleblock, scale width
                  height = verticalWhiteSpace;
                  width *= height / croppedImg.height;
                }
              } else {
                // use the height between item list and titleblock, scale width
                height = verticalWhiteSpace;
                width *= height / croppedImg.height;
              }
            } else {
              top = 10;
              left = itemListRef.current.clientWidth * itemListRefScalar + 10;

              width = horizontalWhiteSpace;
              height *= width / croppedImg.width;

              if (
                height >
                pageDimensions.h - titleBlockRef.current.clientHeight - 20
              ) {
                height =
                  croppedImg.height *
                  ((pageDimensions.h -
                    titleBlockRef.current.clientHeight -
                    20) /
                    croppedImg.height);
                width = croppedImg.width * (height / croppedImg.height);
              }
            }
            const dimensionsToUse = {
              width,
              height,
            };
            await createImageAnnotation(
              transparentPng,
              {
                top,
                left,
              },
              dimensionsToUse,
              "Spool Image"
            );
          };
          croppedImg.src = croppedPng;
        };
        img.src = cloudSheetSpoolImage;
      })();
    }
  }, [
    instance,
    cloudSheetSpoolImage,
    pageDimensions,
    drawingInfo,
    userInfo,
    logoDimensions,
  ]);

  useEffect(() => {
    if (pageDimensions) {
      const img = new Image();
      img.onload = function () {
        let w, h;

        w = img.width;
        h = img.height;

        setLogoDimensions({ w: w * (50 / h), h: 50 });
      };
      // in case client doesn't have a logo file, set default
      // to trigger useEffect to generate titleblock
      img.onerror = function () {
        setLogoDimensions({ w: 50, h: 50 });
      };

      img.src = `${process.env.REACT_APP_FABPRO}/companies/${systemSettings.domain_prefix}/${systemSettings.logo}`;
    }
  }, [pageDimensions]);

  return (
    <>
      {pageDimensions && drawingInfo && userInfo && (
        <div
          className="sheet-table-container"
          style={{ position: "fixed", top: -1000, left: -1000 }}
        >
          <table ref={titleBlockRef}>
            <tbody>
              <tr>
                <td
                  rowSpan={3}
                  style={{
                    width: pageDimensions.w / 4 - 14,
                    padding: 0,
                    borderBottom: "1px solid black",
                  }}
                >
                  <img
                    src={`${process.env.REACT_APP_FABPRO}/companies/${systemSettings.domain_prefix}/${systemSettings.logo}`}
                    width={
                      logoDimensions ? logoDimensions.w : pageDimensions.w / 4
                    }
                    height={logoDimensions ? logoDimensions.h : 50}
                    alt="company logo"
                  />
                </td>
                <td
                  style={{
                    width: pageDimensions.w / 4 - 14,
                  }}
                >
                  Job Number: {drawingInfo.job_number}
                </td>
                <td
                  style={{
                    width: pageDimensions.w / 4 - 14,
                  }}
                >
                  Spool Name: {drawingInfo.name}
                </td>
                <td
                  style={{
                    width: pageDimensions.w / 4 - 14,
                  }}
                >
                  Drawn By:{" "}
                  {userInfo.first_name +
                    (userInfo.last_name ? " " + userInfo.last_name : "")}
                </td>
              </tr>
              <tr>
                <td
                  style={{
                    width: pageDimensions.w / 4 - 14,
                  }}
                >
                  Job Name: {drawingInfo.job_title}
                </td>
                <td
                  style={{
                    width: pageDimensions.w / 4 - 14,
                  }}
                >
                  Package Name: {drawingInfo.package_name}
                </td>
                <td
                  style={{
                    width: pageDimensions.w / 4 - 14,
                  }}
                >
                  Plot Date: {new Date().toLocaleString()}
                </td>
              </tr>
              <tr>
                <td
                  style={{
                    width: pageDimensions.w / 4 - 14,
                    height: "20px",
                  }}
                ></td>
                <td
                  style={{
                    width: pageDimensions.w / 4 - 14,
                    height: "20px",
                  }}
                ></td>
                <td
                  style={{
                    width: pageDimensions.w / 4 - 14,
                    height: "20px",
                  }}
                ></td>
              </tr>
            </tbody>
          </table>
        </div>
      )}
      <div
        className="sheet-table-container"
        style={{ position: "fixed", top: -1500, left: -1500 }}
      >
        <table ref={itemListRef}>
          <thead>
            <tr>
              <td
                colSpan={9}
                style={{
                  textAlign: "center",
                  fontSize: "1.2rem",
                  fontWeight: 600,
                }}
              >
                Item List
              </td>
            </tr>
            <tr
              style={{ borderBottom: "1px solid black", textAlign: "center" }}
            >
              <td
                style={{
                  textAlign: "center",
                  maxWidth: pageDimensions ? pageDimensions.w / 24 : 0,
                  minWidth: pageDimensions ? pageDimensions.w / 24 : 0,
                }}
              >
                Tag #
              </td>
              <td
                style={{
                  textAlign: "center",
                  maxWidth: pageDimensions ? pageDimensions.w / 24 : 0,
                  minWidth: pageDimensions ? pageDimensions.w / 24 : 0,
                }}
              >
                Qty
              </td>
              <td
                style={{
                  textAlign: "center",
                  maxWidth: pageDimensions ? pageDimensions.w / 16 : 0,
                  minWidth: pageDimensions ? pageDimensions.w / 16 : 0,
                }}
              >
                Size
              </td>
              <td
                style={{
                  maxWidth: pageDimensions ? pageDimensions.w / 6 : 0,
                  minWidth: pageDimensions ? pageDimensions.w / 6 : 0,
                }}
              >
                Description
              </td>
              <td
                style={{
                  textAlign: "center",
                  maxWidth: pageDimensions ? pageDimensions.w / 16 : 0,
                  minWidth: pageDimensions ? pageDimensions.w / 16 : 0,
                }}
              >
                Weight (ea)
              </td>
              <td
                style={{
                  textAlign: "center",
                  maxWidth: pageDimensions ? pageDimensions.w / 10 : 0,
                  minWidth: pageDimensions ? pageDimensions.w / 10 : 0,
                }}
              >
                Material
              </td>
              <td
                style={{
                  textAlign: "center",
                  maxWidth: pageDimensions ? pageDimensions.w / 18 : 0,
                  minWidth: pageDimensions ? pageDimensions.w / 18 : 0,
                }}
              >
                Length
              </td>
              <td
                style={{
                  maxWidth: pageDimensions ? pageDimensions.w / 9 : 0,
                  minWidth: pageDimensions ? pageDimensions.w / 9 : 0,
                }}
              >
                End #1
              </td>
              <td
                style={{
                  maxWidth: pageDimensions ? pageDimensions.w / 9 : 0,
                  minWidth: pageDimensions ? pageDimensions.w / 9 : 0,
                }}
              >
                End #2
              </td>
            </tr>
          </thead>
          <tbody>
            {cloudSheetItems.map((m) => {
              return (
                <tr key={m.id}>
                  <td
                    style={{
                      textAlign: "center",
                      maxWidth: pageDimensions ? pageDimensions.w / 24 : 0,
                      minWidth: pageDimensions ? pageDimensions.w / 24 : 0,
                    }}
                  >
                    {m.tag_number}
                  </td>
                  <td
                    style={{
                      textAlign: "center",
                      maxWidth: pageDimensions ? pageDimensions.w / 24 : 0,
                      minWidth: pageDimensions ? pageDimensions.w / 24 : 0,
                    }}
                  >
                    {m.quantity}
                  </td>
                  <td
                    style={{
                      textAlign: "center",
                      maxWidth: pageDimensions ? pageDimensions.w / 18 : 0,
                      minWidth: pageDimensions ? pageDimensions.w / 18 : 0,
                    }}
                  >
                    {m.size}
                  </td>
                  <td
                    style={{
                      maxWidth: pageDimensions ? pageDimensions.w / 6 : 0,
                      minWidth: pageDimensions ? pageDimensions.w / 6 : 0,
                    }}
                  >
                    {m.description}
                  </td>
                  <td
                    style={{
                      textAlign: "center",
                      maxWidth: pageDimensions ? pageDimensions.w / 18 : 0,
                      minWidth: pageDimensions ? pageDimensions.w / 18 : 0,
                    }}
                  >
                    {m.weight}
                  </td>
                  <td
                    style={{
                      textAlign: "center",
                      maxWidth: pageDimensions ? pageDimensions.w / 12 : 0,
                      minWidth: pageDimensions ? pageDimensions.w / 12 : 0,
                    }}
                  >
                    {m.material_name}
                  </td>
                  <td
                    style={{
                      textAlign: "center",
                      maxWidth: pageDimensions ? pageDimensions.w / 18 : 0,
                      minWidth: pageDimensions ? pageDimensions.w / 18 : 0,
                    }}
                  >
                    {m.length}
                  </td>
                  <td
                    style={{
                      maxWidth: pageDimensions ? pageDimensions.w / 9 : 0,
                      minWidth: pageDimensions ? pageDimensions.w / 9 : 0,
                    }}
                  >
                    {m.end_prep_1}
                  </td>
                  <td
                    style={{
                      maxWidth: pageDimensions ? pageDimensions.w / 9 : 0,
                      minWidth: pageDimensions ? pageDimensions.w / 9 : 0,
                    }}
                  >
                    {m.end_prep_2}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </>
  );
};

export default Default;
