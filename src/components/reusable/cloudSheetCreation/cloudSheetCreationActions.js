import {
  fetchDrawingById,
  createNewSheet,
  savePdfAnnotations,
} from "../../../_services";

export const receiveStarted = (type) => ({
  type: `RECEIVE_${type}_STARTED`,
});
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const handleCleanup = (dispatch) => {
  dispatch(receiveSucceeded("SHEET_CREATION_DRAWING_INFO", null));
};

export const handleFetchDrawingInfo = (drawingId) => (dispatch) => {
  const type = "SHEET_CREATION_DRAWING_INFO";

  dispatch(receiveStarted(type));
  return fetchDrawingById(drawingId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res[0]));

    return res;
  });
};

export const handleCreateNewSheet = (multipartFormData) => (dispatch) => {
  return createNewSheet(multipartFormData).then((res) => {
    return res;
  });
};

export const handleSavePdfAnnotations = (multipartFormData) => (dispatch) => {
  return savePdfAnnotations(multipartFormData);
};
