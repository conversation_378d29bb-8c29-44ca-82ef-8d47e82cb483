const initialState = {
  isLoading: false,
  error: null,
  drawingInfo: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_SHEET_CREATION_DRAWING_INFO_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_SHEET_CREATION_DRAWING_INFO_SUCCEEDED":
      return { ...state, drawingInfo: payload, error: null, isLoading: false };
    case "RECEIVE_SHEET_CREATION_DRAWING_INFO_FAILED":
      return { ...state, drawingInfo: null, error: payload, isLoading: false };
    default:
      return state;
  }
}
