@import "../../styles/colors.scss";

div.cloud-sheet-creation-container {
  display: flex;
}

div.cloud-sheet-creation-container > div.template-selector {
  background-color: $darkGrey;
  padding: 20px;

  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

div.cloud-sheet-creation-container > div.template-selector > div.template-list {
  display: flex;
  flex-direction: column;
  row-gap: 10px;

  & > h2 {
    color: #fff;
    margin: 0;
    padding: 0;
  }

  & > button {
    color: #fff;
    background-color: $grey;
    border: 1px solid black;
    padding: 0;
    font-size: 1rem;

    &:not(:disabled):hover {
      background-color: $fabProBlue;
    }

    &:disabled {
      background-color: $fabProBlue;
    }
  }
}

div.cloud-sheet-creation-container
  > div.template-selector
  > div.action-buttons {
  display: flex;
  flex-direction: column;
  row-gap: 10px;

  & > button {
    border: 1px solid black;
    padding: 0;
    font-size: 1rem;
  }

  & > button:first-of-type {
    color: #fff;
    background-color: $fabProBlue;

    &:not(:disabled):hover {
      background-color: darken($fabProBlue, 10%);
    }
  }

  & > button:last-of-type {
    color: #333;
    background-color: #fff;

    &:hover {
      background-color: darken(#fff, 10%);
    }
  }
}

div.sheet-table-container {
  & table {
    border-collapse: collapse;
  }

  & tr {
    min-height: 40px;
  }

  & tr > td {
    border-top: 1px solid black;
  }

  & tr:last-of-type > td {
    border-bottom: 1px solid black;
  }

  & td {
    border-left: 1px solid black;
    padding: 5px;
    word-wrap: break-word;
    font-size: 0.7rem;
  }

  & td:last-child {
    border-right: 1px solid black;
  }

  & td.small {
    min-width: 40px;
    max-width: 40px;
  }

  & td.medium {
    min-width: 60px;
    max-width: 60px;
  }

  & td.large {
    min-width: 75px;
    max-width: 75px;
  }

  & td.extra-large {
    min-width: 150px;
    max-width: 150px;
  }
}
