// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useRef, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import html2canvas from "html2canvas";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import { notify } from "./../alertPopup/alertPopupActions";

// COMPONENT IMPORTS
import Default from "./templates/Default";

// REDUX IMPORTS
import {
  handleCleanup,
  handleCreateNewSheet,
  handleFetchDrawingInfo,
  handleSavePdfAnnotations,
} from "./cloudSheetCreationActions";

// HELPER FUNCTION IMPORTS
import { b64toBlob } from "../../../_utils";

// STYLE IMPORTS
import "./stylesCloudSheetCreation.scss";

const CloudSheetCreation = ({
  open,
  handleClose,
  cloudSheetSpoolImage,
  cloudSheetItems,
  drawingsGridOptionsApi,
  setCurrentRowOrder,
  sheetCallback,
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState("DEFAULT");
  const [PSPDFKit, setPSPDFKit] = useState(null);
  const [instance, setInstance] = useState(null);

  const dispatch = useDispatch();
  const { drawingInfo } = useSelector((state) => state.cloudSheetCreationData);
  const { userInfo, systemSettings } = useSelector(
    (state) => state.profileData
  );

  const containerRef = useRef(null);

  useEffect(() => {
    const container = containerRef.current;
    let PSPDFKit;
    (async function () {
      PSPDFKit = await import("pspdfkit");
      setPSPDFKit(PSPDFKit);
      if (instance) PSPDFKit.unload(instance);
      try {
        setInstance(
          await PSPDFKit.load({
            // Container where PSPDFKit should be mounted.
            container,
            licenseKey:
              process.env.REACT_APP_PROD_PSPDFKIT_LICENSE ||
              process.env.REACT_APP_DEV_PSPDFKIT_LICENSE,
            // The document to open.
            document: "11x17.pdf",
            // Use the public directory URL as a base URL. PSPDFKit will download its library assets from here.
            baseUrl: `${window.location.protocol}//${window.location.host}/`,
            // below removes double click to select text options
            // https://pspdfkit.com/guides/web/user-interface/inline-text-selection-toolbar/remove-all-tools/
            inlineTextSelectionToolbarItems: () => {
              return [];
            },
          })
        );
      } catch (e) {
        console.error(e);
      }
    })();

    return () => PSPDFKit && PSPDFKit.unload(container);
  }, []);

  useEffect(() => {
    if (cloudSheetItems && cloudSheetItems.length) {
      // Example of selection returned null vs a populated drawing_id.
      const items = cloudSheetItems.filter((i) => i.drawing_id != null);
      const drawingId =
        [...new Set([...items.map((i) => i.drawing_id)])][0] ?? undefined;
      // If somehow we get bad data, we want to throw an error instead of trying to call a API without a valid ID
      if (!drawingId) {
        dispatch(
          notify({
            id: Date.now(),
            type: "ERROR",
            message: "No valid drawing to create sheet.",
          })
        );
        // close viewer
        cleanupViewer();
      } else {
        dispatch(handleFetchDrawingInfo(drawingId));
      }
    }
  }, [cloudSheetItems]);

  const createTableAnnotation = (
    tableRef,
    defaultOriginPoint = { left: 0, top: 0 },
    defaultDimensions = { w: 0, h: 0, pixelDensity: 1, scale: 1 },
    tableName = ""
  ) => {
    html2canvas(tableRef, {
      width: defaultDimensions.w,
      height: defaultDimensions.h,
      scale: defaultDimensions.pixelDensity,
    }).then((canvas) => {
      canvas.toBlob(
        async (blob) => {
          const imageAttachmentId = await instance.createAttachment(blob);

          const annotation = new PSPDFKit.Annotations.ImageAnnotation({
            pageIndex: 0,
            contentType: "image/jpeg",
            imageAttachmentId,
            description: tableName,
            boundingBox: new PSPDFKit.Geometry.Rect({
              ...defaultOriginPoint,
              width: defaultDimensions.w,
              height: defaultDimensions.h,
            }).scale(defaultDimensions.scale),
          });
          instance.create(annotation);
        },
        "image/jpeg",
        1.0
      );
    });
  };

  const createImageAnnotation = async (
    b64Image,
    defaultOriginPoint = { left: 0, top: 0 },
    defaultDimensions = { width: 0, height: 0 },
    imageName = ""
  ) => {
    const blob = await b64toBlob(b64Image, "image/png");
    const imageAttachmentId = await instance.createAttachment(blob);

    const annotation = new PSPDFKit.Annotations.ImageAnnotation({
      pageIndex: 0,
      contentType: "image/png",
      imageAttachmentId,
      description: imageName,
      boundingBox: new PSPDFKit.Geometry.Rect({
        ...defaultOriginPoint,
        ...defaultDimensions,
      }),
    });
    instance.create(annotation);
  };

  // http://jsfiddle.net/ruisoftware/ddZfV/7/
  async function removeBlanks(img) {
    const canvas = document.createElement("canvas"),
      context = canvas.getContext("2d"),
      imgWidth = img.width,
      imgHeight = img.height;

    canvas.width = imgWidth;
    canvas.height = imgHeight;

    context.drawImage(img, 0, 0, imgWidth, imgHeight);

    var imageData = context.getImageData(0, 0, imgWidth, imgHeight),
      data = imageData.data,
      getRBG = function (x, y) {
        var offset = imgWidth * y + x;
        return {
          red: data[offset * 4],
          green: data[offset * 4 + 1],
          blue: data[offset * 4 + 2],
          opacity: data[offset * 4 + 3],
        };
      },
      isWhite = function (rgb) {
        // many images contain noise, as the white is not a pure #fff white
        return rgb.red > 200 && rgb.green > 200 && rgb.blue > 200;
      },
      scanY = function (fromTop) {
        var offset = fromTop ? 1 : -1;

        // loop through each row
        for (
          var y = fromTop ? 0 : imgHeight - 1;
          fromTop ? y < imgHeight : y > -1;
          y += offset
        ) {
          // loop through each column
          for (var x = 0; x < imgWidth; x++) {
            var rgb = getRBG(x, y);
            if (!isWhite(rgb)) {
              return y;
            }
          }
        }
        return null; // all image is white
      },
      scanX = function (fromLeft) {
        var offset = fromLeft ? 1 : -1;

        // loop through each column
        for (
          var x = fromLeft ? 0 : imgWidth - 1;
          fromLeft ? x < imgWidth : x > -1;
          x += offset
        ) {
          // loop through each row
          for (var y = 0; y < imgHeight; y++) {
            var rgb = getRBG(x, y);
            if (!isWhite(rgb)) {
              return x;
            }
          }
        }
        return null; // all image is white
      };

    var cropTop = scanY(true),
      cropBottom = scanY(false),
      cropLeft = scanX(true),
      cropRight = scanX(false),
      cropWidth = cropRight - cropLeft,
      cropHeight = cropBottom - cropTop;

    const croppedCanvas = document.createElement("canvas");

    croppedCanvas.width = cropWidth;
    croppedCanvas.height = cropHeight;

    // finally crop the guy
    croppedCanvas
      .getContext("2d")
      .drawImage(
        canvas,
        cropLeft,
        cropTop,
        cropWidth,
        cropHeight,
        0,
        0,
        cropWidth,
        cropHeight
      );

    return croppedCanvas.toDataURL("image/png");
  }

  async function whiteToTransparent(img) {
    var c = document.createElement("canvas");

    var w = img.width,
      h = img.height;

    c.width = w;
    c.height = h;

    var ctx = c.getContext("2d");

    ctx.drawImage(img, 0, 0, w, h);
    var imageData = ctx.getImageData(0, 0, w, h);
    var pixel = imageData.data;

    var r = 0,
      g = 1,
      b = 2,
      a = 3;
    for (var p = 0; p < pixel.length; p += 4) {
      // eslint-disable-next-line eqeqeq
      if (pixel[p + r] == 255 && pixel[p + g] == 255 && pixel[p + b] == 255) {
        // if white then change alpha to 0
        pixel[p + a] = 0;
      }
    }

    ctx.putImageData(imageData, 0, 0);
    return c.toDataURL("image/png");
  }

  const saveSheet = useCallback(async () => {
    if (!PSPDFKit || !instance || !drawingInfo) return;

    // LIST OF ANNOTATIONS
    const instantJSON = await instance.exportInstantJSON();

    if (!instantJSON.annotations) instantJSON.annotations = [];

    instantJSON.pdfId = null;

    // BINARY INFORMATION TO SAVE FILE
    const pdfBuffer = await instance.exportPDF({ flatten: true });
    const pdfBlob = new Blob([pdfBuffer], { type: "application/pdf" });

    // NEW SHEET FORM DATA EXPECTED BY API
    let newSheetFormData = new FormData();
    newSheetFormData.append(
      "drawings",
      pdfBlob,
      drawingInfo.original_name + ".pdf"
    );
    newSheetFormData.append("drawing_id", drawingInfo.id);
    newSheetFormData.append("package_id", drawingInfo.package_id);

    // ANNOTATIONS FORM DATA EXPECTED BY API
    let annotationsFormData = new FormData();
    annotationsFormData.append("annotations", JSON.stringify(instantJSON));
    annotationsFormData.append("drawing_id", drawingInfo.id);
    annotationsFormData.append("annotation_type", "original");

    // PERFORM SAVE
    await dispatch(handleCreateNewSheet(newSheetFormData)).then(async (res) => {
      await dispatch(handleSavePdfAnnotations(annotationsFormData));
      await dispatch(handleFetchDrawingInfo(drawingInfo.id)).then((res) => {
        if (!res.error) {
          // update forge viewer drawing reference
          sheetCallback(res[0]);
          if (drawingsGridOptionsApi) {
            const rowNode = drawingsGridOptionsApi.getRowNode(drawingInfo.id);
            if (rowNode) {
              rowNode.setData({ ...rowNode.data, ...res[0] });

              const newRowOrder = [];
              drawingsGridOptionsApi.forEachNodeAfterFilterAndSort((n) => {
                newRowOrder.push(n.data);
              });
              setCurrentRowOrder(newRowOrder);

              drawingsGridOptionsApi.refreshCells({ rowNodes: [rowNode] });
              drawingsGridOptionsApi.redrawRows({ rowNodes: [rowNode] });
            }
          }
        }
      });

      if (res.error) return;

      // PERFORM CLEANUP
      cleanupViewer();
    });
  }, [dispatch, PSPDFKit, instance, drawingInfo, drawingsGridOptionsApi]);

  const cleanupViewer = useCallback(async () => {
    if (PSPDFKit && instance) await PSPDFKit.unload(instance);
    dispatch(handleCleanup);
    handleClose();
  }, [dispatch, PSPDFKit, instance]);

  return (
    <Modal
      open={open}
      handleClose={async () => {
        if (PSPDFKit && instance) await PSPDFKit.unload(instance);
        handleClose();
      }}
    >
      <div
        className="cloud-sheet-creation-container"
        style={{ height: "calc(100vh - 110px)", width: "calc(100vw - 300px)" }}
      >
        <div
          ref={containerRef}
          style={{ width: "calc(100% - 100px)", height: "100%" }}
        />
        {selectedTemplate === "DEFAULT" && (
          <Default
            drawingInfo={drawingInfo}
            userInfo={userInfo}
            systemSettings={systemSettings}
            createTableAnnotation={createTableAnnotation}
            createImageAnnotation={createImageAnnotation}
            whiteToTransparent={whiteToTransparent}
            removeBlanks={removeBlanks}
            instance={instance}
            cloudSheetSpoolImage={cloudSheetSpoolImage}
            cloudSheetItems={cloudSheetItems}
          />
        )}
        <div className="template-selector">
          <div className="template-list">
            <h2>Templates</h2>
            <Button
              onClick={() => setSelectedTemplate("DEFAULT")}
              disabled={selectedTemplate === "DEFAULT"}
            >
              Default
            </Button>
            {/* <Button
              onClick={() => setSelectedTemplate(2)}
              disabled={selectedTemplate === 2}
            >
              Template 2
            </Button>
            <Button
              onClick={() => setSelectedTemplate(3)}
              disabled={selectedTemplate === 3}
            >
              Template 3
            </Button> */}
            {/* <Button disabled>New Template</Button> */}
          </div>
          <div className="action-buttons">
            <Button onClick={saveSheet}>Save Sheet</Button>
            <Button onClick={cleanupViewer}>Cancel</Button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default CloudSheetCreation;
