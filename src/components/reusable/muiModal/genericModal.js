import {
  IconButton,
  <PERSON>ton,
  Dialog,
  DialogContent,
  DialogActions,
  DialogTitle,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

import "./genericModal.scss";

export default function GenericModal({
  isOpen,
  fullWidth,
  title,
  children,
  handleClose, // required
  isConfirmation = false,
  // START - required for a confirmation dialog
  confirmText = "Confirm",
  cancelText = "Cancel",
  handleConfirm,
  handleCancel,
  // END - required for a confirmation dialog
}) {
  // dynamic content, leaving here...
  const headerStyle = {
    backgroundColor: isConfirmation ? "transparent" : "rgba(0,0,0,.25)",
    p: !isConfirmation ? "8px 48px 8px 24px" : "16px 24px 8px 24px",
  };

  return (
    <Dialog
      open={isOpen}
      onClose={handleClose}
      aria-labelledby="generic-dialog-title"
      aria-describedby="generic-dialog-description"
      fullWidth={fullWidth}
      maxWidth="lg"
      scroll="body"
    >
      {!isConfirmation && (
        <IconButton
          id="generic-dialog-close-button"
          size="small"
          aria-label="close-modal"
          onClick={handleClose}
        >
          <CloseIcon fontSize="small" />
        </IconButton>
      )}
      {title && (
        <DialogTitle id="generic-dialog-title" sx={headerStyle}>
          {title}
        </DialogTitle>
      )}
      {children && (
        <DialogContent id="generic-dialog-description">
          {children}
        </DialogContent>
      )}
      {isConfirmation && (
        <DialogActions id="generic-dialog-actions">
          <Button
            fullWidth
            onClick={handleCancel ?? handleClose}
            size="small"
            variant="outlined"
          >
            {cancelText}
          </Button>
          <Button
            fullWidth
            onClick={handleConfirm ?? handleClose}
            size="small"
            variant="contained"
          >
            {confirmText}{" "}
          </Button>
        </DialogActions>
      )}
    </Dialog>
  );
}
