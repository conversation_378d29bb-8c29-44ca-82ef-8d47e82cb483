@import "../../styles/colors.scss";

div.manage-maj-modal {
  width: 650px;
  height: 600px;

  & > h2.title {
    color: #fff;
    background-color: $fabProBlue;
    margin: 0;
    padding: 5px 10px;
    font-size: 1rem;

    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  & > h2.title > svg {
    font-size: 1.4rem;
  }

  & > h2.title > svg:hover {
    color: darken(#fff, 20%);
    transition: color ease 300ms;
    cursor: pointer;
  }

  & > div.content {
    height: 100%;
    background-color: $grey;
    padding: 10px;
    box-sizing: border-box;

    display: grid;
    grid-template-rows: [download-s] 30px [download-e table-s] 3fr [table-e upload-s] 1fr [upload-e];
  }

  & > div.content > div.download-selected-majs {
    display: grid;
    align-items: center;
    column-gap: 10px;
    grid-template-columns: 20px 1fr;

    color: #fff;
    grid-row: download-s/download-e;
    width: 225px;
  }

  & > div.content > div.download-selected-majs:hover {
    cursor: pointer;

    & > span {
      color: darken(#fff, 20%);
      transition: color ease 300ms;
    }

    & > svg {
      color: darken($fabProBlue, 10%);
      transition: color ease 300ms;
    }
  }

  & > div.content > div.download-selected-majs > svg {
    color: $fabProBlue;
    font-size: 1.4rem;
  }

  & > div.content > div.ag-theme-balham-dark.custom-ag-styles {
    height: calc(100%);
    width: 100%;

    grid-row: table-s/table-e;
  }

  & > div.content > section.file-upload {
    grid-row: upload-s/upload-e;
  }
}
