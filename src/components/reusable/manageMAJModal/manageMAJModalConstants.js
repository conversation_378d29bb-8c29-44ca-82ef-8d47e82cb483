import { naturalSort } from "../../../_utils";

export const columnDefs = (
  currentTable,
  downloadMajFiles,
  multipleDrawings = false
) => {
  let result = [];

  if (/PACKAGES/i.test(currentTable) || multipleDrawings)
    result.push({
      headerName: "",
      headerCheckboxSelection: true,
      headerCheckboxSelectionFilteredOnly: true,
      minWidth: 50,
      width: 50,
      checkboxSelection: true,
      lockVisible: true,
      suppressMovable: true,
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      colId: "checkbox",
    });

  result.push(
    {
      headerName: "Package Name",
      field: "package_name",
      getQuickFilterText: (params) => params.data.package_name,
      minWidth: 120,
      width: 140,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_name",
      autoHeight: true,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Drawing Name",
      field: "drawing_name",
      getQuickFilterText: (params) => params.data.drawing_name,
      minWidth: 140,
      width: 160,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "drawing_name",
      autoHeight: true,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "MAJ Name",
      field: "maj_name",
      getQuickFilterText: (params) => params.data.maj_name,
      minWidth: 140,
      width: 160,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "maj_name",
      autoHeight: true,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Revision",
      field: "maj_revision_number",
      getQuickFilterText: (params) => params.data.maj_revision_number,
      minWidth: 80,
      width: 100,
      resizable: true,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "maj_revision_number",
      autoHeight: true,
    }
  );

  if (/DRAWINGS/i.test(currentTable) && !multipleDrawings)
    result.push({
      headerName: "",
      sortable: false,
      minWidth: 60,
      width: 60,
      valueFormatter: (params) => {
        return {
          handleDownloadFile: () => {
            downloadMajFiles([params.data]);
          },
        };
      },
      pinned: "right",
      cellRenderer: "fileDownloadCellRenderer",
      lockVisible: true,
      suppressMovable: true,
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      colId: "file_download",
    });

  return result;
};
