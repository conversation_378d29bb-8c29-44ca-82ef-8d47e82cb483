// NPM PACKAGE IMPORTS
import configureMockStore from "redux-mock-store";
import axios from "axios";
import Mock<PERSON>dapter from "axios-mock-adapter";
import thunk from "redux-thunk";

// REDUX IMPORTS
import {
  // ACTION CREATORS
  receiveSucceeded,

  // ACTION HANDLERS
  handleCreateDrawingsMAJMap,
  handleFetchDrawingsMAJMap,
} from "./manageMAJModalActions";

// CONSTANTS IMPORTS
import { columnDefs } from "./manageMAJModalConstants";

describe("Manage MAJ Modal", () => {
  const drawingMajMaps = [
    {
      drawing_id: 1,
      package_name: "P-1",
      drawing_name: "D-1",
      maj_name: "MAJ-1",
      maj_revision_number: 0,
    },
    {
      drawing_id: 2,
      package_name: "P-1",
      drawing_name: "D-2",
      maj_name: null,
      maj_revision_number: 0,
    },
  ];

  describe("Manage MAJ Modal Column Defs", () => {
    const defaultHeaders = [
      "Package Name",
      "Drawing Name",
      "MAJ Name",
      "Revision",
    ];
    const downloadAction = jest.fn();

    let packagesPopulatedColumns,
      drawingsPopulatedColumns,
      multipleDrawingsPopulatedColumns;

    beforeEach(() => {
      packagesPopulatedColumns = columnDefs("PACKAGES", downloadAction);
      drawingsPopulatedColumns = columnDefs("DRAWINGS", downloadAction);
      multipleDrawingsPopulatedColumns = columnDefs(
        "DRAWINGS",
        downloadAction,
        true
      );
    });

    it("Default Headers are present", () => {
      [
        packagesPopulatedColumns,
        drawingsPopulatedColumns,
        multipleDrawingsPopulatedColumns,
      ].forEach((columnHeaders) => {
        for (let column of defaultHeaders) {
          expect(!!columnHeaders.find((c) => c.headerName === column)).toBe(
            true
          );
        }
      });
    });

    it("Table-specific columns are present/not present", () => {
      expect(
        !!packagesPopulatedColumns.find((c) => c.colId === "checkbox")
      ).toBe(true);
      expect(
        !!packagesPopulatedColumns.find((c) => c.colId === "file_download")
      ).toBe(false);
      expect(
        !!drawingsPopulatedColumns.find((c) => c.colId === "checkbox")
      ).toBe(false);
      expect(
        !!drawingsPopulatedColumns.find((c) => c.colId === "file_download")
      ).toBe(true);
      expect(
        !!multipleDrawingsPopulatedColumns.find((c) => c.colId === "checkbox")
      ).toBe(true);
      expect(
        !!multipleDrawingsPopulatedColumns.find(
          (c) => c.colId === "file_download"
        )
      ).toBe(false);
    });

    describe("PACKAGE NAME", () => {
      let column;
      beforeEach(() => {
        column = packagesPopulatedColumns.find(
          (c) => c.headerName === "Package Name"
        );
      });
      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText({ data: drawingMajMaps[0] })).toBe(
          "P-1"
        );
      });
      it("comparator", () => {
        expect(column.comparator("P-1", "P-2")).toBe(-1);
        expect(column.comparator("P-2", "P-1")).toBe(1);
      });
    });

    describe("DRAWING NAME", () => {
      let column;
      beforeEach(() => {
        column = packagesPopulatedColumns.find(
          (c) => c.headerName === "Drawing Name"
        );
      });
      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText({ data: drawingMajMaps[0] })).toBe(
          "D-1"
        );
      });
      it("comparator", () => {
        expect(column.comparator("D-1", "D-2")).toBe(-1);
        expect(column.comparator("D-2", "D-1")).toBe(1);
      });
    });

    describe("MAJ NAME", () => {
      let column;
      beforeEach(() => {
        column = packagesPopulatedColumns.find(
          (c) => c.headerName === "MAJ Name"
        );
      });
      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText({ data: drawingMajMaps[0] })).toBe(
          "MAJ-1"
        );
        expect(column.getQuickFilterText({ data: drawingMajMaps[1] })).toBe(
          null
        );
      });
      it("comparator", () => {
        expect(column.comparator("MAJ-1", "MAJ-2")).toBe(-1);
        expect(column.comparator("MAJ-2", "MAJ-1")).toBe(1);
        expect(column.comparator("MAJ-2", null)).toBe(-1);
      });
    });

    describe("FILE DOWNLOAD", () => {
      let column;
      beforeEach(() => {
        column = drawingsPopulatedColumns.find(
          (c) => c.colId === "file_download"
        );
      });
      it("cellRenderer", () => {
        expect(column.cellRenderer).toBe("fileDownloadCellRenderer");
      });
      it("valueFormatter", () => {
        expect(
          !!column.valueFormatter({ data: drawingMajMaps[0] })
            .handleDownloadFile
        ).toBe(true);
      });
    });
  });

  describe("Action handlers should perform the necessary functions", () => {
    const testError = {
      error: { status: 404, message: "No drawing maj maps found" },
    };

    let store;
    let httpMock;

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    it("handleCreateDrawingsMAJMap", async () => {
      httpMock
        .onPost(`${process.env.REACT_APP_API}/drawings/upload-majs`)
        .replyOnce(200, drawingMajMaps)
        .onPost(`${process.env.REACT_APP_API}/drawings/upload-majs`)
        .replyOnce(200, drawingMajMaps)
        .onPost(`${process.env.REACT_APP_API}/drawings/upload-majs`)
        .replyOnce(200, drawingMajMaps)
        .onPost(`${process.env.REACT_APP_API}/drawings/upload-majs`)
        .replyOnce(404, testError);

      await store
        .dispatch(
          handleCreateDrawingsMAJMap(
            [{ drawing_id: 1 }, { drawing_id: 2 }],
            1133,
            { name: "MAJ-1" },
            "DRAWINGS_OR_PACKAGES"
          )
        )
        .then((res) => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveSucceeded("DRAWING_MAJS_UPLOADED", [1, 2]),
          ];
          expect(receivedActions).toEqual(expectedActions);
          expect(JSON.stringify(receivedActions[0].payload)).toEqual(
            JSON.stringify([1, 2])
          );
          expect(JSON.stringify(res)).toEqual(JSON.stringify(drawingMajMaps));

          store.clearActions();
        });
      await store
        .dispatch(
          handleCreateDrawingsMAJMap(
            [{ drawing_id: 1 }, { drawing_id: 2 }],
            1133,
            { name: "MAJ-1" },
            "PENDING_APPROVAL"
          )
        )
        .then((res) => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveSucceeded("DRAWINGS_PENDING_MAJS_UPLOADED", [1, 2]),
          ];
          expect(receivedActions).toEqual(expectedActions);
          expect(JSON.stringify(receivedActions[0].payload)).toEqual(
            JSON.stringify([1, 2])
          );
          expect(JSON.stringify(res)).toEqual(JSON.stringify(drawingMajMaps));

          store.clearActions();
        });
      await store
        .dispatch(
          handleCreateDrawingsMAJMap(
            [{ drawing_id: 1 }, { drawing_id: 2 }],
            1133,
            { name: "MAJ-1" },
            "SENT_BACK"
          )
        )
        .then((res) => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveSucceeded("DRAWINGS_SENT_BACK_MAJS_UPLOADED", [1, 2]),
          ];
          expect(receivedActions).toEqual(expectedActions);
          expect(JSON.stringify(receivedActions[0].payload)).toEqual(
            JSON.stringify([1, 2])
          );
          expect(JSON.stringify(res)).toEqual(JSON.stringify(drawingMajMaps));

          store.clearActions();
        });
      return store
        .dispatch(
          handleCreateDrawingsMAJMap(
            [{ drawing_id: 1 }, { drawing_id: 2 }],
            1133,
            { name: "MAJ-1" }
          )
        )
        .then((res) => {
          const receivedActions = store.getActions();
          const expectedActions = [];
          expect(receivedActions).toEqual(expectedActions);
          expect(JSON.stringify(res)).toEqual(JSON.stringify(testError));

          store.clearActions();
        });
    });

    //TODO: add test for get maj file
    it("handleFetchDrawingsMAJMap", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/drawings/majs?drawing_ids=1,2,3`)
        .replyOnce(200, drawingMajMaps)
        .onGet(`${process.env.REACT_APP_API}/drawings/majs?package_id=1`)
        .replyOnce(200, drawingMajMaps)
        .onGet(
          `${process.env.REACT_APP_API}/drawings/majs?drawing_ids=1,2,3&package_id=1`
        )
        .replyOnce(200, drawingMajMaps)
        .onGet(`${process.env.REACT_APP_API}/drawings/majs?drawing_ids=1,2,3`)
        .replyOnce(404, testError);

      await store
        .dispatch(handleFetchDrawingsMAJMap([1, 2, 3], null))
        .then((res) => {
          const receivedActions = store.getActions();
          const expectedActions = [];
          expect(receivedActions).toEqual(expectedActions);
          expect(JSON.stringify(res)).toEqual(JSON.stringify(drawingMajMaps));

          store.clearActions();
        });

      await store.dispatch(handleFetchDrawingsMAJMap(null, 1)).then((res) => {
        const receivedActions = store.getActions();
        const expectedActions = [];
        expect(receivedActions).toEqual(expectedActions);
        expect(JSON.stringify(res)).toEqual(JSON.stringify(drawingMajMaps));

        store.clearActions();
      });

      await store
        .dispatch(handleFetchDrawingsMAJMap([1, 2, 3], 1))
        .then((res) => {
          const receivedActions = store.getActions();
          const expectedActions = [];
          expect(receivedActions).toEqual(expectedActions);
          expect(JSON.stringify(res)).toEqual(JSON.stringify(drawingMajMaps));

          store.clearActions();
        });

      return store
        .dispatch(handleFetchDrawingsMAJMap([1, 2, 3], null))
        .then((res) => {
          const receivedActions = store.getActions();
          const expectedActions = [];
          expect(receivedActions).toEqual(expectedActions);
          expect(JSON.stringify(res)).toEqual(JSON.stringify(testError));

          store.clearActions();
        });
    });
  });
});
