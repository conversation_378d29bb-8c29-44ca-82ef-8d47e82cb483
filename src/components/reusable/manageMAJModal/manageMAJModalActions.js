import { createDrawingsMAJMap, fetchDrawingsMAJMap } from "../../../_services";

export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});

export const handleCreateDrawingsMAJMap = (
  drawings,
  jobId,
  file,
  currentTable
) => (dispatch) => {
  return createDrawingsMAJMap(drawings, jobId, file).then((res) => {
    if (!res.error) {
      const type = /PENDING_APPROVAL/.test(currentTable)
        ? "DRAWINGS_PENDING_MAJS_UPLOADED"
        : /SENT_BACK/.test(currentTable)
        ? "DRAWINGS_SENT_BACK_MAJS_UPLOADED"
        : "DRAWING_MAJS_UPLOADED";
      dispatch(
        receiveSucceeded(
          type,
          res.map((r) => r.drawing_id)
        )
      );
    }

    return res;
  });
};

export const handleFetchDrawingsMAJMap = (
  drawingIds = null,
  packageId = null
) => () => {
  return fetchDrawingsMAJMap(drawingIds, packageId);
};
