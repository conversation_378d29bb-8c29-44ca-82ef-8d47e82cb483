// NPM PACKAGE IMPORTS
import React, { useCallback, useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import FileUpload from "msuite_storybook/dist/fileUpload/FileUpload";
import Modal from "msuite_storybook/dist/modal/Modal";
import { FaTimes } from "react-icons/fa";
import { IoMdCloudDownload } from "react-icons/io";

// REDUX IMPORTS
import {
  handleCreateDrawingsMAJMap,
  handleFetchDrawingsMAJMap,
} from "./manageMAJModalActions";
import { handleFetchDrawingFiles } from "./../../files/filesActions";
import { notify } from "../../reusable/alertPopup/alertPopupActions";

// COMPONENT IMPORTS
import AgTable from "../agTable/AgTable";
import FileDownloadCellRenderer from "../frameworkComponents/FileDownloadCellRenderer";

// CONSTANTS IMPORTS
import { columnDefs } from "./manageMAJModalConstants";

// HELPER FUNCTION IMPORTS
import { downloadFilesV2 } from "../../../_utils";

// STYLE IMPORTS
import "./manageMAJModalStyles.scss";

const ManageMAJModal = ({
  showModal = false,
  handleClose = (f) => f,
  currentTable = "PACKAGES",
  drawingOrPackage = {},
}) => {
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [selectedRows, setSelectedRows] = useState([]);
  const [uploadDragReject, toggleUploadDragReject] = useState(false);
  const [uploadDisabled, toggleUploadDisabled] = useState(false);

  const dispatch = useDispatch();

  const displayNotification = (message) => {
    dispatch(
      notify({
        id: Date.now(),
        type: "ERROR",
        message,
      })
    );
  };

  useEffect(() => {
    if (gridOptionsApi) {
      let args = [];
      if (/PACKAGES/.test(currentTable))
        args = [null, drawingOrPackage.id || drawingOrPackage.package_id];
      else args = [drawingOrPackage.drawing_ids || [drawingOrPackage.id], null];

      dispatch(handleFetchDrawingsMAJMap(...args)).then((res) => {
        if (!res.error) {
          gridOptionsApi.setRowData(res);
          if (/DRAWINGS/.test(currentTable) && !drawingOrPackage.drawing_ids)
            setSelectedRows(res);
        }
      });
    }
  }, [gridOptionsApi, drawingOrPackage]);

  useEffect(() => {
    if (
      (/PACKAGES/i.test(currentTable) || !!drawingOrPackage.drawing_ids) &&
      !selectedRows.length
    )
      toggleUploadDisabled(true);
    else if (uploadDisabled) toggleUploadDisabled(false);
  }, [selectedRows, currentTable]);

  const downloadMajFiles = (rows) => {
    // repull ALL the maj drawing urls in case the maj has been updated since the last call
    // these urls aren't static like the drawing pdfs...
    if (!rows || rows.length < 1 || !rows.some((i) => i.maj_file_id)) {
      displayNotification("No files to download");
      return;
    }
    // prepare the drawings to query...
    const drawingsToFetch = rows
      .filter((i) => i.maj_file_id)
      .map((d) => d.drawing_id);
    dispatch(handleFetchDrawingFiles(drawingsToFetch.join(","), "maj")).then(
      (res) => {
        // merge the entries with their maj files for download...
        if (!res.error) {
          let files = drawingsToFetch
            .map((id) => ({ maj_file: res[id]?.maj }))
            .filter((i) => i.maj_file);
          // downloads with the same file name as was uploaded...
          downloadFilesV2(files, "maj_file");
        }
      }
    );
  };

  const handleDownloadFiles = useCallback(() => {
    downloadMajFiles(selectedRows);
    gridOptionsApi.deselectAll();
  }, [selectedRows, gridOptionsApi]);

  const handleOnDrop = useCallback(
    (files = []) => {
      const pattern = new RegExp(/^.+\.maj$/, "gi");
      const majFiles = files.filter((f) => pattern.test(f.name));
      if (majFiles.length) {
        dispatch(
          handleCreateDrawingsMAJMap(
            selectedRows,
            drawingOrPackage.job_id,
            majFiles[0],
            currentTable
          )
        ).then((res) => {
          if (!res.error) {
            res.forEach((r) => {
              const rowNode = gridOptionsApi.getRowNode(r.drawing_id);
              if (rowNode) {
                rowNode.setData(r);
                gridOptionsApi.redrawRows([rowNode]);
              }
            });
            // only deselect row if not at drawing level,
            // otherwise can't upload more than once without reopening the modal
            if (
              /PACKAGES/i.test(currentTable) ||
              !!drawingOrPackage.drawing_ids
            ) {
              gridOptionsApi.deselectAll();
            }
          }
        });
        toggleUploadDragReject(false);
      } else toggleUploadDragReject(true);
    },
    [selectedRows, drawingOrPackage, gridOptionsApi]
  );

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
  };
  const onSortChanged = (params) => {
    params.api.redrawRows();
  };
  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };
  const onSelectionChanged = (params) => {
    let rows = params.api.getSelectedRows();
    return setSelectedRows(rows);
  };

  const gridOptions = {
    rowData: [],
    frameworkComponents: {
      fileDownloadCellRenderer: FileDownloadCellRenderer,
    },
    reactNext: true,
    rowClassRules,
    onSelectionChanged,
    onGridReady,
    rowSelection: "multiple",
    suppressRowClickSelection: true,
    defaultColDef: {
      cellClass: ["no-border", "custom-wrap"],
      wrapText: true,
      suppressSizeToFit: false,
    },
    columnDefs: columnDefs(
      currentTable,
      downloadMajFiles,
      !!drawingOrPackage.drawing_ids
    ),
    pagination: true,
    paginationPageSize: 100,
    getRowNodeId: (data) => data.drawing_id,
    onSortChanged,
    sideBar: false,
  };

  return (
    <Modal open={showModal} handleClose={handleClose}>
      <div className="manage-maj-modal">
        <h2 className="title">
          <span>MAJ Manager</span> <FaTimes onClick={handleClose} />
        </h2>
        <div className="content">
          {(/PACKAGES/i.test(currentTable) ||
            !!drawingOrPackage.drawing_ids) && (
            <div
              className="download-selected-majs"
              onClick={handleDownloadFiles}
            >
              <IoMdCloudDownload />
              <span>Download Selected MAJs</span>
            </div>
          )}
          <AgTable gridOptions={gridOptions} />
          <FileUpload
            onDrop={handleOnDrop}
            isDragReject={uploadDragReject}
            isDisabled={uploadDisabled}
            acceptedFileExtensions={[".maj"]}
          />
        </div>
      </div>
    </Modal>
  );
};

export default ManageMAJModal;
