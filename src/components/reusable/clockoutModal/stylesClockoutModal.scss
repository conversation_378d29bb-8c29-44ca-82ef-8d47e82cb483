@import "../../styles/colors.scss";

.modal__clockout {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  color: #333;
  background-color: #fff;

  & > svg {
    font-size: 6rem;
    color: $red;
    background-color: #fff;
    border-radius: 50%;
  }

  & > div.sub-title {
    font-size: 1.3rem;
    margin-top: 20px;
  }

  & > p {
    font-size: 0.9rem;
    margin: 10px 0;
  }

  & > span.button-row {
    display: flex;
    justify-content: space-evenly;
    width: 95%;
    margin: 20px auto 0;

    & > button {
      border: none;
      padding: 8px 14px;
      color: inherit;
      font-weight: bold;
      border-radius: 3px;
      height: 38px;
      cursor: pointer;
      font-size: 12px;
    }

    & > button.cancel {
      background-color: #fff;
      color: #000;

      &:hover {
        background-color: darken(#fff, 10%);
      }
    }

    & > button.submit {
      background-color: $red;
      color: #fff;

      &:hover {
        background-color: darken($red, 10%);
      }
    }
  }
}
