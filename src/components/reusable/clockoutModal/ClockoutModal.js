// NPM PACKAGE IMPORTS
import React from "react";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import { BsExclamationCircle } from "react-icons/bs";

// HELPER FUNCTION IMPORTS
import useTranslations from "../../../hooks/useTranslations";

// STYLE IMPORTS
import "./stylesClockoutModal.scss";

const ClockoutModal = ({
  translations,
  showClockOut,
  toggleClockOut,
  endSessionAndLogout,
}) => {
  const translate = useTranslations(translations);

  return (
    <Modal open={showClockOut} handleClose={() => toggleClockOut(false)}>
      <div className="modal__clockout">
        <BsExclamationCircle />
        <p className="sub-title">{translate("Are you ending your session?")}</p>
        <p>
          {translate("Clocking out will end your session at the current time")}
        </p>
        <span className="button-row">
          <Button className="cancel" onClick={() => toggleClockOut(false)}>
            {translate("Cancel")}
          </Button>
          <Button className="submit" onClick={endSessionAndLogout}>
            {translate("Log Out and End Session")}
          </Button>
        </span>
      </div>
    </Modal>
  );
};

export default ClockoutModal;
