// NPM PACKAGE IMPORTS
import React, { useState, useMemo, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";

// REDUX IMPORTS
import {
  handleUpdateJobs,
  handleUpdateJobsNoRefresh,
} from "../../jobs/jobsActions";
import {
  handleUpdatePackages,
  handleUpdatePackagesNoRefresh,
} from "../../packages/packagesActions";
import {
  handleUpdateDrawings,
  handleFetchItemHeatNumbers,
  handleFetchContainers,
  handleFetchDrawings,
  handleUpdateContainers,
  handleCreateContainer,
  handleUpdateDrawingsNoRefresh,
} from "../../drawings/drawingsActions";
import {
  handleUpdateItems,
  handleDeleteItem,
  handleUpdateGroupedItems,
  handleFetchMaterialTypes,
  handleCreateMaterialType,
  handleCreateLaydownLocation,
  handleCompleteUncomplete,
  handleFetchJoiningProcedures,
  handleFetchWorkTableItems,
} from "../../items/itemsActions";
import { handleFetchLaydownLocations } from "../../shipping/shippingActions";
import { notify } from "../alertPopup/alertPopupActions";

// COMPONENT IMPORTS
import Modal from "../modal/Modal";
import JointHeatNumber from "./jointHeatNumber/JointHeatNumber";
import UpdateDropdown from "./updateDropdown/UpdateDropdown";
import ContainerForm from "./ContainerForm";
import DueDateCellRenderer from "./../frameworkComponents/DueDateCellRenderer";

// HELPER FUNCTION IMPORTS
import {
  convertFracToDec,
  convertStringToUnix,
  fractionalRegex,
  multidimensionOnlyX,
  multidimensionRegex,
} from "../../../_utils";

// STYLE IMPORTS
import "./massUpdate.scss";

const integerFields = ["budget_hours", "weight"];

const fracFields = ["length"];

const basicDropdownFields = ["on_hold", "joining_procedure_name"];

const customDropdownFields = [
  "stage",
  "laydown_location_id",
  "material_type_id",
  "heat_number",
];

const editableFields = {
  jobs: {
    budget_hours: "Budget Hours",
    target_date: "Due Date",
  },
  packages: {
    number: "Package #",
    area: "Area",
    due_date: "Due Date",
    budget_hours: "Budget Hours",
    on_hold: "On Hold",
  },
  drawings: {
    area: "Area",
    on_hold: "On Hold",
    laydown_location: "Laydown Location",
    due_date: "Due Date",
    // cost_code: "Cost Code",
    container_id: "Container",
  },
  items: {
    laydown_location_id: "Laydown Location",
    vendor: "Vendor",
    end_prep_1: "End Prep 1",
    end_prep_2: "End Prep 2",
    end_prep_3: "End Prep 3",
    end_prep_4: "End Prep 4",
    length: "Length", // frac
    tag_number: "Tag Number",
    stock_length: "Stock Length", // frac
    height: "Height", // frac
    width: "Width", // frac
    thickness: "Thickness", // frac
    paint_spec: "Paint Spec",
    texture: "Texture",
    fixture_type: "Fixture Type",
    gauge: "Gauge", // frac
    weight: "Weight",
    hanger_size: "Hanger Size", // frac
    product_code: "Product Code",
    insulation: "Insulation",
    insulation_area: "Insulation Area", // frac
    insulation_specification: "Insulation Spec",
    insulation_gauge: "Insulation Gauge",
    joining_procedure_name: "Joining Procedure",
    material_type_id: "Material Type",
    service_name: "Service",
    service_color_name: "Service Color",
    status: "Status", // Completed / Not Completed
    size: "Size", // multidimensional frac
    area: "Area", // frac
    heat_number: "Heat Number",
    random_length: "Random Length", // frac
    support_rod_length: "Support Rod Length 1", // frac
    support_rod_length_2: "Support Rod Length 2", // frac
    liner_spec: "Liner Spec",
    joint_heat_number: "Joint Heat Number",
    filler_metal: "Filler Metal",
    package_number: "Package Number",
    package_area: "Package Area",
    package_due_date: "Package Due Date",
    drawing_due_date: "Drawing Due Date",
    // shipping_container_id: "Container"
  },
};

const MassUpdate = ({
  toggleModal,
  currentTable,
  editedFields,
  setEditedFields,
  selectedRows,
  setSelectedRows,
  selectedIdsInFilters,
  displayedStageColumns,
  handleJointUpdate,
  showAllWork,
  selectedStages,
  selectedJobs,
  selectedPackages,
  selectedDrawings,
  selectedGrouping,
  selectedGroupableColumns,
  jobsGridOptionsApi,
  packagesGridOptionsApi,
  drawingsGridOptionsApi,
  itemsGridOptionsApi,
  setSelectedPackages,
  setSelectedDrawings,
  rowInfo,
}) => {
  const [selectedField, setSelectedField] = useState(null);
  const [updatedValue, setUpdatedValue] = useState("");
  const [updatedObjValue, setUpdatedObjValue] = useState(null);
  const [createNew, toggleCreateNew] = useState(false);
  const [itemToAdd, setItemToAdd] = useState(null);
  // used for containers
  const [editType, setEditType] = useState(null);
  const [saving, toggleSaving] = useState(false);
  const [error, toggleError] = useState(false);

  const { stages } = useSelector((state) => state.flowsData);
  const { heatNumbers, isLoading: loadingDrawings } = useSelector(
    (state) => state.drawingsData
  );
  const { laydown_locations } = useSelector((state) => state.shippingData);
  const { containers } = useSelector((state) => state.drawingsData);
  const { materialTypes, joiningProcedures } = useSelector(
    (state) => state.itemsData
  );
  const { permissions } = useSelector((state) => state.profileData);

  const dispatch = useDispatch();

  const jobsOfSelected = useMemo(() => {
    if (selectedField === "container_id") {
      const drawingsWithoutContainer = selectedRows.find(
        (r) => !r.container_id
      );
      return drawingsWithoutContainer?.length
        ? [drawingsWithoutContainer[0].job_id]
        : [selectedRows[0].job_id];
    }

    let jobIds = selectedRows.map((row) => row.job_id);
    jobIds = [...new Set(jobIds)];
    return jobIds;
  }, [selectedRows, selectedField]);

  const showHeatNumberDropdown = useMemo(() => {
    if (currentTable !== "ITEMS" || selectedField !== "heat_number")
      return false;
    if (
      !selectedRows ||
      !selectedRows.length ||
      selectedRows[0].size === null ||
      selectedRows[0].material_name === null ||
      !selectedRows.every(
        (r) =>
          r.size === selectedRows[0].size &&
          r.material_name === selectedRows[0].material_name
      )
    )
      return false;
    else return true;
  }, [selectedRows, selectedField]);

  useEffect(() => {
    if (currentTable === "ITEMS") {
      dispatch(handleFetchLaydownLocations());
      dispatch(handleFetchMaterialTypes());
    }
  }, [currentTable, dispatch]);

  useEffect(() => {
    if (selectedField === "joining_procedure_name") {
      dispatch(handleFetchJoiningProcedures);
    }
  }, [selectedField]);

  // fetch containers separate from laydown locations and material types to limit calls
  useEffect(() => {
    if (currentTable !== "DRAWINGS" || selectedField !== "container_id") return;

    dispatch(handleFetchContainers(jobsOfSelected));
  }, [currentTable, selectedField, jobsOfSelected, dispatch]);

  const onClose = (notSaving = false) => {
    if (saving || !notSaving) return;
    setSelectedField(null);
    setUpdatedValue("");
    handleClose();
    toggleModal(false);
  };

  useEffect(() => {
    if (selectedField === "heat_number" && showHeatNumberDropdown) {
      dispatch(
        handleFetchItemHeatNumbers(
          null,
          selectedRows.map((r) => r.job_id),
          selectedRows[0].size,
          selectedRows[0].material_name
        )
      );
    }
  }, [selectedField, showHeatNumberDropdown]);

  useEffect(() => {
    if (
      currentTable !== "ITEMS" ||
      !selectedField ||
      !selectedField.includes("joint_heat_number")
    )
      return;
    const selectedDrawingIds = selectedRows.map((item) => item.drawing_id);
    dispatch(
      handleFetchItemHeatNumbers(
        "JOINT",
        selectedRows.map((r) => r.job_id),
        null,
        null,
        selectedDrawingIds
      )
    );
  }, [currentTable, dispatch, selectedRows, selectedField]);

  const editableColumns = useMemo(() => {
    if (
      selectedGrouping &&
      selectedGroupableColumns &&
      currentTable === "ITEMS"
    ) {
      const editableCols = Object.keys(editableFields["items"]);

      const displayedEditableCols = selectedGroupableColumns.filter((col) => {
        if (editableCols.includes(col.normal_name)) return true;
        if (col.normal_name === "material_name") return true;
        else return false;
      });

      const f_displayedEditableCols = Object.fromEntries(
        displayedEditableCols.map((col) => {
          if (col.normal_name === "material_name")
            return ["material_type_id", col.display_name];
          return [col.normal_name, col.display_name];
        })
      );

      return f_displayedEditableCols || [];
    } else return editableFields[currentTable.toLowerCase()];
  }, [selectedGroupableColumns, currentTable, selectedGrouping]);

  const f_editableColumns = useMemo(() => {
    if (currentTable !== "ITEMS") {
      let arr = Object.entries(editableColumns);
      return arr
        .map((arr) => ({ [arr[0]]: arr[1] }))
        .sort((a, b) => {
          if (Object.values(a)[0] > Object.values(b)[0]) return 1;
          else return -1;
        });
    }

    let columnArr = [];
    // check if none is selected in stage filter

    if (selectedIdsInFilters[3].includes(0)) {
      columnArr = Object.entries(editableColumns).map((arr) => ({
        [arr[0]]: arr[1],
      }));
      for (let i = 0; i < 2; i++) {
        columnArr.push({
          [`joint_heat_number${i + 1}`]: `Joint Heat Number ${i + 1}`,
        });
      }
      columnArr.push({ DELETE_ITEMS: "Delete Items" });

      return columnArr;
    } else if (selectedGrouping) {
      columnArr = Object.entries(editableColumns).map((arr) => ({
        [arr[0]]: arr[1],
      }));
    } else {
      displayedStageColumns &&
        displayedStageColumns.forEach((column) => {
          if (column.display_name === "Joint Heat Number") return;
          if (
            Object.keys(editableColumns).find((c) => c === column.normal_name)
          ) {
            const value = editableColumns[column.normal_name];
            columnArr.push({ [column.normal_name]: value });
          } else if (column.display_name === "Material Name") {
            const value = editableColumns.material_type_id;
            columnArr.push({ material_type_id: value });
          }
          // Only give option to edit laydown_location if no items have container
          else if (
            column.display_name === "Laydown Location" &&
            !selectedRows.some((r) => r.container_name)
          ) {
            const value = editableColumns.laydown_location_id;
            columnArr.push({ laydown_location_id: value });
          }
        });
    }
    if (
      displayedStageColumns.map(
        (col) => col.display_name === "Joint Heat Number"
      ) &&
      selectedGrouping === 0
    ) {
      for (let i = 0; i < 2; i++) {
        columnArr.push({
          [`joint_heat_number${i + 1}`]: `Joint Heat Number ${i + 1}`,
        });
      }
    }

    if (
      selectedIdsInFilters[3].length === 1 &&
      !selectedIdsInFilters[3].includes(0) &&
      permissions &&
      permissions.includes(146)
    ) {
      columnArr.push(
        ...[
          { complete: `Complete at ${selectedStages[0].name}` },
          { uncomplete: `Uncomplete at ${selectedStages[0].name}` },
        ]
      );
    }
    columnArr.push({ DELETE_ITEMS: "Delete Items" });

    return columnArr.sort((a, b) => {
      if (Object.values(a)[0] > Object.values(b)[0]) return 1;
      else return -1;
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    editableColumns,
    displayedStageColumns,
    currentTable,
    selectedIdsInFilters,
    selectedRows,
    editableColumns,
    selectedGrouping,
  ]);

  const handleClose = () => {
    toggleModal(false);
  };

  const isDate = useMemo(() => {
    if (!selectedField) return false;
    return (
      selectedField === "due_date" ||
      selectedField === "target_date" ||
      selectedField === "dueDate" ||
      selectedField === "package_due_date" ||
      selectedField === "drawing_due_date"
    );
  }, [selectedField]);

  useEffect(() => {
    if (selectedField === "container_id") return;
    if (isDate) {
      const f_date = convertStringToUnix(updatedValue);
      setEditedFields([{ [selectedField]: f_date }]);
    } else if (
      selectedField === "budget_hours" ||
      selectedField === "stage_id"
    ) {
      setEditedFields([{ [selectedField]: parseFloat(updatedValue) }]);
    } else {
      setEditedFields([{ [selectedField]: updatedValue }]);
    }
  }, [selectedField, isDate, updatedValue, setEditedFields]);

  const keys = f_editableColumns.map((col) => Object.keys(col)[0]);
  const values = f_editableColumns.map((col) => Object.values(col)[0]);

  const fieldType = useMemo(() => {
    if (!selectedField) return;
    if (integerFields.includes(selectedField)) return "integer";
    else if (basicDropdownFields.includes(selectedField)) return "dropdown";
    else if (customDropdownFields.includes(selectedField))
      return "customDropdown";
    else if (selectedField !== "complete" && selectedField !== "uncomplete")
      return "string";
    else return null;
  }, [selectedField]);

  const OnHoldDropdown = () => {
    return (
      <select
        value={updatedValue}
        onChange={(e) => setUpdatedValue(e.target.value)}
        className="column-select on-hold-select"
      >
        <option className="default" selected hidden>
          Select option
        </option>
        <option value="1">On Hold</option>
        <option value="0">Available For Work</option>
      </select>
    );
  };

  const handleCompleteUncompleteItem = (direction, items) => {
    return dispatch(
      handleCompleteUncomplete(
        direction,
        selectedStages[0].id,
        items
          ? items?.map((item) =>
              item.work_item_ids ? item.work_item_ids : item.id
            )
          : selectedRows?.map((item) =>
              item.work_item_ids ? item.work_item_ids : item.id
            )
      )
    ).then(() => {
      return dispatch(
        handleFetchWorkTableItems(
          selectedJobs.map((j) => j.id),
          selectedPackages.map((p) => p.id),
          selectedDrawings.map((d) => d.id),
          selectedStages.map((s) => s.id),
          showAllWork,
          selectedGrouping !== 0 && selectedStages.length === 1
            ? selectedStages[0].groupable
            : 0,
          selectedGroupableColumns
        )
      );
    });
  };

  const handleCreateNewLaydownLocation = (selectedItems, laydownName) => {
    if (!laydownName || !laydownName.trim()) return;
    return dispatch(
      handleCreateLaydownLocation(
        selectedItems.map((item) => item.id),
        laydownName
      )
    ).then(() => {
      return dispatch(
        handleFetchWorkTableItems(
          selectedJobs.map((j) => j.id),
          selectedPackages.map((p) => p.id),
          selectedDrawings.map((d) => d.id),
          selectedStages.map((s) => s.id),
          showAllWork,
          selectedGrouping !== 0 && selectedStages.length === 1
            ? selectedStages[0].groupable
            : 0,
          selectedGroupableColumns
        )
      );
    });
  };

  // only handles updating to new material type (not existing)
  const handleCreateNewMaterialType = (materialName) => {
    return dispatch(handleCreateMaterialType(materialName)).then(() => {
      return dispatch(
        handleUpdateItems(
          selectedRows.map((item) => item.id),
          [{ material_name: materialName }],
          selectedIdsInFilters
        )
      );
    });
  };

  const handleCreateNewContainer = (editedData) => {
    jobsOfSelected.forEach((jobId) => {
      const drawingIds =
        selectedRows && selectedRows.length
          ? selectedRows
              .filter((d) => d.job_id === jobId)
              .map((d) => d.id)
              .join(",")
          : null;

      return dispatch(
        handleCreateContainer(jobId, drawingIds, null, "work-items", editedData)
      ).then(async () => {
        toggleSaving(false);
        onClose(true);
        setSelectedRows([]);
        await dispatch(handleFetchContainers(jobsOfSelected));
        const newDrawingList = await dispatch(
          handleFetchDrawings(
            selectedPackages.map((p) => p.id),
            showAllWork,
            selectedStages.map((s) => s.id).join(",")
          )
        );

        setSelectedDrawings(newDrawingList);
      });
    });
  };

  const updateContainers = (drawingsToUpdate) => {
    if (drawingsToUpdate.some((d) => d.container_id)) {
      return dispatch(
        handleUpdateDrawings(
          drawingsToUpdate.filter((d) => d.container_id).map((d) => d.id),
          generateContainerParamObj("REMOVE"),
          "remove"
        )
      ).then((res) => {
        if (res.error) return;
        return dispatch(
          handleUpdateDrawings(
            drawingsToUpdate.map((d) => d.id),
            generateContainerParamObj("ADD"),
            "add",
            [selectedIdsInFilters[1], showAllWork]
          )
        );
      });
    } else {
      return dispatch(
        handleUpdateDrawings(
          drawingsToUpdate.map((d) => d.id),
          generateContainerParamObj("ADD"),
          "add",
          [selectedIdsInFilters[1], showAllWork]
        )
      ).then((res) => setSelectedDrawings(res));
    }
  };

  const handleSelectedStateUpdates = (
    currentSelectedItems,
    updatedSelectedItems,
    callback
  ) => {
    for (let updatedSelectedItem of updatedSelectedItems) {
      const updateIndex = currentSelectedItems.findIndex(
        (item) => item.id === updatedSelectedItem.id
      );

      if (updateIndex !== -1) {
        currentSelectedItems[updateIndex] = updatedSelectedItem;
      }
    }
    callback(currentSelectedItems);
  };

  useEffect(() => {
    if (selectedField !== "container_id" || selectedRows?.length <= 1) return;

    const uniqueJobs = [...new Set(selectedRows.map((d) => d.job_id))];
    if (uniqueJobs?.length > 1) {
      dispatch(
        notify({
          id: Date.now(),
          type: "WARN",
          message:
            "You have selected drawings with differing jobs. Only the first drawing's job will be assigned to the container.",
        })
      );
    }
  }, [selectedField, selectedRows]);

  // used only when updating containers of drawings
  const drawingsToUpdate = useMemo(() => {
    return selectedRows.filter((r) => r.job_id === jobsOfSelected[0]);
  }, [jobsOfSelected, selectedRows]);

  const attachedContainers = useMemo(() => {
    if (!containers) return null;
    if (selectedField !== "container_id") return null;

    const containerIds = drawingsToUpdate
      .filter((row) => row.container_id)
      .map((row) => row.container_id);

    if (!containerIds.length) return null;

    return containers.filter((c) => containerIds.includes(c.id));
  }, [selectedField, drawingsToUpdate, containers]);

  const generateContainerParamObj = (callAction = "ADD") => {
    return {
      container_id: callAction === "ADD" ? parseInt(itemToAdd) : null,
      stage_id: null,
      area: "work-items",
    };
  };

  const handleContainerUpdate = () => {
    const drawingIdsWithContainer = selectedRows
      .filter((d) => d.container_id)
      .map((d) => d.id);

    if (editType === "EXISTING") {
      if (!itemToAdd || itemToAdd === "") {
        // removing containers
        return dispatch(
          handleUpdateDrawings(
            drawingIdsWithContainer,
            generateContainerParamObj("REMOVE"),
            "remove",
            [selectedIdsInFilters[1], showAllWork]
          )
        ).then((res) => setSelectedDrawings(res));
      } else {
        // adding existing container

        // check if we are updating container info as well
        if (updatedObjValue) {
          return dispatch(
            handleUpdateContainers(itemToAdd, updatedObjValue)
          ).then(() => {
            // check if the container being updated is already attached to each drawing, if so we don't need to add it to drawings but we do need to clear the selected drawings because not using res
            if (
              drawingsToUpdate.every(
                (d) => d.container_id === parseInt(itemToAdd)
              )
            )
              return drawingsGridOptionsApi.forEachNodeAfterFilter((node) =>
                node.setSelected(false)
              );

            updateContainers(drawingsToUpdate);
          });
        }

        return updateContainers(drawingsToUpdate);
      }
    } else {
      // creating new container
      return handleCreateNewContainer(updatedObjValue);
    }
  };

  const massUpdateWork = async () => {
    const selectedIds = selectedRows.map((item) => item.id).join(",");
    let field = Object.keys(editedFields[0])[0];
    let value = Object.values(editedFields[0])[0];

    switch (currentTable) {
      case "JOBS":
        dispatch(
          handleUpdateJobs(
            selectedIds,
            editedFields,
            selectedIdsInFilters[0].length ? null : [showAllWork, false]
          )
        ).then((res) => {
          if (!res.error && selectedIdsInFilters[0].length) {
            selectedIds.split(",").forEach((id, index) => {
              const rowNode = jobsGridOptionsApi.getRowNode(parseInt(id));
              rowNode.setData(res[index]);
            });
          }
          dispatch(handleUpdateJobsNoRefresh(res));
        });
        break;
      case "PACKAGES":
        dispatch(handleUpdatePackages(selectedIds, editedFields)).then(
          (res) => {
            if (!res.error && selectedIdsInFilters[1].length) {
              handleSelectedStateUpdates(
                selectedPackages,
                res,
                setSelectedPackages
              );
              dispatch(handleUpdatePackagesNoRefresh(res));
              const newSelectedPackages = [];
              const changedPackages = [];

              selectedIds.split(",").forEach((id, index) => {
                const rowNode = packagesGridOptionsApi.getRowNode(parseInt(id));
                if (field === "due_date") field = "due_date_unix";

                // need to update selectedPackages specifically here when updating on_hold value
                if (field === "on_hold") {
                  const rowData = selectedPackages.find(
                    (p) => p.id === parseInt(id)
                  );
                  const newRowData = {
                    ...rowData,
                    on_hold: parseInt(value),
                  };
                  changedPackages.push(newRowData);
                } else {
                  return rowNode.setData(res[index]);
                }
              });
              if (field === "on_hold") {
                const changedPackageIds = changedPackages.map((p) => p.id);
                selectedPackages.forEach((sp) => {
                  if (changedPackageIds.includes(sp.id)) {
                    newSelectedPackages.push(
                      changedPackages.find((cp) => cp.id === sp.id)
                    );
                  } else newSelectedPackages.push(sp);
                });
                setSelectedPackages(newSelectedPackages);
                packagesGridOptionsApi.redrawRows();
              }
            }
          }
        );

        break;
      case "DRAWINGS":
        dispatch(
          handleUpdateDrawings(selectedIds, editedFields, "update")
        ).then((res) => {
          if (!res.error && selectedIdsInFilters[2].length) {
            const newSelectedDrawings = [];
            const changedDrawings = [];

            selectedIds.split(",").forEach((id, index) => {
              const rowNode = drawingsGridOptionsApi.getRowNode(parseInt(id));
              if (field === "area") field = "drawing_area";
              if (field === "container_id") field = "container_name";
              if (field === "laydown_location_id") field = "laydown_location";

              // need to update selectedPackages specifically here when updating on_hold value
              if (field === "on_hold") {
                const rowData = selectedDrawings.find(
                  (p) => p.id === parseInt(id)
                );
                const newRowData = {
                  ...rowData,
                  on_hold: parseInt(value),
                };

                changedDrawings.push(newRowData);
              } else {
                return rowNode.setData(res[index]);
              }
            });
            if (field === "on_hold") {
              const changedDrawingIds = changedDrawings.map((d) => d.id);
              selectedDrawings.forEach((sd) => {
                if (changedDrawingIds.includes(sd.id)) {
                  newSelectedDrawings.push(
                    changedDrawings.find((cd) => cd.id === sd.id)
                  );
                } else newSelectedDrawings.push(sd);
              });
              setSelectedDrawings(newSelectedDrawings);
              drawingsGridOptionsApi.redrawRows();
            }
          }
          handleSelectedStateUpdates(
            selectedDrawings,
            res,
            setSelectedDrawings
          );
          dispatch(handleUpdateDrawingsNoRefresh(res));
        });
        break;
      case "ITEMS":
        if (
          editedFields[0].package_number !== undefined ||
          editedFields[0].package_area !== undefined ||
          editedFields[0].package_due_date !== undefined
        ) {
          if (
            editedFields[0].package_due_date !== undefined &&
            editedFields[0].package_due_date === ""
          )
            return;

          const updatedData = {
            [selectedField.slice(8)]: editedFields[0][selectedField],
          };

          dispatch(
            handleUpdatePackages(
              Array.from(new Set(selectedRows.map((r) => r.package_id))).join(
                ","
              ),
              updatedData
            )
          ).then((res) => {
            if (!res.error) {
              dispatch(
                handleFetchWorkTableItems(
                  selectedJobs.map((j) => j.id),
                  selectedPackages.map((p) => p.id),
                  selectedDrawings.map((d) => d.id),
                  selectedStages.map((s) => s.id),
                  showAllWork,
                  selectedGrouping !== 0 && selectedStages.length === 1
                    ? selectedStages[0].groupable
                    : 0,
                  selectedGroupableColumns
                )
              );
            }
          });
        } else if (editedFields[0].drawing_due_date !== undefined) {
          if (editedFields[0].drawing_due_date === "") return;

          const updatedData = {
            [selectedField.slice(8)]: editedFields[0][selectedField],
          };
          dispatch(
            handleUpdateDrawings(
              Array.from(new Set(selectedRows.map((r) => r.drawing_id))).join(
                ","
              ),
              updatedData,
              "update",
              [selectedIdsInFilters[1], showAllWork]
            )
          ).then((res) => {
            if (!res.error) {
              dispatch(
                handleFetchWorkTableItems(
                  selectedJobs.map((j) => j.id),
                  selectedPackages.map((p) => p.id),
                  selectedDrawings.map((d) => d.id),
                  selectedStages.map((s) => s.id),
                  showAllWork,
                  selectedGrouping !== 0 && selectedStages.length === 1
                    ? selectedStages[0].groupable
                    : 0,
                  selectedGroupableColumns
                )
              );
            }
          });
        } else {
          if (editedFields[0].size !== undefined) {
            if (
              !multidimensionRegex.test(editedFields[0].size) ||
              !multidimensionOnlyX.test(editedFields[0].size)
            )
              return;
          }

          if (fracFields.find((f) => f === selectedField)) {
            if (
              !fractionalRegex.test(editedFields[0][selectedField]) &&
              updatedValue !== ""
            )
              return;
            // dont want to run convertFracToDecimal if value is null (clearing out value)
            editedFields[0] = {
              [selectedField]: !fractionalRegex.test(
                editedFields[0][selectedField]
              )
                ? ""
                : convertFracToDec(editedFields[0][selectedField]),
            };
          }

          if (
            (typeof editedFields[0][selectedField] === "string" &&
              editedFields[0][selectedField].trim() === "") ||
            editedFields[0][selectedField] === 0
          )
            editedFields[0] = { [selectedField]: null };

          dispatch(handleUpdateItems(selectedIds, editedFields)).then((res) => {
            if (res.error) return;
            selectedIds.split(",").forEach((id, index) => {
              const rowNode = itemsGridOptionsApi.getRowNode(parseInt(id));
              rowNode.setData(res[index]);
            });
          });
        }
        break;
      default:
        return;
    }
  };

  useEffect(() => {
    if (selectedField !== "heat_number") return;
    if (!showHeatNumberDropdown)
      dispatch(
        notify({ id: Date.now(), type: "WARN", message: "No heat numbers." })
      );
  }, [selectedField, showHeatNumberDropdown]);

  const refreshGroupedItems = () => {
    dispatch(
      handleFetchWorkTableItems(
        ...selectedIdsInFilters,
        1,
        selectedGroupableColumns
      )
    );
  };

  const handleSubmit = () => {
    toggleSaving(true);
    // make sure a new item isn't being created and a joint heat number isn't being updated
    if (
      ["JOBS", "PACKAGES", "DRAWINGS"].includes(currentTable) &&
      !createNew &&
      ![
        "container_id",
        "complete",
        "uncomplete",
        "DELETE_ITEMS",
        "joint_heat_number",
      ].includes(selectedField)
    ) {
      return massUpdateWork();
    }

    const itemIds = selectedRows.map((row) =>
      selectedGrouping ? row.work_item_ids : row.id
    );

    if (
      selectedGrouping &&
      !["complete", "uncomplete"].includes(selectedField)
    ) {
      const quantity = selectedRows.reduce((acc, curr) => {
        // when item is not cut, we need to return the actual quantity of row items
        if (!curr.quantity)
          return (
            acc + (curr.is_cut == 0 ? curr.work_item_ids?.split(",").length : 1)
          );
        return acc + curr.quantity;
      }, 0);

      if (selectedField === "DELETE_ITEMS") {
        return handleDeleteConfirmation();
      }

      // typical format for updating grouped
      let newValue = { [selectedField]: updatedValue };

      if (selectedField === "material_type_id") {
        dispatch(
          notify({
            id: Date.now(),
            type: "WARN",
            message:
              "If selected material does not match the conditions for this stage, the item may no longer be available at this stage.",
          })
        );

        // exception when creating new material, it's an object from UpdateDropdown
        if (updatedValue?.material_name) {
          newValue = updatedValue;
        }
      }

      return dispatch(
        handleUpdateGroupedItems(
          itemIds.toString(),
          newValue,
          quantity,
          refreshGroupedItems
        )
      );
    }

    switch (selectedField) {
      case "complete":
        const itemsAtPending = selectedRows.filter(
          (item) => item.drawing_pending_approval
        );

        itemsAtPending?.length &&
          dispatch(
            notify({
              id: Date.now(),
              type: "WARN",
              message:
                "Some item(s) weren't able to be completed because their drawing(s) are at pending approval.",
            })
          );

        const itemsToComplete = itemsAtPending?.length
          ? selectedRows.filter(
              (row) => !itemsAtPending.map((i) => i.id).includes(row.id)
            )
          : selectedRows;

        // if there are items to complete run the function else just return an empty promise
        return itemsToComplete?.length
          ? handleCompleteUncompleteItem(1, itemsToComplete)
          : Promise.resolve();
      case "uncomplete":
        return handleCompleteUncompleteItem(0);
      case "laydown_location_id":
        if (!updatedValue.laydown_location_name.trim()) break;
        return handleCreateNewLaydownLocation(
          selectedRows,
          updatedValue.laydown_location_name
        );
      case "material_type_id":
        dispatch(
          notify({
            id: Date.now(),
            type: "WARN",
            message:
              "If selected material does not match the conditions for this stage, the item may no longer be available at this stage.",
          })
        );
        // updatedValue can be an id of existing material type
        if (Number.isInteger(updatedValue) && typeof updatedValue !== "string")
          return massUpdateWork();
        // or object with new material name
        return handleCreateNewMaterialType(updatedValue.material_name);
      case "joint_heat_number1":
        return handleJointUpdate(
          {
            position: selectedField.slice(-1),
            heat_number:
              updatedValue.heat_number === "0"
                ? null
                : updatedValue.heat_number,
          },
          itemIds
        );
      case "joint_heat_number2":
        return handleJointUpdate(
          {
            position: selectedField.slice(-1),
            heat_number:
              updatedValue.heat_number === "0"
                ? null
                : updatedValue.heat_number,
          },
          itemIds
        );
      case "container_id":
        return handleContainerUpdate();
      case "DELETE_ITEMS":
        return handleDeleteConfirmation();
      default:
        return massUpdateWork();
    }
  };

  const handleDeleteConfirmation = () => {
    return dispatch(
      handleDeleteItem(selectedRows.map((r) => r.id).join(","))
    ).then(() => {
      return dispatch(handleFetchWorkTableItems(...selectedIdsInFilters));
    });
  };

  const disableSubmit = useMemo(() => {
    if (saving) return true;

    if (selectedField === "length") {
      if (updatedValue === "") toggleError(false);
      else {
        if (!fractionalRegex.test(updatedValue)) {
          toggleError(`Please include term of measurement (ex. 2")`);
          return true;
        } else toggleError(false);
      }
    }

    if (selectedField === "complete" || selectedField === "uncomplete")
      return false;
    if (!selectedField) return true;
    if (!updatedValue && selectedField === "on_hold") return true;
    if (selectedField === "material_type_id" && !updatedValue) return true;

    if (selectedField === "heat_number") return false;

    if (selectedField === "container_id") {
      if (!editType) return true;
      if (editType === "EXISTING" && itemToAdd) return false;
      if (
        attachedContainers &&
        attachedContainers.length &&
        !updatedObjValue &&
        itemToAdd !== ""
      ) {
        return true;
      } else if (
        editType === "NEW" &&
        (!updatedObjValue || !updatedObjValue.name)
      )
        return true;
      else return false;
    }

    return false;
  }, [
    selectedField,
    updatedValue,
    updatedObjValue,
    itemToAdd,
    attachedContainers,
    editType,
  ]);

  useEffect(() => {
    if (selectedField !== "container_id") {
      setUpdatedObjValue(null);
      setItemToAdd(null);
      setEditType(null);
    }
  }, [selectedField]);

  useEffect(() => {
    setUpdatedObjValue(null);
  }, [itemToAdd]);

  const selectableLaydownLocations = useMemo(() => {
    if (laydown_locations) {
      return [{ id: 0, name: null }, ...laydown_locations];
    } else return [];
  }, [laydown_locations]);

  return (
    <Modal
      title={`Update ${currentTable.charAt(0)}${currentTable
        .slice(1)
        .toLowerCase()}`}
      showModal={true}
      onClose={onClose}
      onSubmit={() => {
        // don't trigger .then for create container since
        // it's returning the result differently
        if (
          editType === "NEW" &&
          currentTable === "DRAWINGS" &&
          !createNew &&
          selectedField === "container_id"
        ) {
          handleSubmit();
        } else {
          handleSubmit().then((res) => {
            toggleSaving(false);
            if (error) return;
            onClose(true);
            setSelectedRows([]);
          });
        }
      }}
    >
      <div className="field-selector-wrapper">
        <span>
          <div className="column-select-wrapper">
            <label>Column Name</label>
            <select
              onChange={(e) => setSelectedField(e.target.value)}
              className="column-select"
            >
              <option hidden defaultValue>
                Select a field
              </option>
              {values &&
                values.length &&
                values.map((value, index) => (
                  <option key={value} value={keys[index]}>
                    {value}
                  </option>
                ))}
            </select>
          </div>
          {selectedField && selectedField !== "DELETE_ITEMS" && (
            <div className="column-value-wrapper">
              {loadingDrawings ? (
                <></>
              ) : (
                <>
                  {fieldType && <label>Updated Value</label>}
                  {fieldType === "string" &&
                  selectedField &&
                  !selectedField.includes("joint_heat_number") &&
                  !selectedField.includes("container_id") ? (
                    selectedField === "due_date" ||
                    selectedField === "target_date" ||
                    selectedField === "package_due_date" ||
                    selectedField === "drawing_due_date" ? (
                      <DueDateCellRenderer
                        valueFormatted={{
                          value: updatedValue ? updatedValue : 0,
                          editing: true,
                          placeholder: "MM/DD/YYYY",
                          setDueDate: (e) =>
                            setUpdatedValue(
                              e
                                ? moment(new Date(e)).format("MM-DD-yyyy")
                                : currentTable !== "JOBS" // not clearable
                                ? ""
                                : 0
                            ),
                        }}
                      />
                    ) : (
                      <input
                        onChange={(e) => setUpdatedValue(e.target.value)}
                        type="text"
                        placeholder={"updated value"}
                        value={updatedValue}
                      />
                    )
                  ) : undefined}
                  {fieldType === "integer" && (
                    <input
                      onChange={(e) => setUpdatedValue(e.target.value)}
                      type="number"
                      placeholder="updated number"
                      value={updatedValue}
                    />
                  )}
                  {fieldType === "dropdown" && selectedField === "stage_id" && (
                    <select
                      onChange={(e) => setUpdatedValue(e.target.value)}
                      className="select"
                    >
                      {stages &&
                        stages.length &&
                        stages.map((stage) => (
                          <option key={stage.id} value={stage.id}>
                            {stage.name}
                          </option>
                        ))}
                    </select>
                  )}
                  {selectedField === "joining_procedure_name" && (
                    <select
                      onChange={(e) => setUpdatedValue(e.target.value)}
                      className="select"
                    >
                      {joiningProcedures &&
                        joiningProcedures.length &&
                        joiningProcedures.map((jp) => (
                          <option key={jp.id} value={jp.name}>
                            {jp.name}
                          </option>
                        ))}
                    </select>
                  )}
                  {selectedField === "on_hold" && <OnHoldDropdown />}
                  {selectedField &&
                    selectedField.includes("joint_heat_number") && (
                      <JointHeatNumber
                        heatNumbers={heatNumbers}
                        setUpdatedValue={setUpdatedValue}
                        selectedRows={selectedRows}
                      />
                    )}
                  {selectedField === "heat_number" ? (
                    showHeatNumberDropdown ? (
                      <JointHeatNumber
                        heatNumbers={heatNumbers}
                        setUpdatedValue={setUpdatedValue}
                        selectedRows={selectedRows}
                        type="heat_number"
                      />
                    ) : (
                      <input
                        onChange={(e) => setUpdatedValue(e.target.value)}
                        type="text"
                        placeholder={
                          selectedField === "due_date" ||
                          selectedField === "target_date" ||
                          selectedField === "package_due_date" ||
                          selectedField === "drawing_due_date"
                            ? "MM/DD/YYYY"
                            : "updated value"
                        }
                        value={updatedValue}
                      />
                    )
                  ) : (
                    <></>
                  )}
                  {selectedField === "laydown_location_id" && (
                    <UpdateDropdown
                      handleCreate={handleCreateLaydownLocation}
                      handleEdit={massUpdateWork}
                      setUpdatedValue={setUpdatedValue}
                      selectableList={selectableLaydownLocations}
                      keyValue="laydown_location_name"
                      valueToUpdate="laydown_location_id"
                      type="Laydown Location"
                      toggleCreateNew={toggleCreateNew}
                      selectedRows={selectedRows}
                    />
                  )}
                  {selectedField === "material_type_id" && (
                    <UpdateDropdown
                      handleCreate={handleCreateNewMaterialType}
                      handleEdit={massUpdateWork}
                      setUpdatedValue={setUpdatedValue}
                      selectableList={materialTypes}
                      keyValue="material_name"
                      valueToUpdate="material_type_id"
                      type="Material Type"
                      toggleCreateNew={toggleCreateNew}
                      selectedRows={selectedRows}
                    />
                  )}
                  {selectedField === "container_id" && (
                    <ContainerForm
                      setEditedValues={setUpdatedObjValue}
                      editedValues={updatedObjValue}
                      containerList={containers}
                      setItemToAdd={setItemToAdd}
                      itemToAdd={itemToAdd}
                      editType={editType}
                      setEditType={setEditType}
                      selectedRows={selectedRows}
                      attachedContainers={attachedContainers}
                    />
                  )}
                </>
              )}
            </div>
          )}
        </span>
        {selectedField === "DELETE_ITEMS" && (
          <p className="date-error">
            Are you sure you want to delete the selected items?
          </p>
        )}
        {error && <p className="date-error">{error}</p>}
        <div className="button-row">
          <button
            disabled={disableSubmit}
            className={
              !selectedField ||
              (!updatedValue && !isDate && selectedField !== "DELETE_ITEMS") ||
              isDate
                ? "disabled save"
                : selectedField === "DELETE_ITEMS"
                ? "warn save"
                : "save"
            }
            type="submit"
          >
            {`${
              selectedField && selectedField === "DELETE_ITEMS"
                ? "Delete"
                : "Update"
            } (${selectedRows.length})`}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default MassUpdate;
