@import "../../styles/colors.scss";

div.field-selector-wrapper {
  min-width: 400px;
  min-height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;

  & p.date-error {
    color: $red;
    font-size: 0.9rem;
  }

  & label {
    font-size: 0.7rem;
    font-weight: bold;
    color: $lighterGrey;
    margin-bottom: 10px;
  }

  & div.column-select-wrapper {
    display: flex;
    flex-direction: column;
  }

  & div.column-value-wrapper {
    display: flex;
    flex-direction: column;
    width: 180px;
  }

  & span {
    display: flex;
    justify-content: space-between;
    width: 400px;

    & select.column-select {
      background-color: transparent;
      border: none;
      border-bottom: 1px solid $dropdownBlue;
      color: black;
      width: 160px;
      height: 30px;
      margin-right: 20px;

      & option {
        // background-color: $offGrey;
        // color: $lighterSlate;
        color: black;
        background-color: white;
      }
    }
    & input[type="text"],
    & input[type="number"] {
      background-color: #eee;
      border: 1px solid #777;
      border-radius: 3px;
      height: 25px;
      width: 140px;
      padding-left: 5px;
      color: black;

      &:focus {
        outline: none;
        border: 1px solid $fabProBlue;
      }
    }

    & input[type="text"].invalid-date {
      border: 2px solid $red;
    }
    & .on-hold-select option.default {
      color: #ccc;
      cursor: default;
    }
  }

  select.on-hold-select option.default {
    color: #ccc;
    cursor: default;
  }

  & .button-row {
    display: flex;
    margin-left: auto;
    margin-top: 10px;
    width: 125px;
    justify-content: space-between;

    & button {
      width: 100%;
      height: 28px;
      padding: 0 15px;
      border-radius: 3px;
      color: white;
      font-size: 0.8rem;
      cursor: pointer;
    }
    & button.save {
      background-color: $blue;
      border: none;

      &:disabled {
        opacity: 0.5;
        color: white !important;
        cursor: default;
      }
    }
    & button.warn {
      background-color: $red;
    }
  }

  & select.container-edit-select {
    height: 28px;
  }
}

div.results-wrapper {
  max-height: 400px;
}

select.select {
  background-color: transparent;
  border: 1px solid $blue;
  color: black;
  width: 140px;
  height: 28px;
  border-radius: 3px;

  & option {
    background-color: $offGrey;
    color: $lighterSlate;
  }
}

div.container-new {
  // width: 500px;
  // height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  & input[type="text"] {
    margin-bottom: 8px;
  }
}

div.edit-container-form {
  // height: 200px;
  display: flex;
  flex-direction: column;
  width: 200px;

  & select.container-edit-select {
    width: 90%;
    border: 1px solid #ccc;
    margin-bottom: 20px;
  }

  & div.container-info-wrapper {
    & h6 {
      padding: 0;
      margin: 0 0 5px;
      font-size: 0.8rem;
      color: $lighterGrey;
    }
    & label {
      display: block;
      margin-bottom: 0;
      color: lighterGrey;
    }
    & input {
      margin-bottom: 5px;
      width: 90%;
    }
  }
}
