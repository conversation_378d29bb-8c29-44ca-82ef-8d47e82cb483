// NPM PACKAGE IMPORTS
import React, { useEffect, useState } from "react";
import { FiSearch } from "react-icons/fi";

// STYLES IMPORTS
import "./stylesJointHeatNumber.scss";

const JointHeatNumber = ({
  setUpdatedValue,
  heatNumbers,
  selectedRows,
  type = "joint_heat_number",
}) => {
  const [searchInput, setSearchInput] = useState("");
  const [createInput, setCreateInput] = useState("");
  const [displayedOptions, setDisplayedOptions] = useState([]);
  const [selectedHeatNumber, setSelectedHeatNumber] = useState("");

  const handleSelection = (heatNumber) => {
    if (selectedHeatNumber === heatNumber) return setSelectedHeatNumber(null);
    setSelectedHeatNumber(heatNumber);

    if (type === "heat_number") setUpdatedValue(heatNumber.heat_number);
    else setUpdatedValue({ heat_number: heatNumber });
  };

  useEffect(() => {
    if (selectedRows.length > 1 || !heatNumbers) return;

    const current = heatNumbers.find(
      (hn) => hn.heat_number === selectedRows[0].heat_number
    );

    if (current && type === "heat_number")
      setSelectedHeatNumber(current.heat_number);
  }, [selectedRows, heatNumbers]);

  useEffect(() => {
    if (!heatNumbers || !heatNumbers.length) return;
    setDisplayedOptions(heatNumbers);
  }, [heatNumbers]);

  useEffect(() => {
    if (createInput.length) setSelectedHeatNumber(null);
  }, [createInput]);

  useEffect(() => {
    if (selectedHeatNumber) setCreateInput("");
  }, [selectedHeatNumber]);

  useEffect(() => {
    if (!heatNumbers) return;
    if (!searchInput) return setDisplayedOptions(heatNumbers);

    const f_options = heatNumbers.filter((hn) =>
      hn.heat_number.toLowerCase().includes(searchInput.toLowerCase())
    );
    setDisplayedOptions(f_options);
  }, [searchInput, heatNumbers]);

  const handleNewValueChange = (value) => {
    setCreateInput(value);
    if (!selectedHeatNumber) {
      if (type === "heat_number") {
        setUpdatedValue(value);
      } else setUpdatedValue({ heat_number: value });
    }
  };

  return (
    <div className="joint-wrapper">
      <span className="search-wrapper">
        <FiSearch />
        <input
          placeholder="Search"
          onChange={(e) => setSearchInput(e.target.value)}
          type="text"
        />
      </span>
      <input
        placeholder="New heat number"
        className="heat-number-input"
        type="text"
        onChange={(e) => handleNewValueChange(e.target.value)}
        value={createInput}
      />
      {displayedOptions && displayedOptions.length ? (
        <ul>
          {displayedOptions.map((hn) => (
            <li
              key={`${hn.heat_number} ${hn.material_name}`}
              className={
                selectedHeatNumber === hn.heat_number ? "selected" : ""
              }
              onClick={() => handleSelection(hn.heat_number)}
            >
              {hn.heat_number}
            </li>
          ))}
          {type !== "heat_number" && (
            <li
              key="null"
              onClick={() => handleSelection("0")}
              className={selectedHeatNumber === null ? "selected" : ""}
            ></li>
          )}
        </ul>
      ) : (
        <p>No heat numbers</p>
      )}
    </div>
  );
};

export default JointHeatNumber;
