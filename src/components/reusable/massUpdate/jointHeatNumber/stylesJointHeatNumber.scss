@import "../../../styles/colors.scss";

div.joint-wrapper {
  width: 180px;
  border-radius: 3px;
  min-height: 150px;
  max-height: 300px;
  height: 150px;
  margin-bottom: 20px;

  & span.search-wrapper {
    display: flex;
    // justify-content: space-between;
    align-items: center;
    height: 32px;
    width: 180px;
    border: 1px solid #ccc;
    box-sizing: border-box;
    padding: 0 3px;

    & input[type="text"] {
      width: 145px;
      outline: none;
      border: none;
      margin-left: 3px;
      background-color: white !important;

      &:focus {
        border: none !important;
      }
    }

    & svg {
      color: $blue;
      stroke-width: 2px;
      font-size: 1.2rem;
      width: 15px !important;
    }
  }

  & input.heat-number-input[type="text"] {
    width: 180px;
    height: 32px;
    box-sizing: border-box;
    font-size: 0.8rem;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-top: none;
    border-bottom: none;
    padding-left: 5px;
    border-radius: 0 !important;
    background-color: white !important;
    box-sizing: border-box;

    &:focus {
      border: 1px solid $blue;
      outline: none;
    }
  }

  & ul {
    margin: 0;
    padding: 0 0 0 0;
    // background-color: #ccc;
    max-height: 100px;
    overflow-y: auto;
    border: 1px solid #ccc;
    & li {
      min-height: 31px;
      box-sizing: border-box;
      font-size: 0.8rem;
      padding: 8px 5px;
      list-style-type: none;
      cursor: pointer;
      color: black;

      &:hover {
        background-color: darken(#ccc, 10%);
      }
    }

    & .selected {
      background-color: $blue;
      color: white;
    }
  }

  & p {
    color: black;
    text-align: center;
    font-size: 0.8rem;
    border-bottom: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    margin: 0;
    padding: 5px 0;
  }
}
