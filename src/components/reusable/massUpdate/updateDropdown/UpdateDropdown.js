// NPM PACKAGE IMPORTS
import React, { useEffect, useState } from "react";
import { FiSearch } from "react-icons/fi";

// STYLES IMPORTS
import "./stylesUpdateDropdown.scss";

const UpdateDropdown = ({
  handleEdit,
  handleCreate,
  setUpdatedValue,
  selectableList,
  currentValue,
  keyValue,
  valueToUpdate,
  toggleCreateNew,
  type,
  selectedRows,
}) => {
  const [searchInput, setSearchInput] = useState("");
  const [createInput, setCreateInput] = useState("");
  const [displayedOptions, setDisplayedOptions] = useState([]);
  const [selectedValue, setSelectedValue] = useState(null);

  const handleSelection = (item) => {
    if (currentValue === item.name) return setSelectedValue(null);
    setSelectedValue(item);
    if (setUpdatedValue) setUpdatedValue(item.id);
    return toggleCreateNew(false);
  };

  useEffect(() => {
    if (!selectableList || !selectableList.length || selectedRows.length !== 1)
      return;

    const currentItem = selectableList.find(
      (i) => i.name === selectedRows[0][keyValue]
    );

    setSelectedValue(currentItem);
  }, [selectedRows, selectableList]);

  useEffect(() => {
    if (createInput.length) setSelectedValue(null);
  }, [createInput]);

  useEffect(() => {
    if (selectedValue) setCreateInput("");
  }, [selectedValue]);

  useEffect(() => {
    if (!selectableList || !selectableList.length)
      return setDisplayedOptions([]);
    setDisplayedOptions(selectableList);
  }, [selectableList]);

  useEffect(() => {
    if (!selectableList || !selectableList.length) return;
    if (!searchInput || !searchInput.length)
      return setDisplayedOptions(selectableList);
    const f_list = selectableList.filter((item) =>
      item.name.toLowerCase().includes(searchInput.toLowerCase())
    );

    setDisplayedOptions(f_list);
  }, [searchInput, selectableList, keyValue]);

  const handleNewValueChange = (value) => {
    setCreateInput(value);
    if (!selectedValue) {
      toggleCreateNew(true);
      setUpdatedValue({ [keyValue]: value });
    }
  };

  return (
    <div className="update-dropdown-wrapper">
      <span className="search-wrapper">
        <FiSearch />
        <input
          placeholder="Search"
          onChange={(e) => setSearchInput(e.target.value)}
          type="text"
        />
      </span>
      <input
        placeholder={`New ${type}`}
        className="create-input"
        type="text"
        onChange={(e) => handleNewValueChange(e.target.value)}
        value={createInput}
      />
      {displayedOptions && displayedOptions.length ? (
        <ul>
          {displayedOptions.map((item) => (
            <li
              value={item.id}
              key={item.id}
              className={
                selectedValue && selectedValue.name === item.name
                  ? "selected"
                  : ""
              }
              onClick={() => handleSelection(item)}
            >
              {item.name}
            </li>
          ))}
        </ul>
      ) : (
        <p>{`No results`}</p>
      )}
    </div>
  );
};

export default UpdateDropdown;
