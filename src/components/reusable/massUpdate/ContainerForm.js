// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useMemo } from "react";

// STYLES IMPORTS
import "./massUpdate.scss";

const ContainerForm = ({
  containerList,
  setEditedValues,
  editedValues,
  setItemToAdd,
  itemToAdd,
  setEditType,
  attachedContainers,
  editType,
}) => {
  const [containerToUpdate, setContainerToUpdate] = useState(null);

  useEffect(() => {
    if (containerToUpdate === "" || containerToUpdate === null) {
      setItemToAdd("");
      setEditedValues({ ...editedValues, container_id: null });
    } else if (containerToUpdate) {
      setItemToAdd(containerToUpdate);
    }
  }, [containerToUpdate]);

  const selectedContainer = useMemo(() => {
    if (!itemToAdd || !parseInt(itemToAdd)) return {};
    return containerList.find((c) => c.id === parseInt(itemToAdd));
  }, [itemToAdd]);

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    event.persist();
    setEditedValues({ ...editedValues, [name]: value });
  };

  const selectableContainers = useMemo(() => {
    if (attachedContainers && attachedContainers.length)
      return attachedContainers;
    else if (containerList && containerList.length) return containerList;
    else return [];
  }, [containerList, attachedContainers]);

  if (!editType)
    return (
      <select
        className="container-edit-select"
        onChange={(e) => setEditType(e.target.value)}
      >
        <option selected value="" hidden disabled>
          --Select action--
        </option>
        {!attachedContainers?.length && (
          <option key="new" value="NEW">
            Create new container
          </option>
        )}
        {containerList && containerList.length && (
          <option key="existing" value="EXISTING">
            Edit existing container
          </option>
        )}
      </select>
    );

  if (editType === "NEW") {
    return (
      <div className="container-new">
        <input
          onChange={(e) => handleInputChange(e)}
          name="name"
          placeholder="Container Name"
          type="text"
        />
        <input name="description" placeholder="Description" type="text" />
        <input
          name="laydown_location"
          placeholder="Laydown Location"
          type="text"
        />
      </div>
    );
  }

  if (editType === "EXISTING") {
    return (
      <div className="edit-container-form">
        <select
          onChange={(e) => setContainerToUpdate(e.target.value)}
          className="container-edit-select"
        >
          <option hidden selected>
            Select container
          </option>
          <option value={""}>{`(no container)`}</option>
          {selectableContainers &&
            selectableContainers.length &&
            selectableContainers.map((c) => (
              <option key={c.id} value={c.id}>
                {c.name}
              </option>
            ))}
        </select>
        {containerToUpdate && containerToUpdate !== "remove" && (
          <div className="container-info-wrapper">
            <h6>Update container info:</h6>
            <label>Name</label>
            <input
              onChange={(e) => handleInputChange(e)}
              name="name"
              disabled={!containerToUpdate}
              type="text"
              placeholder={selectedContainer ? selectedContainer.name : ""}
            />
            <label>Laydown Location</label>
            <input
              onChange={(e) => handleInputChange(e)}
              name="laydown_location"
              disabled={!containerToUpdate}
              type="text"
              placeholder={
                selectedContainer ? selectedContainer.laydown_location : ""
              }
            />
            <label>Description</label>
            <input
              onChange={(e) => handleInputChange(e)}
              name="description"
              disabled={!containerToUpdate}
              type="text"
              placeholder={
                selectedContainer ? selectedContainer.description : ""
              }
            />
          </div>
        )}
        {containerToUpdate && containerToUpdate === "" && (
          <p>{`*Remove container from drawing`}</p>
        )}
      </div>
    );
  }
};

export default ContainerForm;
