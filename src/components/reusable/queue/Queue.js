// NPM PACKAGE IMPORTS
import PropTypes from "prop-types";
import { v4 as uuid } from "uuid";

function Queue(type) {
  this.data = [];

  this.updateQueue = function (id, value) {
    switch (type) {
      case "SPINNER":
        return (this.data = this.data.map((n) => {
          if (n.id === id) {
            n = {
              ...n,
              ...value,
            };
          }
          return n;
        }));
      default:
        return;
    }
  };
  this.enqueue = function (value) {
    switch (type) {
      case "MESSAGE":
        return this.data.push(new MessageNode(value));
      case "SPINNER":
        return this.data.push(new SpinnerNode(value));
      default:
        return;
    }
  };

  this.dequeue = function (id) {
    switch (type) {
      case "MESSAGE":
        this.data = this.data.map((n) => {
          if (n.id === id) n.shown = true;
          return n;
        });
        return;
      case "SPINNER":
        this.data = this.data.map((n) => {
          if (n.id === id) n.shown = true;
          return n;
        });
        return;
      default:
        return;
    }
  };
}

function MessageNode({ type, message }) {
  return {
    id: uuid(),
    type,
    message,
    shown: false,
  };
}

function SpinnerNode(id) {
  return { id, shown: false, pending: true };
}

Queue.propTypes = {
  type: PropTypes.string,
};

MessageNode.propTypes = {
  type: PropTypes.string,
  message: PropTypes.string,
};

export default Queue;
