// NPM PACKAGE IMPORTS
import React, { useState, useMemo, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "msuite_storybook/dist/modal/Modal";
import Select from "msuite_storybook/dist/select/Select";
import Button from "msuite_storybook/dist/button/Button";
import { CgArrowRight } from "react-icons/cg";

// REDUX IMPORTS
import { handleFetchFlows } from "../../flows/flowsActions";

// STYLE IMPORTS
import "./stylesWorkflowModal.scss";

const WorkflowModal = ({ open, handleClose, handleSubmit, selectedItem }) => {
  const [newFlowValue, setNewFlowValue] = useState("DEFAULT");

  const dispatch = useDispatch();
  const { flows } = useSelector((state) => state.flowsData);

  useEffect(() => {
    dispatch(handleFetchFlows());
  }, []);

  const currentFlowName = useMemo(() => {
    let currentFlow = flows.find(
      (f) => f.id === (selectedItem.flow_id || selectedItem.work_flow_id)
    );
    if (currentFlow) return currentFlow.name;
    else return "";
  }, [flows, selectedItem]);

  const displayedFlows = useMemo(() => {
    return flows.map((f) => ({
      id: f.id,
      value: f.id,
      display: f.name,
    }));
  }, [flows]);

  return (
    <Modal open={open} handleClose={handleClose}>
      <div className={`workflow-modal ${selectedItem.name ? "drawing" : ""}`}>
        <h2 className="title">Change Workflow:</h2>
        <span className="value-label">Parent Job:</span>
        <span>{selectedItem.job_name || selectedItem.job_title}</span>
        {selectedItem.name ? (
          <>
            <span className="value-label">Parent Package:</span>
            <span>
              ({selectedItem.package_id}) {selectedItem.package_name}
            </span>
          </>
        ) : (
          <></>
        )}
        <span className="value-label">
          Selected {selectedItem.name ? "Drawing" : "Package"}:
        </span>
        <span className="item-name">
          {selectedItem.name ||
            `(${selectedItem.package_id || selectedItem.id}) ${
              selectedItem.package_name
            }`}
        </span>
        <span className="value-label">Current Workflow:</span>
        <span className="current-flow-name">{currentFlowName || "None"}</span>
        <span className="value-label">Target Workflow:</span>
        <Select
          options={displayedFlows}
          onInput={(e) => setNewFlowValue(e.target.value)}
          value={newFlowValue}
          placeholder="-- Target Workflow --"
        />
        <Button className="cancel" onClick={handleClose}>
          Cancel
        </Button>
        <Button
          className="submit"
          disabled={!newFlowValue || newFlowValue === "DEFAULT"}
          onClick={() => handleSubmit(parseInt(newFlowValue))}
        >
          Submit
          <CgArrowRight />
        </Button>
      </div>
    </Modal>
  );
};

export default WorkflowModal;
