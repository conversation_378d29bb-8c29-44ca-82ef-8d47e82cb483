@import "../../styles/colors.scss";

div.workflow-modal {
  background-color: #fff;
  width: 400px;
  height: calc(6 * 40px);

  display: grid;
  grid-template-rows: repeat(6, 30px);
  grid-template-columns: 1fr 1fr;
  row-gap: 10px;
  column-gap: 10px;
  align-items: center;

  &.drawing {
    height: calc(7 * 40px);
    grid-template-rows: repeat(7, 30px);
  }

  & > h2.title {
    grid-column: 1/-1;

    background-color: $fabProBlue;
    color: #fff;
    margin: 0;
    padding: 0 0 0 5px;
    font-size: 1rem;
    line-height: 30px;
  }

  & > span.value-label {
    padding-left: 10px;
  }

  & > span:not(.value-label) {
    padding-right: 10px;
    justify-self: end;
  }

  & > span.current-flow-name,
  & > span.item-name {
    color: $fabProBlue;
    font-weight: 600;
  }

  & > div {
    height: 30px;
    margin-right: 10px;

    & > div {
      height: 30px;

      & > select {
        height: 30px;
        font-size: 1rem;
      }
    }
  }

  & > button {
    height: 30px;
    padding: 0;
    border: 1px solid #333;
    font-size: 1rem;

    &.cancel {
      color: #333;
      background-color: #fff;
      margin-left: 10px;

      &:hover {
        background-color: darken(#fff, 10%);
      }
    }

    &.submit {
      color: #fff;
      background-color: $lightGreen;
      margin-right: 10px;

      &:not(:disabled):hover {
        background-color: darken($lightGreen, 10%);
      }

      & > svg {
        padding-left: 5px;
      }
    }
  }
}
