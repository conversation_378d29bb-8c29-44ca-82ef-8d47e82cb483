@import "../../styles/colors.scss";

div.submit-revision-modal {
  width: 500px;
  background-color: #fff;

  & > h2.title {
    background-color: $fabProBlue;
    color: #fff;
    margin: 0;
    padding: 5px 10px;
    font-size: 1rem;
  }

  & > div.content {
    display: grid;
    grid-template-areas:
      "desc desc desc desc"
      "input input input input"
      ". . . close";
    row-gap: 15px;

    padding: 10px;
  }

  & > div.content > p {
    grid-area: desc;
    padding: 0;
    margin: 0;
  }

  & > div.content > div.file-upload-container {
    grid-area: input;

    display: flex;
    column-gap: 10px;
  }

  & > div.content > div.file-upload-container > div {
    height: 30px;

    & > input {
      height: 30px;
      font-size: 1rem;
    }
  }

  & > div.content > div.file-upload-container > button {
    height: 30px;
    padding: 0 10px;
    font-size: 0.8rem;
    width: 100px;

    display: flex;
    align-items: center;
    justify-content: space-around;

    &.upload {
      background-color: $fabProBlue;
      color: #fff;
    }
  }

  & > div.content > button.cancel {
    grid-area: close;

    height: 30px;
    padding: 0 10px;
    font-size: 0.8rem;
    background-color: #fff;
  }
}
