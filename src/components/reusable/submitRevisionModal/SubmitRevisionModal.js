// NPM PACKAGE IMPORTS
import React, { useState } from "react";
import { useDispatch } from "react-redux";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import Input from "msuite_storybook/dist/input/Input";
import { FaTrashAlt } from "react-icons/fa";
import { RiUploadCloudLine } from "react-icons/ri";

// REDUX IMPORTS
import { handleSubmitRevisedPdf } from "../../drawings/drawingsActions";

// STYLES IMPORTS
import "./stylesSubmitRevisionModal.scss";

const SubmitRevisionModal = ({
  submitRevisionInfo,
  handleClose,
  updateRow,
}) => {
  const [fileToUpload, setFileToUpload] = useState(null);

  const dispatch = useDispatch();

  const submitRevisedPdf = () => {
    if (!fileToUpload) return;

    dispatch(
      handleSubmitRevisedPdf(submitRevisionInfo.id, fileToUpload.file)
    ).then((res) => {
      if (!res.error) {
        setFileToUpload(null);
        updateRow(res[0]);
        handleClose();
      }
    });
  };

  return (
    <Modal open={!!submitRevisionInfo} handleClose={handleClose}>
      <div className="submit-revision-modal">
        <h2 className="title">Submit a Revised PDF</h2>
        <div className="content">
          <p>Upload a new revision for the drawing</p>
          <div className="file-upload-container">
            <Input
              type="file"
              placeholder="No File Selected"
              className="file-upload"
              accept=".pdf,application/pdf"
              value={fileToUpload ? fileToUpload.fakepath : ""}
              onChange={(e) => {
                e.persist();
                setFileToUpload({
                  fakepath: e.target.value,
                  file: e.target.files[0],
                });
              }}
            />
            <Button
              className="upload"
              disabled={!fileToUpload}
              onClick={submitRevisedPdf}
            >
              <RiUploadCloudLine /> Upload
            </Button>
            <Button
              className="remove"
              disabled={!fileToUpload}
              onClick={() => setFileToUpload(null)}
            >
              <FaTrashAlt /> Remove
            </Button>
          </div>
          <Button
            className="cancel"
            onClick={() => {
              setFileToUpload(null);
              handleClose();
            }}
          >
            Close
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default SubmitRevisionModal;
