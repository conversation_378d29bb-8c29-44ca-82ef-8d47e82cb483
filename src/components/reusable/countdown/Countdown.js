import React, { useEffect, useRef } from "react";

// STYLES IMPORTS
import "./countdown.scss";

const Countdown = ({ onTimerEnd, seconds, setSeconds, showAt }) => {
  // useRef is used to make the counter stop at 0 https://dev.to/zhiyueyi/how-to-create-a-simple-react-countdown-timer-4mc3
  const timerRef = useRef(null);

  const clear = () => {
    window.clearInterval(timerRef.current);
  };

  useEffect(() => {
    if (setSeconds) {
      timerRef.current = setInterval(() => {
        setSeconds((second) => second - 1);
      }, 1000);

      return () => clear();
    }
  }, []);

  useEffect(() => {
    if (seconds === 0 && onTimerEnd) {
      clear();
      onTimerEnd();
    }
  }, [seconds, onTimerEnd]);

  return seconds !== 0 && (!showAt || seconds <= showAt) ? (
    <div className="countdown-timer">
      <p>{`${seconds}s`}</p>
    </div>
  ) : (
    <></>
  );
};

export default Countdown;
