import { Component } from "react";
import { logger } from "./../../../utils/_dataDogUtils";

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, retryCount: 0 };
  }

  componentDidCatch(error, errorInfo) {
    logger.error(this.props.message, { error, errorInfo });
    this.setState((p) => ({
      ...p,
      hasError: true,
    }));
  }

  componentDidUpdate() {
    if (!this.state.hasError) {
      return;
    }

    if (this.state.retryCount >= this.props.maxRetryCount) {
      return;
    }

    // Update state so the next render will mount the child component
    this.setState((p) => ({
      hasError: false,
      retryCount: ++p.retryCount,
    }));
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallbackNode ?? null;
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
