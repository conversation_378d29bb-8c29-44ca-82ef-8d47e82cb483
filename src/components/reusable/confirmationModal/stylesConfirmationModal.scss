@import "../../styles/colors.scss";

div.delete-archive-confirmation-modal {
  background-color: #fff;
  width: 400px;
  padding: 15px;

  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 15px;
  row-gap: 15px;
  justify-items: center;

  & > svg,
  & > p {
    grid-column: 1/-1;
    color: black;
  }

  & > svg {
    font-size: 6rem;

    &.DELETE {
      color: $red;
    }

    &.ARCHIVE,
    &.WARN,
    &.MOVE {
      color: $orange;
    }

    &.SAVE {
      color: $green;
    }
  }

  & > p span.action {
    font-weight: 600;

    &.DELETE {
      color: $red;
    }

    &.ARCHIVE,
    &.WARN,
    &.MOVE {
      color: $orange;
    }

    &.SAVE {
      color: $green;
    }
  }

  & > p span.item {
    color: $fabProBlue;
  }

  & > button {
    width: 150px;
    height: 40px;
    padding: 0;
    font-size: 1rem;

    &.cancel {
      background-color: #fff;
      border: 1px solid #333;
      color: #333;

      &:hover {
        background-color: darken(#fff, 10%);
      }
    }

    &.submit {
      color: #fff;
      border: 1px solid #333;

      &.DELETE {
        background-color: $red;

        &:hover {
          background-color: darken($red, 10%);
        }
      }

      &.ARCHIVE,
      &.WARN,
      &.MOVE {
        color: white;
        background-color: $orange;

        &:hover {
          background-color: darken($orange, 10%);
        }
      }

      &.SAVE {
        background-color: $green;

        &:hover {
          background-color: darken($green, 10%);
        }
      }
    }
  }
}
