// NPM PACKAGE IMPORTS
import React from "react";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";

// STYLES IMPORTS
import { BsExclamationCircle } from "react-icons/bs";
import "./stylesConfirmationModal.scss";

const ConfirmationModal = ({
  showModal,
  handleClick,
  action,
  item,
  toggleModal,
  message,
  submitText,
  cancelText,
  handleCancel,
}) => {
  return (
    <Modal open={showModal} handleClose={() => toggleModal(null)}>
      <div className="delete-archive-confirmation-modal">
        <BsExclamationCircle className={action} />
        {message ? (
          <p>{message}</p>
        ) : (
          <p>
            Are you sure you want to{" "}
            <span className={`action ${action}`}>{action}</span>{" "}
            <span className="item">{item}</span>?
          </p>
        )}
        <Button
          className="cancel"
          onClick={
            handleCancel ? () => handleCancel() : () => toggleModal(null)
          }
        >
          {cancelText || "Cancel"}
        </Button>
        <Button className={`submit ${action}`} onClick={() => handleClick()}>
          {submitText || action}
        </Button>
      </div>
    </Modal>
  );
};

export default ConfirmationModal;
