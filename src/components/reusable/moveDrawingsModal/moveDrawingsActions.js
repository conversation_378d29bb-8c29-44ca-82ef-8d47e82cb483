import { fetchJobs, fetchPackages } from "../../../_services";

export const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const handleFetchJobs = (dispatch) => {
  dispatch(receiveStarted("JOBS"));
  return fetchJobs(true, null).then(async (res) => {
    if (res.error) dispatch(receiveFailed("JOBS", res));
    else dispatch(receiveSucceeded("JOBS", res));
    return res;
  });
};

export const handleFetchPackages = (jobIds) => (dispatch) => {
  dispatch(receiveStarted("PACKAGES"));
  return fetchPackages(jobIds, true).then((res) => {
    if (res.error) dispatch(receiveFailed("PACKAGES", res));
    else dispatch(receiveSucceeded("PACKAGES", res));

    return res;
  });
};
