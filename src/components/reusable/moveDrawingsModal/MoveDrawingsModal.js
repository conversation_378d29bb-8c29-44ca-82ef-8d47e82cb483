// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import Button from "msuite_storybook/dist/button/Button";

// REDUX IMPORTS
import {
  handleFetchPackages as handleFetchJobsTablePackages,
  handleCreatePackages,
} from "../../packages/packagesActions";
import {
  handleFetchDrawings,
  handleMoveDrawingsBetweenPackages,
} from "../../drawings/drawingsActions";
import { handleFetchPackages, handleFetchJobs } from "./moveDrawingsActions";
import { handleFetchFlows } from "../../flows/flowsActions";

// COMPONENT IMPORTS
import Modal from "../modal/Modal";
import InlineFilter from "../inlineFilter/InlineFilter";
import CreatePackageForm from "../../reusable/createPackageForm/CreatePackageForm";
import ConfirmationModal from "../../reusable/confirmationModal/ConfirmationModal";

// HELPER FUNCTION IMPORTS
import { naturalSort } from "../../../_utils";

// STYLES IMPORTS
import "./stylesMoveDrawingsModal.scss";

const MoveDrawingsModal = ({
  drawingsToMove,
  clearSelectedRows,
  showModal,
  toggleModal,
  selectedFilterIds,
  // following are used for refreshing jobs table
  selectedDrawingsInFilter,
  setSelectedDrawingsInFilter,
  setCurrentTable,
}) => {
  const [selectedJobs, setSelectedJobs] = useState([]);
  const [selectedPackages, setSelectedPackages] = useState([]);
  const [showCreationForm, toggleCreationForm] = useState(false);
  const [showConfirmation, toggleConfirmation] = useState(false);

  const { jobs, packages } = useSelector((state) => state.moveDrawingsData);
  const { flows } = useSelector((state) => state.flowsData);

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(handleFetchFlows());
    dispatch(handleFetchJobs);
  }, []);

  useEffect(() => {
    if (selectedJobs?.length)
      dispatch(handleFetchPackages(selectedJobs.map((j) => j.id)));
  }, [selectedJobs, dispatch]);

  const handleCreatePackageAndAddToSelection = (data) => {
    dispatch(handleCreatePackages(selectedJobs[0].id, [data])).then((res) => {
      // automatically select new package in filter
      if (!res.error && res[2].length) {
        setSelectedPackages(res[2]);
        toggleCreationForm(false);
      }
    });
  };

  const handleMoveDrawingsSubmit = (drawingIds, packageId) => {
    dispatch(handleMoveDrawingsBetweenPackages(drawingIds, packageId)).then(
      (res) => {
        if (res.error) return;
        // refetch jobs table data
        dispatch(
          handleFetchJobsTablePackages(
            selectedFilterIds[0],
            selectedFilterIds[4]
          )
        );
        // refetch drawings and filter out drawings that were moved from selectedDrawings
        const f_selectedDrawings = selectedDrawingsInFilter.filter(
          (sd) => !drawingIds.includes(sd.id)
        );

        dispatch(
          handleFetchDrawings(selectedFilterIds[1], selectedFilterIds[4])
        ).then(() => {
          setSelectedDrawingsInFilter(f_selectedDrawings);
          // go back to packages table if all drawings were moved
          if (!f_selectedDrawings?.length) setCurrentTable("PACKAGES");
        });
        toggleModal(false);
        clearSelectedRows();
      }
    );

    toggleConfirmation(false);
  };

  const handleConfirmation = (e) => {
    e.preventDefault();
    toggleConfirmation(true);
  };

  const displayedFlows = useMemo(() => {
    return (flows || [])
      .map((f) => ({ id: f.id, value: f.id, display: f.name }))
      .sort((a, b) => naturalSort(a.display, b.display));
  }, [flows]);

  return (
    <Modal
      title={`Move Drawings (${drawingsToMove.length})`}
      showModal={showModal}
      onClose={() => toggleModal(false)}
      suppressForm
      showExit
    >
      <div className="move-drawings-wrapper">
        <h4>Select job and package to move drawings </h4>
        <div className="filters-wrapper">
          <div className="filter-wrapper">
            <p></p>
            <InlineFilter
              type="Jobs"
              list={jobs}
              selected={selectedJobs}
              handleParentSelect={setSelectedJobs}
              isMulti={false}
              nameKey="job_name"
              idKey="job_number"
            />
          </div>
          {selectedJobs?.length > 0 && (
            <div className="filter-wrapper">
              <InlineFilter
                type="Packages"
                list={packages}
                selected={selectedPackages}
                handleParentSelect={setSelectedPackages}
                isMulti={false}
                nameKey="package_name"
                idKey="package_id"
                canCreate={true}
                handleCreateClick={() => toggleCreationForm(true)}
              />
            </div>
          )}
        </div>
        {showCreationForm && selectedJobs?.length > 0 && (
          <CreatePackageForm
            displayedFlows={displayedFlows}
            createButtonText="Create and Select"
            toggleCreationForm={toggleCreationForm}
            createCallback={handleCreatePackageAndAddToSelection}
          />
        )}
        {selectedPackages?.length > 0 && (
          <Button
            onClick={(e) => handleConfirmation(e)}
            className="move-drawings-submit button"
          >
            Move Drawings
          </Button>
        )}
      </div>
      {showConfirmation && (
        <ConfirmationModal
          toggleModal={toggleConfirmation}
          item={`${drawingsToMove.length} Drawing${
            drawingsToMove.length > 1 ? `s` : ""
          }`}
          action="MOVE"
          handleClick={() =>
            handleMoveDrawingsSubmit(
              drawingsToMove.map((d) => d.id),
              selectedPackages[0].id
            )
          }
        />
      )}
    </Modal>
  );
};

export default MoveDrawingsModal;
