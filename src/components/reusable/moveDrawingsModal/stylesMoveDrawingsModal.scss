@import "../../styles//colors.scss";

div.move-drawings-wrapper {
  // height: 600px;
  height: auto;
  width: 500px;
  padding: 5px 20px;
  box-sizing: border-box;

  & div.filters-wrapper {
    margin-bottom: 40px;
    padding: 0;
  }

  & div.inline-filter-button p {
    font-size: 1rem;
  }

  & button.button {
    background-color: transparent;
    color: $fabProBlue;
    border: 1px solid $fabProBlue;
    border-radius: 3px;
    height: 35px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    box-shadow: none;
  }

  & button.selected {
    background-color: $fabProBlue;
    color: white;
  }

  & button.move-drawings-submit {
    color: white;
    background-color: $fabProBlue;
    float: right;
    margin: 10px 0;
  }
}
