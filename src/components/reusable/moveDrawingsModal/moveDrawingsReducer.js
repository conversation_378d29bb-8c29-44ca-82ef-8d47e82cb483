const initialState = {
  jobs: null,
  packages: null,
  isLoading: false,
  error: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_JOBS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_JOBS_SUCCEEDED":
      return { ...state, isLoading: false, jobs: payload, error: null };
    case "RECEIVE_JOBS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_PACKAGES_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_PACKAGES_SUCCEEDED":
      return { ...state, isLoading: false, packages: payload, error: null };
    case "RECEIVE_PACKAGES_FAILED":
      return { ...state, isLoading: false, error: payload };
    default:
      return state;
  }
}
