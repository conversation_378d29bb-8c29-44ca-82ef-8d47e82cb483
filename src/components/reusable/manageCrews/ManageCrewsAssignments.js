// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useMemo, useRef } from "react";
import Button from "msuite_storybook/dist/button/Button";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import { handleFetchUsers } from "../../wizard/wizardActions";
import { handleUpdateCrewAssignments } from "../manageCrews/manageCrewsActions";

// COMPONENT IMPORTS
import AgTable from "../agTable/AgTable";
import AssignUnassignCellRenderer from "../frameworkComponents/AssignUnassignCellRenderer";
import FullNameCellRenderer from "../frameworkComponents/FullNameCellRenderer";
import ConfirmationModal from "../confirmationModal/ConfirmationModal";

// CONSTANTS IMPORTS
import { columns } from "../assignments/assignmentsConstants";

import "../assignments/stylesAssignments.scss";

const Assignments = ({ selectedCrew, assignedUsers, setAssignedUsers }) => {
  const [personnelFilter, setPersonnelFilter] = useState("0");
  const [usersGridOptionsApi, setUsersGridOptionsApi] = useState(null);
  const [
    assignedUsersGridOptionsApi,
    setAssignedUsersGridOptionsApi,
  ] = useState(null);
  const [allUsers, setAllUsers] = useState(null);
  const [allUsersSearchInput, setAllUsersSearchInput] = useState("");
  const [assignedUsersSearchInput, setAssignedUsersSearchInput] = useState("");
  const [showAssignmentConfirmation, toggleAssignmentConfirmation] = useState(
    false
  );
  const [assignmentConfirmationType, setAssignmentConfirmationType] = useState(
    "ASSIGN"
  );
  const [currentUser, setCurrentUser] = useState(null);

  const dispatch = useDispatch();
  const { isLoading, allUsers: fullListOfUsers } = useSelector(
    (state) => state.wizardData
  );

  const f_fullListOfUsers = useMemo(() => {
    if (fullListOfUsers) {
      return fullListOfUsers.map((u) => {
        const pattern = new RegExp(`(^|,)${u.id}($|,)`);
        if (
          selectedCrew.assigned_personnel_ids &&
          pattern.test(selectedCrew.assigned_personnel_ids)
        )
          u.is_assigned = selectedCrew.id;

        return u;
      });
    } else return fullListOfUsers;
  }, [fullListOfUsers]);

  useEffect(() => {
    dispatch(handleFetchUsers);

    return () => {
      setCurrentUser(null);
    };
  }, []);

  useEffect(() => {
    if (f_fullListOfUsers && personnelFilter !== "0") {
      setAllUsers(
        f_fullListOfUsers.filter((u) => u.role_id === parseInt(personnelFilter))
      );
    }
  }, [f_fullListOfUsers, personnelFilter]);

  useEffect(() => {
    if (usersGridOptionsApi) {
      usersGridOptionsApi.setRowData(allUsers);
    }
  }, [allUsers]);

  useEffect(() => {
    if (selectedCrew.role_id !== parseInt(personnelFilter)) {
      setPersonnelFilter(selectedCrew.role_id.toString());
    }
  }, [selectedCrew.role_id]);

  useEffect(() => {
    if (!usersGridOptionsApi) return;
    usersGridOptionsApi.setQuickFilter(allUsersSearchInput);
  }, [allUsersSearchInput, usersGridOptionsApi]);

  useEffect(() => {
    if (!assignedUsersGridOptionsApi) return;
    assignedUsersGridOptionsApi.setQuickFilter(assignedUsersSearchInput);
  }, [assignedUsersSearchInput, assignedUsersGridOptionsApi]);

  const selectedRole = useMemo(() => {
    const rolesMap = {
      1: "Office",
      2: "Field",
      3: "Shop",
      4: "Client",
    };

    return rolesMap[personnelFilter];
  }, [personnelFilter]);

  useEffect(() => {
    if (assignedUsersGridOptionsApi && usersGridOptionsApi && !isLoading) {
      usersGridOptionsApi.setColumnDefs(
        columns("USERS", assignedUsers, handleAssignUnassignClick, true)
      );
      assignedUsersGridOptionsApi.setColumnDefs(
        columns(
          "ASSIGNED_USERS",
          assignedUsers,
          handleAssignUnassignClick,
          true
        )
      );
    }
  }, [
    assignedUsers,
    isLoading,
    assignedUsersGridOptionsApi,
    usersGridOptionsApi,
  ]);

  useEffect(() => {
    if (isLoading || !f_fullListOfUsers) return;

    const assigned = selectedCrew.assigned_personnel_ids
      ? f_fullListOfUsers.filter((u) => {
          const pattern = new RegExp(`(^|,)${u.id}($|,)`);
          return pattern.test(selectedCrew.assigned_personnel_ids);
        })
      : [];
    setAssignedUsers(assigned);
  }, [isLoading, f_fullListOfUsers, selectedCrew.assigned_personnel_ids]);

  useEffect(() => {
    if (assignedUsersGridOptionsApi) {
      assignedUsersGridOptionsApi.setRowData(assignedUsers);
    }
  }, [assignedUsers]);

  const handleAssignmentConfirmation = (updateUserAssignments, direction) => {
    // if assigning/unassigning all
    if (allRef.current) {
      handleAllAssignment(assignmentConfirmationType, updateUserAssignments);
    } else {
      dispatch(
        handleUpdateCrewAssignments(
          `${currentUser.id}`,
          selectedCrew.id,
          assignmentConfirmationType === "ASSIGN" ? 1 : 0,
          updateUserAssignments ? 1 : 0
        )
      ).then((res) => {
        if (!res.error) {
          direction === "ASSIGN"
            ? setAssignedUsers([...assignedUsers, currentUser])
            : setAssignedUsers(
                assignedUsers.filter((u) => u.id !== currentUser.id)
              );

          if (usersGridOptionsApi) {
            const rowNode = usersGridOptionsApi.getRowNode(currentUser.id);
            if (rowNode) {
              rowNode.setData({
                ...currentUser,
                is_assigned: direction === "ASSIGN" ? selectedCrew.id : null,
              });
            }
          }
        }
      });
    }

    setCurrentUser(false);
    toggleAssignmentConfirmation(false);
  };

  // used to determine if "Assign All" was clicked when assigning user(s) to crew's packages
  const allRef = useRef(null);

  const handleAssignUnassignClick = (user, direction = "ASSIGN") => {
    setAssignmentConfirmationType(direction);
    toggleAssignmentConfirmation(true);
    setCurrentUser(user);
  };

  const onGridReady = (params, table) => {
    switch (table) {
      case "USERS":
        params.api.setSideBarVisible(false);
        params.api.setSortModel([
          {
            colId: "full_name",
            sort: "asc",
          },
        ]);
        return setUsersGridOptionsApi(params.api);
      case "ASSIGNED_USERS":
        params.api.setSideBarVisible(false);
        params.api.setSortModel([
          {
            colId: "full_name",
            sort: "asc",
          },
        ]);
        return setAssignedUsersGridOptionsApi(params.api);
      default:
        return;
    }
  };

  const onSortChanged = (params) => {
    params.api.redrawRows();
  };
  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };
  const gridOptions = (rowData, table) => ({
    rowData,
    defaultColDef: {
      wrapText: true,
      cellClass: "custom-wrap",
    },
    columnDefs: columns(table, assignedUsers, handleAssignUnassignClick, true),
    frameworkComponents: {
      assignUnassignCellRenderer: AssignUnassignCellRenderer,
      fullNameCellRenderer: FullNameCellRenderer,
    },
    rowClassRules,
    suppressRowClickSelection: true,
    onGridReady: (params) => onGridReady(params, table),
    onSortChanged,
    getRowNodeId: (data) => data.id,
  });

  const itemName = useMemo(() => {
    let result = selectedCrew.name;

    if (result.length > 45) {
      result = `${result.slice(0, 42)}...`;
    }

    return result;
  }, [selectedCrew]);

  // NEED TO SEPARATE USER AND CREW CALLS WHEN ASSIGNING/UNASSIGNING ALL
  const handleAllAssignment = (direction, updateUserAssignments) => {
    if (direction === "ASSIGN") {
      // clear out currentUser for confirmationMessage
      setCurrentUser(null);
      let newAssignedUsers = [];

      newAssignedUsers = f_fullListOfUsers.filter(
        (u) => u.role_id === parseInt(personnelFilter)
      );

      dispatch(
        handleUpdateCrewAssignments(
          newAssignedUsers.map((u) => u.id).join(","),
          selectedCrew.id,
          1,
          updateUserAssignments ? 1 : 0
        )
      ).then((res) => {
        if (!res.error) {
          setAssignedUsers(newAssignedUsers);
          allRef.current = false;
        }
      });
    } else {
      const newAssignedUsers = assignedUsers.filter(
        (u) => u.role_id !== parseInt(personnelFilter)
      );
      const usersToUnassign = assignedUsers
        .filter((u) => u.role_id === parseInt(personnelFilter))
        .map((u) => u.id)
        .join(",");
      dispatch(
        handleUpdateCrewAssignments(
          usersToUnassign,
          selectedCrew.id,
          0,
          updateUserAssignments ? 1 : 0
        )
      ).then((res) => {
        if (!res.error) {
          setAssignedUsers(newAssignedUsers);
          allRef.current = false;
        }
      });
    }
  };

  const customAssignmentMessage = useMemo(() => {
    if (!currentUser) {
      return assignmentConfirmationType === "ASSIGN"
        ? "Should the crew's current packages be assigned to all users?"
        : "Should the crew's current packages be unassigned for all users?";
    }

    return assignmentConfirmationType === "ASSIGN"
      ? `Should the crew's current packages be assigned to ${currentUser.first_name} ${currentUser.last_name}?`
      : `Should the crew's current packages be unassigned from ${currentUser.first_name} ${currentUser.last_name}?`;
  }, [currentUser, assignmentConfirmationType]);

  return (
    <div className="assignments">
      <div className="tables">
        <div className="table left">
          <div className="header-wrapper">
            <div className="header">
              <Button
                onClick={() => {
                  allRef.current = true;
                  setAssignmentConfirmationType("UNASSIGN");
                  toggleAssignmentConfirmation(true);
                }}
              >{`Unassign All ${selectedRole ? selectedRole : ""}`}</Button>
              <Button
                onClick={() => {
                  allRef.current = true;
                  setAssignmentConfirmationType("ASSIGN");
                  toggleAssignmentConfirmation(true);
                }}
              >{`Assign All ${selectedRole ? selectedRole : ""}`}</Button>
            </div>
            <input
              className="search-input"
              onChange={(e) => setAllUsersSearchInput(e.target.value)}
              value={allUsersSearchInput}
              placeholder={`Search users`}
              type="text"
            />
          </div>
          {allUsers && allUsers.length > 0 && assignedUsers !== null && (
            <AgTable gridOptions={gridOptions(allUsers, "USERS")} />
          )}
        </div>
        <div className="table right">
          <div className="header-wrapper">
            <div className="header">
              <h3 className="title">{`${itemName}'s Assigned Personnel`}</h3>
            </div>
            <input
              className="search-input"
              onChange={(e) => setAssignedUsersSearchInput(e.target.value)}
              value={assignedUsersSearchInput}
              placeholder={`Search assigned users`}
              type="text"
            />
          </div>
          {assignedUsers && assignedUsers.length > 0 ? (
            <AgTable
              gridOptions={gridOptions(assignedUsers, "ASSIGNED_USERS")}
            />
          ) : (
            <div className="no-assigned-users">
              <p>No users assigned</p>
            </div>
          )}
        </div>
      </div>
      {showAssignmentConfirmation && (
        <ConfirmationModal
          showModal={showAssignmentConfirmation}
          handleClick={() =>
            handleAssignmentConfirmation(true, assignmentConfirmationType)
          }
          action="WARN"
          item={currentUser}
          message={customAssignmentMessage}
          submitText={
            assignmentConfirmationType === "ASSIGN" ? `Assign` : `Unassign`
          }
          cancelText={
            assignmentConfirmationType === "ASSIGN"
              ? `Don't Assign`
              : `Don't Unassign`
          }
          toggleModal={toggleAssignmentConfirmation}
          handleCancel={() =>
            handleAssignmentConfirmation(false, assignmentConfirmationType)
          }
        />
      )}
    </div>
  );
};

export default Assignments;
