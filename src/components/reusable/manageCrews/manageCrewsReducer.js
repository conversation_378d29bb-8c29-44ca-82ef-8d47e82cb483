const initialState = {
  isLoading: false,
  error: null,
  crews: null,
  packagesByCrew: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_CREWS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_CREWS_SUCCEEDED":
      return { ...state, isLoading: false, error: null, crews: payload };
    case "RECEIVE_CREWS_FAILED":
      return { ...state, isLoading: false, error: payload, crews: [] };
    case "UPDATE_CREWS_SUCCEEDED":
      return {
        ...state,
        crews: state.crews.map((c) => (c.id === payload.id ? payload : c)),
      };
    case "UPDATE_ARCHIVE_CREW_SUCCEEDED":
      return { ...state, crews: state.crews.filter((c) => c.id !== payload) };
    case "RECEIVE_SINGLE_CREW_SUCCEEDED":
      return { ...state, crews: [...state.crews, payload] };
    case "RECEIVE_PACKAGES_BY_CREW_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_PACKAGES_BY_CREW_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        packagesByCrew: payload,
        error: null,
      };
    case "RECEIVE_PACKAGES_BY_CREW_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        packagesByCrew: null,
      };
    default:
      return state;
  }
}
