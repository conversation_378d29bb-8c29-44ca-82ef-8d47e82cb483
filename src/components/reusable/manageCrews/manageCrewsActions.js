import {
  archiveCrew,
  createCrew,
  fetchCrews,
  updateCrewAssignments,
  updateCrewInfo,
  fetchPackagesByCrew,
} from "../../../_services";

export const receiveStarted = (type) => ({
  type: `RECEIVE_${type}_STARTED`,
});
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});
export const updateSucceeded = (type, payload) => ({
  type: `UPDATE_${type}_SUCCEEDED`,
  payload,
});

export const handleFetchCrews = (dispatch) => {
  const type = "CREWS";

  dispatch(receiveStarted(type));
  return fetchCrews().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleUpdateCrewAssignments = (
  userIds,
  crewId,
  direction,
  updateUserAssignments,
  all
) => (dispatch) => {
  const type = "CREWS";
  return updateCrewAssignments(
    userIds,
    crewId,
    direction,
    all,
    updateUserAssignments
  ).then((res) => {
    if (!res.error) dispatch(updateSucceeded(type, res[0]));

    return res;
  });
};

export const handleUpdateCrewInfo = (crewId, field, newValue) => (dispatch) => {
  const type = "CREWS";
  return updateCrewInfo(crewId, field, newValue).then((res) => {
    if (!res.error) dispatch(updateSucceeded(type, res[0]));

    return res;
  });
};

export const handleArchiveCrew = (crewId) => (dispatch) => {
  const type = "ARCHIVE_CREW";
  return archiveCrew(crewId).then((res) => {
    if (!res.error) dispatch(updateSucceeded(type, crewId));

    return res;
  });
};

export const handleCreateCrew = (name, roleId) => (dispatch) => {
  const type = "SINGLE_CREW";
  return createCrew(name, roleId).then((res) => {
    if (!res.error) dispatch(receiveSucceeded(type, res[0]));

    return res;
  });
};

export const handleFetchPackagesByCrew = (crewId) => (dispatch) => {
  const type = "PACKAGES_BY_CREW";
  dispatch(receiveStarted(type));
  return fetchPackagesByCrew(crewId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};
