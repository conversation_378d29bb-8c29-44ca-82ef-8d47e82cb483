@import "../../styles/colors.scss";

div.manage-crews-container {
  display: grid;
  grid-template-rows: 30px 1fr;

  height: calc(100vh - 130px);
  min-width: 1300px;
  min-height: 300px;
  background-color: $darkGrey;

  overflow-y: auto;

  & > h2.title {
    background-color: $fabProBlue;
    color: #fff;
    margin: 0;
    padding: 0 10px;
    font-size: 1rem;
    line-height: 30px;
  }

  & > div.content {
    display: grid;
    grid-template-rows: 60px 1fr;
  }

  & > div.content > div.crew-info {
    display: grid;
    grid-template-areas: "select name name role . . . . . . . . . . . . . . archive";
    column-gap: 10px;

    padding: 10px;

    & > div:last-of-type {
      grid-area: role;
    }

    & > div:first-of-type {
      grid-area: select;
    }

    & > div:nth-of-type(2) {
      grid-area: name;
    }

    & > button {
      grid-area: archive;
    }

    & > div > div > select {
      height: 40px;
      font-size: 1rem;
    }

    & > div > input {
      height: 40px;
      font-size: 1rem;
    }

    & > button {
      height: 40px;
      font-size: 1rem;
      color: #fff;
      background-color: $fabProBlue;
      padding: 0 10px;

      &:hover {
        background-color: darken($fabProBlue, 10%);
      }
    }
  }

  & div.no-assigned-users {
    height: auto;
    margin-top: 20px;
  }

  & > div.content > div.assignments > div.tables {
    // height of modal - header/inputs
    max-height: calc(100vh - 130px - 90px);
    & div.ag-theme-balham-dark {
      max-height: 100%;
    }
  }
}
