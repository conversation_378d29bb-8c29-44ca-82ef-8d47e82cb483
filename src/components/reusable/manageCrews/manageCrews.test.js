// NPM PACKAGE IMPORTS
import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockAdapter from "axios-mock-adapter";
import thunk from "redux-thunk";

// REDUX IMPORTS
import {
  // ACTION CREATORS
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  updateSucceeded,

  // ACTION HANDLERS
  handleFetchCrews,
  handleUpdateCrewAssignments,
  handleUpdateCrewInfo,
  handleArchiveCrew,
  handleCreateCrew,
} from "./manageCrewsActions";

describe("MANAGE CREWS", () => {
  const testError = {
    error: { status: 404, message: "No Crews Found" },
  };

  describe("Action handlers should perform the necessary functions", () => {
    let store;
    let httpMock;

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    const testCrews = [
      {
        id: 1,
        name: "Test 1",
        role_id: 1,
        assigned_users_ids: "1,2,3",
      },
      {
        id: 2,
        name: "Test 2",
        role_id: 2,
        assigned_users_ids: "4,5,6",
      },
    ];

    it("handleFetchCrews", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/crews`)
        .replyOnce(200, testCrews)
        .onGet(`${process.env.REACT_APP_API}/crews`)
        .replyOnce(404, testError);

      await store.dispatch(handleFetchCrews).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("CREWS"),
          receiveSucceeded("CREWS", testCrews),
        ];
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testCrews);

        store.clearActions();
      });
      return store.dispatch(handleFetchCrews).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("CREWS"),
          receiveFailed("CREWS", testError.error),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleUpdateCrewAssignments", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/crews/assignments`)
        .replyOnce(200, [testCrews[0]])
        .onPut(`${process.env.REACT_APP_API}/crews/assignments`)
        .replyOnce(200, [{ ...testCrews[0], assigned_users_ids: null }]);

      await store
        .dispatch(handleUpdateCrewAssignments("1,2,3", 1, 1))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [updateSucceeded("CREWS", testCrews[0])];
          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[0].payload).toEqual(testCrews[0]);
          expect(httpMock.history.put[0].data).toEqual(
            JSON.stringify({ crew_id: 1, direction: 1, user_ids: "1,2,3" })
          );

          store.clearActions();
        });
      return store
        .dispatch(handleUpdateCrewAssignments("1,2,3", 1, 0))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateSucceeded("CREWS", {
              ...testCrews[0],
              assigned_users_ids: null,
            }),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[0].payload).toEqual({
            ...testCrews[0],
            assigned_users_ids: null,
          });
          expect(httpMock.history.put[1].data).toEqual(
            JSON.stringify({ crew_id: 1, direction: 0, user_ids: "1,2,3" })
          );
        });
    });

    it("handleUpdateCrewInfo", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/crews/1`)
        .replyOnce(200, [{ ...testCrews[0], name: "Updated" }])
        .onPut(`${process.env.REACT_APP_API}/crews/1`)
        .replyOnce(200, [{ ...testCrews[0], role_id: 2 }]);

      await store
        .dispatch(handleUpdateCrewInfo(1, "name", "Updated"))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateSucceeded("CREWS", { ...testCrews[0], name: "Updated" }),
          ];
          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[0].payload).toEqual({
            ...testCrews[0],
            name: "Updated",
          });
          expect(httpMock.history.put[0].data).toEqual(
            JSON.stringify({ name: "Updated" })
          );

          store.clearActions();
        });
      return store.dispatch(handleUpdateCrewInfo(1, "role_id", 2)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          updateSucceeded("CREWS", { ...testCrews[0], role_id: 2 }),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload).toEqual({
          ...testCrews[0],
          role_id: 2,
        });
        expect(httpMock.history.put[1].data).toEqual(
          JSON.stringify({ role_id: 2 })
        );
      });
    });

    it("handleArchiveCrew", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/crews/archive/1`)
        .replyOnce(200, "Successfully archived crew")
        .onPut(`${process.env.REACT_APP_API}/crews/archive/1`)
        .replyOnce(400, testError);

      await store.dispatch(handleArchiveCrew(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [updateSucceeded("ARCHIVE_CREW", 1)];
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload).toEqual(1);

        store.clearActions();
      });
      return store.dispatch(handleArchiveCrew(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleCreateCrew", async () => {
      httpMock
        .onPost(`${process.env.REACT_APP_API}/crews`)
        .replyOnce(200, [testCrews[0]])
        .onPost(`${process.env.REACT_APP_API}/crews`)
        .replyOnce(400, testError);

      await store.dispatch(handleCreateCrew("Test Crew 1", 1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [receiveSucceeded("SINGLE_CREW", testCrews[0])];
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload).toEqual(testCrews[0]);
        expect(httpMock.history.post[0].data).toEqual(
          JSON.stringify({ name: "Test Crew 1", role_id: 1 })
        );

        store.clearActions();
      });
      return store.dispatch(handleCreateCrew("Test Crew 1", 1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [];

        expect(receivedActions).toEqual(expectedActions);
      });
    });
  });
});
