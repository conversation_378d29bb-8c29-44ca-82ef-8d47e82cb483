// NPM PACKAGE IMPORTS
import React, { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "msuite_storybook/dist/modal/Modal";
import Select from "msuite_storybook/dist/select/Select";
import Input from "msuite_storybook/dist/input/Input";
import Button from "msuite_storybook/dist/button/Button";

// COMPONENT IMPORTS
import { default as Assignments } from "./ManageCrewsAssignments";
import ConfirmationModal from "../confirmationModal/ConfirmationModal";

// REDUX IMPORTS
import { notify } from "../alertPopup/alertPopupActions";
import {
  handleArchiveCrew,
  handleCreateCrew,
  handleFetchCrews,
  handleUpdateCrewInfo,
  handleUpdateCrewAssignments,
} from "./manageCrewsActions";
import { handleFetchUsers } from "../../wizard/wizardActions";

// CONSTANTS IMPORTS
import { roleOptions } from "./manageCrewsConstants";

// STYLE IMPORTS
import "./stylesManageCrews.scss";

const ManageCrews = ({ open, handleClose }) => {
  const [selectedCrew, setSelectedCrew] = useState(null);
  const [crewName, setCrewName] = useState("");
  const [role, setRole] = useState(null);
  const [confirmationType, setConfirmationType] = useState(null);
  const [updatedRole, setUpdatedRole] = useState(null);
  const [assignedUsers, setAssignedUsers] = useState(null);

  const dispatch = useDispatch();
  const { crews } = useSelector((state) => state.crewsData);

  useEffect(() => {
    dispatch(handleFetchCrews);
    dispatch(handleFetchUsers);
  }, []);

  useEffect(() => {
    if (selectedCrew) {
      setCrewName(selectedCrew.name);
      setRole(selectedCrew.role_id);
    }
  }, [selectedCrew]);

  const selectCrew = (option) => {
    if (option === "new") {
      setSelectedCrew({
        id: 0,
        name: "",
        role_id: null,
        assigned_personnel_ids: null,
      });
    } else {
      setSelectedCrew(JSON.parse(option));
    }
  };

  const createNewCrew = (name, role) => {
    if (!name || !name.trim() || !role) return;

    dispatch(handleCreateCrew(name.trim(), role)).then((res) => {
      if (!res.error) setSelectedCrew(res[0]);
    });
  };

  const saveCrewInfo = (field, newValue) => {
    if (field === "name" && selectedCrew.name === newValue) return;
    if (field === "name" && (!newValue || !newValue.trim())) {
      dispatch(
        notify({
          id: Date.now(),
          type: "ERROR",
          message: "Crew name cannot be empty. Please enter a name.",
        })
      );
      setCrewName(selectedCrew.name);
    }

    if (field === "role_id" && newValue === selectedCrew.role_id) return;

    if (field === "role_id") {
      dispatch(
        handleUpdateCrewAssignments(null, selectedCrew.id, 0, 1, 1)
      ).then((res) => {
        if (!res.error) {
          setRole(newValue);
          dispatch(handleFetchUsers);
        }
      });
    }

    dispatch(
      handleUpdateCrewInfo(
        selectedCrew.id,
        field,
        field === "name" ? newValue.trim() : newValue
      )
    ).then((res) => {
      if (!res.error) setSelectedCrew(res[0]);
    });
  };

  const archiveCrew = (confirmed = false) => {
    if (confirmed) {
      dispatch(handleArchiveCrew(selectedCrew.id)).then((res) => {
        if (!res.error) {
          setRole(null);
          setCrewName("");
          setSelectedCrew(null);
          setConfirmationType(null);
        }
      });
    } else setConfirmationType("archiveCrew");
  };

  const displayedCrews = useMemo(() => {
    const newCrewOption = {
      id: "new",
      value: "new",
      display: "Create New Crew",
    };
    if (crews) {
      return [
        newCrewOption,
        ...crews.map((c) => ({
          id: c.id,
          value: JSON.stringify(c),
          display: c.name,
        })),
      ];
    } else return [newCrewOption];
  }, [crews]);

  const confirmationProps = {
    archiveCrew: {
      cancelAction: () => setConfirmationType(null),
      continueAction: () => archiveCrew(true),
    },
    updateCrewRole: {
      message: `By changing the role of the crew all current users will be removed from the crew. This action cannot be undone. Do you wish to continue?`,
      cancelAction: () => {
        setConfirmationType(null);
        setUpdatedRole(null);
      },
      continueAction: () => {
        saveCrewInfo("role_id", updatedRole);
        setUpdatedRole(null);
        setConfirmationType(null);
      },
    },
  };

  const handleRoleChange = (newRole) => {
    // creating crew
    if (selectedCrew.id === 0)
      return createNewCrew(crewName, parseInt(newRole));

    // only display confirmation if crew has users currently assigned otherwise just update value as normal
    if (assignedUsers?.length) {
      setUpdatedRole(newRole);
      setConfirmationType("updateCrewRole");
    } else {
      saveCrewInfo("role_id", parseInt(newRole));
    }
  };

  return (
    <Modal open={open} handleClose={handleClose}>
      <div className="manage-crews-container">
        <h2 className="title">Crew Assignments</h2>
        <div className="content">
          <div className="crew-info">
            <Select
              options={displayedCrews}
              placeholder="Choose Crew"
              onInput={(e) => selectCrew(e.target.value)}
              value={selectedCrew ? JSON.stringify(selectedCrew) : null}
            />
            {selectedCrew && (
              <>
                <Input
                  value={crewName}
                  onChange={(e) => setCrewName(e.target.value)}
                  onBlur={(e) =>
                    selectedCrew.id !== 0
                      ? saveCrewInfo("name", e.target.value)
                      : createNewCrew(e.target.value, role)
                  }
                />
                <Select
                  options={roleOptions}
                  value={role}
                  onInput={(e) => handleRoleChange(e.target.value)}
                />
                {selectedCrew.id !== 0 && (
                  <Button onClick={() => setConfirmationType("archiveCrew")}>
                    Archive Crew
                  </Button>
                )}
              </>
            )}
          </div>
          {selectedCrew && selectedCrew.id !== 0 && (
            <Assignments
              assignedUsers={assignedUsers}
              setAssignedUsers={setAssignedUsers}
              selectedCrew={selectedCrew}
              currentTable="CREWS"
            />
          )}
        </div>
      </div>
      {confirmationType && (
        <ConfirmationModal
          showModal={confirmationType ? true : false}
          handleClick={confirmationProps[confirmationType].continueAction}
          action="ARCHIVE"
          item={crewName}
          toggleModal={confirmationProps[confirmationType].cancelAction}
          submitText={
            confirmationProps[confirmationType].submitText || "Continue"
          }
          message={confirmationProps[confirmationType].message || null}
        />
      )}
    </Modal>
  );
};

export default ManageCrews;
