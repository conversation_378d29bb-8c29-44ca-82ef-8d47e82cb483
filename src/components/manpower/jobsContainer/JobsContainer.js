// NPM PACKAGE IMPORTS
import React, { useState, useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import ManpowerCard from "msuite_storybook/dist/manpowerCard/ManpowerCard";
import CreateNewCard from "msuite_storybook/dist/createNewCard/CreateNewCard";
import CollapsibleList from "msuite_storybook/dist/collapsibleList/CollapsibleList";
import ManpowerUserCard from "msuite_storybook/dist/manpowerUserCard/ManpowerUserCard";
import Button from "msuite_storybook/dist/button/Button";
import DraggableContainer from "msuite_storybook/dist/dragndrop/DraggableContainer";
import DropTargetContainer from "msuite_storybook/dist/dragndrop/DropTargetContainer";
import CreateUserForm from "msuite_storybook/dist/createUserForm/CreateUserForm";
import UserInfoModal from "msuite_storybook/dist/userInfoModal/UserInfoModal";
import Modal from "msuite_storybook/dist/modal/Modal";

// REDUX IMPORTS
import {
  handleEditUserInformation,
  handleCreateNewUser,
  handleFetchAllUsers,
  handleFetchAppGroups,
  handleFetchGroupContents,
  handleFetchAssignedUsers,
  handleFetchUsersByLocation,
  handleUpdateUserStatus,
  handleFetchAllUserStatuses,
} from "../manpowerActions";
import {
  handleFetchItemGroupTypes,
  handleFetchUserWork,
  handleFetchUnassignedWork,
  handleAssignUsersToItemGroups,
  handleUnassignUsersFromItemGroups,
} from "../../itemGroups/itemGroupsActions";
import {
  handleFetchUserStages,
  handleFetchUnassignedStages,
  handleAssignUsersToStages,
  handleUnassignUsersFromStages,
} from "../../stageGroups/stageGroupsActions";

// COMPONENT IMPORTS
import JobTypeHeader from "./JobTypeHeader";

// TRANSLATIONS IMPORTS
import useTranslations from "../../../hooks/useTranslations";
import manpowerTranslations from "../manpowerTranslations.json";

// HELPER FUNCTION IMPORTS
import { canDrop, generateKpis } from "../../../_utils";

// STYLES IMPORTS
import "./jobsContainer.scss";

const JobsContainer = ({ selectedLocation, subDomain }) => {
  const [selectedAppType, setSelectedAppType] = useState(null);
  const [isHeaderExpanded, toggleHeaderExpanded] = useState(true);
  const [showCreateUserModal, toggleCreateUserModal] = useState(false);
  const [showUserInfoModal, toggleShowUserInfoModal] = useState(false);
  const [userToEdit, setUserToEdit] = useState(null);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [unassignedUsers, setUnassignedUsers] = useState([]);
  const [drilldown, updateDrilldown] = useState([]);

  const { itemGroupTypes, userWork, unassignedWork } = useSelector(
    (state) => state.itemGroupsData
  );
  const {
    allUsers,
    appGroups,
    assignedUsers,
    groupContents,
    usersByLocation,
    allUserStatuses,
  } = useSelector((state) => state.manpowerData);
  const { userStages, unassignedStages } = useSelector(
    (state) => state.stageGroupsData
  );

  const dispatch = useDispatch();
  const translate = useTranslations(manpowerTranslations);

  useEffect(() => {
    dispatch(handleFetchItemGroupTypes);
    dispatch(handleFetchAppGroups);
    dispatch(handleFetchAllUsers);
    dispatch(handleFetchAllUserStatuses);
  }, [dispatch]);

  useEffect(() => {
    if (appGroups && appGroups.length) {
      const groupIds = appGroups.map((c) => c.id).join(",");
      dispatch(handleFetchAssignedUsers(groupIds));
    }
  }, [appGroups, dispatch]);

  useEffect(() => {
    if (groupContents && groupContents.length) {
      const groupIds = (drilldown && drilldown.length > 1
        ? groupContents
        : [...appGroups, ...groupContents]
      )
        .map((c) => c.id)
        .join(",");
      dispatch(handleFetchAssignedUsers(groupIds));
    }
  }, [groupContents]);

  useEffect(() => {
    if (!selectedLocation) updateDrilldown([]);
  }, [selectedLocation]);

  useEffect(() => {
    if (drilldown && drilldown.length) {
      dispatch(handleFetchGroupContents(drilldown[drilldown.length - 1].id));
    }
  }, [drilldown, dispatch]);

  useEffect(() => {
    if (selectedGroup && assignedUsers) {
      const pattern = new RegExp(`(^|,)${selectedGroup}($|,)`);
      const assignedUsersIds = assignedUsers
        .filter((u) => pattern.test(u.item_group_ids))
        .map((u) => u.id);
      const userList = selectedLocation ? usersByLocation : allUsers;
      const newUnassignedUsers = userList.filter((u) => {
        return !assignedUsersIds.includes(u.id);
      });
      setUnassignedUsers(newUnassignedUsers);
    }
  }, [
    selectedGroup,
    assignedUsers,
    allUsers,
    selectedLocation,
    usersByLocation,
  ]);

  const locGroupContents = useMemo(() => {
    if (groupContents && groupContents.length && selectedLocation)
      return groupContents.filter(
        (item) => item.location_id === selectedLocation.id
      );
  }, [groupContents, selectedLocation]);

  const locAssignedUsers = useMemo(() => {
    if (assignedUsers && selectedLocation)
      return assignedUsers.filter((u) => u.location_id === selectedLocation.id);
  }, [selectedLocation, assignedUsers]);

  const allUserStatusesMap = useMemo(() => {
    return allUserStatuses
      ? allUserStatuses.reduce((acc, curr) => {
          acc[curr.name.toLowerCase()] = curr.id;
          return acc;
        }, {})
      : {};
  }, [allUserStatuses]);

  const handleEditSave = (e, type) => {
    if (type === "ACTIVE_INACTIVE") {
      const activeStatusId = allUserStatusesMap["active"];
      const inactiveStatusId = allUserStatusesMap["inactive"];

      if (activeStatusId) {
        let addStatusIds;
        let removeStatusIds;

        const pattern = new RegExp(`(^|,)${activeStatusId}($|,)`);
        if (pattern.test(userToEdit.user_status_ids)) {
          addStatusIds = inactiveStatusId;
          removeStatusIds = activeStatusId;
        } else {
          addStatusIds = activeStatusId;
          removeStatusIds = inactiveStatusId;
        }
        dispatch(
          handleUpdateUserStatus(
            userToEdit.id.toString(),
            addStatusIds.toString(),
            removeStatusIds.toString()
          )
        ).then((res) => {
          if (!res.error) {
            if (pattern.test(userToEdit.user_status_ids)) {
              setUserToEdit({
                ...userToEdit,
                is_active: 0,
                user_status_ids: [
                  ...userToEdit.user_status_ids
                    .split(",")
                    .filter((id) => id !== activeStatusId.toString()),
                  inactiveStatusId,
                ].join(","),
              });
            } else {
              setUserToEdit({
                ...userToEdit,
                is_active: 1,
                user_status_ids: [
                  ...userToEdit.user_status_ids
                    .split(",")
                    .filter((id) => id !== inactiveStatusId.toString()),
                  activeStatusId,
                ].join(","),
              });
            }
          }
        });
      }
    } else {
      dispatch(handleEditUserInformation(e, type, userToEdit.id));
    }
  };

  const handlePostClose = () => {
    // updated this!
    selectedLocation
      ? dispatch(handleFetchUsersByLocation(selectedLocation.id))
      : dispatch(handleFetchAllUsers);
  };

  const handleCreateUserSubmit = async (newUser) => {
    return await dispatch(handleCreateNewUser(newUser));
  };

  const handleDropAssign = (userId, itemId) => {
    dispatch(handleAssignUsersToItemGroups(userId, itemId)).then(() => {
      let itemsToFetch = [];

      if (appGroups) itemsToFetch.push(...appGroups);
      if (groupContents) itemsToFetch.push(...groupContents);

      itemsToFetch = itemsToFetch.map((i) => i.id).join(",");
      dispatch(handleFetchAssignedUsers(itemsToFetch));
    });
  };

  const handleDropUnassign = (userId, itemId) => {
    dispatch(handleUnassignUsersFromItemGroups(userId, itemId)).then(() => {
      let itemsToFetch = [];

      if (appGroups) itemsToFetch.push(...appGroups);
      if (groupContents) itemsToFetch.push(...groupContents);

      itemsToFetch = itemsToFetch.map((i) => i.id).join(",");
      dispatch(handleFetchAssignedUsers(itemsToFetch));
    });
  };

  const handleAssignToItemGroups = (userId, item) => {
    dispatch(handleAssignUsersToItemGroups(userId, item.id)).then(() => {
      dispatch(handleFetchUserWork(userId));
      dispatch(handleFetchUnassignedWork(userId));
    });
  };

  const handleUnassignFromItemGroups = (userId, item) => {
    dispatch(handleUnassignUsersFromItemGroups(userId, item.id)).then(() => {
      dispatch(handleFetchUserWork(userId));
      dispatch(handleFetchUnassignedWork(userId));
    });
  };

  const handleAssignToStage = (userId, stage) => {
    dispatch(handleAssignUsersToStages(userId, stage.id)).then(() => {
      dispatch(handleFetchUserStages(userId));
      dispatch(handleFetchUnassignedStages(userId));
    });
  };

  const handleUnassignFromStage = (userId, stage) => {
    dispatch(handleUnassignUsersFromStages(userId, stage.id)).then(() => {
      dispatch(handleFetchUserStages(userId));
      dispatch(handleFetchUnassignedStages(userId));
    });
  };

  const handleDrilldown = (idx) => {
    updateDrilldown(
      selectedLocation
        ? drilldown
            .slice(0, idx + 1)
            .filter((item) => item.location_id === selectedLocation.id)
        : drilldown.slice(0, idx + 1)
    );
  };

  const DraggableManpowerUserCard = ({ u }) => {
    return (
      <DraggableContainer
        id={u.id}
        type="USER_CARD"
        dropOrigin={u.dropOrigin}
        parentId={u.parentId}
      >
        <ManpowerUserCard
          key={u.id}
          userInfo={u}
          handleMoreInfo={(userInfo) => {
            let userToEditObj = { ...userInfo };

            const activeStatusId = allUserStatusesMap["active"];
            if (activeStatusId) {
              const pattern = new RegExp(`(^|,)${activeStatusId}($|,)`);
              Object.assign(userToEditObj, {
                is_active: pattern.test(userToEditObj.user_status_ids) ? 1 : 0,
              });
            }

            setUserToEdit(userToEditObj);
            dispatch(handleFetchUserWork(userInfo.id));
            dispatch(handleFetchUnassignedWork(userInfo.id));
            dispatch(handleFetchUserStages(userInfo.id));
            dispatch(handleFetchUnassignedStages(userInfo.id));
            toggleShowUserInfoModal(true);
          }}
        />
      </DraggableContainer>
    );
  };

  return (
    <>
      <div className="sidebar-wrapper">
        <CollapsibleList
          defaultCollapsed={false}
          title={translate("All Users")}
        >
          {selectedLocation &&
            (usersByLocation && usersByLocation.length > 0 ? (
              usersByLocation.map((u) => {
                return <DraggableManpowerUserCard u={u} />;
              })
            ) : (
              <p>No users on location</p>
            ))}
          {!selectedLocation &&
            allUsers &&
            allUsers.length > 0 &&
            allUsers.map((u) => {
              const newU = { ...u, dropOrigin: "ALL_USERS" };
              return <DraggableManpowerUserCard u={newU} key={newU.id} />;
            })}
        </CollapsibleList>
        <Button
          onClick={() => toggleCreateUserModal(true)}
          className="manpower-new-user-button"
        >
          {translate("New User")}
        </Button>
        <DropTargetContainer
          specs={{
            accept: ["USER_CARD"],
            collect: (monitor) => ({
              canDrop: canDrop(monitor, "UNASSIGNED", ["ALL_USERS"]),
            }),
            drop: ({ id, parentId }) => handleDropUnassign(id, parentId),
          }}
        >
          <CollapsibleList
            defaultCollapsed={unassignedUsers && !!unassignedUsers.length}
            title={translate("Unassigned")}
          >
            {unassignedUsers &&
              unassignedUsers.length > 0 &&
              unassignedUsers.map((u) => {
                const newU = { ...u, dropOrigin: "UNASSIGNED" };
                return <DraggableManpowerUserCard u={newU} key={newU.id} />;
              })}
          </CollapsibleList>
        </DropTargetContainer>
      </div>
      <div className="jobs-dashboard">
        {drilldown && drilldown.length > 1 ? (
          <div className="jobs-dashboard-drilldown">
            {drilldown.map((i, idx) => {
              if (idx < drilldown.length - 1 || drilldown.length === 1) {
                return (
                  <React.Fragment key={i.id}>
                    <span
                      className="jobs-dashboard-drilldown-item"
                      onClick={() => handleDrilldown(idx)}
                    >
                      {i.name}
                    </span>
                    {drilldown.length > 1 && (
                      <span className="jobs-dashboard-drilldown-item-divider">
                        {">"}
                      </span>
                    )}
                  </React.Fragment>
                );
              } else {
                return <span key={i.id}>{i.name}</span>;
              }
            })}
          </div>
        ) : (
          <JobTypeHeader
            isHeaderExpanded={isHeaderExpanded}
            setSelectedAppType={setSelectedAppType}
            selectedAppType={selectedAppType}
            toggleHeaderExpanded={toggleHeaderExpanded}
            appGroups={appGroups}
            assignedUsers={selectedLocation ? locAssignedUsers : assignedUsers}
            setSelectedGroup={(item) => {
              setSelectedGroup(item.id);
              updateDrilldown([item]);
            }}
            handleAssign={handleDropAssign}
            handleUnassign={handleDropUnassign}
          />
        )}
        {selectedLocation && selectedAppType ? (
          <div className="cards-container">
            <CreateNewCard
              handleClick={() => console.log("create new job")}
              type="Job"
            />
            {locGroupContents &&
              locGroupContents.length > 0 &&
              locGroupContents.map((item) => {
                const pattern = new RegExp(`(^|,)${item.id}($|,)`);
                const users = assignedUsers
                  ? assignedUsers.filter((u) => pattern.test(u.item_group_ids))
                  : [];

                const kpis = generateKpis(users);

                return (
                  <DropTargetContainer
                    specs={{
                      accept: ["USER_CARD"],
                      collect: (monitor) => ({
                        canDrop: canDrop(monitor, "JOB" + item.id),
                      }),
                      drop: ({ id }) => handleDropAssign(id, item.id),
                    }}
                    key={item.id}
                  >
                    <ManpowerCard
                      type="JOB"
                      title={item.name}
                      kpis={kpis}
                      rowData={item}
                      users={users}
                      draggableUsers
                      drillCb={() => updateDrilldown([...drilldown, item])}
                      handleClick={() => setSelectedGroup(item.id)}
                      isSelected={selectedGroup === item.id}
                      handleRemoveUser={(userId) =>
                        handleDropUnassign(userId, item.id)
                      }
                    />
                  </DropTargetContainer>
                );
              })}
          </div>
        ) : (
          <div className="empty-cards-container">
            <p>Select a job type and location to view jobs</p>
          </div>
        )}
      </div>
      <CreateUserForm
        handleSubmit={handleCreateUserSubmit}
        show={showCreateUserModal}
        handleClose={() => toggleCreateUserModal(false)}
        subDomain={subDomain}
        tiers={[]}
      />
      {showUserInfoModal && (
        <Modal
          open={showUserInfoModal}
          handleClose={() => {
            setTimeout(() => {
              toggleShowUserInfoModal(false);
              handlePostClose();
            }, 50);
          }}
        >
          <UserInfoModal
            userInfo={userToEdit}
            handleEditSave={handleEditSave}
            workGroupTypes={itemGroupTypes}
            userWork={userWork}
            unassignedWork={unassignedWork}
            handleAssignToWork={handleAssignToItemGroups}
            handleUnassignFromWork={handleUnassignFromItemGroups}
            userStages={userStages}
            unassignedStages={unassignedStages}
            handleAssignToStage={handleAssignToStage}
            handleUnassignFromStage={handleUnassignFromStage}
          />
        </Modal>
      )}
    </>
  );
};

export default JobsContainer;
