// NPM PACKAGE IMPORTS
import React from "react";
import ManpowerCard from "msuite_storybook/dist/manpowerCard/ManpowerCard";
import DropTargetContainer from "msuite_storybook/dist/dragndrop/DropTargetContainer";
import { FiChevronUp, FiChevronDown } from "react-icons/fi";

// HELPER FUNCTION IMPORTS
import { canDrop, generateKpis } from "../../../_utils";

const JobTypeHeader = ({
  isHeaderExpanded,
  selectedAppType,
  setSelectedAppType,
  toggleHeaderExpanded,
  appGroups,
  assignedUsers,
  setSelectedGroup,
  handleAssign,
  handleUnassign,
}) => (
  <div
    className={
      isHeaderExpanded ? "container-wrapper" : "container-wrapper-collapsed"
    }
  >
    {appGroups.map((ac) => {
      const pattern = new RegExp(`(^|,)${ac.id}($|,)`);
      const users = assignedUsers
        ? assignedUsers.filter((u) => pattern.test(u.item_group_ids))
        : [];

      const handleClick = () => {
        setSelectedAppType(ac.name);
        setSelectedGroup(ac);
      };

      const kpis = generateKpis(users);

      return (
        <DropTargetContainer
          specs={{
            accept: ["USER_CARD"],
            collect: (monitor) => ({ canDrop: canDrop(monitor, ac.name) }),
            drop: ({ id }) => handleAssign(id, ac.id),
          }}
          key={ac.id}
        >
          <ManpowerCard
            type={ac.name}
            isSelected={
              selectedAppType &&
              selectedAppType.toUpperCase() === ac.name.toUpperCase()
            }
            users={users}
            kpis={kpis}
            isCollapsed={isHeaderExpanded ? false : true}
            handleClick={handleClick}
            rowData={ac}
            draggableUsers
            handleRemoveUser={(userId) => handleUnassign(userId, ac.id)}
          />
        </DropTargetContainer>
      );
    })}
    <div
      onClick={() => toggleHeaderExpanded(!isHeaderExpanded)}
      className={
        isHeaderExpanded ? "collapse-button" : "collapse-button--collapsed"
      }
    >
      {isHeaderExpanded ? <FiChevronUp /> : <FiChevronDown />}
    </div>
  </div>
);

export default JobTypeHeader;
