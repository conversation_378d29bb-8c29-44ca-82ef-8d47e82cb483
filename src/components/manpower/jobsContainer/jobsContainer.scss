@import "../../styles/colors.scss";

.cards-container {
  display: flex;
  flex-flow: wrap;
  justify-content: flex-start;
  flex: 1;
  overflow-x: scroll;
  padding: 10px;

  &::-webkit-scrollbar {
    display: none;
  }

  .manpower-card-wrapper {
    margin: 15px 15px;
  }

  .create-new-card {
    margin: 15px 15px;
    cursor: pointer;
  }

  & div.drop-target-container {
    height: 280px;
  }
}

.empty-cards-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;

  p {
    color: $blue;
  }
}

.jobs-dashboard {
  max-width: calc(100vw - 55px);
  height: calc(100vh - 187px);
  width: 100%;
  display: flex;
  flex-direction: column;
}

div.jobs-dashboard-drilldown {
  color: #fff;
  padding: 10px;

  & span.jobs-dashboard-drilldown-item {
    text-decoration: underline;
    cursor: pointer;
    transition: color 250ms ease;

    &:hover {
      color: $blue;
    }

    &-divider {
      padding: 0 10px;
    }
  }
}
