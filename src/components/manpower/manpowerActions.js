import {
  fetchUsers,
  // saveUserInformation,
  fetchAppGroups,
  fetchAssignedUsers,
  fetchGroupContents,
  updateUserStatus,
  fetchAllUserStatuses,
} from "../../_services";

const receiveAllUsersStarted = () => ({ type: "RECEIVE_ALL_USERS_STARTED" });
const receiveAllUsersSucceeded = (users) => ({
  type: "RECEIVE_ALL_USERS_SUCCEEDED",
  payload: users,
});
const receiveAllUsersFailed = (error) => ({
  type: "RECEIVE_ALL_USERS_FAILED",
  payload: error,
});
const receiveUpdatedUserSucceeded = (user) => ({
  type: "RECEIVE_UPDATED_USER_SUCCEEDED",
  payload: user,
});
const setWorkView = (bool) => ({
  type: "SET_WORK_VIEW",
  payload: bool,
});
const receiveUsersByLocationStarted = () => ({
  type: "RECEIVE_USERS_BY_LOCATION_STARTED",
});
const receiveUsersByLocationSucceeded = (users) => ({
  type: "RECEIVE_USERS_BY_LOCATION_SUCCEEDED",
  payload: users,
});
const receiveUsersByLocationFailed = (error) => ({
  type: "RECEIVE_USERS_BY_LOCATION_FAILED",
});
const receiveAppGroupsStarted = () => ({
  type: "RECEIVE_APP_GROUPS_STARTED",
});
const receiveAppGroupsSucceeded = (appGroups) => ({
  type: "RECEIVE_APP_GROUPS_SUCCEEDED",
  payload: appGroups,
});
const receiveAppGroupsFailed = (error) => ({
  type: "RECEIVE_APP_GROUPS_FAILED",
  payload: error,
});
const receiveAssignedUsersStarted = () => ({
  type: "RECEIVE_ASSIGNED_USERS_STARTED",
});
const receiveAssignedUsersSucceeded = (assignedUsers) => ({
  type: "RECEIVE_ASSIGNED_USERS_SUCCEEDED",
  payload: assignedUsers,
});
const receiveAssignedUsersFailed = (error) => ({
  type: "RECEIVE_ASSIGNED_USERS_FAILED",
  payload: error,
});
const receiveGroupContentsStarted = () => ({
  type: "RECEIVE_GROUP_CONTENTS_STARTED",
});
const receiveGroupContentsSucceeded = (groupContents) => ({
  type: "RECEIVE_GROUP_CONTENTS_SUCCEEDED",
  payload: groupContents,
});
const receiveGroupContentsFailed = (error) => ({
  type: "RECEIVE_GROUP_CONTENTS_FAILED",
  payload: error,
});
const receiveAllUserStatusesStarted = () => ({
  type: "RECEIVE_ALL_USER_STATUSES_STARTED",
});
const receiveAllUserStatusesSucceeded = (allUserStatuses) => ({
  type: "RECEIVE_ALL_USER_STATUSES_SUCCEEDED",
  payload: allUserStatuses,
});
const receiveAllUserStatusesFailed = (error) => ({
  type: "RECEIVE_ALL_USER_STATUSES_FAILED",
  payload: error,
});

export const handleFetchAllUsers = (dispatch) => {
  dispatch(receiveAllUsersStarted());
  return fetchUsers({ withAssignedGroups: 1 }).then((res) => {
    if (res.error) return dispatch(receiveAllUsersFailed(res.error));
    const users = res.map((u) => {
      u.distance_metric = "miles";
      return u;
    });
    return dispatch(receiveAllUsersSucceeded(users));
  });
};

export const handleUpdateWorkView = (bool) => (dispatch) =>
  dispatch(setWorkView(bool));

// export const handleEditUserInformation = (e, type, userId) => dispatch => {
//   return saveUserInformation(e, type, userId).then(res => {
//     if (res.error) return null;
//     return res[0];
//   });
// };

export const handleAddUpdatedUserToState = (updatedUser) => (dispatch) => {
  return dispatch(receiveUpdatedUserSucceeded(updatedUser));
};

// export const handleCreateNewUser = newUser => dispatch => {
//   dispatch(createUserStarted());
//   return createNewUser(newUser).then(res => {
//     if (res.error) return res;
//     //churnzero event
//     window.ChurnZero.push([
//       "trackEvent",
//       "FP User Created",
//       `User was created`,
//       1,
//       {
//         Product: "FabPro",
//         SubGroup: "Users",
//         Version: process.env.REACT_APP_ENVIRONMENT
//       }
//     ]);
//     return dispatch(createUserSucceeded(res[0]));
//   });
// };

export const handleFetchUsersByLocation = (locationId) => (dispatch) => {
  dispatch(receiveUsersByLocationStarted());
  return fetchUsers({ location_id: locationId }).then((res) => {
    if (res.error) return dispatch(receiveUsersByLocationFailed(res.error));
    return dispatch(receiveUsersByLocationSucceeded(res));
  });
};

export const handleFetchAppGroups = (dispatch) => {
  dispatch(receiveAppGroupsStarted());
  return fetchAppGroups().then((res) => {
    if (res.error) return dispatch(receiveAppGroupsFailed(res.error));
    return dispatch(receiveAppGroupsSucceeded(res));
  });
};

export const handleFetchAssignedUsers = (groupIds) => (dispatch) => {
  dispatch(receiveAssignedUsersStarted());
  return fetchAssignedUsers(groupIds).then((res) => {
    if (res.error) return dispatch(receiveAssignedUsersFailed(res.error));
    return dispatch(receiveAssignedUsersSucceeded(res));
  });
};

export const handleFetchGroupContents = (groupId) => (dispatch) => {
  dispatch(receiveGroupContentsStarted());
  return fetchGroupContents(groupId).then((res) => {
    if (res.error) return dispatch(receiveGroupContentsFailed(res.error));
    return dispatch(receiveGroupContentsSucceeded(res));
  });
};

export const handleUpdateUserStatus = (
  userIds,
  addStatusIds,
  removeStatusIds = null
) => (dispatch) => {
  return updateUserStatus(userIds, addStatusIds, removeStatusIds).then(
    (res) => {
      if (res.error) return res;
      return res[0];
    }
  );
};

export const handleFetchAllUserStatuses = (dispatch) => {
  dispatch(receiveAllUserStatusesStarted());
  return fetchAllUserStatuses().then((res) => {
    if (res.error) return dispatch(receiveAllUserStatusesFailed(res.error));
    return dispatch(receiveAllUserStatusesSucceeded(res));
  });
};
