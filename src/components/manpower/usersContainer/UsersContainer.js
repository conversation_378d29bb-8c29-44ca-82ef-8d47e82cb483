// NPM PACKAGE IMPORTS
import React, { useState, useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import CollapsibleDropdownList from "msuite_storybook";
import CreateNewCard from "msuite_storybook/dist/createNewCard/CreateNewCard";
import CreateUserForm from "msuite_storybook/dist/createUserForm/CreateUserForm";
import Modal from "msuite_storybook/dist/modal/Modal";
import UserInfoModal from "msuite_storybook/dist/userInfoModal/UserInfoModal";

// REDUX IMPORTS
import {
  handleEditUserInformation,
  handleFetchAllUsers,
  handleCreateNewUser,
  handleFetchAllUserStatuses,
  handleUpdateUserStatus,
  handleFetchAppGroups,
} from "../manpowerActions";
import {
  handleFetchItemGroupTypes,
  handleFetchItemGroups,
  handleFetchUserWork,
  handleFetchUnassignedWork,
  handleAssignUsersToItemGroups,
  handleUnassignUsersFromItemGroups,
} from "../../itemGroups/itemGroupsActions";
import {
  handleFetchUserStages,
  handleFetchUnassignedStages,
  handleAssignUsersToStages,
  handleUnassignUsersFromStages,
} from "../../stageGroups/stageGroupsActions";

// COMPONENT IMPORTS
import AgTable from "../../reusable/agTable/AgTable";
import MoreInfoCellRenderer from "../../reusable/frameworkComponents/MoreInfoCellRenderer";

// HELPER FUNCTION IMPORTS
import { onSortChanged, getRowHeight, rowClassRules } from "../../../_utils";

// TRANSLATIONS IMPORTS
import useTranslations from "../../../hooks/useTranslations";
import manpowerTranslations from "../manpowerTranslations.json";

// STYLES IMPORTS
import "./usersContainer.scss";

const UsersContainer = ({
  tableView,
  subDomain,
  selectedLocation,
  usersByLocation,
}) => {
  const [showUserInfoModal, toggleShowUserInfoModal] = useState(false);
  const [userToEdit, setUserToEdit] = useState(null);
  const [showCreateUserModal, toggleCreateUserModal] = useState(false);
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [jobTypeId, setJobTypeId] = useState(null);

  const translate = useTranslations(manpowerTranslations);
  const dispatch = useDispatch();

  const { allUsers, allUserStatuses, appGroups } = useSelector(
    (state) => state.manpowerData
  );
  const { itemGroupTypes, itemGroups, userWork, unassignedWork } = useSelector(
    (state) => state.itemGroupsData
  );
  const { userStages, unassignedStages } = useSelector(
    (state) => state.stageGroupsData
  );

  useEffect(() => {
    dispatch(handleFetchAllUsers);
    dispatch(handleFetchItemGroupTypes);
    dispatch(handleFetchAllUserStatuses);
    dispatch(handleFetchAppGroups);
  }, []);

  useEffect(() => {
    if (itemGroupTypes) {
      const jobGroupType = itemGroupTypes.find((i) => /job/i.test(i.name));

      if (jobGroupType) {
        setJobTypeId(jobGroupType.id);
      }
    }
  }, [itemGroupTypes]);

  useEffect(() => {
    if (jobTypeId) {
      dispatch(handleFetchItemGroups(jobTypeId));
    }
  }, [jobTypeId]);

  useEffect(() => {
    if (gridOptionsApi) {
      gridOptionsApi.setRowData(allUsers);
    }
  }, [allUsers, gridOptionsApi]);

  const onOptionClick = (target, rowInfo) => {
    switch (target) {
      case "EDIT_PERMISSIONS":
        return;
      case "EDIT_USER_INFO":
        handleMoreInfo(rowInfo);
        return;
      default:
        return;
    }
  };

  const allUserStatusesMap = useMemo(() => {
    return allUserStatuses
      ? allUserStatuses.reduce((acc, curr) => {
          acc[curr.name.toLowerCase()] = curr.id;
          return acc;
        }, {})
      : {};
  }, [allUserStatuses]);

  const handleEditSave = (e, type, id) => {
    if (type === "ACTIVE_INACTIVE") {
      const activeStatusId = allUserStatusesMap["active"];
      const inactiveStatusId = allUserStatusesMap["inactive"];

      if (activeStatusId) {
        let addStatusIds;
        let removeStatusIds;

        const pattern = new RegExp(`(^|,)${activeStatusId}($|,)`);
        if (pattern.test(userToEdit.user_status_ids)) {
          addStatusIds = inactiveStatusId;
          removeStatusIds = activeStatusId;
        } else {
          addStatusIds = activeStatusId;
          removeStatusIds = inactiveStatusId;
        }
        dispatch(
          handleUpdateUserStatus(
            userToEdit.id.toString(),
            addStatusIds.toString(),
            removeStatusIds.toString()
          )
        ).then((res) => {
          if (!res.error) {
            if (pattern.test(userToEdit.user_status_ids)) {
              setUserToEdit({
                ...userToEdit,
                is_active: 0,
                user_status_ids: [
                  ...userToEdit.user_status_ids
                    .split(",")
                    .filter((id) => id !== activeStatusId.toString()),
                  inactiveStatusId,
                ].join(","),
              });
            } else {
              setUserToEdit({
                ...userToEdit,
                is_active: 1,
                user_status_ids: [
                  ...userToEdit.user_status_ids
                    .split(",")
                    .filter((id) => id !== inactiveStatusId.toString()),
                  activeStatusId,
                ].join(","),
              });
            }
          }
        });
      }
    } else {
      if (userToEdit || id) {
        dispatch(handleEditUserInformation(e, type, id || userToEdit.id));
      }
    }
  };

  const handlePostClose = () => {
    dispatch(handleFetchAllUsers);
  };

  const handleCreateUserSubmit = async (newUser) => {
    return await dispatch(handleCreateNewUser(newUser));
  };

  useEffect(() => {
    if (!gridOptionsApi) return;
    gridOptionsApi.setRowData(selectedLocation ? usersByLocation : allUsers);
  }, [gridOptionsApi, selectedLocation, usersByLocation, allUsers]);

  const handleAssignToItemGroups = (userId, item) => {
    dispatch(handleAssignUsersToItemGroups(userId, item.id)).then(() => {
      dispatch(handleFetchUserWork(userId));
      dispatch(handleFetchUnassignedWork(userId));
    });
  };

  const handleUnassignFromItemGroups = (userId, item, fromModal = true) => {
    dispatch(handleUnassignUsersFromItemGroups(userId, item.id)).then(() => {
      if (fromModal) {
        dispatch(handleFetchUserWork(userId));
        dispatch(handleFetchUnassignedWork(userId));
      }
      if (!fromModal) dispatch(handleFetchAllUsers);
    });
  };

  const handleAssignToStage = (userId, stage) => {
    dispatch(handleAssignUsersToStages(userId, stage.id)).then(() => {
      dispatch(handleFetchUserStages(userId));
      dispatch(handleFetchUnassignedStages(userId));
    });
  };

  const handleUnassignFromStage = (userId, stage) => {
    dispatch(handleUnassignUsersFromStages(userId, stage.id)).then(() => {
      dispatch(handleFetchUserStages(userId));
      dispatch(handleFetchUnassignedStages(userId));
    });
  };

  const handleMoreInfo = (userInfo) => {
    let userToEditObj = { ...userInfo };

    const activeStatusId = allUserStatusesMap["active"];
    if (activeStatusId) {
      const pattern = new RegExp(`(^|,)${activeStatusId}($|,)`);
      Object.assign(userToEditObj, {
        is_active: pattern.test(userToEditObj.user_status_ids) ? 1 : 0,
      });
    }

    setUserToEdit(userToEditObj);
    dispatch(handleFetchUserWork(userInfo.id));
    dispatch(handleFetchUnassignedWork(userInfo.id));
    dispatch(handleFetchUserStages(userInfo.id));
    dispatch(handleFetchUnassignedStages(userInfo.id));
    toggleShowUserInfoModal(true);
  };

  const gridOptions = {
    onGridReady: (params) => setGridOptionsApi(params.api),
    rowData: allUsers,
    columnDefs: [
      {
        headerName: "First Name",
        field: "first_name",
      },
      {
        headerName: "Last Name",
        field: "last_name",
      },
      {
        headerName: "Username",
        field: "user_name",
      },
      {
        headerName: "Role",
        field: "role_name",
      },
      {
        headerName: "Title",
        field: "title",
      },
      {
        headerName: "Tier",
        field: "tier_name",
      },
      {
        headerName: "Active",
        valueFormatter: (params) => {
          const { user_status_ids } = params.data;

          const activeStatusId = allUserStatusesMap["active"];
          const pattern = new RegExp(`(^|,)${activeStatusId}($|,)`);

          if (pattern.test(user_status_ids)) return "ACTIVE";
          return "INACTIVE";
        },
      },
      {
        headerName: "Manage",
        sortable: false,
        suppressMenu: true,
        lockVisible: true,
        suppressMovable: true,
        cellRenderer: "moreInfoCellRenderer",
      },
    ],
    onSortChanged,
    getRowHeight,
    rowClassRules,
    frameworkComponents: {
      moreInfoCellRenderer: (params) => (
        <MoreInfoCellRenderer params={params} onOptionClick={onOptionClick} />
      ),
    },
  };

  const bimFabFieldIds = useMemo(() => {
    return appGroups
      ? appGroups.reduce((acc, curr) => {
          acc[curr.name.toUpperCase()] = curr.id;
          return acc;
        }, {})
      : {};
  }, [appGroups]);

  const bimFabFieldWork = useMemo(() => {
    return itemGroups
      ? itemGroups.reduce(
          (acc, curr) => {
            if (/::BIM::/i.test(curr.item_group_parent_info)) {
              acc["BIM"].push(curr);
            }
            if (/::FAB::/i.test(curr.item_group_parent_info)) {
              acc["FAB"].push(curr);
            }
            if (/::FIELD::/i.test(curr.item_group_parent_info)) {
              acc["FIELD"].push(curr);
            }

            return acc;
          },
          { BIM: [], FAB: [], FIELD: [] }
        )
      : { BIM: [], FAB: [], FIELD: [] };
  }, [itemGroups]);

  // const handleDropAssign = (userId, itemId) => {
  //   dispatch(handleAssignUsersToItemGroups(userId, itemId)).then(() => {
  //     dispatch(handleFetchAllUsers);
  //   });
  // };

  // const displayedUsers = selectedLocation ? usersByLocation : allUsers;

  return (
    <>
      <div className="sidebar-wrapper">
        {itemGroups && (
          <CollapsibleDropdownList
            bimWork={bimFabFieldWork["BIM"]}
            fabWork={bimFabFieldWork["FAB"]}
            fieldWork={bimFabFieldWork["FIELD"]}
            bimFabFieldIds={bimFabFieldIds}
            draggableJobs
          ></CollapsibleDropdownList>
        )}
      </div>
      <div className="users-dashboard">
        {tableView ? (
          <AgTable gridOptions={gridOptions} />
        ) : (
          <>
            <CreateNewCard
              handleClick={() => toggleCreateUserModal(true)}
              type="User"
            />
            {/* {displayedUsers &&
              displayedUsers.length > 0 &&
              displayedUsers.map(user => {
                const pattern = /(?<=::)job(?=($|,))/g;
                const jobsCount =
                  user.assigned_item_groups &&
                  user.assigned_item_groups.match(pattern);

                const _userWork = user.assigned_item_groups
                  ? user.assigned_item_groups
                      .split(",")
                      .filter(i => pattern.test(i))
                      .map(i => {
                        let item = i.split("::");
                        return {
                          id: item[0],
                          name: item[1],
                          number: item[2],
                          item_group_type_id: item[3]
                        };
                      })
                  : [];

                const kpis = [
                  {
                    name: "Jobs",
                    value: jobsCount ? jobsCount.length : 0,
                    selected: true
                  },
                  {
                    name: "Exp",
                    value: `${user.experience_years || 0} years`,
                    selected: true
                  },
                  {
                    name: "$",
                    value: user.labor_rate ? user.labor_rate.toFixed(2) : 0.0,
                    selected: true
                  }
                ];

                return (
                  <DropTargetContainer
                    specs={{
                      accept: ["JOB_CARD"],
                      collect: monitor => ({
                        canDrop: canDrop(monitor, "USER" + user.id)
                      }),
                      drop: ({ id }) => handleDropAssign(user.id, id)
                    }}
                    key={user.id}
                  >
                    <LargeUserCard
                      key={user.id}
                      assignedWork={_userWork}
                      userInfo={user}
                      kpis={kpis}
                      handleMoreInfo={handleMoreInfo}
                      handleRemove={item =>
                        handleUnassignFromItemGroups(user.id, item, false)
                      }
                    />
                  </DropTargetContainer>
                );
              })} */}
            {allUsers && !allUsers.length && (
              <p className="no-users-message">{translate("No users")}</p>
            )}
          </>
        )}
      </div>
      <CreateUserForm
        handleSubmit={handleCreateUserSubmit}
        show={showCreateUserModal}
        handleClose={() => toggleCreateUserModal(false)}
        subDomain={subDomain}
        tiers={[]}
      />
      {showUserInfoModal && (
        <Modal
          open={showUserInfoModal}
          handleClose={() => {
            setTimeout(() => {
              toggleShowUserInfoModal(false);
              handlePostClose();
            }, 50);
          }}
        >
          <UserInfoModal
            userInfo={userToEdit}
            handleEditSave={handleEditSave}
            workGroupTypes={itemGroupTypes}
            userWork={userWork}
            unassignedWork={unassignedWork}
            handleAssignToWork={handleAssignToItemGroups}
            handleUnassignFromWork={handleUnassignFromItemGroups}
            userStages={userStages}
            unassignedStages={unassignedStages}
            handleAssignToStage={handleAssignToStage}
            handleUnassignFromStage={handleUnassignFromStage}
          />
        </Modal>
      )}
    </>
  );
};

export default UsersContainer;
