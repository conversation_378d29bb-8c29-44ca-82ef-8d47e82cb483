@import "../../styles/colors.scss";

div.sidebar-wrapper {
  & div.sub-work-card {
    width: 95%;
    cursor: pointer;
    transition: background-color 250ms ease;
    border-radius: 3px;

    &:hover {
      background-color: darken($lighterGrey, 10%);
    }
  }
}

div.users-dashboard {
  display: flex;
  flex-flow: wrap;
  justify-content: flex-start;
  flex: 1;
  height: calc(100vh - 172px);
  overflow-x: scroll;
  border-left: 2px solid $grey;

  & div.drop-target-container {
    height: 280px;

    & div.storybook-manpower-work-card:hover {
      background-color: darken($lighterGrey, 10%);
    }
  }

  & .custom-ag-styles.ag-theme-balham-dark {
    height: 100%;
  }

  &::-webkit-scrollbar {
    display: none;
  }

  & div.create-new-card {
    margin: 15px;
  }

  .user-wrapper {
    margin: 15px;
  }

  & p.no-users-message {
    color: $fabProBlue;
    text-align: center;
    font-size: 1.1rem;
    margin-top: -15%;
    width: 100%;
    font-weight: bold;
  }

  & p.no-location-users {
    color: $blue;
    font-weight: bold;
    text-align: center;
    width: 200px;
  }
}
