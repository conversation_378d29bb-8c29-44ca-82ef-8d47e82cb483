@import "../styles/colors.scss";

.manpower-container {
  background-color: #434c5e;
  height: calc(100vh - 100px - 32px);

  .top-toolbar {
    height: 40px;
    background-color: #283141;
    display: flex;
    justify-content: space-between;
    align-items: center;

    & .filter-buttons {
      display: flex;
      align-items: center;
      min-width: 350px;
      & .locations-filter {
        font-size: 0.8rem;
        & .filter-label {
          margin-right: 0;
        }
        & .filter-label,
        .css-f9pm0r-Control,
        .css-1smh3ei-Control,
        .css-e3ou72-Control {
          background-color: transparent;
        }
        & .css-1gtu0rj-indicatorContainer,
        .css-tlfecz-indicatorContainer {
          padding: 0;
          & svg {
            height: 16px;
            width: 16px;
            cursor: pointer;
          }
        }
        & svg {
          font-size: 0.9rem;
        }
      }
    }

    & .button-row {
      display: flex;
      width: 140px;
      justify-content: space-evenly;
      align-items: center;
      position: relative;

      svg {
        color: #a3b7c8;
        cursor: pointer;
      }
      .selected {
        color: $blue;
      }
    }
    .left {
      margin-left: 20px;
    }
    .right {
      margin-right: 20px;

      & div.toggle-switch {
        position: absolute;
        left: -75px;
        top: -40%;
      }
    }
    h4 {
      color: $blue;
      text-align: center;
      margin: 10px 100px 10px 0;
    }
  }
  .content {
    display: flex;
  }
}

.container-wrapper {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  background-color: #363f50;
  height: 284px;
  overflow-x: auto;

  .manpower-card-wrapper {
    margin: 0 5px;
  }

  @media (max-width: 1500px) {
    & .manpower-card-wrapper {
      width: 300px;
      padding: 0 10px;
      font-size: 0.9rem;
    }
    & .kpi-wrapper {
      width: 280px;
    }
    & .users-wrapper {
      max-width: 280px;
      grid-template-columns: 1fr 1fr;
      .manpower-user-wrapper {
        width: 130px;
      }
    }
  }

  @media (max-width: 1200px) {
    & .manpower-card-wrapper {
      width: 220px;
      font-size: 0.8rem;
      padding: 0 5px;
    }
    & .kpi-wrapper {
      width: 198px;
    }
    & .users-wrapper {
      max-width: 200px;
      grid-template-columns: 1fr;
      .manpower-user-wrapper {
        width: 180px;
      }
    }
  }
}

.container-wrapper-collapsed {
  @extend .container-wrapper;
  max-height: 100px;

  @media (max-width: 1500px) {
    & .collapsed-card-wrapper {
      width: 300px;
    }
    & .kpi-wrapper-collapsed {
      margin-left: 20px;
    }
  }

  @media (max-width: 1200px) {
    & .collapsed-card-wrapper {
      width: 230px;
    }
    & .card-title {
      font-size: 0.8rem;
      padding-left: 5px;
    }
    & .kpi-wrapper-collapsed {
      margin-left: 20px;
      width: 60%;
      & p {
        font-size: 0.8rem;
        padding: 0;
        margin: 0;
      }
      & span {
        flex-direction: column;
        justify-content: space-evenly;
      }
      svg {
        font-size: 0.8rem;
      }
    }
  }
}

.collapse-button {
  position: absolute;
  top: 340px;
  right: 20px;
  height: 30px;
  width: 30px;
  background-color: #26282d;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $blue;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  cursor: pointer;

  svg {
    font-size: 1.2rem;
  }
}

.collapse-button--collapsed {
  @extend .collapse-button;
  top: 155px;
  right: 20px;
}

.sidebar-wrapper {
  width: 230px;
  background-color: #283141;
  height: calc(100vh - 100px - 72px);
  box-sizing: border-box;

  & .dropdown-item button {
    width: 200px;
    border-radius: 3px;
  }

  @media (max-width: 1200px) {
    width: 200px;
    .large-manpower-user-wrapper {
      width: 170px;
    }

    & .dropdown-item button {
      width: 170px;
    }
  }

  p {
    color: $blue;
    margin: 0;
  }

  & div.collapsible-list-wrapper {
    & header.header {
      color: $blue;

      h3.title {
        font-size: 1rem;
      }
      &:hover {
        background-color: lighten($grey, 10%);
      }
    }
  }

  & button.manpower-new-user-button {
    background-color: $blue;
    color: #fff;
    height: 30px;
    width: calc(100% - 20px);
    margin: 10px auto;
    padding: 0;
    display: block;
    font-size: 0.8rem;
    font-weight: bold;

    &:hover {
      background-color: darken($blue, 10%);
    }
  }
}
