// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FiSearch, FiFilter } from "react-icons/fi";
import { FaRegBuilding, FaUsers } from "react-icons/fa";
import Toggle from "msuite_storybook/dist/toggle/Toggle";

// REDUX IMPORTS
import { handleSetPageTitle } from "../../redux/generalActions";
import {
  handleUpdateWorkView,
  handleFetchUsersByLocation,
} from "./manpowerActions";
import { handleFetchJobLocations } from "../jobs/jobsActions";

// COMPONENT IMPORTS
import JobsContainer from "./jobsContainer/JobsContainer";
import UsersContainer from "./usersContainer/UsersContainer";
import LocationFilter from "../reusable/locationFilter/LocationFilter";

// TRANSLATIONS IMPORTS
import useTranslations from "../../hooks/useTranslations";
import manpowerTranslations from "./manpowerTranslations.json";

// STYLES IMPORTS
import "./manpower.scss";

const ManpowerHome = () => {
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [userTableView, toggleUserTableView] = useState(false);

  const translate = useTranslations(manpowerTranslations);
  const dispatch = useDispatch();

  const {
    // isLoading: isManpowerLoading,
    // error: manpowerError,
    usersByLocation,
    workView,
  } = useSelector((state) => state.manpowerData);
  const { jobLocations, isLoading: isJobsLoading } = useSelector(
    (state) => state.jobsData
  );
  const { userInfo } = useSelector((state) => state.profileData);

  useEffect(() => {
    dispatch(handleSetPageTitle(translate("Manpower")));
    dispatch(handleFetchJobLocations);
  }, [dispatch]);

  useEffect(() => {
    if (selectedLocation) {
      dispatch(handleFetchUsersByLocation(selectedLocation.id));
    }
  }, [selectedLocation, dispatch]);

  const f_locations = useMemo(() => {
    if (!isJobsLoading && jobLocations && jobLocations.length)
      return jobLocations.map((loc) => ({ id: loc.id, site: loc.name }));
  }, [jobLocations, isJobsLoading]);

  useEffect(() => {
    if (f_locations && f_locations.length === 1)
      setSelectedLocation(f_locations[0]);
  }, [f_locations]);

  const onToggleChanged = (e) => {
    toggleUserTableView(!userTableView);
  };

  const subDomain = useMemo(() => {
    if (userInfo && userInfo.user_name) {
      const domainRegex = /(?=@)(.*)$/g;
      return userInfo.user_name.match(domainRegex)[0];
    }
  }, [userInfo]);

  return (
    <div className="manpower-container">
      <div className="top-toolbar">
        <span className="left filter-buttons">
          <LocationFilter
            setSelectedLocation={setSelectedLocation}
            list={f_locations}
            selectedLocation={selectedLocation}
          />
          <span className="button-row">
            <FiSearch />
            <FiFilter />
          </span>
        </span>
        <h4>{workView ? translate("Jobs") : translate("Users")}</h4>
        <span className="right button-row">
          {!workView ? (
            <Toggle
              name="float-toggle"
              text={["table", "card"]}
              onToggleChanged={onToggleChanged}
              defaultChecked={userTableView}
            />
          ) : (
            <></>
          )}
          <FaRegBuilding
            onClick={() => dispatch(handleUpdateWorkView(true))}
            className={workView && "selected"}
          />
          <FaUsers
            onClick={() => dispatch(handleUpdateWorkView(false))}
            className={!workView && "selected"}
          />
        </span>
      </div>
      <div className="content">
        {workView ? (
          <JobsContainer
            subDomain={subDomain}
            usersByLocation={usersByLocation}
            selectedLocation={selectedLocation}
          />
        ) : (
          <UsersContainer
            usersByLocation={usersByLocation}
            tableView={userTableView}
            selectedLocation={selectedLocation}
            subDomain={subDomain}
          />
        )}
      </div>
    </div>
  );
};

export default ManpowerHome;
