const initialState = {
  isLoading: false,
  error: null,
  allUsers: [],
  usersByLocation: [],
  unassignedUsers: null,
  workView: true,
  appGroups: [],
  assignedUsers: null,
  groupContents: null,
  allUserStatuses: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_ALL_USERS_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_ALL_USERS_SUCCEEDED":
      return { ...state, isLoading: false, allUsers: payload };
    case "RECEIVE_ALL_USERS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_UNASSIGNED_USERS_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_UNASSIGNED_USERS_SUCCEEDED":
      return { ...state, isLoading: false, unassignedUsers: payload };
    case "RECEIVE_UNASSIGNED_USERS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        unassignedUsers: null,
      };
    case "RECEIVE_UPDATED_USER_SUCCEEDED":
      return {
        ...state,
        allUsers: state.allUsers.map((u) => {
          if (u.id === payload.id) return payload;
          return u;
        }),
      };
    case "SET_WORK_VIEW":
      return { ...state, workView: payload };
    case "CREATE_USER_STARTED":
      return { ...state, isLoading: true };
    case "CREATE_USER_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "CREATE_USER_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        allUsers: [payload, ...state.allUsers],
      };
    case "RECEIVE_USERS_BY_LOCATION_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_USERS_BY_LOCATION_SUCCEEDED":
      return { ...state, isLoading: false, usersByLocation: payload };
    case "RECEIVE_USERS_BY_LOCATION_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_APP_GROUPS_STARTED":
      return { ...state, isLoading: true, error: null, appGroups: [] };
    case "RECEIVE_APP_GROUPS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        appGroups: payload,
      };
    case "RECEIVE_APP_GROUPS_FAILED":
      return { ...state, isLoading: false, error: payload, appGroups: [] };
    case "RECEIVE_ASSIGNED_USERS_STARTED":
      return { ...state, isLoading: true, error: null, assignedUsers: null };
    case "RECEIVE_ASSIGNED_USERS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        assignedUsers: payload,
      };
    case "RECEIVE_ASSIGNED_USERS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        assignedUsers: null,
      };
    case "RECEIVE_GROUP_CONTENTS_STARTED":
      return {
        ...state,
        isLoading: true,
        error: null,
        groupContents: null,
      };
    case "RECEIVE_GROUP_CONTENTS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        groupContents: payload,
      };
    case "RECEIVE_GROUP_CONTENTS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        groupContents: null,
      };
    case "RECEIVE_ALL_USER_STATUSES_STARTED":
      return {
        ...state,
        isLoading: true,
        error: null,
        allUserStatuses: null,
      };
    case "RECEIVE_ALL_USER_STATUSES_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        allUserStatuses: payload,
      };
    case "RECEIVE_ALL_USER_STATUSES_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        allUserStatuses: null,
      };
    default:
      return state;
  }
}
