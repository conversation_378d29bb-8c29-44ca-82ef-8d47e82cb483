// NPM PACKAGE IMPORTS
import React from "react";
import { Redirect } from "react-router-dom";
import { connect } from "react-redux";

// REDUX IMPORTS
import { handleReceiveToken } from "../profile/profileActions";

// EXPORTS
const Login = ({ token, username, userId, dispatchSetToken }) => {
  localStorage.removeItem("msuite_token");
  localStorage.removeItem("initFinished");
  const newToken = window.location.search.split("token=")[1].trim();
  if (newToken) {
    localStorage["msuite_token"] = newToken;
    dispatchSetToken(newToken);
    window.ChurnZero.push([
      "trackEvent",
      "FP Login",
      "Password",
      1,
      {
        Product: "FabPro",
        SubGroup: "Login",
        Version: process.env.REACT_APP_ENVIRONMENT,
        UserName: username,
        UserId: userId,
      },
    ]);
  }

  if (token) return <Redirect to="/dashboard/" />;

  return <></>;
};

const mapStateToProps = ({ profileData }) => ({
  token: profileData.token,
  username: profileData.userInfo.username,
  userId: profileData.userId,
});

const mapDispatchToProps = (dispatch) => ({
  dispatchSetToken: (token) => handleReceiveToken(token)(dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(Login);
