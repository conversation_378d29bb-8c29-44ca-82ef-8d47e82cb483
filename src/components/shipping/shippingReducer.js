const initialState = {
  isLoading: false,
  error: null,
  laydown_locations: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_LAYDOWN_LOCATIONS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        laydown_locations: payload,
      };
    case "RECEIVE_LAYDOWN_LOCATIONS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        laydown_locations: [],
      };
    case "CREATE_LAYDOWN_LOCATIONS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "CREATE_LAYDOWN_LOCATIONS_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "CREATE_LAYDOWN_LOCATIONS_FAILED":
      return { ...state, isLoading: false, error: payload };
    default:
      return state;
  }
}
