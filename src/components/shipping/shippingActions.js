import { fetchLaydownLocations } from "../../_services";

// const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});
// const createStarted = type => ({ type: `CREATE_${type}_STARTED` });
// const createSucceeded = (type, payload) => ({
//   type: `CREATE_${type}_SUCCEEDED`,
//   payload
// });
// const createFailed = (type, error) => ({
//   type: `CREATE_${type}_FAILED`,
//   payload: error
// });

export const handleFetchLaydownLocations = (type) => (dispatch) => {
  const item = "LAYDOWN_LOCATIONS";

  return fetchLaydownLocations(type).then((res) => {
    if (res.error) dispatch(receiveFailed(item, res));
    else dispatch(receiveSucceeded(item, res));

    return res;
  });
};
