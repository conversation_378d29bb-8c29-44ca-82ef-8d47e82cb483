import {
  fetchPackages,
  updatePackages,
  deletePackage,
  archivePackage,
  fetchStatusTrackerStages,
  createPackages,
  updatePackageMap,
} from "../../_services";
import store from "../../redux/store";

export const receiveStarted = (type) => ({ type: `RECEIVE_${type}_STARTED` });
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});
export const updateStarted = (type) => ({ type: `UPDATE_${type}_STARTED` });
export const updateSucceeded = (type, payload) => ({
  type: `UPDATE_${type}_SUCCEEDED`,
  payload,
});
export const updateFailed = (type, error) => ({
  type: `UPDATE_${type}_FAILED`,
  payload: error,
});
export const deleteStarted = (type) => ({ type: `DELETE_${type}_STARTED` });
export const deleteEnded = (type, pkgId) => ({
  type: `DELETE_${type}_ENDED`,
  payload: pkgId,
});
export const archiveStarted = (type) => ({ type: `ARCHIVE_${type}_STARTED` });
export const archiveEnded = (type) => ({ type: `ARCHIVE_${type}_ENDED` });

export const handleClearPackagesSavedColumnState = (dispatch) => {
  dispatch(receiveSucceeded("PACKAGES_CLEAR_COLUMN_STATE"));
};

export const handleFetchPackages = (jobIds, all, workable, testUserId) => (
  dispatch
) => {
  dispatch(receiveStarted("PACKAGES"));
  return fetchPackages(jobIds, all, workable, testUserId).then((res) => {
    if (res.error) dispatch(receiveFailed("PACKAGES", res));
    else dispatch(receiveSucceeded("PACKAGES", res));

    return res;
  });
};

export const handleDeletePackage = (pkgId, updateStore = false) => (
  dispatch
) => {
  dispatch(deleteStarted("PACKAGE"));
  return deletePackage(pkgId).then((res) => {
    dispatch(deleteEnded("PACKAGE", updateStore ? pkgId : null));
    return res;
  });
};
export const handleFetchAllPackages = (dispatch) => {
  dispatch(receiveStarted("ALL_PACKAGES"));
  return fetchPackages([], true).then((res) => {
    if (res.error) return dispatch(receiveFailed("ALL_PACKAGES", res));
    return dispatch(receiveSucceeded("ALL_PACKAGES", res));
  });
};

export const handleUpdatePackages = (
  idsToUpdate,
  updatedData,
  parentData,
  testUserId
) => (dispatch) => {
  dispatch(updateStarted("PACKAGES"));
  return updatePackages(idsToUpdate, updatedData).then((res) => {
    if (res.error) dispatch(updateFailed("PACKAGES", res));
    else dispatch(updateSucceeded("PACKAGES", res));

    if (parentData) {
      return fetchPackages(...parentData, testUserId).then((res) => {
        dispatch(receiveStarted("PACKAGES"));
        if (res.error) dispatch(receiveFailed("PACKAGES", res));
        else dispatch(receiveSucceeded("PACKAGES", res));

        return res;
      });
    }

    return res;
  });
};

export const handleArchivePackage = (pkgId) => (dispatch) => {
  dispatch(archiveStarted("PACKAGE"));
  return archivePackage(pkgId).then((res) => {
    dispatch(archiveEnded("PACKAGE"));
    return res;
  });
};

export const handleFetchStatusTrackerStages = (packageId) => (dispatch) => {
  const type = "PACKAGES_STATUS_TRACKER";

  return fetchStatusTrackerStages("packages", packageId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res.error));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleCreatePackages = (jobId, packagesData) => (dispatch) => {
  const type = "ADDITIONAL_PACKAGES";

  return createPackages(jobId, packagesData).then((res) => {
    if (!res.error && res[2].length) dispatch(receiveSucceeded(type, res[2]));

    return res;
  });
};

export const handleUpdatePackageMap = (pkgId, jobId, file) => (dispatch) => {
  return updatePackageMap(pkgId, jobId, file);
};

export const handleResetPackagesList = (dispatch) => {
  const type = "RESET_PACKAGES_LIST";
  dispatch(receiveSucceeded(type));
};

export const handleSetPackagesList = (packages = []) => (dispatch) => {
  const type = "SET_PACKAGES_LIST";
  dispatch(receiveSucceeded(type, packages));
};

// Update the redux package list without a full refresh...
// Need to update both packages and storedListOfPackages in order to avoid any stale cached data
export const handleUpdatePackageNoRefresh = (packages, updatedPackage) => (
  dispatch
) => {
  const type1 = "PACKAGES";
  const type2 = "SET_PACKAGES_LIST";

  const { storedListOfPackages } = store.getState().packagesData;

  // update the in state memory of the record without refetching ALL.
  if (packages && updatedPackage) {
    let updatedPackages = [...packages];
    let storedListOfUpdatedPackages = [...storedListOfPackages];
    const isUpdate = typeof updatedPackage === "object";

    let index1 = updatedPackages.findIndex(
      (p) => p.id === (isUpdate ? updatedPackage.id : updatedPackage)
    );
    let index2 = storedListOfUpdatedPackages.findIndex(
      (p) => p.id === (isUpdate ? updatedPackage.id : updatedPackage)
    );

    // if update, update, else we want to delete the package id...
    if (index1 !== -1 && index2 !== -1) {
      if (isUpdate) {
        updatedPackages[index1] = updatedPackage;
        storedListOfUpdatedPackages[index2] = updatedPackage;
      } else {
        updatedPackages.splice(index1, index1);
        storedListOfUpdatedPackages.splice(index1, index1);
      }
    }

    dispatch(receiveSucceeded(type1, updatedPackages));
    dispatch(receiveSucceeded(type2, storedListOfUpdatedPackages));
  }
};

export const handleUpdatePackagesNoRefresh = (updatedPackages) => (
  dispatch
) => {
  const type1 = "PACKAGES";
  const type2 = "SET_PACKAGES_LIST";

  const { packages, storedListOfPackages } = store.getState().packagesData;

  // update the in state memory of the record without refetching ALL.
  let storedListOfUpdatedPackages = [...storedListOfPackages];
  if (packages) {
    for (let updatedPackage of updatedPackages) {
      const isUpdate = typeof updatedPackage === "object";

      let index1 = packages.findIndex(
        (p) => p.id === (isUpdate ? updatedPackage.id : updatedPackage)
      );
      let index2 = storedListOfUpdatedPackages.findIndex(
        (p) => p.id === (isUpdate ? updatedPackage.id : updatedPackage)
      );

      // if update, update, else we want to delete the package id...
      if (index1 !== -1 && index2 !== -1) {
        if (isUpdate) {
          packages[index1] = updatedPackage;
          storedListOfUpdatedPackages[index2] = updatedPackage;
        } else {
          packages.splice(index1, index1);
          storedListOfUpdatedPackages.splice(index1, index1);
        }
      }
    }
  }
  dispatch(receiveSucceeded(type1, packages));
  dispatch(receiveSucceeded(type2, storedListOfUpdatedPackages));
};
