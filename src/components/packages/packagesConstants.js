// NPM PACKAGE IMPORTS
import moment from "moment";
import "moment-timezone";

// REDUX IMPORTS
import store from "../../redux/store";
import { notify } from "../reusable/alertPopup/alertPopupActions";

// HELPER FUNCTION IMPORTS
import { generateTime, naturalSort, decimalRegex } from "../../_utils";

// STYLE IMPORTS
import "../styles/tables.scss";

// EXPORTS
export const packagesColumnDefs = (
  savedColumnState,
  moreInfoClick,
  toggleMoreInfo,
  handleChildSelection,
  sortState,
  testStore = null,
  customColumns
) => {
  const { systemSettings, permissions } = (
    testStore || store
  ).getState().profileData;
  let dateFormatting = systemSettings && systemSettings.date_display;

  const defaultDefs = [
    {
      headerName: "Package #",
      field: "number",
      getQuickFilterText: (params) => params.data?.number,
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue;

        return params.newValue;
      },
      editable: true,
      minWidth: 100,
      width: 120,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "number",
      sort:
        sortState.sorting_column_name === "number"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Package Name",
      field: "package_name",
      getQuickFilterText: (params) => params.data?.package_name,
      valueParser: (params) => {
        if (
          params.newValue === "" ||
          params.newValue === undefined ||
          (typeof params.newValue === "string" && !params.newValue.trim())
        )
          return params.oldValue;

        return params.newValue;
      },
      valueFormatter: (params) => ({
        wizardPermission:
          permissions &&
          (permissions.includes(279) ||
            permissions.includes(280) ||
            permissions.includes(281)),
      }),
      editable: true,
      minWidth: 120,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_name",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "package_name"
          ? sortState.sorting_method
          : null,
      cellRenderer: "packageNameCellRenderer",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Package ID",
      field: "id",
      minWidth: 80,
      width: 100,
      getQuickFilterText: (params) => params.data?.id,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "id",
      sort:
        sortState.sorting_column_name === "id"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Job Name",
      field: "job_name",
      valueFormatter: (params) => ({
        wizardPermission:
          permissions &&
          (permissions.includes(279) ||
            permissions.includes(280) ||
            permissions.includes(281)),
      }),
      getQuickFilterText: (params) => params.data?.job_name,
      minWidth: 120,
      width: 140,
      cellRenderer: "jobNameCellRenderer",
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "job_name",
      autoHeight: true,
      sort:
        sortState.sorting_column_name === "job_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Job #",
      field: "job_number",
      minWidth: 80,
      width: 100,
      getQuickFilterText: (params) => params.data?.job_number,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "job_number",
      sort:
        sortState.sorting_column_name === "job_number"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Drawings",
      valueGetter: (params) =>
        params.data?.assigned_drawings_count
          ? params.data?.assigned_drawings_count
          : params.data?.drawing_count || 0,
      cellRendererParams: {
        handleChildSelection,
        type: "DRAWINGS",
      },
      cellRenderer: "childCellRenderer",
      minWidth: 100,
      width: 120,
      permissions: [51],
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      comparator: (valueA, valueB, nodeA, nodeB, isInverted) => valueA - valueB,
      colId: "drawings",
      sort:
        sortState.sorting_column_name === "drawings"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Work Items",
      valueGetter: (params) =>
        params.data?.assigned_work_item_count
          ? params.data?.assigned_work_item_count
          : params.data?.work_item_count,
      minWidth: 100,
      width: 120,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      comparator: (valueA, valueB, nodeA, nodeB, isInverted) => valueA - valueB,
      colId: "work_items",
      sort:
        sortState.sorting_column_name === "work_items"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Area",
      field: "area",
      getQuickFilterText: (params) => params.data?.area,
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        return params.newValue;
      },
      editable: true,
      minWidth: 100,
      width: 120,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "area",
      sort:
        sortState.sorting_column_name === "area"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Due Date",
      field: "due_date_unix",
      valueFormatter: (params) => {
        return params.value
          ? typeof params.value === "number"
            ? generateTime(params.value * 1000, false, true, "-", testStore)
            : params.value
          : "-";
      },
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue;

        const defaultRegex = new RegExp("\\d{2}-\\d{2}-\\d{4}");
        const dateFormattingRegex = dateFormatting
          ? /-/.test(dateFormatting)
            ? new RegExp(
                dateFormatting
                  .split("-")
                  .map((part) => `\\d{${part.length}}`)
                  .join("\\-")
              )
            : /\//.test(dateFormatting)
            ? new RegExp(
                dateFormatting
                  .split("/")
                  .map((part) => `\\d{${part.length}}`)
                  .join("\\/")
              )
            : defaultRegex
          : defaultRegex;

        if (!dateFormattingRegex.test(params.newValue)) {
          return params.oldValue;
        } else return Math.floor(new Date(params.newValue).getTime() / 1000);
      },
      getQuickFilterText: (params) => params.colDef.valueFormatter(params),
      filter: "agDateColumnFilter",
      editable: true,
      cellEditor: "dueDateEditorRenderer",
      minWidth: 100,
      width: 120,
      filterParams: {
        buttons: ["reset"],
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = cellValue
            ? typeof cellValue === "number"
              ? new Date(
                  generateTime(cellValue * 1000, false, true, "-", testStore)
                )
              : new Date(cellValue)
            : "-";

          return cellDate < filterLocalDateAtMidnight
            ? -1
            : cellDate > filterLocalDateAtMidnight
            ? 1
            : 0;
        },
      },
      menuTabs: ["filterMenuTab"],
      colId: "due_date_unix",
      sort:
        sortState.sorting_column_name === "due_date_unix"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "% Complete",
      field: "percent_complete",
      minWidth: 100,
      width: 120,
      // @ToDo: Fix rounding...
      valueGetter: (params) => {
        if (!params.data) return 0;

        const { percent_complete } = params.data;
        if (!percent_complete) return 0;
        return Math.floor(percent_complete);
      },
      valueFormatter: (params) => {
        if (!params.data) return "0%";

        const { percent_complete } = params.data;
        if (!percent_complete) return "0%";
        return Math.floor(percent_complete);
      },
      getQuickFilterText: (params) => params.data?.percent_complete,
      cellRenderer: "percentCompleteCellRenderer",
      permissions: [63],
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "percent_complete",
      sort:
        sortState.sorting_column_name === "percent_complete"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Status",
      field: "package_status",
      minWidth: 120,
      width: 140,
      getQuickFilterText: (params) => params.data?.package_status,
      resizable: true,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "package_status",
      sort:
        sortState.sorting_column_name === "package_status"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Budget Hours",
      field: "budget_hours",
      valueFormatter: (params) => {
        if (!params.data) return;

        const { budget_hours } = params.data;
        const value = budget_hours
          ? `${parseFloat(budget_hours).toFixed(2)}`
          : "0.00";
        return value;
      },
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue;

        if (!/^\d{0,}\.?\d{1,}$/.test(params.newValue)) return params.oldValue;

        return params.newValue;
      },
      getQuickFilterText: (params) => params.data?.budget_hours,
      editable: true,
      minWidth: 80,
      width: 100,
      permissions: [66],
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "budget_hours",
      sort:
        sortState.sorting_column_name === "budget_hours"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "FP Budget",
      field: "fp_budget",
      valueFormatter: (params) => {
        if (!params.data) return;

        const { fp_budget } = params.data;
        const value = fp_budget ? `${fp_budget.toFixed(1)}` : "0.0";
        return value;
      },
      getQuickFilterText: (params) => params.data?.fp_budget,
      minWidth: 80,
      width: 100,
      permissions: [66],
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "fp_budget",
      sort:
        sortState.sorting_column_name === "fp_budget"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Fab Completed On",
      field: "fab_completed_on",
      valueFormatter: (params) => {
        return params.value
          ? typeof params.value === "number"
            ? generateTime(params.value * 1000, false, false, "-", testStore)
            : params.value
          : "";
      },
      editable: false,
      getQuickFilterText: (params) => params.colDef.valueFormatter(params),
      filter: "agDateColumnFilter",
      minWidth: 100,
      width: 120,
      colId: "fab_completed_on",
      filterParams: {
        buttons: ["reset"],
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const cellDate = cellValue
            ? typeof cellValue === "number"
              ? new Date(
                  generateTime(cellValue * 1000, true, true, "-", testStore)
                )
              : new Date(cellValue)
            : "-";

          return cellDate < filterLocalDateAtMidnight
            ? -1
            : cellDate > filterLocalDateAtMidnight
            ? 1
            : 0;
        },
      },
      menuTabs: ["filterMenuTab"],
      sort:
        sortState.sorting_column_name === "fab_completed_on"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Hours Left",
      field: "est_time_left",
      valueFormatter: (params) => {
        if (!params.data) return;

        const { est_time_left } = params.data;
        const value = est_time_left ? `${est_time_left.toFixed(1)}` : "0.0";
        return value;
      },
      getQuickFilterText: (params) => params.data?.est_time_left,
      minWidth: 80,
      width: 100,
      permissions: [66],
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "est_time_left",
      sort:
        sortState.sorting_column_name === "est_time_left"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Est. Hours +/-",
      field: "hours_ahead_behind",
      valueFormatter: (params) => {
        if (!params.data) return;

        const { hours_ahead_behind } = params.data;
        return hours_ahead_behind ? hours_ahead_behind.toFixed(1) : 0;
      },
      getQuickFilterText: (params) => params.data?.hours_ahead_behind,
      cellRenderer: "estHoursCellRenderer",
      permissions: [70],
      minWidth: 80,
      width: 100,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "hours_ahead_behind",
      sort:
        sortState.sorting_column_name === "hours_ahead_behind"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) => (valueA || 0) - (valueB || 0),
    },
    {
      headerName: "Total Hours",
      field: "cost_time",
      valueFormatter: (params) => {
        if (!params.data) return;

        const { cost_time } = params.data;
        return cost_time ? cost_time.toFixed(1) : 0;
      },
      getQuickFilterText: (params) => params.data?.cost_time,
      minWidth: 80,
      width: 100,
      filter: "agNumberColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "cost_time",
      sort:
        sortState.sorting_column_name === "cost_time"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Work Flow",
      field: "work_flow_names",
      valueFormatter: (params) => {
        if (params.value) {
          return params.value.split(",").length > 1 ? "Multi" : params.value;
        } else return params.value;
      },
      getQuickFilterText: (params) => {
        if (params.data?.work_flow_names) {
          return params.data.work_flow_names.split(",").length > 1
            ? "Multi"
            : params.data.work_flow_names;
        } else return params.data?.work_flow_names;
      },
      minWidth: 80,
      width: 100,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "work_flow_names",
      sort:
        sortState.sorting_column_name === "work_flow_names"
          ? sortState.sorting_method
          : null,
    },
    {
      headerName: "Created By",
      field: "created_by_name",
      getQuickFilterText: (params) => params.data?.created_by_name,
      resizable: true,
      minWidth: 120,
      width: 140,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      colId: "created_by",
      autoHeight: true,
      sortable: true,
      sort:
        sortState.sorting_column_name === "created_by_name"
          ? sortState.sorting_method
          : null,
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Manage",
      sortable: false,
      width: 60,
      minWidth: 60,
      valueFormatter: (params) => {
        return {
          moreInfoClick,
          toggleMoreInfo,
        };
      },
      pinned: "right",
      cellRenderer: "moreInfoCellRenderer",
      lockVisible: true,
      suppressMovable: true,
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      colId: "manage",
    },
  ];

  if (customColumns?.length) {
    for (let column of customColumns) {
      const columnDef = {
        autoHeight: true,
        suppressSizeToFit: false,
        headerName: column.display_name,
        field: column.normal_name,
        valueParser: (params) => {
          if (params.newValue === "" || params.newValue === undefined)
            return params.oldValue === "" || params.oldValue === undefined
              ? params.oldValue
              : null;

          if (column.data_type === "decimal") {
            if (!decimalRegex.test(params.newValue)) {
              (testStore || store).dispatch(
                notify({
                  id: Date.now(),
                  type: "ERROR",
                  message: "Value must be a number",
                })
              );
              return params.oldValue;
            }
          }
          return params.newValue;
        },
        cellClass: "wrap-text",
        menuTabs: [],
        minWidth: 125,
        editable: true,
        sortable: false,
        colId: column.normal_name,
      };

      if (column.data_type === "date") {
        columnDef.filter = "agDateColumnFilter";
        columnDef.filterParams = {
          buttons: ["reset"],
          comparator: (filterLocalDateAtMidnight, cellValue) => {
            const cellDate = cellValue
              ? typeof cellValue === "number"
                ? new Date(generateTime(cellValue * 1000, false, true, "-"))
                : new Date(cellValue)
              : "-";

            return cellDate < filterLocalDateAtMidnight
              ? -1
              : cellDate > filterLocalDateAtMidnight
              ? 1
              : 0;
          },
        };
        columnDef.valueFormatter = (params) => {
          return params.value
            ? typeof params.value === "number"
              ? generateTime(params.value * 1000, false, true, "-", testStore)
              : params.value
            : "";
        };

        if (column.editable) {
          columnDef.cellEditor = "dueDateEditorRenderer";
          columnDef.valueParser = (params) => {
            if (params.newValue === "" || params.newValue === undefined)
              return params.oldValue;
            if (params.newValue === 0) return 0;

            const defaultRegex = new RegExp("\\d{2}-\\d{2}-\\d{4}");
            const dateFormattingRegex = dateFormatting
              ? /-/.test(dateFormatting)
                ? new RegExp(
                    dateFormatting
                      .split("-")
                      .map((part) => `\\d{${part.length}}`)
                      .join("\\-")
                  )
                : /\//.test(dateFormatting)
                ? new RegExp(
                    dateFormatting
                      .split("/")
                      .map((part) => `\\d{${part.length}}`)
                      .join("\\/")
                  )
                : defaultRegex
              : defaultRegex;
            if (!dateFormattingRegex.test(params.newValue)) {
              return params.oldValue;
            } else return params.newValue; //new Date(params.newValue);
          };
        }
      }

      defaultDefs.push(columnDef);
    }
  }

  if (savedColumnState && savedColumnState.length) {
    let result = [];

    for (let i = 0; i < defaultDefs.length; i++) {
      let savedDef = savedColumnState.find(
        (c) => c.header_name === defaultDefs[i].headerName
      );

      if (savedDef) {
        result.push({
          ...defaultDefs[i],
          pinned: savedDef.pinned,
          hide: savedDef.visible ? false : true,
          position: savedDef.position,
        });
      } else result.push(defaultDefs[i]);
    }

    result = result
      .sort((a, b) => {
        if (a.position === b.position) {
          if (a.headerName.toLowerCase() > b.headerName.toLowerCase()) return 1;
          else return -1;
        } else return a.position - b.position;
      })
      .map((col) => {
        if (col.position !== undefined) delete col.position;
        return col;
      });

    result.unshift(
      {
        headerName: "",
        minWidth: 50,
        maxWidth: 50,
        width: 50,
        suppressMenu: true,
        sortable: true,
        comparator: (valueA, valueB, nodeA, nodeB) => {
          return nodeA.data.on_hold - nodeB.data.on_hold;
        },
        // cellRenderer: "onHoldCellRenderer",
        resizable: true,
        suppressSizeToFit: true,
        lockVisible: true,
        suppressMovable: true,
        pinned: "left",
        cellClassRules: {
          "cell-yellow": (params) => params.data?.on_hold === 1,
          "cell-green": (params) => params.data?.on_hold === 0,
        },
        suppressColumnsToolPanel: true,
        colId: "on_hold",
        headerClass: "status-indicator-button",
        sort:
          sortState.sorting_column_name === "on_hold"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "",
        headerCheckboxSelection: customColumns?.length ? false : true,
        headerCheckboxSelectionFilteredOnly: true,
        minWidth: 40,
        maxWidth: 40,
        width: 40,
        sortable: false,
        checkboxSelection: true,
        suppressMenu: true,
        pinned: "left",
        suppressColumnsToolPanel: true,
        lockVisible: true,
        suppressMovable: true,
        colId: "checkbox",
      }
    );

    return result;
  } else {
    defaultDefs.unshift(
      {
        headerName: "",
        minWidth: 50,
        maxWidth: 50,
        width: 50,
        suppressMenu: true,
        lockVisible: true,
        suppressMovable: true,
        sortable: true,
        comparator: (valueA, valueB, nodeA, nodeB) => {
          return nodeA.data.on_hold - nodeB.data.on_hold;
        },
        resizable: false,
        pinned: "left",
        cellClassRules: {
          "cell-yellow": (params) => params.data?.on_hold === 1,
          "cell-green": (params) => params.data?.on_hold === 0,
        },
        suppressColumnsToolPanel: true,
        colId: "status-indicator",
        headerClass: "status-indicator-button",
        sort:
          sortState.sorting_column_name === "status-indicator"
            ? sortState.sorting_method
            : null,
      },
      {
        headerName: "",
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        minWidth: 40,
        maxWidth: 40,
        width: 40,
        sortable: false,
        checkboxSelection: true,
        suppressMenu: true,
        lockVisible: true,
        suppressMovable: true,
        pinned: "left",
        suppressColumnsToolPanel: true,
        colId: "checkbox",
      }
    );

    return defaultDefs;
  }
};
