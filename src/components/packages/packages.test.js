import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockA<PERSON>pter from "axios-mock-adapter";
import thunk from "redux-thunk";

import {
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  updateStarted,
  updateSucceeded,
  updateFailed,
  deleteStarted,
  deleteEnded,
  archiveStarted,
  archiveEnded,
  handleFetchPackages,
  handleDeletePackage,
  handleFetchAllPackages,
  handleUpdatePackages,
  handleArchivePackage,
  handleFetchStatusTrackerStages,
  handleCreatePackages,
} from "./packagesActions";
import { packagesColumnDefs } from "./packagesConstants";

describe("Packages", () => {
  const testError = (type, message) => ({
    error: {
      status: 400,
      message: `${message ? message : `No ${type} found.`}`,
    },
  });

  describe("action handlers should perform the necessary functions", () => {
    let store;
    let httpMock;

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    const testPackages = [
      {
        id: 1,
        job_id: 1,
        job_name: "test job 1",
        job_number: "1",
        package_name: "test package 1",
        number: "1",
        drawing_count: 1,
      },
      {
        id: 2,
        job_id: 2,
        job_name: "test job 2",
        job_number: "2",
        package_name: "test package 2",
        number: "2",
        drawing_count: 1,
      },
      {
        id: 10,
        job_id: 1,
        job_name: "test job 1",
        job_number: "1",
        package_name: "test package 10",
        number: "10",
        drawing_count: 5,
        assigned_drawing_count: 3,
      },
    ];

    it("handleFetchPackages fetches all packages or based off job", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/packages?is_active=1&job_ids=1`)
        .replyOnce(200, testPackages.slice(0, 2))
        .onGet(
          `${process.env.REACT_APP_API}/packages?is_active=1&user_id=1&with_assigned_counts=1`
        )
        .replyOnce(200, testPackages[2])
        .onGet(`${process.env.REACT_APP_API}/packages?is_active=1&job_ids=10`)
        .replyOnce(404, testError("packages"));

      await store
        .dispatch(handleFetchPackages([1], true, null, null))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("PACKAGES"),
            receiveSucceeded("PACKAGES", testPackages.slice(0, 2)),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testPackages.slice(0, 2));

          store.clearActions();
        });

      await store
        .dispatch(handleFetchPackages(null, false, null, 1))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("PACKAGES"),
            receiveSucceeded("PACKAGES", testPackages[2]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testPackages[2]);

          store.clearActions();
        });

      return store
        .dispatch(handleFetchPackages([10], true, null, null))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("PACKAGES"),
            receiveFailed("PACKAGES", testError("packages")),
          ];

          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleFetchAllPackages fetches all packages", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/packages?is_active=1`)
        .replyOnce(200, testPackages.slice(0, 2))
        .onGet(`${process.env.REACT_APP_API}/packages?is_active=1`)
        .replyOnce(404, testError("packages"));

      await store.dispatch(handleFetchAllPackages).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("ALL_PACKAGES"),
          receiveSucceeded("ALL_PACKAGES", testPackages.slice(0, 2)),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testPackages.slice(0, 2));

        store.clearActions();
      });

      return store.dispatch(handleFetchAllPackages).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("ALL_PACKAGES"),
          receiveFailed("ALL_PACKAGES", testError("packages")),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleArchivePackage archives package", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/packages/archive?ids=1`)
        .replyOnce(200);

      await store.dispatch(handleArchivePackage(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          archiveStarted("PACKAGE"),
          archiveEnded("PACKAGE"),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleDeletePackage deletes package", async () => {
      httpMock
        .onPut(`${process.env.REACT_APP_API}/packages/delete?ids=1`)
        .replyOnce(200)
        .onPut(`${process.env.REACT_APP_API}/packages/delete?ids=1`)
        .replyOnce(200);

      await store.dispatch(handleDeletePackage(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          deleteStarted("PACKAGE"),
          deleteEnded("PACKAGE", null),
        ];

        expect(receivedActions).toEqual(expectedActions);

        store.clearActions();
      });

      return store.dispatch(handleDeletePackage(1, true)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          deleteStarted("PACKAGE"),
          deleteEnded("PACKAGE", 1),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleUpdatePackages updates packages", async () => {
      const testBody = {
        callParams: {
          idString: "1",
          updateData: [{ package_name: "new test package" }],
        },
      };
      const testUpdatedPackage = {
        package_name: "new test package",
        ...testPackages[0],
      };

      httpMock
        .onPut(`${process.env.REACT_APP_API}/packages`)
        .replyOnce(200, testUpdatedPackage)
        .onPut(`${process.env.REACT_APP_API}/packages`)
        .replyOnce(200, testUpdatedPackage)
        .onGet(`${process.env.REACT_APP_API}/packages?is_active=1&job_ids=1`)
        .replyOnce(200, testPackages)
        .onPut(`${process.env.REACT_APP_API}/packages`)
        .replyOnce(400, testError(null, "Failed to update specified package."));

      await store
        .dispatch(handleUpdatePackages(1, { package_name: "new test package" }))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("PACKAGES"),
            updateSucceeded("PACKAGES", testUpdatedPackage),
          ];

          expect(httpMock.history.put[0].data).toBe(JSON.stringify(testBody));
          expect(receivedActions).toEqual(expectedActions);

          store.clearActions();
        });

      await store
        .dispatch(
          handleUpdatePackages(
            1,
            { package_name: "new test package" },
            [[1]],
            1
          )
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("PACKAGES"),
            updateSucceeded("PACKAGES", testUpdatedPackage),
            receiveStarted("PACKAGES"),
            receiveSucceeded("PACKAGES", testPackages),
          ];

          expect(httpMock.history.put[0].data).toBe(JSON.stringify(testBody));
          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[3].payload).toEqual(testPackages);

          store.clearActions();
        });

      return store
        .dispatch(
          handleUpdatePackages(100, { package_name: "test new item failed" })
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            updateStarted("PACKAGES"),
            updateFailed(
              "PACKAGES",
              testError(null, "Failed to update specified package.")
            ),
          ];

          expect(httpMock.history.put[0].data).toBe(JSON.stringify(testBody));
          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(
            testError(null, "Failed to update specified package.")
          );
        });
    });

    it("handleFetchStatusTrackerStages fetches stages for status tracker", async () => {
      const testStages = [
        {
          stage_status_group_name: "test 1",
          total_items: 1,
          stage_status_group_breakout: [
            {
              stage_id: 1,
              stage_name: "test stage 1",
              total_items: 1,
              completed_items: 0,
            },
          ],
        },
      ];

      httpMock
        .onGet(`${process.env.REACT_APP_API}/packages/status-tracker/1`)
        .replyOnce(200, testStages)
        .onGet(`${process.env.REACT_APP_API}/packages/status-tracker/10`)
        .replyOnce(400, testError(null, "No status tracker stages found."));

      await store.dispatch(handleFetchStatusTrackerStages(1)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveSucceeded("PACKAGES_STATUS_TRACKER", testStages),
        ];

        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[0].payload).toEqual(testStages);

        store.clearActions();
      });

      return store.dispatch(handleFetchStatusTrackerStages(10)).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveFailed(
            "PACKAGES_STATUS_TRACKER",
            testError(null, "No status tracker stages found.").error
          ),
        ];

        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleCreatePackages creates packages", async () => {
      const type = "ADDITIONAL_PACKAGES";
      const testResponse = [[], [], [{ id: 1 }]];
      const testBody = {
        app_type: "fab",
        callParams: {
          data: [
            {
              job_id: 2,
              package_name: "Test",
              number: "Test",
              due_date: "2022-01-04",
              description: null,
              budget_hours: null,
              work_flow_id: 15,
              area: null,
              exclude_float_mode: 0,
            },
          ],
        },
      };

      httpMock
        .onPost(`${process.env.REACT_APP_API}/packages`)
        .replyOnce(200, testResponse)
        .onPost(`${process.env.REACT_APP_API}/packages`)
        .replyOnce(400, [[], [], []]);

      await store
        .dispatch(handleCreatePackages(2, [testBody.callParams.data[0]]))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [receiveSucceeded(type, testResponse[2])];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[0].payload).toEqual(testResponse[2]);
          expect(httpMock.history.post[0].data).toBe(JSON.stringify(testBody));

          store.clearActions();
        });

      return store
        .dispatch(handleCreatePackages(2, [testBody.callParams.data[0]]))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [];

          expect(receivedActions).toEqual(expectedActions);
        });
    });
  });
  describe("Packages Column Defs", () => {
    const defaultHeaders = [
      "",
      "",
      "% Complete",
      "Area",
      "Budget Hours",
      "Drawings",
      "Due Date",
      "Est. Hours +/-",
      "Fab Completed On",
      "FP Budget",
      "Hours Left",
      "Job #",
      "Job Name",
      "Manage",
      "Package #",
      "Package ID",
      "Package Name",
      "Status",
      "Total Hours",
      "Work Flow",
      "Work Items",
    ];
    const testPackages = [
      {
        id: 32,
        job_name: "REVIT test",
        job_number: "0306159-1",
        target_date: "2030-10-12T05:00:00.000Z",
        unix_target_date: 1918008000,
        description: null,
        address: null,
        city: null,
        state: null,
        zip: null,
        archived: 0,
        latitude: null,
        longitude: null,
        budget_hours: 1500,
        fp_budget: 652.6275,
        percent_complete: 0,
        est_time_left: 652.6275,
        hours_ahead_behind: 0,
        heat_numbers_required: 0,
        hold_points_required: 0,
        active: 1,
        archived_on: null,
        archived_by: null,
        created_by: 1,
        deleted: 0,
        deleted_by: null,
        deleted_on: null,
        cad_status_flow_id: null,
        prod_time: 0,
        cost_time: 0,
        package_count: 8,
        drawings_count: 95,
        work_item_count: 519,
        assigned_drawings_count: 95,
        assigned_packages_count: 8,
        assigned_work_item_count: 519,
        fab_completed_on: null,
      },
    ];
    const sortState = { sorting_column_name: "id", sorting_method: "asc" };
    const testStore = {
      getState: () => ({ profileData: testStore.profileData }),
      profileData: {
        systemSettings: {
          date_display: "MM-DD-YYYY",
          timezone: "America/Chicago",
        },
        permissions: [279, 280, 281],
      },
    };

    const moreInfoClick = jest.fn();
    const toggleMoreInfo = jest.fn();
    const togglePDFViewer = jest.fn();

    let populatedColumns;

    beforeEach(() => {
      populatedColumns = packagesColumnDefs(
        testPackages,
        moreInfoClick,
        toggleMoreInfo,
        togglePDFViewer,
        sortState,
        testStore
      );
    });

    it("Packages headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);
      expect(columnHeaders).toEqual(defaultHeaders);
    });

    describe("PACKAGE NUMBER", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Package #");
      });
      it("valueParser", () => {
        const newValueParams = {
          oldValue: "oldValue",
          newValue: "newValue",
          data: {
            job_number: "1",
            id: 1,
          },
        };
        const oldValueParams = {
          oldValue: "oldValue",
          newValue: "",
          data: {
            job_number: "2",
            id: 1,
          },
        };
        expect(column.valueParser(oldValueParams)).toEqual("oldValue");
        expect(column.valueParser(newValueParams)).toEqual("newValue");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            number: 1,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(1);
      });
    });

    describe("PACKAGE NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Package Name");
      });
      it("valueParser", () => {
        const newValueParams = {
          oldValue: "oldValue",
          newValue: "newValue",
          data: {
            name: "test",
            id: 1,
          },
        };
        const oldValueParams = {
          oldValue: "oldValue",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };
        expect(column.valueParser(oldValueParams)).toEqual("oldValue");
        expect(column.valueParser(newValueParams)).toEqual("newValue");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            package_name: "test job",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test job");
      });
      it("valueFormatter Package Name", () => {
        expect(column.valueFormatter()).toEqual({ wizardPermission: true });
      });
    });

    describe("DRAWINGS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Drawings");
      });
      it("valueGetter", () => {
        const params = {
          data: {
            assigned_drawings_count: 1,
          },
        };
        const noCountParams = {
          data: {},
        };
        expect(column.valueGetter(params)).toEqual(1);
        expect(column.valueGetter(noCountParams)).toEqual(0);
      });
      it("Comparator", () => {
        expect([20, 2, 67, 38, 8].sort(column.comparator)).toEqual([
          2,
          8,
          20,
          38,
          67,
        ]);
      });
    });

    describe("JOB NAME", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Job Name");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            job_name: "test job",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test job");
      });
      it("valueFormatter Job Name", () => {
        expect(column.valueFormatter()).toEqual({ wizardPermission: true });
      });
    });

    describe("JOB #", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Job #");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            job_number: "123456",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("123456");
      });
    });

    describe("WORK ITEMS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Work Items");
      });
      it("valueGetter", () => {
        const params = {
          data: {
            assigned_work_item_count: 1,
          },
        };
        const noCountParams = {
          data: {},
        };
        expect(column.valueGetter(params)).toEqual(1);
        expect(column.valueGetter(noCountParams)).toEqual(0);
      });
      it("Comparator", () => {
        expect([1, 2, 3, 4, 6, 5].sort(column.comparator)).toEqual([
          1,
          2,
          3,
          4,
          5,
          6,
        ]);
      });
    });

    describe("AREA", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Area");
      });
      it("valueParser", () => {
        const newValueParams = {
          oldValue: "oldValue",
          newValue: "newValue",
          data: {
            name: "test",
            id: 1,
          },
        };
        const oldValueParams = {
          oldValue: "oldValue",
          newValue: "",
          data: {
            name: "test",
            id: 1,
          },
        };
        expect(column.valueParser(oldValueParams)).toEqual(null);
        expect(column.valueParser(newValueParams)).toEqual("newValue");
      });
      it("getQuickFilterText gets value from params.data", () => {
        const params = {
          data: {
            area: "test area",
          },
        };
        expect(column.getQuickFilterText(params)).toEqual("test area");
      });
    });

    describe("DUE DATE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Due Date");
      });
      it("valueParser", () => {
        const params = {
          oldValue: 1634533200,
          newValue: "10-20-2021",
        };
        const badParams = {
          oldValue: "",
          newValue: "",
        };
        expect(column.valueParser(params)).toEqual(1634706000);
        expect(column.valueParser(badParams)).toEqual("");
      });
      it("valueFormatter", () => {
        const params = {
          value: 1621018878,
        };
        const badParams = {
          value: "",
        };
        expect(column.valueFormatter(params)).toEqual("05-14-2021");
        expect(column.valueFormatter(badParams)).toEqual("-");
      });
      it("getQuickFilterText", () => {
        const params = {
          colDef: {
            ...column,
          },
          value: "1634706000",
        };
        expect(column.getQuickFilterText(params)).toEqual("1634706000");
      });
    });

    describe("PERCENT COMPLETE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "% Complete");
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            percent_complete: 15,
          },
        };
        const emptyParams = {
          data: {},
        };
        expect(column.valueFormatter(params)).toEqual(15);
        expect(column.valueFormatter(emptyParams)).toEqual("0%");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            percent_complete: 15,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(15);
      });
    });

    describe("BUDGET HOURS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Budget Hours");
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            budget_hours: 2.45999,
          },
        };
        const emptyParams = {
          data: {},
        };
        expect(column.valueFormatter(params)).toEqual("2.46");
        expect(column.valueFormatter(emptyParams)).toEqual("0.00");
      });
      it("valueParser", () => {
        const params = {
          oldValue: 10,
          newValue: 100,
        };
        const badParams = {
          oldValue: 10,
          newValue: "",
        };
        expect(column.valueParser(params)).toEqual(100);
        expect(column.valueParser(badParams)).toEqual(10);
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            budget_hours: 15,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(15);
      });
    });

    describe("FP BUDGET", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "FP Budget");
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            fp_budget: 2.45999,
          },
        };
        const emptyParams = {
          data: {},
        };
        expect(column.valueFormatter(params)).toEqual("2.5");
        expect(column.valueFormatter(emptyParams)).toEqual("0.0");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            fp_budget: 15,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(15);
      });
    });

    describe("HOURS LEFT", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Hours Left");
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            est_time_left: 2.45999,
          },
        };
        const emptyParams = {
          data: {},
        };
        expect(column.valueFormatter(params)).toEqual("2.5");
        expect(column.valueFormatter(emptyParams)).toEqual("0.0");
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            est_time_left: 15,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(15);
      });
    });

    describe("EST. HOURS +/-", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Est. Hours +/-"
        );
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            hours_ahead_behind: 2.45999,
          },
        };
        const emptyParams = {
          data: {},
        };
        expect(column.valueFormatter(params)).toEqual("2.5");
        expect(column.valueFormatter(emptyParams)).toEqual(0);
      });
      it("getQuickFilterText", () => {
        const params = {
          data: {
            hours_ahead_behind: 15,
          },
        };
        expect(column.getQuickFilterText(params)).toEqual(15);
      });
    });

    describe("TOTAL HOURS", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Total Hours");
      });
      const params = {
        data: {
          cost_time: 200,
        },
      };
      it("valueFormatter", () => {
        expect(column.valueFormatter(params)).toEqual("200.0");
      });
      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual(200);
      });
    });

    describe("WORK FLOW", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Work Flow");
      });
      const params = {
        value: "Britton Test,EC Test Flow",
        data: {
          work_flow_names: "Britton Test,EC Test Flow",
        },
      };
      it("valueFormatter", () => {
        expect(column.valueFormatter(params)).toEqual("Multi");
      });
      it("getQuickFilterText", () => {
        expect(column.getQuickFilterText(params)).toEqual("Multi");
      });
    });

    describe("MANAGE", () => {
      let column;
      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Manage");
      });
      it("valueFormatter", () => {
        const params = {
          data: {
            moreInfoClick,
            toggleMoreInfo,
          },
        };
        expect(column.valueFormatter(params)).toEqual({
          moreInfoClick,
          toggleMoreInfo,
        });
      });
    });
  });
});
