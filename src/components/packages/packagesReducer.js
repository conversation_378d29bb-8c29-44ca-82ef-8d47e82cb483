const initialState = {
  isLoading: false,
  error: null,
  packages: [],
  allPackages: [],
  statusTracker: [],
  savedColumnState: null,
  storedListOfPackages: [],
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_PACKAGES_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_PACKAGES_SUCCEEDED":
      return { ...state, isLoading: false, error: null, packages: payload };
    case "RECEIVE_PACKAGES_FAILED":
      return { ...state, isLoading: false, error: payload, packages: [] };
    case "RECEIVE_ALL_PACKAGES_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_ALL_PACKAGES_SUCCEEDED":
      return { ...state, isLoading: false, error: null, allPackages: payload };
    case "RECEIVE_ALL_PACKAGES_FAILED":
      return { ...state, isLoading: false, error: payload, allPackages: [] };
    case "UPDATE_PACKAGES_STARTED":
      return { ...state, isLoading: true };
    case "UPDATE_PACKAGES_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "UPDATE_PACKAGES_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "DELETE_PACKAGE_STARTED":
      return { ...state, isLoading: true };
    case "DELETE_PACKAGE_ENDED":
      return {
        ...state,
        isLoading: false,
        packages:
          payload !== null
            ? state.packages.filter((p) => p.id !== payload)
            : state.packages,
      };
    case "ARCHIVE_PACKAGE_STARTED":
      return { ...state, isLoading: true };
    case "ARCHIVE_PACKAGE_ENDED":
      return { ...state, isLoading: false };
    case "RECEIVE_PACKAGES_STATUS_TRACKER_SUCCEEDED":
      return {
        ...state,
        statusTracker: payload,
      };
    case "RECEIVE_PACKAGES_STATUS_TRACKER_FAILED":
      return { ...state, statusTracker: [] };
    case "RECEIVE_PACKAGES_COLUMN_STATE_STARTED":
      return { ...state, savedColumnState: null };
    case "RECEIVE_PACKAGES_COLUMN_STATE_SUCCEEDED":
      return { ...state, savedColumnState: payload };
    case "RECEIVE_PACKAGES_COLUMN_STATE_FAILED":
      return { ...state, savedColumnState: [] };
    case "RECEIVE_ADDITIONAL_PACKAGES_SUCCEEDED":
      return { ...state, packages: [...payload, ...state.packages] };
    case "RECEIVE_PACKAGES_CLEAR_COLUMN_STATE_SUCCEEDED":
      return { ...state, savedColumnState: null };
    case "RECEIVE_SET_PACKAGES_LIST_SUCCEEDED":
      return { ...state, storedListOfPackages: payload };
    case "RECEIVE_RESET_PACKAGES_LIST_SUCCEEDED":
      return { ...state, packages: state.storedListOfPackages };
    case "RECEIVE_PACKAGES_AND_STORED_LIST_SUCCEEDED":
      return { ...state, packages: payload, storedListOfPackages: payload };
    default:
      return state;
  }
}
