// NPM PACKAGE IMPORTS
import React from "react";

// CONSTANT IMPORTS
import { dependencies } from "./creditsConstants";

// STYLES IMPORTS
import "./stylesCredits.scss";

// EXPORTS
const Credits = () => {
  const sortedDeps = dependencies.sort((a, b) => a.name.localeCompare(b.name));
  return (
    <div className="credits-container">
      <h3>Credits</h3>
      <div className="credits-list">
        {sortedDeps?.map((dep) => (
          <p key={dep.id}>
            <a target="_blank" rel="noopener noreferrer" href={dep.url}>
              {dep.name}
            </a>{" "}
            - {dep.license} License
          </p>
        ))}
      </div>
    </div>
  );
};

export default Credits;
