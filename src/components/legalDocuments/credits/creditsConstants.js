export const dependencies = [
  {
    id: 1,
    name: "axios",
    url: "https://github.com/axios/axios",
    license: "MIT",
  },
  {
    id: 2,
    name: "chart.js",
    url: "https://github.com/chartjs/Chart.js",
    license: "MIT",
  },
  {
    id: 3,
    name: "datadog",
    url: "https://github.com/DataDog/browser-sdk",
    license: "Apache-2.0",
  },
  {
    id: 4,
    name: "eslint-plugin-react",
    url: "https://github.com/yannickcr/eslint-plugin-react",
    license: "MIT",
  },
  {
    id: 5,
    name: "html2canvas",
    url: "https://github.com/niklasvh/html2canvas",
    license: "MIT",
  },
  {
    id: 6,
    name: "jsonwebtoken",
    url: "https://github.com/auth0/node-jsonwebtoken",
    license: "MIT",
  },
  {
    id: 7,
    name: "fortawesome",
    url: "https://github.com/FortAwesome/Font-Awesome",
    license: "MIT",
  },
  {
    id: 8,
    name: "mathjs",
    url: "https://github.com/josdejong/mathjs",
    license: "Apache-2.0",
  },
  {
    id: 9,
    name: "moment",
    url: "https://github.com/moment/moment",
    license: "MIT",
  },
  {
    id: 10,
    name: "moment-timezone",
    url: "https://github.com/moment/moment-timezone",
    license: "MIT",
  },
  {
    id: 11,
    name: "npm-force-resolutions",
    url: "https://github.com/rogeriochaves/npm-force-resolutions",
    license: "MIT",
  },
  {
    id: 12,
    name: "polished",
    url: "https://github.com/styled-components/polished",
    license: "MIT",
  },
  {
    id: 13,
    name: "pspdfkit",
    url: "https://pspdfkit.com/",
    license: "MIT",
  },
  {
    id: 14,
    name: "react",
    url: "https://github.com/facebook/react",
    license: "MIT",
  },
  {
    id: 15,
    name: "react-chartjs-2",
    url: "https://github.com/reactchartjs/react-chartjs-2",
    license: "MIT",
  },
  {
    id: 16,
    name: "react-datepicker",
    url: "https://github.com/Hacker0x01/react-datepicker",
    license: "MIT",
  },
  {
    id: 17,
    name: "react-dnd",
    url: "https://github.com/react-dnd/react-dnd",
    license: "MIT",
  },
  {
    id: 18,
    name: "react-dnd-html5-backend",
    url: "https://github.com/react-dnd/react-dnd-html5-backend",
    license: "MIT",
  },
  {
    id: 19,
    name: "react-flow-renderer",
    url: "https://github.com/wbkd/react-flow",
    license: "MIT",
  },
  {
    id: 20,
    name: "react-icons",
    url: "https://github.com/react-icons/react-icons",
    license: "MIT",
  },
  {
    id: 21,
    name: "react-is",
    url: "https://github.com/facebook/react/tree/main/packages/react-is",
    license: "MIT",
  },
  {
    id: 22,
    name: "react-redux",
    url: "https://github.com/reduxjs/react-redux",
    license: "MIT",
  },
  {
    id: 23,
    name: "react-router-dom",
    url: "https://github.com/remix-run/react-router",
    license: "MIT",
  },
  {
    id: 24,
    name: "react-scripts",
    url:
      "https://github.com/facebook/create-react-app/tree/main/packages/react-scripts",
    license: "MIT",
  },
  {
    id: 25,
    name: "react-select",
    url: "https://github.com/JedWatson/react-select",
    license: "MIT",
  },
  {
    id: 26,
    name: "react-switch",
    url: "https://github.com/markusenglund/react-switch",
    license: "MIT",
  },
  {
    id: 27,
    name: "redux-thunk",
    url: "https://github.com/reduxjs/redux-thunk",
    license: "MIT",
  },
  {
    id: 28,
    name: "sass",
    url: "https://github.com/sass/sass",
    license: "MIT",
  },
  {
    id: 29,
    name: "seedrandom",
    url: "https://github.com/davidbau/seedrandom",
    license: "MIT",
  },
  {
    id: 30,
    name: "styled-components",
    url: "https://github.com/styled-components/styled-components",
    license: "MIT",
  },
  {
    id: 31,
    name: "ag-grid-community",
    url: "https://github.com/ag-grid/ag-grid",
    license: "MIT",
  },
  {
    id: 32,
    name: "ag-grid-enterprise",
    url: "https://github.com/ag-grid/ag-grid",
    license: "MIT",
  },
  {
    id: 33,
    name: "ag-grid-react",
    url: "https://github.com/ag-grid/ag-grid",
    license: "MIT",
  },
  {
    id: 34,
    name: "google-maps-react",
    url: "https://github.com/fullstackreact/google-maps-react",
    license: "MIT",
  },
  {
    id: 35,
    name: "papaparse",
    url: "https://github.com/mholt/PapaParse",
    license: "MIT",
  },
  {
    id: 36,
    name: "react-dropzone",
    url: "https://github.com/react-dropzone/react-dropzone",
    license: "MIT",
  },
  {
    id: 37,
    name: "react-pose",
    url:
      "https://github.com/Popmotion/popmotion/tree/master/packages/react-pose",
    license: "MIT",
  },
  {
    id: 38,
    name: "rollup",
    url: "https://github.com/rollup/rollup",
    license: "MIT",
  },
  {
    id: 39,
    name: "thunk",
    url: "https://github.com/jayferd/thunk.js",
    license: "MIT",
  },
  {
    id: 40,
    name: "react-timerange-picker",
    url: "https://github.com/wojtekmaj/react-timerange-picker",
    license: "MIT",
  },
  {
    id: 41,
    name: "acorn",
    url: "https://github.com/acornjs/acorn",
    license: "MIT",
  },
  {
    id: 43,
    name: "prop-types",
    url: "https://github.com/facebook/prop-types",
    license: "MIT",
  },
  {
    id: 44,
    name: "quagga",
    url: "https://github.com/serratus/quaggaJS",
    license: "MIT",
  },
  {
    id: 45,
    name: "react-dnd-touch-backend",
    url: "https://github.com/react-dnd/react-dnd",
    license: "MIT",
  },
  {
    id: 46,
    name: "serve",
    url: "https://github.com/zeit/serve",
    license: "MIT",
  },
  {
    id: 47,
    name: "immutability-helper",
    url: "https://github.com/kolodny/immutability-helper",
    license: "MIT",
  },
  {
    id: 48,
    name: "prop-types",
    url: "https://github.com/facebook/prop-types",
    license: "MIT",
  },
  {
    id: 49,
    name: "superagent",
    url: "https://github.com/visionmedia/superagent",
    license: "MIT",
  },
  {
    id: 50,
    name: "bcryptjs",
    url: "https://github.com/dcodeIO/bcrypt.js",
    license: "MIT",
  },
  {
    id: 51,
    name: "body-parser",
    url: "https://github.com/expressjs/body-parser",
    license: "MIT",
  },
  {
    id: 52,
    name: "cors",
    url: "https://github.com/expressjs/cors",
    license: "MIT",
  },
  {
    id: 53,
    name: "cross-env",
    url: "https://github.com/kentcdodds/cross-env",
    license: "MIT",
  },
  {
    id: 54,
    name: "csvtojson",
    url: "https://github.com/Keyang/node-csvtojson",
    license: "MIT",
  },
  {
    id: 55,
    name: "debug",
    url: "https://github.com/visionmedia/debug",
    license: "MIT",
  },
  {
    id: 56,
    name: "dotenv",
    url: "https://github.com/motdotla/dotenv",
    license: "MIT",
  },
  {
    id: 57,
    name: "eslint",
    url: "https://github.com/eslint/eslintrc",
    license: "MIT",
  },
  {
    id: 58,
    name: "express",
    url: "https://github.com/expressjs/express",
    license: "MIT",
  },
  {
    id: 59,
    name: "forge-apis",
    url: "https://github.com/Autodesk-Forge/forge-api-nodejs-client",
    license: "Apache-2.0",
  },
  {
    id: 60,
    name: "fs-extra",
    url: "https://github.com/jprichardson/node-fs-extra",
    license: "MIT",
  },
  {
    id: 61,
    name: "http-errors",
    url: "https://github.com/jshttp/http-errors",
    license: "MIT",
  },
  {
    id: 62,
    name: "joi",
    url: "https://github.com/sideway/joi",
    license: "MIT",
  },
  {
    id: 63,
    name: "joi-to-swagger",
    url: "https://github.com/Twipped/joi-to-swagger",
    license: "MIT",
  },
  {
    id: 64,
    name: "jwt-decode",
    url: "https://github.com/auth0/jwt-decode",
    license: "MIT",
  },
  {
    id: 65,
    name: "mkdirp",
    url: "https://github.com/isaacs/node-mkdirp",
    license: "MIT",
  },
  {
    id: 66,
    name: "morgan",
    url: "https://github.com/expressjs/morgan",
    license: "MIT",
  },
  {
    id: 67,
    name: "multer",
    url: "https://github.com/expressjs/multer",
    license: "MIT",
  },
  {
    id: 68,
    name: "mysql2",
    url: "https://github.com/sidorares/node-mysql2",
    license: "MIT",
  },
  {
    id: 69,
    name: "path",
    url: "https://github.com/jinder/path",
    license: "MIT",
  },
  {
    id: 70,
    name: "swagger-jsdoc",
    url: "https://github.com/Surnet/swagger-jsdoc",
    license: "MIT",
  },
  {
    id: 71,
    name: "swagger-ui-express",
    url: "https://github.com/scottie1984/swagger-ui-express",
    license: "MIT",
  },
  {
    id: 72,
    name: "dbv",
    url: "https://github.com/victorstanciu/dbv",
    license: "MIT",
  },
  {
    id: 73,
    name: "MariaDB",
    url: "https://github.com/MariaDB/server",
    license: "GNU v2",
  },
  {
    id: 74,
    name: "fpdf",
    url: "https://github.com/Setasign/FPDF",
    license: "MIT",
  },
  {
    id: 76,
    name: "sendgrid",
    url: "https://github.com/sendgrid/sendgrid-php",
    license: "MIT",
  },
  {
    id: 77,
    name: "react-dom",
    url: "https://github.com/facebook/react",
    license: "MIT",
  },
  {
    id: 78,
    name: "redux",
    url: "https://github.com/reduxjs/react-redux",
    license: "MIT",
  },
  {
    id: 79,
    name: "nodemon",
    url: "https://github.com/remy/nodemon",
    license: "MIT",
  },
  {
    id: 80,
    name: "prettier",
    url: "https://github.com/prettier/prettier",
    license: "MIT",
  },
  {
    id: 81,
    name: "pretty-quick",
    url: "https://github.com/azz/pretty-quick",
    license: "MIT",
  },
  {
    id: 82,
    name: "redux-devtools-extension",
    url: "https://github.com/zalmoxisus/redux-devtools-extension",
    license: "MIT",
  },
  {
    id: 83,
    name: "redux-mock-store",
    url: "https://github.com/arnaudbenard/redux-mock-store",
    license: "MIT",
  },
  {
    id: 84,
    name: "wojtekmaj/react-timerange-picker",
    url: "https://github.com/wojtekmaj/react-timerange-picker",
    license: "MIT",
  },
  {
    id: 85,
    name: "ncp",
    url: "https://github.com/AvianFlu/ncp",
    license: "MIT",
  },
  {
    id: 86,
    name: "supertest",
    url: "https://github.com/visionmedia/supertest",
    license: "MIT",
  },
  {
    id: 87,
    name: "@microsoft/signalr",
    url: "https://github.com/aspnet/AspNetCore",
    license: "Apache-2.0",
  },
  {
    id: 88,
    name: "@types/react",
    url: "https://github.com/DefinitelyTyped/DefinitelyTyped",
    license: "MIT",
  },
  {
    id: 89,
    name: "@types/react-dom",
    url: "https://github.com/DefinitelyTyped/DefinitelyTyped",
    license: "MIT",
  },
  {
    id: 90,
    name: "html-to-image",
    url: "https://github.com/bubkoo/html-to-image",
    license: "MIT",
  },
  {
    id: 91,
    name: "mobx",
    url: "https://github.com/mobxjs/mobx",
    license: "MIT",
  },
  {
    id: 92,
    name: "mobx-react",
    url: "https://github.com/mobxjs/mobx-react",
    license: "MIT",
  },
  {
    id: 93,
    name: "@types/jquery",
    url: "https://github.com/DefinitelyTyped/DefinitelyTyped",
    license: "MIT",
  },
  {
    id: 94,
    name: "@typescript-eslint/eslint-plugin",
    url: "https://github.com/typescript-eslint/typescript-eslint",
    license: "MIT",
  },
  {
    id: 95,
    name: "@typescript-eslint/parser",
    url: "https://github.com/typescript-eslint/typescript-eslint",
    license: "MIT",
  },
  {
    id: 96,
    name: "clean-webpack-plugin",
    url: "https://github.com/johnagan/clean-webpack-plugin",
    license: "MIT",
  },
  {
    id: 97,
    name: "css-loader",
    url: "https://github.com/webpack-contrib/css-loader",
    license: "MIT",
  },
  {
    id: 98,
    name: "html-webpack-plugin",
    url: "https://github.com/jantimon/html-webpack-plugin",
    license: "MIT",
  },
  {
    id: 99,
    name: "style-loader",
    url: "https://github.com/webpack-contrib/style-loader",
    license: "MIT",
  },
  {
    id: 100,
    name: "ts-loader",
    url: "https://github.com/TypeStrong/ts-loader",
    license: "MIT",
  },
  {
    id: 101,
    name: "typescript",
    url: "https://github.com/Microsoft/TypeScript",
    license: "Apache-2.0",
  },
  {
    id: 102,
    name: "webpack",
    url: "https://github.com/webpack/webpack",
    license: "MIT",
  },
  {
    id: 103,
    name: "webpack-cli",
    url: "https://github.com/webpack/webpack-cli",
    license: "MIT",
  },
  {
    id: 104,
    name: "webpack-dev-server",
    url: "https://github.com/webpack/webpack-dev-server",
    license: "Apache-2.0",
  },
  {
    id: 105,
    name: "vesparny/silex-simple-rest",
    url: "http://github.com/vesparny/silex-simple-rest",
    license: "MIT",
  },
  {
    id: 106,
    name: "silex",
    url: "https://github.com/silexphp/Silex",
    license: "MIT",
  },
  {
    id: 107,
    name: "mongolog",
    url: "https://github.com/Seldaek/monolog",
    license: "MIT",
  },
  {
    id: 108,
    name: "dbal",
    url: "https://github.com/doctrine/dbal",
    license: "MIT",
  },
  {
    id: 109,
    name: "carbon",
    url: "https://github.com/briannesbitt/Carbon",
    license: "MIT",
  },
  {
    id: 110,
    name: "doctrine/annotations",
    url: "https://github.com/doctrine/annotations",
    license: "MIT",
  },
  {
    id: 111,
    name: "doctrine/cache",
    url: "https://github.com/doctrine/cache",
    license: "MIT",
  },
  {
    id: 112,
    name: "doctrine/collections",
    url: "https://github.com/doctrine/collections",
    license: "MIT",
  },
  {
    id: 113,
    name: "doctrine/common",
    url: "https://github.com/doctrine/common",
    license: "MIT",
  },
  {
    id: 114,
    name: "doctrine/inflector",
    url: "https://github.com/doctrine/inflector",
    license: "MIT",
  },
  {
    id: 115,
    name: "doctrine/lexer",
    url: "https://github.com/doctrine/lexer",
    license: "MIT",
  },
  {
    id: 116,
    name: "paragonie/random_compat",
    url: "https://github.com/paragonie/random_compat",
    license: "MIT",
  },
  {
    id: 117,
    name: "pimple",
    url: "https://github.com/silexphp/Pimple",
    license: "MIT",
  },
  {
    id: 118,
    name: "psr/log",
    url: "https://github.com/php-fig/log",
    license: "MIT",
  },
  {
    id: 119,
    name: "symfony/browser-kit",
    url: "https://github.com/symfony/browser-kit",
    license: "MIT",
  },
  {
    id: 120,
    name: "symfony/debug",
    url: "https://github.com/symfony/debug",
    license: "MIT",
  },
  {
    id: 121,
    name: "symfony/dom-crawler",
    url: "https://github.com/symfony/dom-crawler",
    license: "MIT",
  },
  {
    id: 122,
    name: "symfony/event-dispatcher",
    url: "https://github.com/symfony/event-dispatcher",
    license: "MIT",
  },
  {
    id: 123,
    name: "symfony/http-foundation",
    url: "https://github.com/symfony/http-foundation",
    license: "MIT",
  },
  {
    id: 124,
    name: "symfony/http-kernel",
    url: "https://github.com/symfony/http-kernel",
    license: "MIT",
  },
  {
    id: 125,
    name: "symfony/inflector",
    url: "https://github.com/symfony/inflector",
    license: "MIT",
  },
  {
    id: 126,
    name: "symfony/polyfill-mbstring",
    url: "https://github.com/symfony/polyfill-mbstring",
    license: "MIT",
  },
  {
    id: 127,
    name: "symfony/polyfill-php56",
    url: "https://github.com/symfony/polyfill-php56",
    license: "MIT",
  },
  {
    id: 128,
    name: "symfony/polyfill-php70",
    url: "https://github.com/symfony/polyfill-php70",
    license: "MIT",
  },
  {
    id: 129,
    name: "symfony/polyfill-util",
    url: "https://github.com/symfony/polyfill-util",
    license: "MIT",
  },
  {
    id: 130,
    name: "symfony/property-access",
    url: "https://github.com/symfony/property-access",
    license: "MIT",
  },
  {
    id: 131,
    name: "symfony/routing",
    url: "https://github.com/symfony/routing",
    license: "MIT",
  },
  {
    id: 132,
    name: "symfony/security",
    url: "https://github.com/symfony/security",
    license: "MIT",
  },
  {
    id: 133,
    name: "symfony/translation",
    url: "https://github.com/symfony/translation",
    license: "MIT",
  },
  {
    id: 134,
    name: "padraic/mockery",
    url: "https://github.com/padraic/mockery",
    license: "BSD-3-Clause",
  },
  {
    id: 135,
    name: "sebastianbergmann/php-code-coverage",
    url: "https://github.com/sebastianbergmann/php-code-coverage",
    license: "BSD-3-Clause",
  },
  {
    id: 136,
    name: "sebastianbergmann/php-file-iterator",
    url: "https://github.com/sebastianbergmann/php-file-iterator",
    license: "BSD-3-Clause",
  },
  {
    id: 137,
    name: "sebastianbergmann/php-text-template",
    url: "https://github.com/sebastianbergmann/php-text-template",
    license: "BSD-3-Clause",
  },
  {
    id: 138,
    name: "php-timer",
    url: "https://github.com/sebastianbergmann/php-timer",
    license: "BSD-3-Clause",
  },
  {
    id: 139,
    name: "sebastianbergmann/php-token-stream",
    url: "https://github.com/sebastianbergmann/php-token-stream",
    license: "BSD-3-Clause",
  },
  {
    id: 140,
    name: "sebastianbergmann/phpunit",
    url: "https://github.com/sebastianbergmann/phpunit",
    license: "BSD-3-Clause",
  },
  {
    id: 141,
    name: "sebastianbergmann/pjpunit-mock-objects",
    url: "https://github.com/sebastianbergmann/phpunit-mock-objects",
    license: "BSD-3-Clause",
  },
  {
    id: 142,
    name: "sebastianbergmann/comparator",
    url: "https://github.com/sebastianbergmann/comparator",
    license: "BSD-3-Clause",
  },
  {
    id: 143,
    name: "sebastianbergmann/diff",
    url: "https://github.com/sebastianbergmann/diff",
    license: "BSD-3-Clause",
  },
  {
    id: 144,
    name: "sebastianbergmann/environment",
    url: "https://github.com/sebastianbergmann/environment",
    license: "BSD-3-Clause",
  },
  {
    id: 145,
    name: "sebastianbergmann/exporter",
    url: "https://github.com/sebastianbergmann/exporter",
    license: "BSD-3-Clause",
  },
  {
    id: 146,
    name: "sebastianbergmann/recursion-context",
    url: "https://github.com/sebastianbergmann/recursion-context",
    license: "BSD-3-Clause",
  },
  {
    id: 147,
    name: "sebastianbergmann/version",
    url: "https://github.com/sebastianbergmann/version",
    license: "BSD-3-Clause",
  },
  {
    id: 148,
    name: "symfony/yaml",
    url: "https://github.com/symfony/yaml",
    license: "MIT",
  },
  {
    id: 149,
    name: "jquery",
    url: "https://github.com/jquery/jqueryui.com",
    license: "MIT",
  },
  {
    id: 150,
    name: "hammerjs",
    url: "https://github.com/hammerjs/hammer.js",
    license: "MIT",
  },
  {
    id: 151,
    name: "DataTables",
    url: "https://github.com/DataTables/DataTables",
    license: "MIT",
  },
  {
    id: 152,
    name: "Select-DataTables",
    url: "https://github.com/DataTables/Dist-DataTables-Select-DataTables",
    license: "MIT",
  },
  {
    id: 153,
    name: "echarts",
    url: "https://github.com/apache/echarts",
    license: "Apache-2.0",
  },
  {
    id: 154,
    name: "requestAnimationFrame polyfill",
    url: "https://gist.github.com/paulirish/1579671",
    license: "MIT",
  },
  {
    id: 155,
    name: "fullcalendar",
    url: "https://github.com/fullcalendar/fullcalendar",
    license: "MIT",
  },
  {
    id: 156,
    name: "jquery-timepicker",
    url: "http://jonthornton.github.io/jquery-timepicker/",
    license: "Apache-2.0",
  },
  {
    id: 157,
    name: "pnotify",
    url: "sciactive.com/pnotify/",
    license: "Apache-2.0",
  },
  {
    id: 158,
    name: "jquery-ui-touch-punch",
    url: "https://github.com/furf/jquery-ui-touch-punch",
    license: "MIT",
  },
  {
    id: 159,
    name: "bootstrap",
    url: "https://github.com/twbs/bootstrap",
    license: "MIT",
  },
  {
    id: 160,
    name: "normalize.css",
    url: "github.com/necolas/normalize.css",
    license: "MIT",
  },
  {
    id: 161,
    name: "fontawesome font",
    url: "http://fontawesome.io",
    license: "SIL OFL 1.1",
  },
  {
    id: 162,
    name: "fontawesome css",
    url: "http://fontawesome.io",
    license: "MIT",
  },
  {
    id: 163,
    name: "icomoon",
    url: "https://icomoon.io/",
    license: "GPL / CC BY 4.0",
  },
  {
    id: 164,
    name: "animate",
    url: "http://daneden.me/animate",
    license: "MIT",
  },
  {
    id: 165,
    name: "summernote",
    url: "https://github.com/summernote/summernote/",
    license: "MIT",
  },
  {
    id: 166,
    name: "bootstrap-select",
    url: "https://github.com/snapappointments/bootstrap-select",
    license: "MIT",
  },
  {
    id: 167,
    name: "php-barcode",
    url: "https://github.com/davidscotttufts/php-barcode/",
    license: "MIT",
  },
  {
    id: 168,
    name: "roboto",
    url: "https://github.com/googlefonts/roboto",
    license: "Apache-2.0",
  },
  {
    id: 169,
    name: "popper",
    url: "https://unpkg.com/popper.js@1.16.1/dist/umd/popper.min.js",
    license: "MIT",
  },
  {
    id: 170,
    name: "pep",
    url: "https://github.com/jquery-archive/PEP",
    license: "MIT",
  },
  {
    id: 171,
    name: "uri.js",
    url: "http://medialize.github.io/URI.js/",
    license: "MIT",
  },
  {
    id: 172,
    name: "lodash",
    url: "https://github.com/lodash/lodash",
    license: "MIT",
  },
  {
    id: 173,
    name: "datetimepicker",
    url: "https://github.com/xdan/datetimepicker",
    license: "MIT",
  },
  {
    id: 174,
    name: "natural sort algorithm",
    url: "http://js-naturalsort.googlecode.com/svn/trunk/naturalSort.js",
    license: "MIT",
  },
  {
    id: 175,
    name: "jquery-cookie",
    url: "https://github.com/carhartl/jquery-cookie",
    license: "MIT",
  },
  {
    id: 176,
    name: "bootstrap-multiselect",
    url: "https://github.com/davidstutz/bootstrap-multiselect",
    license: "Apache-2.0",
  },
  {
    id: 177,
    name: "bootstrap-fileinput",
    url: "https://github.com/kartik-v/bootstrap-fileinput",
    license: "BSD-3-Clause",
  },
  {
    id: 178,
    name: "jquery-validation",
    url: "https://github.com/jquery-validation/jquery-validation",
    license: "MIT",
  },
  {
    id: 179,
    name: "jasny-bootstrap",
    url: "http://jasny.github.io/bootstrap",
    license: "Apache-2.0",
  },
  {
    id: 180,
    name: "select2",
    url: "https://github.com/select2/select2",
    license: "MIT",
  },
  {
    id: 181,
    name: "jquery-steps",
    url: "https://github.com/rstaib/jquery-steps",
    license: "MIT",
  },
  {
    id: 182,
    name: "prismjs",
    url: "https://github.com/PrismJS/prism/",
    license: "MIT",
  },
  {
    id: 183,
    name: "buttons",
    url: "https://datatables.net/download/",
    license: "MIT",
  },
  {
    id: 184,
    name: "fancyapps/FancyBox",
    url: "https://github.com/fancyapps/fancybox",
    license: "CCANC-3",
  },
  {
    id: 185,
    name: "bootbox",
    url: "https://github.com/makeusabrew/bootbox",
    license: "MIT",
  },
  {
    id: 186,
    name: "pdfmake",
    url: "https://github.com/bpampuch/pdfmake",
    license: "MIT",
  },
  {
    id: 187,
    name: "jszip",
    url: "https://github.com/Stuk/jszip",
    license: "MIT",
  },
  {
    id: 188,
    name: "nodeca/pako",
    url: "https://github.com/nodeca/pako/",
    license: "MIT",
  },
  {
    id: 189,
    name: "jquery",
    url: "https://github.com/jquery/jquery",
    license: "MIT",
  },
  {
    id: 190,
    name: "Buttons-DataTables2",
    url: "https://github.com/DataTables/Dist-DataTables-Buttons-DataTables2",
    license: "MIT",
  },
  {
    id: 191,
    name: "ColReorder-DataTables",
    url: "https://github.com/DataTables/Dist-DataTables-ColReorder-DataTables",
    license: "MIT",
  },
  {
    id: 192,
    name: "KeyTable-DataTables",
    url: "https://github.com/DataTables/Dist-DataTables-KeyTable-DataTables",
    license: "MIT",
  },
  {
    id: 193,
    name: "RowReorder-DataTables",
    url: "https://github.com/DataTables/Dist-DataTables-RowReorder-DataTables",
    license: "MIT",
  },
  {
    id: 193,
    name: "gliphicons_halflings",
    url: "https://glyphicons.com/",
    license: "MIT",
  },
  {
    id: 194,
    name: "node-polyfill-webpack-plugin",
    url: "https://github.com/Richienb/node-polyfill-webpack-plugin",
    license: "MIT",
  },
  {
    id: 195,
    name: "patch-package",
    url: "https://github.com/ds300/patch-package",
    license: "MIT",
  },
];
