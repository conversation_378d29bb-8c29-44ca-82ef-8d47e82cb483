// NPM PACKAGE IMPORTS
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

// REDUX IMPORTS
import { handleSetPageTitle } from "../../../redux/generalActions";

// TRANSLATION IMPORTS
import useTranslations from "../../../hooks/useTranslations";
import termsOfServiceTranslations from "./termsOfServiceTranslations.json";

// STYLE IMPORTS
import "./stylesTermsOfService.scss";

// EXPORTS
const TermsOfService = () => {
  const translate = useTranslations(termsOfServiceTranslations);
  const dispatch = useDispatch();
  const { eula } = useSelector((state) => state.termsOfServiceData);
  const { navExpanded } = useSelector((state) => state.generalData);

  useEffect(() => {
    dispatch(handleSetPageTitle(translate("Terms of Service")));
  }, []);

  return (
    <>
      {eula && (
        <div className="terms-of-service">
          <iframe
            className={`terms-of-service-content ${
              navExpanded ? "nav-expanded" : ""
            }`}
            srcDoc={eula.terms_text}
            title="terms-of-service"
          />
        </div>
      )}
    </>
  );
};

export default TermsOfService;
