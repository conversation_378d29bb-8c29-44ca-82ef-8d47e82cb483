@import "../../styles/colors.scss";

div.terms-of-service {
  background-color: #fff;
  padding: 10px;

  display: grid;
  justify-content: center;
  row-gap: 10px;

  & > iframe.terms-of-service-content {
    border: none;
  }

  &.modal > iframe.terms-of-service-content {
    height: 600px;
    width: 500px;
  }

  &:not(.modal) > iframe.terms-of-service-content {
    height: calc(100vh - 140px);
    width: calc(100vw - 55px);

    &.nav-expanded {
      width: calc(100vw - 255px);
    }
  }

  & > button.terms-of-service-accept {
    width: 150px;
    margin: 0 auto;
    height: 40px;
    padding: 0;
    background-color: $lightGreen;
    color: #fff;

    &:hover {
      background-color: darken($lightGreen, 10%);
    }
  }
}
