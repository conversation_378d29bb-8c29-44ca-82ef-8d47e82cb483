const initialState = {
  isLoading: false,
  error: null,
  eula: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_EULA_STARTED":
      return { ...state, isLoading: true, error: null, eula: null };
    case "RECEIVE_EULA_SUCCEEDED":
      return { ...state, isLoading: false, error: null, eula: payload };
    case "RECEIVE_EULA_FAILED":
      return { ...state, isLoading: false, error: payload, eula: null };
    default:
      return state;
  }
}
