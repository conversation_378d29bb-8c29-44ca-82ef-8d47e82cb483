// NPM PACKAGE IMPORTS
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";

// TRANSLATION IMPORTS
import useTranslations from "../../../hooks/useTranslations";
import termsOfServiceTranslations from "./termsOfServiceTranslations.json";

// STYLE IMPORTS
import "./stylesTermsOfService.scss";
import { handleAcceptEula } from "./termsOfServiceActions";

// EXPORTS
const TermsOfServiceModal = () => {
  const translate = useTranslations(termsOfServiceTranslations);
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state) => state.profileData);
  const { eula } = useSelector((state) => state.termsOfServiceData);

  return (
    <>
      {eula &&
        userInfo &&
        userInfo.hasOwnProperty("eula_version") &&
        eula.eula_version !== userInfo.eula_version && (
          <Modal open={true} handleClose={(f) => f}>
            <div className="terms-of-service modal">
              <iframe
                className="terms-of-service-content"
                srcDoc={eula.terms_text}
                title="Terms of Service"
              />
              <Button
                className="terms-of-service-accept"
                onClick={() => dispatch(handleAcceptEula(eula.eula_version))}
              >
                {translate("Accept")}
              </Button>
            </div>
          </Modal>
        )}
    </>
  );
};

export default TermsOfServiceModal;
