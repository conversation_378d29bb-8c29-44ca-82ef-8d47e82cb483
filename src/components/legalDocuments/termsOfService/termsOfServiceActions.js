import { fetchEula, acceptEula } from "../../../_services";

const receiveStarted = (type) => ({
  type: `RECEIVE_${type}_STARTED`,
});
const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const handleFetchEula = (dispatch) => {
  dispatch(receiveStarted("EULA"));
  return fetchEula().then((res) => {
    if (res.error) return dispatch(receiveFailed("EULA", res.error));
    return dispatch(receiveSucceeded("EULA", res[0]));
  });
};
export const handleAcceptEula = (eulaVersion) => (dispatch) => {
  return acceptEula(eulaVersion).then(() =>
    dispatch(receiveSucceeded("EULA", null))
  );
};
