import {
  fetchUsers,
  updateUserInfo,
  fetchUserTitles,
  fetchTiers,
  resetPassword,
  fetchUserStageAssignments,
  updateUserStageAssignments,
  fetchUserPermissions,
  updateUserPermissions,
  deleteUsers,
  createUser,
  resetUserPermissions,
  fetchUserProducts,
  updateUserProducts,
} from "../../_services";

export const receiveStarted = (type) => ({
  type: `RECEIVE_${type}_STARTED`,
});
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const deleteStarted = (type) => ({
  type: `DELETE_${type}_STARTED`,
});
export const deleteSucceeded = (type) => ({
  type: `DELETE_${type}_SUCCEEDED`,
});
export const deleteFailed = (type, error) => ({
  type: `DELETE_${type}_FAILED`,
  payload: error,
});

export const createStarted = (type) => ({
  type: `CREATE_${type}_STARTED`,
});
export const createSucceeded = (type) => ({
  type: `CREATE_${type}_SUCCEEDED`,
});
export const createFailed = (type, error) => ({
  type: `CREATE_${type}_FAILED`,
  payload: error,
});

export const handleFetchUsers = (includeInactive = false) => (dispatch) => {
  const type = "USERS";
  dispatch(receiveStarted(type));
  return fetchUsers({ withAssignedGroups: 1, includeInactive }).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleUpdateUserInfo = (userId, newData, refresh = false) => (
  dispatch
) => {
  const type = "USERS";
  return updateUserInfo(userId, newData).then((res) => {
    dispatch(receiveStarted(type));

    if (refresh) {
      return fetchUsers({ withAssignedGroups: 1 }).then((res) => {
        if (res.error) dispatch(receiveFailed(type, res));
        else dispatch(receiveSucceeded(type, res));

        return res;
      });
    }

    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchUserTitles = (dispatch) => {
  const type = "USER_TITLES";
  dispatch(receiveStarted(type));
  return fetchUserTitles().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchTiers = (dispatch) => {
  const type = "TIERS";
  dispatch(receiveStarted(type));
  return fetchTiers().then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleResetPassword = (userId) => (dispatch) => {
  const type = "PASSWORD";
  return resetPassword(userId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type));

    return res;
  });
};

export const handleFetchUserStageAssignments = (userId) => (dispatch) => {
  const type = "USER_STAGE_ASSIGNMENTS";
  dispatch(receiveStarted(type));
  return fetchUserStageAssignments(userId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleUpdateUserStageAssignments = (userId, stageIds, action) => (
  dispatch
) => {
  const type = "USER_STAGE_ASSIGNMENTS";
  dispatch(receiveStarted(type));
  return updateUserStageAssignments(userId, stageIds, action).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchUserPermissions = (userId) => (dispatch) => {
  const type = "PERMISSIONS";
  dispatch(receiveStarted(type));
  return fetchUserPermissions(userId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleUpdateUserPermissions = (
  userId,
  permissionIds,
  action,
  suppressRefresh = false
) => (dispatch) => {
  const type = "PERMISSIONS";
  if (suppressRefresh) {
    return updateUserPermissions(userId, permissionIds, action).then(
      (res) => res
    );
  }

  dispatch(receiveStarted(type));
  return updateUserPermissions(userId, permissionIds, action).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleDeleteUsers = (userIds) => (dispatch) => {
  const type = "USERS";
  dispatch(deleteStarted(type));
  return deleteUsers(userIds).then((res) => {
    if (res.error) dispatch(deleteFailed(type, res));
    else dispatch(deleteSucceeded(type));

    return res;
  });
};

export const handleCreateUser = (newData, refreshUsers) => (dispatch) => {
  const type = "USER";
  dispatch(createStarted(type));
  return createUser(newData).then((res) => {
    if (res.error) dispatch(createFailed(type, res));
    else dispatch(createSucceeded(type));

    if (!refreshUsers || res.error) return res;
    dispatch(receiveStarted("USERS"));
    return fetchUsers({ withAssignedGroups: 1 }).then((res) => {
      if (res.error) dispatch(receiveFailed("USERS", res));
      else dispatch(receiveSucceeded("USERS", res));

      return res;
    });
  });
};

export const handleResetUserPermissions = (userId) => (dispatch) => {
  const type = "PERMISSIONS";
  dispatch(receiveStarted(type));
  return resetUserPermissions(userId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchUserProducts = (userId) => (dispatch) => {
  const type = "USER_PRODUCTS";
  dispatch(receiveStarted(type));
  return fetchUserProducts(userId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleUpdateUserProducts = (userId, productIds, action) => (
  dispatch
) => {
  const type = "USER_PRODUCTS";
  dispatch(receiveStarted(type));
  return updateUserProducts(userId, productIds, action).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};
