@import "../styles/colors.scss";

div.users-wrapper {
  & div.action-row {
    display: flex;
    justify-content: space-between;
    color: white;
    margin-top: 20px;
  }

  & div.table-container {
    height: calc(100vh - 185px);
  }

  & div.table-container-tabs div.table-search-wrapper {
    margin: 0;
  }

  & .ag-theme-balham-dark .ag-cell-inline-editing {
    border: none;
  }
  & .ag-body-horizontal-scroll-viewport {
    overflow-x: auto;
  }
  & .ag-center-cols-viewport {
    overflow: hidden;
  }
}

// STAGE ASSIGNMENTS MODAL
div.stage-assignments-wrapper {
  height: 360px;
  min-height: 300px;
  width: 400px;
  background-color: white;

  & h2 {
    margin: 0;
    font-size: 1rem;
    background-color: $fabProBlue;
    padding: 8px;
    color: white;
  }

  & div.content-wrapper {
    margin-top: 20px;
    & p {
      margin-left: 8px;
    }

    & span {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 32px;
    }

    & button {
      background-color: transparent;
      box-shadow: none;
      border: none;
      color: $fabProBlue;
      font-size: 1rem;
      padding: 0 8px 0;
      text-decoration: underline;
      height: 32px;
    }

    & input.search-input {
      width: 250px;
      height: 32px;
      margin-left: 8px;
      margin-top: 3px;

      &::placeholder {
        font-size: 1rem;
      }
    }
  }

  & div.stages-list {
    display: flex;
    flex-direction: column;
    overflow: auto;
    height: 273px;

    & input[type="checkbox"] {
      width: 20px;
      height: 20px;
      margin-top: 12px;
    }
  }

  & label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px;
  }

  & .checked {
    background-color: $fabProBlue;
    color: white;
  }
}
