// NPM PACKAGE IMPORTS
import configureMockStore from "redux-mock-store";
import axios from "axios";
import Mock<PERSON>dapter from "axios-mock-adapter";
import thunk from "redux-thunk";

// REDUX IMPORTS
import {
  // ACTION CREATORS
  receiveStarted,
  receiveSucceeded,
  receiveFailed,
  createStarted,
  createSucceeded,
  createFailed,
  deleteStarted,
  deleteSucceeded,
  deleteFailed,
  // ACTION HANDLERS
  handleFetchUsers,
  handleUpdateUserInfo,
  handleFetchUserTitles,
  handleFetchTiers,
  handleFetchUserStageAssignments,
  handleUpdateUserStageAssignments,
  handleResetUserPermissions,
  handleCreateUser,
  handleDeleteUsers,
  handleFetchUserProducts,
  handleUpdateUserProducts,
} from "./usersActions";

describe("USERS", () => {
  let store, httpMock;

  const baseURL = `${process.env.REACT_APP_API}`;

  const testError = (message) => ({
    error: { status: 404, message },
  });

  describe("Action handlers should perform the necessary functions", () => {
    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    const testUsers = [
      { id: 1, first_name: "First", last_name: "Last" },
      { id: 2, first_name: "First2", last_name: "Last2" },
    ];

    it("handleFetchUsers", async () => {
      const type = "USERS";
      httpMock
        .onGet(
          `${baseURL}/users?app_type=fab&with_stage_count=1&send_assigned_groups=1`
        )
        .replyOnce(200, testUsers)
        .onGet(
          `${baseURL}/users?app_type=fab&with_stage_count=1&send_assigned_groups=1`
        )
        .replyOnce(404, testError("No users"));

      let response = await store.dispatch(handleFetchUsers());

      let receivedActions = store.getActions();
      let expectedActions = [
        receiveStarted(type),
        receiveSucceeded(type, testUsers),
      ];
      expect(response).toEqual(testUsers);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleFetchUsers());

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted(type),
        receiveFailed(type, testError("No users")),
      ];
      expect(response).toEqual(testError("No users"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });

    it("handleUpdateUserInfo", async () => {
      const type = "USERS";
      const updatedUser = { ...testUsers[0], first_name: "Updated" };

      httpMock.onPut(`${baseURL}/users/${1}`).replyOnce(200, updatedUser);

      let response = await store.dispatch(handleUpdateUserInfo(1, updatedUser));

      let receivedActions = store.getActions();
      let expectedActions = [
        receiveStarted(type),
        receiveSucceeded(type, updatedUser),
      ];
      expect(response).toEqual(updatedUser);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });

    it("handleFetchUserTitles", async () => {
      const type = "USER_TITLES";
      const testTitles = [
        { id: 1, title: "first" },
        { id: 2, title: "second" },
      ];

      httpMock
        .onGet(`${baseURL}/users/titles`)
        .replyOnce(200, testTitles)
        .onGet(`${baseURL}/users/titles`)
        .replyOnce(404, testError("No titles"));

      let response = await store.dispatch(handleFetchUserTitles);

      let receivedActions = store.getActions();
      let expectedActions = [
        receiveStarted(type),
        receiveSucceeded(type, testTitles),
      ];
      expect(response).toEqual(testTitles);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleFetchUserTitles);

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted(type),
        receiveFailed(type, testError("No titles")),
      ];
      expect(response).toEqual(testError("No titles"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });

    it("handleFetchTiers", async () => {
      const testTiers = [
        { id: 1, name: "first" },
        { id: 2, name: "second" },
      ];
      const type = "TIERS";

      httpMock
        .onGet(`${baseURL}/tiers`)
        .replyOnce(200, testTiers)
        .onGet(`${baseURL}/tiers`)
        .replyOnce(404, testError("No tiers"));

      let response = await store.dispatch(handleFetchTiers);

      let receivedActions = store.getActions();
      let expectedActions = [
        receiveStarted(type),
        receiveSucceeded(type, testTiers),
      ];
      expect(response).toEqual(testTiers);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleFetchTiers);

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted(type),
        receiveFailed(type, testError("No tiers")),
      ];
      expect(response).toEqual(testError("No tiers"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });

    it("handleFetchUserStageAssignments", async () => {
      const testStageAssignments = [
        { id: 1, name: "first" },
        { id: 2, name: "second" },
      ];
      const type = "USER_STAGE_ASSIGNMENTS";

      httpMock
        .onGet(`${baseURL}/work-stages/user-assignments/${1}`)
        .replyOnce(200, testStageAssignments)
        .onGet(`${baseURL}/work-stages/user-assignments/${5}`)
        .replyOnce(404, testError("No assigned stages"));

      let response = await store.dispatch(handleFetchUserStageAssignments(1));

      let receivedActions = store.getActions();
      let expectedActions = [
        receiveStarted(type),
        receiveSucceeded(type, testStageAssignments),
      ];
      expect(response).toEqual(testStageAssignments);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleFetchUserStageAssignments(5));

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted(type),
        receiveFailed(type, testError("No assigned stages")),
      ];
      expect(response).toEqual(testError("No assigned stages"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });

    it("handleUpdateUserStageAssignments", async () => {
      const testStageAssignments = [
        { id: 1, name: "first" },
        { id: 2, name: "second" },
        { id: 3, name: "third" },
      ];
      const type = "USER_STAGE_ASSIGNMENTS";

      httpMock
        .onPut(`${baseURL}/work-stages/user-assignments/${1}`)
        .replyOnce(200, testStageAssignments)
        .onPut(`${baseURL}/work-stages/user-assignments/${2}`)
        .replyOnce(404, testError("No assigned stages"));

      let response = await store.dispatch(
        handleUpdateUserStageAssignments(1, 3, "assign")
      );

      let receivedActions = store.getActions();
      let expectedActions = [
        receiveStarted(type),
        receiveSucceeded(type, testStageAssignments),
      ];
      expect(response).toEqual(testStageAssignments);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(
        handleUpdateUserStageAssignments(2, 3, "unassign")
      );

      receivedActions = store.getActions();
      expectedActions = [
        receiveStarted(type),
        receiveFailed(type, testError("No assigned stages")),
      ];
      expect(response).toEqual(testError("No assigned stages"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });

    it("handleCreateUser", async () => {
      const type = "USER";
      const createdUser = {
        id: 100,
        first_name: "Test",
        last_name: "User",
      };
      const newData = {
        first_name: "Test",
        last_name: "User",
      };

      httpMock
        .onPost(`${baseURL}/users`)
        .replyOnce(200, createdUser)
        .onPost(`${baseURL}/users`)
        .replyOnce(404, testError("Unable to create user"));

      let response = await store.dispatch(handleCreateUser(newData));
      let receivedActions = store.getActions();
      let expectedActions = [createStarted(type), createSucceeded(type)];

      expect(response).toEqual(createdUser);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleCreateUser(newData));
      receivedActions = store.getActions();
      expectedActions = [
        createStarted(type),
        createFailed(type, testError("Unable to create user")),
      ];

      expect(response).toEqual(testError("Unable to create user"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });

    it("handleDeleteUsers", async () => {
      const type = "USERS";
      const testRes = {
        affectedRows: 1,
        changedRows: 1,
      };
      httpMock
        .onPut(`${baseURL}/users/delete/1`)
        .replyOnce(200, testRes)
        .onPut(`${baseURL}/users/delete/1`)
        .replyOnce(404, testError("Unable to delete user"));

      let response = await store.dispatch(handleDeleteUsers("1"));
      let receivedActions = store.getActions();
      let expectedActions = [deleteStarted(type), deleteSucceeded(type)];

      expect(response).toEqual(testRes);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();

      response = await store.dispatch(handleDeleteUsers("1"));
      receivedActions = store.getActions();
      expectedActions = [
        deleteStarted(type),
        deleteFailed(type, testError("Unable to delete user")),
      ];

      expect(response).toEqual(testError("Unable to delete user"));
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });

    it("handleResetUserPermissions", async () => {
      const type = "PERMISSIONS";
      const testPermissions = [
        { id: 1, name: "test", is_assigned: 1 },
        { id: 2, name: "test2", is_assigned: 0 },
      ];

      httpMock
        .onPut(`${baseURL}/users/${testUsers[0].id}/permissions/reset`)
        .replyOnce(200, testPermissions);

      let response = await store.dispatch(
        handleResetUserPermissions(testUsers[0].id)
      );
      let receivedActions = store.getActions();
      let expectedActions = [
        receiveStarted(type),
        receiveSucceeded(type, testPermissions),
      ];

      expect(response).toEqual(testPermissions);
      expect(receivedActions).toEqual(expectedActions);

      store.clearActions();
    });

    describe("USER_PRODUCTS", () => {
      const type = "USER_PRODUCTS";
      const testProducts = [
        { id: 1, name: "Hangerworks" },
        { id: 2, name: "BIMPro" },
      ];

      it("handleFetchUserProducts", async () => {
        httpMock
          .onGet(`${baseURL}/users/${testUsers[0].id}/products`)
          .replyOnce(200, testProducts);

        let response = await store.dispatch(
          handleFetchUserProducts(testUsers[0].id)
        );
        let receivedActions = store.getActions();
        let expectedActions = [
          receiveStarted(type),
          receiveSucceeded(type, testProducts),
        ];

        expect(response).toEqual(testProducts);
        expect(receivedActions).toEqual(expectedActions);

        store.clearActions();
      });

      it("handleUpdateUserProducts", async () => {
        httpMock
          .onPut(`${baseURL}/users/${testUsers[0].id}/products`)
          .replyOnce(200, testProducts[0]);

        let response = await store.dispatch(
          handleUpdateUserProducts(testUsers[0].id, [2], "unassign")
        );
        let receivedActions = store.getActions();
        let expectedActions = [
          receiveStarted(type),
          receiveSucceeded(type, testProducts[0]),
        ];

        expect(response).toEqual(testProducts[0]);
        expect(receivedActions).toEqual(expectedActions);
      });
    });
  });
});
