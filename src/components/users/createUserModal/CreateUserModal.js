// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import Select from "msuite_storybook/dist/select/Select";
import Input from "msuite_storybook/dist/input/Input";

// REDUX IMPORTS
import {
  handleCreateUser,
  handleUpdateUserStageAssignments,
  handleFetchUsers,
  handleUpdateUserProducts,
} from "../usersActions";
import { handleFetchWorkStages } from "../../flows/flowsActions";

// CONSTANT IMPORTS
import { roles } from "../usersConstants";

// HELPER FUNCTION IMPORTS
import { validatePassword } from "../../../_utils";

// STYLES IMPORTS
import "./stylesCreateUserModal.scss";

const CreateUserModal = ({ open, handleClose }) => {
  const [inputs, setInputs] = useState({});
  const [tierId, setTierId] = useState(null);
  const [roleId, setRoleId] = useState(null);
  const [titleId, setTitleId] = useState(null);
  const [password, setPassword] = useState("");
  const [passwordRepeat, setPasswordRepeat] = useState("");
  const [defaultPassword, toggleDefaultPassword] = useState(true);
  const [isAdmin, toggleAdmin] = useState(false);
  const [domain, setDomain] = useState(null);
  const [error, setError] = useState(null);
  const [hangerworks, toggleHangerworks] = useState(false);
  const [bimpro, toggleBimpro] = useState(false);

  const { tiers, userTitles } = useSelector((state) => state.usersData);
  const { systemSettings, userInfo, products } = useSelector(
    (state) => state.profileData
  );
  const { workStages } = useSelector((state) => state.flowsData);

  const dispatch = useDispatch();

  useEffect(() => {
    if (!systemSettings) return;
    setDomain(systemSettings?.domain_prefix);
  }, [systemSettings]);

  useEffect(() => {
    if (!userInfo) return;
    toggleAdmin(userInfo.id === 1 ? true : false);

    // fetch stages for handleCreate
    dispatch(handleFetchWorkStages());
  }, [userInfo, dispatch]);

  const handleChange = (e) => {
    let { name, value, type, checked } = e.target;
    setInputs({ ...inputs, [name]: type === "checkbox" ? checked : value });
  };

  const handleCreate = async () => {
    const newUser = {
      ...inputs,
      tierId,
      titleId,
      roleId,
      username: `${inputs.username}`,
    };

    if (!defaultPassword) newUser.password = password;

    const user = await dispatch(handleCreateUser(newUser));
    if (user.error) {
      setPassword(null);
      setPasswordRepeat(null);
      return;
    }

    // check if products were updated
    if (hangerworks || bimpro) {
      const hangerworksProduct = products.find((p) => p.name === "Hangerworks");
      const bimproProduct = products.find((p) => p.name === "BIMPro");

      const productIds = [];
      if (hangerworks && hangerworksProduct)
        productIds.push(hangerworksProduct?.id);
      if (bimpro && bimproProduct) productIds.push(bimproProduct?.id);

      dispatch(handleUpdateUserProducts(user[0]?.id, productIds, "assign"));
    }

    // after user is created, assign all active stages to user
    const stageIdString = workStages?.map((s) => s.id).join(",");
    await dispatch(
      handleUpdateUserStageAssignments(user[0]?.id, stageIdString, "assign")
    );
    dispatch(handleFetchUsers(true));
    handleClose();
  };

  const handleProductToggle = (event) => {
    const { name } = event.target;
    name === "Hangerworks"
      ? toggleHangerworks(!hangerworks)
      : toggleBimpro(!bimpro);
  };

  const formattedTiers = useMemo(() => {
    if (!tiers?.length) return [];
    return tiers.map((t) => ({ value: t.id, display: t.name }));
  }, [tiers]);

  const formattedTitles = useMemo(() => {
    if (!userTitles?.length) return [];
    const f_titles = userTitles.map((t) => ({ value: t.id, display: t.title }));
    // unable to clear in API
    // f_titles.unshift({ value: "", display: "" });
    return f_titles;
  }, [userTitles]);

  const formattedRoles = useMemo(() => {
    if (!roles?.length) return [];
    return roles.map((r) => ({ value: r.id, display: r.name }));
  }, []);

  const validPasswordMatch = useMemo(() => {
    let result;
    if (!password || !password.length) {
      result = false;
    } else if (password.trim() !== passwordRepeat.trim()) {
      setError("Passwords do not match");
      result = false;
    } else if (!validatePassword(password)) {
      setError("Password does not meet requirements");
      result = false;
    } else {
      setError(null);
      result = true;
    }

    return result;
  }, [password, passwordRepeat]);

  const validPasswordState = useMemo(() => {
    if (defaultPassword) return true;
    else if (validPasswordMatch) return true;
    else return false;
  }, [validPasswordMatch, defaultPassword]);

  const disableCreate = useMemo(() => {
    if (
      !inputs?.username ||
      !inputs?.firstName ||
      !inputs?.lastName ||
      !tierId ||
      !roleId
    )
      return true;
    else if (!validPasswordState) return true;
    else return false;
  }, [inputs, tierId, roleId, validPasswordState]);

  return (
    <Modal open={open} handleClose={handleClose}>
      <div className="create-user-modal">
        <h2>Create New User</h2>
        <div className="form-wrapper">
          <span className="input-wrapper">
            <label>
              Username: <span className="required">*</span>
            </label>
            <span className="username-input">
              <Input
                type="text"
                value={inputs?.username?.split("@")[0]}
                onChange={handleChange}
                name="username"
              />
              <p>{`@${domain}`}</p>
            </span>
          </span>
          <span className="input-wrapper">
            <label>
              First Name: <span className="required">*</span>
            </label>
            <Input
              value={inputs?.firstName || ""}
              onChange={handleChange}
              type="text"
              name="firstName"
            />
          </span>
          <span className="input-wrapper">
            <label>
              Last Name: <span className="required">*</span>
            </label>
            <Input
              value={inputs?.lastName || ""}
              onChange={handleChange}
              type="text"
              name="lastName"
            />
          </span>
          <span className="input-wrapper">
            <label>Employee Number:</label>
            <Input
              value={inputs?.employeeNumber || ""}
              onChange={handleChange}
              type="text"
              name="employeeNumber"
            />
          </span>
          <span className="input-wrapper">
            <label>Welder Number:</label>
            <Input
              value={inputs?.welderId || ""}
              onChange={handleChange}
              type="text"
              name="welderId"
            />
          </span>
          <span className="input-wrapper">
            <label>Email:</label>
            <Input
              value={inputs?.email || ""}
              onChange={handleChange}
              type="email"
              name="email"
            />
          </span>
          <span className="input-wrapper">
            <label>Phone:</label>
            <Input
              value={inputs?.phone || ""}
              onChange={handleChange}
              type="text"
              name="phone"
            />
          </span>
          <span className="input-wrapper">
            <label>
              Tier: <span className="required">*</span>
            </label>
            <Select
              value={tierId || ""}
              options={formattedTiers}
              onInput={(e) => setTierId(e.target.value)}
              placeholder="Tier"
              name="tier_id"
            />
          </span>
          <span className="input-wrapper">
            <label>
              Role: <span className="required">*</span>
            </label>
            <Select
              value={roleId || ""}
              options={formattedRoles}
              onInput={(e) => setRoleId(e.target.value)}
              placeholder="Role"
              name="role_id"
            />
          </span>
          <span className="input-wrapper">
            <label>Title:</label>
            <Select
              value={titleId || ""}
              options={formattedTitles}
              onInput={(e) => setTitleId(e.target.value)}
              placeholder="Title"
              name="title_id"
            />
          </span>
          <span className="input-wrapper">
            <label>Wage Rate:</label>
            <Input
              value={inputs?.laborRate || ""}
              onChange={handleChange}
              type="text"
              name="laborRate"
            />
          </span>
          <span className="input-wrapper group">
            <span className="checkbox">
              <label>Float Mode:</label>
              <Input
                checked={inputs?.floatMode ? true : false}
                onChange={handleChange}
                type="checkbox"
                name="floatMode"
              />
            </span>
            {isAdmin && (
              <>
                <span className="checkbox">
                  <label>Hangerworks:</label>
                  <Input
                    checked={hangerworks}
                    onChange={handleProductToggle}
                    type="checkbox"
                    name="Hangerworks"
                  />
                </span>
                <span className="checkbox">
                  <label>BimPro:</label>
                  <Input
                    checked={bimpro}
                    onChange={handleProductToggle}
                    type="checkbox"
                    name="BIMPro"
                  />
                </span>
              </>
            )}
          </span>
          <span className="input-wrapper">
            <label>
              Password: <span className="required">*</span>
            </label>
            <Input
              value={password}
              type="password"
              onInput={(e) => setPassword(e.target.value)}
              placeholder="Password"
              name="password"
            />
          </span>
          <span className="input-wrapper">
            <label>
              Re-enter Password: <span className="required">*</span>
            </label>
            <Input
              value={passwordRepeat}
              type="password"
              onInput={(e) => setPasswordRepeat(e.target.value)}
              placeholder="Password"
              name="password_repeat"
            />
          </span>
          <span className="input-wrapper">
            <span className="password-checkbox">
              <Input
                checked={defaultPassword}
                onChange={(e) => toggleDefaultPassword(!defaultPassword)}
                type="checkbox"
                name="password-checkbox"
              />
              <p>
                Use default password{" "}
                <span>{`(They will be forced to change the password upon initial login.)`}</span>
              </p>
            </span>
            <p className="info">If checked, password value will not be used</p>
          </span>
          <div className="password-reqs">
            {error && <p className="error">{error}</p>}
            <p>Password Requirements:</p>
            <ul>
              <li>Password must be between 5 and 32 characters</li>
              <li>
                Password must not contain spaces, commas, single or double
                quotation marks, colons, or slashes (,'":/')
              </li>
              <li>Password must not be the same as default password</li>
            </ul>
          </div>
          <Button
            disabled={disableCreate}
            className="create-user"
            onClick={handleCreate}
          >
            Create User
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default CreateUserModal;
