// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import Input from "msuite_storybook/dist/input/Input";
import Select from "msuite_storybook/dist/select/Select";

// REDUX IMPORTS
import {
  handleFetchUserPermissions,
  handleUpdateUserPermissions,
  handleResetUserPermissions,
  handleUpdateUserInfo,
} from "../usersActions";

// COMPONENT IMPORTS
import AgTable from "../../reusable/agTable/AgTable";
import ToggleButtonCellRenderer from "../../reusable/frameworkComponents/ToggleButtonCellRenderer";
import TableExportDropdown from "../../reusable/tableExportDropdown/TableExportDropdown";

// CONSTANTS IMPORTS
import { columnDefs } from "./permissionConstants";

// HELPER FUNCTION IMPORTS
import { permissionLock } from "../../../_utils";

// STYLE IMPORTS
import "./stylesPermissionsModal.scss";

const PermissionsModal = ({ currentUser, open, handleClose }) => {
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [searchInput, setSearchInput] = useState("");
  const [tierId, setTierId] = useState(null);

  const { permissions, tiers } = useSelector((state) => state.usersData);

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(handleFetchUserPermissions(currentUser.id));
    setTierId(currentUser.tier_id);
  }, [dispatch, currentUser.id, currentUser.tier_id]);

  const permissionLockedColumns = useMemo(() => {
    if (!permissions?.length) return [];

    const handlePermissionToggle = (permissionId, newValue) => {
      dispatch(
        handleUpdateUserPermissions(
          currentUser.id,
          permissionId + "",
          newValue ? "assign" : "unassign",
          true
        )
      );
    };

    const columns = columnDefs(handlePermissionToggle);
    return permissionLock(columns);
  }, [permissions, currentUser.id, dispatch]);

  // useEffect to set rowData and columnData
  useEffect(() => {
    if (
      !gridOptionsApi ||
      !permissions?.length ||
      !permissionLockedColumns?.length
    )
      return;

    gridOptionsApi.setColumnDefs(permissionLockedColumns);
    gridOptionsApi.setRowData(permissions);
  }, [gridOptionsApi, permissionLockedColumns, permissions]);

  useEffect(() => {
    if (!gridOptionsApi) return;
    gridOptionsApi.setQuickFilter(searchInput);
    gridOptionsApi.redrawRows();
  }, [searchInput, gridOptionsApi]);

  const handleAllowAll = () => {
    const permissionIdString = permissions
      .map((p) => p.permission_id)
      .join(",");
    dispatch(
      handleUpdateUserPermissions(currentUser.id, permissionIdString, "assign")
    );
  };

  const handleUnallowAll = () => {
    const permissionIdString = permissions
      .map((p) => p.permission_id)
      .join(",");
    dispatch(
      handleUpdateUserPermissions(
        currentUser.id,
        permissionIdString,
        "unassign"
      )
    );
  };

  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
  };
  const onSortChanged = (params) => {
    params.api.redrawRows();
  };
  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };

  const gridOptions = {
    defaultColDef: {
      wrapText: true,
      cellClass: ["no-border", "custom-wrap"],
    },
    suppressRowClickSelection: true,
    frameworkComponents: {
      allowToggleCellRenderer: ToggleButtonCellRenderer,
    },
    rowClassRules,
    onSortChanged,
    onGridReady,
    sideBar: false,
    getRowNodeId: (data) => data.permission_id,
  };

  const exportParams = useMemo(() => {
    if (!permissionLockedColumns?.length) return;

    const exportedColumns = permissionLockedColumns
      .filter((col) => col.field && !col.hide)
      .map((col) => col.field);

    return {
      fileName: `permissions`,
      processCellCallback: (params) => params.value,
      columnKeys: exportedColumns,
    };
  }, [permissionLockedColumns]);

  const handleExcelExport = () => {
    if (gridOptionsApi) gridOptionsApi.exportDataAsExcel(exportParams);
  };
  const handleCsvExport = () => {
    if (gridOptionsApi) gridOptionsApi.exportDataAsCsv(exportParams);
  };

  const formattedTiers = useMemo(() => {
    if (!tiers?.length) return [];
    return tiers.map((t) => ({ value: t.id, display: t.name }));
  }, [tiers]);

  const handleTierSelection = async (event) => {
    const { value } = event.target;
    if (parseInt(value) === tierId) return;

    setTierId(event.target.id);
    const res = dispatch(
      handleUpdateUserInfo(currentUser.id, { tierId: value }, true)
    );

    if (!res.error) dispatch(handleFetchUserPermissions(currentUser.id));
  };

  const handleResetPermissionsClick = () => {
    dispatch(handleResetUserPermissions(currentUser.id));
  };

  return (
    <Modal open={open} handleClose={handleClose}>
      <div className="permissions-modal">
        <h2>{`${currentUser.first_name} ${currentUser.last_name}'s Permissions`}</h2>
        <div className="content">
          <span className="all-buttons">
            <span className="tier-dropdown-wrapper">
              <p>Assigned to Tier: </p>
              <Select
                value={tierId}
                options={formattedTiers}
                onInput={handleTierSelection}
                placeholder="Tier"
                name="tier_id"
              />
            </span>
            <Button onClick={handleAllowAll}>Allow All</Button>
            <Button onClick={handleUnallowAll}>Unallow All</Button>
            <Button className="reset-btn" onClick={handleResetPermissionsClick}>
              Reset Permissions
            </Button>
          </span>
          <div className="top-row">
            <TableExportDropdown
              handleExcel={handleExcelExport}
              handleCSV={handleCsvExport}
            />
            <Input
              type="text"
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              placeholder="Search"
            />
          </div>
          {permissions?.length && <AgTable gridOptions={gridOptions} />}
        </div>
      </div>
    </Modal>
  );
};

export default PermissionsModal;
