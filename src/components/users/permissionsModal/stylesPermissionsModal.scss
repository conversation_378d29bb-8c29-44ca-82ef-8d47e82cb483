@import "../../styles/colors.scss";

div.permissions-modal {
  height: 85vh;
  width: 800px;
  background-color: white;
  overflow-y: auto;

  @media (max-height: 800px) {
    height: calc(100vh - 120px);
  }

  & h2 {
    margin: 0;
    font-size: 1rem;
    background-color: $fabProBlue;
    padding: 8px;
    color: white;
  }

  & div.content {
    height: 91%;
    padding: 10px 20px 0;

    @media (max-height: 800px) {
      height: 85%;
    }

    & input,
    select {
      height: 28px;
      font-size: 0.8rem;
      box-sizing: border-box;
    }

    & div.ag-theme-balham-dark.custom-ag-styles {
      min-height: 250px;
      height: inherit;
      margin: 0;
      width: 100%;
    }

    & div.top-row {
      display: flex;
      width: 100%;
      justify-content: space-between;
      height: 34px;
    }
  }

  & span.all-buttons {
    width: calc(100%);
    display: flex;
    margin-bottom: 8px;
    justify-content: flex-start;
    gap: 20px;
    height: 30px;
    align-items: center;

    & button {
      height: 26px;
      font-size: 0.8rem;
      background-color: $fabProBlue;
      color: white;
      padding: 2px 8px;
      width: auto;
      box-shadow: none;

      &:hover {
        background-color: darken($fabProBlue, 10%);
      }
    }

    & button.reset-btn {
      margin-left: auto;
    }
  }

  & div.export-dropdown-wrapper {
    margin-right: 3px;

    & div.export-button {
      & button {
        color: #333;
        border: 1px solid #ccc;
        border-radius: 5px;
        width: 80px;

        &:focus {
          background-color: inherit;
          border: 1px solid #ccc;
          outline: inherit;
        }
      }
    }
  }

  & span.tier-dropdown-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;

    & p {
      margin: 0;
      font-weight: bold;
      font-size: 0.9rem;
    }

    & select {
      margin-top: 9px;
    }
  }
}
