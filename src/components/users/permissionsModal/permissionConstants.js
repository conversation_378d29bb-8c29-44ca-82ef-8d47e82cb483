export const columnDefs = (handleToggle) => {
  return [
    {
      headerName: "Function",
      field: "permission_display_name",
      getQuickFilterText: (params) => params.data.permission_display_name,
      minWidth: 100,
      width: 120,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      autoHeight: true,
      colId: "permission_display_name",
    },
    {
      headerName: "Category",
      field: "permission_category",
      getQuickFilterText: (params) => params.data.permission_category,
      minWidth: 100,
      width: 120,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      autoHeight: true,
      colId: "permission_category",
    },
    {
      headerName: "Allow/Unallow",
      valueGetter: (params) => (params.data.is_assigned ? true : false),
      cellRenderer: "allowToggleCellRenderer",
      cellRendererParams: (params) => {
        return {
          setValue: params.setValue,
          value: params.value ? true : false,
          labels: ["Allowed", "Unallowed"],
          handleToggle,
          property: "permission_id",
        };
      },
      comparator: (a, b) => {
        if (a && b) return 0;
        return a ? 1 : -1;
      },
      minWidth: 80,
      width: 80,
      autoHeight: true,
      suppressMenu: true,
      suppressMovable: true,
      colId: "toggle",
      pinned: "right",
    },
  ];
};
