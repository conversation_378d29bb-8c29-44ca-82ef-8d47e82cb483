@import "../../styles/colors.scss";

div.detailed-user-wrapper {
  max-height: 600px;
  width: 1200px;
  background-color: white;
  overflow-y: auto;

  @media (max-height: 700px) {
    height: calc(100vh - 130px);
    overflow-y: scroll;
  }

  & h2 {
    background-color: $fabProBlue;
    padding: 10px;
    color: white;
    font-size: 1rem;
    font-weight: normal;
    margin: 0;
  }

  & div.form-wrapper {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 40px;
    padding: 25px;
  }

  & span.input-wrapper {
    display: flex;
    flex-direction: column;
    width: calc(30%);
    gap: 8px;

    & label {
      font-size: 0.8rem;
    }

    & span.required {
      color: $red;
      border: none;
      font-size: 0.9rem;
    }

    & input,
    select {
      font-size: 0.9rem;
      height: 34px;
    }

    & input[type="checkbox"] {
      height: 20px;
    }

    & span.username-input {
      display: flex;
      align-items: center;

      & input {
        width: 250px;
      }

      & p {
        margin: 0 0 0 5px;
        color: $fabProBlue;
        font-size: 0.9rem;
        font-weight: bold;
        transform: translateY(-5px);
      }
    }
  }

  & span.group {
    flex-direction: row;
    justify-content: space-between;
  }

  & span.checkbox {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100px;

    & input {
      width: 20px;
      margin-top: 12px;
      justify-self: center;
    }
  }

  & div.button-row {
    margin-top: 20px;
    gap: 20px;

    & button {
      width: calc(30%);
    }

    & .delete-btn {
      background-color: $red;
    }
  }

  & div.left {
    justify-content: flex-start;
    margin-left: 40px;
  }
}
