// NPM PACKAGE IMPORTS
import React, {
  useEffect,
  useState,
  useMemo,
  useCallback,
  useRef,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import Input from "msuite_storybook/dist/input/Input";
import Select from "msuite_storybook/dist/select/Select";

// REDUX IMPORTS
import {
  handleUpdateUserInfo,
  handleResetPassword,
  handleDeleteUsers,
  handleFetchUsers,
  handleFetchUserProducts,
  handleUpdateUserProducts,
} from "../usersActions";

// COMPONENT IMPORTS
import ConfirmationModal from "../../reusable/confirmationModal/ConfirmationModal";

// CONSTANT IMPORTS
import { roles } from "../usersConstants";

// HELPER FUNCTION IMPORTS
import usePrevious from "../../../hooks/usePrevious";

// STYLES IMPORTS
import "./stylesDetailedUserModal.scss";

// TODO - hangerworks/bimpro toggles

const DetailedUserModal = ({ open, handleClose, currentUser }) => {
  const [showDeleteConfirmation, toggleDeleteConfirmation] = useState(false);
  const [inputs, setInputs] = useState({});
  const [tierId, setTierId] = useState(null);
  const [roleId, setRoleId] = useState(null);
  const [titleId, setTitleId] = useState(null);
  const [hangerworks, toggleHangerworks] = useState(false);
  const [bimpro, toggleBimpro] = useState(false);
  const [isAdmin, toggleAdmin] = useState(false);
  const [domain, setDomain] = useState(null);
  const [productsLoaded, setProductsLoaded] = useState(false);

  const { tiers, userTitles, userProducts } = useSelector(
    (state) => state.usersData
  );
  const { userInfo, products } = useSelector((state) => state.profileData);

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(handleFetchUserProducts(currentUser.id)).then(() =>
      setProductsLoaded(true)
    );

    const currentRole = roles.find((r) => r.id === currentUser.role_id);
    setRoleId(currentRole?.id);

    setInputs({
      username: currentUser.username?.split("@")[0] || "",
      firstName: currentUser.first_name || "",
      lastName: currentUser.last_name || "",
      employeeNumber: currentUser.employee_number || "",
      welderId: currentUser.welder_id || "",
      email: currentUser.email || "",
      phone: currentUser.phone_number || "",
      laborRate: currentUser.labor_rate || "",
      floatMode: currentUser.float_mode,
    });

    setDomain(currentUser?.username?.split("@")[1]);

    // check if user is admin
    if (currentUser.id === 1) toggleAdmin(true);
  }, [currentUser]);

  // if admin user, display the products checkboxes
  useEffect(() => {
    if (!userInfo) return;
    toggleAdmin(userInfo.id === 1 ? true : false);
  }, [userInfo]);

  const populatedProductsRef = useRef(null);
  // populate assigned products
  useEffect(() => {
    if (
      !userProducts?.length ||
      !productsLoaded ||
      populatedProductsRef.current
    )
      return;
    const hangerworks = userProducts.find((up) => up.name === "Hangerworks");
    const bimpro = userProducts.find((up) => up.name === "BIMPro");

    hangerworks && toggleHangerworks(true);
    bimpro && toggleBimpro(true);
    populatedProductsRef.current = true;
  }, [userProducts, products, productsLoaded]);

  // populate tier select
  useEffect(() => {
    if (!tiers?.length) return;
    const currentTier = tiers.find((t) => t.id === currentUser.tier_id) || null;
    if (!currentTier) return;
    setTierId(currentTier?.id);
  }, [tiers, currentUser.tier_id]);

  // populate user titles select
  useEffect(() => {
    if (!userTitles?.length) return;
    const currentTitle = userTitles.find((t) => t.id === currentUser.group_id);
    if (!currentTitle) return;
    setTitleId(currentTitle?.id);
  }, [userTitles, currentUser.group_id]);

  const handleChange = (e) => {
    let { name, value, type, checked } = e.target;
    let valueToUse = value;
    if (name === "phone")
      valueToUse = value.replace(/[^0-9]/gi, "").slice(0, 10);
    else if (type === "checkbox") valueToUse = checked;
    else if (name === "laborRate") valueToUse = value.replace(/[^0-9\.]/gi, "");
    setInputs({ ...inputs, [name]: valueToUse });
  };

  const onRoleBlur = useCallback(() => {
    const updatedRole = roles.find((t) => t.id === parseInt(roleId));
    if (!updatedRole || updatedRole.id === roleId) return;
    dispatch(
      handleUpdateUserInfo(currentUser.id, { roleId: updatedRole.id }, true)
    );
  }, [roleId, dispatch, currentUser.id]);

  const onTierBlur = useCallback(() => {
    const updatedTier = tiers.find((t) => t.id === parseInt(tierId));
    if (!updatedTier || updatedTier.id === tierId) return;
    dispatch(
      handleUpdateUserInfo(currentUser.id, { tierId: updatedTier.id }, true)
    );
  }, [tierId, currentUser.id, dispatch, tiers]);

  const onTitleBlur = useCallback(() => {
    const updatedTitle = userTitles.find((t) => t.id === parseInt(titleId));
    if (!updatedTitle || updatedTitle.id === titleId) return;
    dispatch(
      handleUpdateUserInfo(currentUser.id, { titleId: updatedTitle.id }, true)
    );
  }, [titleId, userTitles, currentUser.id, dispatch]);

  const prevInputs = usePrevious(inputs);

  const onUserInfoBlur = useCallback(() => {
    if (JSON.stringify(inputs) === JSON.stringify(prevInputs)) return;

    // add domain back to username
    const username =
      inputs.username?.replace(/ /g, "") ||
      currentUser?.username?.split("@")[0];
    inputs.username = `${username}@${domain}`;

    const inputsToTrim = ["firstName", "lastName"];
    const inputsToClean = [
      "email",
      "phone",
      "welderId",
      "employeeNumber",
      "laborRate",
    ];

    for (let input of inputsToTrim) {
      inputs[input] = inputs[input]?.trim();
    }

    for (let input of inputsToClean) {
      inputs[input] = inputs[input]?.toString().replace(/ /g, "");
    }

    inputs.firstName = inputs.firstName || currentUser?.first_name;
    inputs.lastName = inputs.lastName || currentUser?.last_name;
    inputs.laborRate = inputs.laborRate
      ? parseFloat(inputs.laborRate).toFixed(2)
      : inputs.laborRate;

    const newInputs = { ...inputs, username };
    setInputs(newInputs);

    if (
      JSON.stringify(newInputs) ===
      JSON.stringify({
        ...prevInputs,
        username: prevInputs.username?.replace(/ /g, "").split("@")[0],
      })
    )
      return;
    dispatch(handleUpdateUserInfo(currentUser?.id, inputs, true));
  }, [currentUser, inputs, prevInputs]);

  const prevFloatMode = usePrevious(inputs?.floatMode);
  useEffect(() => {
    if (prevFloatMode === undefined || prevFloatMode === inputs?.floatMode)
      return;

    dispatch(
      handleUpdateUserInfo(
        currentUser.id,
        { floatMode: inputs.floatMode ? 1 : 0 },
        true
      )
    );
  }, [inputs.floatMode, prevFloatMode, dispatch, currentUser.id]);

  const handleProductToggle = (event) => {
    const { name, checked } = event.target;
    const product = products?.find((p) => p.name === name) || null;
    if (!product) return;

    name === "Hangerworks"
      ? toggleHangerworks(!hangerworks)
      : toggleBimpro(!bimpro);

    const action = checked ? "assign" : "unassign";
    dispatch(handleUpdateUserProducts(currentUser.id, [product.id], action));
  };

  const handleDeleteConfirmation = async () => {
    const idString = currentUser.id + "";
    const deleteRes = await dispatch(handleDeleteUsers(idString));
    if (deleteRes.error) return;

    // refresh users table
    dispatch(handleFetchUsers(true));
    handleClose();
  };

  const handleResetPasswordConfirmation = () => {
    dispatch(handleResetPassword(currentUser.id));
  };

  const formattedTiers = useMemo(() => {
    if (!tiers?.length) return [];
    return tiers.map((t) => ({ value: t.id, display: t.name }));
  }, [tiers]);

  const formattedTitles = useMemo(() => {
    if (!userTitles?.length) return [];
    const f_titles = userTitles.map((t) => ({ value: t.id, display: t.title }));
    // unable to clear in API
    // f_titles.unshift({ value: "", display: "" });
    return f_titles;
  }, [userTitles]);

  const formattedRoles = useMemo(() => {
    if (!roles?.length) return [];
    return roles.map((r) => ({ value: r.id, display: r.name }));
  }, []);

  return (
    <Modal open={open} handleClose={handleClose}>
      <div className="detailed-user-wrapper">
        <h2>{`Detailed Info for ${inputs?.firstName} ${inputs?.lastName}`}</h2>
        <div className="form-wrapper">
          <span className="input-wrapper">
            <label>
              Username: <span className="required">*</span>
            </label>
            <span className="username-input">
              <Input
                type="text"
                value={inputs?.username?.split("@")[0]}
                onChange={handleChange}
                onBlur={onUserInfoBlur}
                name="username"
              />
              <p>{domain}</p>
            </span>
          </span>
          <span className="input-wrapper">
            <label>
              First Name: <span className="required">*</span>
            </label>
            <Input
              value={inputs?.firstName || ""}
              onChange={handleChange}
              type="text"
              onBlur={onUserInfoBlur}
              name="firstName"
            />
          </span>
          <span className="input-wrapper">
            <label>
              Last Name: <span className="required">*</span>
            </label>
            <Input
              value={inputs?.lastName || ""}
              onChange={handleChange}
              type="text"
              onBlur={onUserInfoBlur}
              name="lastName"
            />
          </span>
          <span className="input-wrapper">
            <label>Employee Number:</label>
            <Input
              value={inputs?.employeeNumber || ""}
              onChange={handleChange}
              type="text"
              onBlur={onUserInfoBlur}
              name="employeeNumber"
            />
          </span>
          <span className="input-wrapper">
            <label>Welder Number:</label>
            <Input
              value={inputs?.welderId || ""}
              onChange={handleChange}
              type="text"
              onBlur={onUserInfoBlur}
              name="welderId"
            />
          </span>
          <span className="input-wrapper">
            <label>Email:</label>
            <Input
              value={inputs?.email || ""}
              onChange={handleChange}
              type="email"
              onBlur={onUserInfoBlur}
              name="email"
            />
          </span>
          <span className="input-wrapper">
            <label>Phone:</label>
            <Input
              value={inputs?.phone || ""}
              onChange={handleChange}
              type="text"
              onBlur={onUserInfoBlur}
              name="phone"
            />
          </span>
          <span className="input-wrapper">
            <label>
              Tier: <span className="required">*</span>
            </label>
            <Select
              value={tierId || ""}
              options={formattedTiers}
              onInput={(e) => setTierId(e.target.value)}
              placeholder="Tier"
              onBlur={onTierBlur}
              name="tier_id"
            />
          </span>
          <span className="input-wrapper">
            <label>
              Role: <span className="required">*</span>
            </label>
            <Select
              value={roleId || ""}
              options={formattedRoles}
              onInput={(e) => setRoleId(e.target.value)}
              placeholder="Role"
              onBlur={onRoleBlur}
              name="role_id"
            />
          </span>
          <span className="input-wrapper">
            <label>Title:</label>
            <Select
              value={titleId || ""}
              options={formattedTitles}
              onInput={(e) => setTitleId(e.target.value)}
              placeholder="Title"
              onBlur={onTitleBlur}
              name="title_id"
            />
          </span>
          <span className="input-wrapper">
            <label>Wage Rate:</label>
            <Input
              value={inputs?.laborRate || ""}
              onChange={handleChange}
              type="text"
              onBlur={onUserInfoBlur}
              name="laborRate"
            />
          </span>
          <span className="input-wrapper group">
            <span className="checkbox">
              <label>Float Mode:</label>
              <Input
                checked={inputs?.floatMode ? true : false}
                onChange={handleChange}
                type="checkbox"
                name="floatMode"
              />
            </span>
            {isAdmin && products?.find((p) => p.name === "Hangerworks") && (
              <span className="checkbox">
                <label>Hangerworks:</label>
                <Input
                  checked={hangerworks ? true : false}
                  onChange={handleProductToggle}
                  type="checkbox"
                  name="Hangerworks"
                />
              </span>
            )}
            {isAdmin && products?.find((p) => p.name === "BIMPro") && (
              <span className="checkbox">
                <label>BimPro:</label>
                <Input
                  checked={bimpro ? true : false}
                  onChange={handleProductToggle}
                  type="checkbox"
                  name="BIMPro"
                />
              </span>
            )}
          </span>
        </div>
        <div className="button-row left">
          <Button onClick={handleResetPasswordConfirmation}>
            Reset Password
          </Button>
          <Button
            className="delete-btn"
            onClick={() => toggleDeleteConfirmation(true)}
          >
            Delete User
          </Button>
        </div>
      </div>
      {showDeleteConfirmation && (
        <ConfirmationModal
          showModal={showDeleteConfirmation}
          toggleModal={toggleDeleteConfirmation}
          handleClick={handleDeleteConfirmation}
          action="DELETE"
          message="Are you sure you want to delete user? Deleted users can only be restored by FabPro admins, please be sure you want to delete this user instead of archiving them."
          submitText="Delete"
        />
      )}
    </Modal>
  );
};

export default DetailedUserModal;
