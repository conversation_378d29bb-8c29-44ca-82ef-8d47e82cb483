const initialState = {
  users: null,
  userTitles: null,
  tiers: null,
  isLoading: false,
  error: null,
  stageAssignments: null,
  permissions: null,
  userProducts: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_USERS_STARTED":
      return { ...state, isLoading: true, users: null };
    case "RECEIVE_USERS_SUCCEEDED":
      return { ...state, isLoading: false, users: payload, error: null };
    case "RECEIVE_USERS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_USER_TITLES_STARTED":
      return { ...state, isLoading: true, userTitles: null };
    case "RECEIVE_USER_TITLES_SUCCEEDED":
      return { ...state, isLoading: false, userTitles: payload, error: null };
    case "RECEIVE_USER_TITLES_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_TIERS_STARTED":
      return { ...state, isLoading: true, tiers: null };
    case "RECEIVE_TIERS_SUCCEEDED":
      return { ...state, isLoading: false, tiers: payload, error: null };
    case "RECEIVE_TIERS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_RESET_PASSWORD_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "RECEIVE_RESET_PASSWORD_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_USER_STAGE_ASSIGNMENTS_STARTED":
      return { ...state, isLoading: true, stageAssignments: null };
    case "RECEIVE_USER_STAGE_ASSIGNMENTS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        stageAssignments: payload,
        error: null,
      };
    case "RECEIVE_USER_STAGE_ASSIGNMENTS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_PERMISSIONS_STARTED":
      return { ...state, isLoading: true, users: null };
    case "RECEIVE_PERMISSIONS_SUCCEEDED":
      return { ...state, isLoading: false, permissions: payload, error: null };
    case "RECEIVE_PERMISSIONS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "DELETE_USERS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "DELETE_USERS_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "DELETE_USERS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "CREATE_USER_STARTED":
      return { ...state, isLoading: true, error: null };
    case "CREATE_USER_SUCCEEDED":
      return { ...state, isLoading: false, error: null };
    case "CREATE_USER_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_USER_PRODUCTS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "RECEIVE_USER_PRODUCTS_SUCCEEDED":
      return { ...state, isLoading: false, userProducts: payload };
    case "RECEIVE_USER_PRODUCTS_FAILED":
      return { ...state, isLoading: false, error: payload, userProducts: null };
    default:
      return state;
  }
}
