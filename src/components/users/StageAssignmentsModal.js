// NPM PACKAGE IMPORTS
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "msuite_storybook/dist/modal/Modal";
import Button from "msuite_storybook/dist/button/Button";
import Input from "msuite_storybook/dist/input/Input";

// REDUX IMPORTS
import { handleUpdateUserStageAssignments } from "./usersActions";

const Checkbox = ({ checked, onChange, stageId }) => {
  return (
    <Input
      name={stageId}
      checked={checked}
      onChange={onChange}
      type="checkbox"
    />
  );
};

const StageAssignmentsModal = ({
  open,
  handleClose,
  currentUser,
  refreshUsers,
}) => {
  const [searchInput, setSearchInput] = useState("");
  const [refresh, toggleRefresh] = useState(false);
  const [checkboxState, setCheckboxState] = useState({});
  const [displayedStages, setDisplayedStages] = useState([]);
  const [isAllSelected, toggleAllSelected] = useState(false);

  const { stageAssignments, isLoading } = useSelector(
    (state) => state.usersData
  );
  const { workStages } = useSelector((state) => state.flowsData);

  const dispatch = useDispatch();

  useEffect(() => {
    // if a selection is updated refresh users on close to update stage assigned count
    return () => refresh && refreshUsers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refresh]);

  useEffect(() => {
    if (!workStages?.length) return;
    setDisplayedStages(workStages);
  }, [workStages]);

  useEffect(() => {
    if (!workStages?.length || isLoading) return;

    const assignedStageIds = stageAssignments?.map((s) => s.id) || [];
    const combinedState = workStages.reduce(
      (obj, item) => ({
        ...obj,
        [item.id]: assignedStageIds.includes(item.id),
      }),
      {}
    );

    const allChecked = Object.values(combinedState).every(
      (value) => value === true
    );

    toggleAllSelected(allChecked ? true : false);

    setCheckboxState(combinedState);
  }, [workStages, stageAssignments, isLoading]);

  const handleChange = (event) => {
    const { name, checked } = event.target;
    setCheckboxState({
      ...checkboxState,
      [name]: checked,
    });

    toggleRefresh(true);
    dispatch(
      handleUpdateUserStageAssignments(
        currentUser.id,
        name + "",
        checked ? "assign" : "unassign"
      )
    );
  };

  const handleAll = () => {
    toggleRefresh(true);
    const allChecked = Object.values(checkboxState).every(
      (value) => value === true
    );
    const action = allChecked ? "unassign" : "assign";
    let idsToSend;
    idsToSend = Object.keys(checkboxState).filter((key) => {
      if (action === "assign") return checkboxState[key] === false;
      else return (checkboxState[key] = true);
    });

    // Future TODO - Unassigning all stages will cause an error to be thrown
    dispatch(
      handleUpdateUserStageAssignments(
        currentUser.id,
        idsToSend.join(","),
        action
      )
    );
  };

  const handleSearchInput = (value) => {
    setSearchInput(value);
    if (value) {
      const f_stages = workStages.filter((ws) =>
        ws.name.toLowerCase().includes(value.toLowerCase().trim())
      );
      setDisplayedStages(f_stages);
    } else setDisplayedStages(workStages);
  };

  return (
    <Modal open={open} handleClose={handleClose} title="Stage Assignments">
      <div className="stage-assignments-wrapper">
        <h2>{`${currentUser.first_name} ${currentUser.last_name}'s Stage Assignments`}</h2>
        <div className="content-wrapper">
          <span>
            <Input
              className="search-input"
              placeholder="Search stages"
              value={searchInput}
              onChange={(e) => handleSearchInput(e.target.value)}
            />
            <Button onClick={handleAll}>
              {isAllSelected ? "Unselect All" : "Select All"}
            </Button>
          </span>

          <div className="stages-list">
            {displayedStages?.map((stage) => (
              <label
                className={`stage-row ${
                  checkboxState[stage.id] ? "checked" : ""
                }`}
                key={stage.id}
              >
                {stage.name}
                <Checkbox
                  checked={checkboxState[stage.id]}
                  onChange={handleChange}
                  stageId={stage.id}
                />
              </label>
            ))}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default StageAssignmentsModal;
