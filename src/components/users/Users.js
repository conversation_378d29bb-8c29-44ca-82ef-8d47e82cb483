// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useMemo, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import Button from "msuite_storybook/dist/button/Button";

// REDUX IMPORTS
import {
  handleFetchUsers,
  handleUpdateUserInfo,
  handleFetchUserTitles,
  handleFetchTiers,
  handleFetchUserStageAssignments,
  handleFetchUserPermissions,
} from "./usersActions";
import { handleFetchWorkStages } from "../flows/flowsActions";
import { handleFetchProducts } from "../profile/profileActions";

// COMPONENT IMPORTS
import AgTable from "../reusable/agTable/AgTable";
import TableContainer from "../reusable/tableContainer/TableContainer";
import ToggleCellEditor from "../reusable/frameworkComponents/ToggleCellEditor";
import DropdownEditorRenderer from "../reusable/frameworkComponents/DropdownEditorRenderer";
import ActionCellRenderer from "../reusable/frameworkComponents/ActionCellRenderer";
import UsernameCellRenderer from "../reusable/frameworkComponents/UsernameCellRenderer";
import LinkCellRenderer from "../reusable/frameworkComponents/LinkCellRenderer";
import ToggleButtonCellRenderer from "../reusable/frameworkComponents/ToggleButtonCellRenderer";
import StageAssignmentsModal from "./StageAssignmentsModal";
import DetailedUserModal from "./detailedUserModal/DetailedUserModal";
import UsernameCellEditor from "../reusable/frameworkComponents/UsernameCellEditor";
import PermissionsModal from "./permissionsModal/PermissionsModal";

// CONSTANTS IMPORTS
import { usersColumnDefs, roles } from "./usersConstants";

// HELPER FUNCTION IMPORTS
import { naturalSort, permissionLock } from "../../_utils";

// STYLE IMPORTS
import "./stylesUsers.scss";
import CreateUserModal from "./createUserModal/CreateUserModal";

const Users = () => {
  const [gridOptionsApi, setGridOptionsApi] = useState(null);
  const [searchInput, setSearchInput] = useState("");
  const [selectedRow, setSelectedRow] = useState(null);
  const [showPermissionModal, togglePermissionModal] = useState(false);
  const [showNewUserModal, toggleNewUserModal] = useState(false);
  const [showUserAssignmentsModal, toggleUserAssignmentsModal] = useState(
    false
  );
  const [showDetailedModal, toggleDetailedModal] = useState(false);
  const [displayedRows, setDisplayedRows] = useState(null);

  const { users, userTitles, tiers, isLoading } = useSelector(
    (state) => state.usersData
  );
  const { permissions, userSettings } = useSelector(
    (state) => state.profileData
  );

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(handleFetchUsers(true));
    dispatch(handleFetchTiers);
    dispatch(handleFetchUserTitles);
    dispatch(handleFetchWorkStages());
    dispatch(handleFetchProducts);
  }, []);

  useEffect(() => {
    if (!permissions.length) return;

    // redirect to homepage if user doesn't have permission to view settings or edit users
    if (!permissions.includes(62) || !permissions.includes(248)) {
      const homePage = userSettings?.homePage
        ? `${userSettings?.home_page}`
        : "jobs";
      const URL = `${process.env.REACT_APP_FABPRO}/${homePage}`;
      window.location.assign(URL);
    }
  }, [permissions, userSettings]);

  const tiersRef = useRef(null);
  const userTitlesRef = useRef(null);

  const permissionLockedColumns = useMemo(() => {
    if (isLoading || !tiers?.length || !userTitles?.length) return;

    tiersRef.current = tiers;
    userTitlesRef.current = userTitles;

    const handleStageAssignmentsClick = async (params) => {
      setSelectedRow(params.data);
      await dispatch(handleFetchUserStageAssignments(params.data.id));
      toggleUserAssignmentsModal(true);
    };

    const handleMoreInfoClick = (params) => {
      setSelectedRow(params.data);
      toggleDetailedModal(true);
    };

    const handlePermissionClick = async (params) => {
      setSelectedRow(params.data);
      await dispatch(handleFetchUserPermissions(params.data.id));
      togglePermissionModal(true);
    };

    const handleActiveToggle = (userId, value) => {
      dispatch(handleUpdateUserInfo(userId, { isActive: value ? 1 : 0 }, true));
    };

    const cols = usersColumnDefs(
      roles,
      userTitles,
      tiers,
      handleActiveToggle,
      handleMoreInfoClick,
      handlePermissionClick,
      handleStageAssignmentsClick,
      setSelectedRow
    );

    return permissionLock(cols);
  }, [userTitles, tiers, dispatch]);

  useEffect(() => {
    if (!users?.length || !tiers?.length || !userTitles?.length) return;
    const results = users
      .map((u) => {
        const title = userTitles.find((t) => t.id === u.group_id) || "";
        const tier = tiers.find((t) => t.id === u.tier_id) || "";
        return {
          ...u,
          title_display: title ? title.title : "",
          tier_display: tier ? tier.name : "",
        };
      })
      .sort((a, b) => naturalSort(a.first_name, b.first_name));

    setDisplayedRows(results);
  }, [users, tiers, userTitles]);

  useEffect(() => {
    if (!gridOptionsApi) return;
    gridOptionsApi.setQuickFilter(searchInput);
    gridOptionsApi.redrawRows();
  }, [searchInput, gridOptionsApi]);

  useEffect(() => {
    if (!displayedRows?.length || !gridOptionsApi || !permissionLockedColumns)
      return;

    // set columns and permissions at same time or row height will break
    gridOptionsApi.setColumnDefs(permissionLockedColumns);
    gridOptionsApi.setRowData(displayedRows);
  }, [displayedRows, gridOptionsApi, permissionLockedColumns]);

  const onCellValueChanged = (params) => {
    if (!params.newValue && !params.oldValue) return;
    const { colId } = params.colDef;

    let property;
    let value;
    switch (colId) {
      case "username":
        value = params.newValue;
        property = "username";
        break;
      case "first_name":
        value = params.newValue;
        property = "firstName";
        break;
      case "last_name":
        value = params.newValue;
        property = "lastName";
        break;
      case "role_name":
        value =
          (roles.find((role) => role.name === params.newValue) || {}).id ||
          null;
        property = "roleId";
        break;
      case "group_id":
        value =
          (
            userTitlesRef.current?.find(
              (title) => title.title === params.newValue
            ) || {}
          ).id || null;
        property = "titleId";
        break;
      case "tier_id":
        value =
          (
            tiersRef.current?.find((tier) => tier.name === params.newValue) ||
            {}
          ).id || null;
        property = "tierId";
        break;
      default:
        return;
    }

    const newData = { [property]: value };

    dispatch(handleUpdateUserInfo(params.data.id, newData, true));
  };
  const onRowEditingStarted = (params) => {
    setSelectedRow(params.data);
  };
  const onGridReady = (params) => {
    setGridOptionsApi(params.api);
  };
  const onSortChanged = (params) => {
    params.api.redrawRows();
  };
  const rowClassRules = {
    "--custom-grid-odd": (params) => params.node.childIndex % 2 === 1,
    "--custom-grid-even": (params) => params.node.childIndex % 2 === 0,
  };

  const exportParams = useMemo(() => {
    if (!permissionLockedColumns?.length) return;

    const exportedColumns = permissionLockedColumns
      .filter((col) => col.field && !col.hide)
      .map((col) => col.field);

    return {
      columnKeys: exportedColumns,
      fileName: "active-users",
      processCellCallback: (params) => {
        if (typeof params.value === "number" && params.column.colId !== "id") {
          return params.value.toFixed(2);
        } else return params.value;
      },
    };
  }, [permissionLockedColumns]);

  const handleExcelExport = () => {
    if (gridOptionsApi) gridOptionsApi.exportDataAsExcel(exportParams);
  };
  const handleCSVExport = () => {
    if (gridOptionsApi) gridOptionsApi.exportDataAsCsv(exportParams);
  };

  const gridOptions = {
    defaultColDef: {
      wrapText: true,
      cellClass: ["no-border", "custom-wrap"],
    },
    frameworkComponents: {
      emailCellRenderer: ToggleCellEditor,
      dropdownEditorRenderer: DropdownEditorRenderer,
      actionCellRenderer: ActionCellRenderer,
      usernameCellRenderer: UsernameCellRenderer,
      linkCellRenderer: LinkCellRenderer,
      usernameCellEditor: UsernameCellEditor,
      activeToggleCellRenderer: ToggleButtonCellRenderer,
    },
    suppressRowClickSelection: true,
    stopEditingWhenGridLosesFocus: true,
    editType: "fullRow",
    rowClassRules,
    onRowEditingStarted,
    onSortChanged,
    onGridReady,
    onCellValueChanged,
    getRowNodeId: (data) => data.id,
  };

  return (
    <div className="users-wrapper">
      <div className="action-row">
        <p>Users</p>
        <span className="action-buttons">
          <Button onClick={() => toggleNewUserModal(true)}>Add User</Button>
        </span>
      </div>
      <TableContainer
        searchInput={searchInput}
        setSearchInput={setSearchInput}
        selectedRows={[]}
        showExport={true}
        handleCSV={handleCSVExport}
        handleExcel={handleExcelExport}
      >
        {<AgTable gridOptions={gridOptions} />}
      </TableContainer>
      {showUserAssignmentsModal && (
        <StageAssignmentsModal
          open={showUserAssignmentsModal}
          handleClose={() => toggleUserAssignmentsModal(false)}
          currentUser={selectedRow}
          refreshUsers={() => dispatch(handleFetchUsers(true))}
        />
      )}
      {showPermissionModal && (
        <PermissionsModal
          currentUser={selectedRow}
          open={showPermissionModal}
          handleClose={() => togglePermissionModal(false)}
        />
      )}
      {showDetailedModal && (
        <DetailedUserModal
          open={showDetailedModal}
          handleClose={() => toggleDetailedModal(false)}
          currentUser={selectedRow}
        />
      )}
      {showNewUserModal && (
        <CreateUserModal
          open={showNewUserModal}
          handleClose={() => toggleNewUserModal(false)}
        />
      )}
    </div>
  );
};

export default Users;
