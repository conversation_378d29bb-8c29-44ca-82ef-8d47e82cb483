// HELPER FUNCTION IMPORTS
import { naturalSort } from "../../_utils";

export const usersColumnDefs = (
  roles,
  userTitles,
  tiers,
  handleActiveToggle,
  toggleDetailedModal,
  handlePermissionClick,
  handleStageAssignmentsClick,
  setSelectedRow
) => {
  return [
    {
      headerName: "First Name",
      field: "first_name",
      getQuickFilterText: (params) => params.data.first_name,
      minWidth: 100,
      width: 120,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      autoHeight: true,
      editable: true,
      colId: "first_name",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Last Name",
      field: "last_name",
      getQuickFilterText: (params) => params.data.last_name,
      minWidth: 100,
      width: 120,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      autoHeight: true,
      editable: true,
      colId: "last_name",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "User Name",
      field: "username",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue;

        return params.newValue;
      },
      cellRenderer: "usernameCellRenderer",
      cellEditor: "usernameCellEditor",
      getQuickFilterText: (params) => params.data.username,
      minWidth: 100,
      width: 120,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      autoHeight: true,
      editable: true,
      menuTabs: ["filterMenuTab"],
      colId: "username",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Role",
      field: "role_name",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue;

        if (typeof params.newValue === "number") {
          const role = roles.find((r) => r.id === params.newValue);
          return role?.name;
        }

        return params.newValue;
      },
      cellEditor: "dropdownEditorRenderer",
      cellEditorParams: (params) => {
        return {
          values: roles,
          value: params.data.role_id,
        };
      },
      getQuickFilterText: (params) => params.data.role_name,
      minWidth: 60,
      width: 80,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      autoHeight: true,
      editable: true,
      colId: "role_name",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Title",
      field: "title_display",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return null;

        if (typeof params.newValue === "number") {
          const title = userTitles.find((t) => t.id === params.newValue);
          return title?.title;
        }

        return params.newValue;
      },
      cellEditor: "dropdownEditorRenderer",
      cellEditorParams: (params) => {
        return {
          values: userTitles || [],
          value: params.data.group_id,
          propertyKey: "title",
        };
      },
      getQuickFilterText: (params) => {
        const titleObj = userTitles.find((t) => t.id === params.data.group_id);
        if (!titleObj) return "";
        return titleObj?.title;
      },
      minWidth: 80,
      width: 100,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      autoHeight: true,
      editable: true,
      colId: "group_id",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Tier",
      field: "tier_display",
      valueParser: (params) => {
        if (params.newValue === "" || params.newValue === undefined)
          return params.oldValue;

        if (typeof params.newValue === "number") {
          const tier = tiers.find((t) => t.id === params.newValue);
          return tier?.name;
        }

        return params.newValue;
      },
      cellEditor: "dropdownEditorRenderer",
      cellEditorParams: (params) => {
        return {
          values: tiers || [],
          value: params.data.tier_id,
          propertyKey: "name",
          suppressClearing: true,
        };
      },
      getQuickFilterText: (params) => {
        const tier = tiers.find((t) => t.id === params.data.tier_id);
        if (!tier) return "";
        return tier?.name;
      },
      minWidth: 80,
      width: 100,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["reset"],
      },
      menuTabs: ["filterMenuTab"],
      autoHeight: true,
      editable: true,
      colId: "tier_id",
      comparator: (valueA, valueB) =>
        valueA ? (valueB ? naturalSort(valueA, valueB) : -1) : 1,
    },
    {
      headerName: "Stage Assignments",
      field: "assigned_stage_count",
      cellRenderer: "linkCellRenderer",
      cellRendererParams: {
        handleClick: handleStageAssignmentsClick,
        setSelectedRow,
        text: "stages",
      },
      minWidth: 100,
      width: 120,
      colId: "stage_assignments",
    },
    {
      headerName: "Active",
      valueGetter: (params) => (params.data.is_active ? true : false),
      cellRenderer: "activeToggleCellRenderer",
      cellRendererParams: (params) => ({
        setValue: params.setValue,
        value: params.value,
        labels: ["Active", "Inactive"],
        handleToggle: handleActiveToggle,
        width: 100,
        property: "id",
      }),
      comparator: (a, b) => {
        if (a && b) return 0;
        return a ? 1 : -1;
      },
      minWith: 80,
      width: 80,
      autoHeight: true,
      lockVisible: true,
      suppressMovable: true,
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      colId: "active",
    },
    {
      headerName: "Permissions",
      cellRenderer: "actionCellRenderer",
      valueFormatter: (params) => ({
        actions: [
          {
            action: (e) => handlePermissionClick(params),
            actionName: "Permissions",
          },
        ],
      }),
      minWidth: 100,
      width: 100,
      autoHeight: true,
      lockVisible: true,
      suppressMovable: true,
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      colId: "permissions",
    },
    {
      headerName: "More Info",
      cellRenderer: "actionCellRenderer",
      valueFormatter: (params) => ({
        actions: [
          {
            action: (e) => toggleDetailedModal(params),
            actionName: "More Info",
          },
        ],
      }),
      minWidth: 60,
      width: 80,
      autoHeight: true,
      lockVisible: true,
      suppressMovable: true,
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      colId: "more-info",
    },
  ];
};

export const roles = [
  { id: 1, name: "Office" },
  { id: 2, name: "Field" },
  { id: 3, name: "Shop" },
  { id: 4, name: "Client" },
];
