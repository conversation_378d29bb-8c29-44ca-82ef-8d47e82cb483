import {
  fetchSelfTimers,
  startWorkTimer,
  startWorkTimerv2,
  stopWorkTimer,
  removeItemsFromTimer,
  fetchAllTimers,
} from "../../_services";
import store from "../../redux/store";
import { trackMixPanelEvent } from "../../utils/_mixPanelUtils";

const startStarted = (type) => ({ type: `START_${type}_STARTED` });
const startSucceeded = (type, items) => ({
  type: `START_${type}_SUCCEEDED`,
  payload: items,
});
const startFailed = (type, error) => ({
  type: `START_${type}_FAILED`,
  payload: error,
});
const stopStarted = (type) => ({ type: `STOP_${type}_STARTED` });
const stopSucceeded = (type, items) => ({
  type: `STOP_${type}_SUCCEEDED`,
  payload: items,
});
const stopFailed = (type, error) => ({
  type: `STOP_${type}_FAILED`,
  payload: error,
});
const removeStarted = (type) => ({ type: `REMOVE_${type}_STARTED` });
const removeSucceeded = (type, items) => ({
  type: `REMOVE_${type}_SUCCEEDED`,
  payload: items,
});
const removeFailed = (type, error) => ({
  type: `REMOVE_${type}_FAILED`,
  payload: error,
});
const receiveStarted = (type) => ({
  type: `RECEIVE_${type}_STARTED`,
});
const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});
const addStarted = (type) => ({
  type: `ADD_${type}_STARTED`,
});
const addSucceeded = (type, payload) => ({
  type: `ADD_${type}_SUCCEEDED`,
  payload,
});
const addFailed = (type, error) => ({
  type: `ADD_${type}_FAILED`,
  payload: error,
});

export const handleStartTimer = (
  itemIds,
  shiftId,
  stageId,
  timerId,
  itemType,
  isGeneric,
  costCodeId
) => (dispatch) => {
  const type = "TIMER";

  const mixPanelEventStart = Date.now();

  dispatch(startStarted(type));
  return startWorkTimer(
    itemIds,
    shiftId,
    stageId,
    timerId,
    itemType,
    isGeneric,
    costCodeId
  ).then((res) => {
    if (res.error) {
      dispatch(startFailed(type, res));
      return fetchSelfTimers(true).then((res) => {
        if (res.error) dispatch(receiveFailed("ACTIVE_TIMER", res));
        return dispatch(receiveSucceeded("ACTIVE_TIMER", res));
      });
    } else {
      const { username } = store.getState().profileData.userInfo;
      const { userId } = store.getState().profileData;

      window.ChurnZero.push([
        "trackEvent",
        `FP Timer Started`,
        `${itemType} ${isGeneric ? "Generic " : "Work"} Timer`,
        1,
        {
          Product: "FabPro",
          SubGroup: "Timers",
          Version: process.env.REACT_APP_ENVIRONMENT,
          UserName: username,
          UserId: userId,
        },
      ]);
      trackMixPanelEvent(
        "Timer Started",
        res?.timer?.length,
        "Timers",
        mixPanelEventStart,
        `${isGeneric ? "Generic " : "Work"} Timer`
      );

      return dispatch(startSucceeded(type, res));
    }
  });
};

export const handleStopTimer = (
  timerId,
  stopTypeId,
  callback,
  isGeneric = false
) => (dispatch) => {
  const type = "TIMER";

  const mixPanelEventStart = Date.now();

  let timerType = stopTypeId === 1 ? "Paused" : "Complete";
  dispatch(stopStarted(type));
  return stopWorkTimer(timerId, stopTypeId).then((res) => {
    if (res.error) return dispatch(stopFailed(type, res));
    const { username } = store.getState().profileData.userInfo;
    const { userId } = store.getState().profileData;

    window.ChurnZero.push([
      "trackEvent",
      "FP Timer Stopped",
      `${timerType} ${isGeneric ? "Generic" : "Work"} Timer`,
      1,
      {
        Product: "FabPro",
        SubGroup: "Timers",
        Version: process.env.REACT_APP_ENVIRONMENT,
        UserName: username,
        UserId: userId,
      },
    ]);
    trackMixPanelEvent(
      "Timer Stopped",
      res.length,
      "Timers",
      mixPanelEventStart,
      `${isGeneric ? "Generic " : "Work"} Timer`
    );

    if (callback && typeof callback === "function") callback();
    return dispatch(stopSucceeded(type, res));
  });
};

export const handleRemoveItemsFromTimer = (timerId, itemIds, quantity) => (
  dispatch
) => {
  const type = "ITEMS";
  dispatch(removeStarted(type));
  return removeItemsFromTimer(timerId, itemIds, quantity).then((res) => {
    if (res.error) {
      dispatch(removeFailed(type, res));
      return res;
    }
    if (res.result.length && res.result[0].state === "error") {
      dispatch(removeFailed("ITEMS", res));
      const type = "ACTIVE_TIMER";
      return fetchSelfTimers(true).then((res) => {
        if (res.error) dispatch(receiveFailed(type, res));
        return dispatch(receiveSucceeded(type, res));
      });
    } else {
      const { username } = store.getState().profileData.userInfo;
      const { userId } = store.getState().profileData;

      window.ChurnZero.push([
        "trackEvent",
        "FP Timer Removed",
        `Removed Work Item Timer`,
        1,
        {
          Product: "FabPro",
          SubGroup: "Timers",
          Version: process.env.REACT_APP_ENVIRONMENT,
          UserName: username,
          UserId: userId,
        },
      ]);
      dispatch(removeSucceeded(type, res));
      return res;
    }
  });
};

export const handleAddItemsToTimer = (itemIds, shiftId, stageId, timerId) => (
  dispatch
) => {
  const type = "ITEMS";

  dispatch(addStarted(type));
  return startWorkTimer(itemIds, shiftId, stageId, timerId).then((res) => {
    if (res.error) return dispatch(addFailed(type, res));
    const { username } = store.getState().profileData.userInfo;
    const { userId } = store.getState().profileData;

    window.ChurnZero.push([
      "trackEvent",
      "FP Timer Started",
      `${type} Work Timer`,
      1,
      {
        Product: "FabPro",
        SubGroup: "Timers",
        Version: process.env.REACT_APP_ENVIRONMENT,
        UserName: username,
        UserId: userId,
      },
    ]);
    return dispatch(addSucceeded(type, res));
  });
};

export const handleFetchActiveTimer = (dispatch) => {
  const type = "ACTIVE_TIMER";

  dispatch(receiveStarted(type));
  return fetchSelfTimers(true).then((res) => {
    if (res.error) return dispatch(receiveFailed(type, res));
    return dispatch(receiveSucceeded(type, res));
  });
};

/**
 * POST v2/self/work-timers
 *
 * Improvement - Returns error before creating a timer
 * if another user is already working on the same work-item
 * at the same stage.
 *
 * @param {*} itemIds send itemIds, comma separated, for the items to add to timer
 * @param {*} shiftId send shiftId for the active user shift
 * @param {*} stageId send stageId for the timer
 * @param {*} timerId if a timer exists, send timerId
 * @param {*} itemType job, package, drawing, or work-item
 * @param {*} isGeneric 0 or 1
 * @param {*} costCodeId cost code id
 * @returns successful timer or error message
 */
export const handleStartTimerv2 = (
  itemIds,
  shiftId,
  stageId,
  timerId,
  itemType,
  isGeneric,
  costCodeId
) => (dispatch) => {
  const type = "TIMER";

  const mixPanelEventStart = Date.now();

  dispatch(startStarted(type));
  return startWorkTimerv2(
    itemIds,
    shiftId,
    stageId,
    timerId,
    itemType,
    isGeneric,
    costCodeId
  ).then((res) => {
    if (res.error) {
      dispatch(startFailed(type, res));
      // return error message that should be shown
      if (res.error?.message?.includes("refresh")) {
        return res;
      }

      return fetchSelfTimers(true).then((res) => {
        if (res.error) dispatch(receiveFailed("ACTIVE_TIMER", res));
        return dispatch(receiveSucceeded("ACTIVE_TIMER", res));
      });
    } else {
      const { username } = store.getState().profileData.userInfo;
      const { userId } = store.getState().profileData;

      window.ChurnZero.push([
        "trackEvent",
        `FP Timer Started`,
        `${itemType} ${isGeneric ? "Generic " : "Work"} Timer`,
        1,
        {
          Product: "FabPro",
          SubGroup: "Timers",
          Version: process.env.REACT_APP_ENVIRONMENT,
          UserName: username,
          UserId: userId,
        },
      ]);
      trackMixPanelEvent(
        "Timer Started",
        res?.timer?.length,
        "Timers",
        mixPanelEventStart,
        `${isGeneric ? "Generic " : "Work"} Timer`
      );

      return dispatch(startSucceeded(type, res));
    }
  });
};

/**
 * POST v2/self/work-timers
 *
 * Improvement - Returns error that the UI can display
 * if another user is already working on the same work-item
 * at the same stage.
 *
 * @param {*} itemIds item ids to add to work timer
 * @param {*} shiftId active users shift id
 * @param {*} stageId stage id for items
 * @param {*} timerId active timer id
 * @returns active timer or error message
 */
export const handleAddItemsToTimerv2 = (itemIds, shiftId, stageId, timerId) => (
  dispatch
) => {
  const type = "ITEMS";

  dispatch(addStarted(type));
  return startWorkTimerv2(itemIds, shiftId, stageId, timerId).then((res) => {
    if (res.error) {
      dispatch(addFailed(type, res));
      // return error message that should be shown
      if (res.error?.message?.includes("refresh")) {
        return res;
      }
      return fetchSelfTimers(true).then((res) => {
        if (res.error) dispatch(receiveFailed("ACTIVE_TIMER", res));
        return dispatch(receiveSucceeded("ACTIVE_TIMER", res));
      });
    }
    const { username } = store.getState().profileData.userInfo;
    const { userId } = store.getState().profileData;

    window.ChurnZero.push([
      "trackEvent",
      "FP Timer Started",
      `${type} Work Timer`,
      1,
      {
        Product: "FabPro",
        SubGroup: "Timers",
        Version: process.env.REACT_APP_ENVIRONMENT,
        UserName: username,
        UserId: userId,
      },
    ]);
    return dispatch(addSucceeded(type, res));
  });
};

export const handleFetchAllTimers = (status, stageIds) => (dispatch) => {
  const type = "ALL_TIMERS";
  dispatch(receiveStarted(type));
  return fetchAllTimers(status, stageIds).then((res) => {
    if (res.error) return dispatch(receiveFailed(type, res));
    return dispatch(receiveSucceeded(type, res));
  });
};
