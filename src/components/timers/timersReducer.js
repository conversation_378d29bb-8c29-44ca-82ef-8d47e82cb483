const initialState = {
  isLoading: false,
  activeTimer: null,
  error: null,
  allTimers: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_ACTIVE_TIMER_STARTED":
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case "RECEIVE_ACTIVE_TIMER_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        activeTimer: payload,
      };
    case "RECEIVE_ACTIVE_TIMER_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        activeTimer: null,
      };
    case "RECEIVE_ALL_TIMERS_STARTED":
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case "RECEIVE_ALL_TIMERS_SUCCEEDED":
      return {
        ...state,
        isLoading: true,
        error: null,
        allTimers: payload,
      };
    case "RECEIVE_ALL_TIMERS_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
        allTimers: null,
      };
    case "START_TIMER_STARTED":
      return { ...state, isLoading: true, error: null };
    case "START_TIMER_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        activeTimer: payload.timer,
      };
    case "START_TIMER_FAILED":
      return {
        ...state,
        isLoading: false,
        error: payload,
      };
    case "STOP_TIMER_STARTED":
      return { ...state, isLoading: true, error: null, activeTimer: payload };
    case "STOP_TIMER_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        activeTimer: null,
      };
    case "STOP_TIMER_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "REMOVE_ITEMS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "REMOVE_ITEMS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        error: null,
        activeTimer: payload.timer,
      };
    case "REMOVE_ITEMS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "ADD_ITEMS_STARTED":
      return { ...state, isLoading: true, error: null };
    case "ADD_ITEMS_SUCCEEDED":
      return {
        ...state,
        isLoading: false,
        activeTimer: payload.timer,
        error: null,
      };
    case "ADD_ITEMS_FAILED":
      return { ...state, isLoading: false, error: payload };
    default:
      return state;
  }
}
