// NPM PACKAGE IMPORTS
import React, { useEffect } from "react";
import { useDispatch } from "react-redux";

// REDUX IMPORTS
import { handleSetPageTitle } from "../../redux/generalActions";

// COMPONENT IMPORTS
import TabList from "msuite_storybook/dist/tabs/TabList";
import WorkflowAssignments from "./workflowAssignments/WorkflowAssignments";

// STYLES IMPORTS
import "./manageAssignments.scss";
import "./../styles/general.scss";

const ManageAssignments = () => {
  const dispatch = useDispatch();
  const tabs = [
    {
      title: "Workflows",
      header: "Workflow Assignments",
      isDisabled: false,
      component: <WorkflowAssignments />,
    },
  ];

  useEffect(() => {
    dispatch(handleSetPageTitle("Manage Assignments"));
  }, [dispatch]);

  return (
    <div className="manage-assignments-container">
      <TabList tabs={tabs} />
    </div>
  );
};

export default ManageAssignments;
