// NPM PACKAGE IMPORTS
import React, { useEffect, useState, useMemo, useCallback } from "react";
import { useDispatch } from "react-redux";
import Select from "msuite_storybook/dist/select/Select";

// COMPONENTS
import AgTable from "../../reusable/agTable/AgTable";
import AssignUnassignCellRenderer from "../../reusable/frameworkComponents/AssignUnassignCellRenderer";
import FullNameCellRenderer from "../../reusable/frameworkComponents/FullNameCellRenderer";
import ConfirmationModal from "../../reusable/confirmationModal/ConfirmationModal";

// REDUX IMPORTS
import { handleFetchFlows } from "../../flows/flowsActions";
import { handleFetchUsers } from "../../users/usersActions";
import { handleFetchCrews } from "../../reusable/manageCrews/manageCrewsActions";

// SERVICE FNS
import {
  fetchWorkflowAssignments,
  assignWorkflow,
  unassignWorkflow,
} from "../../../_services";

// CONSTANTS
import { roles } from "../../users/usersConstants";
import { columns } from "./workflowAssignmentsConstants";

// HELPER FUNCTION IMPORTS
import { rowClassRules, onSortChanged, naturalSort } from "../../../_utils";

// STYLES IMPORTS
import "./workflowAssignments.scss";
import "./../../styles/general.scss";

const WorkflowAssignments = () => {
  const dispatch = useDispatch();
  const [selectedFlow, setSelectedFlow] = useState(null);
  const [flows, setFlows] = useState([]);
  const [users, setUsers] = useState({}); // store as obj with keys being ids
  const [crews, setCrews] = useState({}); // store as obj with keys being ids
  const [usersAndCrews, setUsersAndCrews] = useState(null);
  const [workflowAssignments, setWorkflowAssignments] = useState([]);
  const [
    filteredWorkflowAssignments,
    setFilteredWorkflowAssignments,
  ] = useState([]);
  const [wfAssignmentsIsLoading, setWFAssignmentsIsLoading] = useState(false);

  // TABLE
  const [allUsersSearchInput, setAllUsersSearchInput] = useState("");
  const [assignedSearchInput, setAssignedSearchInput] = useState("");
  const [usersGridOptionsApi, setUsersGridOptionsApi] = useState(null);
  const [assignedGridOptionsApi, setAssignedGridOptionsApi] = useState(null);

  // CONFIRMATION MODAL
  const [showAssignmentConfirmation, toggleAssignmentConfirmation] = useState(
    false
  );
  const [assignmentConfirmationType, setAssignmentConfirmationType] = useState(
    "ASSIGN"
  );
  const [currentUser, setCurrentUser] = useState(null);

  // GATHER and SET UP data
  const mapWorkFlowAssignments = (allWorkflowAssignments) => {
    let user = {};
    let crew = {};

    // map user / crew details for display
    // filter out any invlaid rows (no name to display)
    const mappedRes = allWorkflowAssignments
      .map((wa) => {
        user = users[wa.user_id] ?? {};
        crew = crews[wa.crew_id] ?? {};
        return {
          ...wa,
          full_name: crew.name ?? user.full_name,
          role_name: roles.find((r) => r.id === wa.role_id)?.name ?? "",
          is_crew: crew.id ? 1 : 0, // adding for Unassign button
        };
      })
      .filter((wa) => !!wa.full_name);

    // store users
    let filteredAssignments = mappedRes.filter((a) => !a.crew_id);
    // grab all unique crew ids
    let uniqCrewIds = [
      ...new Set([
        ...mappedRes.filter((a) => !!a.crew_id).map((o) => o.crew_id),
      ]),
    ];
    // add in a single row per unique crew id
    uniqCrewIds.forEach((id) =>
      filteredAssignments.push(mappedRes.find((mr) => mr.crew_id === id))
    );

    setFilteredWorkflowAssignments(filteredAssignments);
    setWorkflowAssignments(mappedRes);
  };

  useEffect(() => {
    async function fetchData() {
      let tempUandC = [];
      dispatch(handleFetchFlows()).then((f) => {
        if (f && Array.isArray(f)) {
          setFlows(
            f.map((o) => ({
              id: o.id,
              name: o.name,
            }))
          );
        }
      });
      await dispatch(handleFetchUsers()).then((u) => {
        if (u && Array.isArray(u)) {
          let obj = {};
          u.map((o) => ({
            id: o.id,
            user_id: o.id,
            first_name: o.first_name,
            last_name: o.last_name,
            full_name: `${o.first_name} ${o.last_name}`,
            role_id: o.role_id,
            role_name: roles.find((r) => r.id === o.role_id).name ?? "",
            is_crew: 0, // adding for use in table constants
          })).forEach((o) => (obj[o.id] = o));
          setUsers(obj);
          tempUandC.push(...Object.values(obj));
        }
      });
      // Crews return is fairly clean, can use as is
      await dispatch(handleFetchCrews).then((c) => {
        if (c && Array.isArray(c)) {
          let obj = {};
          c.map((o) => ({
            ...o,
            role_name: roles.find((r) => r.id === o.role_id).name ?? "",
            full_name: o.name, // to have it work for display with users
            is_crew: 1, // adding for use in table constants
            crew_id: o.id, // have to include this prop for FullNameCellRenderer
          })).forEach((o) => (obj[o.id] = o));
          setCrews(obj);
          tempUandC.push(...Object.values(obj));
        }
      });

      // store combined users and crews as array, ids may conflict
      setUsersAndCrews(tempUandC);
    }
    fetchData();
  }, []);

  useEffect(() => {
    if (selectedFlow) {
      setWFAssignmentsIsLoading(true);
      fetchWorkflowAssignments(selectedFlow.id)
        .then((res) => {
          if (res && Array.isArray(res)) {
            mapWorkFlowAssignments(res);
          }
        })
        .finally(() => {
          // set loading to false once request is made to trigger table refresh
          setWFAssignmentsIsLoading(false);
        });
    } else if (workflowAssignments.length) {
      setFilteredWorkflowAssignments([]);
      setWorkflowAssignments([]);
    }
  }, [selectedFlow]);

  // DISPLAY
  const displayedFlows = useMemo(() => {
    return flows
      .map((f) => ({ id: f.id, value: f.id, display: f.name }))
      .sort((a, b) => naturalSort(a.display, b.display));
  }, [flows]);

  const customAssignmentMessage = useMemo(() => {
    if (!currentUser) return "";

    return assignmentConfirmationType === "ASSIGN"
      ? `Should the workflow's current work be assigned to ${currentUser.full_name}?`
      : `Should the workflow's current work be unassigned from ${currentUser.full_name}?`;
  }, [currentUser, assignmentConfirmationType]);

  // UPDATE API calls
  const handleAssignmentConfirmation = useCallback(
    (updatePastAssignments, direction) => {
      const userIds = currentUser.is_crew ? null : `${currentUser.user_id}`;
      const crewIds = currentUser.is_crew ? `${currentUser.crew_id}` : null;

      setWFAssignmentsIsLoading(true);

      // assign user/crew to workflow
      if (direction === "ASSIGN") {
        assignWorkflow(
          selectedFlow.id,
          userIds,
          crewIds,
          updatePastAssignments ? 1 : 0
        )
          .then((res) => {
            if (res && Array.isArray(res)) {
              mapWorkFlowAssignments(res);
            }
          })
          .finally(() => {
            // set loading to false once request is made to trigger table refresh
            setWFAssignmentsIsLoading(false);
          });
      } else {
        // unassign
        unassignWorkflow(
          selectedFlow.id,
          userIds,
          crewIds,
          updatePastAssignments ? 1 : 0
        )
          .then((res) => {
            if (res && Array.isArray(res)) {
              mapWorkFlowAssignments(res);
            }
          })
          .finally(() => {
            // set loading to false once request is made to trigger table refresh
            setWFAssignmentsIsLoading(false);
          });
      }

      setCurrentUser(false);
      toggleAssignmentConfirmation(false);
    },
    [workflowAssignments, filteredWorkflowAssignments, currentUser]
  );

  const handleAssignUnassignClick = (user, direction = "ASSIGN") => {
    setAssignmentConfirmationType(direction);
    toggleAssignmentConfirmation(true);
    setCurrentUser(user);
  };

  // AG-GRID handling
  // need to set row data / column defs on data loaded
  useEffect(() => {
    if (assignedGridOptionsApi && !wfAssignmentsIsLoading) {
      assignedGridOptionsApi.setRowData(filteredWorkflowAssignments);
      assignedGridOptionsApi.setColumnDefs(tableColumns);
    }
  }, [assignedGridOptionsApi, wfAssignmentsIsLoading]);

  useEffect(() => {
    if (usersGridOptionsApi && !wfAssignmentsIsLoading && !!usersAndCrews) {
      usersGridOptionsApi.setRowData(usersAndCrews);
      usersGridOptionsApi.setColumnDefs(tableColumns);
    }
  }, [usersGridOptionsApi, wfAssignmentsIsLoading, usersAndCrews]);

  // search
  useEffect(() => {
    if (!assignedGridOptionsApi) return;
    assignedGridOptionsApi.setQuickFilter(assignedSearchInput);
    assignedGridOptionsApi.redrawRows();
  }, [assignedSearchInput, assignedGridOptionsApi]);

  useEffect(() => {
    if (!usersGridOptionsApi) return;
    usersGridOptionsApi.setQuickFilter(allUsersSearchInput);
    usersGridOptionsApi.redrawRows();
  }, [allUsersSearchInput, usersGridOptionsApi]);

  // cols
  const tableColumns = useMemo(() => {
    if (!wfAssignmentsIsLoading) {
      return columns(workflowAssignments, handleAssignUnassignClick);
    }
  }, [wfAssignmentsIsLoading]);

  // grid
  const onGridReady = (params, table) => {
    switch (table) {
      case "USERS":
        params.api.setSideBarVisible(false);
        params.api.setSortModel([
          {
            colId: "full_name",
            sort: "asc",
          },
        ]);
        return setUsersGridOptionsApi(params.api);
      case "ASSIGNED_USERS":
        params.api.setSideBarVisible(false);
        params.api.setSortModel([
          {
            colId: "full_name",
            sort: "asc",
          },
        ]);
        return setAssignedGridOptionsApi(params.api);
      default:
        return;
    }
  };

  const gridOptions = (rowData, table) => ({
    rowData,
    defaultColDef: {
      wrapText: true,
      cellClass: "custom-wrap",
    },
    columnDefs: tableColumns,
    frameworkComponents: {
      assignUnassignCellRenderer: AssignUnassignCellRenderer,
      fullNameCellRenderer: FullNameCellRenderer,
    },
    rowClassRules,
    suppressRowClickSelection: true,
    onGridReady: (params) => onGridReady(params, table),
    onSortChanged,
  });

  return (
    <div className="workflow-assignments">
      <Select
        placeholder="---Select Workflow---"
        options={displayedFlows}
        onInput={(e) =>
          setSelectedFlow(
            displayedFlows.find(
              (f) => f.id === Number.parseInt(e.target.value ?? 0)
            ) ?? null
          )
        }
        value={selectedFlow?.id}
        className="workflow-filter"
        disabled={!usersAndCrews}
      />
      {selectedFlow && (
        <div className="flex-container column">
          <p id="flow-name" className="w-49">
            {selectedFlow.display} Personnel
          </p>
          <div className="flex-container" id="table-container">
            <div className="w-49">
              <input
                className="search-input"
                onChange={(e) => setAllUsersSearchInput(e.target.value)}
                value={allUsersSearchInput}
                placeholder={`Search users`}
                type="text"
              />
              <AgTable
                gridOptions={gridOptions(usersAndCrews ?? [], "USERS")}
              />
            </div>
            <div className="w-49">
              <input
                className="search-input"
                onChange={(e) => setAssignedSearchInput(e.target.value)}
                value={assignedSearchInput}
                placeholder={`Search users`}
                type="text"
              />
              <AgTable
                gridOptions={gridOptions(
                  filteredWorkflowAssignments,
                  "ASSIGNED_USERS"
                )}
              />
            </div>
          </div>
        </div>
      )}

      {showAssignmentConfirmation && (
        <ConfirmationModal
          showModal={showAssignmentConfirmation}
          handleClick={() =>
            handleAssignmentConfirmation(true, assignmentConfirmationType)
          }
          action="WARN"
          item={currentUser}
          message={customAssignmentMessage}
          submitText={
            assignmentConfirmationType === "ASSIGN" ? `Assign` : `Unassign`
          }
          cancelText={
            assignmentConfirmationType === "ASSIGN"
              ? `Don't Assign`
              : `Don't Unassign`
          }
          toggleModal={toggleAssignmentConfirmation}
          handleCancel={() =>
            handleAssignmentConfirmation(false, assignmentConfirmationType)
          }
        />
      )}
    </div>
  );
};

export default WorkflowAssignments;
