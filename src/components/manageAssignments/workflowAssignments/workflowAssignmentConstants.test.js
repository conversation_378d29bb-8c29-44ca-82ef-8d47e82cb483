import { columns } from "./workflowAssignmentsConstants";

describe("Workflow Assignments Constants", () => {
  const testAssignedUsers = [
    {
      user_id: 1,
      full_name: "<PERSON>",
    },
    {
      user_id: 2,
      full_name: "<PERSON>",
    },
  ];

  const fullNameValueGetterParams = {
    data: {
      full_name: "Austin Jess",
    },
  };

  describe("all users / crews column defs", () => {
    const defaultHeaders = ["Full Name", "Role", "Assign/Unassign"];

    const assignUnassign = jest.fn();

    let populatedColumns;

    beforeEach(() => {
      populatedColumns = columns(testAssignedUsers, assignUnassign);
    });

    it("Headers are correct", () => {
      let columnHeaders = populatedColumns.map((c) => c.headerName);
      expect(columnHeaders).toEqual(defaultHeaders);
    });

    describe("FULL NAME", () => {
      let column;

      beforeEach(() => {
        column = populatedColumns.find((c) => c.headerName === "Full Name");
      });

      it("valueGetter", () => {
        expect(column.valueGetter(fullNameValueGetterParams)).toEqual(
          "Austin Jess"
        );
      });
    });

    describe("ASSIGN/UNASSIGN", () => {
      let column;

      beforeEach(() => {
        column = populatedColumns.find(
          (c) => c.headerName === "Assign/Unassign"
        );
      });

      const params1 = {
        value: "assigned",
        data: {
          user_id: 2,
          full_name: "Jessica Pegorick",
        },
      };
      const params2 = {
        value: "unassigned",
        data: {
          user_id: 1,
          full_name: "Austin Jess",
        },
      };

      it("valueGetter assigned", () => {
        expect(column.valueGetter(params1)).toEqual("assigned");
      });

      it("valueFormatter assigned", () => {
        expect(JSON.stringify(column.valueFormatter(params1))).toEqual(
          JSON.stringify({
            assigned: true,
            assignUnassign: () => assignUnassign(params1.data, "UNASSIGN"),
          })
        );
      });

      it("valueGetter unassigned", () => {
        expect(column.valueGetter(params2)).toEqual("assigned");
      });

      it("valueFormatter unassigned", () => {
        expect(JSON.stringify(column.valueFormatter(params2))).toEqual(
          JSON.stringify({
            assigned: false,
            assignUnassign: () => assignUnassign(params2.data, "ASSIGN"),
          })
        );
      });
    });
  });
});
