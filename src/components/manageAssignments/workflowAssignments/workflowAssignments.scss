@import "../../styles/sizes.scss";
@import "../../styles/colors.scss";

.workflow-assignments {
  padding-top: 1rem;

  .workflow-filter {
    max-width: 15rem;
    font-size: 1rem;
    height: 2rem;
  }

  #flow-name {
    color: $textLight;
    align-self: flex-end;
    margin-top: 0;
  }
  .custom-ag-styles.ag-theme-balham-dark {
    width: 100%;

    // total - action row - padding - flow dropdown - assigned personnel text
    height: calc(
      100vh - $headerFooter - $pageTitleAndMargin - 52.4px - 16px - 45px - 51px -
        $tableSearch
    );
  }
  #table-container {
    justify-content: space-between;

    div input.search-input {
      margin-bottom: 0.3rem;
    }
  }
}

// overwrite table height if tabs are displayed - tab list height
.tablist-container:has(> div.tablist-tabs)
  .custom-ag-styles.ag-theme-balham-dark {
  height: calc(
    100vh - $headerFooter - $pageTitleAndMargin - 44.6px - 52.4px - 16px - 45px -
      51px - $tableSearch
  );
}
