import { comparator } from "../../../utils/agTable";

export const columns = (assignedUsers = [], assignUnassign = (f) => f) => {
  return [
    {
      headerName: "Full Name",
      valueGetter: (params) => params.data.full_name,
      cellRenderer: "fullNameCellRenderer",
      cellClass: ["bold", "wrap"],
      sortable: true,
      comparator: comparator,
      colId: "full_name",
      minWidth: 120,
      autoHeight: true,
      menuTabs: ["filterMenuTab"],
      filterParams: {
        buttons: ["reset"],
      },
      filter: "agTextColumnFilter",
    },
    {
      headerName: "Role",
      cellClass: "bold",
      sortable: true,
      field: "role_name",
      menuTabs: ["filterMenuTab"],
      filterParams: {
        buttons: ["reset"],
      },
      filter: "agTextColumnFilter",
    },
    {
      headerName: "Assign/Unassign",

      valueGetter: (params) => {
        return assignedUsers.find((u) =>
          !params.data.is_crew && !params.data.crew_id
            ? u.user_id === params.data.user_id
            : u.crew_id === params.data.crew_id
        )
          ? "assigned"
          : "unassigned";
      },
      valueFormatter: (params) => {
        let assigned = params.value === "assigned",
          direction;

        direction = assigned ? "UNASSIGN" : "ASSIGN";

        return {
          assigned,
          assignUnassign: () => assignUnassign(params.data, direction),
        };
      },
      sortable: true,
      cellRenderer: "assignUnassignCellRenderer",
      pinned: "right",
      suppressMovable: true,
      width: 140,
      menuTabs: ["filterMenuTab"],
      filterParams: {
        buttons: ["reset"],
      },
      filter: "agTextColumnFilter",
    },
  ];
};
