import configureMockStore from "redux-mock-store";
import axios from "axios";
import MockAdapter from "axios-mock-adapter";
import thunk from "redux-thunk";

import {
  receiveStarted,
  receiveFailed,
  receiveSucceeded,
  handleFetchWorkspaces,
  handleFetchReportsByWorkspace,
  handleFetchReportEmbedToken,
  handleFetchDatasetsByWorkspace,
  handleRefreshDataset,
  handleFetchLastSuccessfulRefresh,
  handleFetchRefreshDetails,
  handleClearInProgressRefreshId,
} from "./powerbiAnalyticsActions";

describe("PowerBIEmbedded", () => {
  const testError = (type, customMessage, status) => ({
    error: {
      status: status ? status : 404,
      message: customMessage ? customMessage : `No ${type} found`,
    },
  });

  describe("Action handlers should perform the necessary functions", () => {
    let store;
    let httpMock;

    beforeEach(() => {
      httpMock = new MockAdapter(axios);
      const mockStore = configureMockStore([thunk]);
      store = mockStore({});
    });

    const testWorkspaces = [
      {
        id: "123456789",
        name: "MSUI<PERSON>",
      },
    ];

    const testReports = [
      {
        id: "1",
        name: "report1",
        embedUrl: "embed-url",
      },
      {
        id: "2",
        name: "report2",
        embedUrl: "embed-url",
      },
    ];

    const testEmbedToken = {
      token: "testtoken123456789",
      tokenId: "123",
      expriation: Date.now(),
    };

    const testDatasets = [
      {
        id: "1234",
        name: "Test Dataset",
      },
    ];

    const testLastSuccessfulRefresh = {
      refreshType: "ViaApi",
      startTime: "",
      endTime: "",
      status: "Completed",
    };

    it("handleFetchWorkspaces fetches powerbi workspaces", async () => {
      httpMock
        .onGet(`${process.env.REACT_APP_API}/powerbi/workspaces`)
        .replyOnce(200, testWorkspaces)
        .onGet(`${process.env.REACT_APP_API}/powerbi/workspaces`)
        .replyOnce(404, testError("workspaces"));

      await store.dispatch(handleFetchWorkspaces()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKSPACES"),
          receiveSucceeded("WORKSPACES", testWorkspaces[0]),
        ];
        expect(receivedActions).toEqual(expectedActions);
        expect(receivedActions[1].payload).toEqual(testWorkspaces[0]);

        store.clearActions();
      });

      return store.dispatch(handleFetchWorkspaces()).then(() => {
        const receivedActions = store.getActions();
        const expectedActions = [
          receiveStarted("WORKSPACES"),
          receiveFailed("WORKSPACES", testError("workspaces")),
        ];
        expect(receivedActions).toEqual(expectedActions);
      });
    });

    it("handleFetchReportsByWorkspace fetches all reports from specified workspace", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/powerbi/workspaces/${testWorkspaces[0].id}/reports`
        )
        .replyOnce(200, testReports)
        .onGet(
          `${process.env.REACT_APP_API}/powerbi/workspaces/badworkspace/reports`
        )
        .replyOnce(404, testError("reports"));

      await store
        .dispatch(handleFetchReportsByWorkspace(testWorkspaces[0].id))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("REPORTS"),
            receiveSucceeded("REPORTS", testReports[0]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testReports[0]);

          store.clearActions();
        });

      return store
        .dispatch(handleFetchReportsByWorkspace("badworkspace"))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("REPORTS"),
            receiveFailed("REPORTS", testError("reports")),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testError("reports"));
        });
    });

    it("handleFetchReportEmbedToken fetches embed token for specified report", async () => {
      httpMock
        .onPost(`${process.env.REACT_APP_API}/powerbi/generate-embed-token`)
        .replyOnce(200, testEmbedToken)
        .onPost(`${process.env.REACT_APP_API}/powerbi/generate-embed-token`)
        .replyOnce(404, testError("embed_token", "Invalid request", 400));

      await store
        .dispatch(
          handleFetchReportEmbedToken(testDatasets[0].id, testReports[0].id)
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("EMBED_TOKEN"),
            receiveSucceeded("EMBED_TOKEN", testEmbedToken),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(JSON.stringify(receivedActions[1].payload)).toEqual(
            JSON.stringify(testEmbedToken)
          );

          store.clearActions();
        });

      return store
        .dispatch(
          handleFetchReportEmbedToken(testDatasets[0].id, "badreportid")
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("EMBED_TOKEN"),
            receiveFailed(
              "EMBED_TOKEN",
              testError("embed_token", "Invalid request", 400)
            ),
          ];
          expect(receivedActions).toEqual(expectedActions);
        });
    });

    it("handleFetchDatasetsByWorkspace fetchs all datasets associated with workspace", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/powerbi/workspaces/${testWorkspaces[0].id}/datasets`
        )
        .replyOnce(200, testDatasets)
        .onGet(
          `${process.env.REACT_APP_API}/powerbi/workspaces/badworkspace/datasets`
        )
        .replyOnce(404, testError("datasets"));

      await store
        .dispatch(handleFetchDatasetsByWorkspace(testWorkspaces[0].id))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("DATASETS"),
            receiveSucceeded("DATASETS", testDatasets[0]),
          ];

          expect(receivedActions).toEqual(expectedActions);
          expect(JSON.stringify(receivedActions[1].payload)).toEqual(
            JSON.stringify(testDatasets[0])
          );

          store.clearActions();
        });

      return store
        .dispatch(handleFetchDatasetsByWorkspace("badworkspace"))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("DATASETS"),
            receiveFailed("DATASETS", testError("datasets")),
          ];
          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(testError("datasets"));
        });
    });

    it("handleRefreshDataset refreshes dataset and returns request_id", async () => {
      httpMock
        .onPost(
          `${process.env.REACT_APP_API}/powerbi/workspaces/${testWorkspaces[0].id}/datasets/${testDatasets[0].id}/refresh`
        )
        .replyOnce(200, { request_id: "1" })
        .onPost(
          `${process.env.REACT_APP_API}/powerbi/workspaces/${testWorkspaces[0].id}/datasets/baddataset/refresh`
        )
        .replyOnce(404, testError(null, "Unable to refresh dataset"));

      await store
        .dispatch(
          handleRefreshDataset(testWorkspaces[0].id, testDatasets[0].id)
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("REFRESH_DATASET"),
            receiveSucceeded("REFRESH_DATASET", { request_id: "1" }),
          ];
          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual({ request_id: "1" });

          store.clearActions();
        });

      return store
        .dispatch(handleRefreshDataset(testWorkspaces[0].id, "baddataset"))
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("REFRESH_DATASET"),
            receiveFailed(
              "REFRESH_DATASET",
              testError(null, "Unable to refresh dataset")
            ),
          ];
          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(
            testError(null, "Unable to refresh dataset")
          );
        });
    });

    it("handleFetchLastSuccessfullRefresh fetches refresh object of most successful dataset refresh", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/powerbi/workspaces/${testWorkspaces[0].id}/datasets/${testDatasets[0].id}/refresh-history?refresh_type=latest_successful`
        )
        .replyOnce(200, testLastSuccessfulRefresh)
        .onGet(
          `${process.env.REACT_APP_API}/powerbi/workspaces/${testWorkspaces[0].id}/datasets/baddataset/refresh-history?refresh_type=latest_successful`
        )
        .replyOnce(404, testError("last successful refresh"));

      await store
        .dispatch(
          handleFetchLastSuccessfulRefresh(
            testWorkspaces[0].id,
            testDatasets[0].id
          )
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("LAST_SUCCESSFUL_REFRESH"),
            receiveSucceeded(
              "LAST_SUCCESSFUL_REFRESH",
              testLastSuccessfulRefresh[0]
            ),
          ];
          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(
            testLastSuccessfulRefresh[0]
          );

          store.clearActions();
        });

      return store
        .dispatch(
          handleFetchLastSuccessfulRefresh(testWorkspaces[0].id, "baddataset")
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("LAST_SUCCESSFUL_REFRESH"),
            receiveFailed(
              "LAST_SUCCESSFUL_REFRESH",
              testError("last successful refresh")
            ),
          ];
          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(
            testError("last successful refresh")
          );
        });
    });

    it("handleFetchRefreshDetails fetches refresh object based off id", async () => {
      httpMock
        .onGet(
          `${process.env.REACT_APP_API}/powerbi/workspaces/${testWorkspaces[0].id}/datasets/${testDatasets[0].id}/refreshes/1`
        )
        .replyOnce(200, testLastSuccessfulRefresh)
        .onGet(
          `${process.env.REACT_APP_API}/powerbi/workspaces/${testWorkspaces[0].id}/datasets/${testDatasets[0].id}/refreshes/badid`
        )
        .replyOnce(404, testError("refresh details"));

      await store
        .dispatch(
          handleFetchRefreshDetails(
            testWorkspaces[0].id,
            testDatasets[0].id,
            "1"
          )
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("REFRESH_DETAILS"),
            receiveSucceeded("REFRESH_DETAILS"),
          ];
          expect(receivedActions).toEqual(expectedActions);

          store.clearActions();
        });

      return store
        .dispatch(
          handleFetchRefreshDetails(
            testWorkspaces[0].id,
            testDatasets[0].id,
            "badid"
          )
        )
        .then(() => {
          const receivedActions = store.getActions();
          const expectedActions = [
            receiveStarted("REFRESH_DETAILS"),
            receiveFailed("REFRESH_DETAILS", testError("refresh details")),
          ];
          expect(receivedActions).toEqual(expectedActions);
          expect(receivedActions[1].payload).toEqual(
            testError("refresh details")
          );
        });
    });

    it("handleClearInProgressRefreshId", () => {
      store.dispatch(handleClearInProgressRefreshId);

      const receivedActions = store.getActions();
      const expectedActions = [receiveSucceeded("CLEAR_IN_PROGRESS_REFRESH")];

      expect(receivedActions).toEqual(expectedActions);
      store.clearActions();
    });
  });
});
