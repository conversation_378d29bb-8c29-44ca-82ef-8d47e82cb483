// NPM PACKAGE IMPORTS
import React from "react";
import { PowerBIEmbed } from "powerbi-client-react";

const PowerBIEmbedded = ({
  embedConfig,
  setEmbeddedComponent,
  setCurrentPage,
}) => {
  // Map of event handlers to be applied to the embedding report
  const eventHandlersMap = new Map([
    [
      "loaded",
      () => {
        console.log("Report has loaded");
      },
    ],
    [
      "rendered",
      () => {
        console.log("Report has rendered");
      },
    ],
    [
      "pageChanged",
      (event) => {
        if (!event) return;
        setCurrentPage(event?.detail?.newPage);
      },
    ],
    [
      "error",
      (event) => {
        if (!event) return;
        console.error(event?.detail);
      },
    ],
  ]);

  return (
    <>
      <PowerBIEmbed
        embedConfig={embedConfig}
        eventHandlers={eventHandlersMap}
        cssClassName={`powerbi-wrapper`}
        getEmbeddedComponent={(embedObject) => {
          setEmbeddedComponent(embedObject);
        }}
      />
    </>
  );
};

export default PowerBIEmbedded;
