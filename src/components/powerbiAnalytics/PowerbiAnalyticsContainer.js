// NPM PACKAGE IMPORTS
import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { models } from "powerbi-client";
import Button from "msuite_storybook/dist/button/Button";

// REDUX IMPORTS
import {
  handleFetchDatasetsByWorkspace,
  handleFetchReportEmbedToken,
  handleFetchReportsByWorkspace,
  handleFetchWorkspaces,
  handleRefreshDataset,
  handleFetchLastSuccessfulRefresh,
  handleFetchRefreshDetails,
  handleClearInProgressRefreshId,
} from "./powerbiAnalyticsActions";
import { handleSetPageTitle } from "../../redux/generalActions";

// COMPONENT IMPORTS
import PowerBIEmbedded from "./PowerbiEmbedded";

// UTIL IMPORTS
import { formatDateTime } from "./utils";

// STYLE IMPORTS
import "./stylesPowerbiAnalytics.scss";

const POWERBI_SYSTEM_FEATURE_ID = 35;
const POWERBI_PERMISSION_ID = 304;

const PowerBIEmbeddedContainer = () => {
  const [embedConfig, setEmbedConfig] = useState(null);
  const [embeddedComponent, setEmbeddedComponent] = useState(null);
  const [currentPage, setCurrentPage] = useState(null);

  const {
    workspace,
    sharedWorkspace,
    report,
    dataset,
    inProgressRefresh,
    lastSuccessfulRefresh,
  } = useSelector((state) => state.powerbiData);
  const { systemSettings, features, permissions, userSettings } = useSelector(
    (state) => state.profileData
  );

  const dispatch = useDispatch();

  const sharedWorkspaceName = process.env.REACT_APP_PBI_SHARED_WORKSPACE_NAME;

  // Check for feature and redirect if not included
  useEffect(() => {
    if (!features?.length || !permissions.length) return;

    // redirect to homepage if user doesn't have permission or system feature
    if (
      !features.includes(POWERBI_SYSTEM_FEATURE_ID) ||
      !permissions.includes(POWERBI_PERMISSION_ID)
    ) {
      const homePage = userSettings?.homePage
        ? `${userSettings?.home_page}`
        : "jobs";
      const URL = `${process.env.REACT_APP_FABPRO}/${homePage}`;
      window.location.assign(URL);
    }
  }, [features, userSettings, permissions]);

  // FETCH WORKSPACES (client and shared)
  useEffect(() => {
    dispatch(handleSetPageTitle());
    if (!systemSettings?.powerbi_workspace_name) return;

    dispatch(handleFetchWorkspaces(systemSettings.powerbi_workspace_name));
    dispatch(handleFetchWorkspaces(sharedWorkspaceName, true));
  }, [systemSettings]);

  // FETCH SHARED REPORT / CLIENT DATASET
  useEffect(() => {
    if (!workspace || !sharedWorkspace) return;

    dispatch(handleFetchReportsByWorkspace(sharedWorkspace?.id));
    dispatch(handleFetchDatasetsByWorkspace(workspace?.id));
  }, [workspace, sharedWorkspace]);

  // FETCH EMBED TOKEN
  useEffect(() => {
    if (!report || !dataset) return;

    dispatch(handleFetchReportEmbedToken(dataset?.id, report?.id)).then(
      (res) => {
        if (res.error) return;

        setEmbedConfig({
          type: "report",
          id: report?.id,
          embedUrl: report?.embedUrl,
          accessToken: res?.token,
          tokenType: models.TokenType.Embed,

          // Bind client dataset to the shared report
          datasetBinding: {
            datasetId: dataset?.id,
          },
          settings: {
            layoutType: models.LayoutType.Custom,
            customLayout: {
              displayOption: models.DisplayOption.FitToWidth,
            },
          },
        });
      }
    );
  }, [report, dataset]);

  // FETCH LAST SUCCESSFUL REFRESH
  useEffect(() => {
    if (!workspace || !dataset) return;

    dispatch(handleFetchLastSuccessfulRefresh(workspace?.id, dataset?.id));
  }, [workspace, dataset]);

  const handleRefreshClick = async () => {
    if (!workspace?.id || !dataset?.id) return;

    // TODO - might be good to rework this at some point
    const refreshObject = await dispatch(
      handleRefreshDataset(workspace?.id, dataset?.id)
    );

    const refreshInterval = setInterval(async () => {
      if (!refreshObject?.request_id) {
        handleReportReload();
        clearInterval(refreshInterval);
      }

      let refreshDetails = await dispatch(
        handleFetchRefreshDetails(
          workspace?.id,
          dataset?.id,
          refreshObject?.request_id
        )
      );

      // if the refresh request has been completed, clear out the inProgressRefresh redux state
      if (refreshDetails?.extendedStatus === "Completed") {
        handleReportReload();
        clearInterval(refreshInterval);
      }
    }, 5000);
  };

  const handleReportReload = async () => {
    await dispatch(handleClearInProgressRefreshId);
    await dispatch(
      handleFetchLastSuccessfulRefresh(workspace?.id, dataset?.id)
    );

    // reload embedded viewer to pull in refreshed data
    await embeddedComponent.reload();

    // set to previous page of report after reloading - timeout is needed to give the report (embeddedComponent) time to build out pages
    setTimeout(async () => {
      // confirm we have a page match to our currentPage state
      const pages = await embeddedComponent.getPages();
      if (!pages.find((page) => page.name === currentPage?.name)) return;

      currentPage && embeddedComponent.setPage(currentPage?.name);
    }, 800);
  };

  return (
    <div className="embedded-wrapper">
      <div className="report-toolbar">
        <h2>Analytics</h2>
        <div>
          {workspace?.id && dataset?.id && (
            <Button
              disabled={inProgressRefresh?.request_id}
              onClick={handleRefreshClick}
            >
              Refresh
            </Button>
          )}
          {lastSuccessfulRefresh?.endTime && (
            <p>
              Last Refresh:{" "}
              <span>{formatDateTime(lastSuccessfulRefresh?.endTime)}</span>
            </p>
          )}
          {inProgressRefresh?.request_id && (
            <p className="in-progress">Refresh In Progress</p>
          )}
        </div>
      </div>
      {embedConfig && (
        <PowerBIEmbedded
          embedConfig={embedConfig}
          setEmbeddedComponent={setEmbeddedComponent}
          setCurrentPage={setCurrentPage}
        />
      )}
    </div>
  );
};

export default PowerBIEmbeddedContainer;
