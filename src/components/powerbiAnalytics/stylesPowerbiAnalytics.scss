@import "../styles/colors.scss";

div.embedded-wrapper {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: calc(100vh - 110px);
  padding: 0 10px;

  & div.report-toolbar {
    & div {
      margin-top: 5px;
      display: flex;
      height: 32px;
      align-items: center;
      gap: 10px;

      & p {
        color: $lighterSlate;
        font-size: 0.9rem;
      }

      & p.in-progress {
        color: $red;
        font-weight: bold;
      }
    }
    & button {
      background-color: $fabProBlue;
      color: white;
      height: 28px;
      font-size: 0.8rem;
      padding: 4px 12px;
    }
  }

  & h2 {
    margin: 0;
    padding: 5px;
    color: white;
  }

  & div.powerbi-wrapper {
    flex-grow: 1;
    height: 100%;
  }
}
