const initialState = {
  error: null,
  isLoading: false,
  workspace: null,
  sharedWorkspace: null,
  report: null,
  embedToken: null,
  dataset: null,
  inProgressRefresh: null,
  lastSuccessfulRefresh: null,
};

export default function reducer(state = initialState, { type, payload }) {
  switch (type) {
    case "RECEIVE_WORKSPACES_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_WORKSPACES_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_WORKSPACES_SUCCEEDED":
      return { ...state, isLoading: false, workspace: payload };
    case "RECEIVE_REPORTS_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_REPORTS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_REPORTS_SUCCEEDED":
      return { ...state, isLoading: false, report: payload };
    case "RECEIVE_EMBED_TOKEN_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_EMBED_TOKEN_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_EMBED_TOKEN_SUCCEEDED":
      return { ...state, isLoading: false, embedToken: payload };
    case "RECEIVE_DATASETS_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_DATASETS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_DATASETS_SUCCEEDED":
      return { ...state, isLoading: false, dataset: payload };
    case "RECEIVE_SHARED_WORKSPACE_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_SHARED_WORKSPACE_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_SHARED_WORKSPACE_SUCCEEDED":
      return { ...state, isLoading: false, sharedWorkspace: payload };
    case "RECEIVE_REFRESH_DATASET_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_REFRESH_DATASET_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_REFRESH_DATASET_SUCCEEDED":
      return { ...state, isLoading: false, inProgressRefresh: payload };
    case "RECEIVE_CLEAR_IN_PROGRESS_REFRESH_SUCCEEDED":
      return { ...state, isLoading: false, inProgressRefresh: null };
    case "RECEIVE_LAST_SUCCESSFUL_REFRESH_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_LAST_SUCCESSFUL_REFRESH_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_LAST_SUCCESSFUL_REFRESH_SUCCEEDED":
      return { ...state, isLoading: false, lastSuccessfulRefresh: payload };
    case "RECEIVE_REFRESH_DETAILS_STARTED":
      return { ...state, isLoading: true };
    case "RECEIVE_REFRESH_DETAILS_FAILED":
      return { ...state, isLoading: false, error: payload };
    case "RECEIVE_REFRESH_DETAILS_SUCCEEDED":
      return { ...state, isLoading: false };
    default:
      return state;
  }
}
