import utils from "../../_utils";

export const fetchWorkspaces = (workspaceName) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `powerbi/workspaces`;
  const method = "GET";

  if (workspaceName && typeof workspaceName === "string")
    apiPath += `?workspace_name=${workspaceName}`;

  return apiCall(apiPath, method);
};

export const fetchReportsByWorkspace = (workspaceId) => {
  const { apiCall } = utils.useApiCall();

  const apiPath = `powerbi/workspaces/${workspaceId}/reports`;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const fetchReportEmbedToken = (datasetId, reportId) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `powerbi/generate-embed-token`;
  const method = "POST";

  const body = {
    client_dataset_id: datasetId,
    report_id: reportId,
  };

  return apiCall(apiPath, method, body, false);
};

export const fetchDatasetsByWorkspace = (workspaceId, profileId) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `powerbi/workspaces/${workspaceId}/datasets`;
  const method = "GET";

  return apiCall(apiPath, method);
};

export const refreshDataset = (workspaceId, datasetId) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `powerbi/workspaces/${workspaceId}/datasets/${datasetId}/refresh`;
  const method = "POST";

  return apiCall(apiPath, method);
};

export const fetchRefreshHistory = (
  workspaceId,
  datasetId,
  refreshType = "LATEST_SUCCESSFUL"
) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `powerbi/workspaces/${workspaceId}/datasets/${datasetId}/refresh-history?refresh_type=${refreshType?.toLowerCase()}`;
  const method = "GET";

  return apiCall(apiPath, method, null, null, true);
};

export const fetchRefreshById = (workspaceId, datasetId, refreshId) => {
  const { apiCall } = utils.useApiCall();

  let apiPath = `powerbi/workspaces/${workspaceId}/datasets/${datasetId}/refreshes/${refreshId}`;
  const method = "GET";

  // disable loading spinner
  return apiCall(apiPath, method, null, null, false, true);
};
