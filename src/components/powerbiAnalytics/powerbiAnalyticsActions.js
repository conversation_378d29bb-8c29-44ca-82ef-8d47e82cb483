import { datadogLogs } from "@datadog/browser-logs";

import {
  fetchWorkspaces,
  fetchReportsByWorkspace,
  fetchReportEmbedToken,
  fetchDatasetsByWorkspace,
  refreshDataset,
  fetchRefreshHistory,
  fetchRefreshById,
} from "./services";

import store from "../../redux/store";

export const receiveStarted = (type) => ({
  type: `RECEIVE_${type}_STARTED`,
});
export const receiveSucceeded = (type, payload) => ({
  type: `RECEIVE_${type}_SUCCEEDED`,
  payload,
});
export const receiveFailed = (type, error) => ({
  type: `RECEIVE_${type}_FAILED`,
  payload: error,
});

export const handleFetchWorkspaces = (
  workspaceName,
  isSharedWorkspace = false
) => (dispatch) => {
  const type = isSharedWorkspace ? "SHARED_WORKSPACE" : "WORKSPACES";

  dispatch(receiveStarted(type));
  return fetchWorkspaces(workspaceName).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res[0]));

    return res;
  });
};

export const handleFetchReportsByWorkspace = (workspaceId) => (dispatch) => {
  const type = "REPORTS";

  dispatch(receiveStarted(type));
  return fetchReportsByWorkspace(workspaceId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res[0]));

    return res;
  });
};

export const handleFetchReportEmbedToken = (datasetId, reportId) => (
  dispatch
) => {
  const type = "EMBED_TOKEN";

  dispatch(receiveStarted(type));
  return fetchReportEmbedToken(datasetId, reportId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    return res;
  });
};

export const handleFetchDatasetsByWorkspace = (workspaceId, profileId) => (
  dispatch
) => {
  const type = "DATASETS";

  dispatch(receiveStarted(type));
  return fetchDatasetsByWorkspace(workspaceId, profileId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res[0]));

    return res;
  });
};

// TODO - add profileId when ready
export const handleRefreshDataset = (workspaceId, datasetId) => (dispatch) => {
  const type = "REFRESH_DATASET";

  dispatch(receiveStarted(type));
  return refreshDataset(workspaceId, datasetId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type, res));

    datadogLogs.logger.info("PowerBI - Dataset Refresh", {
      user_id: store.getState().profileData.userId,
      username: store.getState().profileData.userInfo.username,
      name: "ViaAPI",
    });

    return res;
  });
};

export const handleClearInProgressRefreshId = (dispatch) => {
  dispatch(receiveSucceeded("CLEAR_IN_PROGRESS_REFRESH"));
};

export const handleFetchLastSuccessfulRefresh = (workspaceId, datasetId) => (
  dispatch
) => {
  const type = "LAST_SUCCESSFUL_REFRESH";

  dispatch(receiveStarted(type));
  return fetchRefreshHistory(workspaceId, datasetId, "LATEST_SUCCESSFUL").then(
    (res) => {
      if (res.error) dispatch(receiveFailed(type, res));
      else dispatch(receiveSucceeded(type, res[0]));

      return res;
    }
  );
};

export const handleFetchRefreshDetails = (
  workspaceId,
  datasetId,
  refreshId
) => (dispatch) => {
  const type = "REFRESH_DETAILS";

  dispatch(receiveStarted(type));
  return fetchRefreshById(workspaceId, datasetId, refreshId).then((res) => {
    if (res.error) dispatch(receiveFailed(type, res));
    else dispatch(receiveSucceeded(type));

    return res;
  });
};
