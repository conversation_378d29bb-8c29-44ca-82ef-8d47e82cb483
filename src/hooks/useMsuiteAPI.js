/**
 *  Wrapper for useAxios hook that handles msuite auth and base URL.
 *  This hook can handle everything including cancels and built in caching in a declarative
 *  or dispatch style behavior.
 */

/** @typedef {import("axios-1x").AxiosRequestConfig} AxiosRequestConfig */
/** @typedef {import("axios-hooks").Options} AxiosHooksOptions */
/** @typedef {import("axios-hooks").UseAxiosResult} UseAxiosResult */

// NPM PACKAGE IMPORTS
import { useCallback, useState } from "react";
// import { v4 as uuid } from "uuid";
import { makeUseAxios } from "axios-hooks";
import axios from "axios-1x"; // Using ALIAS here to use the latest version

// HELPER FUNCTION IMPORTS
import { useApiCall } from "../_utils";
// import {
//   handleDequeueSpinner,
//   handleEnqueueSpinner,
// } from "../components/reusable/generalSpinner/generalSpinnerActions";
import { notify } from "../components/reusable/alertPopup/alertPopupActions";
import store from "../redux/store";

// Store pending requests
const pendingRequests = new Map();

// Create a custom Axios instance with your base URL
const axiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API,
});

// const spinnerId = uuid();

const handleErrorResponseFromApi = (data) => {
  // TODO - update this to handle more error responses
  if (data?.error) {
    return data.error?.message;
  }
};

/**
 * These interceptors will ensure that duplicate requets will get canceled
 * regardless of different query parameters!
 */
axiosInstance.interceptors.request.use((config) => {
  const requestKey = `${config.method}:${config.url}`;
  if (pendingRequests.has(requestKey)) {
    const cancelToken = pendingRequests.get(requestKey);
    cancelToken.cancel("Canceled duplicate request");
  }
  const cancelToken = axios.CancelToken.source();
  config.cancelToken = cancelToken.token;
  pendingRequests.set(requestKey, cancelToken);
  return config;
});

axiosInstance.interceptors.response.use(
  (response) => {
    const requestKey = `${response.config.method}:${response.config.url}`;
    pendingRequests.delete(requestKey);

    // hide the spinner on success
    // store.dispatch(handleDequeueSpinner(spinnerId));
    return response;
  },
  (error) => {
    if (axios.isCancel(error)) {
      console.log("Request canceled", error.message);
    } else if (error.config) {
      const requestKey = `${error.config.method}:${error.config.url}`;
      pendingRequests.delete(requestKey);
    }

    const errorMessage = handleErrorResponseFromApi(error?.response?.data);

    if (errorMessage) {
      store.dispatch(
        notify({
          id: Date.now(),
          type: "ERROR",
          message: errorMessage,
        })
      );
    }
    // hide the spinner on error
    // store.dispatch(handleDequeueSpinner(spinnerId));

    return Promise.reject(error);
  }
);

// Create a customized version of useAxios with makeUseAxios
const useAxios = makeUseAxios({
  axios: axiosInstance, // Use the custom Axios instance
  defaultOptions: {
    manual: true, // Control when the request is made
    useCache: false, // Disable cache
    ssr: false, // Adjust for Server-Side Rendering if needed
    autoCancel: true,
  },
});

/**
 * @param {AxiosRequestConfig} config
 * @param {AxiosHooksOptions} [options={}]
 * @returns {UseAxiosResult}
 */
const useMsuiteAPI = (config, options = {}, msuiteOptions = {}) => {
  const { tokenToUse } = useApiCall(); // Get token

  // Manually if last req ncanceled, because axios doesn't do it for us
  const [isCanceled, setIsCanceled] = useState(false);

  if (!tokenToUse) {
    console.error("useMsuiteAPI: No bearer token found, returning empty data");
  }

  // Pass through to useAxios, injecting the token dynamically
  const [
    { data, loading, error },
    originalRefetch,
    originalCancelFunction,
  ] = useAxios(
    {
      ...config,
      headers: {
        ...config.headers,
        Authorization: `Bearer ${tokenToUse}`, // Inject the token
      },
    },
    { ...options }
  );

  // Create an enhanced refetch function that resets isCanceled state
  const refetch = useCallback(
    (...args) => {
      // display spinner
      // if (!msuiteOptions.disableLoadingSpinner)
      // store.dispatch(handleEnqueueSpinner(spinnerId, 3000));

      setIsCanceled(false); // Reset canceled state when refetching
      return originalRefetch(...args);
    },
    [originalRefetch]
  );

  // Create an enhanced cancel function that tracks the cancel and refreshes state
  const cancelFunction = useCallback(() => {
    originalCancelFunction();
    setIsCanceled(true);
  }, [originalCancelFunction]);

  // Determine if error is a cancellation
  const errorIsCancel = axios.isCancel(error) || isCanceled;

  return [
    {
      data,
      loading: errorIsCancel ? false : loading,
      error: errorIsCancel ? null : error,
      canceled: errorIsCancel,
    },
    refetch,
    cancelFunction,
  ];
};

export default useMsuiteAPI;
