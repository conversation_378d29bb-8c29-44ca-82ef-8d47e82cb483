import useLocalStorage from "use-local-storage";
import { useSelector } from "react-redux";

/**
 * persistant useState()
 * Identical syntax except extra parameter for key name
 */
export function useLocalStorageForUser(key, defaultValue = undefined) {
  const { userId } = useSelector((state) => state.profileData);
  const keyWithUserId = `${key}-${userId || 0}`;
  const [value, setValue] = useLocalStorage(keyWithUserId, defaultValue);
  return [value, setValue];
}
