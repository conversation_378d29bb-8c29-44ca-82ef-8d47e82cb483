// NPM PACKAGE IMPORTS
import { useSelector } from "react-redux";
import PropTypes from "prop-types";

// EXPORTS
const useTranslations = (jsonObject) => {
  const { language } = useSelector((state) => state.generalData);

  return (str) => {
    if (jsonObject && jsonObject[language]) {
      return jsonObject[language][str];
    }

    return str;
  };
};

useTranslations.propTypes = {
  // IMPORTED JSON FILE
  // i.e. - footerTranslations, NOT "footerTranslations.json"
  jsonObject: PropTypes.object,
};

export default useTranslations;
