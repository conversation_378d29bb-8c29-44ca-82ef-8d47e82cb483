import { useEffect } from "react";

export default function useOutsideClick(ref, callback) {
  // Close dropdown if clicked outside of element
  function handleClickOutside(event) {
    if (ref.current && !ref.current.contains(event.target)) {
      callback(event);
    }
  }

  useEffect(() => {
    // Bind the event listener
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      // Unbind the event listener on cleanup
      document.removeEventListener("mousedown", handleClickOutside);
    };
  });
}
