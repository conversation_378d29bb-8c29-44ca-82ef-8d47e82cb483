parameters:
  - name: P_API_BRANCH
    type: string
    default: "$(Build.SourceBranchName)"
  - name: P_FORGE_BRANCH
    type: string
    default: "development"
  - name: P_LOGIN_BRANCH
    type: string
    default: "development"
  - name: P_MYSQL_BRANCH
    type: string
    default: "$(Build.SourceBranchName)"
  - name: P_PHP_BRANCH
    type: string
    default: "development"
  - name: P_REACT_BRANCH
    type: string
    default: "$(Build.SourceBranchName)"
  - name: P_DB_HOST
    type: string
    default: "default"
  - name: P_DB_PASS
    type: string
    default: "default"
  - name: P_DB_USER
    type: string
    default: "default"
  - name: TEST_AT_DATABASE
    type: string
    default: "bandiflorida_dev"
  - name: TEST_AT_USERNAME
    type: string
    default: "admin@bandiflorida_dev.com"
  - name: TEST_AT_PASSWORD
    type: string
    default: "tester"
  - name: P_QA_BOOL
    type: boolean
    default: false
trigger:
  branches:
    include:
      - "feature/*"
      - "main"
      - "qa"
      - "staging"
      - "development"
stages:
  - stage: variable_validation
    condition: or(eq(variables['Build.SourceBranchName'], 'qa'), startsWith( variables['Build.SourceBranch'], 'refs/heads/feature') )
    displayName: "variable_validation"
    jobs:
      - job: "bool_check"
        displayName: "bool_check"
        pool:
          vmImage: "ubuntu-latest"
        steps:
          - script: |
              source ci-variables.config
              if [ "$ON_DEMAND" == 'true' ] && \
                [ "$P_FORGE_BRANCH" == 'main' ] && \
                [ "$P_LOGIN_BRANCH" == 'main' ] && \
                [ "$P_MYSQL_BRANCH" == 'main' ] && \
                [ "$P_PHP_BRANCH" == 'main' ] && \
                [ "$P_REACT_BRANCH" == 'main' ]; then
                 echo "ON_DEMAND is set true. Rest of the Repos are pointing towards 'main' so, \n Throwing error to stop Automatic pipeline"
                 exit 1
              fi 
              echo "P_API_BRANCH is $P_API_BRANCH"
              echo "P_FORGE_BRANCH is $P_FORGE_BRANCH"
              echo "P_LOGIN_BRANCH is $P_LOGIN_BRANCH"
              echo "P_MYSQL_BRANCH is $P_MYSQL_BRANCH"
              echo "P_PHP_BRANCH is $P_PHP_BRANCH"
              echo "P_REACT_BRANCH is $P_REACT_BRANCH"
            displayName: "check your branches"
            env:
              P_API_BRANCH: ${{ parameters.P_API_BRANCH }}
              P_FORGE_BRANCH: ${{ parameters.P_FORGE_BRANCH }}
              P_LOGIN_BRANCH: ${{ parameters.P_LOGIN_BRANCH }}
              P_MYSQL_BRANCH: ${{ parameters.P_MYSQL_BRANCH }}
              P_PHP_BRANCH: ${{ parameters.P_PHP_BRANCH }}
              P_REACT_BRANCH: ${{ parameters.P_REACT_BRANCH }}
  # - stage: "Unit_Testing"
  #   condition: and(not(failed()), startsWith(variables['Build.SourceBranch'], 'refs/heads/feature'))
  #   displayName: api_unit_test
  #   jobs:
  #     - job: "npm_test"
  #       displayName: "npm_test"
  #       pool:
  #         vmImage: "ubuntu-latest"
  #       steps:
  #         - task: NodeTool@0
  #           inputs:
  #             versionSpec: "16.x"
  #           displayName: "Install Node.js"
  #         - script: |
  #             echo "test placeholder"
  #           displayName: "automation test"
  - stage: "Build_and_Deploy_Feature_Branch"
    pool:
      name: dev-pool
    condition: "and(startsWith(variables['Build.SourceBranch'], 'refs/heads/feature'), not(failed()))"
    jobs:
      - job: api_and_fab
        displayName: Deploy_API_and_FAB
        steps:
          - bash: sudo python3 /opt/azure-cicd/cicd.py $BUILD_SOURCEBRANCHNAME fab_pro_web $P_API_BRANCH main $P_PHP_BRANCH $BUILD_SOURCEBRANCHNAME $P_FORGE_BRANCH $P_LOGIN_BRANCH
            env:
              BUILD_SOURCEBRANCHNAME: $(BUILD_SOURCEBRANCHNAME)
              BUILD_SOURCEBRANCH: $(BUILD_SOURCEBRANCHNAME)
              GIT_CREDS_PASS: $(GIT_CREDS_PASS)
              P_API_BRANCH: ${{ parameters.P_API_BRANCH }}
              P_PHP_BRANCH: ${{ parameters.P_PHP_BRANCH }}
              P_REACT_BRANCH: ${{ parameters.P_REACT_BRANCH }}
              P_LOGIN_BRANCH: ${{ parameters.P_LOGIN_BRANCH }}
              P_FORGE_BRANCH: ${{ parameters.P_FORGE_BRANCH }}
  # - stage: "Automation_Testing"
  #   condition: and(not(failed()), startsWith(variables['Build.SourceBranch'], 'refs/heads/feature'))
  #   displayName: feature_branch_automation_test
  #   jobs:
  #     - job: "automation_test"
  #       displayName: "automation_test"
  #       pool:
  #         name: dev-pool
  #       steps:
  #         - script: |
  #                   REPO_VAR=fab_pro_web ## replace this value based on the base repo
  #                   echo "Fetching secrets from AWS Secrets Manager..."
  #                   secret=$(aws secretsmanager get-secret-value --region "us-east-1" --secret-id "dev-secrets" --query SecretString --output text)
  #                   echo "Secrets fetched successfully."
  #                   echo "Setting environment variables..."
  #                   export GIT_CREDS_USER=$(echo "$secret" | jq -r '.git_user')
  #                   export GIT_CREDS_PASS=$(echo "$secret" | jq -r '.git_pass')
  #                   export DB_HOST=$(echo "$secret" | jq -r '.db_ip')
  #                   export DB_USER=$(echo "$secret" | jq -r '.db_user')
  #                   export DB_PASSWORD=$(echo "$secret" | jq -r '.db_pass')
  #                   CLONE_URL="https://$GIT_CREDS_USER:$<EMAIL>/MSuiteTech/Automation/_git/Automation"
  #                   echo "Clone URL is $CLONE_URL"
  #                   export FOLDER_CODE_BASE="/var/www/automation"
  #                   echo "updating the codebase to latest"
  #                   if [ -d "$FOLDER_CODE_BASE" ]; then
  #                     if [ -d "$FOLDER_CODE_BASE/.git" ]; then
  #                       echo "Directory exists and is a Git repository. Performing git pull."
  #                       cd "$FOLDER_CODE_BASE"
  #                       sudo git fetch --all
  #                       sudo git reset --hard origin/main
  #                       sudo git pull origin main
  #                       sudo chown -R ubuntu:ubuntu "$FOLDER_CODE_BASE"
  #                     else
  #                       echo "Automation Testing Directory exists but is not a Git repository. Please check with Admin" && exit 1 ;
  #                     fi
  #                   else
  #                     echo "Directory does not exist. Performing git clone."
  #                     sudo git clone "$CLONE_URL" "$FOLDER_CODE_BASE"
  #                     sudo chown -R ubuntu:ubuntu "$FOLDER_CODE_BASE"
  #                   fi
  #                   cd "$FOLDER_CODE_BASE"
  #                   echo "Setting additional environment variables..."
  #                   export API_URL=$(sudo python3 /opt/azure-cicd/construct_urls.py $BUILD_SOURCEBRANCHNAME $REPO_VAR | grep api | rev | awk '{print $1}' | rev)/api/
  #                   export SITE_URL=$(sudo python3 /opt/azure-cicd/construct_urls.py $BUILD_SOURCEBRANCHNAME $REPO_VAR | grep fab | rev | awk '{print $1}' | rev)/
  #                   export APP_USERNAME="$P_TEST_AT_USERNAME"
  #                   export APP_PASSWORD="$P_TEST_AT_PASSWORD"
  #                   export DATABASE="$P_TEST_AT_DATABASE"
  #                   export DB_USER_PATTERN="devapi"
  #                   export DB_PORT=3306
  #                   echo "Creating .env file..."
  #                   [ ! -f .env ] && touch .env
  #                   echo "API_URL=$API_URL" > .env
  #                   echo "SITE_URL=$SITE_URL" >> .env
  #                   echo "APP_USERNAME=$APP_USERNAME" >> .env
  #                   echo "APP_PASSWORD=\"$APP_PASSWORD\"" >> .env
  #                   echo "DATABASE=\"$DATABASE\"" >> .env
  #                   echo "DB_USER_PATTERN=\"$DB_USER_PATTERN\"" >> .env
  #                   echo "DB_PORT=$DB_PORT" >> .env
  #                   echo "DB_HOST=$DB_HOST" >> .env
  #                   echo "DB_USER=$DB_USER" >> .env
  #                   echo "DB_PASSWORD=$DB_PASSWORD" >> .env
  #                   echo "Contents of .env file:"
  #                   cat .env
  #                   echo "Installing dependencies..."
  #                   npm install
  #                   echo "Running the stage command..."
  #                   npm run stage || /bin/true
  #                   echo "Storing artifacts"
  #                   REPORT_FOLDER=$(Build.SourceBranchName)
  #                   mkdir -p /home/<USER>/automation_test_reports/$REPORT_FOLDER/api_automation_report
  #                   echo "##vso[task.setvariable variable=API_REPORT_PATH]/home/<USER>/automation_test_reports/$REPORT_FOLDER/api_automation_report"
  #                   cd /var/www/automation/mochawesome-report
  #                   LATEST_FILE=$(ls -t | head -n 1)
  #                   rm -rf /home/<USER>/automation_test_reports/$REPORT_FOLDER/api_automation_report/*
  #                   cp $LATEST_FILE /home/<USER>/automation_test_reports/$REPORT_FOLDER/api_automation_report/
  #           displayName: "Automation test"
  #           env:
  #               P_TEST_AT_DATABASE: ${{ parameters.TEST_AT_DATABASE }}
  #               P_TEST_AT_USERNAME: ${{ parameters.TEST_AT_USERNAME }}
  #               P_TEST_AT_PASSWORD: ${{ parameters.TEST_AT_PASSWORD }}
  #         - task: PublishPipelineArtifact@1
  #           inputs:
  #             targetPath: '$(API_REPORT_PATH)' # Path to the artifact files
  #             artifact: 'MochawesomeReport_dev' # Name of the artifact
  #             publishLocation: 'pipeline'
  # - stage: "UI_Automation_feature_branch"
  #   condition: and(not(failed()), startsWith(variables['Build.SourceBranch'], 'refs/heads/feature'))
  #   displayName: feature_branch_UI_automation_test
  #   jobs:
  #     - job: "UI_automation_test"
  #       displayName: "UI_automation_test"
  #       pool:
  #         name: dev-pool
  #       steps:
  #         - script: |
  #                   REPO_VAR=fab_pro_web
  #                   secret=$(aws secretsmanager get-secret-value --region "us-east-1" --secret-id "dev-secrets" --query SecretString --output text)
  #                   echo "Secrets fetched successfully."
  #                   echo "Setting environment variables..."
  #                   export GIT_CREDS_USER=$(echo "$secret" | jq -r '.git_user')
  #                   export GIT_CREDS_PASS=$(echo "$secret" | jq -r '.git_pass')
  #                   CLONE_URL="https://$GIT_CREDS_USER:$<EMAIL>/MSuiteTech/Automation/_git/msuite-cypress-prod-validation"
  #                   echo "Clone URL is $CLONE_URL"
  #                   export FOLDER_CODE_BASE="/var/www/msuite_ui_automation"
  #                   echo "updating the codebase to latest"
  #                   if [ -d "$FOLDER_CODE_BASE" ]; then
  #                     if [ -d "$FOLDER_CODE_BASE/.git" ]; then
  #                       echo "Directory exists and is a Git repository. Performing git pull."
  #                       cd "$FOLDER_CODE_BASE"
  #                       sudo git fetch --all
  #                       sudo git reset --hard origin/main
  #                       sudo git pull origin main
  #                       sudo chown -R ubuntu:ubuntu "$FOLDER_CODE_BASE"
  #                     else
  #                       echo "Msuite UI Automation Testing Directory exists but is not a Git repository. Please check with Admin" && exit 1 ;
  #                     fi
  #                   else
  #                     echo "Directory does not exist. Performing git clone."
  #                     sudo git clone "$CLONE_URL" "$FOLDER_CODE_BASE"
  #                     sudo chown -R ubuntu:ubuntu "$FOLDER_CODE_BASE"
  #                   fi
  #                   cd "$FOLDER_CODE_BASE"
  #                   echo "Setting additional environment variables..."
  #                   export API_URL=$(sudo python3 /opt/azure-cicd/construct_urls.py $BUILD_SOURCEBRANCHNAME $REPO_VAR | grep api | rev | awk '{print $1}' | rev)/api/
  #                   export BASE_URL=$(sudo python3 /opt/azure-cicd/construct_urls.py $BUILD_SOURCEBRANCHNAME $REPO_VAR | grep fab | rev | awk '{print $1}' | rev)/
  #                   export LOGIN_URL=$(sudo python3 /opt/azure-cicd/construct_urls.py $BUILD_SOURCEBRANCHNAME $REPO_VAR | grep login | rev | awk '{print $1}' | rev)/
  #                   export ADMIN_USERNAME="$P_TEST_AT_USERNAME"
  #                   export ADMIN_PASSWORD="$P_TEST_AT_PASSWORD"
  #                   echo "Creating .env file..."
  #                   [ ! -f .env ] && touch .env
  #                   echo "API_URL=$API_URL" > .env
  #                   echo "BASE_URL=$BASE_URL" >> .env
  #                   echo "LOGIN_URL=$LOGIN_URL" >> .env
  #                   echo "ADMIN_USERNAME=$ADMIN_USERNAME" >> .env
  #                   echo "ADMIN_PASSWORD=$ADMIN_PASSWORD" >> .env
  #                   echo "Contents of .env file:"
  #                   cat .env
  #                   echo "Installing dependencies..."
  #                   npm install
  #                   npm install cypress
  #                   echo "Running the npm run command..."
  #                   npm run smoke || /bin/true
  #                   echo "Storing artifacts"
  #                   REPORT_FOLDER=$(Build.SourceBranchName)
  #                   mkdir -p /home/<USER>/automation_test_reports/$REPORT_FOLDER/ui_automation_report
  #                   echo "##vso[task.setvariable variable=REPORT_PATH]/home/<USER>/automation_test_reports/$REPORT_FOLDER/ui_automation_report"
  #                   rm -rf /home/<USER>/automation_test_reports/$REPORT_FOLDER/ui_automation_report/*
  #                   cp -r -f /var/www/msuite_ui_automation/cypress/reports/html/* /home/<USER>/automation_test_reports/$REPORT_FOLDER/ui_automation_report/
  #           displayName: "UI Automation test"
  #           env:
  #               P_TEST_AT_USERNAME: ${{ parameters.TEST_AT_USERNAME }}
  #               P_TEST_AT_PASSWORD: ${{ parameters.TEST_AT_PASSWORD }}
  #         - task: PublishPipelineArtifact@1
  #           inputs:
  #             targetPath: '$(REPORT_PATH)' # Path to the artifact files
  #             artifact: 'UI_Automation_Report' # Name of the artifact
  #             publishLocation: 'pipeline'
  #         - task: PublishPipelineArtifact@1
  #           inputs:
  #             targetPath: '/var/www/msuite_ui_automation/cypress/videos/' # Path to the artifact files
  #             artifact: 'UI_Automation_video' # Name of the artifact
  #             publishLocation: 'pipeline'
  # - stage: DEV_Deploy
  #   condition: and(not(failed()), eq(variables['Build.SourceBranchName'], 'development'))
  #   displayName: "DEV_Deploy"
  #   pool:
  #     name: dev-pool
  #   jobs:
  #     - job: Dev_Deploy_fab_pro_Web
  #       displayName: Dev_Deploy_fab_pro_Web
  #       steps:
  #         - bash: cd /home/<USER>/msuite_servers_ansible && sudo ansible-playbook full_stack_server.yaml --tags fab_react_full_deployment  -e @group_vars/all.yaml  -e api_release_tag=development  -e bimt_release_tag=development -e forge_release_tag=development -e login_release_tag=development -e mysql_release_tag=development -e php_release_tag=development   -e react_release_tag=development
  # - stage: "Automation_Testing_development_branch"
  #   condition: and(always(), startsWith(variables['Build.SourceBranch'], 'refs/heads/development'))
  #   displayName: development_branch_automation_test
  #   jobs:
  #     - job: "automation_test"
  #       displayName: "automation_test"
  #       pool:
  #         name: dev-pool
  #       steps:
  #         - script: |
  #                   echo "Fetching secrets from AWS Secrets Manager..."
  #                   secret=$(aws secretsmanager get-secret-value --region "us-east-1" --secret-id "dev-secrets" --query SecretString --output text)
  #                   echo "Secrets fetched successfully."
  #                   echo "Setting environment variables..."
  #                   export GIT_CREDS_USER=$(echo "$secret" | jq -r '.git_user')
  #                   export GIT_CREDS_PASS=$(echo "$secret" | jq -r '.git_pass')
  #                   export DB_HOST=$(echo "$secret" | jq -r '.db_ip')
  #                   export DB_USER=$(echo "$secret" | jq -r '.db_user')
  #                   export DB_PASSWORD=$(echo "$secret" | jq -r '.db_pass')
  #                   CLONE_URL="https://$GIT_CREDS_USER:$<EMAIL>/MSuiteTech/Automation/_git/Automation"
  #                   echo "Clone URL is $CLONE_URL"
  #                   export FOLDER_CODE_BASE="/var/www/automation"
  #                   echo "updating the codebase to latest"
  #                   if [ -d "$FOLDER_CODE_BASE" ]; then
  #                     if [ -d "$FOLDER_CODE_BASE/.git" ]; then
  #                       echo "Directory exists and is a Git repository. Performing git pull."
  #                       cd "$FOLDER_CODE_BASE"
  #                       sudo git fetch --all
  #                       sudo git reset --hard origin/main
  #                       sudo git pull origin main
  #                       sudo chown -R ubuntu:ubuntu "$FOLDER_CODE_BASE"
  #                     else
  #                       echo "Automation Testing Directory exists but is not a Git repository. Please check with Admin" && exit 1 ;
  #                     fi
  #                   else
  #                     echo "Directory does not exist. Performing git clone."
  #                     sudo git clone "$CLONE_URL" "$FOLDER_CODE_BASE"
  #                     sudo chown -R ubuntu:ubuntu "$FOLDER_CODE_BASE"
  #                   fi
  #                   cd "$FOLDER_CODE_BASE"
  #                   echo "Setting additional environment variables..."
  #                   export API_URL=https://dev-api.msuite.com/api/
  #                   export SITE_URL=https://dev-fab.msuite.com/
  #                   export APP_USERNAME="$P_TEST_AT_USERNAME"
  #                   export APP_PASSWORD="$P_TEST_AT_PASSWORD"
  #                   export DATABASE="$P_TEST_AT_DATABASE"
  #                   export DB_USER_PATTERN="devapi"
  #                   export DB_PORT=3306
  #                   echo "Creating .env file..."
  #                   [ ! -f .env ] && touch .env
  #                   echo "API_URL=$API_URL" > .env
  #                   echo "SITE_URL=$SITE_URL" >> .env
  #                   echo "APP_USERNAME=$APP_USERNAME" >> .env
  #                   echo "APP_PASSWORD=\"$APP_PASSWORD\"" >> .env
  #                   echo "DATABASE=\"$DATABASE\"" >> .env
  #                   echo "DB_USER_PATTERN=\"$DB_USER_PATTERN\"" >> .env
  #                   echo "DB_PORT=$DB_PORT" >> .env
  #                   echo "DB_HOST=$DB_HOST" >> .env
  #                   echo "DB_USER=$DB_USER" >> .env
  #                   echo "DB_PASSWORD=$DB_PASSWORD" >> .env
  #                   echo "Contents of .env file:"
  #                   cat .env
  #                   echo "Installing dependencies..."
  #                   npm install
  #                   echo "Running the stage command..."
  #                   npm run stage || /bin/true
  #                   echo "Storing artifacts"
  #                   REPORT_FOLDER=$(Build.SourceBranchName)
  #                   mkdir -p /home/<USER>/automation_test_reports/$REPORT_FOLDER/api_automation_report
  #                   echo "##vso[task.setvariable variable=API_REPORT_PATH]/home/<USER>/automation_test_reports/$REPORT_FOLDER/api_automation_report"
  #                   cd /var/www/automation/mochawesome-report
  #                   LATEST_FILE=$(ls -t | head -n 1)
  #                   rm -rf /home/<USER>/automation_test_reports/$REPORT_FOLDER/api_automation_report/*
  #                   cp $LATEST_FILE /home/<USER>/automation_test_reports/$REPORT_FOLDER/api_automation_report/
  #           displayName: "Automation test"
  #           env:
  #               P_TEST_AT_DATABASE: ${{ parameters.TEST_AT_DATABASE }}
  #               P_TEST_AT_USERNAME: ${{ parameters.TEST_AT_USERNAME }}
  #               P_TEST_AT_PASSWORD: ${{ parameters.TEST_AT_PASSWORD }}
  #         - task: PublishPipelineArtifact@1
  #           inputs:
  #             targetPath: '$(API_REPORT_PATH)' # Path to the artifact files
  #             artifact: 'MochawesomeReport_dev' # Name of the artifact
  #             publishLocation: 'pipeline'
  # - stage: "UI_Automation_development"
  #   condition: and(always(), startsWith(variables['Build.SourceBranch'], 'refs/heads/development'))
  #   displayName: development_branch_UI_automation_test
  #   jobs:
  #     - job: "UI_automation_test"
  #       displayName: "UI_automation_test"
  #       pool:
  #         name: dev-pool
  #       steps:
  #         - script: |
  #                   secret=$(aws secretsmanager get-secret-value --region "us-east-1" --secret-id "dev-secrets" --query SecretString --output text)
  #                   echo "Secrets fetched successfully."
  #                   echo "Setting environment variables..."
  #                   export GIT_CREDS_USER=$(echo "$secret" | jq -r '.git_user')
  #                   export GIT_CREDS_PASS=$(echo "$secret" | jq -r '.git_pass')
  #                   CLONE_URL="https://$GIT_CREDS_USER:$<EMAIL>/MSuiteTech/Automation/_git/msuite-cypress-prod-validation"
  #                   echo "Clone URL is $CLONE_URL"
  #                   export FOLDER_CODE_BASE="/var/www/msuite_ui_automation"
  #                   echo "updating the codebase to latest"
  #                   if [ -d "$FOLDER_CODE_BASE" ]; then
  #                     if [ -d "$FOLDER_CODE_BASE/.git" ]; then
  #                       echo "Directory exists and is a Git repository. Performing git pull."
  #                       cd "$FOLDER_CODE_BASE"
  #                       sudo git fetch --all
  #                       sudo git reset --hard origin/main
  #                       sudo git pull origin main
  #                       sudo chown -R ubuntu:ubuntu "$FOLDER_CODE_BASE"
  #                     else
  #                       echo "Msuite UI Automation Testing Directory exists but is not a Git repository. Please check with Admin" && exit 1 ;
  #                     fi
  #                   else
  #                     echo "Directory does not exist. Performing git clone."
  #                     sudo git clone "$CLONE_URL" "$FOLDER_CODE_BASE"
  #                     sudo chown -R ubuntu:ubuntu "$FOLDER_CODE_BASE"
  #                   fi
  #                   cd "$FOLDER_CODE_BASE"
  #                   echo "Setting additional environment variables..."
  #                   export API_URL=https://dev-api.msuite.com/
  #                   export BASE_URL=https://dev-fab.msuite.com/
  #                   export LOGIN_URL=https://dev-login.msuite.com/
  #                   export ADMIN_USERNAME="$P_TEST_AT_USERNAME"
  #                   export ADMIN_PASSWORD="$P_TEST_AT_PASSWORD"
  #                   echo "Creating .env file..."
  #                   [ ! -f .env ] && touch .env
  #                   echo "API_URL=$API_URL" > .env
  #                   echo "BASE_URL=$BASE_URL" >> .env
  #                   echo "LOGIN_URL=$LOGIN_URL" >> .env
  #                   echo "ADMIN_USERNAME=$ADMIN_USERNAME" >> .env
  #                   echo "ADMIN_PASSWORD=$ADMIN_PASSWORD" >> .env
  #                   echo "Contents of .env file:"
  #                   cat .env
  #                   echo "Installing dependencies..."
  #                   npm install
  #                   npm install cypress
  #                   echo "Running the npm run command..."
  #                   npm run smoke
  #                   echo "Storing artifacts"
  #                   REPORT_FOLDER=$(Build.SourceBranchName)
  #                   mkdir -p /home/<USER>/automation_test_reports/$REPORT_FOLDER/ui_automation_report
  #                   echo "##vso[task.setvariable variable=REPORT_PATH]/home/<USER>/automation_test_reports/$REPORT_FOLDER/ui_automation_report"
  #                   rm -rf /home/<USER>/automation_test_reports/$REPORT_FOLDER/ui_automation_report/*
  #                   cp -r -f /var/www/msuite_ui_automation/cypress/reports/html/* /home/<USER>/automation_test_reports/$REPORT_FOLDER/ui_automation_report/
  #           displayName: "UI Automation test"
  #           env:
  #               P_TEST_AT_USERNAME: ${{ parameters.TEST_AT_USERNAME }}
  #               P_TEST_AT_PASSWORD: ${{ parameters.TEST_AT_PASSWORD }}
  #         - task: PublishPipelineArtifact@1
  #           inputs:
  #             targetPath: '$(REPORT_PATH)' # Path to the artifact files
  #             artifact: 'UI_Automation_Report' # Name of the artifact
  #             publishLocation: 'pipeline'
  # - stage: Stage_Deploy
  #   condition: and(not(failed()), eq(variables['Build.SourceBranchName'], 'main'))
  #   displayName: "Stage_Deploy"
  #   pool:
  #     name: dev-pool
  #   jobs:
  #     - job: deploy_react
  #       displayName: deploy_react
  #       steps:
  #         - bash: echo "The deployment in the higher environments will be handled by the operations team."
