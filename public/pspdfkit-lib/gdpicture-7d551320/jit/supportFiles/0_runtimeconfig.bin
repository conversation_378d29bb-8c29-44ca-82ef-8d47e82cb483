
MMicrosoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmabilitytruefSystem.ComponentModel.TypeConverter.EnableUnsafeBinaryFormatterInDesigntimeLicenseContextSerializationfalse6System.Reflection.Metadata.MetadataUpdater.IsSupportedfalse9System.Resources.ResourceManager.AllowCustomResourceTypesfalse<System.Runtime.InteropServices.BuiltInComInterop.IsSupportedfalseJSystem.Runtime.InteropServices.EnableConsumingManagedCodeFromNativeHostingfalse9System.Runtime.InteropServices.EnableCppCLIHostActivationfalseVSystem.Runtime.InteropServices.Marshalling.EnableGeneratedComInterfaceComImportInteropfalseESystem.Runtime.Serialization.EnableUnsafeBinaryFormatterSerializationfalse&System.StartupHookProvider.IsSupportedfalse-System.Text.Encoding.EnableUnsafeUTF7Encodingfalse<System.Text.Json.JsonSerializer.IsReflectionEnabledByDefaulttrue-System.Threading.Thread.EnableAutoreleasePoolfalse