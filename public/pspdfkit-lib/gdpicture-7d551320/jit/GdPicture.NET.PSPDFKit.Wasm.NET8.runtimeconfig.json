{"runtimeOptions": {"tfm": "net8.0", "includedFrameworks": [{"name": "Microsoft.NETCore.App", "version": "8.0.10"}], "configProperties": {"Microsoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmability": true, "System.ComponentModel.TypeConverter.EnableUnsafeBinaryFormatterInDesigntimeLicenseContextSerialization": false, "System.Reflection.Metadata.MetadataUpdater.IsSupported": false, "System.Resources.ResourceManager.AllowCustomResourceTypes": false, "System.Runtime.InteropServices.BuiltInComInterop.IsSupported": false, "System.Runtime.InteropServices.EnableConsumingManagedCodeFromNativeHosting": false, "System.Runtime.InteropServices.EnableCppCLIHostActivation": false, "System.Runtime.InteropServices.Marshalling.EnableGeneratedComInterfaceComImportInterop": false, "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false, "System.StartupHookProvider.IsSupported": false, "System.Text.Encoding.EnableUnsafeUTF7Encoding": false, "System.Text.Json.JsonSerializer.IsReflectionEnabledByDefault": true, "System.Threading.Thread.EnableAutoreleasePool": false}, "wasmHostProperties": {"runtimeArgs": [], "perHostConfig": [], "mainAssembly": "GdPicture.NET.PSPDFKit.Wasm.NET8.dll"}}}