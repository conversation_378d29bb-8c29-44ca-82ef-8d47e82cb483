{"mainAssemblyName": "GdPicture.NET.PSPDFKit.Wasm.NET8.dll", "resources": {"hash": "sha256-ETgY2MnjQruxuPRdoS2Dt7T6idaAtmL57fz88YJKBO8=", "jsModuleNative": {"dotnet.native.js": "sha256-xgLhbEDfF/lBICPUZWUMQS4MmZSO7mXWr0/bFM0eeWE="}, "jsModuleRuntime": {"dotnet.runtime.js": "sha256-FjrgL9dY6dc2yUoXDWowSfachSgM8O29qUMWDQOJKRI="}, "wasmNative": {"dotnet.native.wasm": "sha256-kLRyUYpwL7S9smLm3Ywp1tBZ+yHMM8qHw07xCAxnZkg="}, "icu": {"icudt_CJK.dat": "sha256-SZLtQnRc0JkwqHab0VUVP7T3uBPSeYzxzDnpxPpUnHk=", "icudt_EFIGS.dat": "sha256-8fItetYY8kQ0ww6oxwTLiT3oXlBwHKumbeP2pRF4yTc=", "icudt_no_CJK.dat": "sha256-L7sV7NEYP37/Qr2FPCePo5cJqRgTXRwGHuwF5Q+0Nfs="}, "assembly": {"BouncyCastle.Cryptography.wasm": "sha256-67QrGetT6fEbzmu5Hnc1dob1HtVaLn+D1TVLf3csq4M=", "ChromeHtmlToPdfLib.wasm": "sha256-C+CIb3XpJpvz8tcIXb83Ne+6FIUu5ZJNjyC51Aq8QgM=", "DocumentFormat.OpenXml.wasm": "sha256-upA93T2lQOiZpMLrMzRzkn8Vc6HnKkSCw35MGmwkIrc=", "DocumentFormat.OpenXml.Framework.wasm": "sha256-6Jbj8OWdFmitrjZR2zJYecVWJ21KxE21MvSDvAfkNyw=", "GdPicture.NET.14.API.wasm": "sha256-kTadkJH8BJ3pzqgTvOcAUn7ImASSFlxcj/EYHHlmCRA=", "GdPicture.NET.14.barcode.1d.writer.wasm": "sha256-FK0hkWKGZkoKVX5fMKmUB9KlkEUUFohy2seCDi2zF7w=", "GdPicture.NET.14.barcode.2d.writer.wasm": "sha256-E5JEO3N+rsTL35GZg8DuCBEGuHIdr2zsyrIR+sBvVeg=", "GdPicture.NET.14.CAD.wasm": "sha256-L+KCq5D/cFbGNKzdo4KcSqv7TS8LCgK7awfXz9QjooE=", "GdPicture.NET.14.CAD.DWG.wasm": "sha256-fAyu54gINNwsQgci8RhB8Itv49yP7YNRaglU8HiOmuI=", "GdPicture.NET.14.Common.wasm": "sha256-1qJHjnWln51wp4zI6fwMrMNydV3MqhD7b0Q0W/uoyfw=", "GdPicture.NET.14.Document.wasm": "sha256-sGLNZsP3vFHVNB0K1g03tQr4lJEmSH1O5dhp0yv3j2U=", "GdPicture.NET.14.Imaging.wasm": "sha256-9zqK357IWYSaL/lO2jOn4KBHxijczHEuKySLLb19iKE=", "GdPicture.NET.14.Imaging.Formats.Conversion.wasm": "sha256-6OdN51nbUfRESnoNK0RPdW3u6T3eXzYg2cHQX6Svlt4=", "GdPicture.NET.14.Imaging.Formats.wasm": "sha256-R8VEH+5Gj6+FeDg+czCGRZSC3S+TxjNFdfBkbkWF8kk=", "GdPicture.NET.14.Imaging.Rendering.wasm": "sha256-7cmZd15EKKTtFdmuVdzXFhJWbRbjjFuPdOkvxF9I0B8=", "GdPicture.NET.14.MSOfficeBinary.wasm": "sha256-YWsR6KoGgdIgo2cFuwm4Ge29ST/73lvSjkRM+rMmR2I=", "GdPicture.NET.14.OpenDocument.wasm": "sha256-y6+ewhdJJjMl//T1IPxuMCjNTXf3XDOQBP6m4TaNEAE=", "GdPicture.NET.14.OpenXML.wasm": "sha256-kH+7qJ8kor1roZpVKwNX5gykXbd4j+NU+1iVQk3anxc=", "GdPicture.NET.14.PDF.wasm": "sha256-i3+Jf8Y9phhVpd9LiVRVj+YwORzAYNjY7agazKWm5ao=", "GdPicture.NET.14.RTF.wasm": "sha256-KonW334ufJ8RI6S8Fbvh6SwyoBNdnDU72nWT/B7xCqA=", "GdPicture.NET.14.SVG.wasm": "sha256-Fi9Yeabp7WtcuJrz61BbuU+XzhgoZdO0UniwtP9ehHg=", "GdPicture.NET.OpenXML.Templating.Wasm.NET8.wasm": "sha256-PBuTQs8SQXyo0Xbuvv+o2f0LL0iEbt657lrOdJHfZXo=", "GdPicture.NET.PSPDFKit.Wasm.NET8.wasm": "sha256-jhiyWHAHIjt3pSS/KFbCdr12ib9+EkTQ4HNxjfJ2DX8=", "GdPicture.NET.Wasm.NET8.wasm": "sha256-/v4DvZl0k94d8yS6U3DZAmdHWk/5oeEaMiygbi5rVeE=", "Microsoft.CSharp.wasm": "sha256-iTpxvn7D6jT4LBm1HY917bxABVkmM3CCJLO7Zjn6bco=", "Microsoft.Win32.Registry.wasm": "sha256-OtiLzmWJ/mphfl9hIeCzsOCaVKuFKWMujEuo/52LTr8=", "MsgReader.wasm": "sha256-tIUBZaEybSTyKHmkpupyamt6p7SqboJtqRrc2PJmUDA=", "Newtonsoft.Json.wasm": "sha256-G97Lg+/AsbDXNl4UtTZ3yGm1qvl/Hb1Qb9AKu32Xe8k=", "OpenMcdf.wasm": "sha256-GbY1SKBNj8uu05INK8eBBENtxiqKH95I8DwwSlRZay0=", "protobuf-net.Core.wasm": "sha256-uGbMq1/fsamkvm0zeYda4/hKINHVJbe+Me2XSUWpE5w=", "protobuf-net.wasm": "sha256-Meo7PL8zpAUe4czMjQRA9Onl5PGLrEhaOX9fL3gwUfY=", "RtfPipe.wasm": "sha256-3FHRVYJ5KNZ7uWogZCE45lZ//jfH5IULrRdfVg4GRLQ=", "System.Collections.Concurrent.wasm": "sha256-a2tQtwMZ7liWnLgedGhHaE/GLLBDk6co8OLDNAYwj24=", "System.Collections.wasm": "sha256-PSft+JWk3MrIIwVpPGPMBjGJ4bTjXK4Q+D1O3LwJdeI=", "System.Collections.Immutable.wasm": "sha256-SoxRSTH4WsDgqqP/OCwVQo6ef9vhx8iBUs3HZ2F+bWg=", "System.Collections.NonGeneric.wasm": "sha256-sAhQceBp+d8nKcN8EI0QGI9E7AeeOaZjFE2THl2ppkk=", "System.Collections.Specialized.wasm": "sha256-NnqwAyJhwpYRwzB/1fuue3OjRjEcleXWeAgpQ8LL988=", "System.ComponentModel.wasm": "sha256-Nlg/nIYedk72Brjsfnc42VsWnSq5XKHC2Jpyq7jRYy0=", "System.ComponentModel.EventBasedAsync.wasm": "sha256-dJzfGfUtK7OGadgi5ldNGc7rL4K5hMoYVjOJoZvZ0l4=", "System.ComponentModel.Primitives.wasm": "sha256-Pv2+Z5KqXsVcbO4QHwttt0gEnizut1Ucu3bXhZKNMfg=", "System.ComponentModel.TypeConverter.wasm": "sha256-CCw9T4oAkH+hb46jMnTsf2Esr6M+wIXVx/6kXoonKuA=", "System.Console.wasm": "sha256-TXWjTee91uD/IyRK8xARbmgRFkTqZJLNrNv5fa5GZP0=", "System.Data.Common.wasm": "sha256-gRyslUOx5wps667OaT8MRZiS9hLWq9VBs3/Rf6ppgD4=", "System.Diagnostics.DiagnosticSource.wasm": "sha256-vV5VJmw3/989muq+4b5vA23pGkZKbYlNACMetj6GVlU=", "System.Diagnostics.Process.wasm": "sha256-2DS3+eU6aeU90iwMDCJQN+oREbqBURPDlrkQDELkiF4=", "System.Diagnostics.TraceSource.wasm": "sha256-TvlRt602GwFEFVa3ZkEfdZ3ghcpu0CRs8XH0NQSVDZA=", "System.wasm": "sha256-jsKXa9gY3uvAXdaGqYVsmioMiLRA9lrZoN8/mb5JUv4=", "System.Drawing.wasm": "sha256-wAGRQJC+s7tGQt0TW9OJ02e7nVR9viPSGRnmM16xnlM=", "System.Drawing.Primitives.wasm": "sha256-wH4n2ReQ3mGKz3pOg/5BPnPAYwMW3L8tzRcJYsxYnG8=", "System.Formats.Asn1.wasm": "sha256-k5GiJaMtWhjTMUaZB4gifJWRfer19abIH8Lptc0zA9I=", "System.IO.Compression.wasm": "sha256-hcEwqbCwJgDyZCqxDVVAvV2KvtF4/OaOUt3/Es6VraY=", "System.IO.Packaging.wasm": "sha256-cTFTNn+xCHLQWZe8PlZGkvRYJFGiFyA78evDsn64Gpw=", "System.Linq.wasm": "sha256-9on8n1s/a0vSU8gcJZeB4pzyDo7VAsfNGKlpGeMCIPE=", "System.Linq.Expressions.wasm": "sha256-oZvNNYmqTQ+6bmAyy/zqRgpOkkCVlVEjdraOmNzcMaA=", "System.Memory.wasm": "sha256-+CXDzUfeRegLsqXpZEYXvvpMGsGJqk0Qbboph8KKqcQ=", "System.Net.Http.wasm": "sha256-KLW9QL2wKDZ5mRJ0LNtutxs3UjbvaTYs4gWyVKs1/gY=", "System.Net.Http.Formatting.wasm": "sha256-un42fxjDyQmAldV9kdz6UXWHWG07zW1H9BmJaIkXhaM=", "System.Net.Mail.wasm": "sha256-hnQXEimS/nE6hIhVX/02TEFjrkP7qjotHjGRyPuEATE=", "System.Net.NetworkInformation.wasm": "sha256-II7PqCWSjRonRR1Sp0hh4ekaCRDK9Rw9j14VqCuFB64=", "System.Net.Primitives.wasm": "sha256-abc+IhzCP13rnotUpP6/0uVUm421ySBkZOFKXiiI5Ko=", "System.Net.Requests.wasm": "sha256-w7kaegp2TJP4rQ1UXZFv11NwdnA/TG0/MN5qiSSB4rk=", "System.Net.Security.wasm": "sha256-97gKTkKNdUfuWzuu5BLAMTlHoM0BSzc8uW8iiYu3BCQ=", "System.Net.ServicePoint.wasm": "sha256-zGWc2gZbIPDQDsZCtsgGnkwk/aPyZ31KYKM5cX5x8xQ=", "System.Net.Sockets.wasm": "sha256-5MbgOJlaA+4MXZ1abk+wmxvhCEezm1W6G8LE7HmQSbs=", "System.Net.WebHeaderCollection.wasm": "sha256-FWmoWdRfM9A+JaTRKJDFk9VukSWEXlFV6j5WVR8MDCU=", "System.Net.WebSockets.Client.wasm": "sha256-fzzBVGWgk1ii7Ax+M0ubxZ+YlABiQNAMkiMKvAZ4PWI=", "System.Net.WebSockets.wasm": "sha256-YRkNIgzSFb0dLaoY7j3BJds3aFWGXEcfp1A4407n6hM=", "System.ObjectModel.wasm": "sha256-fP9zaax7wUDO2qGQQ4IDIAnRYe5/goDo1Yjn3X+0zEw=", "System.Private.CoreLib.wasm": "sha256-b+K4dU0NYp6aDv7Ug7N3lDXjLqMA14LccWL9frV9zFE=", "System.Private.Uri.wasm": "sha256-nxSqrx+dTHWd/Y0v2ajVSLOm1iB8VcuTd/ybo0LBUFE=", "System.Private.Xml.wasm": "sha256-M3iLtqXmMYthNiwbPUIG2eVB4X5PLLHdSEhLbtyyHx8=", "System.Private.Xml.Linq.wasm": "sha256-W1YdMIOZoPgSQqLr035mC1QPM5AI2zxCzIR2Wxp1LGU=", "System.Runtime.InteropServices.JavaScript.wasm": "sha256-+SQhY2+x8q9+DC62PLQOU7eE3qGoCp/yzTzL85YJYXA=", "System.Runtime.Numerics.wasm": "sha256-BkQCI0NrY9HYO0usEOZCpG8EzrfJ+9YjXhsvHKoFrs4=", "System.Runtime.Serialization.Formatters.wasm": "sha256-+tHiDrxzAH+vsdjzywsvZ6Dr66o+2CMTVOFOefWipK0=", "System.Runtime.Serialization.Primitives.wasm": "sha256-Ybesv9WVVHCOm58g3QgFCSOlIqx7N2tg9n8P+HQ+sSQ=", "System.Security.Cryptography.wasm": "sha256-SbX4TMHweKLKVQy+6ITgQ1JldzoXfxjtF0nx0kuXZ6g=", "System.Security.Cryptography.Pkcs.wasm": "sha256-t89Eb56oMNngXYqJEZkrKzluo3riy1fcOoQQjaGHOGc=", "System.Text.Encoding.CodePages.wasm": "sha256-tOHyXwz4QpN95oH3StELlaG5OQHye1ZiNeVy5u/ncCo=", "System.Text.Encodings.Web.wasm": "sha256-m4NMSXmG2FoX8G8SegvaO8bYO2IS+/j3ofwHU+lYS9U=", "System.Text.Json.wasm": "sha256-X19P6bnU0EGvxnoYYe4L7JxxzXzOvUahrM8KTsJfARk=", "System.Text.RegularExpressions.wasm": "sha256-SW6n91GDn5vBcO6R1+GBRw5qco/HsTZTJOnGTuMxinw=", "System.Threading.Tasks.Parallel.wasm": "sha256-st4G7D5P8R93kxztaMj+IJVmYg1C71JslXP8sUwLQoE=", "System.Windows.Extensions.wasm": "sha256-G+RkLqqV+p65mqNO3xT3gnya+vP+Phsq4TUk2ZrXTe8=", "System.Xml.Linq.wasm": "sha256-+wSJnzp1rQiHBUV2Jpsq6/fC1ZJB6gW3Io5Lnbi/9VA="}, "vfs": {"runtimeconfig.bin": {"supportFiles/0_runtimeconfig.bin": "sha256-thfax//rPSFO1ZKFH5Mqadj5fakhuqdFx2JvVSVyq0U="}}}, "debugLevel": 0, "globalizationMode": "sharded"}