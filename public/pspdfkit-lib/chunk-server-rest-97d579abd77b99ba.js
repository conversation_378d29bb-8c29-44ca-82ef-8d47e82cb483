/*!
 * PSPDFKit for Web 2024.8.1 (https://pspdfkit.com/web)
 *
 * Copyright (c) 2016-2024 PSPDFKit GmbH. All rights reserved.
 *
 * THIS SOURCE CODE AND ANY ACCOMPANYING DOCUMENTATION ARE PROTECTED BY INTERNATIONAL COPYRIGHT LAW
 * AND MAY NOT BE RESOLD OR REDISTRIBUTED. USAGE IS BOUND TO THE PSPDFKIT LICENSE AGREEMENT.
 * UNAUTHORIZED REPRODUCTION OR DISTRIBUTION IS SUBJECT TO CIVIL AND CRIMINAL PENALTIES.
 * This notice may not be removed from this file.
 *
 * PSPDFKit uses several open source third-party components: https://pspdfkit.com/acknowledgements/web/
 */
"use strict";(globalThis.webpackChunkPSPDFKit=globalThis.webpackChunkPSPDFKit||[]).push([[6269],{33393:(a,t,e)=>{e.d(t,{RESTProvider:()=>m});var s=e(67136),o=e(49568),i=e(85409),n=e(97881),r=e(82987);class d extends(o.mS({alreadyLoadedPages:(0,o.T5)(),serverURL:null,authPayload:null,isLoaded:!1,isFormsEnabled:!0,loadBookmarksPromise:null,ignoredFormFieldNames:null})){}var l=e(60156),c=e(78236),h=e(85410);class m{constructor(a,t,e){let{isFormsEnabled:o}=e;(0,s.A)(this,"canCreateBackendOrphanWidgets",!0),(0,s.A)(this,"setDocumentHandleConflictCallback",(()=>{})),this.state=new d({serverURL:a,authPayload:t,isFormsEnabled:o}),this._setReadStateCallbacksPromise=new Promise((a=>{this._setReadStateCallbacksPromiseResolve=a}))}async load(){return this.state=this.state.set("isLoaded",!0),this.state.isFormsEnabled&&await this._initializeFormFieldValues(),this}destroy(){}setReadStateCallbacks(a){this._readStateCallbacks=a,this._setReadStateCallbacksPromiseResolve?.()}setAnnotationCallbacks(a){this.annotationCallbacks=a}setBookmarkCallbacks(a){this.bookmarkCallbacks=a}setFormFieldValueCallbacks(a){this.formFieldValueCallbacks=a}async createAnnotation(a,t){this._verifyLoaded();const{id:e,...s}=(0,n.eq)(a),o={id:e,content:s},r=await this._request("/annotations","POST",o);if(200!==r.status)throw new i.uE("PSPDFKit Document Engine returned an error, when saving an annotation.");if("attachment_missing"===(await r.json()).error){const a=function(a,t){const e=new FormData;return e.append("annotation",JSON.stringify(a)),t.forEach(((a,t)=>{t&&a.data&&e.append(t,a.data)})),e}(o,t);if(200!==(await this._request("/annotations","POST",a)).status)throw new i.uE("PSPDFKit Document Engine returned an error, when saving an annotation attachment.")}}async updateAnnotation(a){this._verifyLoaded();const{id:t,...e}=(0,n.eq)(a);await this._request(`/annotations/${t}`,"PUT",{id:t,content:e})}async deleteAnnotation(a){this._verifyLoaded(),await this._request(`/annotations/${a.id}`,"DELETE")}async createBookmark(a){this._verifyLoaded(),await this.loadBookmarks();const{id:t,...e}=(0,r.U)(a);if(200!==(await this._request("/bookmarks","POST",{id:t,content:e})).status)throw new i.uE("PSPDFKit Document Engine returned an error, when saving an bookmark.")}async updateBookmark(a){this._verifyLoaded(),await this.loadBookmarks();const{id:t,...e}=(0,r.U)(a);await this._request(`/bookmarks/${t}`,"PUT",{id:t,content:e})}async deleteBookmark(a){this._verifyLoaded(),await this.loadBookmarks(),await this._request(`/bookmarks/${a}`,"DELETE")}async setFormFieldValue(a){this._verifyLoaded();const t={id:(0,l.B)(a),content:(0,n.cA)(a)};await this._request("/form-field-values","POST",{formFieldValues:[t]})}async createFormFieldValue(){}async deleteFormFieldValue(){}async loadAnnotationsForPageIndex(a){if(this._verifyLoaded(),this.state.alreadyLoadedPages.has(a))await this.state.alreadyLoadedPages.get(a);else try{const t=this._request(`/page-${a}-annotations`,"GET").then((a=>a.json())).catch((a=>{throw a}));this.state=this.state.setIn(["alreadyLoadedPages",a],t);const e=await t;this.state=this.state.setIn(["alreadyLoadedPages",a],Promise.resolve());const s=(0,o.B8)().withMutations((a=>{e.annotations.forEach((t=>{try{a.push((0,n.h8)(t.id,t.content))}catch(a){(0,i.pq)(`Skipped creating annotation #${t.id} from payload because an error occurred while deserializing.`,t.content),(0,i.pq)(a)}}))}));s.size>0&&((0,i.V1)(this.annotationCallbacks),this.annotationCallbacks.createAnnotations(s,(0,o.T5)(),c.n))}catch(a){this._handleError(a,"annotations")}}async loadBookmarks(){if(this._verifyLoaded(),this.state.loadBookmarksPromise)await this.state.loadBookmarksPromise;else try{const a=this._request("/bookmarks","GET").then((a=>a.json())).then((a=>a.data)).catch((a=>{throw a}));this.state=this.state.set("loadBookmarksPromise",a);const t=await a;this.state=this.state.set("loadBookmarksPromise",Promise.resolve()),(0,i.V1)(Array.isArray(t.bookmarks),"Unexpected reply from bookmarks endpoint.");const e=(0,o.B8)().withMutations((a=>{t.bookmarks.forEach((t=>{try{a.push((0,r.r)(t.id,t.content))}catch(a){(0,i.pq)(`Skipped creating bookmark #${t.id} from payload because an error occurred while deserializing.`,t),(0,i.pq)(a)}}))}));e.size>0&&((0,i.V1)(this.bookmarkCallbacks),this.bookmarkCallbacks.createBookmarks(e,c.n))}catch(a){this._handleError(a,"bookmarks")}}async syncChanges(){}async _initializeFormFieldValues(){const a=await this._request("/form-field-values","GET"),t=await a.json();(0,i.V1)(Array.isArray(t.formFieldValues),"Unexpected reply from form-values endpoint.");const e=(0,o.B8)(t.formFieldValues.map((a=>{let{content:t}=a;try{return(0,n.R5)(t)}catch(a){return(0,i.pq)(`Skipped form field value ${t.name} from payload because an error occurred while deserializing.`,t),(0,i.pq)(a),null}})).filter(Boolean));(0,i.V1)(this.formFieldValueCallbacks),this.state.ignoredFormFieldNames&&this.state.ignoredFormFieldNames.size?this.formFieldValueCallbacks.setFormFieldValues(e.filter((a=>!this.state.ignoredFormFieldNames?.includes(a.name)))):this.formFieldValueCallbacks.setFormFieldValues(e)}_handleError(a,t){(0,i.z3)(`Loading or updating ${t} failed:\n\n${a.message}`)}_request(a,t,e){(0,i.V1)(null!=this.state.authPayload,"Cannot call request without authPayload");const s=e instanceof FormData||"object"!=typeof e?null:{"Content-Type":"application/json"},o={"X-PSPDFKit-Token":this.state.authPayload.token,"PSPDFKit-Platform":"web","PSPDFKit-Version":(0,h._q)(),...s};return fetch(`${this.state.serverURL}${a}`,{method:t,headers:o,body:e instanceof FormData?e:"object"==typeof e?JSON.stringify(e):void 0,credentials:"include"})}_verifyLoaded(){if(!this.state.isLoaded)throw new Error("not loaded")}setIgnoredFormFieldNames(a){this.state=this.state.set("ignoredFormFieldNames",a)}async updateTabOrder(){throw new i.uE("Tab order is not supported on this backend.")}async setTabOrder(){throw new i.uE("setTabOrder is not supported on this backend.")}}}}]);