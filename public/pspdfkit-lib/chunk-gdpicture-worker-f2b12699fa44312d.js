/*!
 * PSPDFKit for Web 2024.8.1 (https://pspdfkit.com/web)
 *
 * Copyright (c) 2016-2024 PSPDFKit GmbH. All rights reserved.
 *
 * THIS SOURCE CODE AND ANY ACCOMPANYING DOCUMENTATION ARE PROTECTED BY INTERNATIONAL COPYRIGHT LAW
 * AND MAY NOT BE RESOLD OR REDISTRIBUTED. USAGE IS BOUND TO THE PSPDFKIT LICENSE AGREEMENT.
 * UNAUTHORIZED REPRODUCTION OR DISTRIBUTION IS SUBJECT TO CIVIL AND CRIMINAL PENALTIES.
 * This notice may not be removed from this file.
 *
 * PSPDFKit uses several open source third-party components: https://pspdfkit.com/acknowledgements/web/
 */
"use strict";(globalThis.webpackChunkPSPDFKit=globalThis.webpackChunkPSPDFKit||[]).push([[11],{37979:(t,e,n)=>{n.d(e,{default:()=>a});var r=n(55512),o=n.n(r);function a(){return o()('/*!\n * PSPDFKit for Web 2024.8.1 (https://pspdfkit.com/web)\n *\n * Copyright (c) 2016-2024 PSPDFKit GmbH. All rights reserved.\n *\n * THIS SOURCE CODE AND ANY ACCOMPANYING DOCUMENTATION ARE PROTECTED BY INTERNATIONAL COPYRIGHT LAW\n * AND MAY NOT BE RESOLD OR REDISTRIBUTED. USAGE IS BOUND TO THE PSPDFKIT LICENSE AGREEMENT.\n * UNAUTHORIZED REPRODUCTION OR DISTRIBUTION IS SUBJECT TO CIVIL AND CRIMINAL PENALTIES.\n * This notice may not be removed from this file.\n *\n * PSPDFKit uses several open source third-party components: https://pspdfkit.com/acknowledgements/web/\n */(()=>{"use strict";const t=function t(e){let n;return n=e instanceof Error?e:new Error(e),Object.setPrototypeOf(n,t.prototype),n};t.prototype=Object.create(Error.prototype,{name:{value:"PSPDFKitError",enumerable:!1}});const e=t;function n(t,n){if(!t)throw new e(`Assertion failed: ${n||"Condition not met"}\\n\\nFor further assistance, please go to: https://pspdfkit.com/support/request`)}["a[href]","area[href]","input:not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","iframe","object","embed","[contenteditable]",\'[tabindex]:not([tabindex^="-"])\'].join(",");new WeakMap;async function r(t){const n=(await async function(t){try{const n=await fetch(t).catch((n=>{throw new e(`Error fetching dynamic fonts file ${t}. ${n}`)}));if(200!==n.status)throw new e(`Error fetching dynamic fonts file ${t}. Status code: ${n.status}`);return n}catch(t){throw t}}(t)).json().catch((n=>{throw new e(`Error parsing dynamic fonts file ${t}. ${n}`)}));return n}let o=function(t){return t[t.pdf_a_1a=0]="pdf_a_1a",t[t.pdf_a_1b=1]="pdf_a_1b",t[t.pdf_a_2a=2]="pdf_a_2a",t[t.pdf_a_2u=3]="pdf_a_2u",t[t.pdf_a_2b=4]="pdf_a_2b",t[t.pdf_a_3a=5]="pdf_a_3a",t[t.pdf_a_3u=6]="pdf_a_3u",t[t.pdf_a_3b=7]="pdf_a_3b",t[t.pdf_a_4=8]="pdf_a_4",t[t.pdf_a_4e=9]="pdf_a_4e",t[t.pdf_a_4f=10]="pdf_a_4f",t}({});const a="/create.pdf",i="/save.pdf",s="/create.docx",c="/save.docx",f="/templateData.json",l="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope;let d=null,u=null;function p(t){let e;n(u,"GdPicture WebAssembly is not loaded.");for(var r=arguments.length,o=new Array(r>1?r-1:0),a=1;a<r;a++)o[a-1]=arguments[a];for(const t of o)e=Object.assign(o[0],t);const i=JSON.stringify({type:t,...e}),s=JSON.parse(u.CommandHandler(i));if(!s.success)throw new Error(s.errorReason+": "+s.errorMessage+"\\n"+s.error);return s}const _=new class{_mountCustomFonts(t,e){n(d,"WebAssembly module not loaded."),d.FS.mkdir(e);{const n=l?d.FS.filesystems.WORKERFS:d.FS.filesystems.MEMFS;d.FS.mount(n,{blobs:t},e)}}async loadModule(t,e,n,o,a,i,s){const{Assemblies:c,Module:f}=await async function(t,e){if("string"==typeof t){const n="pspdfkit-lib/",r=`${n}gdpicture-7d551320/jit`,o=`${n}gdpicture-7d551320/aot`;let a,i;e?(a=`${t}${o}/initDotnet.js`,i=`${t}${o}`):(a=`${t}${r}/initDotnet.js`,i=`${t}${r}`);const{initDotnet:s}=await import(a);return s(i,((t,e,n,r,o)=>"blazor.boot.json"===e?fetch(n,{credentials:"same-origin"}):null))}throw new Error("GdPicture WASM loader not implemented")}(t,e);u=c.GdPictureWasm.API,d=f,p("gdpicture/setLicense",{origin:n},{licenseKey:o||"DEMO_PSPDFKIT_WEB"});const l=a?"/fonts":"";if(a&&!d.FS.analyzePath(l).exists&&(this._mountCustomFonts(a,l),p("gdpicture/setFonts",{fontPaths:[l]})),i){const t=await r(i);p("gdpicture/setDynamicFontLoading",{baseUrl:i.split("/").slice(0,-1).join("/"),allowedFonts:t.availableFonts,v:1})}s&&p("gdpicture/setFontSubstitutions",{fontSubstitutions:s})}toPdf(t,e){d.FS.writeFile(a,new Uint8Array(t));const n={file:i,format:"pdf"};e&&e in o&&(n.conformance=e);try{return p("gdpicture/process",{input:{file:a},output:n}),d.FS.readFile(i).buffer}finally{try{d.FS.unlink(i)}catch(t){}}}toOffice(t,e){d.FS.writeFile(a,new Uint8Array(t));const n=`/save.${e}`;try{return p("gdpicture/process",{input:{file:a},output:{file:n,format:e}}),d.FS.readFile(n).buffer}finally{try{d.FS.unlink(n)}catch(t){console.log(t.message)}}}async populateDocumentTemplate(t,e){let n;d.FS.writeFile(s,new Uint8Array(t));try{n=JSON.stringify(e,null,2)}catch(t){throw new Error("Invalid config data")}d.FS.writeFile(f,n);try{return p("gdpicture/process-office-template",{inputFile:s,modelAndConfigFile:f,outputFile:c}),d.FS.readFile(c).buffer}finally{try{d.FS.unlink(c),d.FS.unlink(f)}catch(t){console.log(t.message)}}}},y=self;y.global=y,y.module={},y.onmessage=async t=>{let e,n,{data:r}=t;try{const t=await _[r.action](...r.args);if(e={id:r.id,result:t},Array.isArray(t)){const e=t.filter((t=>t instanceof ArrayBuffer));e.length>0&&(n=e)}t instanceof ArrayBuffer&&(n=[t])}catch(t){const o=[...r.args].filter((t=>t instanceof ArrayBuffer));o.length>0&&(n=o),e={id:r.id,error:t.message||t.toString(),callArgs:r.args}}y.postMessage(e,n)}})();',"Worker",void 0,void 0)}},55512:t=>{t.exports=function(t,e,n,r){var o=self||window;try{try{var a;try{a=new o.Blob([t])}catch(e){(a=new(o.BlobBuilder||o.WebKitBlobBuilder||o.MozBlobBuilder||o.MSBlobBuilder)).append(t),a=a.getBlob()}var i=o.URL||o.webkitURL,s=i.createObjectURL(a),c=new o[e](s,n);return i.revokeObjectURL(s),c}catch(r){return new o[e]("data:application/javascript,".concat(encodeURIComponent(t)),n)}}catch(t){if(!r)throw Error("Inline worker is not supported");return new o[e](r,n)}}}}]);