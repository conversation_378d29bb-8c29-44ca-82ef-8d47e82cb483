/*!
 * PSPDFKit for Web 2024.8.1 (https://pspdfkit.com/web)
 *
 * Copyright (c) 2016-2024 PSPDFKit GmbH. All rights reserved.
 *
 * THIS SOURCE CODE AND ANY ACCOMPANYING DOCUMENTATION ARE PROTECTED BY INTERNATIONAL COPYRIGHT LAW
 * AND MAY NOT BE RESOLD OR REDISTRIBUTED. USAGE IS BOUND TO THE PSPDFKIT LICENSE AGREEMENT.
 * UNAUTHORIZED REPRODUCTION OR DISTRIBUTION IS SUBJECT TO CIVIL AND CRIMINAL PENALTIES.
 * This notice may not be removed from this file.
 *
 * PSPDFKit uses several open source third-party components: https://pspdfkit.com/acknowledgements/web/
 */
(globalThis.webpackChunkPSPDFKit=globalThis.webpackChunkPSPDFKit||[]).push([[5750],{1033:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>z});var n=r(67136),s=r(49568),i=r(85409),o=r(45646),a=r(6163),l=r(99589),h=r(2752),c=r(55994),u=r(34551),d=r(47159),p=r(5293),m=r(97881),f=r(14631),g=r(54781),E=r(85553);class w extends(s.mS({authPayload:null,serverUrl:null,hostedBaseUrl:null,documentId:null,layerName:null,sourcePdfSha256:null,backendPermissions:null,documentURL:null,imageToken:null,instantSettings:null,token:null,features:(0,s.B8)(),signatureFeatureAvailability:E.g.NONE,isFormsEnabled:!0,minSearchQueryLength:1,documentHandle:null,isDocumentHandleOutdated:!1,digitalSignatures:null,defaultGroup:void 0,hasCollaborationPermissions:!1,forceLegacySignaturesFeature:!1,disableWebAssemblyStreaming:!1,overrideMemoryLimit:null,baseCoreUrl:null})){}var v=r(14511),y=r(15168),$=r(28650),S=r(85410),R=r(9627);const P="The image can not be rendered because of an unknown error.";class I{constructor(e){let{identifier:t,url:r,token:n,payload:s,doNotRequestWebP:i=!1}=e;this.identifier=t,this.url=r,this.token=n,this.payload=s,this.doNotRequestWebP=i}abort(){this.httpRequest?.abort()}request(){return new Promise(((e,t)=>{const r=new XMLHttpRequest;this.httpRequest=r,r.open(this.payload?"POST":"GET",this.url,!0),r.setRequestHeader("X-PSPDFKit-Image-Token",this.token),r.setRequestHeader("PSPDFKit-Platform","web"),r.setRequestHeader("PSPDFKit-Version",(0,S._q)()),y.xd&&!this.doNotRequestWebP&&r.setRequestHeader("Accept","image/webp,*/*"),r.responseType="blob",r.onreadystatechange=(async()=>{if(4!==r.readyState)return;if(r.response&&r.response.type.startsWith("application/json")){const n=new FileReader;return n.onload=r=>{const n=JSON.parse(r.target?.result);n.attachments_not_found?e({attachmentsNotFound:n.attachments_not_found}):n.error?"initialization_error"===n.error?e(null):t(new i.uE(`The server could not render the requested image (${n.error})`)):t(new i.uE(P))},n.onerror=()=>t(new i.uE(P)),void n.readAsText(r.response)}if(!(0,v.Nk)(r.status))return void t(new i.uE(P));const n=r.response,s=URL.createObjectURL(n),o=new Image;o.onerror=()=>t(new i.uE(P)),o.src=s;const a=o.decode();try{await a}catch(e){if(!R.H8)throw new i.uE(`The image could not be decoded: ${e.message}`);await new Promise((e=>setTimeout(e,200)))}e(new $.A(o,(()=>URL.revokeObjectURL(s))))}).bind(this),r.send(this.payload)}))}}var A=r(37361),b=r(89574),_=r(41477),N=r(51946),L=r(30026);async function T(e,t,r){const n=await fetch(`${e}/auth`,{method:"POST",headers:{"Content-Type":"application/json","PSPDFKit-Platform":"web","PSPDFKit-Version":(0,S._q)()},body:JSON.stringify({jwt:t.jwt,origin:window.location.href,password:r}),credentials:"include"});return n.ok?n.json():n.text().then((e=>{throw"INVALID_PASSWORD"===e?new i.uE(e):new i.uE(`An error occurred while connecting to PSPDFKit Document Engine: ${e||n.statusText}`)}))}var O=r(25888),x=r(4824),F=r(12219),D=r(37094),C=r(72262),j=r(40927),U=r(9939),k=r(83720),M=r(64337),G=r(89055),B=r(20546),X=r(37506),H=r(26175),V=r(77270),q=r(92026);class z extends C.K{constructor(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;if(super(),t=this,(0,n.A)(this,"_password",null),(0,n.A)(this,"type","SERVER"),(0,n.A)(this,"_requestRenderAnnotation",((e,r,n,s,o,l)=>{const h=`${this._state.documentURL}/render_annotation`,c=`render-annotation-${l?(0,a.Z0)():e.id}`,u=JSON.stringify({data:(0,m.eq)(e),width:s,height:o,detached:l||void 0,formFieldValue:r?(0,m.cA)(r):void 0});let d=!1,p=[];const f=(0,V.yl)();if(e instanceof N.xY){if(this.isVisuallyIdenticalStampAnnotationCached({annotation:e,width:s,height:o}))return this.cachedStampAnnotationDiscardablePromise(f);(0,b.nH)(e)||(this._cachedRenderedAnnotation={index:(0,b.sS)(e),width:s,height:o,APStreamPromise:f.promise})}const g=function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];const a=new FormData;a.append("render",u),r.length>0&&"imageAttachmentId"in e&&e.imageAttachmentId&&n&&a.append(e.imageAttachmentId,n);const l=new I({identifier:c,url:h,token:t._state.imageToken,payload:a,doNotRequestWebP:s>A.HI||o>A.HI}),m=t._requestQueue.enqueue(l,!1);m.promise.then((r=>{d||(r?.attachmentsNotFound?g(r.attachmentsNotFound):r?.attachmentsNotFound?f.reject(new i.uE("Attachment could not be found.")):(e instanceof N.xY&&t._makeEnqueuedRelease(r,(0,b.sS)(e)),f.resolve(r)))})).catch((e=>{d||f.reject(e)})),p.push(m)};return g(),{promise:f.promise,cancel:()=>{d=!0,p.forEach((e=>{e.cancel()}))}}})),(0,n.A)(this,"_requestRenderAnnotations",((e,t,r,n,s)=>{const i=`${this._state.documentURL}/render_annotations`,o=JSON.stringify({annotations:t.map(((t,s)=>({pageIndex:e,pdfObjectId:t,width:r[s],height:n[s]}))),formFieldValues:s});let a,l,h=!1;const c=new Promise(((e,t)=>{a=e,l=t}));return this._fetch(i,{method:"post",body:o,credentials:"include",headers:{"X-PSPDFKit-Image-Token":this._state.imageToken,"Content-Type":"application/json",Accept:"multipart/form-data"}}).then((e=>e.formData())).then((e=>{h||a(Array.from(e.values()))})).catch((e=>{h||l(e)})),{promise:c,cancel:()=>{h=!0}}})),(0,n.A)(this,"handleDocumentHandleConflict",(()=>{this._state=this._state.set("isDocumentHandleOutdated",!0),this.cancelRequests(),this._destroyProvider()})),"object"!=typeof e.authPayload)throw new i.uE("authPayload must be an object that contains the `jwt`. For example: `authPayload: { jwt: 'xxx.xxx.xxx'}`");const s=e.authPayload?.accessToken;let l=null,h=null,c=null;if(s)c=e.hostedBaseUrl||"https://api.pspdfkit.com/",(0,v.Me)(c),(0,f.P1)(s);else{if(l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;const r=e.serverUrl||(0,S.$_)(t.document);if("/"!==r.substr(-1))throw new i.uE("`serverUrl` must have a slash at the end (e.g. `https://pspdfkit.example.com/`).");if(!e.serverUrl){if(r===`${t.location.protocol}//${t.location.host}/`)throw new i.uE('PSPDFKit automatically infers the URL of PSPDFKit Document Engine from the current `<script>` tag.\nIn the current case, this URL is set to the same as the current browser\'s location.\nThis can happen when you bundle pspdfkit.js with your custom JavaScript for example.\n\nTo make sure everything works as expected, please set the `serverUrl` to the URL of PSPDFKit Document Engine:\n\nPSPDFKit.load({\n  serverUrl: "https://pspdfkit-server.example.com/",\n  ...,\n});')}return r}(e,r),"string"!=typeof e.documentId)throw new i.uE("`documentId` must be of type string.");if(h=`${l}i/d/${e.documentId}`,"object"!=typeof e.authPayload||!("jwt"in e.authPayload)||"string"!=typeof e.authPayload.jwt)throw new i.uE("authPayload must be an object that contains the `jwt`. For example: `authPayload: { jwt: 'xxx.xxx.xxx'}`");(0,f.Gm)(e.authPayload.jwt)}!function(e){let t="";if("boolean"!=typeof e&&((0,i.Qd)(e)?(e.hasOwnProperty("clientsPresenceEnabled")&&"boolean"!=typeof e.clientsPresenceEnabled&&(t+="`clientsPresenceEnabled` in instance settings is not valid. Must be `true` or `false`.\n"),e.hasOwnProperty("listenToServerChangesEnabled")&&"boolean"!=typeof e.listenToServerChangesEnabled&&(t+="`listenToServerChangesEnabled` in instance settings is not valid. Must be `true` or `false`.\n")):t="`instant` flag must either be set to `true` or `false`\n",t))throw new i.uE(`${t}\nFor more information about PSPDFKit Instant please visit:\nhttps://pspdfkit.com/guides/web/current/instant/overview/`)}(e.instant);let u=null;if(e.instant)if((0,i.Qd)(e.instant)){const t=e.instant;u={clientsPresenceEnabled:!1!==t.clientsPresenceEnabled,listenToServerChangesEnabled:!1!==t.listenToServerChangesEnabled}}else u=x.S;this._requestQueue=new o.L(A.LB);const d=!!e.electronicSignatures&&Boolean(e.electronicSignatures.forceLegacySignaturesFeature),p=e.baseUrl||(0,S.$_)(window.document),g=e.baseCoreUrl||p;this._state=new w({serverUrl:l,hostedBaseUrl:c,documentId:e.documentId,instantSettings:u,documentURL:h,authPayload:e.authPayload,isFormsEnabled:!e.disableForms,forceLegacySignaturesFeature:d,disableWebAssemblyStreaming:e.disableWebAssemblyStreaming,overrideMemoryLimit:e.overrideMemoryLimit,baseCoreUrl:g}),e.trustedCAsCallback&&(0,i.R8)("PSPDFKit.Configuration#trustedCAsCallback is only used on Standalone deployments. On a Server-Backed deployment, please follow the instructions at https://pspdfkit.com/guides/web")}isUsingInstantProvider(){return null!=this._state.instantSettings}hasClientsPresence(){const e=this._state.instantSettings;return null!=e&&!1!==e.clientsPresenceEnabled}async load(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};await this.tryAuthenticateHostedViewer();const{imageToken:t,token:r,permissions:n,features:o,signatureFeatureAvailability:a,hasPassword:u,minSearchQueryLength:d,layerHandle:p,allowedTileScales:f,digitalSignatures:g,defaultGroup:w,collaborationPermissions:v,creatorName:y,documentEngineVersion:$,layerName:R,sourcePdfSha256:P}=await T(`${this._state.serverUrl}i/d/${this._state.documentId}`,this._state.authPayload,e.password);this._password=e.password;const I=(0,l.valid)((0,l.coerce)($));if(this.documentEngineVersion=I,!$||(0,l.lt)(I||"","1.5.3"))throw new i.uE(`Document Engine version ${$} does not meet minimum required version 1.5.3`);if(this._state.instantSettings&&!o.includes(M.Y.INSTANT))throw new i.uE("Instant feature is not enabled on this server. Please set `instant` to `false`.\n\nFor more information about PSPDFKit Instant please visit:\nhttps://pspdfkit.com/guides/web/current/instant/overview/");const A=a===E.g.ELECTRONIC_SIGNATURES&&(0,G.UX)(o)&&this._state.forceLegacySignaturesFeature?E.g.LEGACY_SIGNATURES:a;this._state=this._state.withMutations((e=>e.set("imageToken",t).set("token",r).set("features",(0,s.B8)(o)).set("signatureFeatureAvailability",A).set("backendPermissions",new L.A({readOnly:-1===n.indexOf("write"),downloadingAllowed:n.indexOf("download")>=0})).set("documentURL",`${this._state.serverUrl}i/d/${this._state.documentId}/h/${p}`).set("documentHandle",p).set("isDocumentHandleOutdated",!1).set("digitalSignatures",(0,m.N5)(g)).set("layerName",R).set("sourcePdfSha256",P)));if(this.corePDFBridge=new H.D(h.MX.createServerContext({url:this._state.serverUrl,token:this._state.token,imageToken:this._state.imageToken,documentId:this._state.documentId,layerHandle:p,headers:{"PSPDFKit-Platform":"web","PSPDFKit-Version":(0,S._q)()}},(async()=>{const t=q.mG.checkOut().object,r=(0,S.f)(null),n=await T(`${this._state.serverUrl}i/d/${this._state.documentId}`,this._state.authPayload,e.password);return(0,i.V1)(n.wasmToken,"No wasmToken was returned from the server."),(0,i.V1)(this._state.baseCoreUrl),await t.loadNativeModule(this._state.baseCoreUrl,{mainThreadOrigin:r,disableWebAssemblyStreaming:this._state.disableWebAssemblyStreaming,enableAutomaticLinkExtraction:!1,overrideMemoryLimit:this._state.overrideMemoryLimit,workerSpawnerFn:()=>c.pj(!0)}).then((async()=>t.load("",n.wasmToken,{mainThreadOrigin:r,productId:null}))),t}),(e=>!!this.documentEngineVersion&&(0,l.satisfies)(this.documentEngineVersion,e)))),v&&!this._state.instantSettings)throw new i.uE("Collaboration Permissions is not supported when `instant` is disabled. Please make sure `configuration#instant` is set to `true`.");return this._state=this._state.withMutations((e=>{e.defaultGroup=w,e.hasCollaborationPermissions=Boolean(v)})),this.provider&&this.provider.destroy(),this.provider=await this._initProvider(),this._state.instantSettings&&this.provider.setDocumentHandleConflictCallback(this.handleDocumentHandleConflict),{features:this._state.features,signatureFeatureAvailability:this._state.signatureFeatureAvailability,hasPassword:u,password:this._password,minSearchQueryLength:d,allowedTileScales:f,creatorName:y,defaultGroup:w}}async tryAuthenticateHostedViewer(){if("accessToken"in this._state.authPayload){const{hostedBaseUrl:e}=this._state,t=this._state.authPayload.accessToken,{serverUrl:r,serverId:n,jwt:s}=await async function(e,t){const r=await fetch(`${e}i/documents/auth`,{method:"POST",headers:{Accept:"application/json","Content-Type":"application/json","PSPDFKit-Platform":"web","PSPDFKit-Version":"cloud-protocol=1, server-protocol=5, client=2024.8.1, client-git=1a78bcd335"},body:JSON.stringify({accessToken:t})});if(r.ok)return r.json();throw new Error(`An error occurred while connecting to PSPDFKit API: ${await r.text()}`)}(e,t);this._state=this._state.withMutations((e=>{e.set("serverUrl",r).set("documentId",n).set("documentURL",`${r}i/d/${n}`).set("authPayload",{jwt:s})}))}}async _initProvider(){if(this._state.instantSettings){const e=`${this._state.serverUrl}i/d/${this._state.documentId}/h/${this._state.documentHandle}`,{InstantProvider:t}=await r.e(5534).then(r.bind(r,15124));return new t(`${this._state.serverUrl}i/d/${this._state.documentId}`,e,{auth_token:this._state.token},this._state.instantSettings)}{const e=this._state.isFormsEnabled&&this._state.features.includes(M.Y.FORMS),{RESTProvider:t}=await r.e(6269).then(r.bind(r,33393));return new t(this._state.documentURL,{token:this._state.token},{isFormsEnabled:e})}}destroy(){this._destroyProvider(),this._requestQueue&&this._requestQueue.destroy()}documentInfo(){return this._fetch(`${this._state.documentURL}/document.json`).then((e=>e.json())).then((e=>e.data))}getFormJSON(){return this._fetch(`${this._state.documentURL}/form.json`).then((e=>403===e.status?{v:1,type:"pspdfkit/form",annotations:[],fields:[]}:e.json().then((e=>e.data))))}async evalFormValuesActions(){throw new Error("not implemented")}async evalScript(){throw new Error("not implemented")}async setFormJSONUpdateBatchMode(){throw new Error("not implemented")}compareDocuments(){throw new Error("not implemented")}updateButtonIcon(){throw new Error("not implemented")}permissions(){return Promise.resolve(this._state.backendPermissions)}getDefaultGroup(){return this._state.defaultGroup}isCollaborationPermissionsEnabled(){return this._state.hasCollaborationPermissions}textForPageIndex(e){return this.corePDFBridge.getTextLines(e)}async getSuggestedLineHeightFactor(){return 1}getAvailableFontFaces(){throw new i.uE("Custom fonts need to be mounted on the server in Server-Backed deployments.")}getContentTreeForPageIndex(e){return this.corePDFBridge.getContentTree(e)}getTextFromRects(e,t){const r=encodeURIComponent(JSON.stringify(t.map(p.w).toArray()));return this._fetch(`${this._state.documentURL}/page-${e}-highlighted?rects=${r}`).then((e=>e.json())).then((e=>e.text))}_getJSONRequestHandler(){return g.A}renderAnnotation(e,t,r,n,s){return this._requestRenderAnnotation(e,t,r,n,s)}async renderPageAnnotations(e,t,r){if(!y.fK){const t=Promise.resolve();return this.pageAPStreamsPromises=this.pageAPStreamsPromises.set(e,t),t}const n=this.provider,s=t.some((e=>e instanceof N.sb));s&&await n._setReadStateCallbacksPromise;const i=[],o=t.filter((e=>{const t=(s?n._readStateCallbacks.getAnnotationWithFormField(e.id):null)?.formField,r=(0,b.lG)(e,t);if(r&&t&&"number"==typeof e.pdfObjectId){i.find((e=>e.name===t.name))||i.push((0,m.cA)((0,k.Af)(t)))}return r&&"number"==typeof e.pdfObjectId}));if(0===o.size&&0===i.length)return Promise.resolve();const a=new Promise(((t,n)=>{const s=o.filter((e=>0!==Math.floor(e.boundingBox.width*r)&&0!==Math.floor(e.boundingBox.height*r))),{promise:a,cancel:l}=this._requestRenderAnnotations(e,s.map((e=>e.pdfObjectId)).toArray(),s.map((e=>Math.floor(e.boundingBox.width*r))).toArray(),s.map((e=>Math.floor(e.boundingBox.height*r))).toArray(),i);a.then((e=>{const r=e.map((e=>e&&(0,U.BJ)(e)));r.forEach((async(e,t)=>{const r=await e,n=s.get(t);if(n){const e=this.annotationAPStreamPromises.get(n.id);e&&(this.annotationAPStreamPromises=this.annotationAPStreamPromises.delete(n.id),e(r)),r&&this.cacheAPStream(r,n)}})),Promise.all(r).then((()=>t()))})).catch((e=>{l(),n(e)}))}));return this.pageAPStreamsPromises=this.pageAPStreamsPromises.set(e,a),a}renderDetachedAnnotation(e,t,r,n){return this._requestRenderAnnotation(e,null,t,r,n,!0)}async getAttachment(e){try{const t=await this._fetch(`${this._state.documentURL}/attachments/${e}`);switch(t.status){case 404:throw new i.uE("Attachment not Found.");case 200:return await t.blob();default:throw new i.uE("Bad Request.")}}catch(e){throw new i.uE(`Could not fetch attachment from PSPDFKit Document Engine. ${e}`)}}async search(e,t,r,n){let s=arguments.length>4&&void 0!==arguments[4]&&arguments[4],i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:D.n.TEXT;const o=`q=${i===D.n.PRESET?e.replace(/_/g,"-"):encodeURIComponent(e)}&start=${t}&limit=${r}&type=${i}&include_annotations=${s.toString()}&case_sensitive=${n.toString()}`,a=`${this._state.documentURL}/search?${o}`,l=await new g.A(a,this._state.token).request();return(0,d.g)(l.data)}async getMeasurementSnappingPoints(e){}async searchAndRedact(e,t){const{searchType:r,annotationPreset:n,searchInAnnotations:i,caseSensitive:o,startPageIndex:a,pageRange:l}=t,{color:h,fillColor:c,outlineColor:u,...d}=n,p=await this._fetch(`${this._state.documentURL}/redactions`,{method:"post",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({strategy:r,strategyOptions:{[r]:r===D.n.PRESET?e.replace(/_/g,"-"):e,includeAnnotations:i,caseSensitive:o,...void 0!==a&&{start:a},...void 0!==l&&{limit:l}},content:{...d,color:h&&h.toHex(),fillColor:c&&c.toHex(),outlineColor:u&&u.toHex()}})}),{data:m}=await p.json();return(0,s.B8)(m&&m.annotations?m.annotations.map((e=>F.A.fromJSON(e.id,e.content))):[])}exportPDF(){let{flatten:e=!1,includeComments:t=!0,excludeAnnotations:r=!1,outputFormat:n=!1,optimize:s=!1,flattenElectronicSignatures:o=e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(e&&!1===o)throw new i.uE("Cannot set `flattenElectronicSignatures` to `false` when `flatten` is set to `true`.");const a=`${this._state.documentURL}/pdf?token=${this._state.token}${e?`&flatten=${String(e)}`:""}&comments=${String(t)}&render_ap_streams=${String(!e)}&remove_annotations=${String(r)}${e?"":`&keep_signatures=${!o}`}`;if(s){const e={documentFormat:"pdf",grayscaleText:!1,grayscaleGraphics:!1,grayscaleFormFields:!1,grayscaleAnnotations:!1,grayscaleImages:!1,disableImages:!1,mrcCompression:!1,imageOptimizationQuality:2,linearize:!1};let t;if("boolean"!=typeof s){t={...e,...s}}else t=e;const{documentFormat:r,grayscaleText:n,grayscaleGraphics:i,grayscaleFormFields:o,grayscaleAnnotations:l,grayscaleImages:h,disableImages:c,mrcCompression:u,imageOptimizationQuality:d,linearize:p}=t;return fetch(a,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({parts:[{document:{id:"#self"}}],output:{type:r,grayscaleText:n,grayscaleGraphics:i,grayscaleFormFields:o,grayscaleAnnotations:l,grayscaleImages:h,disableImages:c,mrcCompression:u,imageOptimizationQuality:d,linearize:p}}),credentials:"include"}).then((e=>e.arrayBuffer()))}if(n){const e={conformance:B.o.PDFA_2B,vectorization:!0,rasterization:!0};let t;if("boolean"!=typeof n){t={...e,...n}}else t=e;const{conformance:r,vectorization:s,rasterization:i}=t;return fetch(a,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({parts:[{document:{id:"#self"}}],output:{type:"pdfa",conformance:r,vectorization:s,rasterization:i}}),credentials:"include"}).then((e=>e.arrayBuffer()))}return fetch(a,{method:"GET",credentials:"include"}).then((e=>e.arrayBuffer()))}exportOffice(e){let{format:t}=e;const r=`${this._state.documentURL}/build`;return this._fetch(r,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({parts:[{document:{id:"#self"}}],output:{type:t.toLowerCase()}}),credentials:"include"}).then((e=>e.arrayBuffer())).catch((e=>{throw new i.uE(`Exporting to Office failed: ${e.message}`)}))}exportXFDF(){return this._fetch(`${this._state.documentURL}/document.xfdf`).then((e=>e.text()))}exportInstantJSON(e){return this._fetch(`${this._state.documentURL}/instant.json${"number"==typeof e?`?version=${e}`:""}`).then((e=>e.json()))}getPDFURL(){let{includeComments:e=!0,excludeAnnotations:t=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{promise:Promise.resolve(`${this._state.documentURL}/pdf?token=${this._state.token}&flatten=true&comments=${String(e)}&remove_annotations=${String(t)}`),revoke:()=>{}}}generatePDFObjectURL(){let e,{includeComments:t=!0,excludeAnnotations:r=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=!1;return{promise:new Promise((s=>{this.exportPDF({flatten:!0,includeComments:t,excludeAnnotations:r}).then((t=>{if(n)return;const r=new Blob([t],{type:"application/pdf"});e=window.URL.createObjectURL(r),s(e)}))})),revoke:()=>{e&&window.URL.revokeObjectURL(e),n=!0}}}async getDocumentOutline(){let e;try{e=(await this._fetch(`${this._state.documentURL}/outline.json`).then((e=>e.json()))).data}catch(t){e={}}const t=Array.isArray(e.outline)?e.outline:[];return(0,s.B8)(t.map(_.r))}async setDocumentOutline(){throw new i.uE("Not implemented in Server backend.")}async getPageGlyphs(){throw new i.uE("Not implemented in Server backend.")}onKeystrokeEvent(){throw new Error("not implemented")}async getMeasurementScales(){let e;try{return e=(await this._fetch(`${this._state.documentURL}/measurement_content_formats`).then((e=>e.json()))).data,e}catch(e){throw new i.uE(`Fetching measurement scales failed: ${e}`)}}async getSecondaryMeasurementUnit(){let e;try{return e=(await this._fetch(`${this._state.documentURL}/secondary_measurement_unit`).then((e=>e.json()))).data,e}catch(e){throw new i.uE(`Fetching secondary measurement unit failed: ${e}`)}}async setSecondaryMeasurementUnit(e){try{const t=JSON.stringify({unitTo:e?e.unitTo:null,precision:e?e.precision:null});await this._fetch(`${this._state.documentURL}/secondary_measurement_unit`,{method:"post",body:t,credentials:"include",headers:{"Content-Type":"application/json"}})}catch(e){throw new i.uE(`Setting secondary measurement unit failed: ${e}`)}}async addMeasurementScale(e){try{const t=JSON.stringify((0,u.U)(e));await this._fetch(`${this._state.documentURL}/measurement_content_formats`,{method:"post",body:t,credentials:"include",headers:{"Content-Type":"application/json"}})}catch(e){throw new i.uE(`Adding a new measurement scale failed: ${e}`)}}async removeMeasurementScale(e){try{const t=JSON.stringify((0,u.U)(e));await this._fetch(`${this._state.documentURL}/measurement_content_formats/delete`,{method:"post",body:t,credentials:"include",headers:{"Content-Type":"application/json"}})}catch(e){throw new i.uE(`Removing scale failed: ${e}`)}}async applyOperationsAndReload(e){try{const t=await J(e);this._destroyProvider(),await this._fetch(`${this._state.documentURL}/apply-operations`,{method:"post",body:t,credentials:"include"})}catch(e){throw new i.uE(`Applying operations failed: ${e}`)}return this.reloadDocument()}async applyRedactionsAndReload(){try{return this._destroyProvider(),await this._fetch(`${this._state.documentURL}/redact`,{method:"post",credentials:"include"}),this.reloadDocument()}catch(e){throw this.provider.load(),new i.uE(`Applying redactions failed: ${e}`)}}async reloadDocument(){try{return await this.load({password:this._password})}catch(e){throw new i.uE(`Reloading the document failed: ${e}`)}}async exportPDFWithOperations(e){try{const t=await J(e);return this._fetch(`${this._state.documentURL}/pdf-with-operations`,{method:"post",body:t,credentials:"include"}).then((e=>e.arrayBuffer()))}catch(e){throw new i.uE(`Exporting PDF with operations failed: ${e}`)}}async setSignaturesLTV(e){throw new Error("not implemented")}async getSignaturesInfo(){return this._refreshSignaturesInfoPromise&&await this._refreshSignaturesInfoPromise,this._state.digitalSignatures}refreshSignaturesInfo(){return this._refreshSignaturesInfoPromise||(this._refreshSignaturesInfoPromise=new Promise(((e,t)=>{this._fetch(`${this._state.documentURL}/signatures`,{method:"get",credentials:"include"}).then((e=>e.json())).then((t=>{let{data:r}=t;this._state=this._state.set("digitalSignatures",(0,m.N5)(r)),this._refreshSignaturesInfoPromise=null,e()})).catch((e=>{this._state=this._state.set("digitalSignatures",null),this._refreshSignaturesInfoPromise=null,t(e)}))}))),this._refreshSignaturesInfoPromise}async signDocumentAndReload(e,t){(0,i.V1)(void 0===t||"object"==typeof t,"Signing service data must be an object if specified.");try{if(void 0!==t&&"object"!=typeof t)throw new i.uE("Signing service data must be an object if specified.");(0,X.r6)(e);const r=e?{..."placeholderSize"in e?{estimatedSize:e.placeholderSize}:null,..."flatten"in e?{flatten:e.flatten}:null,...e?.signatureMetadata?{signatureMetadata:(0,m.sZ)(e.signatureMetadata)}:null,..."position"in e?{position:(0,m.qN)(e.position)}:null,..."appearance"in e?{appearance:await(0,m.z8)(e.appearance)}:null,..."formFieldName"in e?{formFieldName:e.formFieldName}:null,...void 0!==e?.signingData&&"signatureContainer"in e.signingData?{signatureContainer:e.signingData.signatureContainer}:null,...void 0!==e?.signingData&&"signatureType"in e.signingData?{signatureType:m.xz[e.signingData.signatureType]}:null}:null,n=e?.appearance?.watermarkImage||null,s=e?.appearance?.graphicImage||null,o={...t?{signingToken:t.signingToken}:null,...r},a=new FormData;if(a.append("data",JSON.stringify(o)),n&&a.append("image",n),s&&a.append("graphicImage",s),this._destroyProvider(),await this._fetch(`${this._state.documentURL}/sign`,{method:"post",body:a,credentials:"include"}),await this.reloadDocument(),e?.formFieldName)return e.formFieldName}catch(e){throw this.provider.load(),new i.uE(`Adding digital signature failed: ${e.message||e}`)}}async setFontSubstitutions(e){try{const t=`${this._state.documentURL}/font-substitutions`;await this._fetch(t,{method:"put",body:JSON.stringify({fontSubstitutions:e}),credentials:"include",headers:{"Content-Type":"application/json"}})}catch(e){throw new i.uE(`Error setting font substitution: ${e.message}`)}}getDocumentHandle(){return this._state.documentHandle}async getEmbeddedFiles(){const e=await this._fetch(`${this._state.documentURL}/embedded-files`,{method:"get",credentials:"include"}),t=await e.json();return(0,s.B8)(t?.data?.embeddedFiles?.length?t.data.embeddedFiles.map((e=>{let{id:t,content:r}=e;return(0,j.r)(t,r)})):[])}cancelRequests(){this._requestQueue.cancelAll()}_destroyProvider(){this.provider&&(this.provider._clients&&this.provider._clients.disconnect(),this.provider.destroy())}async _fetch(e,t){const r=await fetch(e,{...t,headers:{...t?.headers,"X-PSPDFKit-Token":this._state.token,"PSPDFKit-Platform":"web","PSPDFKit-Version":(0,S._q)()}});if(!r.ok){let e=await function(e){return e.clone().json().catch((()=>e.text()))}(r);e="object"==typeof e?e.reason:e;const t=e||`${r.status} ${r.statusText}`;throw new i.uE(t)}return r}syncChanges(){return this.provider.syncChanges()}async clearAPStreamCache(){}async runPDFFormattingScripts(){return[]}async runPDFFormattingScriptsFromWidgets(){return this.runPDFFormattingScripts()}async lazyLoadPages(){}async contentEditorReload(){return this._destroyProvider(),this.reloadDocument()}getOCGs(){throw new Error("not implemented")}getOCGVisibilityState(){throw new Error("not implemented")}setOCGVisibilityState(){throw new Error("not implemented")}async timestampData(e,t){throw new Error("not implemented")}async waitUntilFullyLoaded(e){throw new Error("not implemented")}}async function J(e){const t={},r=new WeakMap,n=await Promise.all(e.map((async(e,n)=>{if("importDocument"===e.type){const s=e.document;return(0,i.V1)(s instanceof File||s instanceof Blob,"Wrong `importDocument` operation `document` value: it must be a File or a Blob"),(0,O.g)(r,t,s,e,n,"document")}if("applyInstantJson"===e.type){const s=e.instantJson;(0,i.V1)("object"==typeof s&&null!==s,"Wrong `applyInstantJson` operation `instantJson` value: it must be an object");const o=JSON.stringify(s),a=new Blob([o],{type:"application/json"});return(0,O.g)(r,t,a,e,n,"dataFilePath")}if("applyXfdf"===e.type){const s=e.xfdf;(0,i.V1)("string"==typeof s,"Wrong `applyXfdf` operation `xfdf` value: it must be a string");const o=new Blob([s],{type:"application/vnd.adobe.xfdf"});return(0,O.g)(r,t,o,e,n,"dataFilePath")}return e}))),s=new FormData;s.append("operations",JSON.stringify({operations:n}));for(const e in t)s.append(e,t[e]);return s}},4824:(e,t,r)=>{"use strict";r.d(t,{S:()=>n});const n={clientsPresenceEnabled:!0,listenToServerChangesEnabled:!0}},93904:(e,t,r)=>{const n=Symbol("SemVer ANY");class s{static get ANY(){return n}constructor(e,t){if(t=i(t),e instanceof s){if(e.loose===!!t.loose)return e;e=e.value}e=e.trim().split(/\s+/).join(" "),h("comparator",e,t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===n?this.value="":this.value=this.operator+this.semver.version,h("comp",this)}parse(e){const t=this.options.loose?o[a.COMPARATORLOOSE]:o[a.COMPARATOR],r=e.match(t);if(!r)throw new TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==r[1]?r[1]:"","="===this.operator&&(this.operator=""),r[2]?this.semver=new c(r[2],this.options.loose):this.semver=n}toString(){return this.value}test(e){if(h("Comparator.test",e,this.options.loose),this.semver===n||e===n)return!0;if("string"==typeof e)try{e=new c(e,this.options)}catch(e){return!1}return l(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof s))throw new TypeError("a Comparator is required");return""===this.operator?""===this.value||new u(e.value,t).test(this.value):""===e.operator?""===e.value||new u(this.value,t).test(e.semver):(!(t=i(t)).includePrerelease||"<0.0.0-0"!==this.value&&"<0.0.0-0"!==e.value)&&(!(!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0")))&&(!(!this.operator.startsWith(">")||!e.operator.startsWith(">"))||(!(!this.operator.startsWith("<")||!e.operator.startsWith("<"))||(!(this.semver.version!==e.semver.version||!this.operator.includes("=")||!e.operator.includes("="))||(!!(l(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<"))||!!(l(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">")))))))}}e.exports=s;const i=r(98587),{safeRe:o,t:a}=r(99718),l=r(72111),h=r(57272),c=r(53908),u=r(78311)},78311:(e,t,r)=>{class n{constructor(e,t){if(t=i(t),e instanceof n)return e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease?e:new n(e.raw,t);if(e instanceof o)return this.raw=e.value,this.set=[[e]],this.format(),this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map((e=>this.parseRange(e.trim()))).filter((e=>e.length)),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const e=this.set[0];if(this.set=this.set.filter((e=>!g(e[0]))),0===this.set.length)this.set=[e];else if(this.set.length>1)for(const e of this.set)if(1===e.length&&E(e[0])){this.set=[e];break}}this.format()}format(){return this.range=this.set.map((e=>e.join(" ").trim())).join("||").trim(),this.range}toString(){return this.range}parseRange(e){const t=((this.options.includePrerelease&&m)|(this.options.loose&&f))+":"+e,r=s.get(t);if(r)return r;const n=this.options.loose,i=n?h[c.HYPHENRANGELOOSE]:h[c.HYPHENRANGE];e=e.replace(i,N(this.options.includePrerelease)),a("hyphen replace",e),e=e.replace(h[c.COMPARATORTRIM],u),a("comparator trim",e),e=e.replace(h[c.TILDETRIM],d),a("tilde trim",e),e=e.replace(h[c.CARETTRIM],p),a("caret trim",e);let l=e.split(" ").map((e=>v(e,this.options))).join(" ").split(/\s+/).map((e=>_(e,this.options)));n&&(l=l.filter((e=>(a("loose invalid filter",e,this.options),!!e.match(h[c.COMPARATORLOOSE]))))),a("range list",l);const E=new Map,w=l.map((e=>new o(e,this.options)));for(const e of w){if(g(e))return[e];E.set(e.value,e)}E.size>1&&E.has("")&&E.delete("");const y=[...E.values()];return s.set(t,y),y}intersects(e,t){if(!(e instanceof n))throw new TypeError("a Range is required");return this.set.some((r=>w(r,t)&&e.set.some((e=>w(e,t)&&r.every((r=>e.every((e=>r.intersects(e,t)))))))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new l(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(L(this.set[t],e,this.options))return!0;return!1}}e.exports=n;const s=new(r(38357))({max:1e3}),i=r(98587),o=r(93904),a=r(57272),l=r(53908),{safeRe:h,t:c,comparatorTrimReplace:u,tildeTrimReplace:d,caretTrimReplace:p}=r(99718),{FLAG_INCLUDE_PRERELEASE:m,FLAG_LOOSE:f}=r(94493),g=e=>"<0.0.0-0"===e.value,E=e=>""===e.value,w=(e,t)=>{let r=!0;const n=e.slice();let s=n.pop();for(;r&&n.length;)r=n.every((e=>s.intersects(e,t))),s=n.pop();return r},v=(e,t)=>(a("comp",e,t),e=R(e,t),a("caret",e),e=$(e,t),a("tildes",e),e=I(e,t),a("xrange",e),e=b(e,t),a("stars",e),e),y=e=>!e||"x"===e.toLowerCase()||"*"===e,$=(e,t)=>e.trim().split(/\s+/).map((e=>S(e,t))).join(" "),S=(e,t)=>{const r=t.loose?h[c.TILDELOOSE]:h[c.TILDE];return e.replace(r,((t,r,n,s,i)=>{let o;return a("tilde",e,t,r,n,s,i),y(r)?o="":y(n)?o=`>=${r}.0.0 <${+r+1}.0.0-0`:y(s)?o=`>=${r}.${n}.0 <${r}.${+n+1}.0-0`:i?(a("replaceTilde pr",i),o=`>=${r}.${n}.${s}-${i} <${r}.${+n+1}.0-0`):o=`>=${r}.${n}.${s} <${r}.${+n+1}.0-0`,a("tilde return",o),o}))},R=(e,t)=>e.trim().split(/\s+/).map((e=>P(e,t))).join(" "),P=(e,t)=>{a("caret",e,t);const r=t.loose?h[c.CARETLOOSE]:h[c.CARET],n=t.includePrerelease?"-0":"";return e.replace(r,((t,r,s,i,o)=>{let l;return a("caret",e,t,r,s,i,o),y(r)?l="":y(s)?l=`>=${r}.0.0${n} <${+r+1}.0.0-0`:y(i)?l="0"===r?`>=${r}.${s}.0${n} <${r}.${+s+1}.0-0`:`>=${r}.${s}.0${n} <${+r+1}.0.0-0`:o?(a("replaceCaret pr",o),l="0"===r?"0"===s?`>=${r}.${s}.${i}-${o} <${r}.${s}.${+i+1}-0`:`>=${r}.${s}.${i}-${o} <${r}.${+s+1}.0-0`:`>=${r}.${s}.${i}-${o} <${+r+1}.0.0-0`):(a("no pr"),l="0"===r?"0"===s?`>=${r}.${s}.${i}${n} <${r}.${s}.${+i+1}-0`:`>=${r}.${s}.${i}${n} <${r}.${+s+1}.0-0`:`>=${r}.${s}.${i} <${+r+1}.0.0-0`),a("caret return",l),l}))},I=(e,t)=>(a("replaceXRanges",e,t),e.split(/\s+/).map((e=>A(e,t))).join(" ")),A=(e,t)=>{e=e.trim();const r=t.loose?h[c.XRANGELOOSE]:h[c.XRANGE];return e.replace(r,((r,n,s,i,o,l)=>{a("xRange",e,r,n,s,i,o,l);const h=y(s),c=h||y(i),u=c||y(o),d=u;return"="===n&&d&&(n=""),l=t.includePrerelease?"-0":"",h?r=">"===n||"<"===n?"<0.0.0-0":"*":n&&d?(c&&(i=0),o=0,">"===n?(n=">=",c?(s=+s+1,i=0,o=0):(i=+i+1,o=0)):"<="===n&&(n="<",c?s=+s+1:i=+i+1),"<"===n&&(l="-0"),r=`${n+s}.${i}.${o}${l}`):c?r=`>=${s}.0.0${l} <${+s+1}.0.0-0`:u&&(r=`>=${s}.${i}.0${l} <${s}.${+i+1}.0-0`),a("xRange return",r),r}))},b=(e,t)=>(a("replaceStars",e,t),e.trim().replace(h[c.STAR],"")),_=(e,t)=>(a("replaceGTE0",e,t),e.trim().replace(h[t.includePrerelease?c.GTE0PRE:c.GTE0],"")),N=e=>(t,r,n,s,i,o,a,l,h,c,u,d,p)=>`${r=y(n)?"":y(s)?`>=${n}.0.0${e?"-0":""}`:y(i)?`>=${n}.${s}.0${e?"-0":""}`:o?`>=${r}`:`>=${r}${e?"-0":""}`} ${l=y(h)?"":y(c)?`<${+h+1}.0.0-0`:y(u)?`<${h}.${+c+1}.0-0`:d?`<=${h}.${c}.${u}-${d}`:e?`<${h}.${c}.${+u+1}-0`:`<=${l}`}`.trim(),L=(e,t,r)=>{for(let r=0;r<e.length;r++)if(!e[r].test(t))return!1;if(t.prerelease.length&&!r.includePrerelease){for(let r=0;r<e.length;r++)if(a(e[r].semver),e[r].semver!==o.ANY&&e[r].semver.prerelease.length>0){const n=e[r].semver;if(n.major===t.major&&n.minor===t.minor&&n.patch===t.patch)return!0}return!1}return!0}},53908:(e,t,r)=>{const n=r(57272),{MAX_LENGTH:s,MAX_SAFE_INTEGER:i}=r(94493),{safeRe:o,t:a}=r(99718),l=r(98587),{compareIdentifiers:h}=r(61123);class c{constructor(e,t){if(t=l(t),e instanceof c){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw new TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>s)throw new TypeError(`version is longer than ${s} characters`);n("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;const r=e.trim().match(t.loose?o[a.LOOSE]:o[a.FULL]);if(!r)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>i||this.major<0)throw new TypeError("Invalid major version");if(this.minor>i||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>i||this.patch<0)throw new TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map((e=>{if(/^[0-9]+$/.test(e)){const t=+e;if(t>=0&&t<i)return t}return e})):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(n("SemVer.compare",this.version,this.options,e),!(e instanceof c)){if("string"==typeof e&&e===this.version)return 0;e=new c(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof c||(e=new c(e,this.options)),h(this.major,e.major)||h(this.minor,e.minor)||h(this.patch,e.patch)}comparePre(e){if(e instanceof c||(e=new c(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{const r=this.prerelease[t],s=e.prerelease[t];if(n("prerelease compare",t,r,s),void 0===r&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===r)return-1;if(r!==s)return h(r,s)}while(++t)}compareBuild(e){e instanceof c||(e=new c(e,this.options));let t=0;do{const r=this.build[t],s=e.build[t];if(n("prerelease compare",t,r,s),void 0===r&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===r)return-1;if(r!==s)return h(r,s)}while(++t)}inc(e,t,r){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,r),this.inc("pre",t,r);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,r),this.inc("pre",t,r);break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{const e=Number(r)?1:0;if(!t&&!1===r)throw new Error("invalid increment argument: identifier is empty");if(0===this.prerelease.length)this.prerelease=[e];else{let n=this.prerelease.length;for(;--n>=0;)"number"==typeof this.prerelease[n]&&(this.prerelease[n]++,n=-2);if(-1===n){if(t===this.prerelease.join(".")&&!1===r)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let n=[t,e];!1===r&&(n=[t]),0===h(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=n):this.prerelease=n}break}default:throw new Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=c},57414:(e,t,r)=>{const n=r(30144);e.exports=(e,t)=>{const r=n(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null}},72111:(e,t,r)=>{const n=r(94641),s=r(13999),i=r(35580),o=r(54089),a=r(7059),l=r(25200);e.exports=(e,t,r,h)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e===r;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e!==r;case"":case"=":case"==":return n(e,r,h);case"!=":return s(e,r,h);case">":return i(e,r,h);case">=":return o(e,r,h);case"<":return a(e,r,h);case"<=":return l(e,r,h);default:throw new TypeError(`Invalid operator: ${t}`)}}},46170:(e,t,r)=>{const n=r(53908),s=r(30144),{safeRe:i,t:o}=r(99718);e.exports=(e,t)=>{if(e instanceof n)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let r=null;if((t=t||{}).rtl){let t;for(;(t=i[o.COERCERTL].exec(e))&&(!r||r.index+r[0].length!==e.length);)r&&t.index+t[0].length===r.index+r[0].length||(r=t),i[o.COERCERTL].lastIndex=t.index+t[1].length+t[2].length;i[o.COERCERTL].lastIndex=-1}else r=e.match(i[o.COERCE]);return null===r?null:s(`${r[2]}.${r[3]||"0"}.${r[4]||"0"}`,t)}},40909:(e,t,r)=>{const n=r(53908);e.exports=(e,t,r)=>{const s=new n(e,r),i=new n(t,r);return s.compare(i)||s.compareBuild(i)}},11763:(e,t,r)=>{const n=r(50560);e.exports=(e,t)=>n(e,t,!0)},50560:(e,t,r)=>{const n=r(53908);e.exports=(e,t,r)=>new n(e,r).compare(new n(t,r))},51832:(e,t,r)=>{const n=r(30144);e.exports=(e,t)=>{const r=n(e,null,!0),s=n(t,null,!0),i=r.compare(s);if(0===i)return null;const o=i>0,a=o?r:s,l=o?s:r,h=!!a.prerelease.length;if(!!l.prerelease.length&&!h)return l.patch||l.minor?a.patch?"patch":a.minor?"minor":"major":"major";const c=h?"pre":"";return r.major!==s.major?c+"major":r.minor!==s.minor?c+"minor":r.patch!==s.patch?c+"patch":"prerelease"}},94641:(e,t,r)=>{const n=r(50560);e.exports=(e,t,r)=>0===n(e,t,r)},35580:(e,t,r)=>{const n=r(50560);e.exports=(e,t,r)=>n(e,t,r)>0},54089:(e,t,r)=>{const n=r(50560);e.exports=(e,t,r)=>n(e,t,r)>=0},93007:(e,t,r)=>{const n=r(53908);e.exports=(e,t,r,s,i)=>{"string"==typeof r&&(i=s,s=r,r=void 0);try{return new n(e instanceof n?e.version:e,r).inc(t,s,i).version}catch(e){return null}}},7059:(e,t,r)=>{const n=r(50560);e.exports=(e,t,r)=>n(e,t,r)<0},25200:(e,t,r)=>{const n=r(50560);e.exports=(e,t,r)=>n(e,t,r)<=0},32938:(e,t,r)=>{const n=r(53908);e.exports=(e,t)=>new n(e,t).major},46254:(e,t,r)=>{const n=r(53908);e.exports=(e,t)=>new n(e,t).minor},13999:(e,t,r)=>{const n=r(50560);e.exports=(e,t,r)=>0!==n(e,t,r)},30144:(e,t,r)=>{const n=r(53908);e.exports=(e,t,r=!1)=>{if(e instanceof n)return e;try{return new n(e,t)}catch(e){if(!r)return null;throw e}}},24493:(e,t,r)=>{const n=r(53908);e.exports=(e,t)=>new n(e,t).patch},31729:(e,t,r)=>{const n=r(30144);e.exports=(e,t)=>{const r=n(e,t);return r&&r.prerelease.length?r.prerelease:null}},9970:(e,t,r)=>{const n=r(50560);e.exports=(e,t,r)=>n(t,e,r)},74277:(e,t,r)=>{const n=r(40909);e.exports=(e,t)=>e.sort(((e,r)=>n(r,e,t)))},97638:(e,t,r)=>{const n=r(78311);e.exports=(e,t,r)=>{try{t=new n(t,r)}catch(e){return!1}return t.test(e)}},43927:(e,t,r)=>{const n=r(40909);e.exports=(e,t)=>e.sort(((e,r)=>n(e,r,t)))},56953:(e,t,r)=>{const n=r(30144);e.exports=(e,t)=>{const r=n(e,t);return r?r.version:null}},99589:(e,t,r)=>{const n=r(99718),s=r(94493),i=r(53908),o=r(61123),a=r(30144),l=r(56953),h=r(57414),c=r(93007),u=r(51832),d=r(32938),p=r(46254),m=r(24493),f=r(31729),g=r(50560),E=r(9970),w=r(11763),v=r(40909),y=r(43927),$=r(74277),S=r(35580),R=r(7059),P=r(94641),I=r(13999),A=r(54089),b=r(25200),_=r(72111),N=r(46170),L=r(93904),T=r(78311),O=r(97638),x=r(77631),F=r(19628),D=r(270),C=r(41261),j=r(13874),U=r(97075),k=r(75571),M=r(5342),G=r(76780),B=r(72525),X=r(75032);e.exports={parse:a,valid:l,clean:h,inc:c,diff:u,major:d,minor:p,patch:m,prerelease:f,compare:g,rcompare:E,compareLoose:w,compareBuild:v,sort:y,rsort:$,gt:S,lt:R,eq:P,neq:I,gte:A,lte:b,cmp:_,coerce:N,Comparator:L,Range:T,satisfies:O,toComparators:x,maxSatisfying:F,minSatisfying:D,minVersion:C,validRange:j,outside:U,gtr:k,ltr:M,intersects:G,simplifyRange:B,subset:X,SemVer:i,re:n.re,src:n.src,tokens:n.t,SEMVER_SPEC_VERSION:s.SEMVER_SPEC_VERSION,RELEASE_TYPES:s.RELEASE_TYPES,compareIdentifiers:o.compareIdentifiers,rcompareIdentifiers:o.rcompareIdentifiers}},94493:e=>{const t=Number.MAX_SAFE_INTEGER||9007199254740991;e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:t,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},57272:e=>{const t="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};e.exports=t},61123:e=>{const t=/^[0-9]+$/,r=(e,r)=>{const n=t.test(e),s=t.test(r);return n&&s&&(e=+e,r=+r),e===r?0:n&&!s?-1:s&&!n?1:e<r?-1:1};e.exports={compareIdentifiers:r,rcompareIdentifiers:(e,t)=>r(t,e)}},98587:e=>{const t=Object.freeze({loose:!0}),r=Object.freeze({});e.exports=e=>e?"object"!=typeof e?t:e:r},99718:(e,t,r)=>{const{MAX_SAFE_COMPONENT_LENGTH:n,MAX_SAFE_BUILD_LENGTH:s,MAX_LENGTH:i}=r(94493),o=r(57272),a=(t=e.exports={}).re=[],l=t.safeRe=[],h=t.src=[],c=t.t={};let u=0;const d="[a-zA-Z0-9-]",p=[["\\s",1],["\\d",i],[d,s]],m=(e,t,r)=>{const n=(e=>{for(const[t,r]of p)e=e.split(`${t}*`).join(`${t}{0,${r}}`).split(`${t}+`).join(`${t}{1,${r}}`);return e})(t),s=u++;o(e,s,t),c[e]=s,h[s]=t,a[s]=new RegExp(t,r?"g":void 0),l[s]=new RegExp(n,r?"g":void 0)};m("NUMERICIDENTIFIER","0|[1-9]\\d*"),m("NUMERICIDENTIFIERLOOSE","\\d+"),m("NONNUMERICIDENTIFIER","\\d*[a-zA-Z-][a-zA-Z0-9-]*"),m("MAINVERSION",`(${h[c.NUMERICIDENTIFIER]})\\.(${h[c.NUMERICIDENTIFIER]})\\.(${h[c.NUMERICIDENTIFIER]})`),m("MAINVERSIONLOOSE",`(${h[c.NUMERICIDENTIFIERLOOSE]})\\.(${h[c.NUMERICIDENTIFIERLOOSE]})\\.(${h[c.NUMERICIDENTIFIERLOOSE]})`),m("PRERELEASEIDENTIFIER",`(?:${h[c.NUMERICIDENTIFIER]}|${h[c.NONNUMERICIDENTIFIER]})`),m("PRERELEASEIDENTIFIERLOOSE",`(?:${h[c.NUMERICIDENTIFIERLOOSE]}|${h[c.NONNUMERICIDENTIFIER]})`),m("PRERELEASE",`(?:-(${h[c.PRERELEASEIDENTIFIER]}(?:\\.${h[c.PRERELEASEIDENTIFIER]})*))`),m("PRERELEASELOOSE",`(?:-?(${h[c.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${h[c.PRERELEASEIDENTIFIERLOOSE]})*))`),m("BUILDIDENTIFIER","[a-zA-Z0-9-]+"),m("BUILD",`(?:\\+(${h[c.BUILDIDENTIFIER]}(?:\\.${h[c.BUILDIDENTIFIER]})*))`),m("FULLPLAIN",`v?${h[c.MAINVERSION]}${h[c.PRERELEASE]}?${h[c.BUILD]}?`),m("FULL",`^${h[c.FULLPLAIN]}$`),m("LOOSEPLAIN",`[v=\\s]*${h[c.MAINVERSIONLOOSE]}${h[c.PRERELEASELOOSE]}?${h[c.BUILD]}?`),m("LOOSE",`^${h[c.LOOSEPLAIN]}$`),m("GTLT","((?:<|>)?=?)"),m("XRANGEIDENTIFIERLOOSE",`${h[c.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),m("XRANGEIDENTIFIER",`${h[c.NUMERICIDENTIFIER]}|x|X|\\*`),m("XRANGEPLAIN",`[v=\\s]*(${h[c.XRANGEIDENTIFIER]})(?:\\.(${h[c.XRANGEIDENTIFIER]})(?:\\.(${h[c.XRANGEIDENTIFIER]})(?:${h[c.PRERELEASE]})?${h[c.BUILD]}?)?)?`),m("XRANGEPLAINLOOSE",`[v=\\s]*(${h[c.XRANGEIDENTIFIERLOOSE]})(?:\\.(${h[c.XRANGEIDENTIFIERLOOSE]})(?:\\.(${h[c.XRANGEIDENTIFIERLOOSE]})(?:${h[c.PRERELEASELOOSE]})?${h[c.BUILD]}?)?)?`),m("XRANGE",`^${h[c.GTLT]}\\s*${h[c.XRANGEPLAIN]}$`),m("XRANGELOOSE",`^${h[c.GTLT]}\\s*${h[c.XRANGEPLAINLOOSE]}$`),m("COERCE",`(^|[^\\d])(\\d{1,${n}})(?:\\.(\\d{1,${n}}))?(?:\\.(\\d{1,${n}}))?(?:$|[^\\d])`),m("COERCERTL",h[c.COERCE],!0),m("LONETILDE","(?:~>?)"),m("TILDETRIM",`(\\s*)${h[c.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",m("TILDE",`^${h[c.LONETILDE]}${h[c.XRANGEPLAIN]}$`),m("TILDELOOSE",`^${h[c.LONETILDE]}${h[c.XRANGEPLAINLOOSE]}$`),m("LONECARET","(?:\\^)"),m("CARETTRIM",`(\\s*)${h[c.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",m("CARET",`^${h[c.LONECARET]}${h[c.XRANGEPLAIN]}$`),m("CARETLOOSE",`^${h[c.LONECARET]}${h[c.XRANGEPLAINLOOSE]}$`),m("COMPARATORLOOSE",`^${h[c.GTLT]}\\s*(${h[c.LOOSEPLAIN]})$|^$`),m("COMPARATOR",`^${h[c.GTLT]}\\s*(${h[c.FULLPLAIN]})$|^$`),m("COMPARATORTRIM",`(\\s*)${h[c.GTLT]}\\s*(${h[c.LOOSEPLAIN]}|${h[c.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",m("HYPHENRANGE",`^\\s*(${h[c.XRANGEPLAIN]})\\s+-\\s+(${h[c.XRANGEPLAIN]})\\s*$`),m("HYPHENRANGELOOSE",`^\\s*(${h[c.XRANGEPLAINLOOSE]})\\s+-\\s+(${h[c.XRANGEPLAINLOOSE]})\\s*$`),m("STAR","(<|>)?=?\\s*\\*"),m("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),m("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},38357:(e,t,r)=>{"use strict";const n=r(28799),s=Symbol("max"),i=Symbol("length"),o=Symbol("lengthCalculator"),a=Symbol("allowStale"),l=Symbol("maxAge"),h=Symbol("dispose"),c=Symbol("noDisposeOnSet"),u=Symbol("lruList"),d=Symbol("cache"),p=Symbol("updateAgeOnGet"),m=()=>1;const f=(e,t,r)=>{const n=e[d].get(t);if(n){const t=n.value;if(g(e,t)){if(w(e,n),!e[a])return}else r&&(e[p]&&(n.value.now=Date.now()),e[u].unshiftNode(n));return t.value}},g=(e,t)=>{if(!t||!t.maxAge&&!e[l])return!1;const r=Date.now()-t.now;return t.maxAge?r>t.maxAge:e[l]&&r>e[l]},E=e=>{if(e[i]>e[s])for(let t=e[u].tail;e[i]>e[s]&&null!==t;){const r=t.prev;w(e,t),t=r}},w=(e,t)=>{if(t){const r=t.value;e[h]&&e[h](r.key,r.value),e[i]-=r.length,e[d].delete(r.key),e[u].removeNode(t)}};class v{constructor(e,t,r,n,s){this.key=e,this.value=t,this.length=r,this.now=n,this.maxAge=s||0}}const y=(e,t,r,n)=>{let s=r.value;g(e,s)&&(w(e,r),e[a]||(s=void 0)),s&&t.call(n,s.value,s.key,e)};e.exports=class{constructor(e){if("number"==typeof e&&(e={max:e}),e||(e={}),e.max&&("number"!=typeof e.max||e.max<0))throw new TypeError("max must be a non-negative number");this[s]=e.max||1/0;const t=e.length||m;if(this[o]="function"!=typeof t?m:t,this[a]=e.stale||!1,e.maxAge&&"number"!=typeof e.maxAge)throw new TypeError("maxAge must be a number");this[l]=e.maxAge||0,this[h]=e.dispose,this[c]=e.noDisposeOnSet||!1,this[p]=e.updateAgeOnGet||!1,this.reset()}set max(e){if("number"!=typeof e||e<0)throw new TypeError("max must be a non-negative number");this[s]=e||1/0,E(this)}get max(){return this[s]}set allowStale(e){this[a]=!!e}get allowStale(){return this[a]}set maxAge(e){if("number"!=typeof e)throw new TypeError("maxAge must be a non-negative number");this[l]=e,E(this)}get maxAge(){return this[l]}set lengthCalculator(e){"function"!=typeof e&&(e=m),e!==this[o]&&(this[o]=e,this[i]=0,this[u].forEach((e=>{e.length=this[o](e.value,e.key),this[i]+=e.length}))),E(this)}get lengthCalculator(){return this[o]}get length(){return this[i]}get itemCount(){return this[u].length}rforEach(e,t){t=t||this;for(let r=this[u].tail;null!==r;){const n=r.prev;y(this,e,r,t),r=n}}forEach(e,t){t=t||this;for(let r=this[u].head;null!==r;){const n=r.next;y(this,e,r,t),r=n}}keys(){return this[u].toArray().map((e=>e.key))}values(){return this[u].toArray().map((e=>e.value))}reset(){this[h]&&this[u]&&this[u].length&&this[u].forEach((e=>this[h](e.key,e.value))),this[d]=new Map,this[u]=new n,this[i]=0}dump(){return this[u].map((e=>!g(this,e)&&{k:e.key,v:e.value,e:e.now+(e.maxAge||0)})).toArray().filter((e=>e))}dumpLru(){return this[u]}set(e,t,r){if((r=r||this[l])&&"number"!=typeof r)throw new TypeError("maxAge must be a number");const n=r?Date.now():0,a=this[o](t,e);if(this[d].has(e)){if(a>this[s])return w(this,this[d].get(e)),!1;const o=this[d].get(e).value;return this[h]&&(this[c]||this[h](e,o.value)),o.now=n,o.maxAge=r,o.value=t,this[i]+=a-o.length,o.length=a,this.get(e),E(this),!0}const p=new v(e,t,a,n,r);return p.length>this[s]?(this[h]&&this[h](e,t),!1):(this[i]+=p.length,this[u].unshift(p),this[d].set(e,this[u].head),E(this),!0)}has(e){if(!this[d].has(e))return!1;const t=this[d].get(e).value;return!g(this,t)}get(e){return f(this,e,!0)}peek(e){return f(this,e,!1)}pop(){const e=this[u].tail;return e?(w(this,e),e.value):null}del(e){w(this,this[d].get(e))}load(e){this.reset();const t=Date.now();for(let r=e.length-1;r>=0;r--){const n=e[r],s=n.e||0;if(0===s)this.set(n.k,n.v);else{const e=s-t;e>0&&this.set(n.k,n.v,e)}}}prune(){this[d].forEach(((e,t)=>f(this,t,!1)))}}},75571:(e,t,r)=>{const n=r(97075);e.exports=(e,t,r)=>n(e,t,">",r)},76780:(e,t,r)=>{const n=r(78311);e.exports=(e,t,r)=>(e=new n(e,r),t=new n(t,r),e.intersects(t,r))},5342:(e,t,r)=>{const n=r(97075);e.exports=(e,t,r)=>n(e,t,"<",r)},19628:(e,t,r)=>{const n=r(53908),s=r(78311);e.exports=(e,t,r)=>{let i=null,o=null,a=null;try{a=new s(t,r)}catch(e){return null}return e.forEach((e=>{a.test(e)&&(i&&-1!==o.compare(e)||(i=e,o=new n(i,r)))})),i}},270:(e,t,r)=>{const n=r(53908),s=r(78311);e.exports=(e,t,r)=>{let i=null,o=null,a=null;try{a=new s(t,r)}catch(e){return null}return e.forEach((e=>{a.test(e)&&(i&&1!==o.compare(e)||(i=e,o=new n(i,r)))})),i}},41261:(e,t,r)=>{const n=r(53908),s=r(78311),i=r(35580);e.exports=(e,t)=>{e=new s(e,t);let r=new n("0.0.0");if(e.test(r))return r;if(r=new n("0.0.0-0"),e.test(r))return r;r=null;for(let t=0;t<e.set.length;++t){const s=e.set[t];let o=null;s.forEach((e=>{const t=new n(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":o&&!i(t,o)||(o=t);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${e.operator}`)}})),!o||r&&!i(r,o)||(r=o)}return r&&e.test(r)?r:null}},97075:(e,t,r)=>{const n=r(53908),s=r(93904),{ANY:i}=s,o=r(78311),a=r(97638),l=r(35580),h=r(7059),c=r(25200),u=r(54089);e.exports=(e,t,r,d)=>{let p,m,f,g,E;switch(e=new n(e,d),t=new o(t,d),r){case">":p=l,m=c,f=h,g=">",E=">=";break;case"<":p=h,m=u,f=l,g="<",E="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(a(e,t,d))return!1;for(let r=0;r<t.set.length;++r){const n=t.set[r];let o=null,a=null;if(n.forEach((e=>{e.semver===i&&(e=new s(">=0.0.0")),o=o||e,a=a||e,p(e.semver,o.semver,d)?o=e:f(e.semver,a.semver,d)&&(a=e)})),o.operator===g||o.operator===E)return!1;if((!a.operator||a.operator===g)&&m(e,a.semver))return!1;if(a.operator===E&&f(e,a.semver))return!1}return!0}},72525:(e,t,r)=>{const n=r(97638),s=r(50560);e.exports=(e,t,r)=>{const i=[];let o=null,a=null;const l=e.sort(((e,t)=>s(e,t,r)));for(const e of l){n(e,t,r)?(a=e,o||(o=e)):(a&&i.push([o,a]),a=null,o=null)}o&&i.push([o,null]);const h=[];for(const[e,t]of i)e===t?h.push(e):t||e!==l[0]?t?e===l[0]?h.push(`<=${t}`):h.push(`${e} - ${t}`):h.push(`>=${e}`):h.push("*");const c=h.join(" || "),u="string"==typeof t.raw?t.raw:String(t);return c.length<u.length?c:t}},75032:(e,t,r)=>{const n=r(78311),s=r(93904),{ANY:i}=s,o=r(97638),a=r(50560),l=[new s(">=0.0.0-0")],h=[new s(">=0.0.0")],c=(e,t,r)=>{if(e===t)return!0;if(1===e.length&&e[0].semver===i){if(1===t.length&&t[0].semver===i)return!0;e=r.includePrerelease?l:h}if(1===t.length&&t[0].semver===i){if(r.includePrerelease)return!0;t=h}const n=new Set;let s,c,p,m,f,g,E;for(const t of e)">"===t.operator||">="===t.operator?s=u(s,t,r):"<"===t.operator||"<="===t.operator?c=d(c,t,r):n.add(t.semver);if(n.size>1)return null;if(s&&c){if(p=a(s.semver,c.semver,r),p>0)return null;if(0===p&&(">="!==s.operator||"<="!==c.operator))return null}for(const e of n){if(s&&!o(e,String(s),r))return null;if(c&&!o(e,String(c),r))return null;for(const n of t)if(!o(e,String(n),r))return!1;return!0}let w=!(!c||r.includePrerelease||!c.semver.prerelease.length)&&c.semver,v=!(!s||r.includePrerelease||!s.semver.prerelease.length)&&s.semver;w&&1===w.prerelease.length&&"<"===c.operator&&0===w.prerelease[0]&&(w=!1);for(const e of t){if(E=E||">"===e.operator||">="===e.operator,g=g||"<"===e.operator||"<="===e.operator,s)if(v&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===v.major&&e.semver.minor===v.minor&&e.semver.patch===v.patch&&(v=!1),">"===e.operator||">="===e.operator){if(m=u(s,e,r),m===e&&m!==s)return!1}else if(">="===s.operator&&!o(s.semver,String(e),r))return!1;if(c)if(w&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===w.major&&e.semver.minor===w.minor&&e.semver.patch===w.patch&&(w=!1),"<"===e.operator||"<="===e.operator){if(f=d(c,e,r),f===e&&f!==c)return!1}else if("<="===c.operator&&!o(c.semver,String(e),r))return!1;if(!e.operator&&(c||s)&&0!==p)return!1}return!(s&&g&&!c&&0!==p)&&(!(c&&E&&!s&&0!==p)&&(!v&&!w))},u=(e,t,r)=>{if(!e)return t;const n=a(e.semver,t.semver,r);return n>0?e:n<0||">"===t.operator&&">="===e.operator?t:e},d=(e,t,r)=>{if(!e)return t;const n=a(e.semver,t.semver,r);return n<0?e:n>0||"<"===t.operator&&"<="===e.operator?t:e};e.exports=(e,t,r={})=>{if(e===t)return!0;e=new n(e,r),t=new n(t,r);let s=!1;e:for(const n of e.set){for(const e of t.set){const t=c(n,e,r);if(s=s||null!==t,t)continue e}if(s)return!1}return!0}},77631:(e,t,r)=>{const n=r(78311);e.exports=(e,t)=>new n(e,t).set.map((e=>e.map((e=>e.value)).join(" ").trim().split(" ")))},13874:(e,t,r)=>{const n=r(78311);e.exports=(e,t)=>{try{return new n(e,t).range||"*"}catch(e){return null}}},40259:e=>{"use strict";e.exports=function(e){e.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}},28799:(e,t,r)=>{"use strict";function n(e){var t=this;if(t instanceof n||(t=new n),t.tail=null,t.head=null,t.length=0,e&&"function"==typeof e.forEach)e.forEach((function(e){t.push(e)}));else if(arguments.length>0)for(var r=0,s=arguments.length;r<s;r++)t.push(arguments[r]);return t}function s(e,t,r){var n=t===e.head?new a(r,null,t,e):new a(r,t,t.next,e);return null===n.next&&(e.tail=n),null===n.prev&&(e.head=n),e.length++,n}function i(e,t){e.tail=new a(t,e.tail,null,e),e.head||(e.head=e.tail),e.length++}function o(e,t){e.head=new a(t,null,e.head,e),e.tail||(e.tail=e.head),e.length++}function a(e,t,r,n){if(!(this instanceof a))return new a(e,t,r,n);this.list=n,this.value=e,t?(t.next=this,this.prev=t):this.prev=null,r?(r.prev=this,this.next=r):this.next=null}e.exports=n,n.Node=a,n.create=n,n.prototype.removeNode=function(e){if(e.list!==this)throw new Error("removing node which does not belong to this list");var t=e.next,r=e.prev;return t&&(t.prev=r),r&&(r.next=t),e===this.head&&(this.head=t),e===this.tail&&(this.tail=r),e.list.length--,e.next=null,e.prev=null,e.list=null,t},n.prototype.unshiftNode=function(e){if(e!==this.head){e.list&&e.list.removeNode(e);var t=this.head;e.list=this,e.next=t,t&&(t.prev=e),this.head=e,this.tail||(this.tail=e),this.length++}},n.prototype.pushNode=function(e){if(e!==this.tail){e.list&&e.list.removeNode(e);var t=this.tail;e.list=this,e.prev=t,t&&(t.next=e),this.tail=e,this.head||(this.head=e),this.length++}},n.prototype.push=function(){for(var e=0,t=arguments.length;e<t;e++)i(this,arguments[e]);return this.length},n.prototype.unshift=function(){for(var e=0,t=arguments.length;e<t;e++)o(this,arguments[e]);return this.length},n.prototype.pop=function(){if(this.tail){var e=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,e}},n.prototype.shift=function(){if(this.head){var e=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,e}},n.prototype.forEach=function(e,t){t=t||this;for(var r=this.head,n=0;null!==r;n++)e.call(t,r.value,n,this),r=r.next},n.prototype.forEachReverse=function(e,t){t=t||this;for(var r=this.tail,n=this.length-1;null!==r;n--)e.call(t,r.value,n,this),r=r.prev},n.prototype.get=function(e){for(var t=0,r=this.head;null!==r&&t<e;t++)r=r.next;if(t===e&&null!==r)return r.value},n.prototype.getReverse=function(e){for(var t=0,r=this.tail;null!==r&&t<e;t++)r=r.prev;if(t===e&&null!==r)return r.value},n.prototype.map=function(e,t){t=t||this;for(var r=new n,s=this.head;null!==s;)r.push(e.call(t,s.value,this)),s=s.next;return r},n.prototype.mapReverse=function(e,t){t=t||this;for(var r=new n,s=this.tail;null!==s;)r.push(e.call(t,s.value,this)),s=s.prev;return r},n.prototype.reduce=function(e,t){var r,n=this.head;if(arguments.length>1)r=t;else{if(!this.head)throw new TypeError("Reduce of empty list with no initial value");n=this.head.next,r=this.head.value}for(var s=0;null!==n;s++)r=e(r,n.value,s),n=n.next;return r},n.prototype.reduceReverse=function(e,t){var r,n=this.tail;if(arguments.length>1)r=t;else{if(!this.tail)throw new TypeError("Reduce of empty list with no initial value");n=this.tail.prev,r=this.tail.value}for(var s=this.length-1;null!==n;s--)r=e(r,n.value,s),n=n.prev;return r},n.prototype.toArray=function(){for(var e=new Array(this.length),t=0,r=this.head;null!==r;t++)e[t]=r.value,r=r.next;return e},n.prototype.toArrayReverse=function(){for(var e=new Array(this.length),t=0,r=this.tail;null!==r;t++)e[t]=r.value,r=r.prev;return e},n.prototype.slice=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var r=new n;if(t<e||t<0)return r;e<0&&(e=0),t>this.length&&(t=this.length);for(var s=0,i=this.head;null!==i&&s<e;s++)i=i.next;for(;null!==i&&s<t;s++,i=i.next)r.push(i.value);return r},n.prototype.sliceReverse=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var r=new n;if(t<e||t<0)return r;e<0&&(e=0),t>this.length&&(t=this.length);for(var s=this.length,i=this.tail;null!==i&&s>t;s--)i=i.prev;for(;null!==i&&s>e;s--,i=i.prev)r.push(i.value);return r},n.prototype.splice=function(e,t,...r){e>this.length&&(e=this.length-1),e<0&&(e=this.length+e);for(var n=0,i=this.head;null!==i&&n<e;n++)i=i.next;var o=[];for(n=0;i&&n<t;n++)o.push(i.value),i=this.removeNode(i);null===i&&(i=this.tail),i!==this.head&&i!==this.tail&&(i=i.prev);for(n=0;n<r.length;n++)i=s(this,i,r[n]);return o},n.prototype.reverse=function(){for(var e=this.head,t=this.tail,r=e;null!==r;r=r.prev){var n=r.prev;r.prev=r.next,r.next=n}return this.head=t,this.tail=e,this};try{r(40259)(n)}catch(e){}}}]);