module.exports = {
  // Specify the line length that the printer will wrap on.
  printWidth: 80,

  // Changed in 2.0.0 to reflect JavaScript code-standards
  // Print trailing commas wherever possible in multi-line comma-separated syntactic structures. (A single-line array, for example, never gets trailing commas.)
  trailingComma: "es5",

  // Specify the number of spaces per indentation-level.
  tabWidth: 2,

  // Print semicolons at the ends of statements.
  semi: true,

  // Use single quotes instead of double quotes.
  singleQuote: false,

  // Change when properties in objects are quoted.
  quoteProps: "as-needed",

  // Use single quotes instead of double quotes in JSX.
  jsxSingleQuote: false,

  // Print spaces between brackets in object literals.
  bracketSpacing: true,

  // Changed in 2.0.0 to reflect JavaScript code-standards
  // Include parentheses around a sole arrow function parameter.
  arrowParens: "always",

  // <PERSON><PERSON><PERSON> can restrict itself to only format files that contain a special comment, called a pragma, at the top of the file. This is very useful when gradually transitioning large, unformatted codebases to Prettier.
  requirePragma: false,

  // <PERSON><PERSON><PERSON> can insert a special @format marker at the top of files specifying that the file has been formatted with <PERSON><PERSON><PERSON>. This works well when used in tandem with the --require-pragma option. If there is already a docblock at the top of the file then this option will add a newline to it with the @format marker.
  insertPragma: false,

  // All modern text editors in all operating systems are able to correctly display line endings when \n (LF) is used. However, old versions of Notepad for Windows will visually squash such lines into one as they can only deal with \r\n (CRLF).
  endOfLine: "lf",

  // Specify the global whitespace sensitivity for HTML, Vue, Angular, and Handlebars. See whitespace-sensitive formatting for more info.
  htmlWhitespaceSensitivity: "css",
};
