{"name": "fabpro_react", "version": "0.1.0", "private": true, "dependencies": {"@datadog/browser-logs": "^4.49.0", "@datadog/browser-rum": "^4.49.0", "@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/react-fontawesome": "^0.2.0", "@mui/icons-material": "^6.1.8", "@mui/material": "^6.1.8", "@mui/styles": "^6.4.5", "@mui/x-charts": "^7.29.1", "@mui/x-data-grid": "^7.28.0", "@sbd-ctg/user-behavior-tracking": "^2.0.1", "ag-grid-enterprise": "23.2.1", "ag-grid-react": "23.2.1", "axios": "^0.27.2", "axios-1x": "npm:axios@^1.9.0", "axios-hooks": "^5.1.1", "chart.js": "^4.4.0", "core-js": "^3.33.2", "html2canvas": "^1.3.2", "jsonwebtoken": "^9.0.2", "luxon": "^3.5.0", "mathjs": "^11.11.1", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "msuite_storybook": "^2.5.2", "node-polyfill-webpack-plugin": "^2.0.1", "patch-package": "^8.0.0", "polished": "^4.1.3", "powerbi-client": "2.23.1", "powerbi-client-react": "1.4.0", "pspdfkit": "2024.8.1", "react": "^17.0.2", "react-chartjs-2": "^5.2.0", "react-cropper": "^2.1.8", "react-datepicker": "^3.8.0", "react-dnd": "^11.1.3", "react-dnd-html5-backend": "^11.1.3", "react-dom": "^17.0.2", "react-flow-renderer": "^9.7.3", "react-icons": "^4.3.1", "react-is": "^18.2.0", "react-redux": "^8.1.2", "react-router-dom": "^5.3.4", "react-scripts": "5.0.1", "react-select": "^5.2.1", "react-switch": "^7.0.0", "redux": "^4.1.2", "redux-thunk": "^2.4.0", "sass": "^1.48.0", "seedrandom": "^3.0.5", "styled-components": "^6.0.8", "use-local-storage": "^3.0.0"}, "scripts": {"start": "react-scripts start", "start-2": "set PORT=3001&& react-scripts start", "build": "set \"GENERATE_SOURCEMAP=false\" && react-scripts --max_old_space_size=4096 build", "test": "react-scripts test --transformIgnorePatterns 'node_modules/(?!@sbd-ctg)'", "coverage": "npm test -- --coverage", "eject": "react-scripts eject", "lint": "eslint src/**/*.js", "pretty": "prettier --write \"src/**/*.{js,scss,css,json}\" --config ./.prettierrc.js", "refreshVSToken": "vsts-npm-auth -config .npmrc", "prepare": "husky install", "pretty-quick": "pretty-quick --staged", "postinstall": "npx patch-package"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.23.7", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/preset-env": "^7.23.8", "@babel/preset-react": "^7.23.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "axios-mock-adapter": "^1.22.0", "babel-jest": "^29.7.0", "concurrently": "^8.2.2", "eslint-plugin-react": "^7.30.1", "husky": "^8.0.3", "jest": "^27.5.1", "jest-environment-jsdom": "^29.7.0", "prettier": "^2.0.1", "pretty-quick": "^3.1.1", "react-test-renderer": "^17.0.2", "redux-devtools-extension": "^2.13.8", "redux-mock-store": "^1.5.4", "underscore": "^1.13.6"}, "babel": {"presets": ["@babel/preset-env", "@babel/preset-react"]}}