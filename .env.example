REACT_APP_API=https://devapi.fabpro1.com/api
REACT_APP_LOGIN=https://devlogin.msuite.tech
REACT_APP_FABPRO=https://devfab.msuite.tech
REACT_APP_DEV_PSPDFKIT_LICENSE='license key for pspdf for given domain'
REACT_APP_AG_GRID_LICENSE='dev license key for ag-grid'
REACT_APP_ENVIRONMENT=dev
REACT_APP_CHURN_APP_KEY = 'churn app key'
REACT_APP_CHURN_ENDPOINT='https://msuite-dev.us1app.churnzero.net/churnzero.js'
GENERATE_SOURCEMAP=false
REACT_APP_USER_BEHAVIOR_TRACKING_TOKEN = ""
REACT_APP_PBI_SHARED_WORKSPACE_NAME=""

#referenced in source code
DD_APP_ID = ''
DD_CLIENT_TOKEN = ''
DD_ENV=development
DD_SERVICE="dev-fab.msuite.com"

#referenced in environment config only
DD_LOGS_INJECTION=true
DD_PROFILING_ENABLED=true
DD_TRACE_AGENT_URL=""
DD_VERSION=0.1
DD_APPSEC_ENABLED=true
DD_DBM_PROPAGATION_MODE=full